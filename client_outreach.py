#!/usr/bin/env python3
"""
Client Outreach Script

This script handles the outreach process for new insurance clients/leads.
It can send an email, text message, and leave a voicemail for the new client.
"""

import os
import sys
import json
import asyncio
import argparse
from datetime import datetime
from typing import Dict, Any, Optional, List

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import required modules
from services.gmail_service import GmailService

# Set up basic logging
import logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("client_outreach")

class ClientOutreach:
    """
    Handles the outreach process for new insurance clients/leads.
    """

    def __init__(self):
        """Initialize the client outreach service."""
        self.config = self._load_config()
        self.gmail_service = None

    async def initialize(self):
        """Initialize the services."""
        try:
            # Initialize Gmail service
            email_config = self.config.get("email_integration", {})
            if email_config.get("enabled", False):
                # Create credentials directory if it doesn't exist
                os.makedirs("credentials", exist_ok=True)
                os.makedirs("tokens", exist_ok=True)

                # Initialize Gmail service with proper paths
                self.gmail_service = GmailService(
                    credentials_path="credentials/gmail_credentials.json",
                    token_path="tokens/gmail_token.json"
                )

                if self.gmail_service.is_enabled():
                    logger.info(f"Gmail service initialized for {self.gmail_service.get_user_email()}")
                else:
                    logger.warning("Gmail service initialization failed")

        except Exception as e:
            logger.exception(f"Error initializing services: {e}")

    def _load_config(self) -> Dict:
        """
        Load configuration from the communication_services.json file.

        Returns:
            Dict: Configuration dictionary
        """
        try:
            config_path = os.path.join("config", "communication_services.json")
            with open(config_path, "r", encoding="utf-8") as f:
                return json.load(f)
        except UnicodeDecodeError:
            # Try with a different encoding if UTF-8 fails
            try:
                with open(config_path, "r", encoding="latin-1") as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading configuration with latin-1 encoding: {e}")
                return {}
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            return {}

    async def send_email(self,
                        client_name: str,
                        email: str,
                        template_name: str = "new_client",
                        template_vars: Optional[Dict] = None,
                        send_quote: bool = False) -> Dict:
        """
        Send an email to the client using a template.

        Args:
            client_name (str): Client's name
            email (str): Client's email address
            template_name (str): Template name to use
            template_vars (Optional[Dict]): Additional template variables
            send_quote (bool): Whether to send a quote email as well

        Returns:
            Dict: Email sending result
        """
        if not self.gmail_service or not self.gmail_service.is_enabled():
            logger.warning("Gmail service not enabled. Using mock mode.")
            return await self._mock_send_email(client_name, email, template_name, template_vars, send_quote)

        try:
            # Get email template
            email_templates = self.config.get("email_integration", {}).get("email_templates", {})
            if template_name not in email_templates:
                return {"error": f"Unknown email template: {template_name}"}

            template = email_templates[template_name]

            # Prepare template variables
            vars_dict = template_vars or {}
            vars_dict["client_name"] = client_name
            # Extract first name for more personalized communication
            first_name = client_name.split()[0] if client_name else ""
            vars_dict["first_name"] = first_name
            vars_dict["agent_name"] = vars_dict.get("agent_name", "Sandra")
            vars_dict["phone_number"] = vars_dict.get("phone_number", "(*************")

            # Format subject and body
            subject = template["subject"]
            body = template["body"].format(**vars_dict)

            # Send email
            logger.info(f"Sending email to {email}")
            result = await self.gmail_service.send_message(
                to=email,
                subject=subject,
                body=body
            )

            # Send quote email if requested
            if send_quote and "new_client_quote" in email_templates:
                quote_template = email_templates["new_client_quote"]
                quote_subject = quote_template["subject"]

                # Add quote-specific variables
                vars_dict["insurance_type"] = vars_dict.get("insurance_type", "Auto")
                vars_dict["estimated_premium"] = vars_dict.get("estimated_premium", "150")

                quote_body = quote_template["body"].format(**vars_dict)

                # Send quote email
                logger.info(f"Sending quote email to {email}")
                quote_result = await self.gmail_service.send_message(
                    to=email,
                    subject=quote_subject,
                    body=quote_body
                )

                # Combine results
                result["quote_email"] = quote_result

            return result

        except Exception as e:
            logger.exception(f"Error sending email: {e}")
            return {"error": str(e)}

    async def _mock_send_email(self,
                              client_name: str,
                              email: str,
                              template_name: str = "new_client",
                              template_vars: Optional[Dict] = None,
                              send_quote: bool = False) -> Dict:
        """
        Simulate sending an email to the client using a template.

        Args:
            client_name (str): Client's name
            email (str): Client's email address
            template_name (str): Template name to use
            template_vars (Optional[Dict]): Additional template variables
            send_quote (bool): Whether to send a quote email as well

        Returns:
            Dict: Email sending result
        """
        try:
            # Get email template
            email_templates = self.config.get("email_integration", {}).get("email_templates", {})
            if template_name not in email_templates:
                return {"error": f"Unknown email template: {template_name}"}

            template = email_templates[template_name]

            # Prepare template variables
            vars_dict = template_vars or {}
            vars_dict["client_name"] = client_name
            # Extract first name for more personalized communication
            first_name = client_name.split()[0] if client_name else ""
            vars_dict["first_name"] = first_name
            vars_dict["agent_name"] = vars_dict.get("agent_name", "Sandra")
            vars_dict["phone_number"] = vars_dict.get("phone_number", "(*************")

            # Format subject and body
            subject = template["subject"]
            body = template["body"].format(**vars_dict)

            # Log the email that would be sent
            logger.info(f"MOCK EMAIL: Would send email to {email}")
            logger.info(f"MOCK EMAIL: From: <EMAIL>")
            logger.info(f"MOCK EMAIL: Subject: {subject}")
            logger.info(f"MOCK EMAIL: Body: {body[:100]}...")

            result = {
                "status": "success",
                "message_id": f"mock-email-{datetime.now().timestamp()}",
                "to": email,
                "subject": subject,
                "body_preview": body[:100] + "..."
            }

            # Send quote email if requested
            if send_quote and "new_client_quote" in email_templates:
                quote_template = email_templates["new_client_quote"]
                quote_subject = quote_template["subject"]

                # Add quote-specific variables
                vars_dict["insurance_type"] = vars_dict.get("insurance_type", "Auto")
                vars_dict["estimated_premium"] = vars_dict.get("estimated_premium", "150")

                quote_body = quote_template["body"].format(**vars_dict)

                # Log the quote email that would be sent
                logger.info(f"MOCK EMAIL: Would send quote email to {email}")
                logger.info(f"MOCK EMAIL: From: <EMAIL>")
                logger.info(f"MOCK EMAIL: Subject: {quote_subject}")
                logger.info(f"MOCK EMAIL: Body: {quote_body[:100]}...")

                # Combine results
                result["quote_email"] = {
                    "status": "success",
                    "message_id": f"mock-quote-email-{datetime.now().timestamp()}",
                    "to": email,
                    "subject": quote_subject,
                    "body_preview": quote_body[:100] + "..."
                }

            return result

        except Exception as e:
            logger.exception(f"Error sending mock email: {e}")
            return {"error": str(e)}

    async def send_text(self,
                       client_name: str,
                       phone_number: str,
                       template_name: str = "new_client",
                       template_vars: Optional[Dict] = None) -> Dict:
        """
        Simulate sending a text message to the client using a template.

        Args:
            client_name (str): Client's name
            phone_number (str): Client's phone number
            template_name (str): Template name to use
            template_vars (Optional[Dict]): Additional template variables

        Returns:
            Dict: Text message sending result
        """
        try:
            # Get text template
            text_templates = self.config.get("voice_calling_service", {}).get("text_templates", {})
            if template_name not in text_templates:
                return {"error": f"Unknown text template: {template_name}"}

            template = text_templates[template_name]

            # Prepare template variables
            vars_dict = template_vars or {}
            vars_dict["client_name"] = client_name
            # Extract first name for more personalized communication
            first_name = client_name.split()[0] if client_name else ""
            vars_dict["first_name"] = first_name
            vars_dict["agent_name"] = vars_dict.get("agent_name", "Sandra")
            vars_dict["phone_number"] = vars_dict.get("phone_number", "(*************")

            # Format message
            message = template.format(**vars_dict)

            # Log the text message that would be sent
            logger.info(f"MOCK TEXT: Would send text to {phone_number}")
            logger.info(f"MOCK TEXT: From: (*************")
            logger.info(f"MOCK TEXT: Message: {message}")

            return {
                "success": True,
                "message_id": f"mock-text-{datetime.now().timestamp()}",
                "to": phone_number,
                "message": message
            }

        except Exception as e:
            logger.exception(f"Error sending mock text: {e}")
            return {"error": str(e)}

    async def leave_voicemail(self,
                             client_name: str,
                             phone_number: str,
                             template_name: str = "new_client",
                             template_vars: Optional[Dict] = None) -> Dict:
        """
        Simulate leaving a voicemail for the client using a template.

        Args:
            client_name (str): Client's name
            phone_number (str): Client's phone number
            template_name (str): Template name to use
            template_vars (Optional[Dict]): Additional template variables

        Returns:
            Dict: Voicemail result
        """
        try:
            # Get voicemail template
            voicemail_templates = self.config.get("voice_calling_service", {}).get("voicemail_templates", {})
            if template_name not in voicemail_templates:
                return {"error": f"Unknown voicemail template: {template_name}"}

            template = voicemail_templates[template_name]

            # Prepare template variables
            vars_dict = template_vars or {}
            vars_dict["client_name"] = client_name
            # Extract first name for more personalized communication
            first_name = client_name.split()[0] if client_name else ""
            vars_dict["first_name"] = first_name
            vars_dict["agent_name"] = vars_dict.get("agent_name", "Sandra")
            vars_dict["phone_number"] = vars_dict.get("phone_number", "(*************")

            # Format message
            message = template.format(**vars_dict)

            # Log the voicemail that would be left
            logger.info(f"MOCK VOICEMAIL: Would leave voicemail for {phone_number}")
            logger.info(f"MOCK VOICEMAIL: From: (*************")
            logger.info(f"MOCK VOICEMAIL: Using voice: ElevenLabs female voice")
            logger.info(f"MOCK VOICEMAIL: Message: {message}")

            return {
                "success": True,
                "voicemail_id": f"mock-voicemail-{datetime.now().timestamp()}",
                "to": phone_number,
                "message": message
            }

        except Exception as e:
            logger.exception(f"Error leaving mock voicemail: {e}")
            return {"error": str(e)}

    async def full_outreach(self,
                           client_name: str,
                           email: str,
                           phone_number: str,
                           dob: Optional[str] = None,
                           address: Optional[str] = None,
                           insurance_type: Optional[str] = None,
                           estimated_premium: Optional[str] = None,
                           agent_name: str = "Sandra",
                           notes: Optional[str] = None,
                           send_quote: bool = True) -> Dict:
        """
        Perform full outreach to a new client (email, text, voicemail).

        Args:
            client_name (str): Client's name
            email (str): Client's email address
            phone_number (str): Client's phone number
            dob (Optional[str]): Client's date of birth
            address (Optional[str]): Client's address
            insurance_type (Optional[str]): Type of insurance
            estimated_premium (Optional[str]): Estimated premium amount
            agent_name (str): Agent's name
            notes (Optional[str]): Additional notes about the client's needs
            send_quote (bool): Whether to send a quote email

        Returns:
            Dict: Results of all outreach methods
        """
        # Prepare template variables
        template_vars = {
            "agent_name": agent_name,
            "phone_number": "(*************",
            "dob": dob or "",
            "address": address or "",
            "insurance_type": insurance_type or "Auto",
            "estimated_premium": estimated_premium or "150",
            "notes": notes or ""
        }

        # Send email
        email_result = await self.send_email(
            client_name=client_name,
            email=email,
            template_name="new_client",
            template_vars=template_vars,
            send_quote=send_quote
        )

        # Send text message
        text_result = await self.send_text(
            client_name=client_name,
            phone_number=phone_number,
            template_name="new_client",
            template_vars=template_vars
        )

        # Leave voicemail
        voicemail_result = await self.leave_voicemail(
            client_name=client_name,
            phone_number=phone_number,
            template_name="new_client",
            template_vars=template_vars
        )

        # Log results
        logger.info(f"Full outreach completed for {client_name}")

        # Return combined results
        return {
            "email": email_result,
            "text": text_result,
            "voicemail": voicemail_result,
            "timestamp": datetime.now().isoformat()
        }

async def main():
    """Main function to run the script."""
    parser = argparse.ArgumentParser(description="Client Outreach Script")
    parser.add_argument("--name", required=True, help="Client's full name")
    parser.add_argument("--email", required=True, help="Client's email address")
    parser.add_argument("--phone", required=True, help="Client's phone number")
    parser.add_argument("--dob", help="Client's date of birth (MM/DD/YY)")
    parser.add_argument("--address", help="Client's address")
    parser.add_argument("--insurance-type", help="Type of insurance")
    parser.add_argument("--premium", help="Estimated premium amount")
    parser.add_argument("--agent", default="Sandra", help="Agent's name")
    parser.add_argument("--notes", help="Additional notes about the client's needs")
    parser.add_argument("--quote", action="store_true", help="Send quote email")
    parser.add_argument("--mock", action="store_true", help="Use mock mode (don't actually send emails/texts)")

    args = parser.parse_args()

    # Initialize outreach service
    outreach = ClientOutreach()

    # Initialize services if not in mock mode
    if not args.mock:
        await outreach.initialize()

    # Perform full outreach
    result = await outreach.full_outreach(
        client_name=args.name,
        email=args.email,
        phone_number=args.phone,
        dob=args.dob,
        address=args.address,
        insurance_type=args.insurance_type,
        estimated_premium=args.premium,
        agent_name=args.agent,
        notes=args.notes,
        send_quote=args.quote
    )

    # Print results
    print(json.dumps(result, indent=2))

if __name__ == "__main__":
    asyncio.run(main())
