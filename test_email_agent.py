"""
Test script for the Email Agent.
This script tests the Email Agent's ability to read, analyze, and respond to emails.
"""
import os
import sys
import json
import asyncio
import argparse
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime

from agents.email_agent import EmailAgent
from agents.multi_account_email_agent import MultiAccountEmailAgent
from llm.llm_router import LLMRouter
from core.logger import setup_logger
from core.state_manager import StateManager
from core.advanced_memory import AdvancedMemory

# Set up logger
logger = setup_logger("test_email_agent")

async def test_read_emails(email_agent: EmailAgent, query: Optional[str] = None, max_results: int = 5):
    """Test reading emails."""
    print("\n=== Testing Read Emails ===")
    print(f"Query: {query or 'None'}")
    print(f"Max Results: {max_results}")

    result = await email_agent.read_emails(query, max_results)

    if "error" in result:
        print(f"Error: {result['error']}")
        return

    print(f"Found {len(result['messages'])} emails")

    if "summary" in result:
        print("\nSummary:")
        print(result["summary"])

    if result["messages"]:
        print("\nFirst few emails:")
        for i, message in enumerate(result["messages"][:3]):
            print(f"\nEmail {i+1}:")
            print(f"From: {message['from']}")
            print(f"Subject: {message['subject']}")
            print(f"Date: {message['date']}")
            print(f"Snippet: {message['snippet']}")

    return result["messages"] if "messages" in result else []

async def test_analyze_email(email_agent: EmailAgent, email_id: str):
    """Test analyzing an email."""
    print("\n=== Testing Analyze Email ===")
    print(f"Email ID: {email_id}")

    result = await email_agent.analyze_email(email_id)

    if "error" in result:
        print(f"Error: {result['error']}")
        return

    print(f"Email details:")
    print(f"From: {result['from']}")
    print(f"Subject: {result['subject']}")
    print(f"Date: {result['date']}")

    if "analysis" in result:
        print("\nAnalysis:")
        print(result["analysis"])

    return result

async def test_draft_response(email_agent: EmailAgent, email_id: str,
                             response_type: str = "professional",
                             include_reasoning: bool = True):
    """Test drafting a response to an email."""
    print("\n=== Testing Draft Response ===")
    print(f"Email ID: {email_id}")
    print(f"Response Type: {response_type}")
    print(f"Include Reasoning: {include_reasoning}")

    result = await email_agent.draft_response(email_id, response_type, include_reasoning)

    if "error" in result:
        print(f"Error: {result['error']}")
        return

    print(f"\nDraft response to email from: {result['original_email']['from']}")
    print(f"Subject: {result['original_email']['subject']}")

    print("\nDraft Response:")
    print(result["draft_response"])

    if include_reasoning and result["reasoning"]:
        print("\nReasoning:")
        print(result["reasoning"])

    return result

async def test_send_email(email_agent: EmailAgent, to: str, subject: str, body: str):
    """Test sending an email."""
    print("\n=== Testing Send Email ===")
    print(f"To: {to}")
    print(f"Subject: {subject}")

    # Confirm before sending
    confirm = input("\nDo you want to send this email? (y/n): ").lower()
    if confirm != 'y':
        print("Email sending cancelled.")
        return

    result = await email_agent.send_email(to, subject, body)

    if "error" in result:
        print(f"Error: {result['error']}")
        return

    print(f"Email sent successfully!")
    print(f"Message ID: {result['message_id']}")

    return result

async def run_interactive_test(email_agent: EmailAgent):
    """Run an interactive test of the Email Agent."""
    print("\n=== Interactive Email Agent Test ===")
    print("This test will guide you through testing the Email Agent's capabilities.")

    # Test reading emails
    query = input("\nEnter a search query for emails (or leave blank for all): ")
    max_results = int(input("Enter maximum number of emails to retrieve (default 5): ") or "5")

    messages = await test_read_emails(email_agent, query if query else None, max_results)

    if not messages:
        print("No emails found to analyze or respond to.")
        return

    # Select an email to analyze
    print("\nSelect an email to analyze:")
    for i, message in enumerate(messages[:min(5, len(messages))]):
        print(f"{i+1}. From: {message['from']} - Subject: {message['subject']}")

    selection = int(input("\nEnter the number of the email to analyze (1-5): ") or "1") - 1
    if selection < 0 or selection >= len(messages):
        print("Invalid selection.")
        return

    selected_email = messages[selection]

    # Analyze the selected email
    analysis_result = await test_analyze_email(email_agent, selected_email['id'])

    if not analysis_result:
        print("Failed to analyze email.")
        return

    # Draft a response
    response_type = input("\nEnter response type (formal, casual, sales, support, professional): ") or "professional"
    include_reasoning = input("Include reasoning? (y/n): ").lower() == 'y'

    draft_result = await test_draft_response(email_agent, selected_email['id'], response_type, include_reasoning)

    if not draft_result:
        print("Failed to draft response.")
        return

    # Optionally send the email
    send_email = input("\nDo you want to send this email? (y/n): ").lower() == 'y'

    if send_email:
        to = input(f"Enter recipient email (default: {analysis_result['from']}): ") or analysis_result['from']
        subject = input(f"Enter subject (default: Re: {analysis_result['subject']}): ") or f"Re: {analysis_result['subject']}"
        body = draft_result["draft_response"]

        print("\nEmail Body:")
        print(body)

        await test_send_email(email_agent, to, subject, body)

class MockLLMRouter:
    """Mock LLM router for testing."""

    def __init__(self):
        self.logger = logger

    async def generate_text(self, prompt, **kwargs):
        """Generate mock text."""
        self.logger.info(f"Generating text for prompt: {prompt[:100]}...")
        return {
            "text": f"Mock response for: {prompt[:50]}...",
            "model": "mock-model",
            "tokens": len(prompt) // 4
        }

class MockMemory:
    """Mock memory for testing."""

    def __init__(self):
        self.memories = []
        self.logger = logger

    async def add_memory(self, content, memory_type="episodic", source="test", importance=0.5):
        """Add a mock memory."""
        memory = {
            "id": str(uuid.uuid4()),
            "content": content,
            "type": memory_type,
            "source": source,
            "importance": importance,
            "timestamp": datetime.now().isoformat()
        }
        self.memories.append(memory)
        self.logger.info(f"Added memory: {memory['id']}")
        return memory

async def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Test the Email Agent")
    parser.add_argument("--interactive", action="store_true", help="Run an interactive test")
    parser.add_argument("--read", action="store_true", help="Test reading emails")
    parser.add_argument("--analyze", help="Test analyzing an email (provide email ID)")
    parser.add_argument("--draft", help="Test drafting a response (provide email ID)")
    parser.add_argument("--send", action="store_true", help="Test sending an email")
    parser.add_argument("--to", help="Recipient email address for send test", default="<EMAIL>")
    parser.add_argument("--subject", help="Email subject for send test", default="Test Email from AI Agent System")
    parser.add_argument("--body", help="Email body for send test", default="This is a test email sent from the AI Agent System.")
    parser.add_argument("--query", help="Search query for read test")
    parser.add_argument("--max-results", type=int, default=5, help="Maximum results for read test")
    parser.add_argument("--multi-account", action="store_true", help="Test multi-account email agent")

    args = parser.parse_args()

    # Create state manager
    state_manager = StateManager()
    await state_manager.initialize()

    # Create message queue
    message_queue = asyncio.Queue()

    # Create mock LLM router
    llm_router = MockLLMRouter()

    # Create mock memory
    memory = MockMemory()

    # Create Email Agent
    email_agent = EmailAgent(
        agent_id="email_agent",
        name="Email Agent",
        description="Handles email communications with reasoning capabilities",
        config={},
        message_queue=message_queue,
        state_manager=state_manager
    )

    # Set services
    email_agent.register_service("llm_router", llm_router)
    email_agent.register_service("memory", memory)

    # Initialize email agent
    await email_agent.initialize()

    # Create Multi-Account Email Agent if needed
    multi_account_email_agent = None
    if args.multi_account:
        multi_account_email_agent = MultiAccountEmailAgent(
            agent_id="multi_account_email_agent",
            name="Multi-Account Email Agent",
            description="Handles email communications across multiple accounts with reasoning capabilities",
            config={},
            message_queue=message_queue,
            state_manager=state_manager
        )

        # Set services
        multi_account_email_agent.register_service("llm_router", llm_router)
        multi_account_email_agent.register_service("memory", memory)

        # Initialize multi-account email agent
        await multi_account_email_agent.initialize()

    # Run tests
    if args.interactive or not (args.read or args.analyze or args.draft or args.send):
        await run_interactive_test(email_agent)
    else:
        if args.read:
            await test_read_emails(email_agent, args.query, args.max_results)

        if args.analyze:
            await test_analyze_email(email_agent, args.analyze)

        if args.draft:
            response_type = input("Enter response type (formal, casual, sales, support, professional): ") or "professional"
            include_reasoning = input("Include reasoning? (y/n): ").lower() == 'y'
            await test_draft_response(email_agent, args.draft, response_type, include_reasoning)

        if args.send:
            if args.multi_account and multi_account_email_agent:
                print(f"\n=== Testing Send Email (Multi-Account) ===")
                print(f"Account: <EMAIL>")
                print(f"To: {args.to}")
                print(f"Subject: {args.subject}")

                # Confirm before sending
                confirm = input("\nDo you want to send this email? (y/n): ").lower()
                if confirm != 'y':
                    print("Email sending cancelled.")
                else:
                    result = await multi_account_email_agent.send_email(
                        account_email="<EMAIL>",
                        to=args.to,
                        subject=args.subject,
                        body=args.body
                    )

                    if "error" in result:
                        print(f"Error: {result['error']}")
                    else:
                        print(f"Email sent successfully!")
            else:
                await test_send_email(email_agent, args.to, args.subject, args.body)

    # Shutdown agents
    await email_agent.shutdown()
    if multi_account_email_agent:
        await multi_account_email_agent.shutdown()

if __name__ == "__main__":
    asyncio.run(main())
