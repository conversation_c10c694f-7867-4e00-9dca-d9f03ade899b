"""
UI-TARS Screenshot Analyzer

This script uses UI-TARS to analyze a screenshot and provide a description.
"""
import os
import sys
import time
import asyncio
import logging
import argparse
import requests
from typing import Dict, Optional, Any

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ui_tars_screenshot_analyzer")

class UITarsScreenshotAnalyzer:
    """Class to analyze screenshots using UI-TARS."""
    
    def __init__(self, 
                 api_url: str = "http://127.0.0.1:8000",
                 api_key: str = "dummy_key",
                 model_name: str = "UI-TARS-1.5-7B"):
        """
        Initialize the UI-TARS Screenshot Analyzer.
        
        Args:
            api_url (str): URL of the UI-TARS API
            api_key (str): API key for UI-TARS
            model_name (str): Name of the model to use
        """
        self.api_url = api_url
        self.api_key = api_key
        self.model_name = model_name
        self.session = None
    
    async def initialize(self) -> bool:
        """
        Initialize the screenshot analyzer.
        
        Returns:
            bool: True if initialization was successful, False otherwise
        """
        logger.info("Initializing UI-TARS Screenshot Analyzer")
        
        # Create a session for API requests
        self.session = requests.Session()
        
        # Set up headers
        if self.api_key:
            self.session.headers.update({"Authorization": f"Bearer {self.api_key}"})
        
        # Check if UI-TARS API is running
        try:
            # Try to access the API
            response = self.session.get(f"{self.api_url}/health", timeout=5)
            
            if response.status_code == 200:
                logger.info("UI-TARS API is running")
                return True
            else:
                logger.error(f"UI-TARS API returned status code {response.status_code}")
                return False
        
        except requests.exceptions.RequestException as e:
            logger.error(f"Error connecting to UI-TARS API: {e}")
            
            # Try alternative endpoint
            try:
                response = self.session.get(f"{self.api_url}/v1/models", timeout=5)
                
                if response.status_code == 200:
                    logger.info("UI-TARS API is running (alternative endpoint)")
                    return True
                else:
                    logger.error(f"UI-TARS API returned status code {response.status_code}")
                    return False
            
            except requests.exceptions.RequestException as e2:
                logger.error(f"Error connecting to UI-TARS API (alternative endpoint): {e2}")
                return False
    
    async def analyze_screenshot(self, 
                               screenshot_path: str,
                               prompt: str = "What do you see in this screenshot? Describe all UI elements and their functions.") -> Dict[str, Any]:
        """
        Analyze a screenshot using UI-TARS.
        
        Args:
            screenshot_path (str): Path to the screenshot
            prompt (str): Prompt for UI-TARS
            
        Returns:
            Dict[str, Any]: Analysis results
        """
        logger.info(f"Analyzing screenshot: {screenshot_path}")
        
        if not os.path.exists(screenshot_path):
            logger.error(f"Screenshot not found: {screenshot_path}")
            return {"success": False, "error": f"Screenshot not found: {screenshot_path}"}
        
        try:
            # Prepare the request
            files = {
                "file": (os.path.basename(screenshot_path), open(screenshot_path, "rb"), "image/png")
            }
            
            data = {
                "model": self.model_name,
                "prompt": prompt,
                "max_tokens": 500
            }
            
            # Send the request
            response = self.session.post(f"{self.api_url}/v1/vision", files=files, data=data, timeout=60)
            
            if response.status_code == 200:
                logger.info("Screenshot analysis successful")
                return {"success": True, "analysis": response.json()}
            else:
                logger.error(f"Screenshot analysis failed with status code {response.status_code}")
                return {"success": False, "error": f"Status code: {response.status_code}"}
        
        except requests.exceptions.RequestException as e:
            logger.error(f"Error analyzing screenshot: {e}")
            return {"success": False, "error": str(e)}
        except Exception as e:
            logger.error(f"Error processing screenshot: {e}")
            return {"success": False, "error": str(e)}
    
    async def take_and_analyze_screenshot(self, 
                                        prompt: str = "What do you see in this screenshot? Describe all UI elements and their functions.") -> Dict[str, Any]:
        """
        Take a screenshot and analyze it using UI-TARS.
        
        Args:
            prompt (str): Prompt for UI-TARS
            
        Returns:
            Dict[str, Any]: Analysis results
        """
        logger.info("Taking and analyzing screenshot")
        
        try:
            # Take a screenshot
            screenshot_path = "current_screen.png"
            
            # Use different screenshot methods based on OS
            if sys.platform == "win32":
                # Windows
                import pyautogui
                screenshot = pyautogui.screenshot()
                screenshot.save(screenshot_path)
            elif sys.platform == "darwin":
                # macOS
                os.system(f"screencapture -x {screenshot_path}")
            else:
                # Linux
                os.system(f"import -window root {screenshot_path}")
            
            logger.info(f"Screenshot saved to {screenshot_path}")
            
            # Analyze the screenshot
            return await self.analyze_screenshot(screenshot_path, prompt)
        
        except Exception as e:
            logger.error(f"Error taking and analyzing screenshot: {e}")
            return {"success": False, "error": str(e)}
    
    async def shutdown(self) -> None:
        """Shut down the screenshot analyzer."""
        if self.session:
            self.session.close()
        logger.info("UI-TARS Screenshot Analyzer shut down")

async def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="UI-TARS Screenshot Analyzer")
    parser.add_argument("--api-url", type=str, default="http://127.0.0.1:8000", help="URL of the UI-TARS API")
    parser.add_argument("--api-key", type=str, default="dummy_key", help="API key for UI-TARS")
    parser.add_argument("--model", type=str, default="UI-TARS-1.5-7B", help="Name of the model to use")
    parser.add_argument("--screenshot", type=str, help="Path to a screenshot to analyze")
    parser.add_argument("--prompt", type=str, default="What do you see in this screenshot? Describe all UI elements and their functions.", help="Prompt for UI-TARS")
    parser.add_argument("--take-screenshot", action="store_true", help="Take a screenshot and analyze it")
    
    args = parser.parse_args()
    
    # Create UI-TARS Screenshot Analyzer
    analyzer = UITarsScreenshotAnalyzer(
        api_url=args.api_url,
        api_key=args.api_key,
        model_name=args.model
    )
    
    # Initialize
    initialized = await analyzer.initialize()
    if not initialized:
        logger.error("Failed to initialize UI-TARS Screenshot Analyzer")
        return
    
    try:
        # Analyze screenshot
        if args.take_screenshot:
            result = await analyzer.take_and_analyze_screenshot(args.prompt)
        elif args.screenshot:
            result = await analyzer.analyze_screenshot(args.screenshot, args.prompt)
        else:
            logger.error("No screenshot provided. Use --screenshot or --take-screenshot.")
            return
        
        if result["success"]:
            logger.info("Screenshot analysis successful")
            print("\nAnalysis Results:")
            print("=================")
            print(result["analysis"]["choices"][0]["text"])
        else:
            logger.error(f"Screenshot analysis failed: {result['error']}")
    
    finally:
        # Shut down
        await analyzer.shutdown()

if __name__ == "__main__":
    asyncio.run(main())
