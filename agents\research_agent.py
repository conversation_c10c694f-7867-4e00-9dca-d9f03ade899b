"""
Research Agent for web scraping and information gathering.
"""
import asyncio
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any
import json
import re
import uuid
import hashlib
import os

from agents.base_agent import BaseAgent
from core.logger import setup_logger
from llm.llm_router import <PERSON><PERSON>outer
from utils.web_scraper import WebScraper
from services.github_service import GitHubServiceFactory
from services.huggingface_service import HuggingFaceServiceFactory

# Import GitHub and Hugging Face method implementations
from agents.research_agent_github import (
    _handle_github_search as github_search_impl,
    _handle_github_repository as github_repository_impl,
    _handle_github_code_analysis as github_code_analysis_impl
)
from agents.research_agent_huggingface import (
    _handle_huggingface_search as huggingface_search_impl,
    _handle_huggingface_model_analysis as huggingface_model_analysis_impl,
    _handle_technical_documentation as technical_documentation_impl
)

class ResearchAgent(BaseAgent):
    """
    Agent specialized for web scraping and information gathering.

    This agent handles tasks related to retrieving information from the web,
    including searching, scraping, and summarizing content.
    """

    def __init__(
        self,
        agent_id: str,
        config: Dict,
        state_manager,
        message_queue,
        shutdown_event
    ):
        """Initialize the research agent."""
        super().__init__(agent_id, config, state_manager, message_queue, shutdown_event)

        # Research-specific configuration
        self.llm_provider = config.get("llm_provider", "anthropic")
        self.llm_router = None

        # Web scraper
        self.web_scraper = None

        # GitHub and Hugging Face services
        self.github_service = None
        self.huggingface_service = None

        # Research data
        self.search_history = {}
        self.knowledge_base = {}
        self.monitoring_topics = {}
        self.github_repositories = {}
        self.huggingface_models = {}

        # Agent capabilities
        self.capabilities = [
            "web_search",
            "content_scraping",
            "information_summarization",
            "topic_monitoring",
            "knowledge_management",
            "github_research",
            "code_analysis",
            "model_research",
            "technical_documentation",
        ]

    async def initialize(self):
        """Initialize the research agent."""
        await super().initialize()

        # Initialize LLM router
        self.llm_router = LLMRouter()
        await self.llm_router.initialize()

        # Initialize web scraper
        self.web_scraper = WebScraper()
        await self.web_scraper.initialize()

        # Initialize GitHub service
        self.github_service = GitHubServiceFactory.create_service()
        if self.github_service and self.github_service.is_enabled():
            self.logger.info("GitHub service initialized")
        else:
            self.logger.warning("GitHub service not available or not enabled")

        # Initialize Hugging Face service
        self.huggingface_service = HuggingFaceServiceFactory.create_service()
        if self.huggingface_service and self.huggingface_service.is_enabled():
            self.logger.info("Hugging Face service initialized")
        else:
            self.logger.warning("Hugging Face service not available or not enabled")

        # Load research data
        await self._load_research_data()

        self.logger.info(f"Research agent initialized with provider: {self.llm_provider}")

    async def _load_research_data(self):
        """Load research data from state manager."""
        # Load search history
        search_history_data = await self.state_manager.get_state("research", "search_history")
        if search_history_data:
            self.search_history = search_history_data
            self.logger.info(f"Loaded search history with {len(self.search_history)} entries")

        # Load knowledge base
        knowledge_base_data = await self.state_manager.get_state("research", "knowledge_base")
        if knowledge_base_data:
            self.knowledge_base = knowledge_base_data
            self.logger.info(f"Loaded knowledge base with {len(self.knowledge_base)} entries")

        # Load monitoring topics
        monitoring_topics_data = await self.state_manager.get_state("research", "monitoring_topics")
        if monitoring_topics_data:
            self.monitoring_topics = monitoring_topics_data
            self.logger.info(f"Loaded {len(self.monitoring_topics)} monitoring topics")

        # Load GitHub repositories
        github_repositories_data = await self.state_manager.get_state("research", "github_repositories")
        if github_repositories_data:
            self.github_repositories = github_repositories_data
            self.logger.info(f"Loaded {len(self.github_repositories)} GitHub repositories")

        # Load Hugging Face models
        huggingface_models_data = await self.state_manager.get_state("research", "huggingface_models")
        if huggingface_models_data:
            self.huggingface_models = huggingface_models_data
            self.logger.info(f"Loaded {len(self.huggingface_models)} Hugging Face models")

    async def execute_cycle(self):
        """Execute one cycle of the research agent's logic."""
        self.logger.debug("Executing research agent cycle")

        try:
            # Check for pending tasks
            pending_tasks = await self.state_manager.get_state("research", "pending_tasks")
            if pending_tasks:
                for task_id, task in pending_tasks.items():
                    if task.get("status") == "pending":
                        await self._process_task(task_id, task)

            # Check monitoring topics
            await self._check_monitoring_topics()

            # Update state with any changes
            await self._save_research_data()

        except Exception as e:
            self.logger.exception(f"Error in research agent cycle: {e}")

    async def _process_task(self, task_id: str, task: Dict):
        """
        Process a pending task.

        Args:
            task_id (str): Task identifier
            task (Dict): Task data
        """
        task_type = task.get("type")
        self.logger.info(f"Processing task: {task_id} ({task_type})")

        try:
            if task_type == "web_search":
                await self._handle_web_search(task)
            elif task_type == "content_scraping":
                await self._handle_content_scraping(task)
            elif task_type == "information_summarization":
                await self._handle_information_summarization(task)
            elif task_type == "topic_monitoring":
                await self._handle_topic_monitoring(task)
            elif task_type == "github_search":
                await self._handle_github_search(task)
            elif task_type == "github_repository":
                await self._handle_github_repository(task)
            elif task_type == "github_code_analysis":
                await self._handle_github_code_analysis(task)
            elif task_type == "huggingface_search":
                await self._handle_huggingface_search(task)
            elif task_type == "huggingface_model_analysis":
                await self._handle_huggingface_model_analysis(task)
            elif task_type == "technical_documentation":
                await self._handle_technical_documentation(task)
            else:
                self.logger.warning(f"Unknown task type: {task_type}")
                return

            # Update task status
            task["status"] = "completed"
            task["completed_at"] = datetime.now().isoformat()

            # Update pending tasks
            pending_tasks = await self.state_manager.get_state("research", "pending_tasks") or {}
            pending_tasks[task_id] = task
            await self.state_manager.update_state("research", "pending_tasks", pending_tasks)

        except Exception as e:
            self.logger.exception(f"Error processing task {task_id}: {e}")

            # Update task status
            task["status"] = "error"
            task["error"] = str(e)

            # Update pending tasks
            pending_tasks = await self.state_manager.get_state("research", "pending_tasks") or {}
            pending_tasks[task_id] = task
            await self.state_manager.update_state("research", "pending_tasks", pending_tasks)

    async def _handle_web_search(self, task: Dict):
        """
        Handle a web search task.

        Args:
            task (Dict): Task data
        """
        query = task.get("query")
        max_results = task.get("max_results", 5)

        # In a real implementation, this would use a search API
        # For now, we'll simulate search results
        search_results = [
            {
                "title": f"Result {i+1} for {query}",
                "url": f"https://example.com/result{i+1}",
                "snippet": f"This is a snippet for result {i+1} related to {query}...",
            }
            for i in range(max_results)
        ]

        # Store search results in task
        task["results"] = search_results

        # Add to search history
        search_id = str(uuid.uuid4())
        self.search_history[search_id] = {
            "id": search_id,
            "query": query,
            "timestamp": datetime.now().isoformat(),
            "results": search_results,
        }

        # If requested, scrape the top results
        if task.get("scrape_results", False):
            for result in search_results[:2]:  # Limit to top 2 for efficiency
                scrape_task_id = f"TASK-SCRAPE-{datetime.now().strftime('%Y%m%d%H%M%S')}"

                scrape_task = {
                    "task_id": scrape_task_id,
                    "type": "content_scraping",
                    "url": result["url"],
                    "parent_task_id": task.get("task_id"),
                    "created_at": datetime.now().isoformat(),
                    "status": "pending",
                }

                # Add task to pending tasks
                pending_tasks = await self.state_manager.get_state("research", "pending_tasks") or {}
                pending_tasks[scrape_task_id] = scrape_task
                await self.state_manager.update_state("research", "pending_tasks", pending_tasks)

    async def _handle_content_scraping(self, task: Dict):
        """
        Handle a content scraping task.

        Args:
            task (Dict): Task data
        """
        url = task.get("url")

        # Scrape the URL
        article_data = await self.web_scraper.scrape_article(url)

        if not article_data:
            raise ValueError(f"Failed to scrape URL: {url}")

        # Store scraped content in task
        task["content"] = article_data

        # Add to knowledge base
        content_hash = hashlib.md5(url.encode()).hexdigest()
        self.knowledge_base[content_hash] = {
            "id": content_hash,
            "url": url,
            "title": article_data.get("title", ""),
            "content": article_data.get("text", ""),
            "metadata": article_data.get("metadata", {}),
            "scraped_at": datetime.now().isoformat(),
        }

        # If this is part of a parent task, update the parent
        parent_task_id = task.get("parent_task_id")
        if parent_task_id:
            pending_tasks = await self.state_manager.get_state("research", "pending_tasks") or {}
            if parent_task_id in pending_tasks:
                parent_task = pending_tasks[parent_task_id]
                if "scraped_content" not in parent_task:
                    parent_task["scraped_content"] = []

                parent_task["scraped_content"].append({
                    "url": url,
                    "title": article_data.get("title", ""),
                    "content_hash": content_hash,
                })

                pending_tasks[parent_task_id] = parent_task
                await self.state_manager.update_state("research", "pending_tasks", pending_tasks)

    async def _handle_information_summarization(self, task: Dict):
        """
        Handle an information summarization task.

        Args:
            task (Dict): Task data
        """
        content_hashes = task.get("content_hashes", [])
        topic = task.get("topic", "")

        # Collect content to summarize
        contents = []
        for content_hash in content_hashes:
            if content_hash in self.knowledge_base:
                entry = self.knowledge_base[content_hash]
                contents.append({
                    "title": entry.get("title", ""),
                    "content": entry.get("content", ""),
                    "url": entry.get("url", ""),
                })

        if not contents:
            raise ValueError("No content found to summarize")

        # Generate summary using LLM
        prompt = f"""
        You are a research assistant. Please summarize the following information about "{topic}":

        {json.dumps(contents, indent=2)}

        Provide a comprehensive summary that includes:
        1. Key points from all sources
        2. Any conflicting information
        3. Main conclusions
        4. Areas that need further research

        Keep your summary factual and well-organized.
        """

        response = await self.llm_router.generate_text(
            prompt=prompt,
            provider=self.llm_provider,
            max_tokens=1000,
            temperature=0.7
        )

        # Store summary in task
        task["summary"] = response.get("text")

        # Add to knowledge base as a new entry
        summary_hash = hashlib.md5(f"{topic}-summary-{datetime.now().isoformat()}".encode()).hexdigest()
        self.knowledge_base[summary_hash] = {
            "id": summary_hash,
            "type": "summary",
            "topic": topic,
            "content": response.get("text"),
            "sources": content_hashes,
            "created_at": datetime.now().isoformat(),
        }

        # If this is part of a parent task, update the parent
        parent_task_id = task.get("parent_task_id")
        if parent_task_id:
            pending_tasks = await self.state_manager.get_state("research", "pending_tasks") or {}
            if parent_task_id in pending_tasks:
                parent_task = pending_tasks[parent_task_id]
                parent_task["summary_hash"] = summary_hash
                pending_tasks[parent_task_id] = parent_task
                await self.state_manager.update_state("research", "pending_tasks", pending_tasks)

    async def _handle_topic_monitoring(self, task: Dict):
        """
        Handle a topic monitoring task.

        Args:
            task (Dict): Task data
        """
        topic = task.get("topic")
        keywords = task.get("keywords", [])
        sources = task.get("sources", [])
        frequency = task.get("frequency", "daily")  # daily, hourly, etc.

        # Create or update monitoring topic
        topic_id = hashlib.md5(topic.encode()).hexdigest()

        self.monitoring_topics[topic_id] = {
            "id": topic_id,
            "topic": topic,
            "keywords": keywords,
            "sources": sources,
            "frequency": frequency,
            "created_at": datetime.now().isoformat(),
            "last_checked": None,
            "status": "active",
        }

        # Store topic ID in task
        task["topic_id"] = topic_id

        # Perform initial check
        await self._check_topic(topic_id)

    async def _check_monitoring_topics(self):
        """Check all active monitoring topics for updates."""
        current_time = datetime.now()

        for topic_id, topic in self.monitoring_topics.items():
            if topic.get("status") != "active":
                continue

            # Check if it's time to check this topic
            last_checked = None
            if topic.get("last_checked"):
                last_checked = datetime.fromisoformat(topic["last_checked"])

            should_check = False
            if not last_checked:
                should_check = True
            elif topic["frequency"] == "hourly" and (current_time - last_checked) >= timedelta(hours=1):
                should_check = True
            elif topic["frequency"] == "daily" and (current_time - last_checked) >= timedelta(days=1):
                should_check = True
            elif topic["frequency"] == "weekly" and (current_time - last_checked) >= timedelta(weeks=1):
                should_check = True

            if should_check:
                await self._check_topic(topic_id)

    async def _check_topic(self, topic_id: str):
        """
        Check a monitoring topic for updates.

        Args:
            topic_id (str): Topic identifier
        """
        topic = self.monitoring_topics.get(topic_id)
        if not topic:
            return

        self.logger.info(f"Checking monitoring topic: {topic['topic']}")

        # Update last checked timestamp
        topic["last_checked"] = datetime.now().isoformat()
        self.monitoring_topics[topic_id] = topic

        # Create search task for each keyword
        for keyword in topic["keywords"]:
            search_query = f"{topic['topic']} {keyword}"

            task_id = f"TASK-SEARCH-{datetime.now().strftime('%Y%m%d%H%M%S')}"

            task = {
                "task_id": task_id,
                "type": "web_search",
                "query": search_query,
                "max_results": 3,
                "scrape_results": True,
                "topic_id": topic_id,
                "created_at": datetime.now().isoformat(),
                "status": "pending",
            }

            # Add task to pending tasks
            pending_tasks = await self.state_manager.get_state("research", "pending_tasks") or {}
            pending_tasks[task_id] = task
            await self.state_manager.update_state("research", "pending_tasks", pending_tasks)

    async def _save_research_data(self):
        """Save research data to state manager."""
        # Save search history
        await self.state_manager.update_state("research", "search_history", self.search_history)

        # Save knowledge base
        await self.state_manager.update_state("research", "knowledge_base", self.knowledge_base)

        # Save monitoring topics
        await self.state_manager.update_state("research", "monitoring_topics", self.monitoring_topics)

        # Save GitHub repositories
        await self.state_manager.update_state("research", "github_repositories", self.github_repositories)

        # Save Hugging Face models
        await self.state_manager.update_state("research", "huggingface_models", self.huggingface_models)

    async def handle_query(self, message: Dict):
        """
        Handle a query message.

        Args:
            message (Dict): Query message
        """
        query = message.get("content", {}).get("query")
        query_type = message.get("content", {}).get("type")

        if query_type == "search":
            # Create a web search task
            task_id = f"TASK-SEARCH-{datetime.now().strftime('%Y%m%d%H%M%S')}"

            task = {
                "task_id": task_id,
                "type": "web_search",
                "query": query,
                "max_results": message.get("content", {}).get("max_results", 5),
                "scrape_results": message.get("content", {}).get("scrape_results", False),
                "created_at": datetime.now().isoformat(),
                "status": "pending",
                "requester_id": message.get("sender_id"),
            }

            # Add task to pending tasks
            pending_tasks = await self.state_manager.get_state("research", "pending_tasks") or {}
            pending_tasks[task_id] = task
            await self.state_manager.update_state("research", "pending_tasks", pending_tasks)

            # Acknowledge receipt
            await self.send_message(
                message.get("sender_id"),
                "acknowledgement",
                {
                    "query": query,
                    "task_id": task_id,
                    "status": "processing",
                }
            )

        elif query_type == "github_search":
            # Create a GitHub search task
            task_id = f"TASK-GITHUB-{datetime.now().strftime('%Y%m%d%H%M%S')}"

            task = {
                "task_id": task_id,
                "type": "github_search",
                "query": query,
                "language": message.get("content", {}).get("language"),
                "search_type": message.get("content", {}).get("search_type", "code"),
                "analyze_results": message.get("content", {}).get("analyze_results", True),
                "created_at": datetime.now().isoformat(),
                "status": "pending",
                "requester_id": message.get("sender_id"),
            }

            # Add task to pending tasks
            pending_tasks = await self.state_manager.get_state("research", "pending_tasks") or {}
            pending_tasks[task_id] = task
            await self.state_manager.update_state("research", "pending_tasks", pending_tasks)

            # Acknowledge receipt
            await self.send_message(
                message.get("sender_id"),
                "acknowledgement",
                {
                    "query": query,
                    "task_id": task_id,
                    "status": "processing",
                }
            )

        elif query_type == "huggingface_search":
            # Create a Hugging Face search task
            task_id = f"TASK-HF-{datetime.now().strftime('%Y%m%d%H%M%S')}"

            task = {
                "task_id": task_id,
                "type": "huggingface_search",
                "query": query,
                "task_type": message.get("content", {}).get("task_type"),
                "library": message.get("content", {}).get("library"),
                "search_type": message.get("content", {}).get("search_type", "models"),
                "analyze_results": message.get("content", {}).get("analyze_results", True),
                "created_at": datetime.now().isoformat(),
                "status": "pending",
                "requester_id": message.get("sender_id"),
            }

            # Add task to pending tasks
            pending_tasks = await self.state_manager.get_state("research", "pending_tasks") or {}
            pending_tasks[task_id] = task
            await self.state_manager.update_state("research", "pending_tasks", pending_tasks)

            # Acknowledge receipt
            await self.send_message(
                message.get("sender_id"),
                "acknowledgement",
                {
                    "query": query,
                    "task_id": task_id,
                    "status": "processing",
                }
            )

        elif query_type == "technical_documentation":
            # Create a technical documentation task
            task_id = f"TASK-DOC-{datetime.now().strftime('%Y%m%d%H%M%S')}"

            task = {
                "task_id": task_id,
                "type": "technical_documentation",
                "topic": query,
                "sources": message.get("content", {}).get("sources", []),
                "format": message.get("content", {}).get("format", "markdown"),
                "created_at": datetime.now().isoformat(),
                "status": "pending",
                "requester_id": message.get("sender_id"),
            }

            # Add task to pending tasks
            pending_tasks = await self.state_manager.get_state("research", "pending_tasks") or {}
            pending_tasks[task_id] = task
            await self.state_manager.update_state("research", "pending_tasks", pending_tasks)

            # Acknowledge receipt
            await self.send_message(
                message.get("sender_id"),
                "acknowledgement",
                {
                    "query": query,
                    "task_id": task_id,
                    "status": "processing",
                }
            )

        elif query_type == "knowledge":
            # Search knowledge base
            topic = message.get("content", {}).get("topic")
            results = []

            for entry_id, entry in self.knowledge_base.items():
                if topic.lower() in entry.get("topic", "").lower() or topic.lower() in entry.get("content", "").lower():
                    results.append({
                        "id": entry_id,
                        "type": entry.get("type", "content"),
                        "title": entry.get("title", ""),
                        "url": entry.get("url", ""),
                        "created_at": entry.get("created_at", ""),
                    })

            # Send response
            await self.send_message(
                message.get("sender_id"),
                "response",
                {
                    "query": query,
                    "topic": topic,
                    "results": results,
                }
            )

        elif query_type == "github_repositories":
            # List GitHub repositories
            results = []

            for repo_id, repo in self.github_repositories.items():
                results.append({
                    "id": repo_id,
                    "full_name": repo.get("full_name", ""),
                    "description": repo.get("info", {}).get("description", ""),
                    "analyzed_at": repo.get("analyzed_at", ""),
                })

            # Send response
            await self.send_message(
                message.get("sender_id"),
                "response",
                {
                    "query": query,
                    "type": "github_repositories",
                    "results": results,
                }
            )

        elif query_type == "huggingface_models":
            # List Hugging Face models
            results = []

            for model_id, model in self.huggingface_models.items():
                results.append({
                    "id": model_id,
                    "model_id": model.get("model_id", ""),
                    "analyzed_at": model.get("analyzed_at", ""),
                })

            # Send response
            await self.send_message(
                message.get("sender_id"),
                "response",
                {
                    "query": query,
                    "type": "huggingface_models",
                    "results": results,
                }
            )

        else:
            await super().handle_query(message)

    # GitHub-related methods
    async def _handle_github_search(self, task: Dict):
        """Handle a GitHub search task."""
        return await github_search_impl(self, task)

    async def _handle_github_repository(self, task: Dict):
        """Handle a GitHub repository task."""
        return await github_repository_impl(self, task)

    async def _handle_github_code_analysis(self, task: Dict):
        """Handle a GitHub code analysis task."""
        return await github_code_analysis_impl(self, task)

    # Hugging Face-related methods
    async def _handle_huggingface_search(self, task: Dict):
        """Handle a Hugging Face search task."""
        return await huggingface_search_impl(self, task)

    async def _handle_huggingface_model_analysis(self, task: Dict):
        """Handle a Hugging Face model analysis task."""
        return await huggingface_model_analysis_impl(self, task)

    async def _handle_technical_documentation(self, task: Dict):
        """Handle a technical documentation task."""
        return await technical_documentation_impl(self, task)

    async def shutdown(self):
        """Shutdown the research agent."""
        await super().shutdown()

        # Close web scraper
        if self.web_scraper:
            await self.web_scraper.close()
