"""
Local Hugging Face API Server for UI-TARS.

This script creates a local API server that mimics the Hugging Face API
but uses local models. This allows UI-TARS to work with local models
without requiring a paid Hugging Face subscription.
"""
import os
import json
import logging
import argparse
from typing import Dict, List, Optional, Any, Union

from fastapi import FastAP<PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("local_hf_server")

# Create FastAPI app
app = FastAPI(title="Local Hugging Face API Server")

# Add CORS middleware to allow requests from UI-TARS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Define request models
class GenerationRequest(BaseModel):
    inputs: str
    parameters: Optional[Dict[str, Any]] = None

class ChatRequest(BaseModel):
    model: Optional[str] = None
    messages: List[Dict[str, str]]
    temperature: Optional[float] = 0.7
    max_tokens: Optional[int] = 1000
    stream: Optional[bool] = False

# Global variables
# These are module-level variables
MODEL_PATH = "C:/Users/<USER>/models/UI-TARS-1.5-7B"
MODEL_NAME = "UI-TARS-1.5-7B"
USE_DUMMY_RESPONSES = True  # Set to False if you want to use actual models

# Dummy response for testing
DUMMY_RESPONSE = {
    "generated_text": "This is a dummy response from the local Hugging Face API server. "
                     "UI-TARS is now configured to work with your local models without "
                     "requiring a paid Hugging Face subscription. You can customize this "
                     "response or set USE_DUMMY_RESPONSES to False to use actual models."
}

# Routes
@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "name": "Local Hugging Face API Server",
        "version": "1.0.0",
        "description": "A local API server that mimics the Hugging Face API but uses local models.",
        "models": [MODEL_NAME],
        "status": "running"
    }

@app.post("/models/{model_name}/generate")
async def generate(model_name: str, request: GenerationRequest):
    """Generate text using a local model."""
    logger.info(f"Generating text with model: {model_name}")
    logger.info(f"Input: {request.inputs}")

    if USE_DUMMY_RESPONSES:
        return DUMMY_RESPONSE

    try:
        # Here you would load and use the actual model
        # For now, we just return a dummy response
        return DUMMY_RESPONSE

    except Exception as e:
        logger.exception(f"Error generating text: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/chat/completions")
async def chat_completions(request: ChatRequest):
    """Chat completions endpoint for OpenAI-compatible API."""
    logger.info(f"Chat completion request: {request.model}")

    # Extract the messages
    messages = request.messages
    if not messages:
        raise HTTPException(status_code=400, detail="No messages provided")

    # Get the last user message
    last_user_message = None
    for message in reversed(messages):
        if message.get("role") == "user":
            last_user_message = message.get("content")
            break

    if not last_user_message:
        raise HTTPException(status_code=400, detail="No user message found")

    logger.info(f"Last user message: {last_user_message}")

    if USE_DUMMY_RESPONSES:
        response_text = DUMMY_RESPONSE["generated_text"]
    else:
        # Here you would load and use the actual model
        response_text = DUMMY_RESPONSE["generated_text"]

    # Format the response in OpenAI-compatible format
    response = {
        "id": "chatcmpl-local-123456",
        "object": "chat.completion",
        "created": 1683000000,
        "model": request.model or MODEL_NAME,
        "choices": [
            {
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": response_text
                },
                "finish_reason": "stop"
            }
        ],
        "usage": {
            "prompt_tokens": len(last_user_message.split()),
            "completion_tokens": len(response_text.split()),
            "total_tokens": len(last_user_message.split()) + len(response_text.split())
        }
    }

    return response

@app.post("/models/{model_name}")
async def model_inference(model_name: str, request: Request):
    """Generic model inference endpoint."""
    logger.info(f"Model inference request for: {model_name}")

    # Parse the request body
    body = await request.json()
    logger.info(f"Request body: {body}")

    if USE_DUMMY_RESPONSES:
        return DUMMY_RESPONSE

    try:
        # Here you would load and use the actual model
        return DUMMY_RESPONSE

    except Exception as e:
        logger.exception(f"Error in model inference: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/models")
async def list_models():
    """List available models."""
    return {
        "models": [
            {
                "id": MODEL_NAME,
                "name": MODEL_NAME,
                "path": MODEL_PATH
            }
        ]
    }

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "ok"}

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Local Hugging Face API Server")
    parser.add_argument("--host", type=str, default="127.0.0.1", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    parser.add_argument("--model-path", type=str, default=MODEL_PATH, help="Path to the model")
    parser.add_argument("--model-name", type=str, default=MODEL_NAME, help="Name of the model")
    parser.add_argument("--dummy", action="store_true", help="Use dummy responses")

    args = parser.parse_args()

    MODEL_PATH = args.model_path
    MODEL_NAME = args.model_name
    USE_DUMMY_RESPONSES = args.dummy

    logger.info(f"Starting Local Hugging Face API Server")
    logger.info(f"Host: {args.host}")
    logger.info(f"Port: {args.port}")
    logger.info(f"Model Path: {MODEL_PATH}")
    logger.info(f"Model Name: {MODEL_NAME}")
    logger.info(f"Using Dummy Responses: {USE_DUMMY_RESPONSES}")

    # Start the server
    uvicorn.run(app, host=args.host, port=args.port)

if __name__ == "__main__":
    main()
