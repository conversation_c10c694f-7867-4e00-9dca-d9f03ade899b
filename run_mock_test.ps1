# PowerShell script to run the mock test

# Mock numpy functionality
function Random-Normal {
    param (
        [double]$mean,
        [double]$std,
        [int]$size
    )

    $result = @()
    for ($i = 0; $i -lt $size; $i++) {
        # Box-Muller transform to generate normal distribution
        $u1 = [double](Get-Random -Minimum 0 -Maximum 1000) / 1000
        $u2 = [double](Get-Random -Minimum 0 -Maximum 1000) / 1000

        $z0 = [Math]::Sqrt(-2.0 * [Math]::Log($u1)) * [Math]::Cos(2.0 * [Math]::PI * $u2)

        $result += $mean + $std * $z0
    }

    return $result
}

function Get-VectorNorm {
    param (
        [array]$vector
    )

    $sumSquares = 0
    foreach ($x in $vector) {
        $sumSquares += $x * $x
    }

    return [Math]::Sqrt($sumSquares)
}

function Get-DotProduct {
    param (
        [array]$vec1,
        [array]$vec2
    )

    $result = 0
    for ($i = 0; $i -lt $vec1.Count; $i++) {
        $result += $vec1[$i] * $vec2[$i]
    }

    return $result
}

function Get-VectorMean {
    param (
        [array]$vector
    )

    $sum = 0
    foreach ($x in $vector) {
        $sum += $x
    }

    return $sum / $vector.Count
}

function Get-VectorStd {
    param (
        [array]$vector
    )

    $mean = Get-VectorMean -vector $vector
    $sumSquaredDiff = 0

    foreach ($x in $vector) {
        $diff = $x - $mean
        $sumSquaredDiff += $diff * $diff
    }

    return [Math]::Sqrt($sumSquaredDiff / $vector.Count)
}

# Mock data
$EMBEDDING_DIMENSIONS = 384  # Typical for sentence-transformers/all-MiniLM-L6-v2

function Get-MockEmbedding {
    param (
        [string]$text
    )

    # Use a simple hash of text to generate a deterministic but unique seed
    $hash = 0
    foreach ($char in $text.ToCharArray()) {
        $hash = ($hash * 31 + [int]$char) % 10000
    }

    # Set random seed based on hash
    [System.Random]$random = New-Object System.Random($hash)

    # Generate a random embedding vector
    $embedding = @()
    for ($i = 0; $i -lt $EMBEDDING_DIMENSIONS; $i++) {
        # Generate a random normal value
        $u1 = $random.NextDouble()
        $u2 = $random.NextDouble()
        $z0 = [Math]::Sqrt(-2.0 * [Math]::Log($u1)) * [Math]::Cos(2.0 * [Math]::PI * $u2)

        $embedding += $z0
    }

    # Normalize the embedding
    $norm = Get-VectorNorm -vector $embedding
    $normalizedEmbedding = @()
    foreach ($x in $embedding) {
        $normalizedEmbedding += $x / $norm
    }

    return $normalizedEmbedding
}

function Get-MockEmbeddings {
    param (
        [array]$texts
    )

    $results = @()
    foreach ($text in $texts) {
        $embedding = Get-MockEmbedding -text $text
        $results += ,$embedding
    }

    return $results
}

function Analyze-Embeddings {
    param (
        [array]$embeddings,
        [array]$texts
    )

    Write-Host "Received $($embeddings.Count) embeddings"

    for ($i = 0; $i -lt $embeddings.Count; $i++) {
        $embedding = $embeddings[$i]

        Write-Host "Embedding $($i+1):"
        Write-Host "  Text: $($texts[$i].Substring(0, [Math]::Min(50, $texts[$i].Length)))..."
        Write-Host "  Dimensions: $($embedding.Count)"
        Write-Host "  Mean: $('{0:F6}' -f (Get-VectorMean -vector $embedding))"
        Write-Host "  Std: $('{0:F6}' -f (Get-VectorStd -vector $embedding))"
        Write-Host "  Min: $('{0:F6}' -f ($embedding | Measure-Object -Minimum).Minimum)"
        Write-Host "  Max: $('{0:F6}' -f ($embedding | Measure-Object -Maximum).Maximum)"
        Write-Host ""
    }

    # If we have multiple embeddings, calculate similarity
    if ($embeddings.Count -gt 1) {
        # Embeddings should already be normalized, but just to be sure
        $normalizedEmbeddings = @()
        foreach ($embedding in $embeddings) {
            $norm = Get-VectorNorm -vector $embedding
            $normalizedEmbedding = @()
            foreach ($x in $embedding) {
                $normalizedEmbedding += $x / $norm
            }
            $normalizedEmbeddings += ,$normalizedEmbedding
        }

        # Calculate cosine similarity
        Write-Host "Cosine Similarity Matrix:"
        for ($i = 0; $i -lt $normalizedEmbeddings.Count; $i++) {
            for ($j = $i; $j -lt $normalizedEmbeddings.Count; $j++) {
                $similarity = Get-DotProduct -vec1 $normalizedEmbeddings[$i] -vec2 $normalizedEmbeddings[$j]
                Write-Host "  Texts $($i+1) and $($j+1): $('{0:F6}' -f $similarity)"
            }
        }
    }
}

# Main function
function Main {
    # Test texts
    $texts = @(
        "This is a test sentence about artificial intelligence.",
        "Another completely different text about cats and dogs.",
        "This text is similar to the first one and talks about AI and machine learning."
    )

    try {
        # Generate mock embeddings
        $embeddings = Get-MockEmbeddings -texts $texts

        # Analyze embeddings
        Analyze-Embeddings -embeddings $embeddings -texts $texts

        Write-Host "`nMock test completed successfully!"
        Write-Host "The implementation should work correctly when the environment is properly set up."
    }
    catch {
        Write-Host "Error: $_"
    }
}

# Run the main function
Main
