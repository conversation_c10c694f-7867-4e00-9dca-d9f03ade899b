"""
Alternative Gmail authentication method.
This script provides an alternative method for Gmail authentication.
"""
import os
import sys
import json
import pickle
import webbrowser
import subprocess
from pathlib import Path

def clear_screen():
    """Clear the terminal screen."""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_header():
    """Print the script header."""
    clear_screen()
    print("=" * 80)
    print("                     ALTERNATIVE GMAIL AUTHENTICATION")
    print("=" * 80)
    print("\nThis script provides an alternative method for Gmail authentication.")
    print("It uses a different approach to avoid the 'blocked' message.")
    print("\n")

def get_configured_accounts():
    """
    Get a list of configured Gmail accounts.
    
    Returns:
        list: List of configured Gmail accounts
    """
    accounts = []
    credentials_dir = 'credentials'
    
    if not os.path.exists(credentials_dir):
        return accounts
    
    for filename in os.listdir(credentials_dir):
        if filename.startswith('gmail_') and filename.endswith('_credentials.json'):
            # Extract email from filename
            email_part = filename[6:-16]  # Remove 'gmail_' prefix and '_credentials.json' suffix
            email = email_part.replace('_at_', '@').replace('_dot_', '.')
            accounts.append({
                'email': email,
                'credentials_path': os.path.join(credentials_dir, filename),
                'token_path': os.path.join(credentials_dir, filename.replace('_credentials.json', '_token.pickle'))
            })
    
    return accounts

def authenticate_gmail_account(account):
    """
    Authenticate a Gmail account using an alternative method.
    
    Args:
        account (dict): Account information
        
    Returns:
        bool: True if authentication was successful, False otherwise
    """
    email = account['email']
    credentials_path = account['credentials_path']
    token_path = account['token_path']
    
    print_header()
    print(f"AUTHENTICATING {email}")
    print("-" * 80)
    
    # Install required packages if not already installed
    try:
        from google.auth.transport.requests import Request
        from google.oauth2.credentials import Credentials
        from google_auth_oauthlib.flow import InstalledAppFlow
        from googleapiclient.discovery import build
        from googleapiclient.errors import HttpError
    except ImportError:
        print("\nInstalling required packages...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", 
                              "google-auth", "google-auth-oauthlib", 
                              "google-auth-httplib2", "google-api-python-client"])
        
        from google.auth.transport.requests import Request
        from google.oauth2.credentials import Credentials
        from google_auth_oauthlib.flow import InstalledAppFlow
        from googleapiclient.discovery import build
        from googleapiclient.errors import HttpError
    
    # Gmail API scopes
    SCOPES = [
        'https://www.googleapis.com/auth/gmail.readonly',
        'https://www.googleapis.com/auth/gmail.send',
        'https://www.googleapis.com/auth/gmail.compose',
        'https://www.googleapis.com/auth/gmail.modify'
    ]
    
    # Check if credentials file exists
    if not os.path.exists(credentials_path):
        print(f"\nError: Credentials file not found at {credentials_path}")
        return False
    
    # Remove token file if it exists (to force re-authentication)
    if os.path.exists(token_path):
        os.remove(token_path)
        print(f"\n✓ Removed existing token file: {token_path}")
    
    try:
        print("\nStarting authentication process...")
        
        # Load client secrets
        with open(credentials_path, 'r') as f:
            client_secrets = json.load(f)
        
        # Extract client ID and client secret
        client_id = client_secrets['installed']['client_id']
        client_secret = client_secrets['installed']['client_secret']
        
        print(f"\nClient ID: {client_id}")
        print(f"Redirect URIs: {client_secrets['installed']['redirect_uris']}")
        
        # Create a more permissive flow
        flow = InstalledAppFlow.from_client_secrets_file(
            credentials_path,
            SCOPES,
            redirect_uri='http://localhost:8080/'
        )
        
        # Generate the authorization URL
        auth_url, _ = flow.authorization_url(
            access_type='offline',
            include_granted_scopes='true',
            prompt='consent'
        )
        
        print("\nPlease visit this URL to authorize this application:")
        print(auth_url)
        
        # Open the authorization URL in the browser
        webbrowser.open(auth_url)
        
        # Get the authorization code from the user
        code = input("\nEnter the authorization code: ")
        
        # Exchange the authorization code for credentials
        flow.fetch_token(code=code)
        creds = flow.credentials
        
        # Save the credentials for future use
        with open(token_path, 'wb') as token:
            pickle.dump(creds, token)
        
        print("\n✓ Authentication successful!")
        print(f"✓ Token saved to {token_path}")
        
        # Test the credentials
        service = build('gmail', 'v1', credentials=creds)
        profile = service.users().getProfile(userId='me').execute()
        user_email = profile.get('emailAddress')
        
        print(f"\n✓ Successfully authenticated as {user_email}")
        
        return True
    
    except Exception as e:
        print(f"\nError authenticating: {e}")
        
        print("\nTroubleshooting steps:")
        print("1. Make sure your OAuth consent screen is properly configured")
        print("2. Add all required scopes to your OAuth consent screen")
        print(f"3. Add {email} as a test user")
        print("4. Add http://localhost:8080/ to the authorized redirect URIs")
        print("5. Make sure the Gmail API is enabled for your project")
        
        retry = input("\nDo you want to try again? (y/n): ").lower()
        if retry == 'y':
            return authenticate_gmail_account(account)
        
        return False

def main():
    """Main entry point."""
    print_header()
    
    # Get configured accounts
    accounts = get_configured_accounts()
    
    if not accounts:
        print("\nNo Gmail accounts configured.")
        print("Please run setup_gmail_credentials.py to configure your accounts first.")
        return
    
    print("\nConfigured Gmail accounts:")
    for i, account in enumerate(accounts):
        print(f"{i+1}. {account['email']}")
    
    print(f"{len(accounts)+1}. Exit")
    
    try:
        index = int(input("\nSelect an account to authenticate: ")) - 1
        
        if index == len(accounts):
            # Exit
            print("\nExiting...")
            return
        
        elif 0 <= index < len(accounts):
            # Authenticate account
            authenticate_gmail_account(accounts[index])
            
            # Ask if user wants to authenticate another account
            another = input("\nDo you want to authenticate another account? (y/n): ").lower()
            if another == 'y':
                main()
        
        else:
            print("\nInvalid selection.")
            input("\nPress Enter to try again...")
            main()
    
    except ValueError:
        print("\nInvalid selection.")
        input("\nPress Enter to try again...")
        main()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nExiting...")
        sys.exit(0)
