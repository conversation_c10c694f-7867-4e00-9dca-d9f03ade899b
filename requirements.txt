# Core dependencies
python-dotenv>=1.0.0
pydantic>=2.0.0
fastapi>=0.100.0
uvicorn>=0.22.0
sqlalchemy>=2.0.0
aiohttp>=3.8.5
websockets>=11.0.3
loguru>=0.7.0
tenacity>=8.2.2
schedule>=1.2.0

# LLM integrations
anthropic>=0.5.0
openai>=1.1.0
langchain>=0.0.267
langchain-anthropic>=0.0.1
transformers>=4.35.0
accelerate>=0.25.0
bitsandbytes>=0.41.0
torch>=2.1.0
huggingface-hub>=0.19.0

# DeepSeek LLM integration
deepseek-ai>=0.6.0  # Latest DeepSeek AI Python SDK
sentencepiece>=0.1.99  # Required for DeepSeek tokenization
einops>=0.7.0  # Required for DeepSeek attention patterns

# NVIDIA developer tools and SDKs
cuda-python>=12.3.0
nvidia-cuda-runtime-cu12>=12.1
nvidia-cudnn-cu12>=8.9.2
tensorrt>=10.0.1
nvidia-riva-client>=2.14.0
nvidia-modulus>=0.5.0
nvidia-clara-parabricks>=4.2.0 
tritonclient>=2.38.0  # NVIDIA Triton inference server client
flash-attn>=2.4.0  # Fast attention mechanism for transformer models

# NVIDIA cybersecurity tools
nvidia-morpheus>=24.3.0  # NVIDIA's cybersecurity framework
cudf>=23.10.0  # GPU DataFrame for security analytics
rapids-cuml>=23.10.0  # GPU machine learning algorithms for security
dask-cuda>=23.10.0  # Distributed GPU computing for security analytics

# IBM security tools
ibm-watson>=7.0.0  # IBM Watson services
ibm-security-verify>=0.4.0  # IBM security verification
qiskit-security>=0.5.0  # Quantum security tools
trustyai>=0.4.0  # IBM's TrustyAI for AI security

# Communication services
twilio>=8.5.0  # For phone calls and SMS
sendgrid>=6.10.0  # For email

# Web scraping and data processing (enhanced)
beautifulsoup4>=4.12.2
selenium>=4.11.2
playwright>=1.36.0
pandas>=2.0.3
numpy>=1.24.3
requests>=2.31.0
scrapy>=2.11.0  # Advanced web crawling framework
playwright-stealth>=1.1.0  # Anti-detection for web scraping
trafilatura>=1.6.0  # Extract content from web pages
newspaper3k>=0.2.8  # Article extraction and curation
fastapi-crawler>=0.2.0  # FastAPI-based web crawler
apify-client>=1.4.1  # Apify web scraping platform

# Trading and financial
yfinance>=0.2.28
alpaca-py>=0.8.2
ccxt>=3.1.54

# Social media
tweepy>=4.14.0
facebook-sdk>=3.1.0
google-api-python-client>=2.97.0
instagram-private-api>=1.6.9

# Music industry
spotipy>=2.23.0
musicbrainzngs>=0.7.1
mutagen>=1.46.0  # For metadata

# GitHub and Hugging Face integrations
pygithub>=2.1.1
huggingface-hub>=0.19.0
diffusers>=0.24.0
safetensors>=0.4.0

# Google services
google-api-python-client>=2.97.0
google-auth-httplib2>=0.1.0
google-auth-oauthlib>=1.0.0

# Testing
pytest>=7.4.0
pytest-asyncio>=0.21.1

# GUI and Web interface
flask>=2.3.3
jinja2>=3.1.2
streamlit>=1.29.0  # Interactive GUI development
gradio>=4.11.0  # UI components for machine learning
PyQt6>=6.5.0  # Desktop GUI framework
pyside6>=6.6.0  # Alternative Qt bindings
pywebview>=4.3.2  # Desktop webview GUI

# Cybersecurity tools
python-nmap>=0.7.1  # Python wrapper for Nmap
pymetasploit3>=1.0.3  # Python wrapper for Metasploit
pentestgpt>=0.1.0  # AI-powered penetration testing tool
scapy>=2.5.0  # Packet manipulation library
cryptography>=41.0.0  # Cryptography library
pyopenssl>=23.2.0  # OpenSSL wrapper

# Multi-agent framework (as of May 2025)
langgraph>=0.1.5  # Framework for building multi-agent systems
langchain>=0.1.0  # Language model application framework
agent-development-kit>=0.1.0  # Google's Agent Development Kit
autogen>=1.0.0  # Framework for building autonomous agents

# Account access testing
selenium>=4.1.0  # Web browser automation
requests>=2.27.1  # HTTP requests
cryptography>=41.0.0  # For secure credential storage
webdriver-manager>=3.8.0  # WebDriver management

# Autonomous agent enablers
crewai>=0.28.0  # For organizing agents into functional crews
haystack>=2.0.0  # For document retrieval and QA pipelines
neo4j>=5.13.0  # Graph database for knowledge representation
rdflib>=7.0.0  # For RDF knowledge graphs and semantic integration
