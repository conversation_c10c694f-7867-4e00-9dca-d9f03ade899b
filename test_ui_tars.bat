@echo off
echo UI-TARS Connection Test
echo =====================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Install requests if not already installed
pip install requests >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing requests package...
    pip install requests
    if %errorlevel% neq 0 (
        echo Failed to install requests package. Please check your internet connection.
        exit /b 1
    )
)

REM Run the test script
echo.
echo Testing connection to UI-TARS...
echo.

python test_ui_tars_connection.py

echo.
if %errorlevel% equ 0 (
    echo UI-TARS connection test completed.
) else (
    echo There were issues with the UI-TARS connection test.
)

echo.
pause
