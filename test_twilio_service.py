#!/usr/bin/env python3
"""
Test Twilio Service

This script tests the Twilio service by sending a test text message.
"""

import os
import sys
import asyncio
import argparse
import json
from typing import Dict, Any

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import required modules
from services.voice_calling_service import VoiceCallingService
from core.logger import setup_logger

# Set up logger
logger = setup_logger("test_twilio_service")

async def test_twilio_service(phone_number: str, message: str) -> Dict[str, Any]:
    """
    Test the Twilio service by sending a test text message.
    
    Args:
        phone_number (str): Recipient phone number
        message (str): Message content
        
    Returns:
        Dict[str, Any]: Result of the operation
    """
    # Load configuration
    config_path = os.path.join("config", "communication_services.json")
    with open(config_path, "r") as f:
        config = json.load(f)
    
    # Get voice calling service configuration
    voice_config = config.get("voice_calling_service", {})
    
    # Create a simplified config for the voice calling service
    simplified_config = {
        "enabled": True,
        "default_from_number": voice_config.get("default_from_number", ""),
        "twilio_account_sid": voice_config.get("twilio_account_sid", ""),
        "twilio_auth_token": voice_config.get("twilio_auth_token", "")
    }
    
    # Initialize voice calling service
    voice_calling_service = VoiceCallingService(simplified_config)
    await voice_calling_service.initialize()
    
    if not voice_calling_service.enabled:
        return {"success": False, "error": "Voice calling service is not enabled"}
    
    # Check Twilio status
    twilio_status = voice_calling_service.service_status.get("twilio", {}).get("status")
    if twilio_status != "available":
        return {"success": False, "error": f"Twilio service is not available: {twilio_status}"}
    
    # Send the text message
    result = await voice_calling_service.send_text_message(
        phone_number=phone_number,
        message=message
    )
    
    if "error" in result:
        return {"success": False, "error": result["error"]}
    
    return {"success": True, "message": "Text message sent successfully", "details": result}

async def main():
    """Main function to run the script."""
    parser = argparse.ArgumentParser(description="Test Twilio Service")
    parser.add_argument("--to", required=True, help="Recipient phone number")
    parser.add_argument("--message", default="This is a test message from the AI Agent System.", help="Message content")
    
    args = parser.parse_args()
    
    logger.info(f"Testing Twilio service by sending a text message to {args.to}")
    
    # Test Twilio service
    result = await test_twilio_service(args.to, args.message)
    
    if result["success"]:
        logger.info("Twilio service test successful!")
        logger.info(f"Text message sent to {args.to}")
    else:
        logger.error(f"Twilio service test failed: {result['error']}")
    
    return result

if __name__ == "__main__":
    asyncio.run(main())
