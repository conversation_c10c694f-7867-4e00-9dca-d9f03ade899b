# Quantum Computing Integration

This directory contains the quantum computing integration for the Multi-Agent AI System. It provides a connector for quantum computing services and simulators, allowing agents to leverage quantum algorithms for specific tasks.

## Overview

Quantum computing offers computational advantages for certain types of problems, such as factoring large numbers, searching unsorted databases, and simulating quantum systems. This integration allows agents to access quantum computing resources for these specialized tasks.

## Components

- `quantum_connector.py`: Main connector for quantum computing services
- `quantum_algorithms.py`: Implementation of various quantum algorithms
- `quantum_simulator.py`: Local quantum simulator for testing

## Supported Quantum Providers

The quantum connector supports multiple quantum computing providers:

1. **Simulator**: Local quantum simulator for testing and development
2. **IBM Quantum Experience**: Access to IBM's quantum computers
3. **Google Quantum AI**: Access to Google's quantum processors
4. **PennyLane**: Framework for quantum machine learning

## Quantum Algorithms

The integration supports various quantum algorithms:

1. **Grover's Search Algorithm**: Quadratic speedup for searching unsorted databases
2. **Shor's Factorization Algorithm**: Exponential speedup for factoring large numbers
3. **Quantum Fourier Transform**: Efficient implementation of the Fourier transform
4. **Variational Quantum Eigensolver (VQE)**: Finding eigenvalues of Hamiltonians
5. **Quantum Approximate Optimization Algorithm (QAOA)**: Solving combinatorial optimization problems

## Usage

### Using the Quantum Connector

```python
from quantum_computing.quantum_connector import QuantumConnector
import asyncio

async def run_quantum_algorithm():
    # Create quantum connector
    quantum_config = {
        "provider": "simulator",
        "enabled": True
    }
    connector = QuantumConnector(quantum_config)
    await connector.initialize()
    
    # Run Grover's search algorithm
    result = await connector.run_quantum_algorithm(
        algorithm="grover_search",
        parameters={
            "database_size": 4,
            "marked_item": 2
        },
        shots=1024
    )
    
    print(f"Result: {result}")

asyncio.run(run_quantum_algorithm())
```

### Integration with Agents

Agents can use quantum computing for specialized tasks:

```python
# In an agent's execute_cycle method
async def execute_cycle(self):
    # Get quantum connector from services
    quantum_connector = self.get_service("quantum_connector")
    
    if quantum_connector:
        # Run a quantum algorithm
        result = await quantum_connector.run_quantum_algorithm(
            algorithm="shor_factorization",
            parameters={
                "number": 15
            }
        )
        
        # Use the result
        factors = result.get("factors", [])
        if factors:
            self.logger.info(f"Factorization result: {factors}")
```

## Local Quantum Simulator

For testing and development, a local quantum simulator is provided. This simulator can run basic quantum algorithms without requiring access to actual quantum hardware.

```python
from quantum_computing.quantum_simulator import QuantumSimulator

# Create simulator
simulator = QuantumSimulator()

# Run Grover's algorithm
result = simulator.run_grover_search(
    database_size=4,
    marked_item=2,
    shots=1024
)

print(f"Result: {result}")
```

## Quantum Machine Learning

The integration also supports quantum machine learning algorithms:

1. **Quantum Neural Networks**: Neural networks with quantum layers
2. **Quantum Support Vector Machines**: SVM with quantum kernels
3. **Quantum Generative Models**: Generative models using quantum circuits

Example:

```python
from quantum_computing.quantum_ml import QuantumNeuralNetwork
import numpy as np

# Create quantum neural network
qnn = QuantumNeuralNetwork(
    n_qubits=4,
    n_layers=2
)

# Train on data
X_train = np.random.rand(10, 4)
y_train = np.random.randint(0, 2, 10)
qnn.train(X_train, y_train, epochs=100)

# Make predictions
X_test = np.random.rand(5, 4)
predictions = qnn.predict(X_test)
```

## Configuration

Quantum computing can be configured in the `.env` file:

```
# Quantum computing settings
ENABLE_QUANTUM=True
QUANTUM_PROVIDER=simulator
QUANTUM_API_KEY=your_api_key
QUANTUM_USE_LOCAL=True
QUANTUM_LOCAL_URL=http://localhost:8081
```

## Requirements

To use the quantum computing integration, you may need to install additional packages:

```bash
# For simulator only
pip install numpy

# For IBM Quantum Experience
pip install qiskit

# For Google Quantum
pip install cirq

# For PennyLane
pip install pennylane

# For all providers
pip install numpy qiskit cirq pennylane
```
