"""
Facebook Lead Integration for the Multi-Agent AI System.

This module provides integration with Facebook Lead Ads, allowing the system
to automatically retrieve and process leads from Facebook campaigns.
"""
import asyncio
import json
import logging
import os
import sys
import time
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
import aiohttp
import argparse

# Add parent directory to path to import from core
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.logger import setup_logger
from drip_campaign_workflow import DripCampaignWorkflow

# Set up logger
logger = setup_logger("facebook_lead_integration")

class FacebookLeadIntegration:
    """
    Facebook Lead Integration for the Multi-Agent AI System.

    This class provides integration with Facebook Lead Ads, allowing the system
    to automatically retrieve and process leads from Facebook campaigns.
    """
    
    def __init__(self, 
                 config_path: str = "config/facebook_lead_config.json",
                 credentials_path: str = "credentials/social_media/facebook.json",
                 drip_campaign_config_path: str = "config/drip_campaign_config.json"):
        """
        Initialize the Facebook Lead Integration.
        
        Args:
            config_path (str): Path to Facebook lead configuration
            credentials_path (str): Path to Facebook API credentials
            drip_campaign_config_path (str): Path to drip campaign configuration
        """
        self.config_path = config_path
        self.credentials_path = credentials_path
        self.drip_campaign_config_path = drip_campaign_config_path
        
        self.config = {}
        self.credentials = {}
        self.leads = {}
        self.processed_leads = set()
        
        self.drip_campaign_workflow = None
        self.running = False
    
    async def initialize(self) -> bool:
        """
        Initialize the Facebook Lead Integration.
        
        Returns:
            bool: True if initialization was successful, False otherwise
        """
        logger.info("Initializing Facebook Lead Integration")
        
        # Load configuration
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, "r", encoding="utf-8") as f:
                    self.config = json.load(f)
                logger.info("Loaded Facebook lead configuration")
            else:
                logger.warning(f"Configuration file not found: {self.config_path}")
                self.config = {
                    "check_interval": 300,  # 5 minutes
                    "campaigns": {},
                    "forms": {},
                    "lead_mapping": {
                        "full_name": ["full_name", "name"],
                        "email": ["email", "email_address"],
                        "phone": ["phone_number", "phone"],
                        "insurance_type": ["insurance_type", "insurance_interest"],
                        "budget": ["budget", "monthly_budget"]
                    }
                }
                
                # Create directory if it doesn't exist
                os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
                
                # Save default configuration
                with open(self.config_path, "w", encoding="utf-8") as f:
                    json.dump(self.config, f, indent=4)
                logger.info(f"Created default configuration file: {self.config_path}")
        except Exception as e:
            logger.exception(f"Error loading configuration: {e}")
            return False
        
        # Load credentials
        try:
            if os.path.exists(self.credentials_path):
                with open(self.credentials_path, "r", encoding="utf-8") as f:
                    self.credentials = json.load(f)
                logger.info("Loaded Facebook API credentials")
            else:
                logger.warning(f"Credentials file not found: {self.credentials_path}")
                return False
        except Exception as e:
            logger.exception(f"Error loading credentials: {e}")
            return False
        
        # Initialize drip campaign workflow
        self.drip_campaign_workflow = DripCampaignWorkflow(
            config_path=self.drip_campaign_config_path
        )
        
        success = await self.drip_campaign_workflow.initialize()
        if not success:
            logger.error("Failed to initialize drip campaign workflow")
            return False
        
        logger.info("Facebook Lead Integration initialized successfully")
        return True
    
    async def check_new_leads(self) -> List[Dict]:
        """
        Check for new leads from Facebook campaigns.
        
        Returns:
            List[Dict]: List of new leads
        """
        logger.info("Checking for new Facebook leads")
        
        if not self.credentials.get("access_token"):
            logger.error("Facebook access token not found")
            return []
        
        new_leads = []
        
        try:
            # Get campaigns
            campaigns = self.config.get("campaigns", {})
            
            for campaign_id, campaign_info in campaigns.items():
                if not campaign_info.get("enabled", True):
                    continue
                
                # Get forms for this campaign
                forms = campaign_info.get("forms", [])
                
                for form_id in forms:
                    # Get leads for this form
                    leads = await self._get_form_leads(form_id)
                    
                    for lead in leads:
                        lead_id = lead.get("id")
                        
                        # Skip if already processed
                        if lead_id in self.processed_leads:
                            continue
                        
                        # Get lead details
                        lead_details = await self._get_lead_details(lead_id)
                        
                        if lead_details:
                            # Add to new leads
                            new_leads.append(lead_details)
                            
                            # Mark as processed
                            self.processed_leads.add(lead_id)
                            
                            # Store lead
                            self.leads[lead_id] = lead_details
            
            logger.info(f"Found {len(new_leads)} new Facebook leads")
            return new_leads
            
        except Exception as e:
            logger.exception(f"Error checking for new Facebook leads: {e}")
            return []
    
    async def _get_form_leads(self, form_id: str) -> List[Dict]:
        """
        Get leads for a specific form.
        
        Args:
            form_id (str): Form ID
            
        Returns:
            List[Dict]: List of leads
        """
        try:
            access_token = self.credentials.get("access_token")
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"https://graph.facebook.com/v18.0/{form_id}/leads",
                    params={"access_token": access_token}
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get("data", [])
                    else:
                        error_data = await response.text()
                        logger.error(f"Error getting form leads: {error_data}")
                        return []
        except Exception as e:
            logger.exception(f"Error getting form leads: {e}")
            return []
    
    async def _get_lead_details(self, lead_id: str) -> Optional[Dict]:
        """
        Get details for a specific lead.
        
        Args:
            lead_id (str): Lead ID
            
        Returns:
            Optional[Dict]: Lead details or None if not found
        """
        try:
            access_token = self.credentials.get("access_token")
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"https://graph.facebook.com/v18.0/{lead_id}",
                    params={"access_token": access_token, "fields": "field_data,created_time,ad_id,form_id,campaign_id"}
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        # Extract field data
                        field_data = data.get("field_data", [])
                        lead_info = {
                            "id": lead_id,
                            "created_time": data.get("created_time"),
                            "ad_id": data.get("ad_id"),
                            "form_id": data.get("form_id"),
                            "campaign_id": data.get("campaign_id"),
                            "source": "facebook"
                        }
                        
                        # Process field data
                        for field in field_data:
                            name = field.get("name", "").lower()
                            values = field.get("values", [])
                            if values:
                                lead_info[name] = values[0]
                        
                        return lead_info
                    else:
                        error_data = await response.text()
                        logger.error(f"Error getting lead details: {error_data}")
                        return None
        except Exception as e:
            logger.exception(f"Error getting lead details: {e}")
            return None
    
    async def process_leads(self, leads: List[Dict]) -> None:
        """
        Process leads and start drip campaigns.
        
        Args:
            leads (List[Dict]): List of leads to process
        """
        logger.info(f"Processing {len(leads)} Facebook leads")
        
        for lead in leads:
            try:
                # Map lead fields to drip campaign fields
                lead_mapping = self.config.get("lead_mapping", {})
                
                # Extract client name
                client_name = None
                for field in lead_mapping.get("full_name", []):
                    if field in lead:
                        client_name = lead[field]
                        break
                
                if not client_name:
                    logger.warning(f"No name found for lead {lead.get('id')}")
                    continue
                
                # Extract email
                email = None
                for field in lead_mapping.get("email", []):
                    if field in lead:
                        email = lead[field]
                        break
                
                if not email:
                    logger.warning(f"No email found for lead {lead.get('id')}")
                    continue
                
                # Extract phone
                phone = None
                for field in lead_mapping.get("phone", []):
                    if field in lead:
                        phone = lead[field]
                        break
                
                if not phone:
                    logger.warning(f"No phone found for lead {lead.get('id')}")
                    # Use a placeholder phone number for testing
                    phone = "5555555555"
                
                # Extract insurance type
                insurance_type = None
                for field in lead_mapping.get("insurance_type", []):
                    if field in lead:
                        insurance_type = lead[field]
                        break
                
                if not insurance_type:
                    logger.warning(f"No insurance type found for lead {lead.get('id')}")
                    insurance_type = "Unknown"
                
                # Extract budget
                budget = None
                for field in lead_mapping.get("budget", []):
                    if field in lead:
                        budget = lead[field]
                        break
                
                if not budget:
                    logger.warning(f"No budget found for lead {lead.get('id')}")
                    budget = "Unknown"
                
                # Start drip campaign
                logger.info(f"Starting drip campaign for Facebook lead: {client_name}")
                result = await self.drip_campaign_workflow.start_campaign_for_client(
                    client_name=client_name,
                    phone_number=phone,
                    email=email,
                    insurance_type=insurance_type,
                    budget=budget
                )
                
                if result.get("success"):
                    logger.info(f"Started drip campaign for {client_name} with ID {result.get('campaign_id')}")
                    
                    # Update lead with campaign ID
                    lead["campaign_id"] = result.get("campaign_id")
                    lead["campaign_status"] = "active"
                    lead["last_updated"] = datetime.now().isoformat()
                    
                    # Save updated lead
                    self.leads[lead.get("id")] = lead
                else:
                    logger.error(f"Failed to start drip campaign for {client_name}: {result.get('error')}")
            
            except Exception as e:
                logger.exception(f"Error processing lead {lead.get('id')}: {e}")
    
    async def run(self, check_interval: Optional[int] = None) -> None:
        """
        Run the Facebook lead integration.
        
        Args:
            check_interval (Optional[int]): Interval between checks in seconds
        """
        self.running = True
        
        # Use configured interval or default
        interval = check_interval or self.config.get("check_interval", 300)
        
        logger.info(f"Running Facebook lead integration with interval {interval} seconds")
        
        try:
            while self.running:
                # Check for new leads
                new_leads = await self.check_new_leads()
                
                # Process new leads
                if new_leads:
                    await self.process_leads(new_leads)
                
                # Wait for next check
                logger.info(f"Waiting {interval} seconds before next check")
                await asyncio.sleep(interval)
        
        except asyncio.CancelledError:
            logger.info("Facebook lead integration cancelled")
            self.running = False
        
        except Exception as e:
            logger.exception(f"Error in Facebook lead integration: {e}")
            self.running = False
    
    def stop(self) -> None:
        """Stop the Facebook lead integration."""
        logger.info("Stopping Facebook lead integration")
        self.running = False

async def main():
    """Main function."""
    # Parse arguments
    parser = argparse.ArgumentParser(description="Facebook Lead Integration")
    parser.add_argument("--config", default="config/facebook_lead_config.json", help="Path to Facebook lead configuration")
    parser.add_argument("--credentials", default="credentials/social_media/facebook.json", help="Path to Facebook API credentials")
    parser.add_argument("--drip-campaign-config", default="config/drip_campaign_config.json", help="Path to drip campaign configuration")
    parser.add_argument("--interval", type=int, help="Interval between checks in seconds")
    args = parser.parse_args()
    
    # Create integration
    integration = FacebookLeadIntegration(
        config_path=args.config,
        credentials_path=args.credentials,
        drip_campaign_config_path=args.drip_campaign_config
    )
    
    # Initialize
    success = await integration.initialize()
    if not success:
        logger.error("Failed to initialize Facebook lead integration")
        return 1
    
    try:
        # Run integration
        await integration.run(args.interval)
    except KeyboardInterrupt:
        logger.info("Interrupted by user")
        integration.stop()
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\nInterrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)
