"""
Gmail Browser Automation

This script uses Selenium to automate Gmail in the browser to send emails.
It handles browser setup automatically using webdriver-manager.
"""
import os
import sys
import time
import logging
import getpass
import argparse
from typing import Dict, Optional, Any

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("gmail_browser_automation")

# Check if required packages are installed
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.service import Service as ChromeService
    from selenium.webdriver.firefox.service import Service as FirefoxService
    from selenium.webdriver.edge.service import Service as EdgeService
    from selenium.webdriver.common.by import By
    from selenium.webdriver.common.keys import Keys
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    from webdriver_manager.chrome import Chrome<PERSON>riverManager
    from webdriver_manager.firefox import GeckoDriver<PERSON>anager
    from webdriver_manager.microsoft import EdgeChromiumDriverManager
except ImportError as e:
    logger.error(f"Required package not installed: {e}")
    logger.error("Please install required packages with: pip install -r email_automation_requirements.txt")
    sys.exit(1)

class GmailBrowserAutomation:
    """Class to automate Gmail using Selenium."""

    def __init__(self, browser_type: str = "chrome"):
        """
        Initialize the Gmail Browser Automation.

        Args:
            browser_type (str): Type of browser to use (chrome, firefox, edge)
        """
        self.browser_type = browser_type.lower()
        self.driver = None

    def initialize(self) -> bool:
        """
        Initialize the browser with automatic webdriver management.

        Returns:
            bool: True if initialization was successful, False otherwise
        """
        logger.info(f"Initializing {self.browser_type} browser")

        try:
            if self.browser_type == "chrome":
                service = ChromeService(ChromeDriverManager().install())
                options = webdriver.ChromeOptions()

                # Make the browser appear more human-like
                options.add_argument("--start-maximized")
                options.add_argument("--disable-blink-features=AutomationControlled")
                options.add_argument("--disable-extensions")
                options.add_experimental_option("excludeSwitches", ["enable-automation"])
                options.add_experimental_option("useAutomationExtension", False)

                # Add a common user agent
                options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")

                self.driver = webdriver.Chrome(service=service, options=options)

                # Execute CDP commands to disable automation flags
                self.driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
                    "source": """
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined
                    })
                    """
                })

            elif self.browser_type == "firefox":
                service = FirefoxService(GeckoDriverManager().install())
                options = webdriver.FirefoxOptions()

                # Make the browser appear more human-like
                options.set_preference("dom.webdriver.enabled", False)
                options.set_preference("useAutomationExtension", False)

                # Add a common user agent
                options.set_preference("general.useragent.override", "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:125.0) Gecko/20100101 Firefox/125.0")

                self.driver = webdriver.Firefox(service=service, options=options)

            elif self.browser_type == "edge":
                service = EdgeService(EdgeChromiumDriverManager().install())
                options = webdriver.EdgeOptions()

                # Make the browser appear more human-like
                options.add_argument("--start-maximized")
                options.add_argument("--disable-blink-features=AutomationControlled")
                options.add_argument("--disable-extensions")
                options.add_experimental_option("excludeSwitches", ["enable-automation"])
                options.add_experimental_option("useAutomationExtension", False)

                # Add a common user agent
                options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********")

                self.driver = webdriver.Edge(service=service, options=options)

                # Execute CDP commands to disable automation flags
                self.driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
                    "source": """
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined
                    })
                    """
                })

            else:
                logger.error(f"Unsupported browser type: {self.browser_type}")
                return False

            # Set implicit wait time
            self.driver.implicitly_wait(10)

            logger.info(f"{self.browser_type} browser initialized successfully")
            return True

        except Exception as e:
            logger.exception(f"Error initializing browser: {e}")
            return False

    def send_email(self,
                  email_account: str,
                  password: str,
                  to_email: str,
                  subject: str,
                  body: str) -> Dict[str, Any]:
        """
        Send an email using Gmail through browser automation.

        Args:
            email_account (str): Gmail account to send from
            password (str): Password for the Gmail account
            to_email (str): Recipient email address
            subject (str): Email subject
            body (str): Email body

        Returns:
            Dict[str, Any]: Result of the operation
        """
        if not self.driver:
            logger.error("Browser not initialized")
            return {"success": False, "error": "Browser not initialized"}

        try:
            # Step 1: Navigate to Gmail
            logger.info("Navigating to Gmail")
            self.driver.get("https://mail.google.com")
            time.sleep(2)  # Give the page time to load

            # Step 2: Check if already logged in
            logger.info("Checking login status")

            # Wait for either the email input field or the Gmail interface to load
            try:
                # Look for the email input field
                try:
                    email_input = WebDriverWait(self.driver, 10).until(
                        EC.presence_of_element_located((By.ID, "identifierId"))
                    )

                    # Need to log in
                    logger.info("Login page detected")

                    # Step 3: Enter email
                    logger.info(f"Entering email: {email_account}")
                    email_input.clear()
                    email_input.send_keys(email_account)
                    time.sleep(1)  # Brief pause before clicking next

                    # Click the Next button
                    next_button = self.driver.find_element(By.XPATH, "//span[text()='Next']")
                    next_button.click()

                    # Wait for password field
                    logger.info("Waiting for password field...")
                    password_input = WebDriverWait(self.driver, 15).until(
                        EC.element_to_be_clickable((By.NAME, "Passwd"))
                    )

                    # Step 4: Enter password
                    logger.info("Entering password")
                    time.sleep(2)  # Brief pause before entering password
                    password_input.clear()
                    password_input.send_keys(password)
                    time.sleep(1)  # Brief pause before clicking next

                    # Click the Next button for password
                    next_button = self.driver.find_element(By.XPATH, "//span[text()='Next']")
                    next_button.click()

                    # Wait for Gmail to load
                    logger.info("Waiting for Gmail to load...")
                    WebDriverWait(self.driver, 30).until(
                        EC.presence_of_element_located((By.XPATH, "//div[text()='Compose']"))
                    )

                except NoSuchElementException as e:
                    logger.warning(f"Element not found during login: {e}")
                    # Take a screenshot for debugging
                    screenshot_path = "login_element_not_found.png"
                    self.driver.save_screenshot(screenshot_path)
                    logger.info(f"Screenshot saved to {screenshot_path}")

                    # Try alternative selectors
                    if "Next" in str(e):
                        # Try clicking the button by its role
                        try:
                            next_button = self.driver.find_element(By.CSS_SELECTOR, "button[type='button']")
                            next_button.click()
                            logger.info("Clicked button using CSS selector")
                        except Exception as e2:
                            logger.warning(f"Alternative button click failed: {e2}")
                            try:
                                # Try finding any button
                                buttons = self.driver.find_elements(By.TAG_NAME, "button")
                                if buttons:
                                    buttons[0].click()
                                    logger.info("Clicked first button found")
                            except Exception as e3:
                                logger.warning(f"Failed to find any buttons: {e3}")
                                # Try pressing Enter key as last resort
                                try:
                                    active_element = self.driver.switch_to.active_element
                                    active_element.send_keys(Keys.RETURN)
                                    logger.info("Pressed Enter key on active element")
                                except Exception as e4:
                                    logger.warning(f"Failed to press Enter key: {e4}")

                    # Check for verification challenges
                    page_source = self.driver.page_source.lower()

                    # Check for "browser or app may not be supported" message
                    if "browser or app may not be supported" in page_source:
                        logger.warning("Detected 'browser or app may not be supported' message")

                        # Take a screenshot for debugging
                        screenshot_path = "browser_not_supported_screenshot.png"
                        self.driver.save_screenshot(screenshot_path)
                        logger.info(f"Screenshot saved to {screenshot_path}")

                        # Try to click "Continue" button
                        try:
                            # Try different selectors for the Continue button
                            selectors = [
                                "//span[contains(text(), 'Continue')]",
                                "//button[contains(text(), 'Continue')]",
                                "//div[contains(text(), 'Continue')]",
                                "//span[contains(text(), 'continue')]",
                                "//button[contains(text(), 'continue')]",
                                "//div[contains(text(), 'continue')]"
                            ]

                            for selector in selectors:
                                try:
                                    continue_button = self.driver.find_element(By.XPATH, selector)
                                    logger.info(f"Found Continue button using selector: {selector}")
                                    continue_button.click()
                                    logger.info("Clicked Continue button")
                                    time.sleep(2)
                                    break
                                except NoSuchElementException:
                                    continue

                            # If we couldn't find the Continue button, try clicking any button
                            if "continue" not in page_source.lower():
                                buttons = self.driver.find_elements(By.TAG_NAME, "button")
                                if buttons:
                                    buttons[0].click()
                                    logger.info("Clicked first button found")
                                    time.sleep(2)

                        except Exception as e:
                            logger.warning(f"Failed to click Continue button: {e}")

                        # Wait for manual intervention if needed
                        logger.info("Waiting 60 seconds for manual intervention...")
                        time.sleep(60)

                    # Check for other verification challenges
                    elif "verify" in page_source or "challenge" in page_source or "security" in page_source:
                        logger.warning("Detected a verification challenge. User interaction may be required.")
                        # Give user time to manually complete verification if needed
                        logger.info("Waiting 60 seconds for manual verification...")
                        time.sleep(60)

            except TimeoutException:
                # Check if already logged in
                try:
                    logger.info("Checking if already logged in...")
                    WebDriverWait(self.driver, 10).until(
                        EC.presence_of_element_located((By.XPATH, "//div[text()='Compose']"))
                    )
                    logger.info("Already logged in")
                except TimeoutException:
                    # Take a screenshot for debugging
                    screenshot_path = "login_error_screenshot.png"
                    self.driver.save_screenshot(screenshot_path)
                    logger.warning(f"Timeout while waiting for Gmail interface. Screenshot saved to {screenshot_path}")

                    # Check the page source for known issues
                    page_source = self.driver.page_source.lower()

                    # Check for "browser or app may not be supported" message
                    if "browser or app may not be supported" in page_source:
                        logger.warning("Detected 'browser or app may not be supported' message")

                        # Try to click "Continue" button
                        try:
                            # Try different selectors for the Continue button
                            selectors = [
                                "//span[contains(text(), 'Continue')]",
                                "//button[contains(text(), 'Continue')]",
                                "//div[contains(text(), 'Continue')]",
                                "//span[contains(text(), 'continue')]",
                                "//button[contains(text(), 'continue')]",
                                "//div[contains(text(), 'continue')]"
                            ]

                            for selector in selectors:
                                try:
                                    continue_button = self.driver.find_element(By.XPATH, selector)
                                    logger.info(f"Found Continue button using selector: {selector}")
                                    continue_button.click()
                                    logger.info("Clicked Continue button")
                                    time.sleep(2)

                                    # Wait for Gmail to load after clicking Continue
                                    try:
                                        WebDriverWait(self.driver, 20).until(
                                            EC.presence_of_element_located((By.XPATH, "//div[text()='Compose']"))
                                        )
                                        logger.info("Successfully loaded Gmail after clicking Continue")
                                        break
                                    except TimeoutException:
                                        logger.warning("Still waiting for Gmail to load after clicking Continue")

                                except NoSuchElementException:
                                    continue

                            # If we couldn't find the Continue button, try clicking any button
                            if "continue" not in page_source.lower():
                                buttons = self.driver.find_elements(By.TAG_NAME, "button")
                                if buttons:
                                    buttons[0].click()
                                    logger.info("Clicked first button found")
                                    time.sleep(2)

                                    # Wait for Gmail to load after clicking button
                                    try:
                                        WebDriverWait(self.driver, 20).until(
                                            EC.presence_of_element_located((By.XPATH, "//div[text()='Compose']"))
                                        )
                                        logger.info("Successfully loaded Gmail after clicking button")
                                    except TimeoutException:
                                        logger.warning("Still waiting for Gmail to load after clicking button")

                        except Exception as e:
                            logger.warning(f"Failed to click Continue button: {e}")

                        # Wait for manual intervention
                        logger.info("Waiting 60 seconds for manual intervention...")
                        time.sleep(60)

                        # Check if we made it to Gmail after manual intervention
                        try:
                            WebDriverWait(self.driver, 10).until(
                                EC.presence_of_element_located((By.XPATH, "//div[text()='Compose']"))
                            )
                            logger.info("Successfully loaded Gmail after manual intervention")
                        except TimeoutException:
                            logger.error("Still unable to access Gmail after manual intervention")
                            return {"success": False, "error": "Failed to access Gmail after manual intervention"}
                    else:
                        # If it's not the "browser or app may not be supported" message, return error
                        logger.error("Failed to log in or detect Gmail interface")
                        return {"success": False, "error": "Failed to log in or detect Gmail interface"}

            # Step 5: Click Compose
            logger.info("Clicking Compose button")
            time.sleep(5)  # Give the page more time to fully load

            # Try different selectors for the Compose button
            compose_selectors = [
                "//div[text()='Compose']",
                "//div[contains(text(), 'Compose')]",
                "//div[@role='button' and contains(text(), 'Compose')]",
                "//div[@gh='cm']",  # Gmail's internal attribute for compose button
                "//div[contains(@class, 'compose')]",
                "//div[contains(@aria-label, 'Compose')]"
            ]

            compose_button = None
            for selector in compose_selectors:
                try:
                    compose_button = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    logger.info(f"Found Compose button using selector: {selector}")
                    break
                except TimeoutException:
                    continue

            if not compose_button:
                # Take a screenshot for debugging
                screenshot_path = "compose_button_not_found.png"
                self.driver.save_screenshot(screenshot_path)
                logger.error(f"Could not find Compose button. Screenshot saved to {screenshot_path}")

                # Try to find any button that might be the compose button
                buttons = self.driver.find_elements(By.TAG_NAME, "div")
                for button in buttons:
                    try:
                        if "compose" in button.text.lower() or "new" in button.text.lower() or "write" in button.text.lower():
                            compose_button = button
                            logger.info(f"Found potential Compose button with text: {button.text}")
                            break
                    except:
                        continue

            if not compose_button:
                return {"success": False, "error": "Could not find Compose button"}

            # Click the Compose button
            compose_button.click()
            logger.info("Clicked Compose button")
            time.sleep(3)  # Give the compose window time to open

            # Wait for compose window
            to_input = None
            to_selectors = [
                "//textarea[@name='to']",
                "//input[@name='to']",
                "//input[contains(@aria-label, 'To')]",
                "//input[contains(@placeholder, 'To')]",
                "//div[contains(@aria-label, 'To')]//input",
                "//div[contains(@role, 'textbox') and contains(@aria-label, 'To')]"
            ]

            for selector in to_selectors:
                try:
                    to_input = WebDriverWait(self.driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, selector))
                    )
                    logger.info(f"Found To field using selector: {selector}")
                    break
                except TimeoutException:
                    continue

            if not to_input:
                # Take a screenshot for debugging
                screenshot_path = "to_field_not_found.png"
                self.driver.save_screenshot(screenshot_path)
                logger.error(f"Could not find To field. Screenshot saved to {screenshot_path}")
                return {"success": False, "error": "Could not find To field"}

            # Step 6: Fill in recipient
            logger.info(f"Entering recipient: {to_email}")
            to_input.clear()
            to_input.send_keys(to_email)

            # Step 7: Fill in subject
            logger.info(f"Entering subject: {subject}")

            # Try different selectors for the subject field
            subject_selectors = [
                "//input[@name='subjectbox']",
                "//input[contains(@aria-label, 'Subject')]",
                "//input[contains(@placeholder, 'Subject')]",
                "//input[@name='subject']",
                "//div[contains(@aria-label, 'Subject')]//input"
            ]

            subject_input = None
            for selector in subject_selectors:
                try:
                    subject_input = WebDriverWait(self.driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, selector))
                    )
                    logger.info(f"Found Subject field using selector: {selector}")
                    break
                except (TimeoutException, NoSuchElementException):
                    continue

            if not subject_input:
                # Take a screenshot for debugging
                screenshot_path = "subject_field_not_found.png"
                self.driver.save_screenshot(screenshot_path)
                logger.error(f"Could not find Subject field. Screenshot saved to {screenshot_path}")
                return {"success": False, "error": "Could not find Subject field"}

            subject_input.clear()
            subject_input.send_keys(subject)
            time.sleep(1)  # Brief pause after entering subject

            # Step 8: Fill in body
            logger.info("Entering email body")

            # Try different selectors for the body field
            body_selectors = [
                "//div[@role='textbox']",
                "//div[contains(@aria-label, 'Message Body')]",
                "//div[contains(@aria-label, 'message')]",
                "//div[contains(@g_editable, 'true')]",
                "//div[contains(@contenteditable, 'true')]"
            ]

            body_input = None
            for selector in body_selectors:
                try:
                    body_input = WebDriverWait(self.driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, selector))
                    )
                    logger.info(f"Found Body field using selector: {selector}")
                    break
                except (TimeoutException, NoSuchElementException):
                    continue

            if not body_input:
                # Take a screenshot for debugging
                screenshot_path = "body_field_not_found.png"
                self.driver.save_screenshot(screenshot_path)
                logger.error(f"Could not find Body field. Screenshot saved to {screenshot_path}")
                return {"success": False, "error": "Could not find Body field"}

            body_input.clear()
            body_input.send_keys(body)
            time.sleep(1)  # Brief pause after entering body

            # Step 9: Send the email
            logger.info("Sending email")

            # Try different selectors for the Send button
            send_selectors = [
                "//div[text()='Send']",
                "//div[contains(text(), 'Send')]",
                "//div[@role='button' and contains(text(), 'Send')]",
                "//div[contains(@aria-label, 'Send')]",
                "//div[contains(@data-tooltip, 'Send')]",
                "//div[contains(@class, 'send')]"
            ]

            send_button = None
            for selector in send_selectors:
                try:
                    send_button = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    logger.info(f"Found Send button using selector: {selector}")
                    break
                except (TimeoutException, NoSuchElementException):
                    continue

            if not send_button:
                # Take a screenshot for debugging
                screenshot_path = "send_button_not_found.png"
                self.driver.save_screenshot(screenshot_path)
                logger.error(f"Could not find Send button. Screenshot saved to {screenshot_path}")

                # Try to find any button that might be the send button
                buttons = self.driver.find_elements(By.TAG_NAME, "div")
                for button in buttons:
                    try:
                        if "send" in button.text.lower():
                            send_button = button
                            logger.info(f"Found potential Send button with text: {button.text}")
                            break
                    except:
                        continue

            if not send_button:
                return {"success": False, "error": "Could not find Send button"}

            # Click the Send button
            send_button.click()
            logger.info("Clicked Send button")

            # Step 10: Wait for confirmation
            time.sleep(5)  # Give more time for the email to be sent

            # Check if the email was sent successfully
            try:
                # Look for confirmation message
                confirmation_selectors = [
                    "//div[contains(text(), 'Message sent')]",
                    "//div[contains(text(), 'Sent')]",
                    "//div[contains(@role, 'alert') and contains(text(), 'sent')]"
                ]

                for selector in confirmation_selectors:
                    try:
                        confirmation = WebDriverWait(self.driver, 3).until(
                            EC.presence_of_element_located((By.XPATH, selector))
                        )
                        logger.info(f"Found confirmation message: {confirmation.text}")
                        break
                    except (TimeoutException, NoSuchElementException):
                        continue
            except:
                # Even if we can't find a confirmation message, we'll assume it was sent
                logger.info("Could not find confirmation message, but assuming email was sent")
                pass

            logger.info("Email sent successfully")
            return {"success": True, "message": "Email sent successfully"}

        except Exception as e:
            logger.exception(f"Error sending email: {e}")
            return {"success": False, "error": str(e)}

    def shutdown(self) -> None:
        """Shut down the browser."""
        if self.driver:
            self.driver.quit()
        logger.info("Browser shut down")

def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Gmail Browser Automation")
    parser.add_argument("--email", type=str, default="<EMAIL>", help="Gmail account to send from")
    parser.add_argument("--to", type=str, default="<EMAIL>", help="Recipient email address")
    parser.add_argument("--subject", type=str, default="Test Email from AI Agent System", help="Email subject")
    parser.add_argument("--body", type=str, default="This is a test email sent using browser automation integrated with the AI Agent System.", help="Email body")
    parser.add_argument("--browser", type=str, choices=["chrome", "firefox", "edge"], default="chrome", help="Browser to use")

    args = parser.parse_args()

    # Get password
    password = getpass.getpass(f"Enter password for {args.email}: ")

    # Create Gmail Browser Automation
    browser_automation = GmailBrowserAutomation(browser_type=args.browser)

    # Initialize
    initialized = browser_automation.initialize()
    if not initialized:
        logger.error("Failed to initialize browser")
        return

    try:
        # Send email
        result = browser_automation.send_email(
            email_account=args.email,
            password=password,
            to_email=args.to,
            subject=args.subject,
            body=args.body
        )

        if result["success"]:
            logger.info("Email sent successfully")
        else:
            logger.error(f"Failed to send email: {result['error']}")

    finally:
        # Shut down
        browser_automation.shutdown()

if __name__ == "__main__":
    main()
