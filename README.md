# Multi-Agent AI System

A comprehensive multi-agent AI system designed to operate autonomously across multiple domains including insurance business, trading, social media marketing, and music management.

## System Overview

This system consists of multiple specialized AI agents powered by various LLM frameworks (Claude/<PERSON>, Grok, Sonnet 3.7) that work together to perform tasks with minimal human intervention.

### Core Capabilities
- Autonomous operation with minimal human input
- Communication via phone calls, voicemails, emails, and text messages
- Web scraping and information retrieval
- Domain-specific operations:
  - Insurance business management (Flo Faction Insurance)
  - Trading and investment (stocks, crypto, etc.)
  - Social media marketing and content creation
  - Music industry management and promotion

## Architecture

The system is built with a modular architecture:
- Core Framework: Orchestration, communication, state management
- LLM Integration: Connections to various language models
- Specialized Agents: Domain-specific AI agents
- External Service Connectors: Integrations with external platforms and services

## Specialized Agents

### Insurance Agent
Manages Flo Faction Insurance business operations, including policy management, customer inquiries, claims processing, and quote generation.

### Trading Agent
Handles trading and investment operations across various asset classes, including market analysis, trade execution, portfolio management, and strategy development.

### Social Media Agent
Manages social media marketing and content creation, including post scheduling, audience analysis, campaign management, and engagement monitoring.

### Music Agent
Specializes in music industry management and promotion, including metadata management, sync licensing, EPK creation, artwork design, and release promotion.

### Communication Agent
Handles all forms of communication, including emails, SMS, phone calls, and voicemails, with template management and scheduled communication capabilities.

### Research Agent
Performs web scraping and information gathering, including:
- Web search and content scraping
- Information summarization and topic monitoring
- GitHub repository analysis and code search
- Hugging Face model research and technical documentation
- Knowledge management and technical documentation generation

## Setup and Installation

### Prerequisites
- Python 3.9+
- Required API keys for LLMs and external services

### Quick Setup
The easiest way to set up the system is to use the provided setup script:

```bash
# Run the setup script
python setup.py

# Activate the virtual environment
# On Windows:
.\venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Edit the .env file with your API keys and settings
# Then run the system
python main.py
```

### Manual Installation
```bash
# Create necessary directories
mkdir -p data logs models

# Create .env file from example
cp .env.example .env

# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
.\venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Edit the .env file with your API keys and settings
# Then run the system
python main.py
```

## Configuration

Edit the `.env` file to set up:
- API keys for LLMs (Anthropic, OpenAI, Grok)
- External service credentials (Twilio, SendGrid, etc.)
- GitHub and Hugging Face API keys
- Agent behavior parameters
- System operation settings

The system uses environment variables for configuration, with defaults defined in `config.py`.

### GitHub Integration

To enable GitHub integration:
1. Create a GitHub Personal Access Token with `repo` scope
2. Add the token to your `.env` file:
   ```
   GITHUB_API_KEY=your_github_token
   GITHUB_USERNAME=your_github_username
   ENABLE_GITHUB=True
   ```

### Hugging Face Integration

To enable Hugging Face integration:
1. Create a Hugging Face API token
2. Add the token to your `.env` file:
   ```
   HUGGINGFACE_API_KEY=your_huggingface_token
   ENABLE_HUGGINGFACE=True
   ```

### Google Services Integration

The system supports integration with Google services for email communication and file management:

#### Gmail Integration
To enable Gmail integration:
1. Configure your email accounts in `config/email_accounts.json`
2. Run the setup script:
   ```bash
   # Set up all configured email accounts
   python setup_google_services.py --gmail

   # Set up a specific email account
   python setup_google_services.py --gmail-account <EMAIL>
   ```
3. Follow the prompts to set up OAuth 2.0 credentials for each account
4. The first time you use the Gmail service, you'll need to authorize the application for each account

The system supports multiple Gmail accounts with priority-based handling. You can use the Multi-Account Email Agent to manage all your accounts:

#### Google Drive Integration
To enable Google Drive integration:
1. Run the setup script:
   ```bash
   python setup_google_services.py --drive
   ```
2. Follow the prompts to set up OAuth 2.0 credentials
3. The first time you use the Google Drive service, you'll need to authorize the application

You can also set up both services at once:
```bash
python setup_google_services.py --all
```

#### Running Local Models

You can run Hugging Face models locally using the provided script:
```bash
# Run a local model server with Mistral-7B
python run_local_model_server.py --model mistralai/Mistral-7B-Instruct-v0.2 --quantize

# Run with both text generation and embedding models
python run_local_model_server.py --model mistralai/Mistral-7B-Instruct-v0.2 --embedding-model sentence-transformers/all-MiniLM-L6-v2 --quantize

# Then configure the system to use the local server
# In your .env file:
HUGGINGFACE_USE_LOCAL=True
HUGGINGFACE_LOCAL_URL=http://localhost:8080
```

The local model server supports:
- Text generation via the `/generate` endpoint
- Chat completion via the `/chat` endpoint
- Text embeddings via the `/embeddings` endpoint (requires specifying an embedding model)

## Usage

### Starting the System
```bash
python main.py
```

### Web Interface
The system includes a web interface for monitoring and controlling the agents. By default, it runs on http://localhost:8000.

### Interacting with Agents
Agents can be controlled through:
1. The web interface
2. API calls to the web interface
3. Direct messages to the agent message queue

### Adding New Capabilities
The system is designed to be extensible. To add new capabilities:
1. Create a new agent class extending `BaseAgent`
2. Implement the required methods
3. Add the agent configuration to `config.py`
4. The agent will be automatically loaded on system startup

## Testing

You can test individual components using the provided scripts:

- Test system initialization:
  ```bash
  python tests/test_initialization.py
  ```

- Run a specific agent:
  ```bash
  python run_agent.py insurance
  ```

- Test LLM integration:
  ```bash
  python test_llm.py
  ```

- Test communication services:
  ```bash
  python test_communication.<NAME_EMAIL>
  ```

- Test Gmail integration:
  ```bash
  # Test single account email agent
  python test_email_agent.py --interactive

  # Test multi-account email agent
  python test_multi_account_email_agent.py --interactive
  ```

- Test trading services:
  ```bash
  python test_trading.py market alpaca AAPL
  ```

- Test web scraper:
  ```bash
  python test_scraper.py https://example.com
  ```

- Test GitHub integration:
  ```bash
  # Search for code
  python test_github_huggingface.py github-search "multi-agent system" --language python

  # Get repository information
  python test_github_huggingface.py github-repo "huggingface/transformers"
  ```

- Test Hugging Face integration:
  ```bash
  # Search for models
  python test_github_huggingface.py hf-search "text-to-image" --task "text-to-image"

  # Get model information
  python test_github_huggingface.py hf-model "mistralai/Mistral-7B-Instruct-v0.2"
  ```

- Monitor system state:
  ```bash
  python monitor.py
  ```

- Run web interface only:
  ```bash
  python run_web_interface.py
  ```

## Development

### Project Structure
```
├── agents/                 # Specialized agent implementations
│   ├── email_agent.py      # Email agent for Gmail integration
│   └── ...                 # Other specialized agents
├── core/                   # Core framework components
├── llm/                    # LLM integration
│   ├── huggingface_connector.py # Hugging Face integration
│   └── ...                 # Other LLM connectors
├── services/               # External service connectors
│   ├── gmail_service.py    # Gmail service integration
│   ├── google_drive_service.py # Google Drive integration
│   └── ...                 # Other service connectors
├── utils/                  # Utility functions
├── data/                   # Data storage (created at runtime)
├── logs/                   # Log files (created at runtime)
├── models/                 # Model storage (created at runtime)
├── credentials/            # API credentials (created at runtime)
├── config.py               # Configuration settings
├── main.py                 # Main entry point
├── requirements.txt        # Dependencies
├── setup.py                # Setup script
├── setup_google_services.py # Google services setup script
├── test_email_agent.py     # Email agent test script
└── run_local_model_server.py # Local model server for Hugging Face models
```

### Adding New Agents
See the existing agent implementations in the `agents/` directory for examples.

### Adding New LLM Providers
See the existing LLM connectors in the `llm/` directory for examples.

## License

MIT License

Copyright (c) 2023 Flo Faction LLC

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
