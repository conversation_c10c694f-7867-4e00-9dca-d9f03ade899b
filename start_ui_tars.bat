@echo off
echo Starting UI-TARS with Enhanced Configuration
echo ==========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Ask for UI-TARS path
echo Enter the path to UI-TARS executable (leave empty to auto-detect):
set /p UI_TARS_PATH=""

REM Ask for configuration path
echo.
echo Enter the path to UI-TARS configuration file (leave empty to auto-detect):
set /p CONFIG_PATH=""

REM Ask for browser type
echo.
echo Select browser type:
echo 1. Auto-detect (default)
echo 2. Chrome
echo 3. Edge
echo 4. Firefox
echo 5. Brave
echo.
set /p BROWSER_CHOICE="Enter choice (1-5): "

if "%BROWSER_CHOICE%"=="2" (
    set BROWSER_TYPE=chrome
) else if "%BROWSER_CHOICE%"=="3" (
    set BROWSER_TYPE=edge
) else if "%BROWSER_CHOICE%"=="4" (
    set BROWSER_TYPE=firefox
) else if "%BROWSER_CHOICE%"=="5" (
    set BROWSER_TYPE=brave
) else (
    set BROWSER_TYPE=
)

REM Run the start script
echo.
echo Starting UI-TARS...
echo.

set COMMAND=python start_ui_tars.py

if not "%UI_TARS_PATH%"=="" (
    set COMMAND=%COMMAND% --path "%UI_TARS_PATH%"
)

if not "%CONFIG_PATH%"=="" (
    set COMMAND=%COMMAND% --config "%CONFIG_PATH%"
)

if not "%BROWSER_TYPE%"=="" (
    set COMMAND=%COMMAND% --browser %BROWSER_TYPE%
)

set COMMAND=%COMMAND% --debug

echo Executing: %COMMAND%
echo.

%COMMAND%

echo.
if %errorlevel% equ 0 (
    echo UI-TARS is now running with enhanced configuration!
    echo You can use it with your AI agent system.
) else (
    echo Failed to start UI-TARS. Please check the error messages above.
)

echo.
pause
