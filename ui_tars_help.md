# UI-TARS Configuration Help

## Troubleshooting UI-TARS Configuration

If you're having trouble importing the configuration into UI-TARS, here are some alternative approaches:

### 1. Manual Configuration

1. Open UI-TARS
2. Go to Settings
3. Select "Local" as the provider
4. Enter the following information:
   - Model Path: `C:\Users\<USER>\models\UI-TARS-1.5-7B`
   - Model Name: `UI-TARS-1.5-7B`
   - Check "Use Local Model"
   - Uncheck any API Key fields

### 2. Try Different Configuration Files

We've created multiple configuration files for you to try:

- `ui_tars_import.json` - The original configuration file
- `ui_tars_simple_import.json` - A simplified version
- `ui_tars_config_v2.json` - An alternative format

Try importing each of these files in UI-TARS.

### 3. Check UI-TARS Version

Make sure you're using UI-TARS version 0.1.0, which is the latest version from the official repository.

### 4. Verify Model Directory

Make sure the model directory contains the necessary files:
- `C:\Users\<USER>\models\UI-TARS-1.5-7B\config.json`
- `C:\Users\<USER>\models\UI-TARS-1.5-7B\model.bin`

### 5. Restart UI-TARS

Sometimes restarting UI-TARS can help resolve configuration issues:

1. Close all UI-TARS windows
2. Open Task Manager and end any remaining UI-TARS processes
3. Start UI-TARS again
4. Try importing the configuration

### 6. Check UI-TARS Documentation

The official UI-TARS documentation may have specific instructions for your version:
- GitHub: https://github.com/bytedance/UI-TARS-desktop

## Additional Information

- UI-TARS is installed at: `C:\Users\<USER>\AppData\Local\UI-TARS\UI-TARS.exe`
- The model directory is: `C:\Users\<USER>\models\UI-TARS-1.5-7B`
- The configuration files are in your current directory: `C:\Users\<USER>\Documents\augment-projects\Ai Agent System`
