@echo off
echo Gmail Browser Automation
echo ======================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Install required packages if not already installed
echo Checking and installing required packages...
pip install -r email_automation_requirements.txt
if %errorlevel% neq 0 (
    echo Failed to install required packages. Please check your internet connection.
    exit /b 1
)

REM Ask for browser
echo.
echo Choose a browser:
echo 1. Chrome (recommended)
echo 2. Firefox
echo 3. Edge
echo.
set /p BROWSER_CHOICE="Enter your choice (1-3, default: 1): "

if "%BROWSER_CHOICE%"=="2" (
    set BROWSER=firefox
) else if "%BROWSER_CHOICE%"=="3" (
    set BROWSER=edge
) else (
    set BROWSER=chrome
)

REM Get email details
echo.
set FROM_EMAIL=<EMAIL>
set TO_EMAIL=<EMAIL>
set /p SUBJECT="Enter subject (default: Test Email from AI Agent System): "
if "%SUBJECT%"=="" set SUBJECT=Test Email from AI Agent System
echo.
echo Enter body (type your message and press Enter twice when done):
echo -------------------------------------------------------------
set BODY=
:body_loop
set /p LINE=""
if "%LINE%"=="" goto body_done
set BODY=%BODY%%LINE%^

goto body_loop
:body_done

if "%BODY%"=="" set BODY=This is a test email sent using browser automation integrated with the AI Agent System.

REM Run the Gmail Browser Automation
echo.
echo Sending email from %FROM_EMAIL% to %TO_EMAIL% using %BROWSER%...
echo.

python gmail_browser_automation.py --email "%FROM_EMAIL%" --to "%TO_EMAIL%" --subject "%SUBJECT%" --body "%BODY%" --browser "%BROWSER%"

echo.
if %errorlevel% equ 0 (
    echo Email sent successfully!
) else (
    echo There was an issue sending the email. Please check the logs.
)
echo.
pause
