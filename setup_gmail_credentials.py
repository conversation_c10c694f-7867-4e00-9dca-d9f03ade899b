"""
Setup script for Gmail credentials for multiple accounts.
This script helps you set up the necessary credentials for Gmail integration.
"""
import os
import sys
import json
import webbrowser
from pathlib import Path

def create_credentials_directory():
    """Create the credentials directory if it doesn't exist."""
    os.makedirs('credentials', exist_ok=True)
    print("Created credentials directory.")

def load_email_accounts():
    """Load email accounts from configuration file."""
    accounts_config_path = 'config/email_accounts.json'
    if not os.path.exists(accounts_config_path):
        print(f"Error: Email accounts configuration file not found at {accounts_config_path}")
        print("Please make sure the file exists.")
        return []

    with open(accounts_config_path, 'r') as f:
        accounts_config = json.load(f)

    # Combine priority and additional accounts
    all_accounts = []
    for account in accounts_config.get('priority_accounts', []):
        all_accounts.append(account)
    for account in accounts_config.get('additional_accounts', []):
        all_accounts.append(account)

    return all_accounts

def setup_gmail_account(email):
    """
    Set up Gmail credentials for a specific account.

    Args:
        email (str): Email address to set up
    """
    print(f"\n=== Setting up Gmail for {email} ===")
    print("To use Gmail with your AI agents, you need to create OAuth 2.0 credentials.")
    print("Follow these steps:")
    print("1. Go to https://console.cloud.google.com/")
    print("2. Create a new project or select an existing one")
    print("3. Enable the Gmail API for your project")
    print("4. Create OAuth 2.0 credentials (Desktop application)")
    print("5. Download the credentials JSON file")

    # Create a safe filename from the email address
    safe_email = email.replace("@", "_at_").replace(".", "_dot_")
    credentials_path = f'credentials/gmail_{safe_email}_credentials.json'

    if os.path.exists(credentials_path):
        print(f"\nGmail credentials already exist at {credentials_path}")
        replace = input("Do you want to replace them? (y/n): ").lower()
        if replace != 'y':
            print(f"Keeping existing Gmail credentials for {email}.")
            return

    # Open Google Cloud Console
    print("\nOpening Google Cloud Console in your browser...")
    webbrowser.open("https://console.cloud.google.com/apis/credentials")

    print("\nInstructions:")
    print("1. Create a new project or select an existing one")
    print("2. Enable the Gmail API for your project")
    print("3. Create OAuth 2.0 credentials (Desktop application)")
    print("4. Download the credentials JSON file")
    print(f"5. Save the file as '{credentials_path}'")
    
    input("\nPress Enter when you've downloaded the credentials file...")
    
    # Create the credentials directory if it doesn't exist
    os.makedirs(os.path.dirname(credentials_path), exist_ok=True)
    
    # Ask for the path to the downloaded file
    downloaded_file = input("\nEnter the path to the downloaded credentials file: ")
    
    # Copy the file to the correct location
    if os.path.exists(downloaded_file):
        import shutil
        shutil.copy(downloaded_file, credentials_path)
        print(f"Credentials file copied to {credentials_path}")
    else:
        print(f"Error: Could not find file at {downloaded_file}")
        manual_path = input("Would you like to manually enter the contents of the credentials file? (y/n): ").lower()
        if manual_path == 'y':
            print("Please paste the contents of the credentials file (press Enter, then Ctrl+D when done):")
            contents = sys.stdin.read()
            with open(credentials_path, 'w') as f:
                f.write(contents)
            print(f"Credentials saved to {credentials_path}")

    if os.path.exists(credentials_path):
        print(f"Gmail credentials for {email} saved successfully!")
        print("The first time you use the Gmail service, you'll need to authorize the application.")
    else:
        print(f"Error: Could not find credentials file at {credentials_path}")
        print("Please make sure you've saved the file correctly.")

def main():
    """Main entry point."""
    print("=== Gmail Credentials Setup ===")
    print("This script will help you set up Gmail credentials for multiple accounts.")

    # Create credentials directory
    create_credentials_directory()

    # Load email accounts
    all_accounts = load_email_accounts()
    if not all_accounts:
        print("No email accounts found in the configuration.")
        return

    # Display available accounts
    print("\nAvailable email accounts:")
    for i, account in enumerate(all_accounts):
        print(f"{i+1}. {account['email']} - {account['description']}")

    print(f"{len(all_accounts)+1}. All accounts")

    # Get user selection
    selection = input("\nSelect an account to set up (or 'all' for all accounts): ")

    if selection.lower() == 'all' or selection == str(len(all_accounts)+1):
        # Set up all accounts
        for account in all_accounts:
            setup_gmail_account(account['email'])
        return

    try:
        index = int(selection) - 1
        if 0 <= index < len(all_accounts):
            email = all_accounts[index]['email']
            setup_gmail_account(email)
        else:
            print("Invalid selection.")
            return
    except ValueError:
        print("Invalid selection.")
        return

if __name__ == "__main__":
    main()
