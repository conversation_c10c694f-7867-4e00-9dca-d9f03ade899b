"""
Agent System Integration with UI-TARS for Email Automation

This script integrates the UI-TARS email automation with the agent system.
It provides a way for agents to send emails using UI-TARS browser automation.
"""
import os
import sys
import asyncio
import logging
import argparse
from typing import Dict, Optional, Any, List

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("agent_ui_tars_email")

# Import UI-TARS Gmail automation
try:
    from ui_tars_gmail_automation import GmailUITarsAutomation
except ImportError:
    logger.error("UI-TARS Gmail automation not found. Make sure ui_tars_gmail_automation.py is in the current directory.")
    sys.exit(1)

# Try to import agent system components
try:
    from agents.base_agent import BaseAgent
    from services.gmail_service import GmailService
    from core.logger import setup_logger
except ImportError:
    logger.warning("Agent system components not found. Running in standalone mode.")
    BaseAgent = object
    GmailService = object
    setup_logger = lambda x: logging.getLogger(x)

class EmailUITarsAgent(BaseAgent):
    """Agent for sending emails using UI-TARS browser automation."""
    
    def __init__(self, 
                 name: str = "email_ui_tars_agent",
                 ui_tars_api_url: str = "http://localhost:8080",
                 ui_tars_model_name: str = "UI-TARS-1.5-7B",
                 ui_tars_installation_path: Optional[str] = None,
                 gmail_service: Optional[GmailService] = None):
        """
        Initialize the Email UI-TARS Agent.
        
        Args:
            name (str): Name of the agent
            ui_tars_api_url (str): URL of the UI-TARS API
            ui_tars_model_name (str): Name of the model to use
            ui_tars_installation_path (Optional[str]): Path to UI-TARS installation
            gmail_service (Optional[GmailService]): Gmail service for API-based email sending
        """
        super().__init__(name=name)
        
        self.ui_tars_api_url = ui_tars_api_url
        self.ui_tars_model_name = ui_tars_model_name
        self.ui_tars_installation_path = ui_tars_installation_path
        self.gmail_service = gmail_service
        
        self.ui_tars_automation = None
        self.logger = setup_logger(f"agent.{name}")
    
    async def initialize(self) -> bool:
        """
        Initialize the Email UI-TARS Agent.
        
        Returns:
            bool: True if initialization was successful, False otherwise
        """
        self.logger.info("Initializing Email UI-TARS Agent")
        
        # Initialize base agent
        await super().initialize()
        
        # Create UI-TARS Gmail automation
        self.ui_tars_automation = GmailUITarsAutomation(
            api_url=self.ui_tars_api_url,
            model_name=self.ui_tars_model_name,
            installation_path=self.ui_tars_installation_path
        )
        
        # Initialize UI-TARS Gmail automation
        success = await self.ui_tars_automation.initialize()
        if not success:
            self.logger.error("Failed to initialize UI-TARS Gmail automation")
            return False
        
        self.logger.info("Email UI-TARS Agent initialized successfully")
        return True
    
    async def send_email_ui_tars(self, 
                               email_account: str,
                               password: str,
                               to_email: str, 
                               subject: str, 
                               body: str) -> Dict[str, Any]:
        """
        Send an email using UI-TARS browser automation.
        
        Args:
            email_account (str): Gmail account to send from
            password (str): Password for the Gmail account
            to_email (str): Recipient email address
            subject (str): Email subject
            body (str): Email body
            
        Returns:
            Dict[str, Any]: Result of the operation
        """
        self.logger.info(f"Sending email from {email_account} to {to_email} using UI-TARS")
        
        if not self.ui_tars_automation:
            self.logger.error("UI-TARS Gmail automation not initialized")
            return {"success": False, "error": "UI-TARS Gmail automation not initialized"}
        
        result = await self.ui_tars_automation.send_email(
            email_account=email_account,
            password=password,
            to_email=to_email,
            subject=subject,
            body=body
        )
        
        return result
    
    async def send_email_api(self, 
                           to_email: str, 
                           subject: str, 
                           body: str,
                           cc: Optional[str] = None,
                           bcc: Optional[str] = None) -> Dict[str, Any]:
        """
        Send an email using the Gmail API.
        
        Args:
            to_email (str): Recipient email address
            subject (str): Email subject
            body (str): Email body
            cc (Optional[str]): CC recipients
            bcc (Optional[str]): BCC recipients
            
        Returns:
            Dict[str, Any]: Result of the operation
        """
        self.logger.info(f"Sending email to {to_email} using Gmail API")
        
        if not self.gmail_service:
            self.logger.error("Gmail service not initialized")
            return {"success": False, "error": "Gmail service not initialized"}
        
        if not self.gmail_service.is_enabled():
            self.logger.error("Gmail service not enabled")
            return {"success": False, "error": "Gmail service not enabled"}
        
        result = await self.gmail_service.send_message(
            to=to_email,
            subject=subject,
            body=body,
            cc=cc,
            bcc=bcc
        )
        
        if "error" in result:
            return {"success": False, "error": result["error"]}
        
        return {"success": True, "message": "Email sent successfully", "message_id": result.get("message_id")}
    
    async def shutdown(self) -> None:
        """Shut down the Email UI-TARS Agent."""
        if self.ui_tars_automation:
            await self.ui_tars_automation.shutdown()
        self.logger.info("Email UI-TARS Agent shut down")

async def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Agent System Integration with UI-TARS for Email Automation")
    parser.add_argument("--email", type=str, default="<EMAIL>", help="Gmail account to send from")
    parser.add_argument("--password", type=str, required=True, help="Password for the Gmail account")
    parser.add_argument("--to", type=str, default="<EMAIL>", help="Recipient email address")
    parser.add_argument("--subject", type=str, default="Test Email from AI Agent System using UI-TARS", help="Email subject")
    parser.add_argument("--body", type=str, default="This is a test email sent using UI-TARS browser automation integrated with the AI Agent System.", help="Email body")
    parser.add_argument("--api-url", type=str, default="http://localhost:8080", help="URL of the UI-TARS API")
    parser.add_argument("--model", type=str, default="UI-TARS-1.5-7B", help="Name of the model to use")
    parser.add_argument("--installation-path", type=str, help="Path to UI-TARS installation")
    parser.add_argument("--method", type=str, choices=["ui-tars", "api"], default="ui-tars", help="Method to use for sending email")
    
    args = parser.parse_args()
    
    # Create Email UI-TARS Agent
    agent = EmailUITarsAgent(
        ui_tars_api_url=args.api_url,
        ui_tars_model_name=args.model,
        ui_tars_installation_path=args.installation_path
    )
    
    # Initialize
    initialized = await agent.initialize()
    if not initialized:
        logger.error("Failed to initialize Email UI-TARS Agent")
        return
    
    try:
        # Send email
        if args.method == "ui-tars":
            result = await agent.send_email_ui_tars(
                email_account=args.email,
                password=args.password,
                to_email=args.to,
                subject=args.subject,
                body=args.body
            )
        else:
            result = await agent.send_email_api(
                to_email=args.to,
                subject=args.subject,
                body=args.body
            )
        
        if result["success"]:
            logger.info("Email sent successfully")
        else:
            logger.error(f"Failed to send email: {result['error']}")
    
    finally:
        # Shut down
        await agent.shutdown()

if __name__ == "__main__":
    asyncio.run(main())
