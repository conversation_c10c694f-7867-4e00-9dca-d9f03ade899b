"""
Music Agent for music industry management and promotion.
"""
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import json
import re
import uuid
import random

from agents.base_agent import BaseAgent
from core.logger import setup_logger
from llm.llm_router import LLMRouter

class MusicAgent(BaseAgent):
    """
    Agent specialized for music industry management and promotion.
    
    This agent handles tasks related to music promotion, metadata management,
    sync licensing opportunities, EPK creation, and album artwork design.
    """
    
    def __init__(
        self,
        agent_id: str,
        config: Dict,
        state_manager,
        message_queue,
        shutdown_event
    ):
        """Initialize the music agent."""
        super().__init__(agent_id, config, state_manager, message_queue, shutdown_event)
        
        # Music-specific configuration
        self.llm_provider = config.get("llm_provider", "anthropic")
        self.llm_router = None
        
        # Music data
        self.catalog = {}
        self.releases = {}
        self.licensing_opportunities = {}
        self.contacts = {}
        self.promotion_campaigns = {}
        
        # Agent capabilities
        self.capabilities = [
            "metadata_management",
            "sync_licensing",
            "epk_creation",
            "artwork_design",
            "release_promotion",
            "contact_management",
        ]
    
    async def initialize(self):
        """Initialize the music agent."""
        await super().initialize()
        
        # Initialize LLM router
        self.llm_router = LLMRouter()
        await self.llm_router.initialize()
        
        # Load music data
        await self._load_music_data()
        
        self.logger.info(f"Music agent initialized with provider: {self.llm_provider}")
    
    async def _load_music_data(self):
        """Load music data from state manager."""
        # Load catalog
        catalog_data = await self.state_manager.get_state("music", "catalog")
        if catalog_data:
            self.catalog = catalog_data
            self.logger.info(f"Loaded music catalog with {len(self.catalog)} tracks")
        
        # Load releases
        releases_data = await self.state_manager.get_state("music", "releases")
        if releases_data:
            self.releases = releases_data
            self.logger.info(f"Loaded {len(self.releases)} releases")
        
        # Load licensing opportunities
        licensing_data = await self.state_manager.get_state("music", "licensing_opportunities")
        if licensing_data:
            self.licensing_opportunities = licensing_data
            self.logger.info(f"Loaded {len(self.licensing_opportunities)} licensing opportunities")
        
        # Load contacts
        contacts_data = await self.state_manager.get_state("music", "contacts")
        if contacts_data:
            self.contacts = contacts_data
            self.logger.info(f"Loaded {len(self.contacts)} industry contacts")
        
        # Load promotion campaigns
        campaigns_data = await self.state_manager.get_state("music", "promotion_campaigns")
        if campaigns_data:
            self.promotion_campaigns = campaigns_data
            self.logger.info(f"Loaded {len(self.promotion_campaigns)} promotion campaigns")
    
    async def execute_cycle(self):
        """Execute one cycle of the music agent's logic."""
        self.logger.debug("Executing music agent cycle")
        
        try:
            # Check for pending tasks
            pending_tasks = await self.state_manager.get_state("music", "pending_tasks")
            if pending_tasks:
                for task_id, task in pending_tasks.items():
                    if task.get("status") == "pending":
                        await self._process_task(task_id, task)
            
            # Check for licensing opportunities
            await self._check_licensing_opportunities()
            
            # Check for release promotion needs
            await self._check_release_promotion()
            
            # Update state with any changes
            await self._save_music_data()
            
        except Exception as e:
            self.logger.exception(f"Error in music agent cycle: {e}")
    
    async def _process_task(self, task_id: str, task: Dict):
        """
        Process a pending task.
        
        Args:
            task_id (str): Task identifier
            task (Dict): Task data
        """
        task_type = task.get("type")
        self.logger.info(f"Processing task: {task_id} ({task_type})")
        
        try:
            if task_type == "metadata_update":
                await self._handle_metadata_update(task)
            elif task_type == "epk_creation":
                await self._handle_epk_creation(task)
            elif task_type == "artwork_design":
                await self._handle_artwork_design(task)
            elif task_type == "release_planning":
                await self._handle_release_planning(task)
            elif task_type == "contact_research":
                await self._handle_contact_research(task)
            else:
                self.logger.warning(f"Unknown task type: {task_type}")
                return
            
            # Update task status
            task["status"] = "completed"
            task["completed_at"] = datetime.now().isoformat()
            
            # Update pending tasks
            pending_tasks = await self.state_manager.get_state("music", "pending_tasks") or {}
            pending_tasks[task_id] = task
            await self.state_manager.update_state("music", "pending_tasks", pending_tasks)
            
        except Exception as e:
            self.logger.exception(f"Error processing task {task_id}: {e}")
            
            # Update task status
            task["status"] = "error"
            task["error"] = str(e)
            
            # Update pending tasks
            pending_tasks = await self.state_manager.get_state("music", "pending_tasks") or {}
            pending_tasks[task_id] = task
            await self.state_manager.update_state("music", "pending_tasks", pending_tasks)
    
    async def _handle_metadata_update(self, task: Dict):
        """
        Handle a metadata update task.
        
        Args:
            task (Dict): Task data
        """
        track_id = task.get("track_id")
        metadata_updates = task.get("metadata_updates", {})
        
        # Check if track exists
        if track_id not in self.catalog:
            raise ValueError(f"Track not found: {track_id}")
        
        track = self.catalog[track_id]
        
        # Update metadata
        for key, value in metadata_updates.items():
            if key in ["title", "artist", "album", "genre", "bpm", "key", "isrc", "release_date", "lyrics", "tags"]:
                track[key] = value
        
        # Update track in catalog
        track["updated_at"] = datetime.now().isoformat()
        self.catalog[track_id] = track
        
        # Store updated track in task
        task["updated_track"] = track
    
    async def _handle_epk_creation(self, task: Dict):
        """
        Handle an EPK (Electronic Press Kit) creation task.
        
        Args:
            task (Dict): Task data
        """
        artist_name = task.get("artist_name")
        bio = task.get("bio", "")
        releases = task.get("releases", [])
        press_quotes = task.get("press_quotes", [])
        social_links = task.get("social_links", {})
        contact_info = task.get("contact_info", {})
        
        # Generate EPK content using LLM
        prompt = f"""
        You are a music industry professional creating an Electronic Press Kit (EPK) for {artist_name}.
        
        Artist Bio:
        {bio}
        
        Releases:
        {json.dumps(releases, indent=2)}
        
        Press Quotes:
        {json.dumps(press_quotes, indent=2)}
        
        Please create a comprehensive EPK that includes:
        1. A professional artist bio
        2. Release highlights
        3. Press quotes and testimonials
        4. Suggested press angles
        5. Contact information
        
        Format the EPK in a professional, industry-standard way.
        """
        
        response = await self.llm_router.generate_text(
            prompt=prompt,
            provider=self.llm_provider,
            max_tokens=1200,
            temperature=0.7
        )
        
        # Store EPK content in task
        task["epk_content"] = response.get("text")
        
        # Create EPK entry
        epk_id = str(uuid.uuid4())
        
        if "epks" not in self.releases:
            self.releases["epks"] = {}
        
        self.releases["epks"][epk_id] = {
            "id": epk_id,
            "artist_name": artist_name,
            "content": response.get("text"),
            "bio": bio,
            "releases": releases,
            "press_quotes": press_quotes,
            "social_links": social_links,
            "contact_info": contact_info,
            "created_at": datetime.now().isoformat(),
        }
        
        # Store EPK ID in task
        task["epk_id"] = epk_id
    
    async def _handle_artwork_design(self, task: Dict):
        """
        Handle an artwork design task.
        
        Args:
            task (Dict): Task data
        """
        release_title = task.get("release_title")
        artist_name = task.get("artist_name")
        release_type = task.get("release_type", "single")  # single, EP, album
        genre = task.get("genre", "")
        mood = task.get("mood", "")
        concept = task.get("concept", "")
        
        # Generate artwork concept using LLM
        prompt = f"""
        You are a graphic designer creating album artwork for a {release_type} titled "{release_title}" by {artist_name}.
        
        Genre: {genre}
        Mood: {mood}
        Concept: {concept}
        
        Please provide:
        1. A detailed description of the artwork concept
        2. Color palette suggestions
        3. Typography recommendations
        4. Layout ideas
        5. Visual elements to include
        
        The description should be detailed enough that it could be used to brief a designer or generate an image using AI tools.
        """
        
        response = await self.llm_router.generate_text(
            prompt=prompt,
            provider=self.llm_provider,
            max_tokens=1000,
            temperature=0.8
        )
        
        # Store artwork concept in task
        task["artwork_concept"] = response.get("text")
        
        # Create artwork entry
        artwork_id = str(uuid.uuid4())
        
        if "artwork" not in self.releases:
            self.releases["artwork"] = {}
        
        self.releases["artwork"][artwork_id] = {
            "id": artwork_id,
            "release_title": release_title,
            "artist_name": artist_name,
            "release_type": release_type,
            "genre": genre,
            "mood": mood,
            "concept": concept,
            "design_description": response.get("text"),
            "created_at": datetime.now().isoformat(),
            "status": "concept",
        }
        
        # Store artwork ID in task
        task["artwork_id"] = artwork_id
    
    async def _handle_release_planning(self, task: Dict):
        """
        Handle a release planning task.
        
        Args:
            task (Dict): Task data
        """
        release_title = task.get("release_title")
        artist_name = task.get("artist_name")
        release_type = task.get("release_type", "single")
        track_ids = task.get("track_ids", [])
        release_date = task.get("release_date")
        
        # Validate tracks exist
        for track_id in track_ids:
            if track_id not in self.catalog:
                raise ValueError(f"Track not found: {track_id}")
        
        # Generate release plan using LLM
        prompt = f"""
        You are a music release strategist planning the release of a {release_type} titled "{release_title}" by {artist_name}, scheduled for {release_date}.
        
        Please create a comprehensive release plan that includes:
        1. Pre-release strategy (6-8 weeks before release)
        2. Release day strategy
        3. Post-release promotion (4-6 weeks after release)
        4. Playlist pitching strategy
        5. Social media content plan
        6. Press and blog outreach recommendations
        7. Budget allocation suggestions
        
        The plan should be detailed, actionable, and follow current music industry best practices.
        """
        
        response = await self.llm_router.generate_text(
            prompt=prompt,
            provider=self.llm_provider,
            max_tokens=1500,
            temperature=0.7
        )
        
        # Store release plan in task
        task["release_plan"] = response.get("text")
        
        # Create release entry
        release_id = str(uuid.uuid4())
        
        self.releases[release_id] = {
            "id": release_id,
            "title": release_title,
            "artist_name": artist_name,
            "release_type": release_type,
            "track_ids": track_ids,
            "release_date": release_date,
            "plan": response.get("text"),
            "created_at": datetime.now().isoformat(),
            "status": "planned",
        }
        
        # Store release ID in task
        task["release_id"] = release_id
        
        # Create promotion campaign
        campaign_id = str(uuid.uuid4())
        
        self.promotion_campaigns[campaign_id] = {
            "id": campaign_id,
            "release_id": release_id,
            "title": f"{release_title} Promotion",
            "start_date": datetime.fromisoformat(release_date).replace(
                day=datetime.fromisoformat(release_date).day - 42  # 6 weeks before
            ).isoformat(),
            "end_date": datetime.fromisoformat(release_date).replace(
                day=datetime.fromisoformat(release_date).day + 42  # 6 weeks after
            ).isoformat(),
            "plan": response.get("text"),
            "created_at": datetime.now().isoformat(),
            "status": "planned",
        }
        
        # Store campaign ID in task
        task["campaign_id"] = campaign_id
    
    async def _handle_contact_research(self, task: Dict):
        """
        Handle a contact research task.
        
        Args:
            task (Dict): Task data
        """
        contact_type = task.get("contact_type")  # e.g., "sync licensing", "playlist curator", "music blogger"
        genre = task.get("genre", "")
        region = task.get("region", "")
        
        # Generate contact list using LLM
        prompt = f"""
        You are a music industry researcher finding contacts in the category of {contact_type} for {genre} music in {region if region else "all regions"}.
        
        Please provide a list of 10 relevant contacts that includes:
        1. Name
        2. Company/Organization
        3. Role/Position
        4. Brief description of what they do
        5. Why they would be a good contact
        6. Suggested approach for outreach
        
        Note: Provide realistic contacts that would exist in the music industry, but do not include actual email addresses or phone numbers.
        """
        
        response = await self.llm_router.generate_text(
            prompt=prompt,
            provider=self.llm_provider,
            max_tokens=1500,
            temperature=0.8
        )
        
        # Store contact research in task
        task["contact_research"] = response.get("text")
        
        # Parse contacts (in a real implementation, this would be more sophisticated)
        # For now, we'll just store the raw text
        
        # Create contact list entry
        list_id = str(uuid.uuid4())
        
        if "contact_lists" not in self.contacts:
            self.contacts["contact_lists"] = {}
        
        self.contacts["contact_lists"][list_id] = {
            "id": list_id,
            "type": contact_type,
            "genre": genre,
            "region": region,
            "research": response.get("text"),
            "created_at": datetime.now().isoformat(),
        }
        
        # Store list ID in task
        task["contact_list_id"] = list_id
    
    async def _check_licensing_opportunities(self):
        """Check for sync licensing opportunities."""
        # In a real implementation, this would check external sources
        # For now, we'll just simulate random opportunities
        
        if random.random() < 0.1:  # 10% chance of finding an opportunity
            opportunity_id = str(uuid.uuid4())
            
            opportunity = {
                "id": opportunity_id,
                "type": random.choice(["film", "tv", "commercial", "video game"]),
                "description": f"Simulated licensing opportunity for {random.choice(['drama', 'comedy', 'action', 'documentary'])}",
                "deadline": (datetime.now() + timedelta(days=random.randint(7, 30))).isoformat(),
                "requirements": random.choice(["upbeat", "emotional", "dramatic", "quirky"]),
                "discovered_at": datetime.now().isoformat(),
                "status": "new",
            }
            
            self.licensing_opportunities[opportunity_id] = opportunity
            
            self.logger.info(f"Found new licensing opportunity: {opportunity['type']} - {opportunity['description']}")
            
            # Create task to evaluate opportunity
            task_id = f"TASK-LICENSE-{datetime.now().strftime('%Y%m%d%H%M%S')}"
            
            task = {
                "task_id": task_id,
                "type": "licensing_evaluation",
                "opportunity_id": opportunity_id,
                "created_at": datetime.now().isoformat(),
                "status": "pending",
            }
            
            # Add task to pending tasks
            pending_tasks = await self.state_manager.get_state("music", "pending_tasks") or {}
            pending_tasks[task_id] = task
            await self.state_manager.update_state("music", "pending_tasks", pending_tasks)
    
    async def _check_release_promotion(self):
        """Check for release promotion needs."""
        current_date = datetime.now().date()
        
        for release_id, release in self.releases.items():
            # Skip if not a proper release entry
            if not isinstance(release, dict) or "release_date" not in release:
                continue
            
            release_date = datetime.fromisoformat(release["release_date"]).date()
            days_until_release = (release_date - current_date).days
            
            # Check for upcoming releases that need promotion
            if 0 < days_until_release <= 42 and release.get("status") == "planned":
                # Check if we already have a promotion task for this release
                pending_tasks = await self.state_manager.get_state("music", "pending_tasks") or {}
                has_promotion_task = False
                
                for task in pending_tasks.values():
                    if (task.get("type") == "release_promotion" and 
                        task.get("release_id") == release_id and
                        task.get("status") in ["pending", "in_progress"]):
                        has_promotion_task = True
                        break
                
                if not has_promotion_task:
                    # Create promotion task
                    task_id = f"TASK-PROMO-{datetime.now().strftime('%Y%m%d%H%M%S')}"
                    
                    task = {
                        "task_id": task_id,
                        "type": "release_promotion",
                        "release_id": release_id,
                        "days_until_release": days_until_release,
                        "created_at": datetime.now().isoformat(),
                        "status": "pending",
                    }
                    
                    # Add task to pending tasks
                    pending_tasks[task_id] = task
                    await self.state_manager.update_state("music", "pending_tasks", pending_tasks)
                    
                    self.logger.info(f"Created promotion task for release: {release['title']} ({days_until_release} days until release)")
    
    async def _save_music_data(self):
        """Save music data to state manager."""
        # Save catalog
        await self.state_manager.update_state("music", "catalog", self.catalog)
        
        # Save releases
        await self.state_manager.update_state("music", "releases", self.releases)
        
        # Save licensing opportunities
        await self.state_manager.update_state("music", "licensing_opportunities", self.licensing_opportunities)
        
        # Save contacts
        await self.state_manager.update_state("music", "contacts", self.contacts)
        
        # Save promotion campaigns
        await self.state_manager.update_state("music", "promotion_campaigns", self.promotion_campaigns)
    
    async def handle_command(self, message: Dict):
        """
        Handle a command message.
        
        Args:
            message (Dict): Command message
        """
        command = message.get("content", {}).get("command")
        
        if command == "add_track":
            # Add track to catalog
            track_data = message.get("content", {}).get("track_data", {})
            
            if not track_data.get("title") or not track_data.get("artist"):
                await self.send_message(
                    message.get("sender_id"),
                    "error",
                    {
                        "command": command,
                        "error": "Track title and artist are required",
                    }
                )
                return
            
            track_id = str(uuid.uuid4())
            
            track = {
                "id": track_id,
                "title": track_data.get("title"),
                "artist": track_data.get("artist"),
                "album": track_data.get("album", ""),
                "genre": track_data.get("genre", ""),
                "bpm": track_data.get("bpm", 0),
                "key": track_data.get("key", ""),
                "isrc": track_data.get("isrc", ""),
                "release_date": track_data.get("release_date", ""),
                "lyrics": track_data.get("lyrics", ""),
                "tags": track_data.get("tags", []),
                "created_at": datetime.now().isoformat(),
            }
            
            self.catalog[track_id] = track
            
            await self.send_message(
                message.get("sender_id"),
                "response",
                {
                    "command": command,
                    "track_id": track_id,
                    "status": "added",
                }
            )
            
        elif command == "create_epk":
            # Create EPK creation task
            task_id = f"TASK-EPK-{datetime.now().strftime('%Y%m%d%H%M%S')}"
            
            task = {
                "task_id": task_id,
                "type": "epk_creation",
                "artist_name": message.get("content", {}).get("artist_name"),
                "bio": message.get("content", {}).get("bio", ""),
                "releases": message.get("content", {}).get("releases", []),
                "press_quotes": message.get("content", {}).get("press_quotes", []),
                "social_links": message.get("content", {}).get("social_links", {}),
                "contact_info": message.get("content", {}).get("contact_info", {}),
                "created_at": datetime.now().isoformat(),
                "status": "pending",
                "requester_id": message.get("sender_id"),
            }
            
            # Add task to pending tasks
            pending_tasks = await self.state_manager.get_state("music", "pending_tasks") or {}
            pending_tasks[task_id] = task
            await self.state_manager.update_state("music", "pending_tasks", pending_tasks)
            
            # Acknowledge receipt
            await self.send_message(
                message.get("sender_id"),
                "response",
                {
                    "command": command,
                    "task_id": task_id,
                    "status": "processing",
                }
            )
            
        else:
            await super().handle_command(message)
