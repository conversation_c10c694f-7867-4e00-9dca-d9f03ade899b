Write-Host "Starting UI-TARS with <PERSON> integration..." -ForegroundColor Cyan

# Set the current directory to the script directory
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location -Path $scriptPath

# Start UI-TARS with Jarvis integration
python start_ui_tars_with_jarvis.py --interactive

Write-Host "Press any key to exit..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
