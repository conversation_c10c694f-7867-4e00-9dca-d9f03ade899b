#!/usr/bin/env python3
"""
New Client Outreach Script

This script handles the outreach process for new insurance clients/leads.
It sends an email, text message, and leaves a voicemail for the new client.
"""

import os
import sys
import json
import asyncio
import argparse
from datetime import datetime
from typing import Dict, Any, Optional, List

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import required modules
from services.gmail_service import GmailService
from services.voice_calling_service import VoiceCallingService
from core.logger import setup_logger

# Set up logger
logger = setup_logger("new_client_outreach")

class NewClientOutreach:
    """
    Handles the outreach process for new insurance clients/leads.
    """

    def __init__(self):
        """Initialize the new client outreach service."""
        self.gmail_service = None
        self.voice_calling_service = None
        self.config = self._load_config()

    def _load_config(self) -> Dict:
        """
        Load configuration from the communication_services.json file.

        Returns:
            Dict: Configuration dictionary
        """
        try:
            config_path = os.path.join("config", "communication_services.json")
            with open(config_path, "r") as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            return {}

    async def initialize(self):
        """Initialize the services."""
        try:
            # Initialize Gmail service
            email_config = self.config.get("email_integration", {})
            if email_config.get("enabled", False):
                # Get token path from config or use default
                token_path = email_config.get("gmail_token_path", "tokens/gmail_token.json")
                # Create credentials directory if it doesn't exist
                os.makedirs("credentials", exist_ok=True)
                os.makedirs("tokens", exist_ok=True)

                # Initialize Gmail service with proper paths
                self.gmail_service = GmailService(
                    credentials_path="credentials/gmail_credentials.json",
                    token_path=token_path
                )
                logger.info("Gmail service initialized")

            # Initialize voice calling service
            voice_config = self.config.get("voice_calling_service", {})
            if voice_config.get("enabled", False):
                # Create a simplified config for the voice calling service
                simplified_config = {
                    "enabled": True,
                    "default_from_number": voice_config.get("default_from_number", "(*************"),
                    "default_voice_provider": voice_config.get("default_voice_provider", "elevenlabs"),
                    "default_voice_id": voice_config.get("default_voice_id", "21m00Tcm4TlvDq8ikWAM"),
                    "elevenlabs_api_key": voice_config.get("elevenlabs_api_key", ""),
                    "twilio_account_sid": voice_config.get("twilio_account_sid", ""),
                    "twilio_auth_token": voice_config.get("twilio_auth_token", "")
                }

                self.voice_calling_service = VoiceCallingService(simplified_config)
                await self.voice_calling_service.initialize()
                logger.info("Voice calling service initialized")

        except Exception as e:
            logger.exception(f"Error initializing services: {e}")

    async def send_email(self,
                        client_name: str,
                        email: str,
                        template_name: str = "new_client",
                        template_vars: Optional[Dict] = None,
                        send_quote: bool = False) -> Dict:
        """
        Send an email to the client using a template.

        Args:
            client_name (str): Client's name
            email (str): Client's email address
            template_name (str): Template name to use
            template_vars (Optional[Dict]): Additional template variables
            send_quote (bool): Whether to send a quote email as well

        Returns:
            Dict: Email sending result
        """
        if not self.gmail_service:
            return {"error": "Gmail service not initialized"}

        if not self.gmail_service.is_enabled():
            return {"error": "Gmail service not enabled"}

        try:
            # Get email template
            email_templates = self.config.get("email_integration", {}).get("email_templates", {})
            if template_name not in email_templates:
                return {"error": f"Unknown email template: {template_name}"}

            template = email_templates[template_name]

            # Prepare template variables
            vars_dict = template_vars or {}
            vars_dict["client_name"] = client_name
            vars_dict["agent_name"] = vars_dict.get("agent_name", "Sandra")
            vars_dict["phone_number"] = vars_dict.get("phone_number", "(*************")

            # Format subject and body
            subject = template["subject"]
            body = template["body"].format(**vars_dict)

            # Send email
            result = await self.gmail_service.send_message(
                to=email,
                subject=subject,
                body=body
            )

            logger.info(f"Email sent to {email} using template {template_name}")

            # Send quote email if requested
            if send_quote and "new_client_quote" in email_templates:
                quote_template = email_templates["new_client_quote"]
                quote_subject = quote_template["subject"]

                # Add quote-specific variables
                vars_dict["insurance_type"] = vars_dict.get("insurance_type", "Auto")
                vars_dict["estimated_premium"] = vars_dict.get("estimated_premium", "150")

                quote_body = quote_template["body"].format(**vars_dict)

                # Send quote email
                quote_result = await self.gmail_service.send_message(
                    to=email,
                    subject=quote_subject,
                    body=quote_body
                )

                logger.info(f"Quote email sent to {email}")

                # Combine results
                result["quote_email"] = quote_result

            return result

        except Exception as e:
            logger.exception(f"Error sending email: {e}")
            return {"error": str(e)}

    async def send_text(self,
                       client_name: str,
                       phone_number: str,
                       template_name: str = "new_client",
                       template_vars: Optional[Dict] = None) -> Dict:
        """
        Send a text message to the client using a template.

        Args:
            client_name (str): Client's name
            phone_number (str): Client's phone number
            template_name (str): Template name to use
            template_vars (Optional[Dict]): Additional template variables

        Returns:
            Dict: Text message sending result
        """
        if not self.voice_calling_service:
            return {"error": "Voice calling service not initialized"}

        try:
            # Get text template
            text_templates = self.config.get("voice_calling_service", {}).get("text_templates", {})
            if template_name not in text_templates:
                return {"error": f"Unknown text template: {template_name}"}

            template = text_templates[template_name]

            # Prepare template variables
            vars_dict = template_vars or {}
            vars_dict["client_name"] = client_name
            vars_dict["agent_name"] = vars_dict.get("agent_name", "Sandra")
            vars_dict["phone_number"] = vars_dict.get("phone_number", "(*************")

            # Format message
            message = template.format(**vars_dict)

            # Send text message
            result = await self.voice_calling_service.send_text_message(
                phone_number=phone_number,
                message=message
            )

            logger.info(f"Text message sent to {phone_number} using template {template_name}")
            return result

        except Exception as e:
            logger.exception(f"Error sending text message: {e}")
            return {"error": str(e)}

    async def leave_voicemail(self,
                             client_name: str,
                             phone_number: str,
                             template_name: str = "new_client",
                             template_vars: Optional[Dict] = None) -> Dict:
        """
        Leave a voicemail for the client using a template.

        Args:
            client_name (str): Client's name
            phone_number (str): Client's phone number
            template_name (str): Template name to use
            template_vars (Optional[Dict]): Additional template variables

        Returns:
            Dict: Voicemail result
        """
        if not self.voice_calling_service:
            return {"error": "Voice calling service not initialized"}

        try:
            # Get voicemail template
            voicemail_templates = self.config.get("voice_calling_service", {}).get("voicemail_templates", {})
            if template_name not in voicemail_templates:
                return {"error": f"Unknown voicemail template: {template_name}"}

            template = voicemail_templates[template_name]

            # Prepare template variables
            vars_dict = template_vars or {}
            vars_dict["client_name"] = client_name
            vars_dict["agent_name"] = vars_dict.get("agent_name", "Sandra")
            vars_dict["phone_number"] = vars_dict.get("phone_number", "(*************")

            # Format message
            message = template.format(**vars_dict)

            # Set voice options
            options = {
                "voice_id": vars_dict.get("voice_id", "21m00Tcm4TlvDq8ikWAM"),  # Female friendly voice
                "provider": "elevenlabs"
            }

            # Leave voicemail
            result = await self.voice_calling_service.leave_voicemail(
                phone_number=phone_number,
                message=message,
                options=options
            )

            logger.info(f"Voicemail left for {phone_number} using template {template_name}")
            return result

        except Exception as e:
            logger.exception(f"Error leaving voicemail: {e}")
            return {"error": str(e)}

    async def make_call(self,
                       client_name: str,
                       phone_number: str,
                       template_name: str = "new_client",
                       template_vars: Optional[Dict] = None) -> Dict:
        """
        Make a call to the client using a template.

        Args:
            client_name (str): Client's name
            phone_number (str): Client's phone number
            template_name (str): Template name to use
            template_vars (Optional[Dict]): Additional template variables

        Returns:
            Dict: Call result
        """
        if not self.voice_calling_service:
            return {"error": "Voice calling service not initialized"}

        try:
            # Get call template
            call_templates = self.config.get("voice_calling_service", {}).get("call_templates", {})
            if template_name not in call_templates:
                return {"error": f"Unknown call template: {template_name}"}

            template = call_templates[template_name]

            # Prepare template variables
            vars_dict = template_vars or {}
            vars_dict["client_name"] = client_name
            vars_dict["agent_name"] = vars_dict.get("agent_name", "Sandra")

            # Format script
            script = template["script"].format(**vars_dict)

            # Set call options
            options = {
                "voice_type": template.get("voice_type", "female"),
                "voice_style": template.get("voice_style", "professional"),
                "max_duration": template.get("max_duration", 300),
                "record": template.get("record", True)
            }

            # Make call
            result = await self.voice_calling_service.make_call(
                phone_number=phone_number,
                script=script,
                options=options
            )

            logger.info(f"Call made to {phone_number} using template {template_name}")
            return result

        except Exception as e:
            logger.exception(f"Error making call: {e}")
            return {"error": str(e)}

    async def full_outreach(self,
                           client_name: str,
                           email: str,
                           phone_number: str,
                           dob: Optional[str] = None,
                           address: Optional[str] = None,
                           insurance_type: Optional[str] = None,
                           estimated_premium: Optional[str] = None,
                           agent_name: str = "Sandra",
                           notes: Optional[str] = None,
                           send_quote: bool = True) -> Dict:
        """
        Perform full outreach to a new client (email, text, voicemail).

        Args:
            client_name (str): Client's name
            email (str): Client's email address
            phone_number (str): Client's phone number
            dob (Optional[str]): Client's date of birth
            address (Optional[str]): Client's address
            insurance_type (Optional[str]): Type of insurance
            estimated_premium (Optional[str]): Estimated premium amount
            agent_name (str): Agent's name
            notes (Optional[str]): Additional notes about the client's needs
            send_quote (bool): Whether to send a quote email

        Returns:
            Dict: Results of all outreach methods
        """
        # Prepare template variables
        template_vars = {
            "agent_name": agent_name,
            "phone_number": "(*************",
            "dob": dob or "",
            "address": address or "",
            "insurance_type": insurance_type or "Auto",
            "estimated_premium": estimated_premium or "150",
            "notes": notes or ""
        }

        # Send email
        email_result = await self.send_email(
            client_name=client_name,
            email=email,
            template_name="new_client",
            template_vars=template_vars,
            send_quote=send_quote
        )

        # Send text message
        text_result = await self.send_text(
            client_name=client_name,
            phone_number=phone_number,
            template_name="new_client",
            template_vars=template_vars
        )

        # Leave voicemail
        voicemail_result = await self.leave_voicemail(
            client_name=client_name,
            phone_number=phone_number,
            template_name="new_client",
            template_vars=template_vars
        )

        # Log results
        logger.info(f"Full outreach completed for {client_name}")

        # Return combined results
        return {
            "email": email_result,
            "text": text_result,
            "voicemail": voicemail_result,
            "timestamp": datetime.now().isoformat()
        }

async def main():
    """Main function to run the script."""
    parser = argparse.ArgumentParser(description="New Client Outreach Script")
    parser.add_argument("--name", required=True, help="Client's full name")
    parser.add_argument("--email", required=True, help="Client's email address")
    parser.add_argument("--phone", required=True, help="Client's phone number")
    parser.add_argument("--dob", help="Client's date of birth (MM/DD/YY)")
    parser.add_argument("--address", help="Client's address")
    parser.add_argument("--insurance-type", help="Type of insurance")
    parser.add_argument("--premium", help="Estimated premium amount")
    parser.add_argument("--agent", default="Sandra", help="Agent's name")
    parser.add_argument("--notes", help="Additional notes about the client's needs")
    parser.add_argument("--quote", action="store_true", help="Send quote email")

    args = parser.parse_args()

    # Initialize outreach service
    outreach = NewClientOutreach()
    await outreach.initialize()

    # Perform full outreach
    result = await outreach.full_outreach(
        client_name=args.name,
        email=args.email,
        phone_number=args.phone,
        dob=args.dob,
        address=args.address,
        insurance_type=args.insurance_type,
        estimated_premium=args.premium,
        agent_name=args.agent,
        notes=args.notes,
        send_quote=args.quote
    )

    # Print results
    print(json.dumps(result, indent=2))

if __name__ == "__main__":
    asyncio.run(main())
