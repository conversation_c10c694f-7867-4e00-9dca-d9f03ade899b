"""
Test Gmail Integration.

This script tests the Gmail integration by sending a test email.
"""
import os
import sys
import asyncio
import argparse
import logging
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent))

try:
    from core.logger import setup_logger
    from ui_tars.agent.gmail_automation_agent import GmailAutomationAgent
except ImportError as e:
    print(f"Error importing required modules: {e}")
    print("Make sure you're running this script from the project root directory.")
    sys.exit(1)

# Set up logger
logger = setup_logger("test_gmail_integration")

async def load_config():
    """
    Load configuration from file.
    
    Returns:
        dict: Configuration dictionary
    """
    config_path = Path("ui_tars/config.json")
    if not config_path.exists():
        logger.error(f"Configuration file not found: {config_path}")
        return {}
    
    try:
        import json
        with open(config_path, "r") as f:
            config = json.load(f)
        
        logger.info(f"Configuration loaded from {config_path}")
        return config
    except Exception as e:
        logger.error(f"Error loading configuration: {e}")
        return {}

async def test_gmail_integration(recipient_email, subject, body):
    """
    Test Gmail integration by sending a test email.
    
    Args:
        recipient_email (str): Recipient email address
        subject (str): Email subject
        body (str): Email body
        
    Returns:
        bool: True if successful, False otherwise
    """
    logger.info("Testing Gmail integration")
    
    # Load configuration
    config = await load_config()
    if not config:
        logger.error("Failed to load configuration")
        return False
    
    # Extract Gmail configuration
    gmail_config = config.get("gmail", {})
    
    # Create Gmail agent
    agent = GmailAutomationAgent(
        agent_id="gmail_test_agent",
        api_url="http://localhost:8080",
        api_key=config.get("ui_tars", {}).get("api_key"),
        model_name="UI-TARS-1.5-7B",
        browser_type="chrome",
        auto_start=True,
        auto_restart=True,
        gmail_url=gmail_config.get("url", "https://mail.google.com"),
        default_email=gmail_config.get("default_email"),
        default_password=gmail_config.get("default_password")
    )
    
    # Initialize agent
    logger.info("Initializing Gmail agent")
    success = await agent.initialize()
    if not success:
        logger.error("Failed to initialize Gmail agent")
        return False
    
    try:
        # Log in to Gmail
        logger.info("Logging in to Gmail")
        login_result = await agent.login_to_gmail()
        
        if not login_result["success"]:
            logger.error(f"Failed to log in to Gmail: {login_result.get('error', 'Unknown error')}")
            return False
        
        # Send test email
        logger.info(f"Sending test email to {recipient_email}")
        send_result = await agent.send_email(
            to=recipient_email,
            subject=subject,
            body=body
        )
        
        if not send_result["success"]:
            logger.error(f"Failed to send email: {send_result.get('error', 'Unknown error')}")
            return False
        
        logger.info("Email sent successfully")
        
        # Logout
        logger.info("Logging out from Gmail")
        await agent.logout_from_gmail()
        
        # Shutdown agent
        logger.info("Shutting down Gmail agent")
        await agent.shutdown()
        
        return True
    
    except Exception as e:
        logger.exception(f"Error testing Gmail integration: {e}")
        
        # Try to shutdown agent
        try:
            await agent.shutdown()
        except:
            pass
        
        return False

async def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Test Gmail Integration")
    parser.add_argument("--to", type=str, default="<EMAIL>", help="Recipient email address")
    parser.add_argument("--subject", type=str, default="Test Email from Enhanced UI-TARS", help="Email subject")
    parser.add_argument("--body", type=str, default="This is a test email sent by the Enhanced UI-TARS Gmail integration.", help="Email body")
    
    args = parser.parse_args()
    
    print("Gmail Integration Test")
    print("=====================")
    print()
    
    try:
        # Test Gmail integration
        success = await test_gmail_integration(args.to, args.subject, args.body)
        
        if success:
            print("\nGmail integration test successful")
            print(f"Email sent to {args.to}")
            return 0
        else:
            print("\nGmail integration test failed")
            return 1
    
    except Exception as e:
        logger.exception(f"Error in main: {e}")
        print(f"Error: {e}")
        return 1

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nTest cancelled")
        sys.exit(0)
