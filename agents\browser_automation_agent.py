"""
Browser Automation Agent

This agent uses UI-TARS 1.5 to automate browser interactions for the AI agent system.
It provides a general-purpose interface for browser automation that other agents can use.
"""
import os
import sys
import json
import time
import logging
import requests
import subprocess
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/browser_automation_agent.log", mode='a'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("browser_automation_agent")

class BrowserAutomationAgent:
    """
    Browser Automation Agent using UI-TARS 1.5.
    
    This agent provides a general-purpose interface for browser automation
    that other agents in the system can use for tasks like:
    - Sending emails
    - Filling out forms
    - Navigating websites
    - Extracting information
    - Interacting with web applications
    """
    
    def __init__(self, 
                 ui_tars_path=None, 
                 config_path=None, 
                 api_host="localhost", 
                 api_port=8080,
                 browser_type="chrome",
                 debug=False):
        """
        Initialize the Browser Automation Agent.
        
        Args:
            ui_tars_path: Path to UI-TARS executable
            config_path: Path to UI-TARS configuration file
            api_host: UI-TARS API host
            api_port: UI-TARS API port
            browser_type: Type of browser to use
            debug: Enable debug logging
        """
        self.ui_tars_path = ui_tars_path or self._find_ui_tars_executable()
        self.config_path = config_path or os.path.join("config", "ui_tars_config.json")
        self.api_host = api_host
        self.api_port = api_port
        self.api_url = f"http://{api_host}:{api_port}/v1"
        self.browser_type = browser_type
        self.debug = debug
        self.ui_tars_process = None
        self.browser_process = None
        
        # Create logs directory if it doesn't exist
        os.makedirs("logs", exist_ok=True)
        
        # Set log level
        if debug:
            logging.getLogger().setLevel(logging.DEBUG)
            
        logger.info("Browser Automation Agent initialized")
        
    def _find_ui_tars_executable(self):
        """Find the UI-TARS executable."""
        logger.info("Searching for UI-TARS executable...")
        
        # Common installation locations on Windows
        possible_paths = [
            os.path.join(os.environ.get("PROGRAMFILES", "C:\\Program Files"), "UI-TARS", "UI-TARS.exe"),
            os.path.join(os.environ.get("PROGRAMFILES(X86)", "C:\\Program Files (x86)"), "UI-TARS", "UI-TARS.exe"),
            os.path.join(os.environ.get("LOCALAPPDATA", "C:\\Users\\<USER>\\AppData\\Local".format(os.getlogin())), "UI-TARS", "UI-TARS.exe"),
            "UI-TARS.exe"
        ]
            
        # Check if any of the paths exist
        for path in possible_paths:
            if os.path.exists(path):
                logger.info(f"Found UI-TARS executable at: {path}")
                return path
                
        # Try to find in PATH
        try:
            result = subprocess.run(["where", "UI-TARS.exe"], capture_output=True, text=True)
                
            if result.returncode == 0:
                path = result.stdout.strip()
                logger.info(f"Found UI-TARS executable in PATH: {path}")
                return path
        except Exception as e:
            logger.debug(f"Error searching for UI-TARS in PATH: {e}")
            
        logger.warning("Could not find UI-TARS executable")
        return "C:\\Users\\<USER>\\AppData\\Local\\UI-TARS\\UI-TARS.exe"  # Default path
        
    def ensure_config_exists(self):
        """Ensure UI-TARS configuration exists."""
        logger.info(f"Ensuring UI-TARS configuration exists at: {self.config_path}")
        
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
        
        # Check if config file exists
        if os.path.exists(self.config_path):
            logger.info("UI-TARS configuration file exists")
            return True
            
        # Create config file
        logger.info("Creating UI-TARS configuration file")
        
        # Create configuration
        config = {
            "ui_tars": {
                "version": "1.5",
                "enabled": True,
                "browser": {
                    "type": self.browser_type,
                    "executable_path": self._find_browser_path(),
                    "user_data_dir": os.path.join(os.environ.get("LOCALAPPDATA", ""), "UI-TARS", "browser_data"),
                    "profile_directory": "Default",
                    "remote_debugging_port": 9222,
                    "detection": {
                        "auto_detect": True,
                        "fallback_types": ["chrome", "edge", "firefox", "brave"]
                    }
                },
                "api": {
                    "host": self.api_host,
                    "port": self.api_port,
                    "timeout": 30,
                    "retry_attempts": 3
                },
                "debug": {
                    "enabled": self.debug,
                    "log_level": "debug" if self.debug else "info",
                    "log_file": "logs/ui_tars_debug.log"
                },
                "sandbox": {
                    "enabled": True,
                    "isolation_level": "high"
                },
                "virtual_pc": {
                    "enabled": True,
                    "memory_mb": 2048,
                    "cpu_cores": 2
                },
                "dpo": {
                    "enabled": True,
                    "preference_model": "default"
                }
            }
        }
        
        # Save configuration
        try:
            with open(self.config_path, "w") as f:
                json.dump(config, f, indent=2)
                
            logger.info(f"Created UI-TARS configuration at: {self.config_path}")
            return True
        except Exception as e:
            logger.error(f"Error creating UI-TARS configuration: {e}")
            return False
            
    def _find_browser_path(self):
        """Find the browser executable path."""
        logger.info(f"Finding {self.browser_type} browser path...")
        
        # Common browser paths on Windows
        browser_paths = {
            "chrome": [
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
            ],
            "edge": [
                r"C:\Program Files\Microsoft\Edge\Application\msedge.exe",
                r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe"
            ],
            "firefox": [
                r"C:\Program Files\Mozilla Firefox\firefox.exe",
                r"C:\Program Files (x86)\Mozilla Firefox\firefox.exe"
            ],
            "brave": [
                r"C:\Program Files\BraveSoftware\Brave-Browser\Application\brave.exe",
                r"C:\Program Files (x86)\BraveSoftware\Brave-Browser\Application\brave.exe"
            ]
        }
        
        # Check if browser type is supported
        if self.browser_type not in browser_paths:
            logger.warning(f"Unsupported browser type: {self.browser_type}")
            return ""
            
        # Check if any of the paths exist
        for path in browser_paths.get(self.browser_type, []):
            if os.path.exists(path):
                logger.info(f"Found {self.browser_type} browser at: {path}")
                return path
                
        # Try to find in PATH
        try:
            browser_exe = {
                "chrome": "chrome.exe",
                "edge": "msedge.exe",
                "firefox": "firefox.exe",
                "brave": "brave.exe"
            }.get(self.browser_type)
            
            if browser_exe:
                result = subprocess.run(["where", browser_exe], capture_output=True, text=True)
                if result.returncode == 0:
                    path = result.stdout.strip().split('\n')[0]
                    logger.info(f"Found {self.browser_type} browser in PATH: {path}")
                    return path
        except Exception as e:
            logger.debug(f"Error searching for browser in PATH: {e}")
            
        logger.warning(f"Could not find {self.browser_type} browser")
        return ""
        
    def kill_existing_processes(self):
        """Kill existing UI-TARS and browser processes."""
        logger.info("Killing existing UI-TARS and browser processes...")
        
        try:
            # Kill UI-TARS process
            subprocess.run(["taskkill", "/F", "/IM", "UI-TARS.exe", "/T"], 
                          stdout=subprocess.PIPE, 
                          stderr=subprocess.PIPE)
            
            # Kill browser processes
            browser_processes = {
                "chrome": "chrome.exe",
                "edge": "msedge.exe",
                "firefox": "firefox.exe",
                "brave": "brave.exe"
            }
            
            subprocess.run(["taskkill", "/F", "/IM", browser_processes.get(self.browser_type, "chrome.exe"), "/T"], 
                          stdout=subprocess.PIPE, 
                          stderr=subprocess.PIPE)
            
            # Wait for processes to terminate
            time.sleep(2)
            
            logger.info("Killed existing processes")
            return True
        except Exception as e:
            logger.error(f"Error killing existing processes: {e}")
            return False
            
    def start_browser_with_remote_debugging(self):
        """Start browser with remote debugging enabled."""
        logger.info(f"Starting {self.browser_type} browser with remote debugging...")
        
        # Find browser path
        browser_path = self._find_browser_path()
        if not browser_path:
            logger.error(f"Could not find {self.browser_type} browser")
            return False
            
        # Create user data directory
        user_data_dir = os.path.join(os.environ.get("LOCALAPPDATA", ""), "UI-TARS", "browser_data")
        os.makedirs(user_data_dir, exist_ok=True)
        
        # Prepare command
        command = [
            browser_path,
            "--remote-debugging-port=9222",
            f"--user-data-dir={user_data_dir}",
            "--no-first-run",
            "--no-default-browser-check",
            "--disable-extensions",
            "--disable-component-extensions-with-background-pages",
            "--disable-background-networking",
            "--disable-client-side-phishing-detection",
            "--disable-sync",
            "--metrics-recording-only",
            "--disable-default-apps",
            "--no-default-browser-check",
            "--no-first-run",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding",
            "--disable-background-timer-throttling",
            "about:blank"
        ]
        
        try:
            # Start browser process
            self.browser_process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Wait for browser to start
            time.sleep(5)
            
            # Check if process is still running
            if self.browser_process.poll() is not None:
                logger.error(f"Browser process exited with code: {self.browser_process.returncode}")
                return False
                
            logger.info(f"Started {self.browser_type} browser with remote debugging")
            return True
        except Exception as e:
            logger.error(f"Error starting browser: {e}")
            return False
            
    def start_ui_tars(self):
        """Start UI-TARS with configuration."""
        logger.info("Starting UI-TARS...")
        
        if not self.ui_tars_path:
            logger.error("UI-TARS executable not found")
            return False
            
        if not os.path.exists(self.ui_tars_path):
            logger.error(f"UI-TARS executable not found at: {self.ui_tars_path}")
            return False
            
        # Ensure configuration exists
        if not self.ensure_config_exists():
            logger.error("Failed to ensure UI-TARS configuration exists")
            return False
            
        # Prepare command
        command = [
            self.ui_tars_path,
            "--config", os.path.abspath(self.config_path),
            "--debug" if self.debug else ""
        ]
        
        try:
            # Start UI-TARS process
            self.ui_tars_process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Wait for UI-TARS to start
            time.sleep(10)
            
            # Check if process is still running
            if self.ui_tars_process.poll() is not None:
                logger.error(f"UI-TARS process exited with code: {self.ui_tars_process.returncode}")
                return False
                
            logger.info("Started UI-TARS")
            return True
        except Exception as e:
            logger.error(f"Error starting UI-TARS: {e}")
            return False
            
    def check_ui_tars_api(self):
        """Check if UI-TARS API is running."""
        logger.info("Checking if UI-TARS API is running...")
        
        try:
            response = requests.get(f"{self.api_url}/models", timeout=5)
            if response.status_code == 200:
                logger.info("UI-TARS API is running")
                return True
            else:
                logger.warning(f"UI-TARS API returned status code: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"Error checking UI-TARS API: {e}")
            return False
            
    def send_ui_tars_command(self, command, params=None):
        """Send a command to UI-TARS API."""
        logger.info(f"Sending command to UI-TARS: {command}")
        
        try:
            url = f"{self.api_url}/completions"
            data = {
                "model": "UI-TARS-1.5-7B",
                "prompt": command,
                "max_tokens": 1000,
                "temperature": 0.7,
                "params": params or {}
            }
            
            response = requests.post(url, json=data, timeout=30)
            if response.status_code == 200:
                result = response.json()
                logger.info(f"UI-TARS command successful: {result.get('choices', [{}])[0].get('text', '')[:100]}...")
                return result
            else:
                logger.warning(f"UI-TARS command failed with status code: {response.status_code}")
                return None
        except Exception as e:
            logger.error(f"Error sending command to UI-TARS: {e}")
            return None
            
    def navigate_to_url(self, url):
        """Navigate to a URL using UI-TARS."""
        logger.info(f"Navigating to URL: {url}")
        
        command = f"Navigate to {url}"
        params = {
            "browser": {
                "url": url
            }
        }
        
        result = self.send_ui_tars_command(command, params)
        if not result:
            logger.error(f"Failed to navigate to URL: {url}")
            return False
            
        logger.info(f"Successfully navigated to URL: {url}")
        return True
        
    def fill_form(self, form_data):
        """Fill a form using UI-TARS."""
        logger.info("Filling form...")
        
        command = "Fill form"
        params = {
            "browser": {
                "actions": []
            }
        }
        
        # Add form filling actions
        for field in form_data:
            params["browser"]["actions"].append({
                "type": "input",
                "selector": field["selector"],
                "value": field["value"]
            })
            
        result = self.send_ui_tars_command(command, params)
        if not result:
            logger.error("Failed to fill form")
            return False
            
        logger.info("Successfully filled form")
        return True
        
    def click_element(self, selector):
        """Click an element using UI-TARS."""
        logger.info(f"Clicking element: {selector}")
        
        command = f"Click element: {selector}"
        params = {
            "browser": {
                "actions": [
                    {
                        "type": "click",
                        "selector": selector
                    }
                ]
            }
        }
        
        result = self.send_ui_tars_command(command, params)
        if not result:
            logger.error(f"Failed to click element: {selector}")
            return False
            
        logger.info(f"Successfully clicked element: {selector}")
        return True
        
    def extract_text(self, selector):
        """Extract text from an element using UI-TARS."""
        logger.info(f"Extracting text from element: {selector}")
        
        command = f"Extract text from element: {selector}"
        params = {
            "browser": {
                "actions": [
                    {
                        "type": "extract",
                        "selector": selector
                    }
                ]
            }
        }
        
        result = self.send_ui_tars_command(command, params)
        if not result:
            logger.error(f"Failed to extract text from element: {selector}")
            return None
            
        # Extract text from result
        text = result.get("choices", [{}])[0].get("text", "")
        logger.info(f"Successfully extracted text from element: {selector}")
        return text
        
    def send_email(self, email_data):
        """Send an email using UI-TARS and Gmail."""
        logger.info(f"Sending email to: {email_data['to']}")
        
        # Navigate to Gmail
        if not self.navigate_to_url("https://mail.google.com"):
            logger.error("Failed to navigate to Gmail")
            return False
            
        # Wait for Gmail to load
        time.sleep(5)
        
        # Click Compose button
        if not self.click_element("div[role='button']:contains('Compose')"):
            logger.error("Failed to click Compose button")
            return False
            
        # Wait for compose window
        time.sleep(2)
        
        # Fill form
        form_data = [
            {
                "selector": "input[role='combobox'][aria-label*='To']",
                "value": email_data["to"]
            },
            {
                "selector": "input[name='subjectbox']",
                "value": email_data["subject"]
            },
            {
                "selector": "div[role='textbox'][aria-label*='Message Body']",
                "value": email_data["body"]
            }
        ]
        
        if not self.fill_form(form_data):
            logger.error("Failed to fill email form")
            return False
            
        # Click Send button
        if not self.click_element("div[role='button']:contains('Send')"):
            logger.error("Failed to click Send button")
            return False
            
        # Wait for confirmation
        time.sleep(3)
        
        logger.info(f"Successfully sent email to: {email_data['to']}")
        return True
        
    def start(self):
        """Start the Browser Automation Agent."""
        logger.info("Starting Browser Automation Agent...")
        
        # Kill existing processes
        self.kill_existing_processes()
        
        # Start browser with remote debugging
        if not self.start_browser_with_remote_debugging():
            logger.error("Failed to start browser with remote debugging")
            return False
            
        # Start UI-TARS
        if not self.start_ui_tars():
            logger.error("Failed to start UI-TARS")
            return False
            
        # Check if UI-TARS API is running
        if not self.check_ui_tars_api():
            logger.error("UI-TARS API is not running")
            return False
            
        logger.info("Browser Automation Agent started successfully")
        return True
        
    def stop(self):
        """Stop the Browser Automation Agent."""
        logger.info("Stopping Browser Automation Agent...")
        
        # Stop UI-TARS process
        if self.ui_tars_process:
            try:
                self.ui_tars_process.terminate()
                time.sleep(2)
                
                if self.ui_tars_process.poll() is None:
                    self.ui_tars_process.kill()
                    
                logger.info("UI-TARS process terminated")
            except Exception as e:
                logger.error(f"Error terminating UI-TARS process: {e}")
                
        # Stop browser process
        if self.browser_process:
            try:
                self.browser_process.terminate()
                time.sleep(2)
                
                if self.browser_process.poll() is None:
                    self.browser_process.kill()
                    
                logger.info("Browser process terminated")
            except Exception as e:
                logger.error(f"Error terminating browser process: {e}")
                
        logger.info("Browser Automation Agent stopped")
        
# Example usage
if __name__ == "__main__":
    # Create agent
    agent = BrowserAutomationAgent(debug=True)
    
    # Start agent
    if agent.start():
        print("Browser Automation Agent started successfully")
        
        # Example: Send an email
        email_data = {
            "to": "<EMAIL>",
            "subject": "IUL Policy and Health Insurance Options",
            "body": """
Dear Alyssa,

Thank you for your interest in our insurance products. Based on your $100/month budget, I'd like to discuss some options for an Indexed Universal Life (IUL) policy structured for maximum cash value growth, along with basic health, dental, and vision plans.

Here's what I'm thinking:

1. IUL Policy: We can structure this for optimal cash value growth while maintaining the life insurance benefit. This would be approximately $60-70 of your monthly budget.

2. Health Insurance: For the remaining $30-40, we can look at basic health plans that cover essential services.

3. Dental & Vision: We have some affordable options that can be added if your budget allows, or we can discuss slightly exceeding your budget if these are priorities for you.

Would you be available for a quick call to discuss these options in more detail? I can answer any questions you might have and provide specific policy recommendations based on your needs.

Please let me know what days and times work best for you.

Best regards,
Paul Edwards
Flo Faction Insurance
Phone: (*************
Email: <EMAIL>
"""
        }
        
        if agent.send_email(email_data):
            print("Email sent successfully")
        else:
            print("Failed to send email")
            
        # Stop agent
        agent.stop()
    else:
        print("Failed to start Browser Automation Agent")
