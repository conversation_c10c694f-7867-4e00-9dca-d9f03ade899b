{"name": "email_processing_workflow", "description": "Workflow for processing emails across multiple accounts", "version": "1.0.0", "triggers": [{"type": "schedule", "schedule": "*/15 * * * *", "description": "Check emails every 15 minutes"}, {"type": "event", "event": "new_email", "description": "Triggered when a new email is received"}, {"type": "command", "command": "process_emails", "description": "Manually triggered email processing"}], "steps": [{"id": "check_new_emails", "agent": "multi_account_email_agent", "action": "read_emails", "parameters": {"query": "is:unread", "max_results": 10, "priority_only": true}, "next": {"condition": "result.total_emails > 0", "true": "analyze_emails", "false": "end"}}, {"id": "analyze_emails", "agent": "multi_account_email_agent", "action": "analyze_email", "parameters": {"account_email": "${previous.result.accounts[0].email}", "email_id": "${previous.result.accounts[0].messages[0].id}"}, "next": "draft_response"}, {"id": "draft_response", "agent": "multi_account_email_agent", "action": "draft_response", "parameters": {"account_email": "${previous.result.account_email}", "email_id": "${previous.result.id}", "response_type": "professional", "include_reasoning": true}, "next": "review_response"}, {"id": "review_response", "agent": "insurance_lead_agent", "action": "review_email_response", "parameters": {"draft_response": "${previous.result.draft_response}", "original_email": "${previous.result.original_email}", "reasoning": "${previous.result.reasoning}"}, "next": {"condition": "result.approved", "true": "send_response", "false": "revise_response"}}, {"id": "revise_response", "agent": "multi_account_email_agent", "action": "draft_response", "parameters": {"account_email": "${steps.draft_response.result.account_email}", "email_id": "${steps.draft_response.result.original_email.id}", "response_type": "professional", "include_reasoning": true, "feedback": "${previous.result.feedback}"}, "next": "send_response"}, {"id": "send_response", "agent": "multi_account_email_agent", "action": "send_email", "parameters": {"account_email": "${steps.draft_response.result.account_email}", "to": "${steps.draft_response.result.original_email.from}", "subject": "Re: ${steps.draft_response.result.original_email.subject}", "body": "${previous.result.approved ? previous.result.revised_response : steps.draft_response.result.draft_response}"}, "next": "end"}, {"id": "end", "type": "end"}]}