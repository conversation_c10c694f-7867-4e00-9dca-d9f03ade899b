"""
Web scraping utilities for the Multi-Agent AI System.
"""
import asyncio
from typing import Dict, List, Optional, Any, Union
import aiohttp
from bs4 import BeautifulSoup
import re
import json
import logging
from urllib.parse import urlparse, urljoin

from core.logger import setup_logger

# Set up logger
logger = setup_logger("web_scraper")

class WebScraper:
    """
    Web scraping utility for retrieving information from websites.
    """
    
    def __init__(self, user_agent: Optional[str] = None):
        """
        Initialize the web scraper.
        
        Args:
            user_agent (Optional[str]): User agent string to use for requests
        """
        self.user_agent = user_agent or "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        self.session = None
    
    async def initialize(self):
        """Initialize the web scraper."""
        self.session = aiohttp.ClientSession(
            headers={"User-Agent": self.user_agent}
        )
    
    async def close(self):
        """Close the web scraper."""
        if self.session:
            await self.session.close()
            self.session = None
    
    async def fetch_page(self, url: str) -> Optional[str]:
        """
        Fetch a web page.
        
        Args:
            url (str): URL to fetch
            
        Returns:
            Optional[str]: HTML content of the page, or None if failed
        """
        if not self.session:
            await self.initialize()
        
        try:
            async with self.session.get(url, timeout=30) as response:
                if response.status == 200:
                    return await response.text()
                else:
                    logger.warning(f"Failed to fetch {url}: {response.status}")
                    return None
        except Exception as e:
            logger.exception(f"Error fetching {url}: {e}")
            return None
    
    async def extract_text(self, html: str) -> str:
        """
        Extract readable text from HTML.
        
        Args:
            html (str): HTML content
            
        Returns:
            str: Extracted text
        """
        soup = BeautifulSoup(html, "html.parser")
        
        # Remove script and style elements
        for script in soup(["script", "style"]):
            script.extract()
        
        # Get text
        text = soup.get_text()
        
        # Break into lines and remove leading and trailing space on each
        lines = (line.strip() for line in text.splitlines())
        
        # Break multi-headlines into a line each
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        
        # Remove blank lines
        text = "\n".join(chunk for chunk in chunks if chunk)
        
        return text
    
    async def extract_links(self, html: str, base_url: str) -> List[Dict[str, str]]:
        """
        Extract links from HTML.
        
        Args:
            html (str): HTML content
            base_url (str): Base URL for resolving relative links
            
        Returns:
            List[Dict[str, str]]: List of links with text and URL
        """
        soup = BeautifulSoup(html, "html.parser")
        links = []
        
        for a in soup.find_all("a", href=True):
            href = a["href"]
            text = a.get_text().strip()
            
            # Skip empty links or javascript links
            if not href or href.startswith("javascript:"):
                continue
            
            # Resolve relative URLs
            full_url = urljoin(base_url, href)
            
            links.append({
                "text": text,
                "url": full_url,
            })
        
        return links
    
    async def extract_metadata(self, html: str) -> Dict[str, str]:
        """
        Extract metadata from HTML.
        
        Args:
            html (str): HTML content
            
        Returns:
            Dict[str, str]: Extracted metadata
        """
        soup = BeautifulSoup(html, "html.parser")
        metadata = {}
        
        # Extract title
        title = soup.find("title")
        if title:
            metadata["title"] = title.get_text().strip()
        
        # Extract meta tags
        for meta in soup.find_all("meta"):
            name = meta.get("name", meta.get("property", ""))
            content = meta.get("content", "")
            
            if name and content:
                metadata[name] = content
        
        return metadata
    
    async def search_for_text(self, html: str, search_text: str) -> List[str]:
        """
        Search for text in HTML.
        
        Args:
            html (str): HTML content
            search_text (str): Text to search for
            
        Returns:
            List[str]: List of paragraphs containing the search text
        """
        soup = BeautifulSoup(html, "html.parser")
        results = []
        
        # Convert search text to lowercase for case-insensitive search
        search_text_lower = search_text.lower()
        
        # Search in paragraphs
        for p in soup.find_all("p"):
            text = p.get_text().strip()
            if search_text_lower in text.lower():
                results.append(text)
        
        # Search in headings
        for h in soup.find_all(["h1", "h2", "h3", "h4", "h5", "h6"]):
            text = h.get_text().strip()
            if search_text_lower in text.lower():
                results.append(text)
        
        # Search in list items
        for li in soup.find_all("li"):
            text = li.get_text().strip()
            if search_text_lower in text.lower():
                results.append(text)
        
        return results
    
    async def extract_tables(self, html: str) -> List[List[List[str]]]:
        """
        Extract tables from HTML.
        
        Args:
            html (str): HTML content
            
        Returns:
            List[List[List[str]]]: List of tables, each as a list of rows, each as a list of cells
        """
        soup = BeautifulSoup(html, "html.parser")
        tables = []
        
        for table in soup.find_all("table"):
            table_data = []
            
            # Extract rows
            for tr in table.find_all("tr"):
                row = []
                
                # Extract cells (th and td)
                for cell in tr.find_all(["th", "td"]):
                    row.append(cell.get_text().strip())
                
                if row:
                    table_data.append(row)
            
            if table_data:
                tables.append(table_data)
        
        return tables
    
    async def extract_json_ld(self, html: str) -> List[Dict]:
        """
        Extract JSON-LD structured data from HTML.
        
        Args:
            html (str): HTML content
            
        Returns:
            List[Dict]: List of JSON-LD objects
        """
        soup = BeautifulSoup(html, "html.parser")
        json_ld_data = []
        
        for script in soup.find_all("script", type="application/ld+json"):
            try:
                data = json.loads(script.string)
                json_ld_data.append(data)
            except (json.JSONDecodeError, TypeError) as e:
                logger.warning(f"Error parsing JSON-LD: {e}")
        
        return json_ld_data
    
    async def scrape_article(self, url: str) -> Optional[Dict]:
        """
        Scrape an article from a URL.
        
        Args:
            url (str): URL to scrape
            
        Returns:
            Optional[Dict]: Article data, or None if failed
        """
        html = await self.fetch_page(url)
        if not html:
            return None
        
        soup = BeautifulSoup(html, "html.parser")
        
        # Extract title
        title = ""
        title_tag = soup.find("title")
        if title_tag:
            title = title_tag.get_text().strip()
        
        # Try to find article content
        article = soup.find("article")
        if not article:
            # Try alternative selectors
            article = soup.find("div", class_=re.compile(r"article|content|post"))
        
        if not article:
            # Fall back to main content
            article = soup.find("main")
        
        if not article:
            # Fall back to body
            article = soup.body
        
        # Extract text
        text = ""
        if article:
            # Remove navigation, sidebars, footers, etc.
            for element in article.find_all(["nav", "aside", "footer"]):
                element.extract()
            
            # Get paragraphs
            paragraphs = []
            for p in article.find_all("p"):
                text = p.get_text().strip()
                if text:
                    paragraphs.append(text)
            
            text = "\n\n".join(paragraphs)
        
        # Extract metadata
        metadata = await self.extract_metadata(html)
        
        return {
            "url": url,
            "title": title,
            "text": text,
            "metadata": metadata,
        }
