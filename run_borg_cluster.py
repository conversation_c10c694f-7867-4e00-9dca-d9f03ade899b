"""
Run the Borg Cluster Management System.

This script initializes and runs the Borg Cluster Management System,
providing a centralized command interface for the system.
"""
import asyncio
import argparse
import logging
import os
import sys
import signal
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent))

from core.logger import setup_logger
from core.state_manager import StateManager
from borg_cluster.borg_resource_manager import BorgResourceManager
from borg_cluster.mpc_server_discovery import MPCServerDiscovery
from borg_cluster.borg_load_balancer import BorgLoadBalancer
from borg_cluster.jarvis_interface import JarvisInterface

# Set up logger
logger = setup_logger("run_borg_cluster")

# Global flag to control system shutdown
shutdown_event = asyncio.Event()

def handle_shutdown(sig, frame):
    """Handle shutdown signals gracefully."""
    logger.info(f"Received shutdown signal {sig}")
    shutdown_event.set()

# Register signal handlers
signal.signal(signal.SIGINT, handle_shutdown)
signal.signal(signal.SIGTERM, handle_shutdown)

async def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Run the Borg Cluster Management System")
    parser.add_argument("--config", type=str, default="config/borg_config.json", help="Path to configuration file")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    parser.add_argument("--no-cli", action="store_true", help="Disable command line interface")
    parser.add_argument("--voice", action="store_true", help="Enable voice interaction")
    args = parser.parse_args()
    
    # Set log level
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        # Initialize state manager
        state_manager = StateManager()
        await state_manager.initialize()
        
        # Initialize Borg Resource Manager
        resource_manager = BorgResourceManager(state_manager)
        await resource_manager.initialize()
        
        # Initialize MPC Server Discovery
        server_discovery = MPCServerDiscovery(state_manager, resource_manager)
        await server_discovery.initialize()
        
        # Initialize Borg Load Balancer
        load_balancer = BorgLoadBalancer(state_manager, resource_manager)
        await load_balancer.initialize()
        
        # Initialize Jarvis Interface
        jarvis_config = {
            "voice_enabled": args.voice,
            "monitoring_interval": 10,
        }
        
        jarvis = JarvisInterface(
            state_manager=state_manager,
            resource_manager=resource_manager,
            server_discovery=server_discovery,
            load_balancer=load_balancer,
            config=jarvis_config,
        )
        await jarvis.initialize()
        
        logger.info("Borg Cluster Management System initialized successfully")
        
        # Wait for shutdown signal
        await shutdown_event.wait()
        
        # Shutdown components
        logger.info("Shutting down Borg Cluster Management System...")
        
        await jarvis.close()
        await load_balancer.close()
        await server_discovery.close()
        await resource_manager.close()
        await state_manager.close()
        
        logger.info("Borg Cluster Management System shutdown complete")
        
    except Exception as e:
        logger.exception(f"Error running Borg Cluster Management System: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
