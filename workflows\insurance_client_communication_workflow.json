{"name": "insurance_client_communication_workflow", "description": "Workflow for handling insurance client communications across multiple channels", "version": "1.0.0", "triggers": [{"type": "event", "event": "new_email", "description": "Triggered when a new client email is received"}, {"type": "event", "event": "new_text", "description": "Triggered when a new client text message is received"}, {"type": "event", "event": "missed_call", "description": "Triggered when there's a missed call from a client"}, {"type": "event", "event": "new_voicemail", "description": "Triggered when a new voicemail is received"}, {"type": "schedule", "schedule": "0 9 * * 1-5", "description": "Check for pending client communications every weekday at 9 AM"}], "steps": [{"id": "identify_client", "agent": "insurance_agent", "action": "identify_client", "parameters": {"communication_data": "${trigger.data}"}, "next": {"condition": "result.client_identified", "true": "retrieve_client_history", "false": "handle_new_prospect"}}, {"id": "handle_new_prospect", "agent": "insurance_lead_agent", "action": "process_new_prospect", "parameters": {"prospect_data": "${previous.result.prospect_data}"}, "next": "analyze_communication"}, {"id": "retrieve_client_history", "agent": "insurance_agent", "action": "retrieve_client_history", "parameters": {"client_id": "${previous.result.client_id}"}, "next": "analyze_communication"}, {"id": "analyze_communication", "agent": "insurance_agent", "action": "analyze_client_communication", "parameters": {"communication_data": "${trigger.data}", "client_history": "${previous.result.client_history || {}}"}, "next": "determine_response_type"}, {"id": "determine_response_type", "agent": "insurance_agent", "action": "determine_response_type", "parameters": {"communication_analysis": "${previous.result.analysis}", "client_data": "${steps.retrieve_client_history.result.client_data || steps.handle_new_prospect.result.prospect_data}"}, "next": {"condition": "result.response_type", "cases": {"email": "prepare_email_response", "text": "prepare_text_response", "call": "prepare_call_response", "appointment": "schedule_appointment"}, "default": "prepare_email_response"}}, {"id": "prepare_email_response", "agent": "insurance_agent", "action": "prepare_email_response", "parameters": {"client_data": "${steps.determine_response_type.result.client_data}", "communication_analysis": "${steps.analyze_communication.result.analysis}", "response_requirements": "${steps.determine_response_type.result.requirements}"}, "next": "send_email_response"}, {"id": "send_email_response", "agent": "multi_account_email_agent", "action": "send_email", "parameters": {"account_email": "<EMAIL>", "to": "${steps.determine_response_type.result.client_data.email}", "subject": "${previous.result.subject}", "body": "${previous.result.body}"}, "next": "log_communication"}, {"id": "prepare_text_response", "agent": "insurance_agent", "action": "prepare_text_response", "parameters": {"client_data": "${steps.determine_response_type.result.client_data}", "communication_analysis": "${steps.analyze_communication.result.analysis}", "response_requirements": "${steps.determine_response_type.result.requirements}"}, "next": "send_text_response"}, {"id": "send_text_response", "agent": "communication_agent", "action": "send_text", "parameters": {"phone_number": "${steps.determine_response_type.result.client_data.phone}", "message": "${previous.result.message}"}, "next": "log_communication"}, {"id": "prepare_call_response", "agent": "insurance_agent", "action": "prepare_call_script", "parameters": {"client_data": "${steps.determine_response_type.result.client_data}", "communication_analysis": "${steps.analyze_communication.result.analysis}", "response_requirements": "${steps.determine_response_type.result.requirements}"}, "next": "schedule_call"}, {"id": "schedule_call", "agent": "insurance_agent", "action": "schedule_call", "parameters": {"client_data": "${steps.determine_response_type.result.client_data}", "call_script": "${previous.result.call_script}", "priority": "${previous.result.priority}"}, "next": "log_communication"}, {"id": "schedule_appointment", "agent": "insurance_agent", "action": "schedule_appointment", "parameters": {"client_data": "${steps.determine_response_type.result.client_data}", "appointment_requirements": "${steps.determine_response_type.result.requirements}"}, "next": "send_appointment_confirmation"}, {"id": "send_appointment_confirmation", "agent": "multi_account_email_agent", "action": "send_email", "parameters": {"account_email": "<EMAIL>", "to": "${steps.determine_response_type.result.client_data.email}", "subject": "Appointment Confirmation", "body": "${previous.result.confirmation_email}"}, "next": "log_communication"}, {"id": "log_communication", "agent": "insurance_agent", "action": "log_client_communication", "parameters": {"client_id": "${steps.identify_client.result.client_id || steps.handle_new_prospect.result.prospect_id}", "communication_type": "${trigger.event}", "communication_data": "${trigger.data}", "response_data": "${previous.result}"}, "next": "end"}, {"id": "end", "type": "end"}]}