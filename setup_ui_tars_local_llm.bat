@echo off
echo Setting up UI-TARS with local LLMs...

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Ask for custom model directory
set /p MODEL_DIR="Enter path to your models directory (leave empty to use default paths): "

REM Run the setup script
cd ui_tars
if "%MODEL_DIR%"=="" (
    python setup_local_llm.py --config ui_tars_config.yaml --output ui_tars_import.json
) else (
    python setup_local_llm.py --config ui_tars_config.yaml --output ui_tars_import.json --scan-dir "%MODEL_DIR%"
)

if %errorlevel% neq 0 (
    echo Failed to set up UI-TARS with local LLMs.
    exit /b 1
)

echo.
echo UI-TARS has been set up to use your local LLMs.
echo.
echo Import Instructions:
echo 1. Open UI-TARS Desktop
echo 2. Go to Settings
echo 3. Click on "Import Configuration"
echo 4. Select the file: ui_tars\ui_tars_import.json
echo 5. Click "Import"
echo.
echo The configuration will be imported and UI-TARS will use your local LLMs.
echo.
echo Press any key to exit...
pause >nul

exit /b 0
