@echo off
echo AI Agent Email Sender
echo ===================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Ask for email details
echo.
set /p TO_EMAIL="Enter recipient email (default: <EMAIL>): "
if "%TO_EMAIL%"=="" set TO_EMAIL=<EMAIL>

set /p SUBJECT="Enter subject (default: Message from AI Agent System): "
if "%SUBJECT%"=="" set SUBJECT=Message from AI Agent System

set /p BODY="Enter body (default: This is a message sent using the AI Agent System.): "
if "%BODY%"=="" set BODY=This is a message sent using the AI Agent System.

REM Run the simple email sender
echo.
echo Sending email to %TO_EMAIL%...
echo.

python simple_email_sender.py "%TO_EMAIL%" "%SUBJECT%" "%BODY%"

echo.
if %errorlevel% equ 0 (
    echo Email sent successfully!
) else (
    echo There was an issue sending the email. Please check the logs.
)

echo.
pause
