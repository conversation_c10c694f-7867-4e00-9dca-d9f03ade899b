"""
AlphaEvolve Service.

This script runs AlphaEvolve as a background service, continuing work on previous tasks
and waiting for new requests.
"""
import asyncio
import argparse
import logging
import os
import sys
import signal
import json
import time
from pathlib import Path
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent))

try:
    from core.logger import setup_logger
    from core.state_manager import StateManager
    from borg_cluster.borg_resource_manager import BorgResourceManager
    from borg_cluster.borg_load_balancer import BorgLoadBalancer
    from alpha_evolve.alpha_evolve_engine import AlphaEvolveEngine
    from alpha_evolve.integration.borg_integration import BorgIntegration
    from alpha_evolve.integration.agent_integration import AgentIntegration
    from core.agent_manager import AgentManager
except ImportError as e:
    print(f"Error importing required modules: {e}")
    print("Make sure you're running this script from the project root directory.")
    sys.exit(1)

# Set up logger
logger = setup_logger("alphaevolve_service")

# Global flag to control system shutdown
shutdown_event = asyncio.Event()

# Global components dictionary
components = {}

# Task state file
TASK_STATE_FILE = "alphaevolve_tasks.json"

class AlphaEvolveService:
    """
    AlphaEvolve Service.
    
    This class manages the AlphaEvolve service, continuing work on previous tasks
    and waiting for new requests.
    """
    
    def __init__(self):
        """Initialize the AlphaEvolve Service."""
        self.state_manager = None
        self.agent_manager = None
        self.resource_manager = None
        self.load_balancer = None
        self.alpha_evolve_engine = None
        self.borg_integration = None
        self.agent_integration = None
        
        self.pending_tasks = []
        self.active_tasks = {}
        self.completed_tasks = []
        
        self.initialized = False
    
    async def initialize(self):
        """Initialize the AlphaEvolve Service."""
        logger.info("Initializing AlphaEvolve Service")
        
        try:
            # Initialize state manager
            logger.info("Initializing State Manager")
            self.state_manager = StateManager()
            await self.state_manager.initialize()
            
            # Initialize agent manager
            logger.info("Initializing Agent Manager")
            self.agent_manager = AgentManager(state_manager=self.state_manager)
            await self.agent_manager.initialize()
            
            # Initialize Borg resource manager
            logger.info("Initializing Borg Resource Manager")
            self.resource_manager = BorgResourceManager(state_manager=self.state_manager)
            await self.resource_manager.initialize()
            
            # Initialize Borg load balancer
            logger.info("Initializing Borg Load Balancer")
            self.load_balancer = BorgLoadBalancer(
                resource_manager=self.resource_manager,
                state_manager=self.state_manager
            )
            await self.load_balancer.initialize()
            
            # Initialize AlphaEvolve engine
            logger.info("Initializing AlphaEvolve Engine")
            self.alpha_evolve_engine = AlphaEvolveEngine(state_manager=self.state_manager)
            await self.alpha_evolve_engine.initialize()
            
            # Initialize Borg integration
            logger.info("Initializing Borg Integration")
            self.borg_integration = BorgIntegration(
                alpha_evolve_engine=self.alpha_evolve_engine,
                resource_manager=self.resource_manager,
                load_balancer=self.load_balancer
            )
            await self.borg_integration.initialize()
            
            # Initialize agent integration
            logger.info("Initializing Agent Integration")
            self.agent_integration = AgentIntegration(
                alpha_evolve_engine=self.alpha_evolve_engine,
                agent_manager=self.agent_manager
            )
            await self.agent_integration.initialize()
            
            # Store components globally
            global components
            components = {
                "state_manager": self.state_manager,
                "agent_manager": self.agent_manager,
                "resource_manager": self.resource_manager,
                "load_balancer": self.load_balancer,
                "alpha_evolve_engine": self.alpha_evolve_engine,
                "borg_integration": self.borg_integration,
                "agent_integration": self.agent_integration
            }
            
            # Load pending tasks
            await self._load_tasks()
            
            self.initialized = True
            logger.info("AlphaEvolve Service initialized")
        
        except Exception as e:
            logger.exception(f"Error initializing AlphaEvolve Service: {e}")
            raise
    
    async def _load_tasks(self):
        """Load pending tasks from file."""
        try:
            if os.path.exists(TASK_STATE_FILE):
                with open(TASK_STATE_FILE, "r") as f:
                    task_state = json.load(f)
                
                self.pending_tasks = task_state.get("pending_tasks", [])
                self.completed_tasks = task_state.get("completed_tasks", [])
                
                logger.info(f"Loaded {len(self.pending_tasks)} pending tasks and {len(self.completed_tasks)} completed tasks")
            else:
                logger.info("No task state file found, starting with empty task list")
        except Exception as e:
            logger.exception(f"Error loading tasks: {e}")
    
    async def _save_tasks(self):
        """Save pending tasks to file."""
        try:
            task_state = {
                "pending_tasks": self.pending_tasks,
                "active_tasks": list(self.active_tasks.values()),
                "completed_tasks": self.completed_tasks,
                "timestamp": datetime.now().isoformat()
            }
            
            with open(TASK_STATE_FILE, "w") as f:
                json.dump(task_state, f, indent=2)
            
            logger.info(f"Saved {len(self.pending_tasks)} pending tasks, {len(self.active_tasks)} active tasks, and {len(self.completed_tasks)} completed tasks")
        except Exception as e:
            logger.exception(f"Error saving tasks: {e}")
    
    async def run(self):
        """Run the AlphaEvolve Service."""
        if not self.initialized:
            await self.initialize()
        
        logger.info("Starting AlphaEvolve Service")
        
        try:
            # Start processing pending tasks
            if self.pending_tasks:
                logger.info(f"Resuming {len(self.pending_tasks)} pending tasks")
                
                for task in self.pending_tasks[:]:
                    await self._process_task(task)
            
            # Main service loop
            while not shutdown_event.is_set():
                # Check for new tasks
                new_tasks = await self._check_for_new_tasks()
                
                for task in new_tasks:
                    await self._process_task(task)
                
                # Check active tasks
                await self._check_active_tasks()
                
                # Save task state
                await self._save_tasks()
                
                # Wait a bit before checking again
                await asyncio.sleep(10)
        
        except Exception as e:
            logger.exception(f"Error in AlphaEvolve Service: {e}")
        
        finally:
            # Save task state before shutting down
            await self._save_tasks()
            
            # Shutdown components
            await self._shutdown()
    
    async def _check_for_new_tasks(self):
        """
        Check for new tasks.
        
        Returns:
            list: New tasks
        """
        # In a real implementation, this would check a task queue or API
        # For now, we'll just return an empty list
        return []
    
    async def _process_task(self, task):
        """
        Process a task.
        
        Args:
            task: Task to process
        """
        task_id = task.get("id")
        task_type = task.get("type")
        
        logger.info(f"Processing task {task_id} of type {task_type}")
        
        # Remove from pending tasks
        if task in self.pending_tasks:
            self.pending_tasks.remove(task)
        
        # Add to active tasks
        self.active_tasks[task_id] = task
        
        # Process based on task type
        if task_type == "evolve":
            asyncio.create_task(self._run_evolution(task))
        elif task_type == "optimize":
            asyncio.create_task(self._run_optimization(task))
        elif task_type == "enhance_agent":
            asyncio.create_task(self._run_agent_enhancement(task))
        elif task_type == "optimize_resources":
            asyncio.create_task(self._run_resource_optimization(task))
        elif task_type == "optimize_scheduling":
            asyncio.create_task(self._run_scheduling_optimization(task))
        else:
            logger.warning(f"Unknown task type: {task_type}")
            
            # Mark as completed with error
            task["status"] = "error"
            task["error"] = f"Unknown task type: {task_type}"
            
            # Remove from active tasks
            del self.active_tasks[task_id]
            
            # Add to completed tasks
            self.completed_tasks.append(task)
    
    async def _check_active_tasks(self):
        """Check active tasks for completion."""
        # In a real implementation, this would check the status of active tasks
        # For now, we'll just log the number of active tasks
        logger.info(f"Active tasks: {len(self.active_tasks)}")
    
    async def _run_evolution(self, task):
        """
        Run an evolution task.
        
        Args:
            task: Evolution task
        """
        try:
            # Extract task parameters
            problem = task.get("problem", {})
            generations = task.get("generations", 50)
            population_size = task.get("population_size", 20)
            fitness_threshold = task.get("fitness_threshold", 0.95)
            timeout = task.get("timeout", 3600)
            
            # Run evolution
            result = await self.alpha_evolve_engine.evolve(
                problem=problem,
                generations=generations,
                population_size=population_size,
                fitness_threshold=fitness_threshold,
                timeout=timeout
            )
            
            # Update task with result
            task["status"] = "completed"
            task["result"] = result
            task["completion_time"] = datetime.now().isoformat()
            
            logger.info(f"Evolution task {task['id']} completed with status: {result['status']}")
        
        except Exception as e:
            logger.exception(f"Error running evolution task {task['id']}: {e}")
            
            # Update task with error
            task["status"] = "error"
            task["error"] = str(e)
            task["completion_time"] = datetime.now().isoformat()
        
        finally:
            # Remove from active tasks
            del self.active_tasks[task["id"]]
            
            # Add to completed tasks
            self.completed_tasks.append(task)
            
            # Save task state
            await self._save_tasks()
    
    async def _run_optimization(self, task):
        """
        Run an optimization task.
        
        Args:
            task: Optimization task
        """
        try:
            # Extract task parameters
            code = task.get("code", "")
            problem = task.get("problem", {})
            generations = task.get("generations", 30)
            population_size = task.get("population_size", 15)
            fitness_threshold = task.get("fitness_threshold", 0.9)
            
            # Run optimization
            result = await self.alpha_evolve_engine.optimize_existing_code(
                code=code,
                problem=problem,
                generations=generations,
                population_size=population_size,
                fitness_threshold=fitness_threshold
            )
            
            # Update task with result
            task["status"] = "completed"
            task["result"] = result
            task["completion_time"] = datetime.now().isoformat()
            
            logger.info(f"Optimization task {task['id']} completed with status: {result['status']}")
        
        except Exception as e:
            logger.exception(f"Error running optimization task {task['id']}: {e}")
            
            # Update task with error
            task["status"] = "error"
            task["error"] = str(e)
            task["completion_time"] = datetime.now().isoformat()
        
        finally:
            # Remove from active tasks
            del self.active_tasks[task["id"]]
            
            # Add to completed tasks
            self.completed_tasks.append(task)
            
            # Save task state
            await self._save_tasks()
    
    async def _run_agent_enhancement(self, task):
        """
        Run an agent enhancement task.
        
        Args:
            task: Agent enhancement task
        """
        try:
            # Extract task parameters
            agent_id = task.get("agent_id", "")
            capability = task.get("capability", "")
            optimization_metric = task.get("optimization_metric", "efficiency")
            generations = task.get("generations", 40)
            population_size = task.get("population_size", 20)
            
            # Run agent enhancement
            result = await self.agent_integration.enhance_agent_capability(
                agent_id=agent_id,
                capability=capability,
                optimization_metric=optimization_metric,
                generations=generations,
                population_size=population_size
            )
            
            # Update task with result
            task["status"] = "completed"
            task["result"] = result
            task["completion_time"] = datetime.now().isoformat()
            
            logger.info(f"Agent enhancement task {task['id']} completed with status: {result['status']}")
        
        except Exception as e:
            logger.exception(f"Error running agent enhancement task {task['id']}: {e}")
            
            # Update task with error
            task["status"] = "error"
            task["error"] = str(e)
            task["completion_time"] = datetime.now().isoformat()
        
        finally:
            # Remove from active tasks
            del self.active_tasks[task["id"]]
            
            # Add to completed tasks
            self.completed_tasks.append(task)
            
            # Save task state
            await self._save_tasks()
    
    async def _run_resource_optimization(self, task):
        """
        Run a resource optimization task.
        
        Args:
            task: Resource optimization task
        """
        try:
            # Extract task parameters
            resource_types = task.get("resource_types")
            optimization_metric = task.get("optimization_metric", "utilization")
            generations = task.get("generations", 50)
            population_size = task.get("population_size", 20)
            
            # Run resource optimization
            result = await self.borg_integration.optimize_resource_allocation(
                resource_types=resource_types,
                optimization_metric=optimization_metric,
                generations=generations,
                population_size=population_size
            )
            
            # Update task with result
            task["status"] = "completed"
            task["result"] = result
            task["completion_time"] = datetime.now().isoformat()
            
            logger.info(f"Resource optimization task {task['id']} completed with status: {result['status']}")
        
        except Exception as e:
            logger.exception(f"Error running resource optimization task {task['id']}: {e}")
            
            # Update task with error
            task["status"] = "error"
            task["error"] = str(e)
            task["completion_time"] = datetime.now().isoformat()
        
        finally:
            # Remove from active tasks
            del self.active_tasks[task["id"]]
            
            # Add to completed tasks
            self.completed_tasks.append(task)
            
            # Save task state
            await self._save_tasks()
    
    async def _run_scheduling_optimization(self, task):
        """
        Run a scheduling optimization task.
        
        Args:
            task: Scheduling optimization task
        """
        try:
            # Extract task parameters
            task_types = task.get("task_types")
            optimization_metric = task.get("optimization_metric", "throughput")
            generations = task.get("generations", 50)
            population_size = task.get("population_size", 20)
            
            # Run scheduling optimization
            result = await self.borg_integration.optimize_task_scheduling(
                task_types=task_types,
                optimization_metric=optimization_metric,
                generations=generations,
                population_size=population_size
            )
            
            # Update task with result
            task["status"] = "completed"
            task["result"] = result
            task["completion_time"] = datetime.now().isoformat()
            
            logger.info(f"Scheduling optimization task {task['id']} completed with status: {result['status']}")
        
        except Exception as e:
            logger.exception(f"Error running scheduling optimization task {task['id']}: {e}")
            
            # Update task with error
            task["status"] = "error"
            task["error"] = str(e)
            task["completion_time"] = datetime.now().isoformat()
        
        finally:
            # Remove from active tasks
            del self.active_tasks[task["id"]]
            
            # Add to completed tasks
            self.completed_tasks.append(task)
            
            # Save task state
            await self._save_tasks()
    
    async def _shutdown(self):
        """Shutdown the AlphaEvolve Service."""
        logger.info("Shutting down AlphaEvolve Service")
        
        try:
            # Shutdown in reverse order of initialization
            if self.agent_integration:
                logger.info("Shutting down Agent Integration")
                await self.agent_integration.shutdown()
            
            if self.borg_integration:
                logger.info("Shutting down Borg Integration")
                await self.borg_integration.shutdown()
            
            if self.alpha_evolve_engine:
                logger.info("Shutting down AlphaEvolve Engine")
                await self.alpha_evolve_engine.shutdown()
            
            if self.load_balancer:
                logger.info("Shutting down Borg Load Balancer")
                await self.load_balancer.shutdown()
            
            if self.resource_manager:
                logger.info("Shutting down Borg Resource Manager")
                await self.resource_manager.shutdown()
            
            if self.agent_manager:
                logger.info("Shutting down Agent Manager")
                await self.agent_manager.shutdown()
            
            if self.state_manager:
                logger.info("Shutting down State Manager")
                await self.state_manager.shutdown()
            
            logger.info("AlphaEvolve Service shut down successfully")
        
        except Exception as e:
            logger.exception(f"Error shutting down AlphaEvolve Service: {e}")

def signal_handler():
    """Handle termination signals."""
    logger.info("Received termination signal")
    shutdown_event.set()

async def run_service(args):
    """
    Run the AlphaEvolve Service.
    
    Args:
        args: Command-line arguments
    """
    # Create and run service
    service = AlphaEvolveService()
    await service.run()

def main():
    """Main entry point."""
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="AlphaEvolve Service")
    parser.add_argument("--log-level", type=str, default="INFO", help="Logging level")
    args = parser.parse_args()
    
    # Set up signal handlers
    for sig in (signal.SIGINT, signal.SIGTERM):
        signal.signal(sig, lambda signum, frame: signal_handler())
    
    # Run service
    try:
        asyncio.run(run_service(args))
    except KeyboardInterrupt:
        print("\nShutting down AlphaEvolve Service...")
        sys.exit(0)

if __name__ == "__main__":
    main()
