#!/usr/bin/env python3
"""
Setup <PERSON><PERSON> Follow-up Schedule

This script sets up a follow-up schedule for <PERSON><PERSON> in the AI Agent System.
It creates tasks for follow-up text messages, calls, and emails at specific intervals.
"""

import os
import sys
import json
import argparse
from datetime import datetime, timedelta

def load_config():
    """Load configuration from the communication_services.json file."""
    try:
        config_path = os.path.join("config", "communication_services.json")
        with open(config_path, "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading configuration: {e}")
        return {}

def save_followup_schedule(schedule, output_path="followup/alyssa_followup_schedule.json"):
    """Save the follow-up schedule to a JSON file."""
    try:
        # Create the directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Save the schedule
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(schedule, f, indent=2)
        
        print(f"Follow-up schedule saved to {output_path}")
        return True
    except Exception as e:
        print(f"Error saving follow-up schedule: {e}")
        return False

def create_followup_schedule(client_name, email, phone, start_date=None):
    """
    Create a follow-up schedule for a client.
    
    Args:
        client_name (str): Client's name
        email (str): Client's email address
        phone (str): Client's phone number
        start_date (datetime, optional): Start date for the schedule
        
    Returns:
        dict: Follow-up schedule
    """
    # Use current date if start_date is not provided
    if start_date is None:
        start_date = datetime.now()
    
    # Extract first name
    first_name = client_name.split()[0] if client_name else ""
    
    # Create schedule
    schedule = {
        "client": {
            "name": client_name,
            "first_name": first_name,
            "email": email,
            "phone": phone
        },
        "start_date": start_date.isoformat(),
        "followups": []
    }
    
    # Day 2: Text message follow-up
    day2 = start_date + timedelta(days=2)
    schedule["followups"].append({
        "type": "text",
        "date": day2.isoformat(),
        "message": f"Hey {first_name}! Just checking in to see if you had a chance to review those IUL options I sent. I'm especially excited about Option 2 with the health discount plan for your checkups. Let me know if you have any questions! - Sandra",
        "status": "scheduled"
    })
    
    # Day 4: Call follow-up
    day4 = start_date + timedelta(days=4)
    schedule["followups"].append({
        "type": "call",
        "date": day4.isoformat(),
        "script": f"Hi {first_name}, it's Sandra from Flo Faction Insurance. I wanted to follow up on the IUL options I sent you last week. I'd love to answer any questions you might have and help you get set up with the coverage you need. Please give me a call back at (************* when you have a moment.",
        "status": "scheduled"
    })
    
    # Day 7: Email follow-up
    day7 = start_date + timedelta(days=7)
    schedule["followups"].append({
        "type": "email",
        "date": day7.isoformat(),
        "subject": f"Quick follow-up on your IUL options",
        "body": f"Hi {first_name},\n\nI hope you're doing well! I wanted to check in about the insurance options I sent you last week. Have you had a chance to review them?\n\nI'm still thinking that Option 2 might be perfect for your situation, especially with the health discount plan for your checkups and bloodwork.\n\nI'm happy to answer any questions you might have or make adjustments to better fit your needs. Just let me know!\n\nBest regards,\nSandra\nFlo Faction Insurance\nCell: (*************\nEmail: <EMAIL>",
        "status": "scheduled"
    })
    
    # Day 14: Final text follow-up
    day14 = start_date + timedelta(days=14)
    schedule["followups"].append({
        "type": "text",
        "date": day14.isoformat(),
        "message": f"Hi {first_name}, just wanted to check in one last time about those insurance options. I'm still available to help if you're interested. No pressure at all - just want to make sure you have what you need! - Sandra",
        "status": "scheduled"
    })
    
    return schedule

def create_followup_tasks(schedule, output_path="tasks/alyssa_followup_tasks.json"):
    """
    Create follow-up tasks for the AI Agent System.
    
    Args:
        schedule (dict): Follow-up schedule
        output_path (str): Path to save the tasks
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Create the directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Create tasks
        tasks = []
        
        for followup in schedule["followups"]:
            task = {
                "id": f"followup_{followup['type']}_{datetime.fromisoformat(followup['date']).strftime('%Y%m%d')}",
                "type": "followup",
                "subtype": followup["type"],
                "client": schedule["client"],
                "scheduled_date": followup["date"],
                "status": "pending",
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }
            
            # Add type-specific fields
            if followup["type"] == "email":
                task["subject"] = followup["subject"]
                task["body"] = followup["body"]
            elif followup["type"] == "text":
                task["message"] = followup["message"]
            elif followup["type"] == "call":
                task["script"] = followup["script"]
            
            tasks.append(task)
        
        # Save tasks
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(tasks, f, indent=2)
        
        print(f"Follow-up tasks saved to {output_path}")
        return True
    except Exception as e:
        print(f"Error creating follow-up tasks: {e}")
        return False

def print_schedule(schedule):
    """Print the follow-up schedule in a readable format."""
    print("\n" + "="*80)
    print(" FOLLOW-UP SCHEDULE FOR ALYSSA CHIRINOS ".center(80, "="))
    print("="*80 + "\n")
    
    print(f"Client: {schedule['client']['name']}")
    print(f"Email: {schedule['client']['email']}")
    print(f"Phone: {schedule['client']['phone']}")
    print(f"Start Date: {datetime.fromisoformat(schedule['start_date']).strftime('%Y-%m-%d')}")
    
    print("\nFollow-up Schedule:")
    for followup in schedule["followups"]:
        date = datetime.fromisoformat(followup["date"]).strftime("%Y-%m-%d")
        followup_type = followup["type"].upper()
        
        print(f"\n{date} - {followup_type}:")
        
        if followup_type == "EMAIL":
            print(f"Subject: {followup['subject']}")
            print(f"Body: {followup['body'][:100]}...")
        elif followup_type == "TEXT":
            print(f"Message: {followup['message']}")
        elif followup_type == "CALL":
            print(f"Script: {followup['script'][:100]}...")

def main():
    """Main function to run the script."""
    parser = argparse.ArgumentParser(description="Setup Alyssa Follow-up Schedule")
    parser.add_argument("--name", default="Alyssa Chirinos", help="Client's name")
    parser.add_argument("--email", default="<EMAIL>", help="Client's email address")
    parser.add_argument("--phone", default="9419294330", help="Client's phone number")
    parser.add_argument("--start-date", help="Start date for the schedule (YYYY-MM-DD)")
    parser.add_argument("--output", default="followup/alyssa_followup_schedule.json", help="Output path for the schedule")
    parser.add_argument("--tasks-output", default="tasks/alyssa_followup_tasks.json", help="Output path for the tasks")
    
    args = parser.parse_args()
    
    # Parse start date if provided
    start_date = None
    if args.start_date:
        try:
            start_date = datetime.strptime(args.start_date, "%Y-%m-%d")
        except ValueError:
            print(f"Invalid date format: {args.start_date}. Using current date.")
    
    # Create follow-up schedule
    schedule = create_followup_schedule(
        client_name=args.name,
        email=args.email,
        phone=args.phone,
        start_date=start_date
    )
    
    # Print the schedule
    print_schedule(schedule)
    
    # Save the schedule
    save_followup_schedule(schedule, args.output)
    
    # Create follow-up tasks
    create_followup_tasks(schedule, args.tasks_output)

if __name__ == "__main__":
    main()
