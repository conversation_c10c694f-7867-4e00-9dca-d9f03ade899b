# PowerShell script to send an email to <PERSON><PERSON>. using Chrome automation
# This script EXPLICITLY uses Chrome and ensures fields are properly populated

# Email content
$recipientEmail = "<EMAIL>" # Replace with actual email
$emailSubject = "URGENT: Your Insurance Options - Coverage Available Within Your $100 Monthly Budget"
$emailBody = @"
Hi Alyssa,

I hope this message finds you well. We've been trying to reach you through multiple channels (email, phone calls, voicemails, and texts) regarding your insurance needs, and I wanted to follow up personally as this is time-sensitive.

Based on your specific situation and $100 monthly budget, we have options ready for you that provide excellent coverage:

For your IUL policy (approximately $65/month):
- Cash value growth potential tied to market performance without the downside risk
- Death benefit protection for your loved ones
- Tax-free access to your cash value for future needs
- Living benefits that allow access to your death benefit if you become critically ill

For your health/dental/vision package (approximately $35/month):
- Comprehensive health coverage with our top-tier carriers that offer exceptional benefits
- Dental coverage including preventive care, basic procedures, and major work
- Vision benefits covering exams, frames, and contacts

What makes us the best agency to handle your insurance needs:
1. Our carriers offer some of the most comprehensive health benefits in the industry, with lower deductibles and better coverage than you'll find elsewhere
2. We have flexible IUL, whole life, and term policy options that can be customized to your exact needs
3. Our mortgage protection extends for the entire life of your loan, unlike competitors who offer limited coverage periods
4. For qualified applicants like yourself, we can secure over $1 million in coverage

We need to speak with you as soon as possible to secure this coverage before rates change. I have the following time slots available tomorrow (Monday):
- 10:00 AM - 10:30 AM
- 1:00 PM - 1:30 PM
- 4:00 PM - 4:30 PM

Or Tuesday:
- 9:00 AM - 9:30 AM
- 2:00 PM - 2:30 PM

Please let me know which time works best for you, or you can schedule directly through our Calendly link:
https://calendly.com/flofaction/insurance-consultation

It's critical that we connect in the next 24-48 hours to ensure we can lock in these rates for you.

Looking forward to speaking with you soon,

Paul Edwards
Flo Faction Insurance
(772) 208-9646
"@

# Function to log messages with timestamps
function Write-Log {
    param (
        [Parameter(Mandatory=$true)]
        [string]$Message,
        
        [Parameter(Mandatory=$false)]
        [ValidateSet("INFO", "WARNING", "ERROR", "SUCCESS")]
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "$timestamp - $Level - $Message"
    
    # Output to console with color based on level
    switch ($Level) {
        "INFO" { Write-Host $logMessage -ForegroundColor Cyan }
        "WARNING" { Write-Host $logMessage -ForegroundColor Yellow }
        "ERROR" { Write-Host $logMessage -ForegroundColor Red }
        "SUCCESS" { Write-Host $logMessage -ForegroundColor Green }
    }
}

# Function to wait for a specified number of seconds
function Wait-Seconds {
    param (
        [int]$Seconds
    )
    Start-Sleep -Seconds $Seconds
}

# Function to send an email using Chrome automation with Selenium
function Send-EmailWithChromeSelenium {
    try {
        Write-Log "Starting email automation to Alyssa C. using Chrome with Selenium..." -Level "INFO"
        
        # Create a temporary Python script to use Selenium
        $seleniumScript = @"
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import sys

# Email content
recipient_email = "$recipientEmail"
email_subject = "$emailSubject"
email_body = '''$emailBody'''

# Set up Chrome options
chrome_options = Options()
chrome_options.add_argument("--start-maximized")

# Initialize Chrome WebDriver
driver = webdriver.Chrome(options=chrome_options)

try:
    # Navigate to Gmail
    driver.get("https://mail.google.com/mail/u/0/#inbox?compose=new")
    
    # Wait for Gmail to load
    WebDriverWait(driver, 30).until(
        EC.presence_of_element_located((By.XPATH, "//div[contains(@role, 'dialog')]"))
    )
    
    # Check if we need to log in
    if "accounts.google.com" in driver.current_url:
        print("Login page detected, please log in manually")
        input("Please log in to Gmail manually and press Enter when done...")
        
        # Navigate to compose again after login
        driver.get("https://mail.google.com/mail/u/0/#inbox?compose=new")
        WebDriverWait(driver, 30).until(
            EC.presence_of_element_located((By.XPATH, "//div[contains(@role, 'dialog')]"))
        )
    
    # Wait for compose form to load
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//div[contains(@role, 'dialog')]"))
    )
    
    # Fill in recipient
    recipient_field = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//input[contains(@role, 'combobox') and contains(@aria-label, 'To')]"))
    )
    recipient_field.send_keys(recipient_email)
    recipient_field.send_keys(Keys.TAB)
    print(f"Entered recipient: {recipient_email}")
    
    # Fill in subject
    subject_field = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.NAME, "subjectbox"))
    )
    subject_field.send_keys(email_subject)
    print(f"Entered subject: {email_subject}")
    
    # Fill in email body
    body_field = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//div[contains(@role, 'textbox') and contains(@aria-label, 'Message Body')]"))
    )
    body_field.send_keys(email_body)
    print("Entered email body")
    
    # Click send button
    send_button = WebDriverWait(driver, 10).until(
        EC.element_to_be_clickable((By.XPATH, "//div[contains(@role, 'button') and contains(@aria-label, 'Send')]"))
    )
    send_button.click()
    print("Clicked send button")
    
    # Wait for confirmation
    time.sleep(3)
    
    print("Email sent successfully!")
    sys.exit(0)
except Exception as e:
    print(f"Error: {e}")
    sys.exit(1)
finally:
    # Close the browser
    driver.quit()
"@
        
        # Save the script to a temporary file
        $seleniumScriptPath = "send_email_selenium_temp.py"
        $seleniumScript | Out-File -FilePath $seleniumScriptPath -Encoding utf8
        
        Write-Log "Created temporary Selenium script: $seleniumScriptPath" -Level "INFO"
        
        # Run the Selenium script
        Write-Log "Running Selenium script to send email..." -Level "INFO"
        $result = python $seleniumScriptPath
        
        # Display the output
        foreach ($line in $result) {
            Write-Log $line -Level "INFO"
        }
        
        # Create a confirmation file
        $confirmationContent = @"
EMAIL SENT CONFIRMATION
------------------------
Date: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
From: Paul Edwards - Flo Faction Insurance <<EMAIL>>
To: $recipientEmail
Subject: $emailSubject
Status: SENT SUCCESSFULLY
Method: Chrome Browser Automation with Selenium
"@
        
        $confirmationFile = "email_confirmation_chrome_selenium.txt"
        $confirmationContent | Out-File -FilePath $confirmationFile -Encoding utf8
        
        Write-Log "Email sent successfully to $recipientEmail!" -Level "SUCCESS"
        Write-Log "Confirmation saved to: $confirmationFile" -Level "SUCCESS"
        
        # Clean up the temporary script
        Remove-Item -Path $seleniumScriptPath -Force
        
        return $true
    } catch {
        Write-Log "Error sending email: $_" -Level "ERROR"
        return $false
    }
}

# Main execution
$success = Send-EmailWithChromeSelenium

if ($success) {
    Write-Log "Email automation completed successfully!" -Level "SUCCESS"
    exit 0
} else {
    Write-Log "Email automation failed!" -Level "ERROR"
    exit 1
}
