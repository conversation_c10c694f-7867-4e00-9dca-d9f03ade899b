"""
Test script for website lead handling.

This script tests the website lead handling functionality for the Insurance Lead Agent.
"""
import os
import sys
import json
import asyncio
import argparse
from typing import Dict, List, Optional, Any
from datetime import datetime
import uuid

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.insurance_lead_agent import InsuranceLeadAgent
from core.state_manager import StateManager
from core.logger import setup_logger
from llm.llm_router import LLMRouter

# Set up logger
logger = setup_logger("test_website_lead_handling")

async def simulate_website_lead(agent: InsuranceLeadAgent, lead_data: Dict):
    """
    Simulate a website lead.
    
    Args:
        agent (InsuranceLeadAgent): Insurance Lead Agent
        lead_data (Dict): Lead data
        
    Returns:
        Dict: Response
    """
    logger.info(f"Simulating website lead: {lead_data.get('user_handle', 'Unknown')}")
    
    try:
        # Handle lead
        response = await agent.handle_lead("website", lead_data)
        
        logger.info(f"Lead handling response: {response}")
        
        return response
    
    except Exception as e:
        logger.exception(f"Error simulating website lead: {e}")
        return {"error": str(e)}

async def process_agent_cycle(agent: InsuranceLeadAgent):
    """
    Process agent cycle.
    
    Args:
        agent (InsuranceLeadAgent): Insurance Lead Agent
        
    Returns:
        bool: Success status
    """
    logger.info("Processing agent cycle...")
    
    try:
        # Execute agent cycle
        await agent.execute_cycle()
        
        logger.info("Agent cycle processed successfully")
        
        return True
    
    except Exception as e:
        logger.exception(f"Error processing agent cycle: {e}")
        return False

async def check_lead_status(agent: InsuranceLeadAgent, lead_id: str):
    """
    Check lead status.
    
    Args:
        agent (InsuranceLeadAgent): Insurance Lead Agent
        lead_id (str): Lead ID
        
    Returns:
        Dict: Lead status
    """
    logger.info(f"Checking status for lead: {lead_id}")
    
    try:
        # Get lead
        lead = agent.leads.get(lead_id)
        
        if lead:
            logger.info(f"Lead found: {lead}")
            
            # Get interactions
            interactions = []
            for interaction_id, interaction in agent.interactions.items():
                if interaction.get("lead_id") == lead_id:
                    interactions.append(interaction)
            
            logger.info(f"Found {len(interactions)} interactions for lead")
            
            # Sort interactions by timestamp
            interactions.sort(key=lambda x: x.get("timestamp", ""))
            
            # Print interactions
            for i, interaction in enumerate(interactions):
                direction = interaction.get("direction", "")
                content = interaction.get("content", "")
                status = interaction.get("status", "")
                timestamp = interaction.get("timestamp", "")
                
                logger.info(f"{i+1}. [{timestamp}] {direction}: {content[:50]}... ({status})")
            
            return {
                "lead": lead,
                "interactions": interactions
            }
        else:
            logger.error(f"Lead not found: {lead_id}")
            return {"error": "Lead not found"}
    
    except Exception as e:
        logger.exception(f"Error checking lead status: {e}")
        return {"error": str(e)}

async def main():
    """Run the website lead handling test."""
    parser = argparse.ArgumentParser(description="Website Lead Handling Test")
    parser.add_argument("--name", type=str, help="Lead name")
    parser.add_argument("--email", type=str, help="Lead email")
    parser.add_argument("--phone", type=str, help="Lead phone")
    parser.add_argument("--message", type=str, help="Lead message")
    parser.add_argument("--insurance-type", type=str, help="Insurance type")
    args = parser.parse_args()
    
    # Create lead data
    name = args.name or "Test User"
    email = args.email or "<EMAIL>"
    phone = args.phone or "************"
    message = args.message or "I'm interested in insurance."
    
    if args.insurance_type:
        message = f"I'm looking for {args.insurance_type} insurance. {message}"
    
    lead_id = f"website-{uuid.uuid4()}"
    
    lead_data = {
        "lead_id": lead_id,
        "user_handle": name,
        "email": email,
        "phone": phone,
        "message": message
    }
    
    # Create state manager
    state_manager = StateManager(use_database=False)
    await state_manager.initialize()
    
    # Create LLM router
    llm_router = LLMRouter()
    await llm_router.initialize()
    
    # Create message queue and shutdown event
    message_queue = asyncio.Queue()
    shutdown_event = asyncio.Event()
    
    # Load lead agent configuration
    try:
        with open("config/lead_agent_config.json", "r") as f:
            lead_config = json.load(f)
    except Exception as e:
        logger.exception(f"Error loading lead agent configuration: {e}")
        return
    
    # Create agent configuration
    agent_config = {
        "name": "Insurance Lead Agent",
        "description": "Handles leads from multiple channels",
        "llm_provider": "anthropic",
        "lead_agent_config": lead_config
    }
    
    # Create and initialize the agent
    agent = InsuranceLeadAgent(
        agent_id="insurance_lead_agent_test",
        config=agent_config,
        state_manager=state_manager,
        message_queue=message_queue,
        shutdown_event=shutdown_event
    )
    
    # Set LLM router
    agent.llm_router = llm_router
    
    # Initialize the agent
    await agent.initialize()
    
    logger.info("Insurance Lead Agent initialized")
    
    # Simulate website lead
    await simulate_website_lead(agent, lead_data)
    
    # Process agent cycle
    await process_agent_cycle(agent)
    
    # Check lead status
    await check_lead_status(agent, lead_id)
    
    # Process agent cycle again
    await process_agent_cycle(agent)
    
    # Check lead status again
    await check_lead_status(agent, lead_id)
    
    # Clean up
    await state_manager.close()
    
    logger.info("Test complete")

if __name__ == "__main__":
    asyncio.run(main())
