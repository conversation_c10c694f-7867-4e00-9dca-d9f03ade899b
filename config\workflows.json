{"enabled": true, "workflows": [{"id": "email_processing", "path": "workflows/email_workflow.json", "enabled": true, "description": "Process emails across multiple accounts"}, {"id": "insurance_lead_processing", "path": "workflows/insurance_lead_workflow.json", "enabled": true, "description": "Process and follow up with insurance leads"}, {"id": "agent_collaboration", "path": "workflows/agent_collaboration_workflow.json", "enabled": true, "description": "Enable collaboration between different agents"}, {"id": "autonomous_operations", "path": "workflows/autonomous_operations_workflow.json", "enabled": true, "description": "Enable autonomous operations of all agents"}, {"id": "insurance_client_communication", "path": "workflows/insurance_client_communication_workflow.json", "enabled": true, "description": "Handle insurance client communications across multiple channels"}], "default_workflow": "autonomous_operations", "workflow_execution": {"max_concurrent_workflows": 5, "max_step_retries": 3, "step_timeout_seconds": 300, "workflow_timeout_minutes": 60}, "logging": {"level": "info", "include_workflow_data": true, "include_step_results": true}}