{"enabled": true, "gpu_acceleration": {"enabled": true, "devices": [0], "precision": "mixed", "memory_efficient_attention": true, "compile_models": true}, "riva": {"enabled": false, "server_url": "localhost:50051", "language_code": "en-US", "voice_name": "English-US.Female-1", "sample_rate_hz": 16000, "asr_boosted_lm_words": [], "asr_boosted_lm_score": 10.0, "tts_pitch": 0.0, "tts_speaking_rate": 1.0}, "clara": {"enabled": false, "api_key": "", "server_url": "", "models": {"medical_nlp": "clara-medical-nlp", "medical_imaging": "clara-medical-imaging"}}, "isaac": {"enabled": false, "sim_server_url": "", "models": {"navigation": "isaac_nav_latest", "manipulation": "isaac_manip_latest"}}, "metropolis": {"enabled": false, "server_url": "", "api_key": "", "models": {"object_detection": "metropolis-objdet", "tracking": "metropolis-tracking", "action_recognition": "metropolis-action-recog"}}, "jetson": {"enabled": false, "device_ip": "", "username": "", "password": "", "ssh_port": 22}, "optimizers": {"enabled": true, "use_triton": true, "enable_flash_attention": true, "enable_fused_kernels": true, "cudnn_benchmark": true}, "logging": {"level": "info", "log_gpu_stats": true, "log_interval_seconds": 60}}