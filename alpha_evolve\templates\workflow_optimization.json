{
  "name": "Multi-Agent Workflow Optimization",
  "description": "Template for optimizing multi-agent workflows",
  "template": "You are tasked with optimizing the {workflow_name} workflow that involves multiple agents: {agent_ids}.

The goal is to optimize for {optimization_metric}.

This workflow coordinates multiple specialized agents to accomplish complex tasks that require different capabilities and expertise.

Requirements:
1. The implementation must efficiently coordinate communication between agents
2. It must handle task delegation based on agent capabilities and current load
3. It should manage dependencies between subtasks
4. It must handle error recovery and fallback mechanisms
5. It should optimize resource usage across the entire workflow

Your solution should be implemented as a Python function that follows this interface:
{interface}

Focus on creating a solution that maximizes {optimization_metric} while maintaining reliability and scalability.",
  "variables": ["workflow_name", "agent_ids", "optimization_metric", "interface"]
}
