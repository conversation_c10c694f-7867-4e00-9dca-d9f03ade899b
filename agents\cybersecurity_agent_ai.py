"""
AI-enhanced security analysis for the Cybersecurity Agent.

This module provides AI-enhanced security analysis capabilities
for the Cybersecurity Agent, including vulnerability assessment,
threat modeling, and security recommendations.
"""
import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any
import json
import re
import os
from pathlib import Path

from core.logger import setup_logger
from llm.llm_router import LLMRouter

# Set up logger
logger = setup_logger("agents.cybersecurity_agent_ai")

# AI analysis prompts
VULNERABILITY_ASSESSMENT_PROMPT = """
You are a cybersecurity expert analyzing the results of a security scan. 
Based on the scan results provided, identify vulnerabilities, assess their severity, 
and provide recommendations for remediation.

Scan Results:
{scan_results}

Please provide your analysis in the following format:
1. Vulnerability Summary: A brief overview of the vulnerabilities found
2. Detailed Findings: List each vulnerability with its severity (Critical, High, Medium, Low)
3. Recommendations: Specific actions to remediate each vulnerability
4. Overall Risk Assessment: An assessment of the overall security posture
"""

THREAT_MODELING_PROMPT = """
You are a cybersecurity expert performing threat modeling for a system.
Based on the system description and scan results provided, identify potential threats,
assess their likelihood and impact, and provide recommendations for mitigating them.

System Description:
{system_description}

Scan Results:
{scan_results}

Please provide your threat model in the following format:
1. Threat Summary: A brief overview of the potential threats
2. Detailed Threats: List each threat with its likelihood (High, Medium, Low) and impact (High, Medium, Low)
3. Attack Vectors: Potential ways an attacker could exploit each threat
4. Mitigations: Specific actions to mitigate each threat
5. Overall Risk Assessment: An assessment of the overall security posture
"""

SECURITY_RECOMMENDATIONS_PROMPT = """
You are a cybersecurity expert providing recommendations for improving security.
Based on the scan results and current security posture provided, recommend specific
actions to improve security.

Current Security Posture:
{security_posture}

Scan Results:
{scan_results}

Please provide your recommendations in the following format:
1. Recommendation Summary: A brief overview of your recommendations
2. Detailed Recommendations: List each recommendation with its priority (Critical, High, Medium, Low)
3. Implementation Steps: Specific steps to implement each recommendation
4. Expected Outcomes: The expected security improvements from each recommendation
5. Long-term Security Strategy: Recommendations for ongoing security improvements
"""

INCIDENT_RESPONSE_PROMPT = """
You are a cybersecurity expert analyzing a security incident.
Based on the incident details provided, analyze the incident, determine its scope and impact,
and provide recommendations for containment, eradication, and recovery.

Incident Details:
{incident_details}

Please provide your analysis in the following format:
1. Incident Summary: A brief overview of the incident
2. Scope and Impact: The systems affected and the impact on the organization
3. Root Cause Analysis: The likely cause of the incident
4. Containment Strategy: Immediate actions to contain the incident
5. Eradication Plan: Steps to remove the threat from the environment
6. Recovery Plan: Steps to restore normal operations
7. Lessons Learned: Recommendations to prevent similar incidents in the future
"""

async def analyze_vulnerabilities(agent, scan_results: Dict) -> Dict:
    """
    Analyze vulnerabilities using AI.
    
    Args:
        agent: Cybersecurity agent instance
        scan_results (Dict): Scan results to analyze
    
    Returns:
        Dict: Vulnerability analysis
    """
    logger.info("Analyzing vulnerabilities using AI")
    
    # Format scan results for the prompt
    scan_results_str = json.dumps(scan_results, indent=2)
    
    # Create prompt
    prompt = VULNERABILITY_ASSESSMENT_PROMPT.format(scan_results=scan_results_str)
    
    # Get analysis from LLM
    try:
        response = await agent.llm_router.generate_text(
            prompt=prompt,
            max_tokens=2000,
            temperature=0.3,
        )
        
        analysis_text = response.get("text", "")
        
        # Parse analysis
        analysis = {
            "timestamp": datetime.now().isoformat(),
            "scan_id": scan_results.get("scan_id", ""),
            "target": scan_results.get("target", ""),
            "analysis": analysis_text,
            "summary": extract_section(analysis_text, "Vulnerability Summary"),
            "findings": extract_section(analysis_text, "Detailed Findings"),
            "recommendations": extract_section(analysis_text, "Recommendations"),
            "risk_assessment": extract_section(analysis_text, "Overall Risk Assessment"),
        }
        
        return analysis
    
    except Exception as e:
        logger.exception(f"Error analyzing vulnerabilities: {e}")
        return {
            "timestamp": datetime.now().isoformat(),
            "scan_id": scan_results.get("scan_id", ""),
            "target": scan_results.get("target", ""),
            "error": str(e),
        }

async def perform_threat_modeling(agent, system_description: str, scan_results: Dict) -> Dict:
    """
    Perform threat modeling using AI.
    
    Args:
        agent: Cybersecurity agent instance
        system_description (str): Description of the system
        scan_results (Dict): Scan results to analyze
    
    Returns:
        Dict: Threat model
    """
    logger.info("Performing threat modeling using AI")
    
    # Format scan results for the prompt
    scan_results_str = json.dumps(scan_results, indent=2)
    
    # Create prompt
    prompt = THREAT_MODELING_PROMPT.format(
        system_description=system_description,
        scan_results=scan_results_str,
    )
    
    # Get analysis from LLM
    try:
        response = await agent.llm_router.generate_text(
            prompt=prompt,
            max_tokens=2000,
            temperature=0.3,
        )
        
        analysis_text = response.get("text", "")
        
        # Parse analysis
        threat_model = {
            "timestamp": datetime.now().isoformat(),
            "system_description": system_description,
            "scan_id": scan_results.get("scan_id", ""),
            "target": scan_results.get("target", ""),
            "analysis": analysis_text,
            "summary": extract_section(analysis_text, "Threat Summary"),
            "threats": extract_section(analysis_text, "Detailed Threats"),
            "attack_vectors": extract_section(analysis_text, "Attack Vectors"),
            "mitigations": extract_section(analysis_text, "Mitigations"),
            "risk_assessment": extract_section(analysis_text, "Overall Risk Assessment"),
        }
        
        return threat_model
    
    except Exception as e:
        logger.exception(f"Error performing threat modeling: {e}")
        return {
            "timestamp": datetime.now().isoformat(),
            "system_description": system_description,
            "scan_id": scan_results.get("scan_id", ""),
            "target": scan_results.get("target", ""),
            "error": str(e),
        }

async def generate_security_recommendations(agent, security_posture: str, scan_results: Dict) -> Dict:
    """
    Generate security recommendations using AI.
    
    Args:
        agent: Cybersecurity agent instance
        security_posture (str): Description of the current security posture
        scan_results (Dict): Scan results to analyze
    
    Returns:
        Dict: Security recommendations
    """
    logger.info("Generating security recommendations using AI")
    
    # Format scan results for the prompt
    scan_results_str = json.dumps(scan_results, indent=2)
    
    # Create prompt
    prompt = SECURITY_RECOMMENDATIONS_PROMPT.format(
        security_posture=security_posture,
        scan_results=scan_results_str,
    )
    
    # Get analysis from LLM
    try:
        response = await agent.llm_router.generate_text(
            prompt=prompt,
            max_tokens=2000,
            temperature=0.3,
        )
        
        analysis_text = response.get("text", "")
        
        # Parse analysis
        recommendations = {
            "timestamp": datetime.now().isoformat(),
            "security_posture": security_posture,
            "scan_id": scan_results.get("scan_id", ""),
            "target": scan_results.get("target", ""),
            "analysis": analysis_text,
            "summary": extract_section(analysis_text, "Recommendation Summary"),
            "recommendations": extract_section(analysis_text, "Detailed Recommendations"),
            "implementation_steps": extract_section(analysis_text, "Implementation Steps"),
            "expected_outcomes": extract_section(analysis_text, "Expected Outcomes"),
            "long_term_strategy": extract_section(analysis_text, "Long-term Security Strategy"),
        }
        
        return recommendations
    
    except Exception as e:
        logger.exception(f"Error generating security recommendations: {e}")
        return {
            "timestamp": datetime.now().isoformat(),
            "security_posture": security_posture,
            "scan_id": scan_results.get("scan_id", ""),
            "target": scan_results.get("target", ""),
            "error": str(e),
        }

async def analyze_security_incident(agent, incident_details: str) -> Dict:
    """
    Analyze a security incident using AI.
    
    Args:
        agent: Cybersecurity agent instance
        incident_details (str): Details of the security incident
    
    Returns:
        Dict: Incident analysis
    """
    logger.info("Analyzing security incident using AI")
    
    # Create prompt
    prompt = INCIDENT_RESPONSE_PROMPT.format(incident_details=incident_details)
    
    # Get analysis from LLM
    try:
        response = await agent.llm_router.generate_text(
            prompt=prompt,
            max_tokens=2000,
            temperature=0.3,
        )
        
        analysis_text = response.get("text", "")
        
        # Parse analysis
        incident_analysis = {
            "timestamp": datetime.now().isoformat(),
            "incident_details": incident_details,
            "analysis": analysis_text,
            "summary": extract_section(analysis_text, "Incident Summary"),
            "scope_and_impact": extract_section(analysis_text, "Scope and Impact"),
            "root_cause": extract_section(analysis_text, "Root Cause Analysis"),
            "containment": extract_section(analysis_text, "Containment Strategy"),
            "eradication": extract_section(analysis_text, "Eradication Plan"),
            "recovery": extract_section(analysis_text, "Recovery Plan"),
            "lessons_learned": extract_section(analysis_text, "Lessons Learned"),
        }
        
        return incident_analysis
    
    except Exception as e:
        logger.exception(f"Error analyzing security incident: {e}")
        return {
            "timestamp": datetime.now().isoformat(),
            "incident_details": incident_details,
            "error": str(e),
        }

def extract_section(text: str, section_name: str) -> str:
    """
    Extract a section from the analysis text.
    
    Args:
        text (str): Analysis text
        section_name (str): Name of the section to extract
    
    Returns:
        str: Extracted section text
    """
    # Try to find the section using regex
    pattern = rf"{section_name}:?\s*(.*?)(?:\n\d+\.|\n\n|$)"
    match = re.search(pattern, text, re.DOTALL)
    
    if match:
        return match.group(1).strip()
    
    # If regex fails, try a simpler approach
    lines = text.split("\n")
    section_start = None
    section_end = None
    
    for i, line in enumerate(lines):
        if section_name in line:
            section_start = i + 1
        elif section_start is not None and (line.strip() == "" or line[0].isdigit()):
            section_end = i
            break
    
    if section_start is not None:
        if section_end is None:
            section_end = len(lines)
        
        section_text = "\n".join(lines[section_start:section_end]).strip()
        return section_text
    
    return ""
