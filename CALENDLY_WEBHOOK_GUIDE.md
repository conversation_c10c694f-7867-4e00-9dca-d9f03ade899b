# Calendly Webhook Integration Guide

This guide explains how to set up and use Calendly webhooks with the Insurance Lead Agent.

## Overview

Calendly webhooks allow the Insurance Lead Agent to receive real-time notifications when leads schedule or cancel appointments. This integration enables the agent to automatically update lead status and track appointments.

## Prerequisites

1. A Calendly account (Professional or higher tier)
2. A Calendly API key (Personal Access Token)
3. ngrok installed for local testing (https://ngrok.com/download)
4. Python 3.8 or higher

## Setup Steps

### 1. Get Your Calendly API Key

1. Log in to your Calendly account
2. Go to your account settings
3. Navigate to "Integrations" > "API & Webhooks"
4. Click "Create New Token" to generate a Personal Access Token
5. Copy the token and save it securely

### 2. Save Your Calendly API Key

Save your Calendly API key to the credentials file:

```bash
# Create the credentials directory if it doesn't exist
mkdir -p credentials/calendly

# Create or update the credentials file
echo '{
  "api_key": "YOUR_API_KEY_HERE"
}' > credentials/calendly/calendly.json
```

Replace `YOUR_API_KEY_HERE` with your actual Calendly API key.

### 3. Get User and Organization Information

Run the following command to get your Calendly user and organization information:

```bash
python calendly_webhook_setup.py --user-info --org-memberships
```

This will retrieve your user and organization information and save it to the credentials file.

### 4. List Existing Webhook Subscriptions

To see if you already have webhook subscriptions set up:

```bash
python calendly_webhook_setup.py --list
```

### 5. Set Up Local Webhook Testing with ngrok

To test webhooks locally, you need to expose your local server to the internet using ngrok:

```bash
python setup_ngrok.py
```

This script will:
1. Start ngrok to create a tunnel to your local server
2. Create a webhook subscription in Calendly pointing to your ngrok URL
3. Save the webhook information to `credentials/calendly/webhook_info.json`

### 6. Start the Webhook Receiver

In a separate terminal, start the webhook receiver:

```bash
python calendly_webhook_receiver.py
```

This will start a local server that listens for webhook events from Calendly.

### 7. Test the Integration

1. Create a test appointment using your Calendly booking page
2. Check the webhook receiver logs to see if the event was received
3. Cancel the test appointment
4. Check the logs again to see if the cancellation event was received

## Webhook Events

The integration handles the following webhook events:

### invitee.created

Triggered when someone schedules an appointment. The agent will:
- Update the lead status to "booked"
- Set the appointment_booked flag to true
- Store the appointment ID
- Add a note with the appointment details
- Log the interaction

### invitee.canceled

Triggered when someone cancels an appointment. The agent will:
- Update the lead status to "qualified"
- Set the appointment_booked flag to false
- Clear the appointment ID
- Add a note with the cancellation details
- Log the interaction

## Filtering Events by Event Type

If you have multiple event types in Calendly and only want to receive events for specific types, you can filter the events:

```bash
python calendly_webhook_receiver.py --event-type "https://api.calendly.com/event_types/YOUR_EVENT_TYPE_UUID"
```

Replace `YOUR_EVENT_TYPE_UUID` with the UUID of your event type.

## Production Deployment

For production deployment, you'll need:

1. A publicly accessible server to receive webhook events
2. A valid SSL certificate for secure communication
3. Proper error handling and monitoring

Update the webhook URL in Calendly to point to your production server:

```bash
python calendly_webhook_setup.py --webhook-url "https://your-production-server.com/webhook"
```

## Troubleshooting

### Webhook Events Not Being Received

1. Check if ngrok is running and the tunnel is active
2. Verify that the webhook subscription was created successfully
3. Make sure the webhook receiver is running
4. Check the Calendly webhook logs in your Calendly account

### Authentication Errors

1. Verify that your API key is correct and not expired
2. Check if your Calendly account has the necessary permissions

### Event Processing Errors

1. Check the webhook receiver logs for error messages
2. Verify that the state manager is initialized correctly
3. Make sure the lead data structure matches what the webhook handler expects

## API Reference

### calendly_webhook_setup.py

```
usage: calendly_webhook_setup.py [-h] [--token TOKEN] [--webhook-url WEBHOOK_URL] [--list] [--delete DELETE] [--user-info] [--org-memberships]

Calendly Webhook Setup

options:
  -h, --help            show this help message and exit
  --token TOKEN         Calendly OAuth token
  --webhook-url WEBHOOK_URL
                        Webhook endpoint URL
  --list                List webhook subscriptions
  --delete DELETE       Delete webhook subscription by UUID
  --user-info           Get user information
  --org-memberships     Get organization memberships
```

### calendly_webhook_receiver.py

```
usage: calendly_webhook_receiver.py [-h] [--host HOST] [--port PORT] [--event-type EVENT_TYPE]

Calendly Webhook Receiver

options:
  -h, --help            show this help message and exit
  --host HOST           Host to bind to
  --port PORT           Port to bind to
  --event-type EVENT_TYPE
                        Filter by event type URI
```

### setup_ngrok.py

```
usage: setup_ngrok.py [-h] [--port PORT] [--token TOKEN]

Setup ngrok for Calendly webhook testing

options:
  -h, --help       show this help message and exit
  --port PORT      Port to tunnel
  --token TOKEN    Calendly OAuth token
```
