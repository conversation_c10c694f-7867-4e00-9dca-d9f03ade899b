{"servers": [{"id": "standard-mpc-server", "type": "standard", "host": "0.0.0.0", "port": 8765, "use_ssl": false, "auto_restart": true}, {"id": "simple-mpc-server", "type": "simple", "host": "0.0.0.0", "port": 8766, "use_ssl": false, "auto_restart": true}, {"id": "advanced-mpc-server", "type": "advanced", "host": "0.0.0.0", "port": 8767, "use_ssl": false, "security_tools_dir": "../tools", "auto_restart": true}, {"id": "secure-mpc-server", "type": "advanced", "host": "0.0.0.0", "port": 8768, "use_ssl": true, "cert_file": "../certs/server.crt", "key_file": "../certs/server.key", "security_tools_dir": "../tools", "auto_restart": true}], "client_config": {"default_server": {"host": "localhost", "port": 8765, "use_ssl": false}, "advanced_server": {"host": "localhost", "port": 8767, "use_ssl": false}, "secure_server": {"host": "localhost", "port": 8768, "use_ssl": true, "ca_file": "../certs/ca.crt"}}}