"""
Configure UI-TARS 1.5 for use with the AI Agent System.
This script sets up UI-TARS 1.5 to work with local LLMs and browser automation.
"""
import os
import json
import sys
import subprocess
import time
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("configure_ui_tars")

# Default UI-TARS installation path
DEFAULT_UI_TARS_PATH = "C:\\Program Files\\UI-TARS"

def find_ui_tars_installation():
    """Find the UI-TARS installation directory."""
    # Check default location
    if os.path.exists(DEFAULT_UI_TARS_PATH):
        return DEFAULT_UI_TARS_PATH

    # Check Program Files
    program_files = os.environ.get("ProgramFiles", "C:\\Program Files")
    ui_tars_path = os.path.join(program_files, "UI-TARS")
    if os.path.exists(ui_tars_path):
        return ui_tars_path

    # Check Program Files (x86)
    program_files_x86 = os.environ.get("ProgramFiles(x86)", "C:\\Program Files (x86)")
    ui_tars_path = os.path.join(program_files_x86, "UI-TARS")
    if os.path.exists(ui_tars_path):
        return ui_tars_path

    # Check AppData
    appdata = os.environ.get("APPDATA", "C:\\Users\\<USER>\\AppData\\Roaming")
    ui_tars_path = os.path.join(appdata, "UI-TARS")
    if os.path.exists(ui_tars_path):
        return ui_tars_path

    # Check Local AppData
    local_appdata = os.environ.get("LOCALAPPDATA", "C:\\Users\\<USER>\\AppData\\Local")
    ui_tars_path = os.path.join(local_appdata, "UI-TARS")
    if os.path.exists(ui_tars_path):
        return ui_tars_path

    return None

def find_chrome_installation():
    """Find the Chrome installation directory."""
    # Check default location
    chrome_path = "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe"
    if os.path.exists(chrome_path):
        return chrome_path

    # Check Program Files (x86)
    chrome_path = "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe"
    if os.path.exists(chrome_path):
        return chrome_path

    # Check Local AppData
    local_appdata = os.environ.get("LOCALAPPDATA", "C:\\Users\\<USER>\\AppData\\Local")
    chrome_path = os.path.join(local_appdata, "Google\\Chrome\\Application\\chrome.exe")
    if os.path.exists(chrome_path):
        return chrome_path

    return None

def load_ui_tars_config():
    """Load the UI-TARS configuration from the config file."""
    config_path = os.path.join("config", "ui_tars_config.json")
    if os.path.exists(config_path):
        with open(config_path, "r") as f:
            return json.load(f)
    return None

def update_ui_tars_config(ui_tars_path, chrome_path):
    """Update the UI-TARS configuration file."""
    config = load_ui_tars_config()
    if not config:
        logger.error("UI-TARS configuration file not found")
        return False

    # Update browser path
    if chrome_path:
        config["ui_tars"]["browser"]["executable_path"] = chrome_path

    # Save updated configuration
    config_path = os.path.join("config", "ui_tars_config.json")
    with open(config_path, "w") as f:
        json.dump(config, f, indent=2)

    logger.info(f"Updated UI-TARS configuration file: {config_path}")
    return True

def configure_ui_tars_app(ui_tars_path, config):
    """Configure the UI-TARS application with the settings from our config."""
    if not ui_tars_path or not os.path.exists(ui_tars_path):
        logger.error(f"UI-TARS installation not found at: {ui_tars_path}")
        return False

    # Create config directory if it doesn't exist
    ui_tars_config_dir = os.path.join(ui_tars_path, "config")
    if not os.path.exists(ui_tars_config_dir):
        os.makedirs(ui_tars_config_dir)
        logger.info(f"Created UI-TARS config directory: {ui_tars_config_dir}")

    # Find or create UI-TARS config file
    ui_tars_config_path = os.path.join(ui_tars_config_dir, "config.json")
    if not os.path.exists(ui_tars_config_path):
        # Create default config
        default_config = {
            "llm": {
                "provider": "huggingface",
                "base_url": "http://localhost:8080/v1",
                "api_key": "hf_dummy_key",
                "model": "UI-TARS-1.5-7B"
            },
            "vlm": {
                "provider": "huggingface",
                "base_url": "http://localhost:8080/v1",
                "api_key": "hf_dummy_key",
                "model": "UI-TARS-1.5-7B"
            },
            "browser": {
                "type": "chrome",
                "executable_path": "",
                "user_data_dir": "",
                "profile_directory": "Default"
            }
        }

        # Write default config
        with open(ui_tars_config_path, "w") as f:
            json.dump(default_config, f, indent=2)

        logger.info(f"Created default UI-TARS config file: {ui_tars_config_path}")

    try:
        # Load UI-TARS config
        with open(ui_tars_config_path, "r") as f:
            ui_tars_config = json.load(f)

        # Update LLM settings
        ui_tars_config["llm"] = {
            "provider": config["ui_tars"]["llm"]["provider"],
            "base_url": config["ui_tars"]["llm"]["base_url"],
            "api_key": config["ui_tars"]["llm"]["api_key"],
            "model": config["ui_tars"]["llm"]["model"]
        }

        # Update VLM settings
        ui_tars_config["vlm"] = {
            "provider": config["ui_tars"]["vlm"]["provider"],
            "base_url": config["ui_tars"]["vlm"]["base_url"],
            "api_key": config["ui_tars"]["vlm"]["api_key"],
            "model": config["ui_tars"]["vlm"]["model"]
        }

        # Update browser settings
        ui_tars_config["browser"] = {
            "type": config["ui_tars"]["browser"]["type"],
            "executable_path": config["ui_tars"]["browser"]["executable_path"],
            "user_data_dir": config["ui_tars"]["browser"]["user_data_dir"],
            "profile_directory": config["ui_tars"]["browser"]["profile_directory"]
        }

        # Save updated UI-TARS config
        with open(ui_tars_config_path, "w") as f:
            json.dump(ui_tars_config, f, indent=2)

        logger.info(f"Updated UI-TARS config file: {ui_tars_config_path}")
        return True

    except Exception as e:
        logger.error(f"Error configuring UI-TARS: {str(e)}")
        return False

def start_ui_tars(ui_tars_path):
    """Start the UI-TARS application."""
    if not ui_tars_path or not os.path.exists(ui_tars_path):
        logger.error(f"UI-TARS installation not found at: {ui_tars_path}")
        return False

    # Find UI-TARS executable
    ui_tars_exe = os.path.join(ui_tars_path, "UI-TARS.exe")
    if not os.path.exists(ui_tars_exe):
        logger.error(f"UI-TARS executable not found at: {ui_tars_exe}")
        return False

    try:
        # Start UI-TARS
        subprocess.Popen([ui_tars_exe])
        logger.info(f"Started UI-TARS: {ui_tars_exe}")
        return True
    except Exception as e:
        logger.error(f"Error starting UI-TARS: {str(e)}")
        return False

def main():
    """Main entry point."""
    logger.info("Configuring UI-TARS 1.5")

    # Find UI-TARS installation
    ui_tars_path = find_ui_tars_installation()
    if not ui_tars_path:
        logger.error("UI-TARS installation not found")
        return 1

    logger.info(f"Found UI-TARS installation at: {ui_tars_path}")

    # Find Chrome installation
    chrome_path = find_chrome_installation()
    if not chrome_path:
        logger.warning("Chrome installation not found, using default path")
    else:
        logger.info(f"Found Chrome installation at: {chrome_path}")

    # Update UI-TARS config
    if not update_ui_tars_config(ui_tars_path, chrome_path):
        logger.error("Failed to update UI-TARS configuration")
        return 1

    # Load updated config
    config = load_ui_tars_config()
    if not config:
        logger.error("Failed to load UI-TARS configuration")
        return 1

    # Configure UI-TARS app
    if not configure_ui_tars_app(ui_tars_path, config):
        logger.error("Failed to configure UI-TARS application")
        return 1

    # Start UI-TARS
    if not start_ui_tars(ui_tars_path):
        logger.error("Failed to start UI-TARS")
        return 1

    logger.info("UI-TARS 1.5 configured successfully")
    return 0

if __name__ == "__main__":
    sys.exit(main())
