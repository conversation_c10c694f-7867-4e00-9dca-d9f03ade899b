"""
Selenium Email to Alyssa

This script uses Selenium to control the browser and send an email to <PERSON><PERSON>
with information about insurance options.
"""
import os
import sys
import time
import logging
from typing import Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("selenium_email_to_alyssa.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("selenium_email_to_alyssa")

try:
    from selenium import webdriver
    from selenium.webdriver.chrome.service import Service as ChromeService
    from selenium.webdriver.chrome.options import Options as ChromeOptions
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    from webdriver_manager.chrome import ChromeDriverManager
except ImportError:
    logger.error("Selenium not found. Please install it with: pip install selenium webdriver-manager")
    sys.exit(1)

# Email configuration
EMAIL_ACCOUNT = "<EMAIL>"
EMAIL_PASSWORD = "GodisSoGood!777"
RECIPIENT_EMAIL = "<EMAIL>"  # Test recipient (change to Alyssa's email when ready)
EMAIL_SUBJECT = "IUL Policy and Health Insurance Options - Flo Faction Insurance"

# Email body
EMAIL_BODY = """Hello Alyssa,

I hope this email finds you well. My name is Sandra from Flo Faction Insurance, and I'm reaching out regarding your interest in an IUL (Indexed Universal Life) policy structured for maximum cash value growth, plus basic health, dental and vision plans.

With your budget of $100/month, I've put together some options that I believe will work well for you:

1. IUL Policy ($60-70/month): This would be structured for optimal cash value growth while maintaining the life insurance benefit.

2. Basic Health Coverage ($20-25/month): This would cover your essential needs like checkups, physicals, and bloodwork.

3. Dental and Vision ($10-15/month): Combined plan for regular dental cleanings and eye exams.

I'd be happy to walk you through these options in more detail and answer any questions you might have. Please feel free to call or text me at ************, or you can reply to this email.

You can also schedule a time to talk using my calendar link: https://calendly.com/flofaction-insurance/30min

Looking forward to helping you secure the coverage you need!

Best regards,
Sandra
Flo Faction Insurance
************
<EMAIL>
"""

def send_email_with_selenium() -> Dict[str, Any]:
    """
    Send an email to Alyssa using Selenium browser automation.
    
    Returns:
        Dict[str, Any]: Result of the operation
    """
    logger.info("Starting Selenium email automation")
    
    driver = None
    
    try:
        # Set up Chrome options
        options = ChromeOptions()
        options.add_argument("--start-maximized")
        options.add_argument("--disable-notifications")
        options.add_experimental_option("excludeSwitches", ["enable-logging"])
        
        # Initialize Chrome driver
        driver = webdriver.Chrome(
            service=ChromeService(ChromeDriverManager().install()),
            options=options
        )
        
        # Set implicit wait time
        driver.implicitly_wait(10)
        
        # Navigate to Gmail
        logger.info("Navigating to Gmail")
        driver.get("https://mail.google.com")
        
        # Check if already logged in
        try:
            # Check if already signed in
            WebDriverWait(driver, 5).until(
                EC.presence_of_element_located((By.XPATH, "//div[contains(@aria-label, 'Compose')]"))
            )
            logger.info("Already signed in to Gmail")
        except TimeoutException:
            # Need to sign in
            logger.info("Not signed in, proceeding with login")
            
            # Wait for email input
            email_input = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//input[@type='email']"))
            )
            
            # Enter email
            email_input.clear()
            email_input.send_keys(EMAIL_ACCOUNT)
            
            # Click Next
            next_button = driver.find_element(By.XPATH, "//button[contains(., 'Next')]")
            next_button.click()
            
            # Wait for password input
            password_input = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//input[@type='password']"))
            )
            
            # Enter password
            password_input.clear()
            password_input.send_keys(EMAIL_PASSWORD)
            
            # Click Next
            next_button = driver.find_element(By.XPATH, "//button[contains(., 'Next')]")
            next_button.click()
            
            # Wait for Gmail to load
            WebDriverWait(driver, 20).until(
                EC.presence_of_element_located((By.XPATH, "//div[contains(@aria-label, 'Compose')]"))
            )
        
        # Click Compose
        logger.info("Clicking Compose button")
        compose_button = driver.find_element(By.XPATH, "//div[contains(@aria-label, 'Compose')]")
        compose_button.click()
        
        # Wait for compose window to appear
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//div[contains(@aria-label, 'To')]"))
        )
        
        # Enter recipient
        logger.info(f"Entering recipient: {RECIPIENT_EMAIL}")
        to_field = driver.find_element(By.XPATH, "//div[contains(@aria-label, 'To')]")
        to_field.click()
        to_field.send_keys(RECIPIENT_EMAIL)
        
        # Enter subject
        logger.info(f"Entering subject: {EMAIL_SUBJECT}")
        subject_field = driver.find_element(By.XPATH, "//input[@name='subjectbox']")
        subject_field.click()
        subject_field.send_keys(EMAIL_SUBJECT)
        
        # Enter body
        logger.info("Entering email body")
        body_field = driver.find_element(By.XPATH, "//div[contains(@aria-label, 'Message Body')]")
        body_field.click()
        body_field.send_keys(EMAIL_BODY)
        
        # Send the email
        logger.info("Sending email")
        send_button = driver.find_element(By.XPATH, "//div[contains(@aria-label, 'Send')]")
        send_button.click()
        
        # Wait for confirmation
        time.sleep(3)
        
        logger.info("Email sent successfully")
        return {"success": True, "message": "Email sent successfully"}
        
    except Exception as e:
        logger.exception(f"Error sending email: {e}")
        return {"success": False, "error": str(e)}
    
    finally:
        # Close the browser
        if driver:
            driver.quit()
        logger.info("Browser closed")

def main():
    """Main function."""
    print("Starting Selenium Email to Alyssa")
    print("================================")
    print()
    
    result = send_email_with_selenium()
    
    if result.get("success"):
        print("✅ Email sent successfully!")
    else:
        print(f"❌ Failed to send email: {result.get('error')}")
    
    print()
    print("Process completed")

if __name__ == "__main__":
    main()
