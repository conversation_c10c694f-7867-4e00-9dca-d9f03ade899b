"""
Test script for TikTok integration.

This script tests the TikTok integration for the Insurance Lead Agent.
"""
import os
import sys
import json
import asyncio
import argparse
from typing import Dict, List, Optional, Any
from datetime import datetime
import aiohttp

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.social_media_service import SocialMediaService
from core.logger import setup_logger

# Set up logger
logger = setup_logger("test_tiktok_integration")

async def test_tiktok_connection(app_id: str, app_secret: str, access_token: str):
    """
    Test connection to TikTok API.
    
    Args:
        app_id (str): TikTok App ID
        app_secret (str): TikTok App Secret
        access_token (str): TikTok Access Token
        
    Returns:
        bool: Success status
    """
    logger.info("Testing TikTok connection...")
    
    # Create configuration
    config = {
        "enabled": True,
        "app_id": app_id,
        "app_secret": app_secret,
        "access_token": access_token
    }
    
    # Create social media service
    social_media_service = SocialMediaService("tiktok", config)
    
    try:
        # Check TikTok status
        status = await social_media_service.check_status()
        
        if status.get("status") == "available":
            logger.info("TikTok connection successful!")
            
            # Save credentials
            await save_credentials(app_id, app_secret, access_token)
            
            return True
        else:
            logger.error(f"TikTok connection failed: {status.get('error', 'Unknown error')}")
            return False
    
    except Exception as e:
        logger.exception(f"Error testing TikTok connection: {e}")
        return False

async def get_tiktok_user_info(access_token: str):
    """
    Get TikTok user info.
    
    Args:
        access_token (str): TikTok Access Token
        
    Returns:
        Dict: User info
    """
    logger.info("Getting TikTok user info...")
    
    try:
        async with aiohttp.ClientSession() as session:
            # Get user info
            async with session.get(
                "https://open.tiktokapis.com/v2/user/info/",
                headers={
                    "Authorization": f"Bearer {access_token}",
                    "Content-Type": "application/json"
                }
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info("User info retrieved successfully!")
                    logger.info(f"User info: {data}")
                    return data
                else:
                    error_data = await response.text()
                    logger.error(f"Failed to get user info: {error_data}")
                    return {"error": error_data}
    
    except Exception as e:
        logger.exception(f"Error getting TikTok user info: {e}")
        return {"error": str(e)}

async def get_tiktok_messages(access_token: str):
    """
    Get TikTok messages.
    
    Args:
        access_token (str): TikTok Access Token
        
    Returns:
        List[Dict]: List of messages
    """
    logger.info("Getting TikTok messages...")
    
    try:
        async with aiohttp.ClientSession() as session:
            # Get messages
            async with session.get(
                "https://open.tiktokapis.com/v2/message/get_messages/",
                headers={
                    "Authorization": f"Bearer {access_token}",
                    "Content-Type": "application/json"
                }
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    messages = data.get("data", {}).get("messages", [])
                    
                    logger.info(f"Found {len(messages)} messages")
                    
                    # Print messages
                    for i, message in enumerate(messages[:5]):  # Limit to 5 messages
                        message_id = message.get("id", "")
                        message_text = message.get("text", "")
                        from_id = message.get("from_id", "")
                        
                        logger.info(f"{i+1}. Message: {message_text[:50]}...")
                        logger.info(f"   From: {from_id}, ID: {message_id}")
                    
                    return messages
                else:
                    error_data = await response.text()
                    logger.error(f"Failed to get messages: {error_data}")
                    return []
    
    except Exception as e:
        logger.exception(f"Error getting TikTok messages: {e}")
        return []

async def send_test_message(recipient_id: str, access_token: str):
    """
    Send a test message.
    
    Args:
        recipient_id (str): Recipient ID
        access_token (str): TikTok Access Token
        
    Returns:
        Dict: Response
    """
    logger.info(f"Sending test message to {recipient_id}...")
    
    try:
        async with aiohttp.ClientSession() as session:
            # Send message
            async with session.post(
                "https://open.tiktokapis.com/v2/message/send_message/",
                json={
                    "recipient_id": recipient_id,
                    "text": "This is a test message from the Insurance Lead Agent."
                },
                headers={
                    "Authorization": f"Bearer {access_token}",
                    "Content-Type": "application/json"
                }
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info("Message sent successfully!")
                    logger.info(f"Response: {data}")
                    return data
                else:
                    error_data = await response.text()
                    logger.error(f"Failed to send message: {error_data}")
                    return {"error": error_data}
    
    except Exception as e:
        logger.exception(f"Error sending TikTok message: {e}")
        return {"error": str(e)}

async def save_credentials(app_id: str, app_secret: str, access_token: str):
    """
    Save TikTok credentials to file.
    
    Args:
        app_id (str): TikTok App ID
        app_secret (str): TikTok App Secret
        access_token (str): TikTok Access Token
    """
    try:
        # Load existing credentials
        credentials_path = "credentials/social_media/tiktok.json"
        
        with open(credentials_path, "r") as f:
            credentials = json.load(f)
        
        # Update credentials
        credentials["app_id"] = app_id
        credentials["app_secret"] = app_secret
        credentials["access_token"] = access_token
        credentials["last_updated"] = datetime.now().isoformat()
        
        # Save credentials
        with open(credentials_path, "w") as f:
            json.dump(credentials, f, indent=4)
        
        logger.info(f"Saved credentials to {credentials_path}")
    
    except Exception as e:
        logger.exception(f"Error saving credentials: {e}")

async def main():
    """Run the TikTok integration test."""
    parser = argparse.ArgumentParser(description="TikTok Integration Test")
    parser.add_argument("--app-id", type=str, help="TikTok App ID")
    parser.add_argument("--app-secret", type=str, help="TikTok App Secret")
    parser.add_argument("--access-token", type=str, help="TikTok Access Token")
    parser.add_argument("--recipient-id", type=str, help="Recipient ID for test message")
    args = parser.parse_args()
    
    # If credentials are not provided, try to load from file
    app_id = args.app_id
    app_secret = args.app_secret
    access_token = args.access_token
    recipient_id = args.recipient_id
    
    if not app_id or not app_secret or not access_token:
        try:
            with open("credentials/social_media/tiktok.json", "r") as f:
                credentials = json.load(f)
                app_id = app_id or credentials.get("app_id", "")
                app_secret = app_secret or credentials.get("app_secret", "")
                access_token = access_token or credentials.get("access_token", "")
        except Exception as e:
            logger.exception(f"Error loading credentials: {e}")
    
    if not app_id or not app_secret or not access_token:
        logger.error("TikTok App ID, App Secret, and Access Token are required")
        print("Please provide TikTok credentials using the --app-id, --app-secret, and --access-token arguments")
        print("You can find these credentials in your TikTok Developer account")
        return
    
    # Test TikTok connection
    connection_success = await test_tiktok_connection(app_id, app_secret, access_token)
    
    if not connection_success:
        return
    
    # Get TikTok user info
    user_info = await get_tiktok_user_info(access_token)
    
    # Get TikTok messages
    messages = await get_tiktok_messages(access_token)
    
    # Send test message if recipient ID is provided
    if recipient_id:
        await send_test_message(recipient_id, access_token)

if __name__ == "__main__":
    asyncio.run(main())
