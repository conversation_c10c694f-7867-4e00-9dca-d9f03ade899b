# PentestGPT Integration

This directory contains the integration with PentestGPT, an AI-powered penetration testing tool that can be used for security testing and vulnerability assessment.

## Overview

PentestGPT is an advanced AI-powered penetration testing tool that combines large language models with security tools to automate and enhance security testing. This integration allows agents to leverage PentestGPT for security testing, vulnerability assessment, and security analysis.

## Components

- `pentestgpt_connector.py`: Main connector for PentestGPT
- `pentestgpt_tools.py`: Integration with security tools
- `pentestgpt_analysis.py`: Security analysis capabilities

## Features

### Security Scanning

PentestGPT can perform various types of security scans:

1. **Basic Scan**: Quick security assessment
2. **Comprehensive Scan**: In-depth security testing
3. **Web Scan**: Web application security testing
4. **Network Scan**: Network security testing

Example:

```python
from PentestGPT.pentestgpt_connector import PentestGPTConnector
import asyncio

async def run_security_scan():
    # Create PentestGPT connector
    pentestgpt_config = {
        "enabled": True,
        "local_mode": True
    }
    connector = PentestGPTConnector(pentestgpt_config)
    await connector.initialize()
    
    # Run security scan
    result = await connector.run_security_scan(
        target="example.com",
        scan_type="basic"
    )
    
    print(f"Scan result: {result}")

asyncio.run(run_security_scan())
```

### Vulnerability Assessment

PentestGPT can assess vulnerabilities in systems and applications:

```python
async def assess_vulnerabilities():
    # Create PentestGPT connector
    pentestgpt_config = {
        "enabled": True,
        "local_mode": True
    }
    connector = PentestGPTConnector(pentestgpt_config)
    await connector.initialize()
    
    # Assess vulnerabilities
    result = await connector.assess_vulnerabilities(
        target="example.com",
        assessment_type="web"
    )
    
    print(f"Vulnerabilities found: {len(result['vulnerabilities'])}")
    for vuln in result['vulnerabilities']:
        print(f"- {vuln['name']}: {vuln['severity']}")

asyncio.run(assess_vulnerabilities())
```

### Security Analysis

PentestGPT can analyze security posture and provide recommendations:

```python
async def analyze_security():
    # Create PentestGPT connector
    pentestgpt_config = {
        "enabled": True,
        "local_mode": True
    }
    connector = PentestGPTConnector(pentestgpt_config)
    await connector.initialize()
    
    # Analyze security
    result = await connector.analyze_security(
        target="example.com",
        analysis_type="comprehensive"
    )
    
    print(f"Security score: {result['security_score']}/10")
    print("Recommendations:")
    for rec in result['recommendations']:
        print(f"- {rec}")

asyncio.run(analyze_security())
```

## Integration with Agents

Agents can use PentestGPT for security testing and analysis:

```python
# In a cybersecurity agent's execute_cycle method
async def execute_cycle(self):
    # Get PentestGPT connector from services
    pentestgpt = self.get_service("pentestgpt")
    
    if pentestgpt:
        # Check for pending security tasks
        pending_tasks = await self.state_manager.get_state("cybersecurity", "pending_tasks")
        
        for task_id, task in pending_tasks.items():
            if task["type"] == "security_scan" and task["status"] == "pending":
                # Run security scan
                result = await pentestgpt.run_security_scan(
                    target=task["target"],
                    scan_type=task["scan_type"]
                )
                
                # Update task with result
                task["status"] = "completed"
                task["result"] = result
                task["completed_at"] = datetime.now().isoformat()
                
                # Save updated task
                await self.state_manager.update_state("cybersecurity", "pending_tasks", pending_tasks)
                
                # Notify user
                await self.send_message(
                    recipient_id=task["requester_id"],
                    message_type="notification",
                    content={
                        "type": "security_scan_completed",
                        "task_id": task_id,
                        "summary": f"Security scan of {task['target']} completed with {len(result['vulnerabilities'])} vulnerabilities found."
                    }
                )
```

## Local Mode vs. API Mode

PentestGPT can be used in two modes:

1. **Local Mode**: Runs PentestGPT locally, requiring the PentestGPT repository to be installed
2. **API Mode**: Uses the PentestGPT API, requiring an API key

### Local Mode Setup

To use PentestGPT in local mode:

1. Clone the PentestGPT repository:
   ```bash
   git clone https://github.com/GreyDGL/PentestGPT.git PentestGPT
   ```

2. Install dependencies:
   ```bash
   pip install -r PentestGPT/requirements.txt
   ```

3. Configure PentestGPT in local mode:
   ```
   ENABLE_PENTESTGPT=True
   PENTESTGPT_LOCAL_MODE=True
   PENTESTGPT_LOCAL_PATH=./PentestGPT
   ```

### API Mode Setup

To use PentestGPT in API mode:

1. Get an API key from the PentestGPT API provider

2. Configure PentestGPT in API mode:
   ```
   ENABLE_PENTESTGPT=True
   PENTESTGPT_LOCAL_MODE=False
   PENTESTGPT_API_KEY=your_api_key
   PENTESTGPT_API_URL=https://api.pentestgpt.com
   ```

## Security Tools Integration

PentestGPT integrates with various security tools:

1. **Nmap**: Network scanning
2. **OWASP ZAP**: Web application scanning
3. **SQLMap**: SQL injection testing
4. **Nikto**: Web server scanning
5. **theHarvester**: Information gathering

Example:

```python
# Run a specific security tool through PentestGPT
result = await pentestgpt.run_security_tool(
    tool="nmap",
    target="example.com",
    options="-sV -p 1-1000"
)
```

## Configuration

PentestGPT can be configured in the `.env` file:

```
# PentestGPT settings
ENABLE_PENTESTGPT=True
PENTESTGPT_LOCAL_MODE=True
PENTESTGPT_LOCAL_PATH=./PentestGPT
PENTESTGPT_API_KEY=your_api_key
PENTESTGPT_API_URL=https://api.pentestgpt.com
```

## Requirements

To use PentestGPT, you may need to install additional packages:

```bash
# For local mode
pip install openai langchain

# For security tools
pip install python-nmap pymetasploit3
```
