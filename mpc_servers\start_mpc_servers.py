"""
Start MPC Servers Script

This script starts multiple MPC servers for the Multi-Agent AI System.
It ensures that MPC servers are running and available for agents to use.
"""
import asyncio
import argparse
import logging
import os
import sys
import signal
import ssl
import json
from typing import Dict, List, Optional, Any
from datetime import datetime
import uuid

# Add parent directory to path to import from core
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.logger import setup_logger
from mpc_servers.mpc_server import MPCServer
from mpc_servers.simple_mpc_server import SimpleMPCServer
from mpc_servers.advanced_mpc_server import AdvancedMPCServer

# Set up logger
logger = setup_logger("start_mpc_servers")

# Global variables
servers = []
running = True

async def start_servers(config: Dict):
    """
    Start MPC servers based on configuration.
    
    Args:
        config (Dict): Server configuration
    """
    global servers
    
    # Get server configurations
    server_configs = config.get("servers", [])
    
    if not server_configs:
        logger.warning("No server configurations found")
        return
    
    # Start each server
    for server_config in server_configs:
        server_id = server_config.get("id", str(uuid.uuid4()))
        server_type = server_config.get("type", "standard")
        host = server_config.get("host", "0.0.0.0")
        port = server_config.get("port", 8765)
        use_ssl = server_config.get("use_ssl", False)
        cert_file = server_config.get("cert_file")
        key_file = server_config.get("key_file")
        security_tools_dir = server_config.get("security_tools_dir")
        
        # Create server based on type
        if server_type == "simple":
            server = SimpleMPCServer(
                server_id=server_id,
                host=host,
                port=port,
                use_ssl=use_ssl,
                cert_file=cert_file,
                key_file=key_file,
            )
            logger.info(f"Created Simple MPC Server {server_id} on {host}:{port}")
            
        elif server_type == "advanced":
            server = AdvancedMPCServer(
                server_id=server_id,
                host=host,
                port=port,
                use_ssl=use_ssl,
                cert_file=cert_file,
                key_file=key_file,
                security_tools_dir=security_tools_dir,
            )
            logger.info(f"Created Advanced MPC Server {server_id} on {host}:{port}")
            
        else:  # standard
            server = MPCServer(
                server_id=server_id,
                host=host,
                port=port,
                use_ssl=use_ssl,
                cert_file=cert_file,
                key_file=key_file,
            )
            logger.info(f"Created Standard MPC Server {server_id} on {host}:{port}")
        
        # Start server
        try:
            # Start server in a separate task
            task = asyncio.create_task(server.start())
            
            # Add server to list
            servers.append({
                "server": server,
                "task": task,
                "config": server_config,
            })
            
            logger.info(f"Started MPC Server {server_id} on {host}:{port}")
            
        except Exception as e:
            logger.exception(f"Error starting MPC Server {server_id}: {e}")

async def stop_servers():
    """Stop all MPC servers."""
    global servers
    
    for server_info in servers:
        server = server_info["server"]
        task = server_info["task"]
        
        try:
            # Stop server
            await server.stop()
            
            # Cancel task
            task.cancel()
            
            logger.info(f"Stopped MPC Server {server.server_id}")
            
        except Exception as e:
            logger.exception(f"Error stopping MPC Server {server.server_id}: {e}")
    
    # Clear servers list
    servers = []

def signal_handler(sig, frame):
    """Handle signals to gracefully shut down servers."""
    global running
    
    logger.info(f"Received signal {sig}, shutting down...")
    running = False
    
    # Create event loop if needed
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    
    # Stop servers
    loop.run_until_complete(stop_servers())
    
    # Exit
    sys.exit(0)

async def monitor_servers():
    """Monitor servers and restart if needed."""
    global servers, running
    
    while running:
        # Check each server
        for i, server_info in enumerate(servers):
            server = server_info["server"]
            task = server_info["task"]
            config = server_info["config"]
            
            # Check if task is done
            if task.done():
                try:
                    # Get result (will raise exception if task failed)
                    task.result()
                    
                    # If we get here, task completed normally
                    logger.info(f"MPC Server {server.server_id} stopped normally")
                    
                except asyncio.CancelledError:
                    # Task was cancelled, this is expected
                    logger.info(f"MPC Server {server.server_id} task cancelled")
                    
                except Exception as e:
                    # Task failed with an exception
                    logger.exception(f"MPC Server {server.server_id} failed: {e}")
                    
                    # Restart server if auto_restart is enabled
                    if config.get("auto_restart", True):
                        logger.info(f"Restarting MPC Server {server.server_id}")
                        
                        # Create new server
                        server_id = config.get("id", str(uuid.uuid4()))
                        server_type = config.get("type", "standard")
                        host = config.get("host", "0.0.0.0")
                        port = config.get("port", 8765)
                        use_ssl = config.get("use_ssl", False)
                        cert_file = config.get("cert_file")
                        key_file = config.get("key_file")
                        security_tools_dir = config.get("security_tools_dir")
                        
                        # Create server based on type
                        if server_type == "simple":
                            new_server = SimpleMPCServer(
                                server_id=server_id,
                                host=host,
                                port=port,
                                use_ssl=use_ssl,
                                cert_file=cert_file,
                                key_file=key_file,
                            )
                            
                        elif server_type == "advanced":
                            new_server = AdvancedMPCServer(
                                server_id=server_id,
                                host=host,
                                port=port,
                                use_ssl=use_ssl,
                                cert_file=cert_file,
                                key_file=key_file,
                                security_tools_dir=security_tools_dir,
                            )
                            
                        else:  # standard
                            new_server = MPCServer(
                                server_id=server_id,
                                host=host,
                                port=port,
                                use_ssl=use_ssl,
                                cert_file=cert_file,
                                key_file=key_file,
                            )
                        
                        # Start server
                        new_task = asyncio.create_task(new_server.start())
                        
                        # Update servers list
                        servers[i] = {
                            "server": new_server,
                            "task": new_task,
                            "config": config,
                        }
                        
                        logger.info(f"Restarted MPC Server {server_id} on {host}:{port}")
        
        # Wait before checking again
        await asyncio.sleep(10)

async def main(config_file: str):
    """
    Main function.
    
    Args:
        config_file (str): Path to configuration file
    """
    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Load configuration
    try:
        with open(config_file, "r") as f:
            config = json.load(f)
    except Exception as e:
        logger.exception(f"Error loading configuration file: {e}")
        return
    
    # Start servers
    await start_servers(config)
    
    # Monitor servers
    await monitor_servers()

if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Start MPC servers")
    parser.add_argument("--config", type=str, default="mpc_servers/config.json", help="Path to configuration file")
    args = parser.parse_args()
    
    # Run main function
    asyncio.run(main(args.config))
