"""
Check if UI-TARS is running.

This script checks if UI-TARS is running by:
1. Checking if the UI-TARS executable exists
2. Checking if the UI-TARS API is running
3. Checking if a browser is available
"""
import os
import sys
import socket
import subprocess
import platform
import requests
import time

def check_port_open(host, port, timeout=5):
    """Check if a port is open."""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(timeout)
            result = s.connect_ex((host, port))
            return result == 0
    except Exception as e:
        print(f"Error checking port {port}: {e}")
        return False

def check_ui_tars_api():
    """Check if the UI-TARS API is running."""
    print("Checking if UI-TARS API is running...")
    
    if not check_port_open("localhost", 8080):
        print("UI-TARS API not running on port 8080")
        return False
    
    try:
        # Try different API endpoints
        endpoints = [
            "http://localhost:8080/health",
            "http://localhost:8080/v1/models",
            "http://localhost:8080/api/status"
        ]
        
        for endpoint in endpoints:
            try:
                print(f"Trying endpoint: {endpoint}")
                response = requests.get(endpoint, timeout=5)
                
                if response.status_code < 400:
                    print(f"UI-TARS API is running (endpoint: {endpoint})")
                    return True
            except requests.exceptions.RequestException:
                continue
        
        print("UI-TARS API is not responding to any known endpoints")
        return False
        
    except Exception as e:
        print(f"Error checking UI-TARS API: {e}")
        return False

def find_ui_tars_executable():
    """Find the UI-TARS executable."""
    print("Searching for UI-TARS executable...")
    
    os_type = platform.system()
    
    if os_type == "Windows":
        # Common installation locations on Windows
        possible_paths = [
            os.path.join(os.environ.get("PROGRAMFILES", "C:\\Program Files"), "UI-TARS", "UI-TARS.exe"),
            os.path.join(os.environ.get("PROGRAMFILES(X86)", "C:\\Program Files (x86)"), "UI-TARS", "UI-TARS.exe"),
            os.path.join(os.environ.get("LOCALAPPDATA", "C:\\Users\\<USER>\\AppData\\Local".format(os.getlogin())), "UI-TARS", "UI-TARS.exe"),
            "UI-TARS.exe"
        ]
    elif os_type == "Darwin":  # macOS
        # Common installation locations on macOS
        possible_paths = [
            "/Applications/UI-TARS.app/Contents/MacOS/UI-TARS",
            os.path.expanduser("~/Applications/UI-TARS.app/Contents/MacOS/UI-TARS"),
        ]
    else:  # Linux
        # Common installation locations on Linux
        possible_paths = [
            "/usr/local/bin/ui-tars",
            "/usr/bin/ui-tars",
            os.path.expanduser("~/.local/bin/ui-tars"),
        ]
        
    # Check if any of the paths exist
    for path in possible_paths:
        if os.path.exists(path):
            print(f"Found UI-TARS executable at: {path}")
            return path
            
    # Try to find in PATH
    try:
        if os_type == "Windows":
            result = subprocess.run(["where", "UI-TARS.exe"], capture_output=True, text=True)
        else:
            result = subprocess.run(["which", "ui-tars"], capture_output=True, text=True)
            
        if result.returncode == 0:
            path = result.stdout.strip()
            print(f"Found UI-TARS executable in PATH: {path}")
            return path
    except Exception as e:
        print(f"Error searching for UI-TARS in PATH: {e}")
        
    print("Could not find UI-TARS executable")
    return None

def detect_browsers():
    """Detect installed browsers."""
    print("Detecting installed browsers...")
    
    browsers = {}
    os_type = platform.system()
    
    if os_type == "Windows":
        # Check for common browsers on Windows
        paths = [
            (r"C:\Program Files\Google\Chrome\Application\chrome.exe", "chrome"),
            (r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe", "chrome"),
            (r"C:\Program Files\Mozilla Firefox\firefox.exe", "firefox"),
            (r"C:\Program Files (x86)\Mozilla Firefox\firefox.exe", "firefox"),
            (r"C:\Program Files\Microsoft\Edge\Application\msedge.exe", "edge"),
            (r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe", "edge"),
            (r"C:\Program Files\BraveSoftware\Brave-Browser\Application\brave.exe", "brave"),
            (r"C:\Program Files (x86)\BraveSoftware\Brave-Browser\Application\brave.exe", "brave"),
        ]
    elif os_type == "Darwin":  # macOS
        # Check for common browsers on macOS
        paths = [
            ("/Applications/Google Chrome.app/Contents/MacOS/Google Chrome", "chrome"),
            ("/Applications/Firefox.app/Contents/MacOS/firefox", "firefox"),
            ("/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge", "edge"),
            ("/Applications/Brave Browser.app/Contents/MacOS/Brave Browser", "brave"),
        ]
    else:  # Linux
        # Check for common browsers on Linux
        paths = [
            ("/usr/bin/google-chrome", "chrome"),
            ("/usr/bin/firefox", "firefox"),
            ("/usr/bin/microsoft-edge", "edge"),
            ("/usr/bin/brave-browser", "brave"),
        ]
        
    # Check if any of the paths exist
    for path, browser_type in paths:
        if os.path.exists(path):
            browsers[browser_type] = path
            print(f"Found {browser_type} browser at: {path}")
            
    return browsers

def main():
    """Main entry point for the script."""
    print("UI-TARS Status Check")
    print("===================")
    print()
    
    # Check if UI-TARS executable exists
    ui_tars_path = find_ui_tars_executable()
    if ui_tars_path:
        print("✅ UI-TARS executable found")
    else:
        print("❌ UI-TARS executable not found")
        
    # Check if UI-TARS API is running
    api_running = check_ui_tars_api()
    if api_running:
        print("✅ UI-TARS API is running")
    else:
        print("❌ UI-TARS API is not running")
        
    # Check if browsers are available
    browsers = detect_browsers()
    if browsers:
        print(f"✅ Found {len(browsers)} browsers")
    else:
        print("❌ No browsers detected")
        
    # Print summary
    print()
    print("Summary:")
    print(f"- UI-TARS Executable: {'Found' if ui_tars_path else 'Not Found'}")
    print(f"- UI-TARS API: {'Running' if api_running else 'Not Running'}")
    print(f"- Browsers: {len(browsers)} detected")
    print()
    
    # Print recommendations
    print("Recommendations:")
    if not ui_tars_path:
        print("- Install UI-TARS 1.5 or provide the correct path")
    if not api_running:
        print("- Start UI-TARS API or check if it's running on a different port")
    if not browsers:
        print("- Install a supported browser (Chrome, Edge, Firefox, or Brave)")
    if ui_tars_path and api_running and browsers:
        print("- UI-TARS is properly configured and running!")
        print("- Run the browser fix script to enhance UI-TARS capabilities:")
        print("  python fix_ui_tars_browser.py --browser chrome --start")
    
    return 0 if ui_tars_path and api_running and browsers else 1

if __name__ == "__main__":
    sys.exit(main())
