"""
Direct Browser Email Automation

This script uses Selenium to automate Gmail in the browser to send emails.
It doesn't rely on UI-TARS and can be used as a fallback option.
"""
import os
import sys
import time
import logging
import getpass
import argparse
from typing import Dict, Optional, Any

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("direct_browser_email")

try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.common.keys import Keys
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
except ImportError:
    logger.error("Selenium not installed. Please install it with: pip install selenium")
    logger.error("Also install the appropriate webdriver for your browser.")
    sys.exit(1)

class DirectBrowserEmailAutomation:
    """Class to automate Gmail using Selenium."""
    
    def __init__(self, browser_type: str = "chrome"):
        """
        Initialize the Direct Browser Email Automation.
        
        Args:
            browser_type (str): Type of browser to use (chrome, firefox, edge)
        """
        self.browser_type = browser_type.lower()
        self.driver = None
    
    def initialize(self) -> bool:
        """
        Initialize the browser.
        
        Returns:
            bool: True if initialization was successful, False otherwise
        """
        logger.info(f"Initializing {self.browser_type} browser")
        
        try:
            if self.browser_type == "chrome":
                self.driver = webdriver.Chrome()
            elif self.browser_type == "firefox":
                self.driver = webdriver.Firefox()
            elif self.browser_type == "edge":
                self.driver = webdriver.Edge()
            else:
                logger.error(f"Unsupported browser type: {self.browser_type}")
                return False
            
            # Set window size
            self.driver.set_window_size(1280, 800)
            
            logger.info(f"{self.browser_type} browser initialized successfully")
            return True
            
        except Exception as e:
            logger.exception(f"Error initializing browser: {e}")
            return False
    
    def send_email(self, 
                  email_account: str,
                  password: str,
                  to_email: str, 
                  subject: str, 
                  body: str) -> Dict[str, Any]:
        """
        Send an email using Gmail through browser automation.
        
        Args:
            email_account (str): Gmail account to send from
            password (str): Password for the Gmail account
            to_email (str): Recipient email address
            subject (str): Email subject
            body (str): Email body
            
        Returns:
            Dict[str, Any]: Result of the operation
        """
        if not self.driver:
            logger.error("Browser not initialized")
            return {"success": False, "error": "Browser not initialized"}
        
        try:
            # Step 1: Navigate to Gmail
            logger.info("Navigating to Gmail")
            self.driver.get("https://mail.google.com")
            
            # Step 2: Check if already logged in
            logger.info("Checking login status")
            
            # Wait for either the email input field or the Gmail interface to load
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.ID, "identifierId"))
                )
                
                # Need to log in
                logger.info("Login page detected")
                
                # Step 3: Enter email
                logger.info(f"Entering email: {email_account}")
                email_input = self.driver.find_element(By.ID, "identifierId")
                email_input.send_keys(email_account)
                email_input.send_keys(Keys.RETURN)
                
                # Wait for password field
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.NAME, "Passwd"))
                )
                
                # Step 4: Enter password
                logger.info("Entering password")
                password_input = self.driver.find_element(By.NAME, "Passwd")
                password_input.send_keys(password)
                password_input.send_keys(Keys.RETURN)
                
                # Wait for Gmail to load
                WebDriverWait(self.driver, 20).until(
                    EC.presence_of_element_located((By.XPATH, "//div[contains(text(), 'Compose')]"))
                )
                
            except TimeoutException:
                # Check if already logged in
                try:
                    WebDriverWait(self.driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, "//div[contains(text(), 'Compose')]"))
                    )
                    logger.info("Already logged in")
                except TimeoutException:
                    logger.error("Failed to log in or detect Gmail interface")
                    return {"success": False, "error": "Failed to log in or detect Gmail interface"}
            
            # Step 5: Click Compose
            logger.info("Clicking Compose button")
            compose_button = self.driver.find_element(By.XPATH, "//div[contains(text(), 'Compose')]")
            compose_button.click()
            
            # Wait for compose window
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.NAME, "to"))
            )
            
            # Step 6: Fill in recipient
            logger.info(f"Entering recipient: {to_email}")
            to_input = self.driver.find_element(By.NAME, "to")
            to_input.send_keys(to_email)
            
            # Step 7: Fill in subject
            logger.info(f"Entering subject: {subject}")
            subject_input = self.driver.find_element(By.NAME, "subjectbox")
            subject_input.send_keys(subject)
            
            # Step 8: Fill in body
            logger.info("Entering email body")
            body_input = self.driver.find_element(By.XPATH, "//div[@role='textbox']")
            body_input.send_keys(body)
            
            # Step 9: Send the email
            logger.info("Sending email")
            send_button = self.driver.find_element(By.XPATH, "//div[contains(text(), 'Send')]")
            send_button.click()
            
            # Step 10: Wait for confirmation
            time.sleep(3)
            
            logger.info("Email sent successfully")
            return {"success": True, "message": "Email sent successfully"}
            
        except Exception as e:
            logger.exception(f"Error sending email: {e}")
            return {"success": False, "error": str(e)}
    
    def shutdown(self) -> None:
        """Shut down the browser."""
        if self.driver:
            self.driver.quit()
        logger.info("Browser shut down")

def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Direct Browser Email Automation")
    parser.add_argument("--email", type=str, default="<EMAIL>", help="Gmail account to send from")
    parser.add_argument("--to", type=str, default="<EMAIL>", help="Recipient email address")
    parser.add_argument("--subject", type=str, default="Test Email from Direct Browser Automation", help="Email subject")
    parser.add_argument("--body", type=str, default="This is a test email sent using direct browser automation.", help="Email body")
    parser.add_argument("--browser", type=str, choices=["chrome", "firefox", "edge"], default="chrome", help="Browser to use")
    
    args = parser.parse_args()
    
    # Get password
    password = getpass.getpass(f"Enter password for {args.email}: ")
    
    # Create Direct Browser Email Automation
    browser_automation = DirectBrowserEmailAutomation(browser_type=args.browser)
    
    # Initialize
    initialized = browser_automation.initialize()
    if not initialized:
        logger.error("Failed to initialize browser")
        return
    
    try:
        # Send email
        result = browser_automation.send_email(
            email_account=args.email,
            password=password,
            to_email=args.to,
            subject=args.subject,
            body=args.body
        )
        
        if result["success"]:
            logger.info("Email sent successfully")
        else:
            logger.error(f"Failed to send email: {result['error']}")
    
    finally:
        # Shut down
        browser_automation.shutdown()

if __name__ == "__main__":
    main()
