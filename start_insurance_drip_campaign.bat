@echo off
REM Start Insurance Drip Campaign
REM This script starts a drip campaign for an insurance client

echo.
echo ===================================
echo    Start Insurance Drip Campaign
echo ===================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Get client information
set /p CLIENT_NAME="Enter client name: "
set /p PHONE_NUMBER="Enter client phone number: "
set /p EMAIL="Enter client email address: "
set /p INSURANCE_TYPE="Enter insurance type (life, health, etc.): "
set /p BUDGET="Enter client budget: "

REM Confirm information
echo.
echo Client Information:
echo Name: %CLIENT_NAME%
echo Phone: %PHONE_NUMBER%
echo Email: %EMAIL%
echo Insurance Type: %INSURANCE_TYPE%
echo Budget: %BUDGET%
echo.

set /p CONFIRM="Start drip campaign with this information? (y/n): "
if /i not "%CONFIRM%"=="y" (
    echo Drip campaign creation cancelled.
    goto end
)

REM Start drip campaign
echo.
echo Starting drip campaign for %CLIENT_NAME%...
echo.

python run_drip_campaign.py start --name "%CLIENT_NAME%" --phone "%PHONE_NUMBER%" --email "%EMAIL%" --insurance-type "%INSURANCE_TYPE%" --budget "%BUDGET%"

if %errorlevel% neq 0 (
    echo Failed to start drip campaign.
    goto end
)

echo.
echo Drip campaign started successfully.
echo.

REM Ask if user wants to run the agent
set /p RUN_AGENT="Do you want to run the drip campaign agent now? (y/n): "
if /i not "%RUN_AGENT%"=="y" (
    goto end
)

REM Run agent
echo.
echo Running drip campaign agent...
echo.

python run_drip_campaign.py run

:end
echo.
pause
