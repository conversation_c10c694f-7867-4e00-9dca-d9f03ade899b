{"name": "Code Optimization", "description": "Template for code optimization problems", "template": "\nOptimize the following code for {optimization_goal}:\n\n```python\n{original_code}\n```\n\nYour optimized solution should:\n1. Maintain the same functionality\n2. Improve {optimization_goal}\n3. Be well-structured and readable\n\nReturn only the optimized code.\n", "variables": ["original_code", "optimization_goal"]}