{
  "name": "Cybersecurity Agent Enhancement",
  "description": "Template for enhancing cybersecurity agent capabilities",
  "template": "You are tasked with enhancing the {capability} capability of a cybersecurity agent.

The goal is to optimize for {optimization_metric}.

The cybersecurity agent handles various security tasks including vulnerability scanning, threat detection, incident response, and security assessment.

Requirements:
1. The implementation must be thorough and comprehensive in identifying security issues
2. It must minimize false positives while maximizing true positives
3. It should provide actionable recommendations for remediation
4. It must operate securely and not introduce new vulnerabilities
5. It should leverage advanced security tools like Nmap, Metasploit, and John the Ripper appropriately

Your solution should be implemented as a Python function that follows this interface:
{interface}

Focus on creating a solution that maximizes {optimization_metric} while maintaining security best practices and ethical considerations.",
  "variables": ["capability", "optimization_metric", "interface"]
}
