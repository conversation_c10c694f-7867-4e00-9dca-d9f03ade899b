"""
NVIDIA Manager for coordinating all NVIDIA tool integrations.
"""
import asyncio
import logging
import os
import platform
import time
from typing import Dict, List, Optional, Any, Union
import subprocess
import importlib
import json
import sys

from core.logger import setup_logger
from nvidia_integration.config import NVIDIA_CONFIG

# Optional imports
try:
    import torch
    import torch.cuda
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

# Set up logger
logger = setup_logger("nvidia_manager")

class NVIDIAManager:
    """
    Central manager for all NVIDIA tool integrations.
    
    This class is responsible for:
    - Loading and initializing NVIDIA tools
    - Managing GPU resources
    - Providing interfaces for agents to use NVIDIA capabilities
    - Monitoring GPU usage and performance
    """
    
    def __init__(self, config: Dict = None):
        """
        Initialize the NVIDIA manager.
        
        Args:
            config (Dict, optional): Configuration override
        """
        self.config = config or NVIDIA_CONFIG
        self.enabled = self.config["enabled"]
        
        # Tool components
        self.riva_client = None
        self.clara_client = None
        self.isaac_client = None
        self.metropolis_client = None
        self.jetson_client = None
        self.optimizer = None
        
        # GPU status
        self.gpu_info = {}
        self.gpu_monitor_task = None
        
        # Initialization status
        self.initialized = False
        self.component_status = {
            "riva": False,
            "clara": False,
            "isaac": False,
            "metropolis": False,
            "jetson": False,
            "gpu_acceleration": False,
            "optimizers": False
        }
        
        logger.info("NVIDIA Manager initialized with config: %s", 
                    json.dumps({k: "..." if k in ["api_key", "password"] else v 
                               for k, v in self.config.items()}, indent=2))
    
    async def initialize(self):
        """Initialize all enabled NVIDIA components."""
        if not self.enabled:
            logger.info("NVIDIA integration is disabled. Skipping initialization.")
            return
        
        logger.info("Initializing NVIDIA components...")
        
        try:
            # Check for GPU and CUDA
            await self._check_gpu_availability()
            
            # Start GPU monitoring if enabled
            if self.config["logging"]["log_gpu_stats"]:
                self.gpu_monitor_task = asyncio.create_task(self._monitor_gpu_usage())
            
            # Initialize enabled components
            init_tasks = []
            
            # GPU acceleration
            if self.config["gpu_acceleration"]["enabled"]:
                init_tasks.append(self._initialize_gpu_acceleration())
            
            # NVIDIA Riva for speech AI
            if self.config["riva"]["enabled"]:
                init_tasks.append(self._initialize_riva())
            
            # NVIDIA Clara for healthcare AI
            if self.config["clara"]["enabled"]:
                init_tasks.append(self._initialize_clara())
            
            # NVIDIA Isaac for robotics
            if self.config["isaac"]["enabled"]:
                init_tasks.append(self._initialize_isaac())
            
            # NVIDIA Metropolis for vision AI
            if self.config["metropolis"]["enabled"]:
                init_tasks.append(self._initialize_metropolis())
            
            # NVIDIA Jetson for edge AI
            if self.config["jetson"]["enabled"]:
                init_tasks.append(self._initialize_jetson())
            
            # NVIDIA optimizers
            if self.config["optimizers"]["enabled"]:
                init_tasks.append(self._initialize_optimizers())
            
            # Wait for all initialization tasks to complete
            await asyncio.gather(*init_tasks)
            
            self.initialized = True
            logger.info("NVIDIA Manager initialized successfully")
            logger.info("Component status: %s", json.dumps(self.component_status, indent=2))
            
        except Exception as e:
            logger.exception("Error initializing NVIDIA Manager: %s", e)
    
    async def shutdown(self):
        """Shutdown all NVIDIA components and release resources."""
        logger.info("Shutting down NVIDIA components...")
        
        try:
            # Cancel GPU monitoring task if running
            if self.gpu_monitor_task and not self.gpu_monitor_task.done():
                self.gpu_monitor_task.cancel()
                try:
                    await self.gpu_monitor_task
                except asyncio.CancelledError:
                    pass
            
            # Shutdown components with specific cleanup needs
            shutdown_tasks = []
            
            # Wait for all shutdown tasks to complete
            if shutdown_tasks:
                await asyncio.gather(*shutdown_tasks)
            
            logger.info("NVIDIA Manager shutdown complete")
            
        except Exception as e:
            logger.exception("Error shutting down NVIDIA Manager: %s", e)
    
    async def _check_gpu_availability(self):
        """Check if GPU and CUDA are available."""
        # Check NVIDIA driver installation
        self.gpu_info = {"available": False, "driver_version": None, "cuda_version": None, "gpu_list": []}
        
        try:
            # Check system type for appropriate command
            if platform.system() == "Windows":
                # On Windows, use nvidia-smi
                result = subprocess.run(
                    ["nvidia-smi", "--query-gpu=name,driver_version,memory.total,memory.free,memory.used", "--format=csv,noheader"],
                    capture_output=True, text=True, check=False
                )
                if result.returncode == 0:
                    self.gpu_info["available"] = True
                    gpu_list = []
                    for i, line in enumerate(result.stdout.strip().split("\n")):
                        parts = line.split(", ")
                        if len(parts) >= 5:
                            gpu_list.append({
                                "id": i,
                                "name": parts[0],
                                "driver_version": parts[1],
                                "memory_total": parts[2],
                                "memory_free": parts[3],
                                "memory_used": parts[4]
                            })
                    
                    self.gpu_info["gpu_list"] = gpu_list
                    if gpu_list:
                        self.gpu_info["driver_version"] = gpu_list[0]["driver_version"]
                    
                    # Get CUDA version
                    result = subprocess.run(
                        ["nvcc", "--version"], 
                        capture_output=True, text=True, check=False
                    )
                    if result.returncode == 0:
                        for line in result.stdout.split("\n"):
                            if "release" in line:
                                version = line.split("release ")[1].split(",")[0]
                                self.gpu_info["cuda_version"] = version
                                break
            else:
                # On Linux/macOS, use nvidia-smi
                result = subprocess.run(
                    ["nvidia-smi", "--query-gpu=name,driver_version,memory.total,memory.free,memory.used", "--format=csv,noheader"],
                    capture_output=True, text=True, check=False
                )
                if result.returncode == 0:
                    self.gpu_info["available"] = True
                    gpu_list = []
                    for i, line in enumerate(result.stdout.strip().split("\n")):
                        parts = line.split(", ")
                        if len(parts) >= 5:
                            gpu_list.append({
                                "id": i,
                                "name": parts[0],
                                "driver_version": parts[1],
                                "memory_total": parts[2],
                                "memory_free": parts[3],
                                "memory_used": parts[4]
                            })
                    
                    self.gpu_info["gpu_list"] = gpu_list
                    if gpu_list:
                        self.gpu_info["driver_version"] = gpu_list[0]["driver_version"]
                    
                    # Get CUDA version
                    result = subprocess.run(
                        ["nvcc", "--version"], 
                        capture_output=True, text=True, check=False
                    )
                    if result.returncode == 0:
                        for line in result.stdout.split("\n"):
                            if "release" in line:
                                version = line.split("release ")[1].split(",")[0]
                                self.gpu_info["cuda_version"] = version
                                break
            
            # Check PyTorch CUDA availability
            if TORCH_AVAILABLE:
                self.gpu_info["torch_cuda_available"] = torch.cuda.is_available()
                if self.gpu_info["torch_cuda_available"]:
                    self.gpu_info["torch_cuda_device_count"] = torch.cuda.device_count()
                    self.gpu_info["torch_cuda_current_device"] = torch.cuda.current_device()
                    self.gpu_info["torch_cuda_device_name"] = torch.cuda.get_device_name(0)
                    self.gpu_info["torch_cuda_version"] = torch.version.cuda
            
            logger.info("GPU information: %s", json.dumps(self.gpu_info, indent=2))
            
        except Exception as e:
            logger.warning("Error checking GPU availability: %s", e)
    
    async def _monitor_gpu_usage(self):
        """Monitor GPU usage at regular intervals."""
        interval = self.config["logging"]["log_interval_seconds"]
        
        try:
            while True:
                if TORCH_AVAILABLE and torch.cuda.is_available():
                    for i in range(torch.cuda.device_count()):
                        memory_allocated = torch.cuda.memory_allocated(i) / (1024 ** 2)
                        memory_reserved = torch.cuda.memory_reserved(i) / (1024 ** 2)
                        logger.info(f"GPU {i} [{torch.cuda.get_device_name(i)}]: "
                                   f"Memory allocated: {memory_allocated:.2f} MB, "
                                   f"Memory reserved: {memory_reserved:.2f} MB")
                
                await asyncio.sleep(interval)
        
        except asyncio.CancelledError:
            logger.info("GPU monitoring stopped")
            raise
        except Exception as e:
            logger.exception("Error monitoring GPU usage: %s", e)
    
    async def _initialize_gpu_acceleration(self):
        """Initialize GPU acceleration for ML models."""
        try:
            if not TORCH_AVAILABLE:
                logger.warning("PyTorch not available. GPU acceleration disabled.")
                return
            
            if not torch.cuda.is_available():
                logger.warning("CUDA not available. GPU acceleration disabled.")
                return
            
            # Set CUDA devices
            devices = self.config["gpu_acceleration"]["devices"]
            device_str = ",".join(map(str, devices))
            os.environ["CUDA_VISIBLE_DEVICES"] = device_str
            
            # Set PyTorch settings
            torch.backends.cudnn.benchmark = self.config["optimizers"]["cudnn_benchmark"]
            
            # Configure default precision
            precision = self.config["gpu_acceleration"]["precision"]
            if precision == "fp16":
                torch.set_default_dtype(torch.float16)
            elif precision == "mixed":
                # Mixed precision is handled by training/inference code
                pass
            
            logger.info("GPU acceleration initialized with devices: %s", device_str)
            self.component_status["gpu_acceleration"] = True
            
        except Exception as e:
            logger.exception("Error initializing GPU acceleration: %s", e)
    
    async def _initialize_riva(self):
        """Initialize NVIDIA Riva speech AI client."""
        try:
            # Import Riva client lazily to avoid import errors if not installed
            from nvidia_integration.riva.riva_client import RivaClient
            
            riva_config = self.config["riva"]
            self.riva_client = RivaClient(riva_config)
            await self.riva_client.initialize()
            
            logger.info("NVIDIA Riva client initialized successfully")
            self.component_status["riva"] = True
            
        except ImportError:
            logger.warning("NVIDIA Riva client not available. Install nvidia-riva-client to enable Riva integration.")
        except Exception as e:
            logger.exception("Error initializing NVIDIA Riva client: %s", e)
    
    async def _initialize_clara(self):
        """Initialize NVIDIA Clara healthcare AI client."""
        try:
            # Import Clara client lazily
            from nvidia_integration.clara.clara_client import ClaraClient
            
            clara_config = self.config["clara"]
            self.clara_client = ClaraClient(clara_config)
            await self.clara_client.initialize()
            
            logger.info("NVIDIA Clara client initialized successfully")
            self.component_status["clara"] = True
            
        except ImportError:
            logger.warning("NVIDIA Clara client not available. Install nvidia-clara-parabricks to enable Clara integration.")
        except Exception as e:
            logger.exception("Error initializing NVIDIA Clara client: %s", e)
    
    async def _initialize_isaac(self):
        """Initialize NVIDIA Isaac robotics client."""
        try:
            # Import Isaac client lazily
            from nvidia_integration.isaac.isaac_client import IsaacClient
            
            isaac_config = self.config["isaac"]
            self.isaac_client = IsaacClient(isaac_config)
            await self.isaac_client.initialize()
            
            logger.info("NVIDIA Isaac client initialized successfully")
            self.component_status["isaac"] = True
            
        except ImportError:
            logger.warning("NVIDIA Isaac client not available. Isaac integration disabled.")
        except Exception as e:
            logger.exception("Error initializing NVIDIA Isaac client: %s", e)
    
    async def _initialize_metropolis(self):
        """Initialize NVIDIA Metropolis vision AI client."""
        try:
            # Import Metropolis client lazily
            from nvidia_integration.metropolis.metropolis_client import MetropolisClient
            
            metropolis_config = self.config["metropolis"]
            self.metropolis_client = MetropolisClient(metropolis_config)
            await self.metropolis_client.initialize()
            
            logger.info("NVIDIA Metropolis client initialized successfully")
            self.component_status["metropolis"] = True
            
        except ImportError:
            logger.warning("NVIDIA Metropolis client not available. Metropolis integration disabled.")
        except Exception as e:
            logger.exception("Error initializing NVIDIA Metropolis client: %s", e)
    
    async def _initialize_jetson(self):
        """Initialize NVIDIA Jetson edge AI client."""
        try:
            # Import Jetson client lazily
            from nvidia_integration.jetson.jetson_client import JetsonClient
            
            jetson_config = self.config["jetson"]
            self.jetson_client = JetsonClient(jetson_config)
            await self.jetson_client.initialize()
            
            logger.info("NVIDIA Jetson client initialized successfully")
            self.component_status["jetson"] = True
            
        except ImportError:
            logger.warning("NVIDIA Jetson client not available. Jetson integration disabled.")
        except Exception as e:
            logger.exception("Error initializing NVIDIA Jetson client: %s", e)
    
    async def _initialize_optimizers(self):
        """Initialize NVIDIA optimizers for ML models."""
        try:
            # Import optimizers lazily
            from nvidia_integration.optimizers.nvidia_optimizer import NVIDIAOptimizer
            
            optimizer_config = self.config["optimizers"]
            self.optimizer = NVIDIAOptimizer(optimizer_config)
            await self.optimizer.initialize()
            
            logger.info("NVIDIA optimizer initialized successfully")
            self.component_status["optimizers"] = True
            
        except ImportError:
            logger.warning("NVIDIA optimizers not available. Install TensorRT to enable optimization.")
        except Exception as e:
            logger.exception("Error initializing NVIDIA optimizers: %s", e)
    
    # Common API methods for agents to access NVIDIA capabilities
    
    def optimize_model(self, model, **kwargs):
        """
        Optimize a machine learning model using NVIDIA tools.
        
        Args:
            model: The model to optimize
            **kwargs: Additional optimization parameters
            
        Returns:
            Optimized model
        """
        if not self.optimizer:
            logger.warning("NVIDIA optimizer not initialized")
            return model
        
        return self.optimizer.optimize_model(model, **kwargs)
    
    async def speech_to_text(self, audio_data, **kwargs):
        """
        Convert speech to text using NVIDIA Riva.
        
        Args:
            audio_data: Audio data to transcribe
            **kwargs: Additional transcription parameters
            
        Returns:
            Transcribed text
        """
        if not self.riva_client:
            logger.warning("NVIDIA Riva client not initialized")
            return None
        
        return await self.riva_client.speech_to_text(audio_data, **kwargs)
    
    async def text_to_speech(self, text, **kwargs):
        """
        Convert text to speech using NVIDIA Riva.
        
        Args:
            text: Text to synthesize
            **kwargs: Additional synthesis parameters
            
        Returns:
            Synthesized audio data
        """
        if not self.riva_client:
            logger.warning("NVIDIA Riva client not initialized")
            return None
        
        return await self.riva_client.text_to_speech(text, **kwargs)
    
    async def analyze_medical_data(self, data, data_type, **kwargs):
        """
        Analyze medical data using NVIDIA Clara.
        
        Args:
            data: Medical data to analyze
            data_type: Type of medical data (e.g., 'image', 'text')
            **kwargs: Additional analysis parameters
            
        Returns:
            Analysis results
        """
        if not self.clara_client:
            logger.warning("NVIDIA Clara client not initialized")
            return None
        
        return await self.clara_client.analyze_medical_data(data, data_type, **kwargs)
    
    def get_gpu_info(self):
        """
        Get information about available GPUs.
        
        Returns:
            Dict containing GPU information
        """
        return self.gpu_info
    
    def get_component_status(self):
        """
        Get the status of all NVIDIA components.
        
        Returns:
            Dict containing component status
        """
        return self.component_status