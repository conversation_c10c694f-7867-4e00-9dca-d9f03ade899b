"""
Agent Security Tools Provider for the Multi-Agent AI System.

This module provides a service that ensures all agents have access to security tools.
"""
import asyncio
import json
import logging
import os
import sys
from typing import Dict, List, Optional, Any, Union
from datetime import datetime

# Add parent directory to path to import from core
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.logger import setup_logger
from services.security_tools_manager import SecurityToolsManager

# Set up logger
logger = setup_logger("agent_security_tools_provider")

class AgentSecurityToolsProvider:
    """
    Agent Security Tools Provider for the Multi-Agent AI System.
    
    This class provides a service that ensures all agents have access to security tools.
    """
    
    def __init__(self, config: Dict, state_manager: Any):
        """
        Initialize the agent security tools provider.
        
        Args:
            config (Dict): Provider configuration
            state_manager (Any): State manager instance
        """
        self.config = config
        self.state_manager = state_manager
        self.enabled = config.get("enabled", True)
        
        # Security tools manager
        self.tools_manager = None
        
        # Agent access registry
        self.agent_access = {}
        
        logger.info("Agent security tools provider initialized")
    
    async def initialize(self):
        """Initialize the agent security tools provider."""
        if not self.enabled:
            logger.warning("Agent security tools provider is disabled")
            return
        
        try:
            # Initialize security tools manager
            tools_config = self.config.get("tools_config", {})
            self.tools_manager = SecurityToolsManager(tools_config)
            await self.tools_manager.initialize()
            
            # Load agent access registry from state
            agent_access = await self.state_manager.get_state("security", "agent_tool_access")
            if agent_access:
                self.agent_access = agent_access
            
            logger.info("Agent security tools provider initialized")
            
        except Exception as e:
            logger.exception(f"Error initializing agent security tools provider: {e}")
            self.enabled = False
    
    async def register_agent(self, agent_id: str, agent_type: str) -> bool:
        """
        Register an agent with the security tools provider.
        
        Args:
            agent_id (str): Agent identifier
            agent_type (str): Agent type
            
        Returns:
            bool: True if registered successfully, False otherwise
        """
        if not self.enabled:
            logger.warning("Agent security tools provider is disabled")
            return False
        
        try:
            # Create agent entry if it doesn't exist
            if agent_id not in self.agent_access:
                self.agent_access[agent_id] = {
                    "agent_type": agent_type,
                    "registered_at": datetime.now().isoformat(),
                    "tools_access": {},
                }
            
            # Get default tools for agent type
            default_tools = self._get_default_tools_for_agent_type(agent_type)
            
            # Grant access to default tools
            for tool_id in default_tools:
                await self.grant_tool_access(agent_id, tool_id)
            
            # Save agent access registry to state
            await self.state_manager.update_state("security", "agent_tool_access", self.agent_access)
            
            logger.info(f"Agent {agent_id} registered with security tools provider")
            return True
            
        except Exception as e:
            logger.exception(f"Error registering agent {agent_id}: {e}")
            return False
    
    async def grant_tool_access(self, agent_id: str, tool_id: str) -> bool:
        """
        Grant an agent access to a security tool.
        
        Args:
            agent_id (str): Agent identifier
            tool_id (str): Tool identifier
            
        Returns:
            bool: True if access granted successfully, False otherwise
        """
        if not self.enabled:
            logger.warning("Agent security tools provider is disabled")
            return False
        
        if not self.tools_manager:
            logger.warning("Security tools manager not initialized")
            return False
        
        try:
            # Check if agent is registered
            if agent_id not in self.agent_access:
                logger.warning(f"Agent {agent_id} not registered")
                return False
            
            # Ensure tool is accessible
            accessible = await self.tools_manager.ensure_tool_accessible(tool_id)
            if not accessible:
                logger.warning(f"Tool {tool_id} is not accessible")
                return False
            
            # Grant access
            self.agent_access[agent_id]["tools_access"][tool_id] = {
                "granted_at": datetime.now().isoformat(),
                "status": "granted",
            }
            
            # Save agent access registry to state
            await self.state_manager.update_state("security", "agent_tool_access", self.agent_access)
            
            logger.info(f"Granted agent {agent_id} access to tool {tool_id}")
            return True
            
        except Exception as e:
            logger.exception(f"Error granting agent {agent_id} access to tool {tool_id}: {e}")
            return False
    
    async def revoke_tool_access(self, agent_id: str, tool_id: str) -> bool:
        """
        Revoke an agent's access to a security tool.
        
        Args:
            agent_id (str): Agent identifier
            tool_id (str): Tool identifier
            
        Returns:
            bool: True if access revoked successfully, False otherwise
        """
        if not self.enabled:
            logger.warning("Agent security tools provider is disabled")
            return False
        
        try:
            # Check if agent is registered
            if agent_id not in self.agent_access:
                logger.warning(f"Agent {agent_id} not registered")
                return False
            
            # Revoke access
            if tool_id in self.agent_access[agent_id]["tools_access"]:
                self.agent_access[agent_id]["tools_access"][tool_id] = {
                    "revoked_at": datetime.now().isoformat(),
                    "status": "revoked",
                }
            
            # Save agent access registry to state
            await self.state_manager.update_state("security", "agent_tool_access", self.agent_access)
            
            logger.info(f"Revoked agent {agent_id} access to tool {tool_id}")
            return True
            
        except Exception as e:
            logger.exception(f"Error revoking agent {agent_id} access to tool {tool_id}: {e}")
            return False
    
    async def check_tool_access(self, agent_id: str, tool_id: str) -> bool:
        """
        Check if an agent has access to a security tool.
        
        Args:
            agent_id (str): Agent identifier
            tool_id (str): Tool identifier
            
        Returns:
            bool: True if agent has access, False otherwise
        """
        if not self.enabled:
            logger.warning("Agent security tools provider is disabled")
            return False
        
        try:
            # Check if agent is registered
            if agent_id not in self.agent_access:
                logger.warning(f"Agent {agent_id} not registered")
                return False
            
            # Check if agent has access to tool
            tool_access = self.agent_access[agent_id]["tools_access"].get(tool_id, {})
            
            return tool_access.get("status") == "granted"
            
        except Exception as e:
            logger.exception(f"Error checking agent {agent_id} access to tool {tool_id}: {e}")
            return False
    
    async def run_tool_for_agent(self, agent_id: str, tool_id: str, args: List[str] = None) -> Dict:
        """
        Run a security tool for an agent.
        
        Args:
            agent_id (str): Agent identifier
            tool_id (str): Tool identifier
            args (List[str]): Tool arguments
            
        Returns:
            Dict: Tool execution results
        """
        if not self.enabled:
            return {"error": "Agent security tools provider is disabled"}
        
        if not self.tools_manager:
            return {"error": "Security tools manager not initialized"}
        
        try:
            # Check if agent has access to tool
            has_access = await self.check_tool_access(agent_id, tool_id)
            if not has_access:
                return {"error": f"Agent {agent_id} does not have access to tool {tool_id}"}
            
            # Run tool
            result = await self.tools_manager.run_tool(tool_id, args)
            
            # Log tool usage
            await self._log_tool_usage(agent_id, tool_id, args, result)
            
            return result
            
        except Exception as e:
            logger.exception(f"Error running tool {tool_id} for agent {agent_id}: {e}")
            return {"error": str(e)}
    
    async def _log_tool_usage(self, agent_id: str, tool_id: str, args: List[str], result: Dict):
        """
        Log tool usage by an agent.
        
        Args:
            agent_id (str): Agent identifier
            tool_id (str): Tool identifier
            args (List[str]): Tool arguments
            result (Dict): Tool execution results
        """
        try:
            # Create log entry
            log_entry = {
                "agent_id": agent_id,
                "tool_id": tool_id,
                "args": args,
                "returncode": result.get("returncode"),
                "timestamp": datetime.now().isoformat(),
            }
            
            # Get existing logs
            tool_usage_logs = await self.state_manager.get_state("security", "tool_usage_logs")
            if not tool_usage_logs:
                tool_usage_logs = []
            
            # Add log entry
            tool_usage_logs.append(log_entry)
            
            # Save logs to state
            await self.state_manager.update_state("security", "tool_usage_logs", tool_usage_logs)
            
        except Exception as e:
            logger.exception(f"Error logging tool usage: {e}")
    
    def _get_default_tools_for_agent_type(self, agent_type: str) -> List[str]:
        """
        Get default tools for an agent type.
        
        Args:
            agent_type (str): Agent type
            
        Returns:
            List[str]: List of tool identifiers
        """
        # Default tools for all agents
        default_tools = ["nmap", "john"]
        
        # Agent-specific tools
        if agent_type == "cybersecurity":
            default_tools.extend(["wireshark", "metasploit", "burpsuite", "aircrack-ng", "sqlmap", "zaproxy", "theharvester", "nikto", "pentestgpt"])
        elif agent_type == "insurance":
            default_tools.extend(["nmap", "john"])
        elif agent_type == "trading":
            default_tools.extend(["nmap", "john"])
        elif agent_type == "music":
            default_tools.extend(["nmap", "john"])
        
        return default_tools
