"""
Drip Campaign Workflow for the Multi-Agent AI System.

This module provides the workflow for the insurance drip campaign,
defining the sequence of communications and handling client responses.
"""
import asyncio
import json
import logging
import os
import sys
import time
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta

# Add parent directory to path to import from core
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.logger import setup_logger
from insurance_drip_campaign_agent import InsuranceDripCampaignAgent
from google_voice_ui_tars_automation import GoogleVoiceUITarsAutomation
from ui_tars_gmail_automation import GmailUITarsAutomation
from elevenlabs_voicemail_generator import ElevenLabsVoicemailGenerator

# Set up logger
logger = setup_logger("drip_campaign_workflow")

class DripCampaignWorkflow:
    """
    Drip Campaign Workflow for the Multi-Agent AI System.

    This class provides the workflow for the insurance drip campaign,
    defining the sequence of communications and handling client responses.
    """
    
    def __init__(self, 
                 config_path: str = "config/drip_campaign_config.json",
                 ui_tars_api_url: str = "http://localhost:8080",
                 ui_tars_model_name: str = "UI-TARS-1.5-7B",
                 ui_tars_installation_path: Optional[str] = None,
                 elevenlabs_api_key: Optional[str] = None):
        """
        Initialize the Drip Campaign Workflow.
        
        Args:
            config_path (str): Path to drip campaign configuration
            ui_tars_api_url (str): URL of the UI-TARS API
            ui_tars_model_name (str): Name of the model to use
            ui_tars_installation_path (Optional[str]): Path to UI-TARS installation
            elevenlabs_api_key (Optional[str]): ElevenLabs API key
        """
        self.config_path = config_path
        self.ui_tars_api_url = ui_tars_api_url
        self.ui_tars_model_name = ui_tars_model_name
        self.ui_tars_installation_path = ui_tars_installation_path
        self.elevenlabs_api_key = elevenlabs_api_key
        
        self.drip_campaign_agent = None
        self.config = {}
        
    async def initialize(self) -> bool:
        """
        Initialize the Drip Campaign Workflow.
        
        Returns:
            bool: True if initialization was successful, False otherwise
        """
        logger.info("Initializing Drip Campaign Workflow")
        
        # Load configuration
        try:
            with open(self.config_path, "r", encoding="utf-8") as f:
                self.config = json.load(f)
            
            logger.info(f"Loaded drip campaign configuration")
        except Exception as e:
            logger.exception(f"Error loading drip campaign configuration: {e}")
            return False
        
        # Initialize drip campaign agent
        self.drip_campaign_agent = InsuranceDripCampaignAgent(
            config_path=self.config_path,
            ui_tars_api_url=self.ui_tars_api_url,
            ui_tars_model_name=self.ui_tars_model_name,
            ui_tars_installation_path=self.ui_tars_installation_path,
            elevenlabs_api_key=self.elevenlabs_api_key
        )
        
        # Initialize agent
        success = await self.drip_campaign_agent.initialize()
        if not success:
            logger.error("Failed to initialize drip campaign agent")
            return False
        
        logger.info("Drip Campaign Workflow initialized successfully")
        return True
    
    async def start_campaign_for_client(self, 
                                      client_name: str,
                                      phone_number: str,
                                      email: str,
                                      insurance_type: str,
                                      budget: str) -> Dict:
        """
        Start a drip campaign for a client.
        
        Args:
            client_name (str): Client's name
            phone_number (str): Client's phone number
            email (str): Client's email address
            insurance_type (str): Type of insurance
            budget (str): Client's budget
            
        Returns:
            Dict: Result of the operation
        """
        logger.info(f"Starting drip campaign for {client_name} ({phone_number}, {email})")
        
        if not self.drip_campaign_agent:
            return {"success": False, "error": "Drip campaign agent not initialized"}
        
        # Start campaign
        result = await self.drip_campaign_agent.start_campaign(
            client_name=client_name,
            phone_number=phone_number,
            email=email,
            insurance_type=insurance_type,
            budget=budget
        )
        
        if result.get("success"):
            logger.info(f"Started drip campaign for {client_name} with ID {result.get('campaign_id')}")
        else:
            logger.error(f"Failed to start drip campaign for {client_name}: {result.get('error')}")
        
        return result
    
    async def stop_campaign(self, identifier: str, identifier_type: str = "campaign_id") -> Dict:
        """
        Stop a drip campaign.
        
        Args:
            identifier (str): Campaign identifier, client name, phone number, or email
            identifier_type (str): Type of identifier (campaign_id, name, phone, email)
            
        Returns:
            Dict: Result of the operation
        """
        logger.info(f"Stopping drip campaign with {identifier_type}: {identifier}")
        
        if not self.drip_campaign_agent:
            return {"success": False, "error": "Drip campaign agent not initialized"}
        
        # Stop campaign
        result = await self.drip_campaign_agent.stop_campaign(
            identifier=identifier,
            identifier_type=identifier_type
        )
        
        if result.get("success"):
            logger.info(f"Stopped drip campaign for {result.get('client_name')} with ID {result.get('campaign_id')}")
        else:
            logger.error(f"Failed to stop drip campaign with {identifier_type} {identifier}: {result.get('error')}")
        
        return result
    
    async def handle_client_response(self, 
                                   response_type: str,
                                   sender: str,
                                   content: str) -> Dict:
        """
        Handle a response from a client.
        
        Args:
            response_type (str): Type of response (email, text, call)
            sender (str): Sender identifier (email or phone)
            content (str): Response content
            
        Returns:
            Dict: Result of the operation
        """
        logger.info(f"Handling {response_type} response from {sender}")
        
        if not self.drip_campaign_agent:
            return {"success": False, "error": "Drip campaign agent not initialized"}
        
        # Handle response
        result = await self.drip_campaign_agent.handle_response(
            response_type=response_type,
            sender=sender,
            content=content
        )
        
        if result.get("success"):
            logger.info(f"Handled {response_type} response from {sender} for campaign {result.get('campaign_id')}")
        else:
            logger.error(f"Failed to handle {response_type} response from {sender}: {result.get('error')}")
        
        return result
