"""
Agent Network Graph Visualization for the Multi-Agent AI System.

This module provides visualization capabilities for agent relationships,
allowing users to see how agents interact with each other.
"""
import os
import json
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import networkx as nx
import matplotlib.pyplot as plt
from matplotlib.colors import LinearSegmentedColormap
import matplotlib.patches as mpatches
from pathlib import Path

from core.logger import setup_logger
from core.state_manager import StateManager

# Set up logger
logger = setup_logger("agent_network_graph")

class AgentNetworkGraph:
    """
    Visualizer for agent relationships.
    
    This class provides visualization capabilities for agent relationships,
    allowing users to see how agents interact with each other.
    """
    
    def __init__(self, state_manager: StateManager, output_dir: Optional[str] = None):
        """
        Initialize the agent network graph visualizer.
        
        Args:
            state_manager (StateManager): System state manager
            output_dir (Optional[str]): Directory to save visualizations
        """
        self.state_manager = state_manager
        self.output_dir = output_dir or "visualizations"
        
        # Create output directory if it doesn't exist
        os.makedirs(self.output_dir, exist_ok=True)
    
    async def visualize_agent_network(
        self,
        include_messages: bool = True,
        time_window: Optional[int] = None,
        save: bool = True
    ) -> Optional[str]:
        """
        Visualize the agent network.
        
        Args:
            include_messages (bool): Whether to include message counts on edges
            time_window (Optional[int]): Time window in seconds for messages
            save (bool): Whether to save the visualization
            
        Returns:
            Optional[str]: Path to the saved visualization if save=True, None otherwise
        """
        # Get agent relationships from state manager
        relationships = await self.state_manager.get_state("coordinator", "agent_relationships")
        
        if not relationships:
            logger.error("No agent relationships found")
            return None
        
        # Get agent capabilities
        agent_capabilities = {}
        for agent_id in relationships:
            capabilities = await self.state_manager.get_state("agents", f"{agent_id}.capabilities")
            if capabilities:
                agent_capabilities[agent_id] = capabilities
        
        # Create graph
        G = nx.DiGraph()
        
        # Add nodes (agents)
        for agent_id in relationships:
            # Get agent name
            agent_name = await self.state_manager.get_state("agents", f"{agent_id}.name")
            if not agent_name:
                agent_name = agent_id
            
            # Get agent status
            agent_status = await self.state_manager.get_state("agents", f"{agent_id}.status")
            if not agent_status:
                agent_status = "unknown"
            
            # Add node
            G.add_node(
                agent_id,
                name=agent_name,
                status=agent_status,
                capabilities=agent_capabilities.get(agent_id, [])
            )
        
        # Add edges (relationships)
        for agent_id, agent_data in relationships.items():
            collaborators = agent_data.get("collaborators", [])
            trust_scores = agent_data.get("trust_scores", {})
            communication_stats = agent_data.get("communication_stats", {})
            
            for collaborator_id in collaborators:
                if collaborator_id in relationships:
                    # Get trust score
                    trust_score = trust_scores.get(collaborator_id, 0.5)
                    
                    # Get communication stats
                    comm_stats = communication_stats.get(collaborator_id, {})
                    messages_sent = comm_stats.get("messages_sent", 0)
                    messages_received = comm_stats.get("messages_received", 0)
                    last_communication = comm_stats.get("last_communication")
                    
                    # Check time window if specified
                    if time_window and last_communication:
                        last_time = datetime.fromisoformat(last_communication)
                        now = datetime.now()
                        time_diff = (now - last_time).total_seconds()
                        if time_diff > time_window:
                            continue
                    
                    # Add edge
                    G.add_edge(
                        agent_id,
                        collaborator_id,
                        trust=trust_score,
                        messages_sent=messages_sent,
                        messages_received=messages_received,
                        last_communication=last_communication,
                        weight=trust_score
                    )
        
        # Add message edges if requested
        if include_messages:
            # Get message history
            message_history = await self.state_manager.get_state("agent_manager", "message_history")
            
            if message_history:
                # Count messages between agents
                message_counts = {}
                for message_id, message in message_history.items():
                    sender_id = message.get("sender_id")
                    recipient_id = message.get("recipient_id")
                    timestamp = message.get("timestamp")
                    
                    # Skip if sender or recipient is not an agent
                    if sender_id not in relationships or recipient_id not in relationships:
                        continue
                    
                    # Check time window if specified
                    if time_window and timestamp:
                        message_time = datetime.fromisoformat(timestamp)
                        now = datetime.now()
                        time_diff = (now - message_time).total_seconds()
                        if time_diff > time_window:
                            continue
                    
                    # Update message count
                    key = (sender_id, recipient_id)
                    if key not in message_counts:
                        message_counts[key] = 0
                    message_counts[key] += 1
                
                # Add or update edges
                for (sender_id, recipient_id), count in message_counts.items():
                    if G.has_edge(sender_id, recipient_id):
                        # Update existing edge
                        G[sender_id][recipient_id]["messages"] = count
                    else:
                        # Add new edge
                        G.add_edge(
                            sender_id,
                            recipient_id,
                            messages=count,
                            weight=0.1  # Weak relationship based only on messages
                        )
        
        # Create figure
        plt.figure(figsize=(12, 10))
        
        # Set title
        plt.title("Agent Network Graph")
        
        # Create layout
        pos = nx.spring_layout(G, k=0.3, iterations=50)
        
        # Get node colors based on status
        node_colors = []
        for node in G.nodes():
            status = G.nodes[node].get("status", "unknown")
            if status == "running":
                node_colors.append("green")
            elif status == "initialized":
                node_colors.append("blue")
            elif status == "error":
                node_colors.append("red")
            elif status == "shutdown":
                node_colors.append("gray")
            else:
                node_colors.append("lightgray")
        
        # Get node sizes based on number of capabilities
        node_sizes = []
        for node in G.nodes():
            capabilities = G.nodes[node].get("capabilities", [])
            size = 300 + 100 * len(capabilities)
            node_sizes.append(size)
        
        # Get edge colors and widths based on trust scores
        edge_colors = []
        edge_widths = []
        for u, v, data in G.edges(data=True):
            trust = data.get("trust", 0.1)
            if trust > 0.8:
                edge_colors.append("green")
                edge_widths.append(2.0)
            elif trust > 0.5:
                edge_colors.append("blue")
                edge_widths.append(1.5)
            elif trust > 0.2:
                edge_colors.append("orange")
                edge_widths.append(1.0)
            else:
                edge_colors.append("red")
                edge_widths.append(0.5)
        
        # Draw nodes
        nx.draw_networkx_nodes(
            G, pos,
            node_color=node_colors,
            node_size=node_sizes,
            alpha=0.8
        )
        
        # Draw edges
        nx.draw_networkx_edges(
            G, pos,
            width=edge_widths,
            edge_color=edge_colors,
            alpha=0.7,
            arrows=True,
            arrowsize=15,
            connectionstyle="arc3,rad=0.1"
        )
        
        # Draw labels
        nx.draw_networkx_labels(
            G, pos,
            font_size=10,
            font_family="sans-serif"
        )
        
        # Draw edge labels if including messages
        if include_messages:
            edge_labels = {}
            for u, v, data in G.edges(data=True):
                messages = data.get("messages", 0)
                if messages > 0:
                    edge_labels[(u, v)] = f"{messages}"
            
            nx.draw_networkx_edge_labels(
                G, pos,
                edge_labels=edge_labels,
                font_size=8
            )
        
        # Add legend
        legend_elements = [
            mpatches.Patch(color="green", label="Running"),
            mpatches.Patch(color="blue", label="Initialized"),
            mpatches.Patch(color="red", label="Error"),
            mpatches.Patch(color="gray", label="Shutdown"),
            mpatches.Patch(color="lightgray", label="Unknown"),
        ]
        plt.legend(handles=legend_elements, loc="upper right")
        
        # Save or show
        if save:
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            filename = f"agent_network_{timestamp}.png"
            filepath = os.path.join(self.output_dir, filename)
            plt.savefig(filepath)
            plt.close()
            logger.info(f"Agent network graph saved to {filepath}")
            return filepath
        else:
            plt.show()
            plt.close()
            return None
    
    async def visualize_agent_capabilities(self, save: bool = True) -> Optional[str]:
        """
        Visualize agent capabilities.
        
        Args:
            save (bool): Whether to save the visualization
            
        Returns:
            Optional[str]: Path to the saved visualization if save=True, None otherwise
        """
        # Get all agents
        agents = await self.state_manager.get_state("agents")
        
        if not agents:
            logger.error("No agents found")
            return None
        
        # Extract capabilities
        agent_capabilities = {}
        for agent_id, agent_data in agents.items():
            capabilities = agent_data.get("capabilities", [])
            if capabilities:
                agent_capabilities[agent_id] = capabilities
        
        if not agent_capabilities:
            logger.error("No agent capabilities found")
            return None
        
        # Create bipartite graph
        B = nx.Graph()
        
        # Add agent nodes
        for agent_id in agent_capabilities:
            B.add_node(agent_id, bipartite=0)  # Agents are in set 0
        
        # Add capability nodes and edges
        all_capabilities = set()
        for agent_id, capabilities in agent_capabilities.items():
            for capability in capabilities:
                all_capabilities.add(capability)
                B.add_node(capability, bipartite=1)  # Capabilities are in set 1
                B.add_edge(agent_id, capability)
        
        # Create figure
        plt.figure(figsize=(14, 10))
        
        # Set title
        plt.title("Agent Capabilities")
        
        # Create layout
        pos = nx.spring_layout(B, k=0.5, iterations=50)
        
        # Draw agent nodes
        nx.draw_networkx_nodes(
            B, pos,
            nodelist=[n for n, d in B.nodes(data=True) if d["bipartite"] == 0],
            node_color="lightblue",
            node_size=500,
            alpha=0.8,
            label="Agents"
        )
        
        # Draw capability nodes
        nx.draw_networkx_nodes(
            B, pos,
            nodelist=[n for n, d in B.nodes(data=True) if d["bipartite"] == 1],
            node_color="lightgreen",
            node_size=300,
            alpha=0.8,
            label="Capabilities"
        )
        
        # Draw edges
        nx.draw_networkx_edges(B, pos, width=1.0, alpha=0.5)
        
        # Draw labels
        nx.draw_networkx_labels(B, pos, font_size=10, font_family="sans-serif")
        
        # Add legend
        plt.legend()
        
        # Save or show
        if save:
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            filename = f"agent_capabilities_{timestamp}.png"
            filepath = os.path.join(self.output_dir, filename)
            plt.savefig(filepath)
            plt.close()
            logger.info(f"Agent capabilities graph saved to {filepath}")
            return filepath
        else:
            plt.show()
            plt.close()
            return None
