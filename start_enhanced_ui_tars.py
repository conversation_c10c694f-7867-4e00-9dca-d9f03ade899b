"""
Start Enhanced UI-TARS Integration.

This script starts the enhanced UI-TARS integration with all components.
"""
import os
import sys
import asyncio
import argparse
import logging
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent))

try:
    from core.logger import setup_logger
    from ui_tars.connector.enhanced_ui_tars_connector import EnhancedUITarsConnector
    from ui_tars.agent.enhanced_ui_tars_agent import EnhancedUITarsAgent
    from ui_tars.agent.gmail_automation_agent import GmailAutomationAgent
    from ui_tars.agent.google_voice_automation_agent import GoogleVoiceAutomationAgent
except ImportError as e:
    print(f"Error importing required modules: {e}")
    print("Make sure you're running this script from the project root directory.")
    sys.exit(1)

# Set up logger
logger = setup_logger("start_enhanced_ui_tars")

# Global flag to control system shutdown
shutdown_event = asyncio.Event()

async def start_ui_tars_connector(config):
    """
    Start the enhanced UI-TARS connector.
    
    Args:
        config (dict): Configuration dictionary
        
    Returns:
        EnhancedUITarsConnector: Initialized connector
    """
    logger.info("Starting enhanced UI-TARS connector")
    
    # Extract configuration
    ui_tars_config = config.get("ui_tars", {})
    
    # Create connector
    connector = EnhancedUITarsConnector(
        api_url=ui_tars_config.get("api_url", "http://localhost:8080"),
        api_key=ui_tars_config.get("api_key"),
        model_name=ui_tars_config.get("model_name", "UI-TARS-1.5-7B"),
        installation_path=ui_tars_config.get("installation_path"),
        browser_type=ui_tars_config.get("browser_type", "chrome"),
        browser_path=ui_tars_config.get("browser_path"),
        remote_debugging_port=ui_tars_config.get("remote_debugging_port", 9222),
        auto_start=ui_tars_config.get("auto_start", True),
        auto_restart=ui_tars_config.get("auto_restart", True)
    )
    
    # Initialize connector
    success = await connector.initialize()
    if not success:
        logger.error("Failed to initialize UI-TARS connector")
        return None
    
    logger.info("Enhanced UI-TARS connector started successfully")
    return connector

async def start_gmail_agent(config, connector):
    """
    Start the Gmail automation agent.
    
    Args:
        config (dict): Configuration dictionary
        connector (EnhancedUITarsConnector): UI-TARS connector
        
    Returns:
        GmailAutomationAgent: Initialized agent
    """
    logger.info("Starting Gmail automation agent")
    
    # Extract configuration
    gmail_config = config.get("gmail", {})
    
    # Create agent
    agent = GmailAutomationAgent(
        agent_id="gmail_agent",
        config=config,
        api_url=connector.api_url,
        api_key=connector.api_key,
        model_name=connector.model_name,
        installation_path=connector.installation_path,
        browser_type=connector.browser_type,
        browser_path=connector.browser_path,
        remote_debugging_port=connector.remote_debugging_port,
        auto_start=False,  # Don't start UI-TARS again
        auto_restart=connector.auto_restart,
        gmail_url=gmail_config.get("url", "https://mail.google.com"),
        default_email=gmail_config.get("default_email"),
        default_password=gmail_config.get("default_password")
    )
    
    # Initialize agent
    success = await agent.initialize()
    if not success:
        logger.error("Failed to initialize Gmail agent")
        return None
    
    logger.info("Gmail automation agent started successfully")
    return agent

async def start_google_voice_agent(config, connector):
    """
    Start the Google Voice automation agent.
    
    Args:
        config (dict): Configuration dictionary
        connector (EnhancedUITarsConnector): UI-TARS connector
        
    Returns:
        GoogleVoiceAutomationAgent: Initialized agent
    """
    logger.info("Starting Google Voice automation agent")
    
    # Extract configuration
    google_voice_config = config.get("google_voice", {})
    
    # Create agent
    agent = GoogleVoiceAutomationAgent(
        agent_id="google_voice_agent",
        config=config,
        api_url=connector.api_url,
        api_key=connector.api_key,
        model_name=connector.model_name,
        installation_path=connector.installation_path,
        browser_type=connector.browser_type,
        browser_path=connector.browser_path,
        remote_debugging_port=connector.remote_debugging_port,
        auto_start=False,  # Don't start UI-TARS again
        auto_restart=connector.auto_restart,
        google_voice_url=google_voice_config.get("url", "https://voice.google.com"),
        default_email=google_voice_config.get("default_email"),
        default_password=google_voice_config.get("default_password"),
        default_phone_number=google_voice_config.get("default_phone_number")
    )
    
    # Initialize agent
    success = await agent.initialize()
    if not success:
        logger.error("Failed to initialize Google Voice agent")
        return None
    
    logger.info("Google Voice automation agent started successfully")
    return agent

async def load_config():
    """
    Load configuration from file.
    
    Returns:
        dict: Configuration dictionary
    """
    config_path = Path("ui_tars/config.json")
    if not config_path.exists():
        logger.error(f"Configuration file not found: {config_path}")
        return {}
    
    try:
        import json
        with open(config_path, "r") as f:
            config = json.load(f)
        
        logger.info(f"Configuration loaded from {config_path}")
        return config
    except Exception as e:
        logger.error(f"Error loading configuration: {e}")
        return {}

async def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Start Enhanced UI-TARS Integration")
    parser.add_argument("--gmail", action="store_true", help="Start Gmail automation agent")
    parser.add_argument("--voice", action="store_true", help="Start Google Voice automation agent")
    parser.add_argument("--all", action="store_true", help="Start all components")
    
    args = parser.parse_args()
    
    print("Enhanced UI-TARS Integration")
    print("===========================")
    print()
    
    # Load configuration
    config = await load_config()
    if not config:
        print("Error: Failed to load configuration")
        return 1
    
    try:
        # Start UI-TARS connector
        connector = await start_ui_tars_connector(config)
        if not connector:
            print("Error: Failed to start UI-TARS connector")
            return 1
        
        # Start Gmail agent if requested
        gmail_agent = None
        if args.gmail or args.all:
            gmail_agent = await start_gmail_agent(config, connector)
            if not gmail_agent:
                print("Warning: Failed to start Gmail agent")
        
        # Start Google Voice agent if requested
        voice_agent = None
        if args.voice or args.all:
            voice_agent = await start_google_voice_agent(config, connector)
            if not voice_agent:
                print("Warning: Failed to start Google Voice agent")
        
        print("\nEnhanced UI-TARS Integration started successfully")
        print("Press Ctrl+C to stop")
        
        # Wait for shutdown signal
        try:
            await shutdown_event.wait()
        except KeyboardInterrupt:
            print("\nShutting down...")
        
        # Shutdown components
        if voice_agent:
            await voice_agent.shutdown()
        
        if gmail_agent:
            await gmail_agent.shutdown()
        
        if connector:
            await connector.stop()
        
        print("Enhanced UI-TARS Integration stopped")
        return 0
    
    except Exception as e:
        logger.exception(f"Error in main: {e}")
        print(f"Error: {e}")
        return 1

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nShutting down...")
        sys.exit(0)
