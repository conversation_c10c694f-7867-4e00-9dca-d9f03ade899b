"""
Run the Unified Agent Dashboard.

This script runs the Unified Agent Dashboard for the Multi-Agent AI System.
"""
import os
import sys
import asyncio
import logging
import argparse
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent))

try:
    from unified_agent_dashboard.gui.main_dashboard import UnifiedAgentDashboard
    from core.logger import setup_logger
    from core.state_manager import StateManager
    from core.agent_manager import AgentManager
    from ui_tars.connector.enhanced_ui_tars_connector import EnhancedUITarsConnector
    from borg_cluster.jarvis_interface import JarvisInterface
    from alpha_evolve.alpha_evolve_engine import AlphaEvolveEngine
except ImportError as e:
    print(f"Error importing required modules: {e}")
    # Fallback imports for standalone usage
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler("unified_agent_dashboard.log")
        ]
    )

    def setup_logger(name):
        return logging.getLogger(name)

    StateManager = object
    AgentManager = object
    EnhancedUITarsConnector = object
    JarvisInterface = object
    AlphaEvolveEngine = object

# Set up logger
logger = setup_logger("run_unified_dashboard")

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Run the Unified Agent Dashboard")
    parser.add_argument("--config", type=str, help="Path to configuration file")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    return parser.parse_args()

async def initialize_components():
    """Initialize components."""
    try:
        logger.info("Initializing components")

        # Initialize state manager
        state_manager = StateManager()
        await state_manager.initialize()

        # Initialize agent manager
        agent_manager = AgentManager(state_manager=state_manager)
        await agent_manager.initialize()

        # Initialize UI-TARS connector
        ui_tars_connector = EnhancedUITarsConnector(
            state_manager=state_manager,
            agent_manager=agent_manager
        )
        await ui_tars_connector.initialize()

        # Initialize Jarvis interface
        jarvis_interface = JarvisInterface(
            state_manager=state_manager,
            agent_manager=agent_manager
        )
        await jarvis_interface.initialize()

        # Initialize AlphaEvolve engine
        alpha_evolve_engine = AlphaEvolveEngine(
            state_manager=state_manager,
            agent_manager=agent_manager
        )
        await alpha_evolve_engine.initialize()

        logger.info("Components initialized successfully")

        return {
            "state_manager": state_manager,
            "agent_manager": agent_manager,
            "ui_tars_connector": ui_tars_connector,
            "jarvis_interface": jarvis_interface,
            "alpha_evolve_engine": alpha_evolve_engine
        }

    except Exception as e:
        logger.exception(f"Error initializing components: {e}")
        return {}

def main():
    """Main function."""
    try:
        # Parse arguments
        args = parse_arguments()

        # Set up logging level
        if args.debug:
            logging.getLogger().setLevel(logging.DEBUG)

        logger.info("Starting Unified Agent Dashboard")

        # Initialize components
        components = asyncio.run(initialize_components())

        # Create dashboard
        dashboard = UnifiedAgentDashboard(
            state_manager=components.get("state_manager"),
            agent_manager=components.get("agent_manager"),
            ui_tars_connector=components.get("ui_tars_connector"),
            jarvis_interface=components.get("jarvis_interface"),
            alpha_evolve_engine=components.get("alpha_evolve_engine")
        )

        # Run dashboard
        dashboard.run()

        logger.info("Unified Agent Dashboard stopped")

    except Exception as e:
        logger.exception(f"Error running Unified Agent Dashboard: {e}")

if __name__ == "__main__":
    main()
