#!/usr/bin/env python3
"""
Send Email to Alyssa

This script sends an email to <PERSON><PERSON> using SMTP.
"""

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import json
import os
import argparse
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("send_email_to_alyssa.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("send_email_to_alyssa")

# Email configuration
SENDER_EMAIL = "<EMAIL>"
SENDER_PASSWORD = "GodisSoGood!777"
RECIPIENT_EMAIL = "<EMAIL>"  # Updated to Gmail address

# Email template for Alyssa
EMAIL_TEMPLATE = """
Dear <PERSON><PERSON>,

Thank you for your interest in our insurance products. Based on your $100/month budget, I'd like to discuss some options for an Indexed Universal Life (IUL) policy structured for maximum cash value growth, along with basic health, dental, and vision plans.

Here's what I'm thinking:

1. IUL Policy: We can structure this for optimal cash value growth while maintaining the life insurance benefit. This would be approximately $60-70 of your monthly budget.

2. Health Insurance: For the remaining $30-40, we can look at basic health plans that cover essential services.

3. Dental & Vision: We have some affordable options that can be added if your budget allows, or we can discuss slightly exceeding your budget if these are priorities for you.

Would you be available for a quick call to discuss these options in more detail? I can answer any questions you might have and provide specific policy recommendations based on your needs.

Please let me know what days and times work best for you.

Best regards,
Paul Edwards
Flo Faction Insurance
Phone: (*************
Email: <EMAIL>
"""

def load_templates():
    """Load email templates from the configuration file."""
    try:
        config_path = os.path.join("config", "communication_services.json")
        with open(config_path, "r", encoding="utf-8") as f:
            config = json.load(f)
            return config.get("email_integration", {}).get("email_templates", {})
    except Exception as e:
        logger.error(f"Error loading templates: {e}")
        return {}

def format_template(template, vars_dict):
    """Format a template with the given variables."""
    subject = template["subject"]
    body = template["body"].format(**vars_dict)
    return subject, body

def send_email(to_email, subject, body):
    """Send an email using SMTP."""
    logger.info(f"Sending email to {to_email}...")

    # Create a multipart message
    message = MIMEMultipart()
    message["From"] = SENDER_EMAIL
    message["To"] = to_email
    message["Subject"] = subject

    # Add body to email
    message.attach(MIMEText(body, "plain"))

    # Create secure connection with server and send email
    context = ssl.create_default_context()
    try:
        with smtplib.SMTP_SSL("smtp.gmail.com", 465, context=context) as server:
            server.login(SENDER_EMAIL, SENDER_PASSWORD)
            server.sendmail(SENDER_EMAIL, to_email, message.as_string())
        logger.info(f"Email sent successfully to {to_email}")
        return True
    except Exception as e:
        logger.error(f"Error sending email: {e}")
        return False

def send_direct_email():
    """Send a direct email to Alyssa using the predefined template."""
    logger.info("Sending direct email to Alyssa...")

    subject = "IUL Policy and Health Insurance Options"
    body = EMAIL_TEMPLATE

    print(f"Sending email to {RECIPIENT_EMAIL}...")
    success = send_email(RECIPIENT_EMAIL, subject, body)

    if success:
        print("✅ Email sent successfully to Alyssa!")
    else:
        print("❌ Failed to send email")

    return success

def main():
    """Main function to send emails to Alyssa."""
    parser = argparse.ArgumentParser(description="Send Email to Alyssa")
    parser.add_argument("--direct", action="store_true", help="Send direct email using predefined template")
    parser.add_argument("--template", action="store_true", help="Send email using templates from config")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")

    args = parser.parse_args()

    # Set log level
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)

    print("Send Email to Alyssa")
    print("===================")
    print()

    # Send direct email if requested
    if args.direct:
        return send_direct_email()

    # Otherwise, use templates from config
    # Load templates
    templates = load_templates()
    if not templates:
        logger.warning("No templates found. Sending direct email instead.")
        return send_direct_email()

    # Client information
    client_info = {
        "client_name": "Alyssa Chirinos",
        "first_name": "Alyssa",
        "agent_name": "Paul Edwards",
        "phone_number": "(*************",
        "dob": "8/16/97",
        "address": "Bradenton, Florida",
        "insurance_type": "IUL with Dental, Vision, and Basic Health",
        "estimated_premium": "$100/month",
        "notes": "Primary interest is IUL. Also interested in dental, vision, and basic private health coverage for checkups, physicals, and bloodwork. TOTAL BUDGET IS $100/MONTH. Need to check all carriers for best solution within budget."
    }

    success = True

    # Send initial email
    if "new_client" in templates:
        subject, body = format_template(templates["new_client"], client_info)
        print(f"Sending initial email to {RECIPIENT_EMAIL}...")
        if not send_email(RECIPIENT_EMAIL, subject, body):
            success = False

    # Send quote email
    if "new_client_quote" in templates:
        subject, body = format_template(templates["new_client_quote"], client_info)
        print(f"Sending quote email to {RECIPIENT_EMAIL}...")
        if not send_email(RECIPIENT_EMAIL, subject, body):
            success = False

    if success:
        print("✅ All emails sent successfully to Alyssa!")
    else:
        print("❌ Some emails failed to send")

    return success

if __name__ == "__main__":
    main()
