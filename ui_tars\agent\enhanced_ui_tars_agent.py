"""
Enhanced UI-TARS Agent for the Multi-Agent AI System.

This module provides an improved agent that leverages UI-TARS 1.5 to control
browsers and desktop applications autonomously with enhanced reliability.
"""
import os
import sys
import json
import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import uuid
import time

try:
    from agents.base_agent import BaseAgent
    from core.logger import setup_logger
    from ui_tars.connector.enhanced_ui_tars_connector import EnhancedUITarsConnector
    from llm.llm_router import LLMRouter
except ImportError:
    # Fallback imports for standalone usage
    BaseAgent = object

    # Fallback logging setup
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler("ui_tars_agent.log")
        ]
    )

    def setup_logger(name):
        return logging.getLogger(name)

    EnhancedUITarsConnector = None
    LLMRouter = None

# Set up logger
logger = setup_logger("enhanced_ui_tars_agent")

class EnhancedUITarsAgent(BaseAgent):
    """
    Enhanced Agent for UI-TARS 1.5.

    This agent leverages UI-TARS 1.5 to control browsers and desktop
    applications autonomously with improved reliability and error handling.
    """

    def __init__(self,
                 agent_id: str = "ui_tars_agent",
                 config: Optional[Dict] = None,
                 state_manager = None,
                 message_queue = None,
                 shutdown_event = None,
                 api_url: Optional[str] = "http://localhost:8080",
                 api_key: Optional[str] = None,
                 model_name: Optional[str] = "UI-TARS-1.5-7B",
                 installation_path: Optional[str] = None,
                 browser_type: str = "chrome",
                 browser_path: Optional[str] = None,
                 remote_debugging_port: int = 9222,
                 auto_start: bool = True,
                 auto_restart: bool = True,
                 llm_router = None):
        """
        Initialize the Enhanced UI-TARS agent.

        Args:
            agent_id (str): Agent identifier
            config (Optional[Dict]): Agent configuration
            state_manager: State manager instance
            message_queue: Message queue for agent communication
            shutdown_event: Event to signal agent shutdown
            api_url (Optional[str]): URL of the UI-TARS API
            api_key (Optional[str]): API key for UI-TARS
            model_name (Optional[str]): Name of the model to use
            installation_path (Optional[str]): Path to UI-TARS installation
            browser_type (str): Type of browser to use
            browser_path (Optional[str]): Path to browser executable
            remote_debugging_port (int): Port for browser remote debugging
            auto_start (bool): Whether to automatically start UI-TARS
            auto_restart (bool): Whether to automatically restart UI-TARS on failure
            llm_router: LLM router instance
        """
        # Initialize base agent if available
        if BaseAgent is not object:
            super().__init__(
                agent_id=agent_id,
                config=config or {},
                state_manager=state_manager,
                message_queue=message_queue,
                shutdown_event=shutdown_event
            )

        # Set up logger
        self.logger = logger

        # Extract configuration
        self.config = config or {}
        ui_tars_config = self.config.get("ui_tars", {})

        # UI-TARS settings
        self.api_url = api_url or ui_tars_config.get("api_url", "http://localhost:8080")
        self.api_key = api_key or ui_tars_config.get("api_key")
        self.model_name = model_name or ui_tars_config.get("model_name", "UI-TARS-1.5-7B")
        self.installation_path = installation_path or ui_tars_config.get("installation_path")

        # Browser settings
        self.browser_type = browser_type or ui_tars_config.get("browser_type", "chrome")
        self.browser_path = browser_path or ui_tars_config.get("browser_path")
        self.remote_debugging_port = remote_debugging_port or ui_tars_config.get("remote_debugging_port", 9222)

        # Startup settings
        self.auto_start = auto_start if auto_start is not None else ui_tars_config.get("auto_start", True)
        self.auto_restart = auto_restart if auto_restart is not None else ui_tars_config.get("auto_restart", True)

        # LLM router
        self.llm_router = llm_router

        # UI-TARS connector
        self.connector = None

        # Agent state
        self.initialized = False
        self.current_task = None
        self.current_task_id = None
        self.task_queue = asyncio.Queue()
        self.task_results = {}
        self.last_screenshot = None
        self.last_error = None
        self.health_check_interval = 60  # seconds
        self.health_check_task = None

        self.logger.info("Enhanced UI-TARS agent initialized")

    async def initialize(self):
        """Initialize the Enhanced UI-TARS agent."""
        self.logger.info("Initializing Enhanced UI-TARS agent")

        # Initialize base agent if available
        if BaseAgent is not object:
            await super().initialize()

        # Create Enhanced UI-TARS connector
        self.connector = EnhancedUITarsConnector(
            api_url=self.api_url,
            api_key=self.api_key,
            model_name=self.model_name,
            installation_path=self.installation_path,
            browser_type=self.browser_type,
            browser_path=self.browser_path,
            remote_debugging_port=self.remote_debugging_port,
            auto_start=self.auto_start,
            auto_restart=self.auto_restart
        )

        # Initialize connector
        success = await self.connector.initialize()
        if not success:
            self.logger.error("Failed to initialize UI-TARS connector")
            return False

        # Start health check task
        self.health_check_task = asyncio.create_task(self._health_check_loop())

        self.initialized = True
        self.logger.info("Enhanced UI-TARS agent initialized successfully")
        return True

    async def shutdown(self):
        """Shutdown the Enhanced UI-TARS agent."""
        self.logger.info("Shutting down Enhanced UI-TARS agent")

        # Cancel health check task
        if self.health_check_task:
            self.health_check_task.cancel()
            try:
                await self.health_check_task
            except asyncio.CancelledError:
                pass

        # Stop UI-TARS
        if self.connector:
            await self.connector.stop()

        # Shutdown base agent if available
        if BaseAgent is not object:
            await super().shutdown()

        self.logger.info("Enhanced UI-TARS agent shut down")

    async def run(self):
        """Run the Enhanced UI-TARS agent."""
        self.logger.info("Running Enhanced UI-TARS agent")

        if not self.initialized:
            success = await self.initialize()
            if not success:
                self.logger.error("Failed to initialize Enhanced UI-TARS agent")
                return

        try:
            while True:
                # Check if shutdown is requested
                if hasattr(self, 'shutdown_event') and self.shutdown_event and self.shutdown_event.is_set():
                    break

                # Process tasks from queue
                try:
                    task = await asyncio.wait_for(self.task_queue.get(), timeout=1.0)
                    await self._process_task(task)
                    self.task_queue.task_done()
                except asyncio.TimeoutError:
                    # No task available, continue
                    pass
                except Exception as e:
                    self.logger.exception(f"Error processing task: {e}")

                # Small delay to prevent CPU hogging
                await asyncio.sleep(0.1)

        except asyncio.CancelledError:
            self.logger.info("Enhanced UI-TARS agent run task cancelled")
        except Exception as e:
            self.logger.exception(f"Error in Enhanced UI-TARS agent run loop: {e}")

        self.logger.info("Enhanced UI-TARS agent run loop ended")

    async def _health_check_loop(self):
        """Periodically check UI-TARS health and restart if needed."""
        self.logger.info("Starting UI-TARS health check loop")

        try:
            while True:
                try:
                    # Check if UI-TARS is connected
                    if not self.connector.is_connected:
                        self.logger.warning("UI-TARS is not connected, attempting to reconnect")
                        connection_success = await self.connector._ensure_connection()

                        if not connection_success and self.auto_restart:
                            self.logger.warning("Failed to reconnect to UI-TARS, attempting restart")
                            await self.connector.restart()

                    # Wait for next check
                    await asyncio.sleep(self.health_check_interval)

                except Exception as e:
                    self.logger.error(f"Error in health check: {e}")
                    await asyncio.sleep(10)  # Shorter interval after error

        except asyncio.CancelledError:
            self.logger.info("Health check loop cancelled")

        self.logger.info("Health check loop ended")

    async def _process_task(self, task: Dict):
        """
        Process a task.

        Args:
            task (Dict): Task to process
        """
        task_id = task.get("id", str(uuid.uuid4()))
        task_type = task.get("type", "unknown")
        task_data = task.get("data", {})

        self.logger.info(f"Processing task {task_id} of type {task_type}")

        self.current_task = task
        self.current_task_id = task_id

        try:
            # Process task based on type
            if task_type == "browse_website":
                result = await self.browse_website(
                    url=task_data.get("url"),
                    description=task_data.get("description")
                )
            elif task_type == "execute_command":
                result = await self.execute_command(
                    command=task_data.get("command")
                )
            elif task_type == "take_screenshot":
                result = await self.take_screenshot()
            elif task_type == "search_web":
                result = await self.search_web(
                    query=task_data.get("query")
                )
            else:
                result = {
                    "success": False,
                    "error": f"Unknown task type: {task_type}"
                }

            # Store task result
            self.task_results[task_id] = result

            # Report task completion
            self.logger.info(f"Task {task_id} completed with result: {result.get('success', False)}")

            return result

        except Exception as e:
            error_msg = f"Error processing task {task_id}: {str(e)}"
            self.logger.exception(error_msg)

            # Store error result
            result = {
                "success": False,
                "error": error_msg
            }
            self.task_results[task_id] = result

            return result

        finally:
            self.current_task = None
            self.current_task_id = None

    async def add_task(self, task: Dict) -> str:
        """
        Add a task to the queue.

        Args:
            task (Dict): Task to add

        Returns:
            str: Task ID
        """
        task_id = task.get("id", str(uuid.uuid4()))
        if "id" not in task:
            task["id"] = task_id

        await self.task_queue.put(task)
        self.logger.info(f"Added task {task_id} to queue")

        return task_id

    async def get_task_result(self, task_id: str, wait: bool = True, timeout: Optional[float] = None) -> Optional[Dict]:
        """
        Get the result of a task.

        Args:
            task_id (str): Task ID
            wait (bool): Whether to wait for the task to complete
            timeout (Optional[float]): Timeout in seconds

        Returns:
            Optional[Dict]: Task result or None if not available
        """
        if task_id in self.task_results:
            return self.task_results[task_id]

        if not wait:
            return None

        # Wait for task to complete
        start_time = time.time()
        while timeout is None or time.time() - start_time < timeout:
            if task_id in self.task_results:
                return self.task_results[task_id]
            await asyncio.sleep(0.1)

        return None

    async def browse_website(self, url: str, description: Optional[str] = None) -> Dict:
        """
        Browse a website using UI-TARS.

        Args:
            url (str): URL to browse
            description (Optional[str]): Description of the browsing task

        Returns:
            Dict: Result of the operation
        """
        if not self.connector:
            return {"success": False, "error": "UI-TARS connector not initialized"}

        self.logger.info(f"Browsing website: {url}")

        try:
            # Execute command to browse website
            command = f"Browse the website {url}"
            if description:
                command += f" and {description}"

            result = await self.connector.execute_command(command)

            # Take a screenshot
            screenshot_result = await self.take_screenshot()

            return {
                "success": True,
                "url": url,
                "description": description,
                "result": result,
                "screenshot": screenshot_result.get("screenshot_path")
            }

        except Exception as e:
            error_msg = f"Error browsing website {url}: {str(e)}"
            self.logger.exception(error_msg)
            return {"success": False, "error": error_msg}

    async def execute_command(self, command: str) -> Dict:
        """
        Execute a command in UI-TARS.

        Args:
            command (str): Command to execute

        Returns:
            Dict: Result of the operation
        """
        if not self.connector:
            return {"success": False, "error": "UI-TARS connector not initialized"}

        self.logger.info(f"Executing command: {command}")

        try:
            # Take a screenshot before executing command
            screenshot_result = await self.take_screenshot()
            screenshot_path = screenshot_result.get("screenshot_path")

            # Execute command with screenshot
            result = await self.connector.execute_command(command, screenshot_path)

            return {
                "success": True,
                "command": command,
                "result": result,
                "screenshot_before": screenshot_path
            }

        except Exception as e:
            error_msg = f"Error executing command: {str(e)}"
            self.logger.exception(error_msg)
            return {"success": False, "error": error_msg}

    async def take_screenshot(self) -> Dict:
        """
        Take a screenshot using UI-TARS.

        Returns:
            Dict: Result of the operation
        """
        if not self.connector:
            return {"success": False, "error": "UI-TARS connector not initialized"}

        self.logger.info("Taking screenshot")

        try:
            # Execute command to take screenshot
            command = "Take a screenshot"
            result = await self.connector.execute_command(command)

            # Extract screenshot path from result
            screenshot_path = result.get("screenshot_path", "screenshot.png")
            self.last_screenshot = screenshot_path

            return {
                "success": True,
                "screenshot_path": screenshot_path,
                "result": result
            }

        except Exception as e:
            error_msg = f"Error taking screenshot: {str(e)}"
            self.logger.exception(error_msg)
            return {"success": False, "error": error_msg}

    async def search_web(self, query: str) -> Dict:
        """
        Search the web using UI-TARS.

        Args:
            query (str): Search query

        Returns:
            Dict: Result of the operation
        """
        if not self.connector:
            return {"success": False, "error": "UI-TARS connector not initialized"}

        self.logger.info(f"Searching web for: {query}")

        try:
            # Execute command to search web
            command = f"Search the web for {query}"
            result = await self.connector.execute_command(command)

            # Take a screenshot
            screenshot_result = await self.take_screenshot()

            return {
                "success": True,
                "query": query,
                "result": result,
                "screenshot": screenshot_result.get("screenshot_path")
            }

        except Exception as e:
            error_msg = f"Error searching web: {str(e)}"
            self.logger.exception(error_msg)
            return {"success": False, "error": error_msg}

    async def analyze_screen(self) -> Dict:
        """
        Analyze the current screen using UI-TARS.

        Returns:
            Dict: Analysis result
        """
        if not self.connector:
            return {"success": False, "error": "UI-TARS connector not initialized"}

        self.logger.info("Analyzing screen")

        try:
            # Take a screenshot
            screenshot_result = await self.take_screenshot()
            screenshot_path = screenshot_result.get("screenshot_path")

            if not screenshot_path:
                return {"success": False, "error": "Failed to take screenshot"}

            # Execute command to analyze screen
            command = "Analyze this screenshot and tell me what's happening on the screen. What elements are visible? Is there a login form? Is there an error message? What should I do next?"
            result = await self.connector.execute_command(command, screenshot_path)

            return {
                "success": True,
                "analysis": result,
                "screenshot": screenshot_path
            }

        except Exception as e:
            error_msg = f"Error analyzing screen: {str(e)}"
            self.logger.exception(error_msg)
            return {"success": False, "error": error_msg}

    async def get_next_action(self, current_task: str) -> Dict:
        """
        Get the next action to take based on the current screen.

        Args:
            current_task (str): Description of the current task

        Returns:
            Dict: Next action
        """
        if not self.connector:
            return {"success": False, "error": "UI-TARS connector not initialized"}

        self.logger.info(f"Getting next action for task: {current_task}")

        try:
            # Take a screenshot
            screenshot_result = await self.take_screenshot()
            screenshot_path = screenshot_result.get("screenshot_path")

            if not screenshot_path:
                return {"success": False, "error": "Failed to take screenshot"}

            # Use LLM to decide the next action if available
            if self.llm_router:
                # Prepare the prompt
                prompt = f"""
                You are controlling a browser autonomously to complete the task: {current_task}

                Based on the current screenshot, what action should be taken next?

                Respond with a specific command to execute in UI-TARS.
                """

                # Get the next action from the LLM
                llm_response = await self.llm_router.generate_text(
                    prompt=prompt,
                    system_prompt="You are an AI assistant helping to control a browser autonomously.",
                    model="gpt-4o",
                    max_tokens=100
                )

                # Extract the command from the LLM response
                command = llm_response.strip()

                # Execute the command
                result = await self.connector.execute_command(command, screenshot_path)

                return {
                    "success": True,
                    "next_action": command,
                    "result": result,
                    "screenshot": screenshot_path
                }

            # If LLM router is not available, ask UI-TARS directly
            command = f"I'm trying to {current_task}. Based on this screenshot, what should I do next? Give me a specific action to take."
            result = await self.connector.execute_command(command, screenshot_path)

            return {
                "success": True,
                "next_action": result,
                "screenshot": screenshot_path
            }

        except Exception as e:
            error_msg = f"Error getting next action: {str(e)}"
            self.logger.exception(error_msg)
            return {"success": False, "error": error_msg}
