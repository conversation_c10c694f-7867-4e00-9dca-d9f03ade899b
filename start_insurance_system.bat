@echo off
REM Start Insurance System
REM This script starts all components of the insurance system

echo.
echo ===================================
echo    Start Insurance System
echo ===================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

echo Select components to start:
echo 1. MPC Server Monitoring
echo 2. Alyssa's Drip Campaign
echo 3. Facebook Lead Integration
echo 4. Google Sheets Lead Tracker
echo 5. All Components
echo.

set /p CHOICE="Enter your choice (1-5): "

if "%CHOICE%"=="1" (
    call start_mpc_monitoring.bat
) else if "%CHOICE%"=="2" (
    call start_alyssa_campaign.bat
) else if "%CHOICE%"=="3" (
    call start_facebook_lead_integration.bat
) else if "%CHOICE%"=="4" (
    call start_google_sheets_lead_tracker.bat
) else if "%CHOICE%"=="5" (
    echo.
    echo Starting all components...
    echo.
    
    REM Start MPC Server Monitoring
    echo Starting MPC Server Monitoring...
    start "MPC Server Monitoring" cmd /c start_mpc_monitoring.bat
    
    REM Wait a bit for MPC servers to start
    timeout /t 5 /nobreak > nul
    
    REM Start Facebook Lead Integration
    echo Starting Facebook Lead Integration...
    start "Facebook Lead Integration" cmd /c start_facebook_lead_integration.bat
    
    REM Start Google Sheets Lead Tracker
    echo Starting Google Sheets Lead Tracker...
    start "Google Sheets Lead Tracker" cmd /c start_google_sheets_lead_tracker.bat
    
    REM Start Alyssa's Drip Campaign
    echo Starting Alyssa's Drip Campaign...
    call start_alyssa_campaign.bat
) else (
    echo Invalid choice.
    goto end
)

:end
echo.
pause
