{"ui_tars": {"api_url": "http://localhost:8080", "api_key": "hf_dummy_key", "model_name": "UI-TARS-1.5-7B", "installation_path": null, "browser_type": "chrome", "remote_debugging_port": 9222, "auto_start": true, "auto_restart": true}, "midscene": {"api_url": "http://localhost:8081", "api_key": null, "model_name": "UI-TARS-1.5-7B", "installation_path": null, "browser_type": "chrome", "android_enabled": false, "auto_start_browser": true, "auto_start_android": false, "default_url": "https://www.google.com", "default_device_id": null}, "local_llm": {"model_path": null, "model_type": "ui-tars", "host": "localhost", "port": 8080, "api_base": "http://localhost:8080/v1", "quantization": "4bit", "auto_start": true}, "agent": {"auto_start": true, "voice_commands_enabled": true, "nvidia_acceleration": true, "autonomous_mode": true}, "gmail": {"url": "https://mail.google.com", "default_email": "<EMAIL>", "default_password": "GodisSoGood!777"}, "google_voice": {"url": "https://voice.google.com", "default_phone_number": "7722089646", "default_email": "<EMAIL>", "default_password": "GodisSoGood!777"}, "personas": {"paul": {"name": "<PERSON>", "email": "<EMAIL>", "phone": "7722089646", "voice_type": "male", "elevenlabs_voice_id": "paul_voice_id"}, "sandra": {"name": "<PERSON>", "email": "<EMAIL>", "phone": "7722089646", "voice_type": "female", "elevenlabs_voice_id": "sandra_voice_id"}}, "browser": {"type": "chrome", "remote_debugging_port": 9222}, "api": {"port": 8080, "host": "localhost"}, "logging": {"level": "INFO", "file": "ui_tars.log"}}