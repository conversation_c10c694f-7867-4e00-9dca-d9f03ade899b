@echo off
echo Selenium Email to Alyssa
echo =======================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Check if Selenium is installed
python -c "import selenium" >nul 2>&1
if %errorlevel% neq 0 (
    echo Selenium is not installed. Installing...
    pip install selenium webdriver-manager
    if %errorlevel% neq 0 (
        echo Failed to install Selenium. Please install it manually with: pip install selenium webdriver-manager
        exit /b 1
    )
    echo Selenium installed successfully.
)

REM Run the script
echo Running Selenium Email to Alyssa script...
echo.

python selenium_email_to_alyssa.py

echo.
if %errorlevel% equ 0 (
    echo Script completed successfully!
) else (
    echo There was an error running the script.
)

echo.
pause
