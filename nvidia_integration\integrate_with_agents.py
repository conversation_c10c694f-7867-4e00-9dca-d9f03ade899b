"""
Integration script for NVIDIA tools with the AI agent system.

This module provides the functions needed to initialize NVIDIA acceleration
and capabilities for all agents in the system during startup.
"""
import asyncio
import os
import json
import importlib
from typing import Dict, List, Any, Optional

from core.logger import setup_logger
from nvidia_integration.agent_accelerator import AgentAccelerator

logger = setup_logger("nvidia_integration")

# Global accelerator instance
_nvidia_accelerator = None

async def initialize_nvidia_tools(config_path: Optional[str] = None) -> bool:
    """
    Initialize NVIDIA tools for the entire agent system.
    
    Args:
        config_path: Path to NVIDIA configuration file (optional)
        
    Returns:
        bool: True if initialization was successful, False otherwise
    """
    global _nvidia_accelerator
    
    # Load configuration
    config = {}
    if config_path and os.path.exists(config_path):
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
            logger.info("Loaded NVIDIA configuration from %s", config_path)
        except Exception as e:
            logger.warning("Error loading NVIDIA configuration: %s", e)
    else:
        default_config_path = os.path.join('config', 'nvidia_config.json')
        if os.path.exists(default_config_path):
            try:
                with open(default_config_path, 'r') as f:
                    config = json.load(f)
                logger.info("Loaded default NVIDIA configuration from %s", default_config_path)
            except Exception as e:
                logger.warning("Error loading default NVIDIA configuration: %s", e)
        else:
            logger.info("No NVIDIA configuration found, using built-in defaults")
    
    try:
        # Create the global accelerator
        _nvidia_accelerator = AgentAccelerator(config)
        
        # Initialize the accelerator
        await _nvidia_accelerator.initialize()
        
        if _nvidia_accelerator.initialized:
            logger.info("NVIDIA tools initialized successfully")
            
            # Log available features
            status = _nvidia_accelerator.get_status()
            logger.info("Available NVIDIA features: %s", 
                        ", ".join([k for k, v in status.items() 
                                  if isinstance(v, bool) and v and k != "initialized"]))
            
            # Log GPU information if available
            gpu_info = _nvidia_accelerator.get_gpu_info()
            if gpu_info.get("available", False):
                logger.info("GPU detected: %s", gpu_info.get("torch_cuda_device_name", "Unknown"))
                if "gpu_list" in gpu_info:
                    for i, gpu in enumerate(gpu_info["gpu_list"]):
                        logger.info("GPU %d: %s, Memory: %s", i, gpu.get("name", "Unknown"), gpu.get("memory_total", "Unknown"))
            else:
                logger.warning("No NVIDIA GPUs detected")
            
            return True
        else:
            logger.warning("NVIDIA tools initialization failed")
            return False
        
    except Exception as e:
        logger.exception("Error initializing NVIDIA tools: %s", e)
        return False

def get_nvidia_accelerator() -> Optional[AgentAccelerator]:
    """
    Get the global NVIDIA accelerator instance.
    
    Returns:
        AgentAccelerator or None: The global accelerator instance if initialized
    """
    return _nvidia_accelerator

async def accelerate_agent(agent: Any) -> Any:
    """
    Accelerate an agent's models with NVIDIA technologies.
    
    Args:
        agent: The agent to accelerate
        
    Returns:
        The accelerated agent, or original agent if acceleration fails
    """
    global _nvidia_accelerator
    
    if _nvidia_accelerator is None or not _nvidia_accelerator.initialized:
        logger.warning("NVIDIA accelerator not initialized, returning original agent")
        return agent
    
    agent_name = agent.__class__.__name__
    
    try:
        logger.info("Accelerating agent: %s", agent_name)
        
        # Check if the agent has models to accelerate
        has_models = (
            hasattr(agent, 'model') or 
            hasattr(agent, 'models') or 
            any(attr.endswith('_model') for attr in dir(agent))
        )
        
        if has_models:
            # Accelerate the agent's models
            agent = _nvidia_accelerator.accelerate_agent_model(agent)
            logger.info("Successfully accelerated agent: %s", agent_name)
        else:
            logger.info("Agent %s has no recognized models to accelerate", agent_name)
        
        return agent
    except Exception as e:
        logger.exception("Error accelerating agent %s: %s", agent_name, e)
        return agent

async def accelerate_multi_agent_system(agents: List[Any]) -> List[Any]:
    """
    Accelerate multiple agents in a multi-agent system.
    
    Args:
        agents: List of agents to accelerate
        
    Returns:
        List of accelerated agents
    """
    accelerated_agents = []
    
    for agent in agents:
        accelerated_agent = await accelerate_agent(agent)
        accelerated_agents.append(accelerated_agent)
    
    return accelerated_agents

class NVIDIAEnhancedAgentMixin:
    """
    Mixin class to add NVIDIA capabilities to any agent.
    
    Usage:
        class MyEnhancedAgent(NVIDIAEnhancedAgentMixin, BaseAgent):
            pass
    """
    
    async def _initialize_nvidia_capabilities(self):
        """Initialize NVIDIA capabilities for this agent."""
        global _nvidia_accelerator
        
        if _nvidia_accelerator is None or not _nvidia_accelerator.initialized:
            logger.warning("NVIDIA accelerator not initialized, skipping capability initialization")
            return
        
        # Store reference to accelerator
        self._nvidia_accelerator = _nvidia_accelerator
        
        # Add NVIDIA-specific methods to the agent
        self.has_nvidia_capabilities = True
        
        try:
            # If the agent has models, accelerate them
            if hasattr(self, 'model') or hasattr(self, 'models'):
                self = _nvidia_accelerator.accelerate_agent_model(self)
        except Exception as e:
            logger.warning("Error accelerating agent models: %s", e)
    
    async def speech_to_text(self, audio_data, **kwargs):
        """Convert speech to text using NVIDIA Riva."""
        if hasattr(self, '_nvidia_accelerator') and self._nvidia_accelerator:
            return await self._nvidia_accelerator.speech_to_text(audio_data, **kwargs)
        return {"success": False, "error": "NVIDIA capabilities not initialized"}
    
    async def text_to_speech(self, text, **kwargs):
        """Convert text to speech using NVIDIA Riva."""
        if hasattr(self, '_nvidia_accelerator') and self._nvidia_accelerator:
            return await self._nvidia_accelerator.text_to_speech(text, **kwargs)
        return {"success": False, "error": "NVIDIA capabilities not initialized"}
    
    async def detect_objects(self, image_data, **kwargs):
        """Detect objects in an image using NVIDIA Metropolis."""
        if hasattr(self, '_nvidia_accelerator') and self._nvidia_accelerator:
            return await self._nvidia_accelerator.detect_objects(image_data, **kwargs)
        return {"success": False, "error": "NVIDIA capabilities not initialized"}
    
    async def analyze_medical_data(self, data, data_type, **kwargs):
        """Analyze medical data using NVIDIA Clara."""
        if hasattr(self, '_nvidia_accelerator') and self._nvidia_accelerator:
            return await self._nvidia_accelerator.analyze_medical_data(data, data_type, **kwargs)
        return {"success": False, "error": "NVIDIA capabilities not initialized"}

async def shutdown_nvidia_tools():
    """Shutdown NVIDIA tools and release resources."""
    global _nvidia_accelerator
    
    if _nvidia_accelerator is not None and _nvidia_accelerator.initialized:
        logger.info("Shutting down NVIDIA tools")
        
        try:
            await _nvidia_accelerator.shutdown()
            logger.info("NVIDIA tools shutdown complete")
        except Exception as e:
            logger.exception("Error shutting down NVIDIA tools: %s", e)
    
    _nvidia_accelerator = None

# Example usage in main.py:
"""
from nvidia_integration.integrate_with_agents import (
    initialize_nvidia_tools,
    accelerate_agent,
    accelerate_multi_agent_system,
    shutdown_nvidia_tools
)

async def main():
    # Initialize NVIDIA tools
    nvidia_initialized = await initialize_nvidia_tools()
    
    # Create agents
    agent1 = Agent1()
    agent2 = Agent2()
    
    # Accelerate agents if NVIDIA tools initialized
    if nvidia_initialized:
        agent1 = await accelerate_agent(agent1)
        agent2 = await accelerate_agent(agent2)
    
    # Use agents...
    
    # Shutdown NVIDIA tools when done
    await shutdown_nvidia_tools()
"""