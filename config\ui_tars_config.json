{
  "ui_tars": {
    "version": "1.5",
    "enabled": true,
    "browser": {
      "type": "chrome",
      "executable_path": "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",
      "user_data_dir": "C:\\Users\\<USER>\\AppData\\Local\\UI-TARS\\browser_data",
      "profile_directory": "Default",
      "remote_debugging_port": 9222,
      "detection": {
        "auto_detect": true,
        "fallback_types": ["chrome", "edge", "firefox", "brave"]
      }
    },
    "api": {
      "host": "localhost",
      "port": 8080,
      "timeout": 30,
    },
    "debug": {
      "enabled": true,
      "log_level": "debug",
      "log_file": "ui_tars_debug.log"
    },
    "sandbox": {
      "enabled": true,
      "isolation_level": "high"
    },
    "virtual_pc": {
      "enabled": true,
      "memory_mb": 2048,
    },
    "dpo": {
      "enabled": true,
      "preference_model": "default"
    }
  }
}
