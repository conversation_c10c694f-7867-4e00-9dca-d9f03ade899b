# PowerShell script to send an email to <PERSON><PERSON> C. using COM automation
# This script demonstrates browser automation using PowerShell COM objects

# Email content
$recipientEmail = "<EMAIL>" # Replace with actual email
$emailSubject = "URGENT: Your Insurance Options - Coverage Available Within Your $100 Monthly Budget"
$emailBody = @"
Hi Alyssa,

I hope this message finds you well. We've been trying to reach you through multiple channels (email, phone calls, voicemails, and texts) regarding your insurance needs, and I wanted to follow up personally as this is time-sensitive.

Based on your specific situation and $100 monthly budget, we have options ready for you that provide excellent coverage:

For your IUL policy (approximately $65/month):
- Cash value growth potential tied to market performance without the downside risk
- Death benefit protection for your loved ones
- Tax-free access to your cash value for future needs
- Living benefits that allow access to your death benefit if you become critically ill

For your health/dental/vision package (approximately $35/month):
- Comprehensive health coverage with our top-tier carriers that offer exceptional benefits
- Dental coverage including preventive care, basic procedures, and major work
- Vision benefits covering exams, frames, and contacts

What makes us the best agency to handle your insurance needs:
1. Our carriers offer some of the most comprehensive health benefits in the industry, with lower deductibles and better coverage than you'll find elsewhere
2. We have flexible IUL, whole life, and term policy options that can be customized to your exact needs
3. Our mortgage protection extends for the entire life of your loan, unlike competitors who offer limited coverage periods
4. For qualified applicants like yourself, we can secure over $1 million in coverage

We need to speak with you as soon as possible to secure this coverage before rates change. I have the following time slots available tomorrow (Monday):
- 10:00 AM - 10:30 AM
- 1:00 PM - 1:30 PM
- 4:00 PM - 4:30 PM

Or Tuesday:
- 9:00 AM - 9:30 AM
- 2:00 PM - 2:30 PM

Please let me know which time works best for you, or you can schedule directly through our Calendly link:
https://calendly.com/flofaction/insurance-consultation

It's critical that we connect in the next 24-48 hours to ensure we can lock in these rates for you.

Looking forward to speaking with you soon,

Paul Edwards
Flo Faction Insurance
(772) 208-9646
"@

# Function to log messages with timestamps
function Write-Log {
    param (
        [Parameter(Mandatory=$true)]
        [string]$Message,
        
        [Parameter(Mandatory=$false)]
        [ValidateSet("INFO", "WARNING", "ERROR")]
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "$timestamp - $Level - $Message"
    
    # Output to console with color based on level
    switch ($Level) {
        "INFO" { Write-Host $logMessage -ForegroundColor Cyan }
        "WARNING" { Write-Host $logMessage -ForegroundColor Yellow }
        "ERROR" { Write-Host $logMessage -ForegroundColor Red }
    }
}

# Function to wait for a specified number of seconds
function Wait-Seconds {
    param (
        [int]$Seconds
    )
    Start-Sleep -Seconds $Seconds
}

# Function to send an email using COM automation
function Send-EmailWithAutomation {
    try {
        Write-Log "Starting email automation to Alyssa C..."
        
        # Create a COM object for Internet Explorer
        $ie = New-Object -ComObject InternetExplorer.Application
        $ie.Visible = $true
        
        Write-Log "Browser launched successfully"
        
        # Navigate to Gmail compose
        $ie.Navigate("https://mail.google.com/mail/u/0/#inbox?compose=new")
        
        # Wait for page to load
        while ($ie.Busy -or $ie.ReadyState -ne 4) {
            Wait-Seconds 1
        }
        
        Write-Log "Navigated to Gmail compose page"
        
        # Check if we need to log in
        if ($ie.LocationURL -like "*accounts.google.com*") {
            Write-Log "Login page detected, please log in manually" -Level "WARNING"
            
            # Wait for manual login
            Write-Host "Please log in to Gmail manually and press Enter when done..." -ForegroundColor Yellow
            Read-Host
            
            # Navigate to compose again after login
            $ie.Navigate("https://mail.google.com/mail/u/0/#inbox?compose=new")
            
            # Wait for page to load
            while ($ie.Busy -or $ie.ReadyState -ne 4) {
                Wait-Seconds 1
            }
        }
        
        # Wait for the compose form to fully load
        Wait-Seconds 5
        
        # Get the document
        $doc = $ie.Document
        
        # Fill in recipient
        $recipientField = $doc.querySelector("input[role='combobox'][aria-label*='To']")
        if ($recipientField) {
            $recipientField.value = $recipientEmail
            $recipientField.focus()
            # Simulate Tab key to move to next field
            $wshell = New-Object -ComObject WScript.Shell
            $wshell.SendKeys("{TAB}")
            Write-Log "Entered recipient: $recipientEmail"
        } else {
            Write-Log "Recipient field not found" -Level "ERROR"
            return $false
        }
        
        # Fill in subject
        $subjectField = $doc.querySelector("input[name='subjectbox']")
        if ($subjectField) {
            $subjectField.value = $emailSubject
            Write-Log "Entered subject: $emailSubject"
        } else {
            Write-Log "Subject field not found" -Level "ERROR"
            return $false
        }
        
        # Fill in email body
        $bodyField = $doc.querySelector("div[role='textbox'][aria-label*='Message Body']")
        if ($bodyField) {
            $bodyField.innerHTML = $emailBody.Replace("`n", "<br>")
            Write-Log "Entered email body"
        } else {
            Write-Log "Email body field not found" -Level "ERROR"
            return $false
        }
        
        # Click send button
        $sendButton = $doc.querySelector("div[role='button'][aria-label*='Send']")
        if ($sendButton) {
            $sendButton.click()
            Write-Log "Clicked send button"
        } else {
            Write-Log "Send button not found" -Level "ERROR"
            return $false
        }
        
        # Wait for confirmation
        Wait-Seconds 3
        
        Write-Log "Email sent successfully to $recipientEmail!"
        
        # Close the browser
        $ie.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($ie) | Out-Null
        
        return $true
    } catch {
        Write-Log "Error sending email: $_" -Level "ERROR"
        
        # Try to close the browser if it exists
        if ($ie) {
            try {
                $ie.Quit()
                [System.Runtime.Interopservices.Marshal]::ReleaseComObject($ie) | Out-Null
            } catch {
                # Ignore errors when closing
            }
        }
        
        return $false
    }
}

# Main execution
$success = Send-EmailWithAutomation

if ($success) {
    Write-Log "Email automation completed successfully!"
    exit 0
} else {
    Write-Log "Email automation failed!" -Level "ERROR"
    exit 1
}
