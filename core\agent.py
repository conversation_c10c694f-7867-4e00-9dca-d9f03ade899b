"""
Base Agent class for the Multi-Agent AI System.
This is a simplified Agent class for agents that don't need the full BaseAgent functionality.
"""
import os
import json
import asyncio
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import logging
from abc import ABC, abstractmethod

from core.logger import setup_logger
from llm.llm_connector import LLMConnector

class Agent(ABC):
    """
    Base class for simplified agents in the system.
    
    This class provides a simplified interface for agents that don't need
    the full functionality of BaseAgent, such as standalone agents used
    for specific tasks like email handling.
    """
    
    def __init__(self, name: str, description: str, llm_connector: Optional[LLMConnector] = None):
        """
        Initialize the agent.
        
        Args:
            name (str): Name of the agent
            description (str): Description of the agent
            llm_connector (Optional[LLMConnector]): LLM connector to use
        """
        self.name = name
        self.description = description
        self.llm_connector = llm_connector
        self.logger = setup_logger(f"agent.{name.lower().replace(' ', '_')}")
        self.logger.info(f"Initializing agent: {name}")
    
    @abstractmethod
    async def get_capabilities(self) -> List[Dict]:
        """
        Get the capabilities of this agent.
        
        Returns:
            List[Dict]: List of capability dictionaries
        """
        pass
    
    async def execute(self, capability: str, parameters: Dict) -> Dict:
        """
        Execute a capability with the given parameters.
        
        Args:
            capability (str): Name of the capability to execute
            parameters (Dict): Parameters for the capability
            
        Returns:
            Dict: Result of the capability execution
        """
        self.logger.info(f"Executing capability: {capability}")
        
        # Get all capabilities
        capabilities = await self.get_capabilities()
        
        # Find the requested capability
        capability_info = next((c for c in capabilities if c["name"] == capability), None)
        
        if not capability_info:
            return {"error": f"Capability not found: {capability}"}
        
        # Check if the agent has a method for this capability
        if hasattr(self, capability) and callable(getattr(self, capability)):
            try:
                # Call the method with the parameters
                method = getattr(self, capability)
                result = await method(**parameters)
                return result
            except Exception as e:
                self.logger.exception(f"Error executing capability {capability}: {e}")
                return {"error": f"Error executing capability: {str(e)}"}
        else:
            return {"error": f"Capability method not implemented: {capability}"}
    
    def get_name(self) -> str:
        """
        Get the name of the agent.
        
        Returns:
            str: Agent name
        """
        return self.name
    
    def get_description(self) -> str:
        """
        Get the description of the agent.
        
        Returns:
            str: Agent description
        """
        return self.description
