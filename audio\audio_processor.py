"""
Audio Processor for the Multi-Agent AI System.

This module provides audio processing capabilities for agents,
including speech recognition, text-to-speech, and audio analysis.
"""
import asyncio
import json
import logging
import os
from typing import Dict, List, Optional, Any, Union
import uuid
from datetime import datetime
import aiohttp

from core.logger import setup_logger

# Set up logger
logger = setup_logger("audio_processor")

class AudioProcessor:
    """
    Audio processor for agents.
    
    This class provides audio processing capabilities for agents,
    including speech recognition, text-to-speech, and audio analysis.
    """
    
    def __init__(self, config: Dict):
        """
        Initialize the audio processor.
        
        Args:
            config (Dict): Audio processor configuration
        """
        self.config = config
        self.enabled = config.get("enabled", False)
        self.api_key = config.get("api_key", "")
        self.api_url = config.get("api_url", "")
        self.local_mode = config.get("local_mode", True)
        
        # Audio processing engines
        self.speech_recognition_engine = config.get("speech_recognition_engine", "whisper")
        self.text_to_speech_engine = config.get("text_to_speech_engine", "elevenlabs")
        
        # Local paths
        self.audio_dir = config.get("audio_dir", "./audio")
        
        logger.info("Audio processor initialized")
    
    async def initialize(self):
        """Initialize the audio processor."""
        if not self.enabled:
            logger.warning("Audio processor is disabled")
            return
        
        try:
            # Create audio directory if it doesn't exist
            os.makedirs(self.audio_dir, exist_ok=True)
            
            # Check if required packages are installed
            await self._check_dependencies()
            
            logger.info("Audio processor initialized")
            
        except Exception as e:
            logger.exception(f"Error initializing audio processor: {e}")
            self.enabled = False
    
    async def _check_dependencies(self):
        """Check if required packages are installed."""
        try:
            # Try to import required packages
            if self.local_mode:
                # For speech recognition
                if self.speech_recognition_engine == "whisper":
                    try:
                        import whisper
                        logger.info("Whisper package found")
                    except ImportError:
                        logger.warning("Whisper package not found")
                        logger.warning("Speech recognition may not work properly")
                
                # For text-to-speech
                if self.text_to_speech_engine == "pyttsx3":
                    try:
                        import pyttsx3
                        logger.info("pyttsx3 package found")
                    except ImportError:
                        logger.warning("pyttsx3 package not found")
                        logger.warning("Text-to-speech may not work properly")
                
                # For audio analysis
                try:
                    import librosa
                    logger.info("librosa package found")
                except ImportError:
                    logger.warning("librosa package not found")
                    logger.warning("Audio analysis may not work properly")
            
        except Exception as e:
            logger.exception(f"Error checking audio processor dependencies: {e}")
    
    async def transcribe_audio(
        self,
        audio_path: str,
        language: str = "en",
    ) -> Dict:
        """
        Transcribe audio to text.
        
        Args:
            audio_path (str): Path to audio file
            language (str): Language code
            
        Returns:
            Dict: Transcription results
        """
        if not self.enabled:
            return {"error": "Audio processor is disabled"}
        
        logger.info(f"Transcribing audio: {audio_path}")
        
        try:
            if self.local_mode:
                return await self._transcribe_audio_local(audio_path, language)
            else:
                return await self._transcribe_audio_api(audio_path, language)
                
        except Exception as e:
            logger.exception(f"Error transcribing audio: {e}")
            return {"error": str(e)}
    
    async def _transcribe_audio_local(
        self,
        audio_path: str,
        language: str,
    ) -> Dict:
        """
        Transcribe audio to text using local engine.
        
        Args:
            audio_path (str): Path to audio file
            language (str): Language code
            
        Returns:
            Dict: Transcription results
        """
        if self.speech_recognition_engine == "whisper":
            try:
                import whisper
                
                # Load model
                model = whisper.load_model("base")
                
                # Transcribe audio
                result = model.transcribe(audio_path, language=language)
                
                return {
                    "text": result["text"],
                    "language": result.get("language", language),
                    "segments": result.get("segments", []),
                    "audio_path": audio_path,
                    "timestamp": datetime.now().isoformat(),
                }
                
            except Exception as e:
                logger.exception(f"Error transcribing audio with Whisper: {e}")
                return {"error": str(e)}
        else:
            return {"error": f"Unsupported speech recognition engine: {self.speech_recognition_engine}"}
    
    async def _transcribe_audio_api(
        self,
        audio_path: str,
        language: str,
    ) -> Dict:
        """
        Transcribe audio to text using API.
        
        Args:
            audio_path (str): Path to audio file
            language (str): Language code
            
        Returns:
            Dict: Transcription results
        """
        if not self.api_url:
            return {"error": "API URL not provided"}
        
        if not self.api_key:
            return {"error": "API key not provided"}
        
        try:
            # Read audio file
            with open(audio_path, "rb") as f:
                audio_data = f.read()
            
            # Prepare request
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "audio/wav",  # Adjust based on file type
            }
            
            params = {
                "language": language,
            }
            
            # Send request
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.api_url}/transcribe",
                    headers=headers,
                    params=params,
                    data=audio_data,
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"Error transcribing audio: {error_text}")
                        return {"error": error_text}
                    
                    result = await response.json()
                    
                    return {
                        "text": result.get("text", ""),
                        "language": result.get("language", language),
                        "segments": result.get("segments", []),
                        "audio_path": audio_path,
                        "timestamp": datetime.now().isoformat(),
                    }
                    
        except Exception as e:
            logger.exception(f"Error transcribing audio with API: {e}")
            return {"error": str(e)}
    
    async def text_to_speech(
        self,
        text: str,
        voice: str = "default",
        output_path: Optional[str] = None,
    ) -> Dict:
        """
        Convert text to speech.
        
        Args:
            text (str): Text to convert
            voice (str): Voice to use
            output_path (Optional[str]): Path to save audio file
            
        Returns:
            Dict: Text-to-speech results
        """
        if not self.enabled:
            return {"error": "Audio processor is disabled"}
        
        logger.info(f"Converting text to speech: {text[:50]}...")
        
        try:
            if self.local_mode:
                return await self._text_to_speech_local(text, voice, output_path)
            else:
                return await self._text_to_speech_api(text, voice, output_path)
                
        except Exception as e:
            logger.exception(f"Error converting text to speech: {e}")
            return {"error": str(e)}
    
    async def _text_to_speech_local(
        self,
        text: str,
        voice: str,
        output_path: Optional[str],
    ) -> Dict:
        """
        Convert text to speech using local engine.
        
        Args:
            text (str): Text to convert
            voice (str): Voice to use
            output_path (Optional[str]): Path to save audio file
            
        Returns:
            Dict: Text-to-speech results
        """
        if self.text_to_speech_engine == "pyttsx3":
            try:
                import pyttsx3
                
                # Create output path if not provided
                if not output_path:
                    os.makedirs(os.path.join(self.audio_dir, "tts"), exist_ok=True)
                    output_path = os.path.join(
                        self.audio_dir, "tts", f"tts_{uuid.uuid4()}.wav"
                    )
                
                # Initialize engine
                engine = pyttsx3.init()
                
                # Set voice
                voices = engine.getProperty("voices")
                if voice != "default" and len(voices) > 1:
                    # Try to find voice by name or ID
                    for v in voices:
                        if voice.lower() in v.name.lower() or voice == v.id:
                            engine.setProperty("voice", v.id)
                            break
                
                # Set properties
                engine.setProperty("rate", 150)  # Speed
                engine.setProperty("volume", 1.0)  # Volume
                
                # Save to file
                engine.save_to_file(text, output_path)
                engine.runAndWait()
                
                return {
                    "text": text,
                    "voice": voice,
                    "audio_path": output_path,
                    "timestamp": datetime.now().isoformat(),
                }
                
            except Exception as e:
                logger.exception(f"Error converting text to speech with pyttsx3: {e}")
                return {"error": str(e)}
        else:
            return {"error": f"Unsupported text-to-speech engine: {self.text_to_speech_engine}"}
    
    async def _text_to_speech_api(
        self,
        text: str,
        voice: str,
        output_path: Optional[str],
    ) -> Dict:
        """
        Convert text to speech using API.
        
        Args:
            text (str): Text to convert
            voice (str): Voice to use
            output_path (Optional[str]): Path to save audio file
            
        Returns:
            Dict: Text-to-speech results
        """
        if not self.api_url:
            return {"error": "API URL not provided"}
        
        if not self.api_key:
            return {"error": "API key not provided"}
        
        try:
            # Create output path if not provided
            if not output_path:
                os.makedirs(os.path.join(self.audio_dir, "tts"), exist_ok=True)
                output_path = os.path.join(
                    self.audio_dir, "tts", f"tts_{uuid.uuid4()}.wav"
                )
            
            # Prepare request
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
            }
            
            data = {
                "text": text,
                "voice": voice,
            }
            
            # Send request
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.api_url}/tts",
                    headers=headers,
                    json=data,
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"Error converting text to speech: {error_text}")
                        return {"error": error_text}
                    
                    # Save audio to file
                    with open(output_path, "wb") as f:
                        f.write(await response.read())
                    
                    return {
                        "text": text,
                        "voice": voice,
                        "audio_path": output_path,
                        "timestamp": datetime.now().isoformat(),
                    }
                    
        except Exception as e:
            logger.exception(f"Error converting text to speech with API: {e}")
            return {"error": str(e)}
