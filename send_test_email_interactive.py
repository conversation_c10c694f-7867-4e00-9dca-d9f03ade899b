"""
Interactive script to send a test email from a Gmail account.
This script provides a visual, interactive way to send a test email.
"""
import os
import sys
import json
import time
import pickle
import subprocess
from pathlib import Path

def clear_screen():
    """Clear the terminal screen."""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_header():
    """Print the script header."""
    clear_screen()
    print("=" * 80)
    print("                     INTERACTIVE EMAIL SENDER")
    print("=" * 80)
    print("\nThis tool allows you to send test emails from your configured Gmail accounts.")
    print("You'll see the process happening in real-time with visual feedback.")
    print("\n")

def get_configured_accounts():
    """
    Get a list of configured Gmail accounts.
    
    Returns:
        list: List of configured Gmail accounts
    """
    accounts = []
    credentials_dir = 'credentials'
    
    if not os.path.exists(credentials_dir):
        return accounts
    
    for filename in os.listdir(credentials_dir):
        if filename.startswith('gmail_') and filename.endswith('_credentials.json'):
            # Extract email from filename
            email_part = filename[6:-16]  # Remove 'gmail_' prefix and '_credentials.json' suffix
            email = email_part.replace('_at_', '@').replace('_dot_', '.')
            
            # Check if token file exists (indicating the account is authenticated)
            token_path = os.path.join(credentials_dir, filename.replace('_credentials.json', '_token.pickle'))
            is_authenticated = os.path.exists(token_path)
            
            accounts.append({
                'email': email,
                'credentials_path': os.path.join(credentials_dir, filename),
                'token_path': token_path,
                'is_authenticated': is_authenticated
            })
    
    return accounts

def send_test_email(account):
    """
    Send a test email from a specific account.
    
    Args:
        account (dict): Account information
        
    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    email = account['email']
    credentials_path = account['credentials_path']
    token_path = account['token_path']
    
    print_header()
    print(f"SENDING TEST EMAIL FROM {email}")
    print("-" * 80)
    
    # Check if account is authenticated
    if not account['is_authenticated']:
        print(f"\nAccount {email} is not authenticated.")
        print("Please run the interactive_gmail_setup.py script to authenticate this account first.")
        return False
    
    # Install required packages if not already installed
    try:
        from google.auth.transport.requests import Request
        from google.oauth2.credentials import Credentials
        from google_auth_oauthlib.flow import InstalledAppFlow
        from googleapiclient.discovery import build
        from googleapiclient.errors import HttpError
        from email.mime.text import MIMEText
        import base64
    except ImportError:
        print("\nInstalling required packages...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", 
                              "google-auth", "google-auth-oauthlib", 
                              "google-auth-httplib2", "google-api-python-client"])
        
        from google.auth.transport.requests import Request
        from google.oauth2.credentials import Credentials
        from google_auth_oauthlib.flow import InstalledAppFlow
        from googleapiclient.discovery import build
        from googleapiclient.errors import HttpError
        from email.mime.text import MIMEText
        import base64
    
    # Gmail API scopes
    SCOPES = [
        'https://www.googleapis.com/auth/gmail.readonly',
        'https://www.googleapis.com/auth/gmail.send',
        'https://www.googleapis.com/auth/gmail.compose',
        'https://www.googleapis.com/auth/gmail.modify'
    ]
    
    try:
        print("\nConnecting to Gmail API...")
        
        # Load credentials
        creds = None
        if os.path.exists(token_path):
            with open(token_path, 'rb') as token:
                creds = pickle.load(token)
        
        # Refresh token if expired
        if creds and creds.expired and creds.refresh_token:
            print("Refreshing expired token...")
            creds.refresh(Request())
            
            # Save the refreshed credentials
            with open(token_path, 'wb') as token:
                pickle.dump(creds, token)
        
        # If no valid credentials, authenticate again
        if not creds or not creds.valid:
            print("No valid credentials found. Starting authentication process...")
            flow = InstalledAppFlow.from_client_secrets_file(credentials_path, SCOPES)
            creds = flow.run_local_server(port=0)
            
            # Save the credentials
            with open(token_path, 'wb') as token:
                pickle.dump(creds, token)
        
        # Build the Gmail service
        service = build('gmail', 'v1', credentials=creds)
        
        # Get user profile to confirm authentication
        profile = service.users().getProfile(userId='me').execute()
        authenticated_email = profile.get('emailAddress')
        
        print(f"✓ Successfully authenticated as {authenticated_email}")
        
        # Get email details from user
        print("\nEnter the details for your test email:")
        to_email = input("To: ")
        subject = input("Subject: ")
        body = input("Body: ")
        
        # Create the email message
        print("\nCreating email message...")
        message = MIMEText(body)
        message['to'] = to_email
        message['from'] = authenticated_email
        message['subject'] = subject
        
        # Encode the message
        raw_message = base64.urlsafe_b64encode(message.as_bytes()).decode()
        
        # Send the email
        print("\nSending email...")
        send_message = service.users().messages().send(
            userId='me',
            body={'raw': raw_message}
        ).execute()
        
        print(f"\n✓ Email sent successfully! Message ID: {send_message['id']}")
        return True
    
    except Exception as e:
        print(f"\nError sending email: {e}")
        return False

def main():
    """Main entry point."""
    print_header()
    
    # Get configured accounts
    accounts = get_configured_accounts()
    
    if not accounts:
        print("\nNo Gmail accounts configured.")
        print("Please run the interactive_gmail_setup.py script to configure your accounts first.")
        return
    
    # Filter authenticated accounts
    authenticated_accounts = [account for account in accounts if account['is_authenticated']]
    
    if not authenticated_accounts:
        print("\nNo authenticated Gmail accounts found.")
        print("Please run the interactive_gmail_setup.py script to authenticate your accounts first.")
        return
    
    print("\nAuthenticated Gmail accounts:")
    for i, account in enumerate(authenticated_accounts):
        print(f"{i+1}. {account['email']}")
    
    print(f"{len(authenticated_accounts)+1}. Exit")
    
    try:
        index = int(input("\nSelect an account to send a test email from: ")) - 1
        
        if index == len(authenticated_accounts):
            # Exit
            print("\nExiting...")
            return
        
        elif 0 <= index < len(authenticated_accounts):
            # Send test email
            send_test_email(authenticated_accounts[index])
            
            # Ask if user wants to send another email
            another = input("\nDo you want to send another email? (y/n): ").lower()
            if another == 'y':
                main()
        
        else:
            print("\nInvalid selection.")
            input("\nPress Enter to try again...")
            main()
    
    except ValueError:
        print("\nInvalid selection.")
        input("\nPress Enter to try again...")
        main()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nExiting...")
        sys.exit(0)
