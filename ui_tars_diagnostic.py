"""
UI-TARS Diagnostic Tool

This script provides comprehensive diagnostics for UI-TARS 1.5, including:
1. Checking UI-TARS installation
2. Verifying configuration
3. Testing browser integration
4. Checking API connectivity
5. Analyzing log files
6. Fixing common issues
"""
import os
import sys
import time
import json
import socket
import logging
import argparse
import platform
import subprocess
import re
from pathlib import Path

try:
    import requests
    import psutil
except ImportError:
    print("Installing required packages...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "requests", "psutil"])
    import requests
    import psutil

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("ui_tars_diagnostic.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("ui_tars_diagnostic")

def check_file_exists(path):
    """Check if a file exists."""
    return os.path.isfile(path)

def check_directory_exists(path):
    """Check if a directory exists."""
    return os.path.isdir(path)

def check_port_open(host, port):
    """Check if a port is open."""
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.settimeout(2)
    result = sock.connect_ex((host, port))
    sock.close()
    return result == 0

def find_ui_tars_executable():
    """Find UI-TARS executable path."""
    logger.info("Searching for UI-TARS executable")

    # Common installation paths
    os_type = platform.system()

    if os_type == "Windows":
        paths = [
            os.path.join(os.environ.get("LOCALAPPDATA", ""), "UI-TARS", "UI-TARS.exe"),
            os.path.join(os.environ.get("PROGRAMFILES", ""), "UI-TARS", "UI-TARS.exe"),
            os.path.join(os.environ.get("PROGRAMFILES(X86)", ""), "UI-TARS", "UI-TARS.exe"),
            "C:\\UI-TARS\\UI-TARS.exe",
            os.path.expanduser("~\\UI-TARS\\UI-TARS.exe"),
            "UI-TARS.exe"
        ]
    elif os_type == "Darwin":  # macOS
        paths = [
            "/Applications/UI-TARS.app/Contents/MacOS/UI-TARS",
            os.path.expanduser("~/Applications/UI-TARS.app/Contents/MacOS/UI-TARS")
        ]
    else:  # Linux
        paths = [
            "/usr/bin/ui-tars",
            "/usr/local/bin/ui-tars",
            os.path.expanduser("~/.local/bin/ui-tars")
        ]

    # Check if any of the paths exist
    for path in paths:
        if os.path.exists(path):
            logger.info(f"Found UI-TARS at: {path}")
            return path

    # Try to find in PATH
    try:
        if os_type == "Windows":
            result = subprocess.run(["where", "UI-TARS.exe"], capture_output=True, text=True)
        else:
            result = subprocess.run(["which", "ui-tars"], capture_output=True, text=True)

        if result.returncode == 0:
            path = result.stdout.strip()
            logger.info(f"Found UI-TARS in PATH: {path}")
            return path
    except:
        pass

    # If not found in common paths, search for running process
    for proc in psutil.process_iter(['pid', 'name', 'exe']):
        try:
            if 'UI-TARS' in proc.info['name']:
                exe = proc.info['exe']
                if exe and os.path.exists(exe):
                    logger.info(f"Found running UI-TARS at: {exe}")
                    return exe
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass

    logger.warning("UI-TARS executable not found")
    return None

def detect_browsers():
    """Detect installed browsers."""
    logger.info("Detecting installed browsers")

    browsers = {}
    os_type = platform.system()

    if os_type == "Windows":
        # Check for common browsers on Windows
        paths = [
            (os.path.join(os.environ.get("PROGRAMFILES", ""), "Google", "Chrome", "Application", "chrome.exe"), "chrome"),
            (os.path.join(os.environ.get("PROGRAMFILES(X86)", ""), "Google", "Chrome", "Application", "chrome.exe"), "chrome"),
            (os.path.join(os.environ.get("LOCALAPPDATA", ""), "Google", "Chrome", "Application", "chrome.exe"), "chrome"),
            (os.path.join(os.environ.get("PROGRAMFILES", ""), "Microsoft", "Edge", "Application", "msedge.exe"), "edge"),
            (os.path.join(os.environ.get("PROGRAMFILES(X86)", ""), "Microsoft", "Edge", "Application", "msedge.exe"), "edge"),
            (os.path.join(os.environ.get("PROGRAMFILES", ""), "Mozilla Firefox", "firefox.exe"), "firefox"),
            (os.path.join(os.environ.get("PROGRAMFILES(X86)", ""), "Mozilla Firefox", "firefox.exe"), "firefox"),
            (os.path.join(os.environ.get("PROGRAMFILES", ""), "BraveSoftware", "Brave-Browser", "Application", "brave.exe"), "brave"),
            (os.path.join(os.environ.get("LOCALAPPDATA", ""), "BraveSoftware", "Brave-Browser", "Application", "brave.exe"), "brave")
        ]
    elif os_type == "Darwin":  # macOS
        # Check for common browsers on macOS
        paths = [
            ("/Applications/Google Chrome.app/Contents/MacOS/Google Chrome", "chrome"),
            ("/Applications/Firefox.app/Contents/MacOS/firefox", "firefox"),
            ("/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge", "edge"),
            ("/Applications/Brave Browser.app/Contents/MacOS/Brave Browser", "brave"),
        ]
    else:  # Linux
        # Check for common browsers on Linux
        paths = [
            ("/usr/bin/google-chrome", "chrome"),
            ("/usr/bin/firefox", "firefox"),
            ("/usr/bin/microsoft-edge", "edge"),
            ("/usr/bin/brave-browser", "brave"),
        ]

    # Check if any of the paths exist
    for path, browser_type in paths:
        if os.path.exists(path):
            browsers[browser_type] = path
            logger.info(f"Found {browser_type} browser at: {path}")

    return browsers

def check_browser_debugging_port(port=9222):
    """Check if browser remote debugging port is open."""
    if not check_port_open("localhost", port):
        print(f"❌ Browser remote debugging port {port} is not open")
        return False

    try:
        response = requests.get(f"http://localhost:{port}/json/version", timeout=2)
        if response.status_code == 200:
            print(f"✅ Browser remote debugging is active on port {port}")
            return True
        else:
            print(f"❌ Browser remote debugging returned status code {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error checking browser remote debugging: {e}")
        return False

def start_browser_with_remote_debugging(browser_path, browser_type, port=9222):
    """Start browser with remote debugging enabled."""
    if not browser_path:
        print("❌ Cannot start browser: browser path not provided")
        return False

    try:
        print(f"Starting {browser_type} with remote debugging on port {port}...")

        # Create user data directory
        import tempfile
        user_data_dir = os.path.join(tempfile.gettempdir(), "ui_tars_browser_data")
        os.makedirs(user_data_dir, exist_ok=True)

        # Prepare command
        command = [
            browser_path,
            f"--remote-debugging-port={port}",
            f"--user-data-dir={user_data_dir}",
            "--no-first-run",
            "--no-default-browser-check",
            "--disable-extensions",
            "--disable-component-extensions-with-background-pages",
            "--disable-background-networking",
            "--disable-background-timer-throttling",
            "--disable-backgrounding-occluded-windows",
            "--disable-breakpad",
            "--disable-client-side-phishing-detection",
            "--disable-default-apps",
            "--disable-dev-shm-usage",
            "--disable-features=Translate,BackForwardCache,AcceptCHFrame,MediaRouter,OptimizationHints",
            "--disable-hang-monitor",
            "--disable-ipc-flooding-protection",
            "--disable-popup-blocking",
            "--disable-prompt-on-repost",
            "--disable-renderer-backgrounding",
            "--disable-sync",
            "--disable-web-security",
            "--enable-automation",
            "--force-color-profile=srgb",
            "--metrics-recording-only",
            "--no-sandbox",
            "--password-store=basic",
            "--use-mock-keychain",
            "--window-size=1280,720",
            "about:blank"
        ]

        # Start browser process
        process = subprocess.Popen(command)

        # Wait for browser to start
        time.sleep(5)

        # Check if browser started successfully
        if process.poll() is not None:
            print(f"❌ Browser process exited with code {process.returncode}")
            return False

        # Check if remote debugging is active
        for _ in range(5):  # Try 5 times
            if check_browser_debugging_port(port):
                return True
            time.sleep(1)

        print("❌ Browser remote debugging not active after multiple attempts")
        return False

    except Exception as e:
        print(f"❌ Error starting browser: {e}")
        return False

def check_ui_tars_api(host="localhost", port=8080):
    """Check if the UI-TARS API is running."""
    if not check_port_open(host, port):
        print(f"❌ UI-TARS API not running on {host}:{port}")
        return False

    try:
        response = requests.get(f"http://{host}:{port}/health", timeout=2)
        if response.status_code == 200:
            print(f"✅ UI-TARS API is running on {host}:{port}")
            return True
        else:
            print(f"❌ UI-TARS API returned status code {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error checking UI-TARS API: {e}")
        return False

def check_midscene_api(host="localhost", port=8081):
    """Check if the Midscene API is running."""
    if not check_port_open(host, port):
        print(f"❌ Midscene API not running on {host}:{port}")
        return False

    try:
        response = requests.get(f"http://{host}:{port}/health", timeout=2)
        if response.status_code == 200:
            print(f"✅ Midscene API is running on {host}:{port}")
            return True
        else:
            print(f"❌ Midscene API returned status code {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error checking Midscene API: {e}")
        return False

def check_midscene_installation():
    """Check if Midscene is installed."""
    try:
        result = subprocess.run(
            ["npm", "list", "-g", "@midscene/cli"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )

        if result.returncode == 0 and "@midscene/cli" in result.stdout:
            print("✅ Midscene is installed")
            return True
        else:
            print("❌ Midscene is not installed")
    except Exception as e:
        print(f"❌ Error checking Midscene installation: {e}")

    return False

def start_ui_tars(executable_path):
    """Try to start UI-TARS."""
    if not executable_path:
        print("❌ Cannot start UI-TARS: executable path not provided")
        return False

    try:
        print(f"Starting UI-TARS from {executable_path}...")
        process = subprocess.Popen([executable_path])
        time.sleep(5)  # Give it time to start
        return process.poll() is None  # Still running
    except Exception as e:
        print(f"❌ Error starting UI-TARS: {e}")
        return False

def find_ui_tars_config():
    """Find UI-TARS configuration file."""
    logger.info("Searching for UI-TARS configuration file")

    # Common configuration paths
    os_type = platform.system()

    if os_type == "Windows":
        paths = [
            os.path.join(os.environ.get("LOCALAPPDATA", ""), "UI-TARS", "config.json"),
            os.path.join(os.environ.get("APPDATA", ""), "UI-TARS", "config.json"),
            "ui_tars\\config.json",
            "config\\ui_tars_config.json"
        ]
    else:
        paths = [
            os.path.expanduser("~/.config/ui-tars/config.json"),
            os.path.expanduser("~/.ui-tars/config.json"),
            "ui_tars/config.json",
            "config/ui_tars_config.json"
        ]

    # Check if any of the paths exist
    for path in paths:
        if os.path.exists(path):
            logger.info(f"Found UI-TARS configuration at: {path}")
            return path

    logger.warning("UI-TARS configuration file not found")
    return None

def fix_common_issues(executable_path, config_path, browser_path, browser_type):
    """Fix common UI-TARS issues."""
    print("\nAttempting to fix common issues...")

    fixes_applied = []

    # Check if UI-TARS is running
    ui_tars_running = False
    for proc in psutil.process_iter(['pid', 'name']):
        try:
            if 'UI-TARS' in proc.info['name']:
                ui_tars_running = True
                break
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass

    # Kill UI-TARS if it's running but API is not responding
    if ui_tars_running and not check_ui_tars_api():
        print("UI-TARS is running but API is not responding. Attempting to restart...")
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if 'UI-TARS' in proc.info['name']:
                    proc.terminate()
                    fixes_applied.append("Terminated non-responsive UI-TARS process")
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass

        time.sleep(2)

        # Start UI-TARS
        if executable_path and start_ui_tars(executable_path):
            fixes_applied.append("Restarted UI-TARS")

    # Check if browser debugging port is in use but not by a browser
    if check_port_open("localhost", 9222) and not check_browser_debugging_port():
        print("Port 9222 is in use but not by a browser with remote debugging. Attempting to free port...")

        # Try to kill process using port 9222
        os_type = platform.system()
        if os_type == "Windows":
            try:
                output = subprocess.check_output("netstat -ano | findstr :9222", shell=True).decode()
                for line in output.splitlines():
                    if ":9222" in line and "LISTENING" in line:
                        pid = line.strip().split()[-1]
                        try:
                            subprocess.check_output(f"taskkill /F /PID {pid}", shell=True)
                            fixes_applied.append(f"Killed process with PID {pid} using port 9222")
                        except:
                            pass
            except:
                pass
        else:
            try:
                output = subprocess.check_output("lsof -i :9222 -t", shell=True).decode()
                for pid in output.splitlines():
                    try:
                        subprocess.check_output(f"kill -9 {pid}", shell=True)
                        fixes_applied.append(f"Killed process with PID {pid} using port 9222")
                    except:
                        pass
            except:
                pass

    # Start browser with remote debugging if needed
    if browser_path and not check_browser_debugging_port():
        print("Browser remote debugging not active. Attempting to start browser...")
        if start_browser_with_remote_debugging(browser_path, browser_type):
            fixes_applied.append(f"Started {browser_type} with remote debugging")

    # Create or update UI-TARS configuration if needed
    if config_path:
        try:
            with open(config_path, "r") as f:
                config = json.load(f)

            # Check if configuration needs updates
            config_updated = False

            # Update browser settings if needed
            if browser_path and browser_type:
                if "browser" not in config:
                    config["browser"] = {}

                if "type" not in config["browser"] or config["browser"]["type"] != browser_type:
                    config["browser"]["type"] = browser_type
                    config_updated = True

                if "path" not in config["browser"] or config["browser"]["path"] != browser_path:
                    config["browser"]["path"] = browser_path
                    config_updated = True

                if "remote_debugging_port" not in config["browser"] or config["browser"]["remote_debugging_port"] != 9222:
                    config["browser"]["remote_debugging_port"] = 9222
                    config_updated = True

            # Update API settings if needed
            if "api" not in config:
                config["api"] = {}

            if "port" not in config["api"] or config["api"]["port"] != 8080:
                config["api"]["port"] = 8080
                config_updated = True

            # Save updated configuration
            if config_updated:
                with open(config_path, "w") as f:
                    json.dump(config, f, indent=4)
                fixes_applied.append("Updated UI-TARS configuration")

        except Exception as e:
            print(f"Error updating UI-TARS configuration: {e}")

    return fixes_applied

async def run_enhanced_diagnostics(executable_path, config_path, browser_type, browser_path):
    """
    Run enhanced diagnostics using the EnhancedUITarsConnector.

    Args:
        executable_path (str): Path to UI-TARS executable
        config_path (str): Path to UI-TARS configuration file
        browser_type (str): Browser type
        browser_path (str): Path to browser executable

    Returns:
        tuple: (health_result, repair_result)
    """
    print("\nRunning enhanced diagnostics...")

    try:
        # Add the project root to the Python path
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

        # Import the EnhancedUITarsConnector
        try:
            from ui_tars.connector.enhanced_ui_tars_connector import EnhancedUITarsConnector
        except ImportError:
            print("❌ Failed to import EnhancedUITarsConnector")
            print("Make sure the ui_tars package is properly installed")
            return None, None

        # Create connector
        connector = EnhancedUITarsConnector(
            api_url="http://localhost:8080",
            installation_path=executable_path,
            browser_type=browser_type,
            browser_path=browser_path,
            auto_start=False,
            auto_restart=False
        )

        # Run health check
        print("Running health check...")
        health_result = await connector.health_check()

        # Print health check results
        print("\nHealth Check Results:")
        print(f"Status: {health_result['status']}")

        if health_result["issues"]:
            print("\nIssues:")
            for issue in health_result["issues"]:
                print(f"❌ {issue}")

        print("\nComponents:")
        for component, status in health_result["components"].items():
            status_icon = "✅" if status else "❌"
            print(f"{status_icon} {component}")

        # Run auto-repair if there are issues
        repair_result = None
        if health_result["status"] == "unhealthy":
            print("\nAttempting to auto-repair...")
            repair_result = await connector.auto_repair()

            print("\nAuto-Repair Results:")
            print(f"Success: {'✅ Yes' if repair_result['success'] else '❌ No'}")
            print(f"Message: {repair_result['message']}")

            if repair_result["actions_taken"]:
                print("\nActions Taken:")
                for action in repair_result["actions_taken"]:
                    print(f"✅ {action}")

        return health_result, repair_result

    except Exception as e:
        print(f"❌ Error running enhanced diagnostics: {e}")
        return None, None

def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="UI-TARS Diagnostic Tool")
    parser.add_argument("--path", type=str, help="Path to UI-TARS executable")
    parser.add_argument("--host", type=str, default="localhost", help="UI-TARS API host")
    parser.add_argument("--port", type=int, default=8080, help="UI-TARS API port")
    parser.add_argument("--start", action="store_true", help="Try to start UI-TARS if not running")
    parser.add_argument("--fix", action="store_true", help="Try to fix common issues")
    parser.add_argument("--browser", type=str, choices=["chrome", "edge", "firefox", "brave"], help="Browser to use")
    parser.add_argument("--browser-path", type=str, help="Path to browser executable")
    parser.add_argument("--enhanced", action="store_true", help="Use enhanced diagnostics")

    args = parser.parse_args()

    print("UI-TARS Diagnostic Tool")
    print("======================")
    print()

    # Check UI-TARS executable
    executable_path = args.path or find_ui_tars_executable()
    if executable_path:
        print(f"✅ UI-TARS executable found at: {executable_path}")
    else:
        print("❌ UI-TARS executable not found")
        print("Please provide the path to UI-TARS.exe with --path")

    # Find UI-TARS configuration
    config_path = find_ui_tars_config()
    if config_path:
        print(f"✅ UI-TARS configuration found at: {config_path}")
    else:
        print("❌ UI-TARS configuration not found")

    # Detect browsers
    browsers = detect_browsers()
    if browsers:
        print(f"✅ Found {len(browsers)} browsers:")
        for browser_type, path in browsers.items():
            print(f"  - {browser_type}: {path}")
    else:
        print("❌ No browsers found")

    # Determine browser to use
    browser_type = args.browser
    browser_path = args.browser_path

    if not browser_type and browsers:
        # Prefer Chrome, then Edge, then others
        if "chrome" in browsers:
            browser_type = "chrome"
            browser_path = browsers["chrome"]
        elif "edge" in browsers:
            browser_type = "edge"
            browser_path = browsers["edge"]
        else:
            browser_type = list(browsers.keys())[0]
            browser_path = browsers[browser_type]

    if browser_type and not browser_path and browser_type in browsers:
        browser_path = browsers[browser_type]

    if browser_type and browser_path:
        print(f"✅ Using {browser_type} browser at: {browser_path}")
    else:
        print("❌ No suitable browser selected")

    # Use enhanced diagnostics if requested
    if args.enhanced and executable_path and browser_type and browser_path:
        import asyncio
        health_result, repair_result = asyncio.run(run_enhanced_diagnostics(executable_path, config_path, browser_type, browser_path))

        # If enhanced diagnostics succeeded and repairs were successful, we're done
        if health_result and repair_result and repair_result["success"]:
            print("\n✅ Enhanced diagnostics completed successfully")
            print("UI-TARS 1.5 is properly configured and ready to use.")
            return 0

    # Check browser remote debugging
    browser_debugging = check_browser_debugging_port()

    # Check UI-TARS API
    api_running = check_ui_tars_api(args.host, args.port)

    # Check Midscene installation and API
    midscene_installed = check_midscene_installation()
    midscene_api_running = check_midscene_api(args.host, 8081)

    # Fix common issues if requested
    fixes_applied = []
    if args.fix:
        fixes_applied = fix_common_issues(executable_path, config_path, browser_path, browser_type)

        if fixes_applied:
            print("\nFixes applied:")
            for fix in fixes_applied:
                print(f"✅ {fix}")

            # Re-check status after fixes
            print("\nRe-checking status after fixes...")
            browser_debugging = check_browser_debugging_port()
            api_running = check_ui_tars_api(args.host, args.port)

    # Try to start UI-TARS if not running
    if not api_running and args.start and executable_path:
        print("Trying to start UI-TARS...")
        if start_ui_tars(executable_path):
            print("✅ UI-TARS started successfully")
            time.sleep(5)  # Give it time to initialize
            api_running = check_ui_tars_api(args.host, args.port)
        else:
            print("❌ Failed to start UI-TARS")

    # Print summary
    print("\nDiagnostic Summary:")
    print(f"- UI-TARS Executable: {'Found' if executable_path else 'Not Found'}")
    print(f"- UI-TARS Configuration: {'Found' if config_path else 'Not Found'}")
    print(f"- Browser: {browser_type if browser_type else 'Not Selected'}")
    print(f"- Browser Remote Debugging: {'Active' if browser_debugging else 'Not Active'}")
    print(f"- UI-TARS API: {'Running' if api_running else 'Not Running'}")
    print(f"- Midscene: {'Installed' if midscene_installed else 'Not Installed'}")
    print(f"- Midscene API: {'Running' if midscene_api_running else 'Not Running'}")

    # Print recommendations
    print("\nRecommendations:")

    if not executable_path:
        print("1. Make sure UI-TARS 1.5 is installed")
        print("2. Provide the path to UI-TARS.exe with --path")

    if not config_path:
        print("3. Create a UI-TARS configuration file")

    if not browser_type or not browser_path:
        print("4. Install a supported browser (Chrome, Edge, Firefox, Brave)")
        print("5. Specify the browser to use with --browser")

    if not browser_debugging:
        print("6. Start the browser with remote debugging enabled")
        print("7. Run this script with --fix to attempt automatic fixes")

    if not api_running:
        print("8. Start UI-TARS manually")
        print("9. Check if UI-TARS is configured to expose the API")
        print("10. Run this script with --start to try starting UI-TARS")

    if not midscene_installed:
        print("11. Install Midscene with: npm install -g @midscene/web @midscene/android @midscene/core @midscene/cli")

    if midscene_installed and not midscene_api_running:
        print("12. Start Midscene with: npx @midscene/cli start --browser chrome")

    if executable_path and browser_type and browser_path and browser_debugging and api_running:
        print("✅ UI-TARS components are working correctly!")
        print("UI-TARS 1.5 is properly configured and ready to use.")

    if midscene_installed and midscene_api_running:
        print("✅ Midscene components are working correctly!")
        print("Midscene is properly configured and ready to use.")

    return 0 if executable_path and api_running and browser_debugging else 1

if __name__ == "__main__":
    sys.exit(main())
