"""
Advanced Memory System for the Multi-Agent AI System.

This module provides sophisticated memory capabilities for agents,
including vector-based retrieval, memory categorization, and
memory consolidation mechanisms.
"""
import asyncio
import json
import logging
import os
from typing import Dict, List, Optional, Any, Union, Tuple
import uuid
from datetime import datetime, timedelta
import numpy as np
from pathlib import Path
import pickle
import heapq

from core.logger import setup_logger
from core.state_manager import StateManager

# Set up logger
logger = setup_logger("advanced_memory")

class MemoryItem:
    """
    A single memory item.
    
    This class represents a single memory item with metadata.
    """
    
    def __init__(
        self,
        content: Any,
        memory_type: str = "episodic",
        source: str = "unknown",
        timestamp: Optional[datetime] = None,
        importance: float = 0.5,
        embedding: Optional[np.ndarray] = None,
        metadata: Optional[Dict] = None,
    ):
        """
        Initialize a memory item.
        
        Args:
            content (Any): Memory content
            memory_type (str): Type of memory (episodic, semantic, procedural)
            source (str): Source of the memory
            timestamp (Optional[datetime]): When the memory was created
            importance (float): Importance score (0.0 to 1.0)
            embedding (Optional[np.ndarray]): Vector embedding of the memory
            metadata (Optional[Dict]): Additional metadata
        """
        self.id = str(uuid.uuid4())
        self.content = content
        self.memory_type = memory_type
        self.source = source
        self.timestamp = timestamp or datetime.now()
        self.importance = importance
        self.embedding = embedding
        self.metadata = metadata or {}
        self.access_count = 0
        self.last_accessed = None
        
    def to_dict(self) -> Dict:
        """Convert memory item to dictionary."""
        return {
            "id": self.id,
            "content": self.content,
            "memory_type": self.memory_type,
            "source": self.source,
            "timestamp": self.timestamp.isoformat(),
            "importance": self.importance,
            "metadata": self.metadata,
            "access_count": self.access_count,
            "last_accessed": self.last_accessed.isoformat() if self.last_accessed else None,
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'MemoryItem':
        """Create memory item from dictionary."""
        item = cls(
            content=data["content"],
            memory_type=data["memory_type"],
            source=data["source"],
            timestamp=datetime.fromisoformat(data["timestamp"]),
            importance=data["importance"],
            metadata=data["metadata"],
        )
        item.id = data["id"]
        item.access_count = data["access_count"]
        item.last_accessed = datetime.fromisoformat(data["last_accessed"]) if data.get("last_accessed") else None
        return item
    
    def access(self):
        """Record memory access."""
        self.access_count += 1
        self.last_accessed = datetime.now()

class AdvancedMemory:
    """
    Advanced memory system for agents.
    
    This class provides sophisticated memory capabilities for agents,
    including vector-based retrieval, memory categorization, and
    memory consolidation mechanisms.
    """
    
    def __init__(
        self,
        agent_id: str,
        state_manager: StateManager,
        embedding_dimension: int = 768,
        max_memory_items: int = 10000,
        consolidation_interval: int = 3600,  # seconds
    ):
        """
        Initialize the advanced memory system.
        
        Args:
            agent_id (str): Agent ID
            state_manager (StateManager): State manager
            embedding_dimension (int): Dimension of memory embeddings
            max_memory_items (int): Maximum number of memory items to store
            consolidation_interval (int): Interval for memory consolidation
        """
        self.agent_id = agent_id
        self.state_manager = state_manager
        self.embedding_dimension = embedding_dimension
        self.max_memory_items = max_memory_items
        self.consolidation_interval = consolidation_interval
        
        # Memory storage
        self.episodic_memory: Dict[str, MemoryItem] = {}
        self.semantic_memory: Dict[str, MemoryItem] = {}
        self.procedural_memory: Dict[str, MemoryItem] = {}
        
        # Memory embeddings
        self.episodic_embeddings: Dict[str, np.ndarray] = {}
        self.semantic_embeddings: Dict[str, np.ndarray] = {}
        
        # Memory locks
        self.memory_lock = asyncio.Lock()
        
        # Last consolidation timestamp
        self.last_consolidation = datetime.now()
    
    async def initialize(self):
        """Initialize the memory system."""
        logger.info(f"Initializing advanced memory for agent {self.agent_id}")
        
        # Load memory from state manager
        await self._load_memory()
        
        # Start memory consolidation task
        asyncio.create_task(self._consolidation_loop())
    
    async def _load_memory(self):
        """Load memory from state manager."""
        async with self.memory_lock:
            # Load episodic memory
            episodic_data = await self.state_manager.get_state("memory", f"{self.agent_id}_episodic")
            if episodic_data:
                for item_data in episodic_data:
                    item = MemoryItem.from_dict(item_data)
                    self.episodic_memory[item.id] = item
            
            # Load semantic memory
            semantic_data = await self.state_manager.get_state("memory", f"{self.agent_id}_semantic")
            if semantic_data:
                for item_data in semantic_data:
                    item = MemoryItem.from_dict(item_data)
                    self.semantic_memory[item.id] = item
            
            # Load procedural memory
            procedural_data = await self.state_manager.get_state("memory", f"{self.agent_id}_procedural")
            if procedural_data:
                for item_data in procedural_data:
                    item = MemoryItem.from_dict(item_data)
                    self.procedural_memory[item.id] = item
            
            # Load embeddings
            embeddings_data = await self.state_manager.get_state("memory", f"{self.agent_id}_embeddings")
            if embeddings_data:
                if "episodic" in embeddings_data:
                    self.episodic_embeddings = {k: np.array(v) for k, v in embeddings_data["episodic"].items()}
                if "semantic" in embeddings_data:
                    self.semantic_embeddings = {k: np.array(v) for k, v in embeddings_data["semantic"].items()}
    
    async def _save_memory(self):
        """Save memory to state manager."""
        async with self.memory_lock:
            # Save episodic memory
            episodic_data = [item.to_dict() for item in self.episodic_memory.values()]
            await self.state_manager.update_state("memory", f"{self.agent_id}_episodic", episodic_data)
            
            # Save semantic memory
            semantic_data = [item.to_dict() for item in self.semantic_memory.values()]
            await self.state_manager.update_state("memory", f"{self.agent_id}_semantic", semantic_data)
            
            # Save procedural memory
            procedural_data = [item.to_dict() for item in self.procedural_memory.values()]
            await self.state_manager.update_state("memory", f"{self.agent_id}_procedural", procedural_data)
            
            # Save embeddings
            embeddings_data = {
                "episodic": {k: v.tolist() for k, v in self.episodic_embeddings.items()},
                "semantic": {k: v.tolist() for k, v in self.semantic_embeddings.items()},
            }
            await self.state_manager.update_state("memory", f"{self.agent_id}_embeddings", embeddings_data)
    
    async def add_memory(
        self,
        content: Any,
        memory_type: str = "episodic",
        source: str = "unknown",
        importance: float = 0.5,
        embedding: Optional[np.ndarray] = None,
        metadata: Optional[Dict] = None,
    ) -> str:
        """
        Add a memory item.
        
        Args:
            content (Any): Memory content
            memory_type (str): Type of memory (episodic, semantic, procedural)
            source (str): Source of the memory
            importance (float): Importance score (0.0 to 1.0)
            embedding (Optional[np.ndarray]): Vector embedding of the memory
            metadata (Optional[Dict]): Additional metadata
            
        Returns:
            str: Memory item ID
        """
        # Create memory item
        item = MemoryItem(
            content=content,
            memory_type=memory_type,
            source=source,
            importance=importance,
            embedding=embedding,
            metadata=metadata,
        )
        
        async with self.memory_lock:
            # Add to appropriate memory store
            if memory_type == "episodic":
                self.episodic_memory[item.id] = item
                if embedding is not None:
                    self.episodic_embeddings[item.id] = embedding
            elif memory_type == "semantic":
                self.semantic_memory[item.id] = item
                if embedding is not None:
                    self.semantic_embeddings[item.id] = embedding
            elif memory_type == "procedural":
                self.procedural_memory[item.id] = item
            else:
                raise ValueError(f"Unknown memory type: {memory_type}")
            
            # Save memory
            await self._save_memory()
        
        return item.id
    
    async def get_memory(self, memory_id: str) -> Optional[MemoryItem]:
        """
        Get a memory item by ID.
        
        Args:
            memory_id (str): Memory item ID
            
        Returns:
            Optional[MemoryItem]: Memory item if found, None otherwise
        """
        async with self.memory_lock:
            # Check episodic memory
            if memory_id in self.episodic_memory:
                item = self.episodic_memory[memory_id]
                item.access()
                return item
            
            # Check semantic memory
            if memory_id in self.semantic_memory:
                item = self.semantic_memory[memory_id]
                item.access()
                return item
            
            # Check procedural memory
            if memory_id in self.procedural_memory:
                item = self.procedural_memory[memory_id]
                item.access()
                return item
        
        return None
    
    async def search_memory(
        self,
        query: str,
        memory_type: Optional[str] = None,
        embedding: Optional[np.ndarray] = None,
        max_results: int = 10,
        min_similarity: float = 0.7,
    ) -> List[Tuple[MemoryItem, float]]:
        """
        Search memory items.
        
        Args:
            query (str): Search query
            memory_type (Optional[str]): Type of memory to search
            embedding (Optional[np.ndarray]): Query embedding for vector search
            max_results (int): Maximum number of results
            min_similarity (float): Minimum similarity score
            
        Returns:
            List[Tuple[MemoryItem, float]]: List of memory items with similarity scores
        """
        results = []
        
        async with self.memory_lock:
            if memory_type is None or memory_type == "episodic":
                # Search episodic memory
                if embedding is not None and self.episodic_embeddings:
                    # Vector search
                    for item_id, item_embedding in self.episodic_embeddings.items():
                        similarity = self._cosine_similarity(embedding, item_embedding)
                        if similarity >= min_similarity:
                            results.append((self.episodic_memory[item_id], similarity))
                else:
                    # Text search
                    for item in self.episodic_memory.values():
                        similarity = self._text_similarity(query, str(item.content))
                        if similarity >= min_similarity:
                            results.append((item, similarity))
            
            if memory_type is None or memory_type == "semantic":
                # Search semantic memory
                if embedding is not None and self.semantic_embeddings:
                    # Vector search
                    for item_id, item_embedding in self.semantic_embeddings.items():
                        similarity = self._cosine_similarity(embedding, item_embedding)
                        if similarity >= min_similarity:
                            results.append((self.semantic_memory[item_id], similarity))
                else:
                    # Text search
                    for item in self.semantic_memory.values():
                        similarity = self._text_similarity(query, str(item.content))
                        if similarity >= min_similarity:
                            results.append((item, similarity))
            
            if memory_type is None or memory_type == "procedural":
                # Search procedural memory
                for item in self.procedural_memory.values():
                    similarity = self._text_similarity(query, str(item.content))
                    if similarity >= min_similarity:
                        results.append((item, similarity))
        
        # Sort by similarity and limit results
        results.sort(key=lambda x: x[1], reverse=True)
        results = results[:max_results]
        
        # Record access
        for item, _ in results:
            item.access()
        
        return results
    
    def _cosine_similarity(self, a: np.ndarray, b: np.ndarray) -> float:
        """Calculate cosine similarity between two vectors."""
        return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))
    
    def _text_similarity(self, a: str, b: str) -> float:
        """Calculate simple text similarity."""
        # Simple implementation - in practice, use more sophisticated methods
        a_words = set(a.lower().split())
        b_words = set(b.lower().split())
        
        if not a_words or not b_words:
            return 0.0
        
        intersection = len(a_words.intersection(b_words))
        union = len(a_words.union(b_words))
        
        return intersection / union if union > 0 else 0.0
    
    async def _consolidation_loop(self):
        """Memory consolidation loop."""
        while True:
            # Wait for consolidation interval
            await asyncio.sleep(self.consolidation_interval)
            
            # Perform consolidation
            await self._consolidate_memory()
    
    async def _consolidate_memory(self):
        """Consolidate memory."""
        logger.info(f"Consolidating memory for agent {self.agent_id}")
        
        async with self.memory_lock:
            # Identify important episodic memories to convert to semantic
            candidates = []
            for item in self.episodic_memory.values():
                # Consider memories that are important and accessed frequently
                score = item.importance * (1 + 0.1 * item.access_count)
                if score > 0.8:
                    candidates.append((item, score))
            
            # Sort by score and select top candidates
            candidates.sort(key=lambda x: x[1], reverse=True)
            candidates = candidates[:10]  # Limit to 10 candidates per consolidation
            
            # Convert selected episodic memories to semantic
            for item, _ in candidates:
                # Create semantic memory from episodic memory
                semantic_content = item.content
                semantic_metadata = item.metadata.copy()
                semantic_metadata["derived_from"] = item.id
                
                # Add to semantic memory
                await self.add_memory(
                    content=semantic_content,
                    memory_type="semantic",
                    source=f"consolidation:{item.source}",
                    importance=item.importance,
                    embedding=item.embedding,
                    metadata=semantic_metadata,
                )
            
            # Prune old, unimportant episodic memories if over limit
            if len(self.episodic_memory) > self.max_memory_items:
                # Calculate retention scores
                retention_scores = []
                for item_id, item in self.episodic_memory.items():
                    # Score based on recency, importance, and access count
                    age = (datetime.now() - item.timestamp).total_seconds() / 86400  # age in days
                    recency = 1.0 / (1.0 + age)
                    score = 0.5 * item.importance + 0.3 * recency + 0.2 * min(1.0, item.access_count / 10)
                    retention_scores.append((item_id, score))
                
                # Sort by score (ascending) and remove lowest scoring items
                retention_scores.sort(key=lambda x: x[1])
                to_remove = len(self.episodic_memory) - self.max_memory_items
                for item_id, _ in retention_scores[:to_remove]:
                    del self.episodic_memory[item_id]
                    if item_id in self.episodic_embeddings:
                        del self.episodic_embeddings[item_id]
            
            # Save memory after consolidation
            await self._save_memory()
        
        self.last_consolidation = datetime.now()
        logger.info(f"Memory consolidation completed for agent {self.agent_id}")
