"""
UI-TARS Agent for the Multi-Agent AI System.

This module provides an agent that leverages UI-TARS 1.5 to control
browsers and desktop applications autonomously.
"""
import os
import sys
import json
import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import uuid

from agents.base_agent import BaseAgent
from core.logger import setup_logger
from ui_tars.connector.ui_tars_connector import UITarsConnector
from llm.llm_router import LLMRouter

class UITarsAgent(BaseAgent):
    """
    Agent for UI-TARS 1.5.
    
    This agent leverages UI-TARS 1.5 to control browsers and desktop
    applications autonomously.
    """
    
    def __init__(self, 
                 agent_id: str,
                 config: Dict,
                 state_manager: Any,
                 message_queue: asyncio.Queue,
                 shutdown_event: asyncio.Event,
                 llm_router: Optional[LLMRouter] = None):
        """
        Initialize the UI-TARS agent.
        
        Args:
            agent_id (str): Unique identifier for the agent
            config (Dict): Configuration for the agent
            state_manager (Any): State manager for the agent
            message_queue (asyncio.Queue): Message queue for the agent
            shutdown_event (asyncio.Event): Event to signal shutdown
            llm_router (Optional[LLMRouter]): LLM router for the agent
        """
        super().__init__(
            agent_id=agent_id,
            config=config,
            state_manager=state_manager,
            message_queue=message_queue,
            shutdown_event=shutdown_event
        )
        
        self.llm_router = llm_router
        self.connector = None
        self.api_url = config.get("api_url")
        self.api_key = config.get("api_key")
        self.model_name = config.get("model_name", "UI-TARS-1.5-7B")
        self.installation_path = config.get("installation_path")
        self.auto_start = config.get("auto_start", False)
        self.browser_type = config.get("browser_type", "chrome")
        self.voice_commands_enabled = config.get("voice_commands_enabled", False)
        self.nvidia_acceleration = config.get("nvidia_acceleration", False)
        self.autonomous_mode = config.get("autonomous_mode", False)
        self.current_task = None
        self.task_history = []
        
        # Set up logger
        self.logger = setup_logger(f"agent.ui_tars")
    
    async def initialize(self):
        """Initialize the UI-TARS agent."""
        self.logger.info("Initializing UI-TARS agent")
        
        # Initialize base agent
        await super().initialize()
        
        # Create UI-TARS connector
        self.connector = UITarsConnector(
            api_url=self.api_url,
            api_key=self.api_key,
            model_name=self.model_name,
            installation_path=self.installation_path
        )
        
        # Initialize connector
        success = await self.connector.initialize()
        if not success:
            self.logger.warning("Failed to initialize UI-TARS connector")
            return False
        
        # Start UI-TARS if auto-start is enabled
        if self.auto_start:
            success = await self.connector.start()
            if not success:
                self.logger.warning("Failed to start UI-TARS")
                return False
        
        # Register capabilities
        self._register_capabilities()
        
        self.logger.info("UI-TARS agent initialized")
        return True
    
    def _register_capabilities(self):
        """Register the agent's capabilities."""
        self.capabilities = [
            {
                "name": "start_ui_tars",
                "description": "Start the UI-TARS application",
                "parameters": {}
            },
            {
                "name": "stop_ui_tars",
                "description": "Stop the UI-TARS application",
                "parameters": {}
            },
            {
                "name": "execute_command",
                "description": "Execute a command in UI-TARS",
                "parameters": {
                    "command": {
                        "type": "string",
                        "description": "Command to execute"
                    },
                    "screenshot": {
                        "type": "boolean",
                        "description": "Whether to include a screenshot in the response",
                        "default": True
                    }
                }
            },
            {
                "name": "take_screenshot",
                "description": "Take a screenshot using UI-TARS",
                "parameters": {}
            },
            {
                "name": "browse_website",
                "description": "Browse a website using UI-TARS",
                "parameters": {
                    "url": {
                        "type": "string",
                        "description": "URL to browse"
                    },
                    "task": {
                        "type": "string",
                        "description": "Task to perform on the website",
                        "default": "browse"
                    }
                }
            },
            {
                "name": "search_web",
                "description": "Search the web using UI-TARS",
                "parameters": {
                    "query": {
                        "type": "string",
                        "description": "Search query"
                    },
                    "engine": {
                        "type": "string",
                        "description": "Search engine to use",
                        "default": "google"
                    }
                }
            },
            {
                "name": "start_autonomous_mode",
                "description": "Start autonomous mode",
                "parameters": {
                    "task": {
                        "type": "string",
                        "description": "Task to perform autonomously"
                    }
                }
            },
            {
                "name": "stop_autonomous_mode",
                "description": "Stop autonomous mode",
                "parameters": {}
            }
        ]
    
    async def execute_cycle(self):
        """Execute the agent's main cycle."""
        # Check if there are any messages in the queue
        try:
            message = self.message_queue.get_nowait()
            await self._process_message(message)
            self.message_queue.task_done()
        except asyncio.QueueEmpty:
            pass
        
        # If in autonomous mode, continue the current task
        if self.autonomous_mode and self.current_task:
            await self._continue_autonomous_task()
    
    async def _process_message(self, message: Dict):
        """
        Process a message from the message queue.
        
        Args:
            message (Dict): Message to process
        """
        self.logger.info(f"Processing message: {message}")
        
        # Extract message details
        message_id = message.get("id", str(uuid.uuid4()))
        sender = message.get("sender", "unknown")
        content = message.get("content", {})
        
        # Check if the message is a capability request
        if "capability" in content:
            capability = content["capability"]
            parameters = content.get("parameters", {})
            
            # Execute the capability
            result = await self.execute(capability, parameters)
            
            # Send the response
            response = {
                "id": str(uuid.uuid4()),
                "reply_to": message_id,
                "sender": self.agent_id,
                "recipient": sender,
                "content": {
                    "result": result
                },
                "timestamp": datetime.now().isoformat()
            }
            
            # Add the response to the message queue
            await self._send_message(response)
    
    async def start_ui_tars(self):
        """
        Start the UI-TARS application.
        
        Returns:
            Dict: Result of the operation
        """
        self.logger.info("Starting UI-TARS")
        
        if not self.connector:
            return {"success": False, "error": "Connector not initialized"}
        
        success = await self.connector.start()
        
        return {"success": success}
    
    async def stop_ui_tars(self):
        """
        Stop the UI-TARS application.
        
        Returns:
            Dict: Result of the operation
        """
        self.logger.info("Stopping UI-TARS")
        
        if not self.connector:
            return {"success": False, "error": "Connector not initialized"}
        
        success = await self.connector.stop()
        
        return {"success": success}
    
    async def execute_command(self, command: str, screenshot: bool = True):
        """
        Execute a command in UI-TARS.
        
        Args:
            command (str): Command to execute
            screenshot (bool): Whether to include a screenshot in the response
            
        Returns:
            Dict: Result of the operation
        """
        self.logger.info(f"Executing command: {command}")
        
        if not self.connector:
            return {"success": False, "error": "Connector not initialized"}
        
        result = await self.connector.execute_command(command, screenshot)
        
        return result
    
    async def take_screenshot(self):
        """
        Take a screenshot using UI-TARS.
        
        Returns:
            Dict: Result of the operation
        """
        self.logger.info("Taking screenshot")
        
        if not self.connector:
            return {"success": False, "error": "Connector not initialized"}
        
        result = await self.connector.take_screenshot()
        
        return result
    
    async def browse_website(self, url: str, task: str = "browse"):
        """
        Browse a website using UI-TARS.
        
        Args:
            url (str): URL to browse
            task (str): Task to perform on the website
            
        Returns:
            Dict: Result of the operation
        """
        self.logger.info(f"Browsing website: {url}, task: {task}")
        
        if not self.connector:
            return {"success": False, "error": "Connector not initialized"}
        
        # Construct the command
        command = f"Browse to {url} and {task}"
        
        result = await self.connector.execute_command(command)
        
        return result
    
    async def search_web(self, query: str, engine: str = "google"):
        """
        Search the web using UI-TARS.
        
        Args:
            query (str): Search query
            engine (str): Search engine to use
            
        Returns:
            Dict: Result of the operation
        """
        self.logger.info(f"Searching web: {query}, engine: {engine}")
        
        if not self.connector:
            return {"success": False, "error": "Connector not initialized"}
        
        # Construct the command
        command = f"Search for {query} using {engine}"
        
        result = await self.connector.execute_command(command)
        
        return result
    
    async def start_autonomous_mode(self, task: str):
        """
        Start autonomous mode.
        
        Args:
            task (str): Task to perform autonomously
            
        Returns:
            Dict: Result of the operation
        """
        self.logger.info(f"Starting autonomous mode: {task}")
        
        if not self.connector:
            return {"success": False, "error": "Connector not initialized"}
        
        # Set autonomous mode
        self.autonomous_mode = True
        self.current_task = task
        
        # Add task to history
        self.task_history.append({
            "task": task,
            "start_time": datetime.now().isoformat(),
            "status": "running"
        })
        
        # Start the task
        command = f"Autonomously {task}"
        result = await self.connector.execute_command(command)
        
        return {
            "success": True,
            "message": f"Autonomous mode started: {task}",
            "result": result
        }
    
    async def stop_autonomous_mode(self):
        """
        Stop autonomous mode.
        
        Returns:
            Dict: Result of the operation
        """
        self.logger.info("Stopping autonomous mode")
        
        # Set autonomous mode
        self.autonomous_mode = False
        
        # Update task history
        if self.current_task:
            for task in reversed(self.task_history):
                if task["status"] == "running":
                    task["status"] = "stopped"
                    task["end_time"] = datetime.now().isoformat()
                    break
        
        self.current_task = None
        
        return {
            "success": True,
            "message": "Autonomous mode stopped"
        }
    
    async def _continue_autonomous_task(self):
        """Continue the current autonomous task."""
        self.logger.info(f"Continuing autonomous task: {self.current_task}")
        
        # Check if the task is still valid
        if not self.current_task:
            self.autonomous_mode = False
            return
        
        # Take a screenshot to see the current state
        screenshot_result = await self.connector.take_screenshot()
        
        # If there's an error, log it and continue
        if "error" in screenshot_result:
            self.logger.warning(f"Error taking screenshot: {screenshot_result['error']}")
            return
        
        # Use LLM to decide the next action
        if self.llm_router:
            # Prepare the prompt
            prompt = f"""
            You are controlling a browser autonomously to complete the task: {self.current_task}
            
            Based on the current screenshot, what action should be taken next?
            
            Respond with a specific command to execute in UI-TARS.
            """
            
            # Get the next action from the LLM
            llm_response = await self.llm_router.generate_text(
                prompt=prompt,
                system_prompt="You are an AI assistant helping to control a browser autonomously.",
                model="gpt-4o",
                max_tokens=100
            )
            
            # Extract the command from the LLM response
            command = llm_response.strip()
            
            # Execute the command
            await self.connector.execute_command(command)
        
        # Wait before continuing
        await asyncio.sleep(5)
    
    async def shutdown(self):
        """Shut down the UI-TARS agent."""
        self.logger.info("Shutting down UI-TARS agent")
        
        # Stop autonomous mode if active
        if self.autonomous_mode:
            await self.stop_autonomous_mode()
        
        # Stop UI-TARS if running
        if self.connector:
            await self.connector.stop()
            await self.connector.close()
        
        # Shut down base agent
        await super().shutdown()
        
        self.logger.info("UI-TARS agent shut down")
