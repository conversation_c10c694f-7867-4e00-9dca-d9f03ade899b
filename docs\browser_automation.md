# Browser Automation Integration

This document provides information about the browser automation integration for the multi-agent AI system.

## Overview

The browser automation integration provides a unified interface for browser automation using either UI-TARS or Midscene. It includes automatic fallback between providers when one fails, health monitoring, and auto-repair functionality.

## Components

The browser automation integration consists of the following components:

1. **Browser Automation Manager**: A unified interface for browser automation that can use either UI-TARS or Midscene.
2. **Gmail Agent**: A specialized agent for Gmail automation.
3. **Google Voice Agent**: A specialized agent for Google Voice automation.
4. **Browser Automation Service**: A service that manages the browser automation manager and provides a high-level API for the AI agent system.
5. **Command-Line Interface**: A command-line interface for the browser automation service.
6. **Windows Service**: A Windows service for the browser automation service.

## Installation

### Prerequisites

- Python 3.8 or higher
- Google Chrome or Microsoft Edge
- UI-TARS 1.5 or higher
- Midscene 0.16.0 or higher (optional)

### Installation Steps

1. Clone the repository:
   ```
   git clone https://github.com/Flofactionllc/PaulEdwardsAI.git
   cd PaulEdwardsAI
   ```

2. Install the required Python packages:
   ```
   pip install -r requirements.txt
   ```

3. Install UI-TARS:
   - Download UI-TARS from the official website
   - Install UI-TARS according to the instructions
   - Configure UI-TARS to expose the API on port 8080

4. Install Midscene (optional):
   ```
   npm install -g @midscene/web @midscene/android @midscene/core @midscene/cli
   ```

5. Configure the browser automation integration:
   - Edit the `config/browser_automation_config.json` file
   - Set the preferred provider, API URLs, and other settings

6. Install the browser automation service as a Windows service (optional):
   - Download NSSM from http://nssm.cc/
   - Place the `nssm.exe` file in the `startup` directory
   - Run the `startup/install_service.bat` file as administrator

## Configuration

The browser automation integration is configured using the `config/browser_automation_config.json` file. The following settings are available:

### Browser Automation Settings

- `preferred_provider`: The preferred automation provider (`ui_tars`, `midscene`, or `auto`).
- `auto_start`: Whether to automatically start the provider.
- `auto_restart`: Whether to automatically restart the provider on failure.
- `auto_fallback`: Whether to automatically fallback to another provider when one fails.
- `health_check_interval`: The interval in seconds between health checks.
- `max_retries`: The maximum number of retries for commands.
- `retry_delay`: The delay in seconds between retries.

### UI-TARS Settings

- `api_url`: The URL of the UI-TARS API.
- `api_key`: The API key for UI-TARS.
- `model_name`: The name of the model to use.
- `browser_type`: The type of browser to use.
- `browser_path`: The path to the browser executable.
- `remote_debugging_port`: The port for browser remote debugging.

### Midscene Settings

- `api_url`: The URL of the Midscene API.
- `api_key`: The API key for Midscene.
- `browser_type`: The type of browser to use.
- `browser_path`: The path to the browser executable.

### Gmail Settings

- `email`: The Gmail email address.
- `password`: The Gmail password.
- `signature`: The email signature.

### Google Voice Settings

- `email`: The Google account email address.
- `password`: The Google account password.
- `phone_number`: The Google Voice phone number.

### Logging Settings

- `level`: The logging level.
- `file`: The log file path.
- `max_size`: The maximum log file size in bytes.
- `backup_count`: The number of backup log files to keep.

## Usage

### Command-Line Interface

The browser automation integration provides a command-line interface for executing commands, sending emails, sending text messages, making calls, and performing health checks.

#### Interactive Mode

```
browser_automation.bat --interactive
```

This will start the command-line interface in interactive mode, where you can enter commands interactively.

#### Execute Command

```
browser_automation.bat execute "Browse to https://www.google.com"
```

This will execute a browser automation command.

#### Send Email

```
browser_automation.<NAME_EMAIL> "Subject" "Body"
```

This will send an email using the Gmail agent.

#### Send Text Message

```
browser_automation.bat text ********** "Message"
```

This will send a text message using the Google Voice agent.

#### Make Call

```
browser_automation.bat call **********
```

This will make a phone call using the Google Voice agent.

#### Health Check

```
browser_automation.bat health --repair
```

This will perform a health check on the browser automation manager and attempt to repair it if it's unhealthy.

### API

The browser automation integration provides a high-level API for the AI agent system.

#### Browser Automation Service

```python
from services.browser_automation_service import get_browser_automation_service

# Get the browser automation service
service = get_browser_automation_service()

# Initialize the service
await service.initialize()

# Execute a command
result = await service.execute_command("Browse to https://www.google.com")

# Send an email
success = await service.send_email("<EMAIL>", "Subject", "Body")

# Send a text message
success = await service.send_text_message("**********", "Message")

# Make a call
success = await service.make_call("**********")

# Perform a health check
health = await service.health_check()

# Attempt to repair
repair_result = await service.auto_repair()

# Stop the service
await service.stop()
```

#### Gmail Agent

```python
from agents.gmail_agent import GmailAgent

# Create a Gmail agent
gmail_agent = GmailAgent(
    email="<EMAIL>",
    password="your_password"
)

# Initialize the agent
await gmail_agent.initialize()

# Login to Gmail
success = await gmail_agent.login()

# Compose an email
success = await gmail_agent.compose_email(
    to="<EMAIL>",
    subject="Subject",
    body="Body"
)

# Send the email
success = await gmail_agent.send_email()

# Check the inbox
emails = await gmail_agent.check_inbox()

# Logout
success = await gmail_agent.logout()

# Stop the agent
await gmail_agent.stop()
```

#### Google Voice Agent

```python
from agents.google_voice_agent import GoogleVoiceAgent

# Create a Google Voice agent
google_voice_agent = GoogleVoiceAgent(
    email="<EMAIL>",
    password="your_password",
    phone_number="your_phone_number"
)

# Initialize the agent
await google_voice_agent.initialize()

# Login to Google Voice
success = await google_voice_agent.login()

# Send a text message
success = await google_voice_agent.send_text_message(
    to="**********",
    message="Message"
)

# Make a call
success = await google_voice_agent.make_call("**********")

# End the call
success = await google_voice_agent.end_call()

# Check messages
messages = await google_voice_agent.check_messages()

# Logout
success = await google_voice_agent.logout()

# Stop the agent
await google_voice_agent.stop()
```

## Troubleshooting

### UI-TARS Issues

- **UI-TARS API not running**: Make sure UI-TARS is running and the API is exposed on the configured port.
- **Browser remote debugging not active**: Make sure the browser is running with remote debugging enabled.
- **UI-TARS not detecting browser**: Make sure the browser is running with remote debugging enabled on the configured port.

### Midscene Issues

- **Midscene API not running**: Make sure Midscene is running and the API is exposed on the configured port.
- **Midscene not detecting browser**: Make sure the browser is running and Midscene is configured to use it.

### Gmail Issues

- **Login failed**: Make sure the email and password are correct and that less secure app access is enabled.
- **Compose email failed**: Make sure the Gmail interface hasn't changed and the agent is clicking the correct elements.

### Google Voice Issues

- **Login failed**: Make sure the email and password are correct.
- **Send text message failed**: Make sure the Google Voice interface hasn't changed and the agent is clicking the correct elements.
- **Make call failed**: Make sure the Google Voice interface hasn't changed and the agent is clicking the correct elements.

## Support

For support, please contact Paul <NAME_EMAIL>.
