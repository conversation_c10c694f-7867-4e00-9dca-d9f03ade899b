"""
Tool Executor for running external security tools.

This module provides utilities for securely executing external command-line tools
and processing their output.
"""
import asyncio
import os
import subprocess
import shlex
import sys
import json
from typing import Dict, List, Optional, Any, Union, Tuple
from pathlib import Path
import tempfile
import shutil
import logging
import re

from core.logger import setup_logger

# Set up logger
logger = setup_logger("utils.tool_executor")

class ToolExecutor:
    """
    Utility for securely executing external tools.
    
    This class provides methods for running external command-line tools
    in a controlled environment and processing their output.
    """
    
    def __init__(self, tools_dir: Optional[Path] = None):
        """
        Initialize the tool executor.
        
        Args:
            tools_dir (Optional[Path]): Directory for tool installations
        """
        # Set tools directory
        if tools_dir:
            self.tools_dir = tools_dir
        else:
            self.tools_dir = Path(__file__).resolve().parent.parent / "tools"
        
        # Create tools directory if it doesn't exist
        self.tools_dir.mkdir(exist_ok=True)
        
        # Set environment variables
        self.env = os.environ.copy()
        
        # Add tools directory to PATH
        if str(self.tools_dir) not in self.env.get("PATH", ""):
            self.env["PATH"] = f"{self.tools_dir}{os.pathsep}{self.env.get('PATH', '')}"
    
    async def run_command(
        self,
        command: Union[str, List[str]],
        cwd: Optional[Path] = None,
        env: Optional[Dict[str, str]] = None,
        timeout: Optional[float] = 300,
        capture_output: bool = True,
        check: bool = True,
        shell: bool = False,
    ) -> Dict[str, Any]:
        """
        Run a command asynchronously.
        
        Args:
            command (Union[str, List[str]]): Command to run
            cwd (Optional[Path]): Working directory
            env (Optional[Dict[str, str]]): Environment variables
            timeout (Optional[float]): Timeout in seconds
            capture_output (bool): Whether to capture stdout and stderr
            check (bool): Whether to raise an exception on non-zero exit code
            shell (bool): Whether to run the command in a shell
            
        Returns:
            Dict[str, Any]: Command result with stdout, stderr, and return code
        """
        # Prepare command
        if isinstance(command, str) and not shell:
            command = shlex.split(command)
        
        # Prepare environment
        if env:
            cmd_env = self.env.copy()
            cmd_env.update(env)
        else:
            cmd_env = self.env
        
        # Log command
        cmd_str = command if isinstance(command, str) else " ".join(command)
        logger.info(f"Running command: {cmd_str}")
        
        # Create subprocess
        process = await asyncio.create_subprocess_shell(
            command if shell else " ".join(command),
            stdout=asyncio.subprocess.PIPE if capture_output else None,
            stderr=asyncio.subprocess.PIPE if capture_output else None,
            cwd=cwd,
            env=cmd_env,
            shell=shell,
        )
        
        try:
            # Wait for process to complete with timeout
            stdout_data, stderr_data = await asyncio.wait_for(
                process.communicate(), timeout=timeout
            )
            
            # Decode output
            stdout = stdout_data.decode("utf-8", errors="replace") if stdout_data else ""
            stderr = stderr_data.decode("utf-8", errors="replace") if stderr_data else ""
            
            # Check return code
            if check and process.returncode != 0:
                logger.error(f"Command failed with exit code {process.returncode}: {cmd_str}")
                logger.error(f"stderr: {stderr}")
                raise subprocess.CalledProcessError(
                    process.returncode, cmd_str, stdout, stderr
                )
            
            # Return result
            return {
                "stdout": stdout,
                "stderr": stderr,
                "returncode": process.returncode,
                "command": cmd_str,
            }
        
        except asyncio.TimeoutError:
            # Kill process on timeout
            try:
                process.kill()
            except ProcessLookupError:
                pass
            
            logger.error(f"Command timed out after {timeout} seconds: {cmd_str}")
            raise TimeoutError(f"Command timed out after {timeout} seconds: {cmd_str}")
    
    async def check_tool_installed(self, tool_name: str) -> bool:
        """
        Check if a tool is installed and available.
        
        Args:
            tool_name (str): Name of the tool
            
        Returns:
            bool: True if the tool is installed, False otherwise
        """
        try:
            # Try to run the tool with --version or -V
            for flag in ["--version", "-V", "-version", "-v", "--help", "-h"]:
                try:
                    result = await self.run_command(
                        f"{tool_name} {flag}",
                        capture_output=True,
                        check=False,
                        shell=True,
                        timeout=10,
                    )
                    if result["returncode"] == 0:
                        logger.info(f"Tool {tool_name} is installed")
                        return True
                except Exception:
                    continue
            
            logger.warning(f"Tool {tool_name} is not installed or not working properly")
            return False
        
        except Exception as e:
            logger.error(f"Error checking if {tool_name} is installed: {e}")
            return False
    
    async def install_tool(self, tool_name: str, method: str = "auto") -> bool:
        """
        Install a tool.
        
        Args:
            tool_name (str): Name of the tool
            method (str): Installation method (auto, pip, apt, download)
            
        Returns:
            bool: True if the tool was installed successfully, False otherwise
        """
        logger.info(f"Installing tool: {tool_name} using method: {method}")
        
        # Check if tool is already installed
        if await self.check_tool_installed(tool_name):
            logger.info(f"Tool {tool_name} is already installed")
            return True
        
        try:
            if method == "auto":
                # Try different installation methods
                for install_method in ["pip", "apt", "download"]:
                    if await self.install_tool(tool_name, install_method):
                        return True
                return False
            
            elif method == "pip":
                # Install using pip
                result = await self.run_command(
                    f"{sys.executable} -m pip install {tool_name}",
                    check=False,
                    shell=True,
                )
                return result["returncode"] == 0
            
            elif method == "apt":
                # Install using apt (for Debian/Ubuntu)
                result = await self.run_command(
                    f"apt-get update && apt-get install -y {tool_name}",
                    check=False,
                    shell=True,
                )
                return result["returncode"] == 0
            
            elif method == "download":
                # Custom download logic for specific tools
                # This would need to be implemented for each tool
                logger.error(f"Download method not implemented for {tool_name}")
                return False
            
            else:
                logger.error(f"Unknown installation method: {method}")
                return False
        
        except Exception as e:
            logger.error(f"Error installing {tool_name}: {e}")
            return False
    
    async def parse_tool_output(self, tool_name: str, output: str) -> Dict[str, Any]:
        """
        Parse tool output into a structured format.
        
        Args:
            tool_name (str): Name of the tool
            output (str): Tool output
            
        Returns:
            Dict[str, Any]: Parsed output
        """
        # This would need to be customized for each tool
        # For now, return a simple structure
        return {
            "tool": tool_name,
            "raw_output": output,
            "parsed": {},
        }
