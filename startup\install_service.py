"""
Install Browser Automation Service as a Windows Service.

This script installs the browser automation service as a Windows service
using the NSSM (Non-Sucking Service Manager) tool.
"""
import os
import sys
import subprocess
import argparse
from pathlib import Path

def install_service(service_name, display_name, description):
    """
    Install the browser automation service as a Windows service.
    
    Args:
        service_name (str): Service name
        display_name (str): Display name
        description (str): Description
        
    Returns:
        bool: True if successful, False otherwise
    """
    print(f"Installing service: {service_name}")
    
    # Get the path to the NSSM executable
    nssm_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "nssm.exe")
    
    # Check if NSSM exists
    if not os.path.exists(nssm_path):
        print(f"Error: NSSM not found at {nssm_path}")
        print("Please download NSSM from http://nssm.cc/ and place it in the same directory as this script")
        return False
    
    # Get the path to the Python executable
    python_path = sys.executable
    
    # Get the path to the startup script
    startup_script = os.path.join(os.path.dirname(os.path.abspath(__file__)), "start_browser_automation.py")
    
    # Get the working directory
    working_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        # Install the service
        subprocess.run([
            nssm_path, "install", service_name,
            python_path, startup_script
        ], check=True)
        
        # Set the display name
        subprocess.run([
            nssm_path, "set", service_name,
            "DisplayName", display_name
        ], check=True)
        
        # Set the description
        subprocess.run([
            nssm_path, "set", service_name,
            "Description", description
        ], check=True)
        
        # Set the working directory
        subprocess.run([
            nssm_path, "set", service_name,
            "AppDirectory", working_dir
        ], check=True)
        
        # Set the startup type to automatic
        subprocess.run([
            nssm_path, "set", service_name,
            "Start", "SERVICE_AUTO_START"
        ], check=True)
        
        # Set the restart delay to 30 seconds
        subprocess.run([
            nssm_path, "set", service_name,
            "AppRestartDelay", "30000"
        ], check=True)
        
        # Set the stdout and stderr log files
        log_dir = os.path.join(working_dir, "logs")
        os.makedirs(log_dir, exist_ok=True)
        
        subprocess.run([
            nssm_path, "set", service_name,
            "AppStdout", os.path.join(log_dir, f"{service_name}_stdout.log")
        ], check=True)
        
        subprocess.run([
            nssm_path, "set", service_name,
            "AppStderr", os.path.join(log_dir, f"{service_name}_stderr.log")
        ], check=True)
        
        print(f"Service {service_name} installed successfully")
        print(f"You can start the service with: net start {service_name}")
        return True
    
    except subprocess.CalledProcessError as e:
        print(f"Error installing service: {e}")
        return False

def uninstall_service(service_name):
    """
    Uninstall the browser automation service.
    
    Args:
        service_name (str): Service name
        
    Returns:
        bool: True if successful, False otherwise
    """
    print(f"Uninstalling service: {service_name}")
    
    # Get the path to the NSSM executable
    nssm_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "nssm.exe")
    
    # Check if NSSM exists
    if not os.path.exists(nssm_path):
        print(f"Error: NSSM not found at {nssm_path}")
        print("Please download NSSM from http://nssm.cc/ and place it in the same directory as this script")
        return False
    
    try:
        # Stop the service if it's running
        subprocess.run([
            "net", "stop", service_name
        ], check=False)
        
        # Uninstall the service
        subprocess.run([
            nssm_path, "remove", service_name, "confirm"
        ], check=True)
        
        print(f"Service {service_name} uninstalled successfully")
        return True
    
    except subprocess.CalledProcessError as e:
        print(f"Error uninstalling service: {e}")
        return False

def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Install Browser Automation Service as a Windows Service")
    parser.add_argument("--uninstall", "-u", action="store_true", help="Uninstall the service")
    parser.add_argument("--service-name", "-n", default="BrowserAutomationService", help="Service name")
    parser.add_argument("--display-name", "-d", default="Browser Automation Service", help="Display name")
    parser.add_argument("--description", "-e", default="Browser Automation Service for the Multi-Agent AI System", help="Description")
    
    args = parser.parse_args()
    
    if args.uninstall:
        success = uninstall_service(args.service_name)
    else:
        success = install_service(args.service_name, args.display_name, args.description)
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
