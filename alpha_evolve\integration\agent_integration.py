"""
Agent Integration for AlphaEvolve.

This module provides integration between AlphaEvolve and the Multi-Agent AI System,
enabling evolutionary optimization of agent capabilities and behaviors.
"""
import asyncio
import json
import logging
import os
import importlib
import inspect
from typing import Dict, List, Optional, Any, Union, Tuple
import uuid
from datetime import datetime
import tempfile

# Add the project root to the Python path
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent.parent))

from core.logger import setup_logger
from core.agent_manager import AgentManager
from alpha_evolve.alpha_evolve_engine import AlphaEvolveEngine

# Set up logger
logger = setup_logger("agent_integration")

class AgentIntegration:
    """
    Agent Integration for AlphaEvolve.

    This class provides integration between AlphaEvolve and the Multi-Agent AI System,
    enabling evolutionary optimization of agent capabilities and behaviors.
    """

    def __init__(
        self,
        alpha_evolve_engine: Optional[AlphaEvolveEngine] = None,
        agent_manager: Optional[AgentManager] = None,
    ):
        """
        Initialize the Agent Integration.

        Args:
            alpha_evolve_engine (AlphaEvolveEngine, optional): AlphaEvolve engine
            agent_manager (AgentManager, optional): Agent manager
        """
        self.alpha_evolve_engine = alpha_evolve_engine
        self.agent_manager = agent_manager
        self.initialized = False
        
        # Integration state
        self.enhancement_tasks = {}
        self.capability_models = {}
        self.agent_capabilities = {}
        
    async def initialize(self):
        """Initialize the Agent Integration."""
        logger.info("Initializing Agent Integration")
        
        # Initialize AlphaEvolve engine if not provided
        if not self.alpha_evolve_engine:
            from alpha_evolve.alpha_evolve_engine import AlphaEvolveEngine
            self.alpha_evolve_engine = AlphaEvolveEngine()
            await self.alpha_evolve_engine.initialize()
        
        # Initialize agent manager if not provided
        if not self.agent_manager:
            from core.agent_manager import AgentManager
            self.agent_manager = AgentManager()
            await self.agent_manager.initialize()
        
        # Load agent capabilities
        await self._load_agent_capabilities()
        
        self.initialized = True
        logger.info("Agent Integration initialized")
    
    async def _load_agent_capabilities(self):
        """Load agent capabilities."""
        try:
            # Get all agents
            agents = self.agent_manager.agents
            
            for agent_id, agent in agents.items():
                # Get agent capabilities
                capabilities = []
                
                # Inspect agent methods
                for name, method in inspect.getmembers(agent, predicate=inspect.ismethod):
                    if name.startswith("_"):
                        continue
                    
                    # Add method as capability
                    capabilities.append({
                        "name": name,
                        "description": method.__doc__ or "",
                        "parameters": inspect.signature(method),
                    })
                
                # Store agent capabilities
                self.agent_capabilities[agent_id] = capabilities
                
                logger.info(f"Loaded {len(capabilities)} capabilities for agent {agent_id}")
        
        except Exception as e:
            logger.exception(f"Error loading agent capabilities: {e}")
    
    async def enhance_agent_capability(
        self,
        agent_id: str,
        capability: str,
        optimization_metric: str = "efficiency",
        generations: int = 40,
        population_size: int = 20,
    ) -> Dict:
        """
        Enhance an agent's capability using AlphaEvolve.
        
        Args:
            agent_id (str): Agent ID
            capability (str): Capability to enhance
            optimization_metric (str, optional): Metric to optimize
            generations (int, optional): Number of generations
            population_size (int, optional): Population size
            
        Returns:
            Dict: Enhancement results
        """
        if not self.initialized:
            await self.initialize()
        
        # Check if agent exists
        if agent_id not in self.agent_manager.agents:
            return {
                "status": "error",
                "error": f"Agent not found: {agent_id}",
            }
        
        # Get agent
        agent = self.agent_manager.agents[agent_id]
        
        # Check if capability exists
        if not hasattr(agent, capability):
            return {
                "status": "error",
                "error": f"Capability not found: {capability}",
            }
        
        # Get current implementation
        current_implementation = getattr(agent, capability)
        
        # Get source code if possible
        try:
            source_code = inspect.getsource(current_implementation)
        except Exception:
            source_code = None
        
        # Create task ID
        task_id = str(uuid.uuid4())
        
        # Create enhancement task
        self.enhancement_tasks[task_id] = {
            "id": task_id,
            "agent_id": agent_id,
            "capability": capability,
            "optimization_metric": optimization_metric,
            "status": "running",
            "start_time": datetime.now().isoformat(),
            "end_time": None,
            "result": None,
        }
        
        try:
            # Create problem definition
            problem = {
                "type": "agent_enhancement",
                "agent_id": agent_id,
                "capability": capability,
                "optimization_metric": optimization_metric,
            }
            
            # Run evolution
            if source_code:
                # Optimize existing implementation
                result = await self.alpha_evolve_engine.optimize_existing_code(
                    code=source_code,
                    problem=problem,
                    generations=generations,
                    population_size=population_size,
                )
            else:
                # Discover new implementation
                result = await self.alpha_evolve_engine.evolve(
                    problem=problem,
                    generations=generations,
                    population_size=population_size,
                )
            
            # Update task
            self.enhancement_tasks[task_id]["status"] = "completed"
            self.enhancement_tasks[task_id]["end_time"] = datetime.now().isoformat()
            self.enhancement_tasks[task_id]["result"] = result
            
            # Store capability model
            if result.get("best_solution"):
                model_id = str(uuid.uuid4())
                
                self.capability_models[model_id] = {
                    "id": model_id,
                    "agent_id": agent_id,
                    "capability": capability,
                    "code": result["best_solution"]["code"],
                    "fitness": result["best_fitness"],
                    "timestamp": datetime.now().isoformat(),
                }
                
                # Update task with model ID
                self.enhancement_tasks[task_id]["model_id"] = model_id
            
            return {
                "status": "completed",
                "task_id": task_id,
                "agent_id": agent_id,
                "capability": capability,
                "optimization_metric": optimization_metric,
                "evolution_result": result,
                "model_id": self.enhancement_tasks[task_id].get("model_id"),
            }
        
        except Exception as e:
            logger.exception(f"Error enhancing agent capability: {e}")
            
            # Update task
            self.enhancement_tasks[task_id]["status"] = "error"
            self.enhancement_tasks[task_id]["end_time"] = datetime.now().isoformat()
            self.enhancement_tasks[task_id]["error"] = str(e)
            
            return {
                "status": "error",
                "task_id": task_id,
                "error": str(e),
            }
    
    async def apply_capability_model(self, model_id: str) -> Dict:
        """
        Apply a capability model to an agent.
        
        Args:
            model_id (str): Model ID
            
        Returns:
            Dict: Application results
        """
        if not self.initialized:
            await self.initialize()
        
        if model_id not in self.capability_models:
            return {
                "status": "error",
                "error": f"Model not found: {model_id}",
            }
        
        try:
            # Get model
            model = self.capability_models[model_id]
            
            # Get agent
            agent_id = model["agent_id"]
            capability = model["capability"]
            
            if agent_id not in self.agent_manager.agents:
                return {
                    "status": "error",
                    "error": f"Agent not found: {agent_id}",
                }
            
            agent = self.agent_manager.agents[agent_id]
            
            # Create temporary module
            with tempfile.NamedTemporaryFile(suffix=".py", delete=False) as f:
                f.write(model["code"].encode())
                module_path = f.name
            
            try:
                # Import module
                spec = importlib.util.spec_from_file_location("capability_model", module_path)
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                
                # Find function to use
                function_name = None
                
                # Try to find a function with the same name as the capability
                if hasattr(module, capability):
                    function_name = capability
                
                # Try to find a function named 'main', 'run', or 'execute'
                if not function_name:
                    for name in ["main", "run", "execute"]:
                        if hasattr(module, name):
                            function_name = name
                            break
                
                # If still not found, use the first function defined in the module
                if not function_name:
                    for name in dir(module):
                        if not name.startswith("_") and callable(getattr(module, name)):
                            function_name = name
                            break
                
                if not function_name:
                    raise ValueError("No suitable function found in the model")
                
                # Get function
                function = getattr(module, function_name)
                
                # Apply function to agent
                setattr(agent, capability, function)
                
                # Update agent capabilities
                if agent_id in self.agent_capabilities:
                    for i, cap in enumerate(self.agent_capabilities[agent_id]):
                        if cap["name"] == capability:
                            self.agent_capabilities[agent_id][i] = {
                                "name": capability,
                                "description": function.__doc__ or "",
                                "parameters": inspect.signature(function),
                            }
                            break
                
                return {
                    "status": "success",
                    "agent_id": agent_id,
                    "capability": capability,
                    "function_name": function_name,
                }
            
            finally:
                # Clean up temporary file
                try:
                    os.unlink(module_path)
                except Exception:
                    pass
        
        except Exception as e:
            logger.exception(f"Error applying capability model: {e}")
            
            return {
                "status": "error",
                "error": str(e),
            }
    
    async def get_enhancement_task(self, task_id: str) -> Dict:
        """
        Get enhancement task status.
        
        Args:
            task_id (str): Task ID
            
        Returns:
            Dict: Task status
        """
        if task_id in self.enhancement_tasks:
            return self.enhancement_tasks[task_id]
        
        return {
            "status": "not_found",
        }
    
    async def get_capability_model(self, model_id: str) -> Dict:
        """
        Get capability model.
        
        Args:
            model_id (str): Model ID
            
        Returns:
            Dict: Model
        """
        if model_id in self.capability_models:
            return self.capability_models[model_id]
        
        return {
            "status": "not_found",
        }
    
    async def get_agent_capabilities(self, agent_id: str) -> List[Dict]:
        """
        Get agent capabilities.
        
        Args:
            agent_id (str): Agent ID
            
        Returns:
            List[Dict]: Agent capabilities
        """
        if agent_id in self.agent_capabilities:
            return self.agent_capabilities[agent_id]
        
        return []
    
    async def optimize_agent_behavior(
        self,
        agent_id: str,
        behavior_type: str,
        optimization_metric: str = "performance",
        generations: int = 30,
        population_size: int = 15,
    ) -> Dict:
        """
        Optimize an agent's behavior using AlphaEvolve.
        
        Args:
            agent_id (str): Agent ID
            behavior_type (str): Behavior type to optimize
            optimization_metric (str, optional): Metric to optimize
            generations (int, optional): Number of generations
            population_size (int, optional): Population size
            
        Returns:
            Dict: Optimization results
        """
        if not self.initialized:
            await self.initialize()
        
        # Check if agent exists
        if agent_id not in self.agent_manager.agents:
            return {
                "status": "error",
                "error": f"Agent not found: {agent_id}",
            }
        
        # Create problem definition
        problem = {
            "type": "agent_behavior",
            "agent_id": agent_id,
            "behavior_type": behavior_type,
            "optimization_metric": optimization_metric,
        }
        
        # Run evolution
        try:
            result = await self.alpha_evolve_engine.evolve(
                problem=problem,
                generations=generations,
                population_size=population_size,
            )
            
            return {
                "status": "completed",
                "agent_id": agent_id,
                "behavior_type": behavior_type,
                "optimization_metric": optimization_metric,
                "evolution_result": result,
            }
        
        except Exception as e:
            logger.exception(f"Error optimizing agent behavior: {e}")
            
            return {
                "status": "error",
                "error": str(e),
            }
    
    async def optimize_multi_agent_workflow(
        self,
        workflow_name: str,
        agent_ids: List[str],
        optimization_metric: str = "efficiency",
        generations: int = 40,
        population_size: int = 20,
    ) -> Dict:
        """
        Optimize a multi-agent workflow using AlphaEvolve.
        
        Args:
            workflow_name (str): Workflow name
            agent_ids (List[str]): Agent IDs involved in the workflow
            optimization_metric (str, optional): Metric to optimize
            generations (int, optional): Number of generations
            population_size (int, optional): Population size
            
        Returns:
            Dict: Optimization results
        """
        if not self.initialized:
            await self.initialize()
        
        # Check if agents exist
        for agent_id in agent_ids:
            if agent_id not in self.agent_manager.agents:
                return {
                    "status": "error",
                    "error": f"Agent not found: {agent_id}",
                }
        
        # Create problem definition
        problem = {
            "type": "multi_agent_workflow",
            "workflow_name": workflow_name,
            "agent_ids": agent_ids,
            "optimization_metric": optimization_metric,
        }
        
        # Run evolution
        try:
            result = await self.alpha_evolve_engine.evolve(
                problem=problem,
                generations=generations,
                population_size=population_size,
            )
            
            return {
                "status": "completed",
                "workflow_name": workflow_name,
                "agent_ids": agent_ids,
                "optimization_metric": optimization_metric,
                "evolution_result": result,
            }
        
        except Exception as e:
            logger.exception(f"Error optimizing multi-agent workflow: {e}")
            
            return {
                "status": "error",
                "error": str(e),
            }
    
    async def shutdown(self):
        """Shutdown the Agent Integration."""
        logger.info("Shutting down Agent Integration")
        
        # Shutdown AlphaEvolve engine
        if self.alpha_evolve_engine:
            await self.alpha_evolve_engine.shutdown()
        
        logger.info("Agent Integration shut down")
