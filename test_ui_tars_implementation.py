"""
Test UI-TARS 1.5 Implementation

This script tests the UI-TARS 1.5 implementation to ensure it works properly,
including browser detection, sandbox environment, virtual PC, and DPO capabilities.
"""
import os
import sys
import json
import asyncio
import logging
import argparse
import platform
import subprocess
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Union

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("ui_tars_test.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("ui_tars_test")

# Import UI-TARS utilities if available
try:
    from ui_tars.utils.enhanced_browser_detection import EnhancedBrowserDetector, BrowserInfo
    from ui_tars.utils.browser_sandbox import BrowserSandbox
    from ui_tars.utils.dpo_optimizer import DPOOptimizer
    UTILS_IMPORTED = True
except ImportError:
    logger.warning("UI-TARS utilities not found in path, will test without imports")
    UTILS_IMPORTED = False

class UITarsTester:
    """Test UI-TARS 1.5 implementation."""
    
    def __init__(self, 
                 ui_tars_path: Optional[str] = None,
                 config_path: Optional[str] = None,
                 browser_type: Optional[str] = None,
                 test_sandbox: bool = True,
                 test_virtual_pc: bool = True,
                 test_dpo: bool = True,
                 debug_mode: bool = False):
        """
        Initialize the UI-TARS tester.
        
        Args:
            ui_tars_path (Optional[str]): Path to UI-TARS executable
            config_path (Optional[str]): Path to UI-TARS configuration file
            browser_type (Optional[str]): Type of browser to use
            test_sandbox (bool): Whether to test sandbox mode
            test_virtual_pc (bool): Whether to test virtual PC mode
            test_dpo (bool): Whether to test DPO
            debug_mode (bool): Whether to enable debug mode
        """
        self.ui_tars_path = ui_tars_path
        self.config_path = config_path
        self.browser_type = browser_type
        self.test_sandbox = test_sandbox
        self.test_virtual_pc = test_virtual_pc
        self.test_dpo = test_dpo
        self.debug_mode = debug_mode
        self.os_type = platform.system()
        self.test_results = {}
        
        # Find UI-TARS executable if not provided
        if not self.ui_tars_path:
            self.ui_tars_path = self._find_ui_tars_executable()
            
        # Find config path if not provided
        if not self.config_path:
            self.config_path = self._find_ui_tars_config()
            
    def _find_ui_tars_executable(self) -> Optional[str]:
        """Find the UI-TARS executable."""
        logger.info("Searching for UI-TARS executable...")
        
        if self.os_type == "Windows":
            # Common installation locations on Windows
            possible_paths = [
                os.path.join(os.environ.get("PROGRAMFILES", "C:\\Program Files"), "UI-TARS", "UI-TARS.exe"),
                os.path.join(os.environ.get("PROGRAMFILES(X86)", "C:\\Program Files (x86)"), "UI-TARS", "UI-TARS.exe"),
                os.path.join(os.environ.get("LOCALAPPDATA", "C:\\Users\\<USER>\\AppData\\Local".format(os.getlogin())), "UI-TARS", "UI-TARS.exe"),
                "UI-TARS.exe"
            ]
        elif self.os_type == "Darwin":  # macOS
            # Common installation locations on macOS
            possible_paths = [
                "/Applications/UI-TARS.app/Contents/MacOS/UI-TARS",
                os.path.expanduser("~/Applications/UI-TARS.app/Contents/MacOS/UI-TARS"),
            ]
        else:  # Linux
            # Common installation locations on Linux
            possible_paths = [
                "/usr/local/bin/ui-tars",
                "/usr/bin/ui-tars",
                os.path.expanduser("~/.local/bin/ui-tars"),
            ]
            
        # Check if any of the paths exist
        for path in possible_paths:
            if os.path.exists(path):
                logger.info(f"Found UI-TARS executable at: {path}")
                return path
                
        # Try to find in PATH
        try:
            if self.os_type == "Windows":
                result = subprocess.run(["where", "UI-TARS.exe"], capture_output=True, text=True)
            else:
                result = subprocess.run(["which", "ui-tars"], capture_output=True, text=True)
                
            if result.returncode == 0:
                path = result.stdout.strip()
                logger.info(f"Found UI-TARS executable in PATH: {path}")
                return path
        except Exception as e:
            logger.debug(f"Error searching for UI-TARS in PATH: {e}")
            
        logger.warning("Could not find UI-TARS executable")
        return None
        
    def _find_ui_tars_config(self) -> Optional[str]:
        """Find the UI-TARS configuration file."""
        logger.info("Searching for UI-TARS configuration file...")
        
        # Check common locations
        possible_paths = [
            "config/ui_tars_config.json",
            "ui_tars/config.json",
            "ui_tars_config.json",
            "ui_tars_config_v2.json"
        ]
        
        # Check if any of the paths exist
        for path in possible_paths:
            if os.path.exists(path):
                logger.info(f"Found UI-TARS configuration at: {path}")
                return path
                
        logger.warning("Could not find UI-TARS configuration file")
        return None
        
    def _detect_browsers(self) -> Dict[str, Any]:
        """Detect installed browsers."""
        logger.info("Detecting installed browsers...")
        
        browsers = {}
        
        if UTILS_IMPORTED:
            # Use enhanced browser detection if available
            detector = EnhancedBrowserDetector()
            browsers = detector.detect_browsers(force_refresh=True)
            
            # Convert to dictionary for results
            browser_dict = {}
            for browser_type, browser_info in browsers.items():
                browser_dict[browser_type] = browser_info.to_dict()
                
            return browser_dict
        else:
            # Manual browser detection
            if self.os_type == "Windows":
                # Check for common browsers on Windows
                paths = [
                    (r"C:\Program Files\Google\Chrome\Application\chrome.exe", "chrome"),
                    (r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe", "chrome"),
                    (r"C:\Program Files\Mozilla Firefox\firefox.exe", "firefox"),
                    (r"C:\Program Files (x86)\Mozilla Firefox\firefox.exe", "firefox"),
                    (r"C:\Program Files\Microsoft\Edge\Application\msedge.exe", "edge"),
                    (r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe", "edge"),
                    (r"C:\Program Files\BraveSoftware\Brave-Browser\Application\brave.exe", "brave"),
                    (r"C:\Program Files (x86)\BraveSoftware\Brave-Browser\Application\brave.exe", "brave"),
                ]
            elif self.os_type == "Darwin":  # macOS
                # Check for common browsers on macOS
                paths = [
                    ("/Applications/Google Chrome.app/Contents/MacOS/Google Chrome", "chrome"),
                    ("/Applications/Firefox.app/Contents/MacOS/firefox", "firefox"),
                    ("/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge", "edge"),
                    ("/Applications/Brave Browser.app/Contents/MacOS/Brave Browser", "brave"),
                ]
            else:  # Linux
                # Check for common browsers on Linux
                paths = [
                    ("/usr/bin/google-chrome", "chrome"),
                    ("/usr/bin/firefox", "firefox"),
                    ("/usr/bin/microsoft-edge", "edge"),
                    ("/usr/bin/brave-browser", "brave"),
                ]
                
            # Check if any of the paths exist
            for path, browser_type in paths:
                if os.path.exists(path):
                    browsers[browser_type] = {
                        "browser_type": browser_type,
                        "executable_path": path,
                        "version": None,
                        "user_data_dir": None,
                        "default_profile": None,
                        "is_default": False
                    }
                    logger.info(f"Found {browser_type} browser at: {path}")
                    
            return browsers
            
    def _check_port_open(self, host: str, port: int) -> bool:
        """Check if a port is open."""
        import socket
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(5)
                result = s.connect_ex((host, port))
                return result == 0
        except Exception as e:
            logger.debug(f"Error checking port {port}: {e}")
            return False
            
    def _check_ui_tars_api(self) -> bool:
        """Check if the UI-TARS API is running."""
        import requests
        logger.info("Checking if UI-TARS API is running...")
        
        if not self._check_port_open("localhost", 8080):
            logger.warning("UI-TARS API not running on port 8080")
            return False
        
        try:
            # Try different API endpoints
            endpoints = [
                "http://localhost:8080/health",
                "http://localhost:8080/v1/models",
                "http://localhost:8080/api/status"
            ]
            
            for endpoint in endpoints:
                try:
                    logger.info(f"Trying endpoint: {endpoint}")
                    response = requests.get(endpoint, timeout=5)
                    
                    if response.status_code < 400:
                        logger.info(f"UI-TARS API is running (endpoint: {endpoint})")
                        return True
                except requests.exceptions.RequestException:
                    continue
            
            logger.warning("UI-TARS API is not responding to any known endpoints")
            return False
            
        except Exception as e:
            logger.error(f"Error checking UI-TARS API: {e}")
            return False
            
    async def test_ui_tars_installation(self) -> Dict[str, Any]:
        """Test UI-TARS installation."""
        logger.info("Testing UI-TARS installation...")
        
        results = {
            "executable_found": False,
            "config_found": False,
            "api_running": False,
            "version": None
        }
        
        # Check if executable exists
        if self.ui_tars_path and os.path.exists(self.ui_tars_path):
            results["executable_found"] = True
            logger.info(f"UI-TARS executable found at: {self.ui_tars_path}")
            
            # Try to get version
            try:
                result = subprocess.run(
                    [self.ui_tars_path, "--version"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                
                if result.returncode == 0:
                    results["version"] = result.stdout.strip()
                    logger.info(f"UI-TARS version: {results['version']}")
            except Exception as e:
                logger.debug(f"Error getting UI-TARS version: {e}")
        else:
            logger.warning("UI-TARS executable not found")
            
        # Check if config exists
        if self.config_path and os.path.exists(self.config_path):
            results["config_found"] = True
            logger.info(f"UI-TARS configuration found at: {self.config_path}")
        else:
            logger.warning("UI-TARS configuration not found")
            
        # Check if API is running
        results["api_running"] = self._check_ui_tars_api()
        
        return results
        
    async def test_browser_detection(self) -> Dict[str, Any]:
        """Test browser detection."""
        logger.info("Testing browser detection...")
        
        browsers = self._detect_browsers()
        
        results = {
            "browsers_detected": len(browsers) > 0,
            "browsers": browsers,
            "selected_browser": None
        }
        
        # Select browser
        if self.browser_type and self.browser_type in browsers:
            results["selected_browser"] = browsers[self.browser_type]
        elif browsers:
            # Use first available browser
            browser_type = next(iter(browsers.keys()))
            results["selected_browser"] = browsers[browser_type]
            
        if results["selected_browser"]:
            logger.info(f"Selected browser: {results['selected_browser']['browser_type']}")
        else:
            logger.warning("No browser selected")
            
        return results
        
    async def test_sandbox_environment(self) -> Dict[str, Any]:
        """Test sandbox environment."""
        if not self.test_sandbox:
            logger.info("Skipping sandbox environment test")
            return {"tested": False}
            
        logger.info("Testing sandbox environment...")
        
        results = {
            "tested": True,
            "sandbox_created": False,
            "browser_started": False,
            "debugging_port_open": False
        }
        
        if not UTILS_IMPORTED:
            logger.warning("UI-TARS utilities not imported, skipping sandbox test")
            return results
            
        try:
            # Get browser info
            browser_results = await self.test_browser_detection()
            selected_browser = browser_results.get("selected_browser")
            
            if not selected_browser:
                logger.warning("No browser selected, skipping sandbox test")
                return results
                
            # Create sandbox
            import tempfile
            temp_dir = tempfile.mkdtemp(prefix="ui_tars_test_sandbox_")
            
            sandbox = BrowserSandbox(
                browser_type=selected_browser["browser_type"],
                user_data_dir=os.path.join(temp_dir, "browser_data"),
                remote_debugging_port=9222,
                headless=False,
                isolation_level="high",
                temp_dir=temp_dir
            )
            
            results["sandbox_created"] = True
            logger.info("Sandbox environment created")
            
            # Start browser in sandbox
            success = await sandbox.start()
            results["browser_started"] = success
            
            if success:
                logger.info("Browser started in sandbox")
                
                # Check if debugging port is open
                results["debugging_port_open"] = self._check_port_open("localhost", 9222)
                
                if results["debugging_port_open"]:
                    logger.info("Browser debugging port is open")
                else:
                    logger.warning("Browser debugging port is not open")
                    
                # Stop browser
                await sandbox.stop()
                logger.info("Browser stopped")
            else:
                logger.warning("Failed to start browser in sandbox")
                
            # Clean up
            await sandbox.cleanup()
            logger.info("Sandbox environment cleaned up")
            
        except Exception as e:
            logger.exception(f"Error testing sandbox environment: {e}")
            
        return results
        
    async def test_dpo_capabilities(self) -> Dict[str, Any]:
        """Test DPO capabilities."""
        if not self.test_dpo:
            logger.info("Skipping DPO capabilities test")
            return {"tested": False}
            
        logger.info("Testing DPO capabilities...")
        
        results = {
            "tested": True,
            "dpo_created": False,
            "preference_added": False,
            "optimization_simulated": False
        }
        
        if not UTILS_IMPORTED:
            logger.warning("UI-TARS utilities not imported, skipping DPO test")
            return results
            
        try:
            # Create DPO optimizer
            import tempfile
            temp_file = os.path.join(tempfile.gettempdir(), "ui_tars_test_dpo.json")
            
            dpo = DPOOptimizer(
                model_name="UI-TARS-1.5-7B",
                preference_data_path=temp_file
            )
            
            results["dpo_created"] = True
            logger.info("DPO optimizer created")
            
            # Add preference pair
            dpo.add_preference_pair(
                prompt="What is UI-TARS?",
                chosen_response="UI-TARS is a powerful GUI agent that allows autonomous control of browsers and desktop applications.",
                rejected_response="UI-TARS is a tool.",
                metadata={"source": "test"}
            )
            
            results["preference_added"] = True
            logger.info("Preference pair added")
            
            # Simulate optimization
            optimization_result = dpo.simulate_optimization_function(
                dpo.preference_data,
                dpo.learning_rate,
                dpo.beta,
                dpo.batch_size
            )
            
            results["optimization_simulated"] = True
            results["optimization_result"] = optimization_result
            logger.info(f"Optimization simulated: {optimization_result}")
            
            # Clean up
            if os.path.exists(temp_file):
                os.remove(temp_file)
                logger.info("Temporary file removed")
                
        except Exception as e:
            logger.exception(f"Error testing DPO capabilities: {e}")
            
        return results
        
    async def test_virtual_pc_capabilities(self) -> Dict[str, Any]:
        """Test virtual PC capabilities."""
        if not self.test_virtual_pc:
            logger.info("Skipping virtual PC capabilities test")
            return {"tested": False}
            
        logger.info("Testing virtual PC capabilities...")
        
        results = {
            "tested": True,
            "os_supported": self.os_type == "Windows",
            "virtual_pc_simulated": False
        }
        
        # Virtual PC is primarily for Windows
        if self.os_type != "Windows":
            logger.warning(f"Virtual PC not fully supported on {self.os_type}")
            
        try:
            # Simulate virtual PC
            import tempfile
            temp_dir = tempfile.mkdtemp(prefix="ui_tars_test_virtual_pc_")
            
            # Create virtual PC configuration
            config = {
                "ui_tars": {
                    "version": "1.5",
                    "enabled": True,
                    "virtual_pc": {
                        "enabled": True,
                        "memory_mb": 2048,
                        "cpu_cores": 2,
                        "temp_dir": temp_dir
                    }
                }
            }
            
            # Save configuration
            config_path = os.path.join(temp_dir, "virtual_pc_config.json")
            with open(config_path, "w") as f:
                json.dump(config, f, indent=2)
                
            results["virtual_pc_simulated"] = True
            results["config_path"] = config_path
            logger.info("Virtual PC configuration created")
            
            # Clean up
            if os.path.exists(temp_dir):
                import shutil
                shutil.rmtree(temp_dir)
                logger.info("Temporary directory removed")
                
        except Exception as e:
            logger.exception(f"Error testing virtual PC capabilities: {e}")
            
        return results
        
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all tests."""
        logger.info("Running all UI-TARS tests...")
        
        # Test UI-TARS installation
        self.test_results["installation"] = await self.test_ui_tars_installation()
        
        # Test browser detection
        self.test_results["browser_detection"] = await self.test_browser_detection()
        
        # Test sandbox environment
        self.test_results["sandbox"] = await self.test_sandbox_environment()
        
        # Test DPO capabilities
        self.test_results["dpo"] = await self.test_dpo_capabilities()
        
        # Test virtual PC capabilities
        self.test_results["virtual_pc"] = await self.test_virtual_pc_capabilities()
        
        # Overall result
        self.test_results["overall"] = {
            "success": (
                self.test_results["installation"]["executable_found"] and
                self.test_results["browser_detection"]["browsers_detected"] and
                (not self.test_sandbox or self.test_results["sandbox"]["sandbox_created"]) and
                (not self.test_dpo or self.test_results["dpo"]["dpo_created"]) and
                (not self.test_virtual_pc or self.test_results["virtual_pc"]["virtual_pc_simulated"])
            ),
            "timestamp": time.time()
        }
        
        logger.info(f"All tests completed. Overall success: {self.test_results['overall']['success']}")
        
        return self.test_results
        
    def print_results(self) -> None:
        """Print test results."""
        print("\nUI-TARS 1.5 Test Results")
        print("=======================\n")
        
        # Installation
        installation = self.test_results.get("installation", {})
        print("1. UI-TARS Installation:")
        print(f"   - Executable found: {'✅' if installation.get('executable_found') else '❌'}")
        print(f"   - Configuration found: {'✅' if installation.get('config_found') else '❌'}")
        print(f"   - API running: {'✅' if installation.get('api_running') else '❌'}")
        if installation.get("version"):
            print(f"   - Version: {installation['version']}")
        print()
        
        # Browser detection
        browser_detection = self.test_results.get("browser_detection", {})
        print("2. Browser Detection:")
        print(f"   - Browsers detected: {'✅' if browser_detection.get('browsers_detected') else '❌'}")
        if browser_detection.get("browsers"):
            print(f"   - Found {len(browser_detection['browsers'])} browsers:")
            for browser_type in browser_detection["browsers"].keys():
                print(f"     - {browser_type}")
        if browser_detection.get("selected_browser"):
            print(f"   - Selected browser: {browser_detection['selected_browser']['browser_type']}")
        print()
        
        # Sandbox environment
        sandbox = self.test_results.get("sandbox", {})
        print("3. Sandbox Environment:")
        if not sandbox.get("tested", False):
            print("   - Test skipped")
        else:
            print(f"   - Sandbox created: {'✅' if sandbox.get('sandbox_created') else '❌'}")
            print(f"   - Browser started: {'✅' if sandbox.get('browser_started') else '❌'}")
            print(f"   - Debugging port open: {'✅' if sandbox.get('debugging_port_open') else '❌'}")
        print()
        
        # DPO capabilities
        dpo = self.test_results.get("dpo", {})
        print("4. DPO Capabilities:")
        if not dpo.get("tested", False):
            print("   - Test skipped")
        else:
            print(f"   - DPO created: {'✅' if dpo.get('dpo_created') else '❌'}")
            print(f"   - Preference added: {'✅' if dpo.get('preference_added') else '❌'}")
            print(f"   - Optimization simulated: {'✅' if dpo.get('optimization_simulated') else '❌'}")
        print()
        
        # Virtual PC capabilities
        virtual_pc = self.test_results.get("virtual_pc", {})
        print("5. Virtual PC Capabilities:")
        if not virtual_pc.get("tested", False):
            print("   - Test skipped")
        else:
            print(f"   - OS supported: {'✅' if virtual_pc.get('os_supported') else '⚠️ Limited'}")
            print(f"   - Virtual PC simulated: {'✅' if virtual_pc.get('virtual_pc_simulated') else '❌'}")
        print()
        
        # Overall result
        overall = self.test_results.get("overall", {})
        print("Overall Result:")
        print(f"   - Success: {'✅' if overall.get('success') else '❌'}")
        print()
        
        # Recommendations
        print("Recommendations:")
        if not installation.get("executable_found"):
            print("   - Install UI-TARS 1.5 or provide the correct path")
        if not installation.get("config_found"):
            print("   - Create or provide the correct configuration file")
        if not installation.get("api_running"):
            print("   - Start UI-TARS API or check if it's running on a different port")
        if not browser_detection.get("browsers_detected"):
            print("   - Install a supported browser (Chrome, Edge, Firefox, or Brave)")
        if sandbox.get("tested", False) and not sandbox.get("sandbox_created"):
            print("   - Check browser sandbox implementation")
        if dpo.get("tested", False) and not dpo.get("dpo_created"):
            print("   - Check DPO implementation")
        if virtual_pc.get("tested", False) and not virtual_pc.get("os_supported"):
            print(f"   - Virtual PC has limited support on {self.os_type}")
        print()

async def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Test UI-TARS 1.5 Implementation")
    parser.add_argument("--path", type=str, help="Path to UI-TARS executable")
    parser.add_argument("--config", type=str, help="Path to UI-TARS configuration file")
    parser.add_argument("--browser", type=str, help="Type of browser to use (chrome, edge, firefox, brave)")
    parser.add_argument("--no-sandbox", action="store_true", help="Skip sandbox test")
    parser.add_argument("--no-virtual-pc", action="store_true", help="Skip virtual PC test")
    parser.add_argument("--no-dpo", action="store_true", help="Skip DPO test")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    
    args = parser.parse_args()
    
    # Set log level
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        
    print("UI-TARS 1.5 Implementation Test")
    print("==============================")
    print()
    
    # Create tester
    tester = UITarsTester(
        ui_tars_path=args.path,
        config_path=args.config,
        browser_type=args.browser,
        test_sandbox=not args.no_sandbox,
        test_virtual_pc=not args.no_virtual_pc,
        test_dpo=not args.no_dpo,
        debug_mode=args.debug
    )
    
    # Run tests
    await tester.run_all_tests()
    
    # Print results
    tester.print_results()
    
    # Return success or failure
    return 0 if tester.test_results.get("overall", {}).get("success", False) else 1

if __name__ == "__main__":
    asyncio.run(main())
