"""
Google Voice UI-TARS Automation for the Multi-Agent AI System.

This module provides browser automation for Google Voice using UI-TARS 1.5,
enabling agents to make calls, send texts, and leave voicemails through
the Google Voice web interface.
"""
import asyncio
import json
import logging
import os
import sys
import time
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta

import aiohttp
import requests
from selenium import webdriver
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.firefox.service import Service as FirefoxService
from selenium.webdriver.edge.service import Service as EdgeService
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.firefox.options import Options as FirefoxOptions
from selenium.webdriver.edge.options import Options as EdgeOptions
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from webdriver_manager.firefox import GeckoDriverManager
from webdriver_manager.microsoft import EdgeChromiumDriverManager

# Add parent directory to path to import from core
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.logger import setup_logger
from ui_tars.connector.ui_tars_connector import UITarsConnector

# Set up logger
logger = setup_logger("google_voice_ui_tars_automation")

# Check if UI-TARS is available
try:
    from ui_tars.connector.ui_tars_connector import UITarsConnector
    UI_TARS_AVAILABLE = True
except ImportError:
    logger.warning("UI-TARS connector not available. Running without UI-TARS integration.")
    UI_TARS_AVAILABLE = False

class GoogleVoiceUITarsAutomation:
    """
    Google Voice UI-TARS Automation for the Multi-Agent AI System.

    This class provides browser automation for Google Voice using UI-TARS 1.5,
    enabling agents to make calls, send texts, and leave voicemails through
    the Google Voice web interface.
    """

    def __init__(self,
                 browser_type: str = "chrome",
                 ui_tars_api_url: str = "http://localhost:8080",
                 ui_tars_model_name: str = "UI-TARS-1.5-7B",
                 ui_tars_installation_path: Optional[str] = None):
        """
        Initialize the Google Voice UI-TARS Automation.

        Args:
            browser_type (str): Type of browser to use (chrome, firefox, edge)
            ui_tars_api_url (str): URL of the UI-TARS API
            ui_tars_model_name (str): Name of the model to use
            ui_tars_installation_path (Optional[str]): Path to UI-TARS installation
        """
        self.browser_type = browser_type.lower()
        self.ui_tars_api_url = ui_tars_api_url
        self.ui_tars_model_name = ui_tars_model_name
        self.ui_tars_installation_path = ui_tars_installation_path

        self.driver = None
        self.ui_tars_connector = None
        self.ui_tars_enabled = UI_TARS_AVAILABLE
        self.is_initialized = False
        self.is_logged_in = False

    async def initialize(self) -> bool:
        """
        Initialize the Google Voice UI-TARS Automation.

        Returns:
            bool: True if initialization was successful, False otherwise
        """
        logger.info("Initializing Google Voice UI-TARS Automation")

        try:
            # Initialize browser
            if self.browser_type == "chrome":
                options = ChromeOptions()
                options.add_argument("--start-maximized")
                options.add_argument("--disable-notifications")
                options.add_experimental_option("excludeSwitches", ["enable-logging"])

                self.driver = webdriver.Chrome(
                    service=ChromeService(ChromeDriverManager().install()),
                    options=options
                )
            elif self.browser_type == "firefox":
                options = FirefoxOptions()
                options.add_argument("--start-maximized")

                self.driver = webdriver.Firefox(
                    service=FirefoxService(GeckoDriverManager().install()),
                    options=options
                )
            elif self.browser_type == "edge":
                options = EdgeOptions()
                options.add_argument("--start-maximized")
                options.add_argument("--disable-notifications")

                self.driver = webdriver.Edge(
                    service=EdgeService(EdgeChromiumDriverManager().install()),
                    options=options
                )
            else:
                logger.error(f"Unsupported browser type: {self.browser_type}")
                return False

            # Set implicit wait time
            self.driver.implicitly_wait(10)

            logger.info(f"{self.browser_type} browser initialized successfully")

            # Initialize UI-TARS if available
            if self.ui_tars_enabled:
                logger.info("Initializing UI-TARS connector")

                # Create UI-TARS connector
                self.ui_tars_connector = UITarsConnector(
                    api_url=self.ui_tars_api_url,
                    api_key=None,
                    model_name=self.ui_tars_model_name,
                    installation_path=self.ui_tars_installation_path
                )

                # Initialize connector
                success = await self.ui_tars_connector.initialize()
                if not success:
                    logger.warning("Failed to initialize UI-TARS connector. Running without UI-TARS integration.")
                    self.ui_tars_enabled = False
                else:
                    logger.info("UI-TARS connector initialized successfully")

            self.is_initialized = True
            return True

        except Exception as e:
            logger.exception(f"Error initializing Google Voice UI-TARS Automation: {e}")
            return False

    async def login(self, email_account: str, password: str) -> bool:
        """
        Log in to Google Voice.

        Args:
            email_account (str): Google account email
            password (str): Google account password

        Returns:
            bool: True if login was successful, False otherwise
        """
        if not self.is_initialized:
            logger.error("Google Voice UI-TARS Automation not initialized")
            return False

        try:
            # Navigate to Google Voice
            logger.info("Navigating to Google Voice")
            self.driver.get("https://voice.google.com/")

            # Wait for sign-in button or already signed in
            try:
                # Check if already signed in
                WebDriverWait(self.driver, 5).until(
                    EC.presence_of_element_located((By.XPATH, "//div[contains(@aria-label, 'Messages') or contains(@aria-label, 'Calls')]"))
                )
                logger.info("Already signed in to Google Voice")
                self.is_logged_in = True
                return True
            except TimeoutException:
                # Need to sign in
                logger.info("Not signed in, proceeding with login")

                # Wait for email input
                email_input = WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, "//input[@type='email']"))
                )

                # Enter email
                email_input.clear()
                email_input.send_keys(email_account)

                # Click Next
                next_button = self.driver.find_element(By.XPATH, "//button[contains(., 'Next')]")
                next_button.click()

                # Wait for password input
                password_input = WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, "//input[@type='password']"))
                )

                # Enter password
                password_input.clear()
                password_input.send_keys(password)

                # Click Next
                next_button = self.driver.find_element(By.XPATH, "//button[contains(., 'Next')]")
                next_button.click()

                # Wait for Google Voice to load
                WebDriverWait(self.driver, 20).until(
                    EC.presence_of_element_located((By.XPATH, "//div[contains(@aria-label, 'Messages') or contains(@aria-label, 'Calls')]"))
                )

                logger.info("Successfully logged in to Google Voice")
                self.is_logged_in = True
                return True

        except Exception as e:
            logger.exception(f"Error logging in to Google Voice: {e}")
            return False

    async def send_text_message(self, phone_number: str, message: str) -> Dict[str, Any]:
        """
        Send a text message using Google Voice.

        Args:
            phone_number (str): Recipient phone number
            message (str): Message content

        Returns:
            Dict[str, Any]: Result of the operation
        """
        if not self.is_initialized:
            logger.error("Google Voice UI-TARS Automation not initialized")
            return {"success": False, "error": "Google Voice UI-TARS Automation not initialized"}

        if not self.is_logged_in:
            logger.error("Not logged in to Google Voice")
            return {"success": False, "error": "Not logged in to Google Voice"}

        try:
            # Click on Messages tab if not already selected
            try:
                messages_tab = WebDriverWait(self.driver, 5).until(
                    EC.element_to_be_clickable((By.XPATH, "//div[contains(@aria-label, 'Messages')]"))
                )
                messages_tab.click()
                logger.info("Clicked on Messages tab")
            except:
                logger.info("Messages tab already selected or not found")

            # Click on New conversation button
            new_conversation_button = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//div[contains(@aria-label, 'New conversation') or contains(@aria-label, 'Start chat')]"))
            )
            new_conversation_button.click()
            logger.info("Clicked on New conversation button")

            # Enter phone number
            recipient_input = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//input[contains(@aria-label, 'Type a name, phone number, or email')]"))
            )
            recipient_input.clear()
            recipient_input.send_keys(phone_number)

            # Wait and click on the recipient
            time.sleep(2)  # Give time for the recipient to appear

            # If UI-TARS is enabled, analyze the screen
            if self.ui_tars_enabled:
                logger.info("Analyzing screen with UI-TARS after entering phone number")
                analysis = await self.analyze_screen()
                if analysis["success"]:
                    logger.info(f"UI-TARS analysis: {analysis['analysis']}")

            # Click on the recipient
            try:
                recipient = WebDriverWait(self.driver, 5).until(
                    EC.element_to_be_clickable((By.XPATH, f"//div[contains(text(), '{phone_number}')]"))
                )
                recipient.click()
                logger.info(f"Selected recipient: {phone_number}")
            except TimeoutException:
                # Try to just press Enter
                recipient_input.send_keys("\n")
                logger.info("Pressed Enter to select recipient")

            # Enter message
            message_input = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//textarea[contains(@aria-label, 'Type a message')]"))
            )
            message_input.clear()
            message_input.send_keys(message)

            # Send message
            send_button = self.driver.find_element(By.XPATH, "//button[contains(@aria-label, 'Send message')]")
            send_button.click()

            # Wait for message to be sent
            time.sleep(3)

            logger.info(f"Text message sent to {phone_number}")
            return {"success": True, "recipient": phone_number, "message": message}

        except Exception as e:
            logger.exception(f"Error sending text message: {e}")
            return {"success": False, "error": str(e)}

    async def make_call(self, phone_number: str) -> Dict[str, Any]:
        """
        Make a phone call using Google Voice.

        Args:
            phone_number (str): Recipient phone number

        Returns:
            Dict[str, Any]: Result of the operation
        """
        if not self.is_initialized:
            logger.error("Google Voice UI-TARS Automation not initialized")
            return {"success": False, "error": "Google Voice UI-TARS Automation not initialized"}

        if not self.is_logged_in:
            logger.error("Not logged in to Google Voice")
            return {"success": False, "error": "Not logged in to Google Voice"}

        try:
            # Click on Calls tab if not already selected
            try:
                calls_tab = WebDriverWait(self.driver, 5).until(
                    EC.element_to_be_clickable((By.XPATH, "//div[contains(@aria-label, 'Calls')]"))
                )
                calls_tab.click()
                logger.info("Clicked on Calls tab")
            except:
                logger.info("Calls tab already selected or not found")

            # Click on Dialpad button
            dialpad_button = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//div[contains(@aria-label, 'Dialpad') or contains(@aria-label, 'Make a call')]"))
            )
            dialpad_button.click()
            logger.info("Clicked on Dialpad button")

            # Enter phone number
            dialpad_input = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//input[contains(@aria-label, 'Enter a phone number')]"))
            )
            dialpad_input.clear()
            dialpad_input.send_keys(phone_number)

            # If UI-TARS is enabled, analyze the screen
            if self.ui_tars_enabled:
                logger.info("Analyzing screen with UI-TARS after entering phone number")
                analysis = await self.analyze_screen()
                if analysis["success"]:
                    logger.info(f"UI-TARS analysis: {analysis['analysis']}")

            # Click call button
            call_button = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(@aria-label, 'Call')]"))
            )
            call_button.click()

            # Wait for call to connect
            time.sleep(5)

            logger.info(f"Call initiated to {phone_number}")
            return {"success": True, "recipient": phone_number, "status": "initiated"}

        except Exception as e:
            logger.exception(f"Error making call: {e}")
            return {"success": False, "error": str(e)}

    async def leave_voicemail(self, phone_number: str, wait_time: int = 30, voicemail_audio_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Leave a voicemail using Google Voice.

        Args:
            phone_number (str): Recipient phone number
            wait_time (int): Time to wait before hanging up (seconds)
            voicemail_audio_path (Optional[str]): Path to voicemail audio file to play

        Returns:
            Dict[str, Any]: Result of the operation
        """
        if not self.is_initialized:
            logger.error("Google Voice UI-TARS Automation not initialized")
            return {"success": False, "error": "Google Voice UI-TARS Automation not initialized"}

        if not self.is_logged_in:
            logger.error("Not logged in to Google Voice")
            return {"success": False, "error": "Not logged in to Google Voice"}

        try:
            # Make the call
            call_result = await self.make_call(phone_number)
            if not call_result["success"]:
                return call_result

            # Wait for voicemail to pick up
            logger.info(f"Waiting {wait_time} seconds for voicemail to pick up")
            time.sleep(wait_time)

            # If we have an audio file to play, play it
            if voicemail_audio_path and os.path.exists(voicemail_audio_path):
                logger.info(f"Playing voicemail audio from {voicemail_audio_path}")

                # This would use an audio player to play the voicemail
                # For now, we'll just simulate it
                try:
                    # In a real implementation, this would play the audio file
                    # For example, using the playsound library:
                    # from playsound import playsound
                    # playsound(voicemail_audio_path)

                    # For now, we'll just wait a bit to simulate playing the audio
                    audio_duration = 15  # Assume 15 seconds for now
                    logger.info(f"Playing audio for {audio_duration} seconds")
                    time.sleep(audio_duration)

                except Exception as audio_error:
                    logger.error(f"Error playing voicemail audio: {audio_error}")

            # Hang up
            try:
                hangup_button = WebDriverWait(self.driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, "//button[contains(@aria-label, 'End call') or contains(@aria-label, 'Hang up')]"))
                )
                hangup_button.click()
                logger.info("Call ended")
            except:
                logger.warning("Hangup button not found, call may have already ended")

            logger.info(f"Voicemail left for {phone_number}")
            return {
                "success": True,
                "recipient": phone_number,
                "status": "voicemail_left",
                "audio_played": voicemail_audio_path is not None
            }

        except Exception as e:
            logger.exception(f"Error leaving voicemail: {e}")
            return {"success": False, "error": str(e)}

    async def analyze_screen(self) -> Dict[str, Any]:
        """
        Analyze the current screen using UI-TARS.

        Returns:
            Dict[str, Any]: Analysis result
        """
        if not self.ui_tars_enabled or not self.ui_tars_connector:
            return {"success": False, "error": "UI-TARS not enabled or connector not initialized"}

        try:
            # Take a screenshot
            screenshot_path = "current_screen.png"
            self.driver.save_screenshot(screenshot_path)

            # Ask UI-TARS to analyze the screenshot
            command = f"Analyze this screenshot and tell me what's happening on the screen. What elements are visible? Is there a login form? Is there an error message? What should I do next?"

            result = await self.ui_tars_connector.execute_command(
                command=command,
                screenshot=screenshot_path
            )

            return {"success": True, "analysis": result}

        except Exception as e:
            logger.exception(f"Error analyzing screen: {e}")
            return {"success": False, "error": str(e)}

    async def get_next_action(self, current_task: str) -> Dict[str, Any]:
        """
        Get the next action from UI-TARS.

        Args:
            current_task (str): Current task description

        Returns:
            Dict[str, Any]: Next action
        """
        if not self.ui_tars_enabled or not self.ui_tars_connector:
            return {"success": False, "error": "UI-TARS not enabled or connector not initialized"}

        try:
            # Take a screenshot
            screenshot_path = "current_screen.png"
            self.driver.save_screenshot(screenshot_path)

            # Ask UI-TARS for the next action
            command = f"I'm trying to {current_task}. Based on this screenshot, what should I do next? Give me a specific action to take."

            result = await self.ui_tars_connector.execute_command(
                command=command,
                screenshot=screenshot_path
            )

            return {"success": True, "next_action": result}

        except Exception as e:
            logger.exception(f"Error getting next action: {e}")
            return {"success": False, "error": str(e)}

    async def shutdown(self):
        """Shut down the Google Voice UI-TARS Automation."""
        logger.info("Shutting down Google Voice UI-TARS Automation")

        if self.driver:
            self.driver.quit()
            self.driver = None

        self.is_initialized = False
        self.is_logged_in = False

        logger.info("Google Voice UI-TARS Automation shut down successfully")
