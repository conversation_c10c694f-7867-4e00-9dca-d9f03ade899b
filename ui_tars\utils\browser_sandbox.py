"""
Browser Sandbox for UI-TARS.

This module provides a sandboxed browser environment for UI-TARS 1.5,
allowing safe and isolated browser automation.
"""
import os
import sys
import json
import asyncio
import logging
import subprocess
import platform
import socket
import tempfile
import shutil
import time
import random
import string
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Union

from core.logger import setup_logger

# Set up logger
logger = setup_logger("ui_tars_browser_sandbox")

class BrowserSandbox:
    """
    Sandboxed browser environment for UI-TARS 1.5.
    
    This class provides a sandboxed browser environment for UI-TARS 1.5,
    allowing safe and isolated browser automation.
    """
    
    def __init__(self, 
                 browser_type: str = "chrome",
                 user_data_dir: Optional[str] = None,
                 remote_debugging_port: int = 9222,
                 headless: bool = False,
                 isolation_level: str = "high",
                 temp_dir: Optional[str] = None):
        """
        Initialize the browser sandbox.
        
        Args:
            browser_type (str): Type of browser to use
            user_data_dir (Optional[str]): Directory for browser user data
            remote_debugging_port (int): Port for remote debugging
            headless (bool): Whether to run in headless mode
            isolation_level (str): Level of isolation (low, medium, high)
            temp_dir (Optional[str]): Temporary directory for sandbox
        """
        self.browser_type = browser_type.lower()
        self.user_data_dir = user_data_dir
        self.remote_debugging_port = remote_debugging_port
        self.headless = headless
        self.isolation_level = isolation_level
        self.temp_dir = temp_dir
        self.os_type = platform.system()
        self.browser_path = None
        self.browser_process = None
        self.is_running = False
        self.sandbox_id = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
        
        # Create temp directory if not provided
        if not self.temp_dir:
            self.temp_dir = tempfile.mkdtemp(prefix=f"ui_tars_browser_sandbox_{self.sandbox_id}_")
            logger.info(f"Created temporary directory for browser sandbox: {self.temp_dir}")
            
        # Create user data directory if not provided
        if not self.user_data_dir:
            self.user_data_dir = os.path.join(self.temp_dir, "browser_data")
            os.makedirs(self.user_data_dir, exist_ok=True)
            logger.info(f"Created user data directory for browser: {self.user_data_dir}")
            
        # Find browser path
        self._find_browser_path()
        
    def _find_browser_path(self):
        """Find the browser executable path based on the browser type and OS."""
        logger.info(f"Finding path for {self.browser_type} browser...")
        
        if self.os_type == "Windows":
            # Common browser paths on Windows
            browser_paths = {
                "chrome": [
                    r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                    r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
                ],
                "edge": [
                    r"C:\Program Files\Microsoft\Edge\Application\msedge.exe",
                    r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe"
                ],
                "firefox": [
                    r"C:\Program Files\Mozilla Firefox\firefox.exe",
                    r"C:\Program Files (x86)\Mozilla Firefox\firefox.exe"
                ],
                "brave": [
                    r"C:\Program Files\BraveSoftware\Brave-Browser\Application\brave.exe",
                    r"C:\Program Files (x86)\BraveSoftware\Brave-Browser\Application\brave.exe"
                ]
            }
        elif self.os_type == "Darwin":  # macOS
            # Common browser paths on macOS
            browser_paths = {
                "chrome": [
                    "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                    os.path.expanduser("~/Applications/Google Chrome.app/Contents/MacOS/Google Chrome")
                ],
                "edge": [
                    "/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge",
                    os.path.expanduser("~/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge")
                ],
                "firefox": [
                    "/Applications/Firefox.app/Contents/MacOS/firefox",
                    os.path.expanduser("~/Applications/Firefox.app/Contents/MacOS/firefox")
                ],
                "brave": [
                    "/Applications/Brave Browser.app/Contents/MacOS/Brave Browser",
                    os.path.expanduser("~/Applications/Brave Browser.app/Contents/MacOS/Brave Browser")
                ]
            }
        else:  # Linux
            # Common browser paths on Linux
            browser_paths = {
                "chrome": [
                    "/usr/bin/google-chrome",
                    "/usr/bin/google-chrome-stable"
                ],
                "edge": [
                    "/usr/bin/microsoft-edge",
                    "/usr/bin/microsoft-edge-stable"
                ],
                "firefox": [
                    "/usr/bin/firefox",
                    "/usr/bin/firefox-esr"
                ],
                "brave": [
                    "/usr/bin/brave-browser",
                    "/usr/bin/brave"
                ]
            }
            
        # Check if browser type is supported
        if self.browser_type not in browser_paths:
            logger.error(f"Unsupported browser type: {self.browser_type}")
            return
            
        # Check if any of the paths exist
        for path in browser_paths.get(self.browser_type, []):
            if os.path.exists(path):
                self.browser_path = path
                logger.info(f"Found {self.browser_type} browser at: {path}")
                return
                
        # Try to find browser in PATH
        try:
            if self.os_type == "Windows":
                browser_exe = {
                    "chrome": "chrome.exe",
                    "edge": "msedge.exe",
                    "firefox": "firefox.exe",
                    "brave": "brave.exe"
                }.get(self.browser_type)
                
                if browser_exe:
                    result = subprocess.run(["where", browser_exe], capture_output=True, text=True)
                    if result.returncode == 0:
                        self.browser_path = result.stdout.strip().split('\n')[0]
                        logger.info(f"Found {self.browser_type} browser in PATH: {self.browser_path}")
                        return
            else:
                browser_cmd = {
                    "chrome": "google-chrome",
                    "edge": "microsoft-edge",
                    "firefox": "firefox",
                    "brave": "brave-browser"
                }.get(self.browser_type)
                
                if browser_cmd:
                    result = subprocess.run(["which", browser_cmd], capture_output=True, text=True)
                    if result.returncode == 0:
                        self.browser_path = result.stdout.strip()
                        logger.info(f"Found {self.browser_type} browser in PATH: {self.browser_path}")
                        return
        except Exception as e:
            logger.debug(f"Error searching for browser in PATH: {e}")
            
        logger.warning(f"Could not find {self.browser_type} browser")
        
    def _get_browser_launch_args(self) -> List[str]:
        """Get the command-line arguments for launching the browser."""
        # Base arguments for all browsers
        args = [
            f"--remote-debugging-port={self.remote_debugging_port}",
            f"--user-data-dir={self.user_data_dir}",
            "--no-first-run",
            "--no-default-browser-check"
        ]
        
        # Add headless mode if enabled
        if self.headless:
            if self.browser_type in ["chrome", "edge", "brave"]:
                args.append("--headless=new")
            elif self.browser_type == "firefox":
                args.append("--headless")
                
        # Add isolation-specific arguments
        if self.isolation_level == "high":
            args.extend([
                "--disable-extensions",
                "--disable-sync",
                "--disable-background-networking",
                "--disable-default-apps",
                "--disable-translate",
                "--disable-features=TranslateUI",
                "--disable-infobars",
                "--disable-save-password-bubble",
                "--disable-notifications",
                "--disable-popup-blocking",
                "--disable-component-update",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
                "--disable-background-mode",
                "--disable-breakpad",
                "--disable-client-side-phishing-detection",
                "--disable-hang-monitor",
                "--disable-prompt-on-repost",
                "--metrics-recording-only",
                "--safebrowsing-disable-auto-update",
                "--password-store=basic",
                "--use-mock-keychain"
            ])
            
            # Add no-sandbox only for non-root users on Linux
            if self.os_type == "Linux" and os.geteuid() != 0:
                args.append("--no-sandbox")
        elif self.isolation_level == "medium":
            args.extend([
                "--disable-extensions",
                "--disable-sync",
                "--disable-background-networking",
                "--disable-translate"
            ])
            
        # Browser-specific arguments
        if self.browser_type == "firefox":
            # Firefox-specific arguments
            args = ["-P", "ui-tars-sandbox", "-no-remote", "-url", "about:blank"]
            
        return args
        
    async def start(self) -> bool:
        """Start the browser in sandbox mode."""
        if self.is_running:
            logger.info("Browser is already running in sandbox")
            return True
            
        if not self.browser_path:
            logger.error(f"Browser path not found for {self.browser_type}")
            return False
            
        logger.info(f"Starting {self.browser_type} browser in sandbox mode...")
        
        try:
            # Prepare command
            command = [self.browser_path]
            command.extend(self._get_browser_launch_args())
            
            # Add initial URL
            if self.browser_type != "firefox":  # Firefox URL is already added in args
                command.append("about:blank")
                
            # Start browser process
            self.browser_process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Wait for the process to start
            await asyncio.sleep(3)
            
            # Check if process is still running
            if self.browser_process.poll() is not None:
                logger.error(f"Browser process exited with code: {self.browser_process.returncode}")
                return False
                
            # Check if debugging port is open
            for _ in range(5):  # Try 5 times
                try:
                    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                        s.settimeout(5)
                        result = s.connect_ex(("localhost", self.remote_debugging_port))
                        if result == 0:
                            self.is_running = True
                            logger.info(f"{self.browser_type} browser started successfully in sandbox mode")
                            logger.info(f"Remote debugging available at: http://localhost:{self.remote_debugging_port}")
                            return True
                except Exception:
                    pass
                    
                await asyncio.sleep(1)
                
            logger.warning(f"Browser debugging port {self.remote_debugging_port} is not open")
            return False
                
        except Exception as e:
            logger.exception(f"Error starting browser in sandbox: {e}")
            return False
            
    async def stop(self) -> bool:
        """Stop the sandboxed browser."""
        if not self.is_running:
            logger.info("Browser is not running in sandbox")
            return True
            
        logger.info("Stopping sandboxed browser...")
        
        try:
            if self.browser_process:
                self.browser_process.terminate()
                await asyncio.sleep(2)
                
                # Force kill if still running
                if self.browser_process.poll() is None:
                    self.browser_process.kill()
                    
                self.browser_process = None
                
            self.is_running = False
            logger.info("Sandboxed browser stopped")
            
            return True
            
        except Exception as e:
            logger.exception(f"Error stopping sandboxed browser: {e}")
            return False
            
    async def cleanup(self) -> bool:
        """Clean up sandbox resources."""
        logger.info("Cleaning up browser sandbox resources...")
        
        try:
            # Stop browser if running
            if self.is_running:
                await self.stop()
                
            # Remove temporary directory
            if self.temp_dir and os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
                logger.info(f"Removed temporary directory: {self.temp_dir}")
                
            return True
            
        except Exception as e:
            logger.exception(f"Error cleaning up browser sandbox: {e}")
            return False
            
    def get_debugging_url(self) -> str:
        """Get the browser remote debugging URL."""
        return f"http://localhost:{self.remote_debugging_port}"
        
    async def is_port_open(self) -> bool:
        """Check if the remote debugging port is open."""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(5)
                result = s.connect_ex(("localhost", self.remote_debugging_port))
                return result == 0
        except Exception:
            return False
