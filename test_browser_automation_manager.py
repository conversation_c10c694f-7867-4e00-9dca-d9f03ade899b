"""
Test Browser Automation Manager.

This script tests the browser automation manager with both UI-TARS and Midscene.
"""
import os
import sys
import json
import asyncio
import logging
import time
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent))

try:
    from core.logger import setup_logger
    from ui_tars.browser_automation_manager import BrowserAutomationManager, AutomationProvider
except ImportError:
    # Fallback logging setup if core.logger is not available
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler("test_browser_automation_manager.log")
        ]
    )

    def setup_logger(name):
        return logging.getLogger(name)

# Set up logger
logger = setup_logger("test_browser_automation_manager")

async def load_config():
    """
    Load configuration from file.
    
    Returns:
        dict: Configuration dictionary
    """
    config_path = Path("ui_tars/config.json")
    if not config_path.exists():
        logger.error(f"Configuration file not found: {config_path}")
        return {}
    
    try:
        import json
        with open(config_path, "r") as f:
            config = json.load(f)
        
        logger.info(f"Configuration loaded from {config_path}")
        return config
    except Exception as e:
        logger.error(f"Error loading configuration: {e}")
        return {}

async def test_browser_automation_manager():
    """
    Test the browser automation manager.
    
    Returns:
        bool: True if successful, False otherwise
    """
    logger.info("Testing browser automation manager")
    
    # Load configuration
    config = await load_config()
    if not config:
        logger.error("Failed to load configuration")
        return False
    
    # Create browser automation manager
    manager = BrowserAutomationManager(
        config=config,
        provider=AutomationProvider.AUTO,
        auto_start=True,
        auto_restart=True,
        auto_fallback=True
    )
    
    try:
        # Initialize manager
        logger.info("Initializing browser automation manager")
        success = await manager.initialize()
        if not success:
            logger.error("Failed to initialize browser automation manager")
            return False
        
        # Check active provider
        logger.info(f"Active provider: {manager.active_provider.value}")
        
        # Perform health check
        logger.info("Performing health check")
        health = await manager.health_check()
        logger.info(f"Health check result: {health['status']}")
        
        if health["status"] == "unhealthy":
            logger.warning(f"Health check issues: {health['issues']}")
            
            # Try to repair
            logger.info("Attempting to repair")
            repair_result = await manager.auto_repair()
            logger.info(f"Repair result: {repair_result['success']}")
            
            if not repair_result["success"]:
                logger.error(f"Failed to repair: {repair_result['message']}")
                return False
        
        # Execute a simple command
        logger.info("Executing command")
        result = await manager.execute_command("Browse to https://www.google.com")
        
        if "error" in result:
            logger.error(f"Failed to execute command: {result['error']}")
            return False
        
        logger.info("Command executed successfully")
        
        # Wait a moment
        await asyncio.sleep(5)
        
        # Execute another command
        logger.info("Executing another command")
        result = await manager.execute_command("Take a screenshot")
        
        if "error" in result:
            logger.error(f"Failed to execute command: {result['error']}")
            return False
        
        logger.info("Command executed successfully")
        
        # Test fallback
        if manager.active_provider == AutomationProvider.UI_TARS:
            logger.info("Testing fallback to Midscene")
            
            # Stop UI-TARS to force fallback
            await manager.ui_tars_connector.stop()
            
            # Execute command (should fallback to Midscene)
            result = await manager.execute_command("Browse to https://www.example.com")
            
            if "error" in result:
                logger.error(f"Fallback failed: {result['error']}")
            else:
                logger.info(f"Fallback succeeded, new active provider: {manager.active_provider.value}")
        
        elif manager.active_provider == AutomationProvider.MIDSCENE:
            logger.info("Testing fallback to UI-TARS")
            
            # Stop Midscene to force fallback
            await manager.midscene_connector.stop()
            
            # Execute command (should fallback to UI-TARS)
            result = await manager.execute_command("Browse to https://www.example.com")
            
            if "error" in result:
                logger.error(f"Fallback failed: {result['error']}")
            else:
                logger.info(f"Fallback succeeded, new active provider: {manager.active_provider.value}")
        
        # Stop manager
        logger.info("Stopping browser automation manager")
        await manager.stop()
        
        logger.info("Browser automation manager test completed successfully")
        return True
    
    except Exception as e:
        logger.exception(f"Error testing browser automation manager: {e}")
        
        # Try to stop manager
        try:
            await manager.stop()
        except:
            pass
        
        return False

async def main():
    """Main entry point for the script."""
    print("Browser Automation Manager Test")
    print("==============================")
    print()
    
    try:
        # Test browser automation manager
        success = await test_browser_automation_manager()
        
        if success:
            print("\nBrowser automation manager test successful")
            return 0
        else:
            print("\nBrowser automation manager test failed")
            return 1
    
    except Exception as e:
        logger.exception(f"Error in main: {e}")
        print(f"Error: {e}")
        return 1

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nTest cancelled")
        sys.exit(0)
