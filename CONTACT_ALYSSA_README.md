# Contact Alyssa Test

This package provides tools to test contacting <PERSON><PERSON>, demonstrating that the system is working properly.

## Methods Available

### 1. Direct Email Method

The direct email method uses SMTP to send an email to Alyssa directly, without relying on UI-TARS or browser automation.

```
contact_alyssa_direct.bat
```

This will:
1. Ask for Gmail credentials (<NAME_EMAIL>)
2. Ask for recipient email (<NAME_EMAIL>)
3. Ask if you want to perform a dry run (don't actually send the email)
4. Send an email to Alyssa with information about IUL policy and health insurance options

### 2. UI-TARS Browser Automation Method

The UI-TARS browser automation method uses UI-TARS to automate the browser and send an email to <PERSON><PERSON> through Gmail's web interface.

```
contact_alyssa.bat
```

This will:
1. Check if UI-TARS is running, and start it if needed
2. Ask for Gmail credentials (<NAME_EMAIL>)
3. Ask if you want to perform a dry run (don't actually send the email)
4. Use UI-TARS to navigate to Gmail, login, compose an email to <PERSON><PERSON>, and send it

## Email Template

The email template used for contacting <PERSON><PERSON> is as follows:

```
Dear Alyssa,

Thank you for your interest in our insurance products. Based on your $100/month budget, I'd like to discuss some options for an Indexed Universal Life (IUL) policy structured for maximum cash value growth, along with basic health, dental, and vision plans.

Here's what I'm thinking:

1. IUL Policy: We can structure this for optimal cash value growth while maintaining the life insurance benefit. This would be approximately $60-70 of your monthly budget.

2. Health Insurance: For the remaining $30-40, we can look at basic health plans that cover essential services.

3. Dental & Vision: We have some affordable options that can be added if your budget allows, or we can discuss slightly exceeding your budget if these are priorities for you.

Would you be available for a quick call to discuss these options in more detail? I can answer any questions you might have and provide specific policy recommendations based on your needs.

Please let me know what days and times work best for you.

Best regards,
Paul Edwards
Flo Faction Insurance
Phone: (*************
Email: <EMAIL>
```

## Troubleshooting

### Direct Email Method

If the direct email method fails:

1. Make sure you have the correct Gmail credentials
2. Check if "Less secure app access" is enabled in your Google account
3. Try using an App Password instead of your regular password
4. Check the logs in `contact_alyssa_direct.log`

### UI-TARS Browser Automation Method

If the UI-TARS browser automation method fails:

1. Make sure UI-TARS is running properly with browser integration
2. Run `ui_tars_complete_solution.bat` to fix any UI-TARS issues
3. Check if Chrome is running with remote debugging enabled
4. Check the logs in `contact_alyssa.log`

## Requirements

- Python 3.8 or higher
- UI-TARS 1.5 (for browser automation method)
- Chrome browser (for browser automation method)
- Gmail account with correct credentials

## License

This software is licensed under the Apache License, Version 2.0. See the LICENSE file for the full license text.
