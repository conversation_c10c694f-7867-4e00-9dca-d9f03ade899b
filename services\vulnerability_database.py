"""
Vulnerability Database Service for the Cybersecurity Agent.

This service provides access to vulnerability databases like NVD (National Vulnerability Database)
and CVE (Common Vulnerabilities and Exposures) to retrieve information about known vulnerabilities.
"""
import asyncio
import aiohttp
import json
import os
import time
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import logging
from pathlib import Path

from core.logger import setup_logger

# Set up logger
logger = setup_logger("services.vulnerability_database")

class VulnerabilityDatabase:
    """
    Service for accessing vulnerability databases.

    This service provides methods for retrieving information about known vulnerabilities
    from various vulnerability databases.
    """

    def __init__(self, config: Dict):
        """
        Initialize the vulnerability database service.

        Args:
            config (Dict): Service configuration
        """
        self.config = config
        self.enabled = config.get("enabled", False)
        self.api_key = config.get("api_key", "")
        self.cache_dir = Path(config.get("cache_dir", "data/vulnerability_cache"))
        self.cache_ttl = config.get("cache_ttl", 86400)  # 24 hours in seconds

        # Create cache directory if it doesn't exist
        self.cache_dir.mkdir(parents=True, exist_ok=True)

        # Initialize session
        self.session = None

    async def initialize(self):
        """Initialize the vulnerability database service."""
        self.session = aiohttp.ClientSession()

    async def close(self):
        """Close the vulnerability database service."""
        if self.session:
            await self.session.close()
            self.session = None

    def is_enabled(self) -> bool:
        """
        Check if the vulnerability database service is enabled.

        Returns:
            bool: True if enabled, False otherwise
        """
        return self.enabled

    async def search_cve(self, cve_id: str) -> Dict:
        """
        Search for a CVE by ID.

        Args:
            cve_id (str): CVE ID (e.g., CVE-2021-44228)

        Returns:
            Dict: CVE information
        """
        logger.info(f"Searching for CVE: {cve_id}")

        # Check cache
        cache_file = self.cache_dir / f"cve_{cve_id}.json"
        if cache_file.exists():
            # Check if cache is still valid
            cache_time = cache_file.stat().st_mtime
            if time.time() - cache_time < self.cache_ttl:
                # Cache is still valid
                logger.info(f"Using cached data for CVE: {cve_id}")
                try:
                    with open(cache_file, "r") as f:
                        return json.load(f)
                except Exception as e:
                    logger.warning(f"Error reading cache for CVE {cve_id}: {e}")

        # Cache is invalid or doesn't exist, fetch from API
        try:
            # Use NVD API (updated to 2.0 as of May 2025)
            url = f"https://services.nvd.nist.gov/rest/json/cve/2.0/{cve_id}"
            headers = {}

            if self.api_key:
                headers["apiKey"] = self.api_key

            async with self.session.get(url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()

                    # Cache the result
                    try:
                        with open(cache_file, "w") as f:
                            json.dump(data, f)
                    except Exception as e:
                        logger.warning(f"Error caching CVE {cve_id}: {e}")

                    return data
                else:
                    error_text = await response.text()
                    logger.error(f"Error searching for CVE {cve_id}: {response.status} - {error_text}")
                    return {"error": f"API error: {response.status}", "details": error_text}

        except Exception as e:
            logger.exception(f"Error searching for CVE {cve_id}: {e}")
            return {"error": f"Failed to search for CVE: {str(e)}"}

    async def search_vulnerabilities(self, query: str, max_results: int = 10) -> Dict:
        """
        Search for vulnerabilities by keyword.

        Args:
            query (str): Search query
            max_results (int): Maximum number of results to return

        Returns:
            Dict: Search results
        """
        logger.info(f"Searching for vulnerabilities: {query}")

        # Check cache
        cache_key = query.lower().replace(" ", "_")
        cache_file = self.cache_dir / f"search_{cache_key}.json"
        if cache_file.exists():
            # Check if cache is still valid
            cache_time = cache_file.stat().st_mtime
            if time.time() - cache_time < self.cache_ttl:
                # Cache is still valid
                logger.info(f"Using cached data for search: {query}")
                try:
                    with open(cache_file, "r") as f:
                        return json.load(f)
                except Exception as e:
                    logger.warning(f"Error reading cache for search {query}: {e}")

        # Cache is invalid or doesn't exist, fetch from API
        try:
            # Use NVD API (updated to 2.0 as of May 2025)
            url = "https://services.nvd.nist.gov/rest/json/cves/2.0"
            params = {
                "keywordSearch": query,  # Updated parameter name for API 2.0
                "resultsPerPage": max_results,
            }
            headers = {}

            if self.api_key:
                headers["apiKey"] = self.api_key

            async with self.session.get(url, params=params, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()

                    # Cache the result
                    try:
                        with open(cache_file, "w") as f:
                            json.dump(data, f)
                    except Exception as e:
                        logger.warning(f"Error caching search {query}: {e}")

                    return data
                else:
                    error_text = await response.text()
                    logger.error(f"Error searching for vulnerabilities: {response.status} - {error_text}")
                    return {"error": f"API error: {response.status}", "details": error_text}

        except Exception as e:
            logger.exception(f"Error searching for vulnerabilities: {e}")
            return {"error": f"Failed to search for vulnerabilities: {str(e)}"}

    async def get_cve_details(self, cve_id: str) -> Dict:
        """
        Get detailed information about a CVE.

        Args:
            cve_id (str): CVE ID (e.g., CVE-2021-44228)

        Returns:
            Dict: CVE details
        """
        # Search for the CVE
        cve_data = await self.search_cve(cve_id)

        if "error" in cve_data:
            return cve_data

        # Extract relevant information
        try:
            # API 2.0 has a different response format
            vulnerabilities = cve_data.get("vulnerabilities", [])
            if not vulnerabilities:
                return {"error": f"No vulnerability data found for {cve_id}"}

            cve_item = vulnerabilities[0].get("cve", {})

            # Get CVE information
            descriptions = cve_item.get("descriptions", [])
            description_text = ""
            for desc in descriptions:
                if desc.get("lang") == "en":
                    description_text = desc.get("value", "")
                    break

            # Get metrics information
            metrics = cve_item.get("metrics", {})
            cvss_v31 = metrics.get("cvssMetricV31", [{}])[0].get("cvssData", {}) if metrics.get("cvssMetricV31") else {}
            cvss_v30 = metrics.get("cvssMetricV30", [{}])[0].get("cvssData", {}) if metrics.get("cvssMetricV30") else {}
            cvss_v2 = metrics.get("cvssMetricV2", [{}])[0].get("cvssData", {}) if metrics.get("cvssMetricV2") else {}

            # Get the best CVSS v3 data available
            cvss_v3 = cvss_v31 if cvss_v31 else cvss_v30

            # Get references
            references = cve_item.get("references", [])
            reference_urls = [ref.get("url", "") for ref in references]

            # Create detailed CVE information
            cve_details = {
                "cve_id": cve_id,
                "description": description_text,
                "published_date": cve_item.get("published", ""),
                "last_modified_date": cve_item.get("lastModified", ""),
                "cvss_v3": {
                    "base_score": cvss_v3.get("baseScore", 0),
                    "vector_string": cvss_v3.get("vectorString", ""),
                    "severity": cvss_v3.get("baseSeverity", ""),
                },
                "cvss_v2": {
                    "base_score": cvss_v2.get("baseScore", 0),
                    "vector_string": cvss_v2.get("vectorString", ""),
                    "severity": cvss_v2.get("baseSeverity", ""),
                },
                "references": reference_urls,
            }

            return cve_details

        except Exception as e:
            logger.exception(f"Error extracting CVE details for {cve_id}: {e}")
            return {"error": f"Failed to extract CVE details: {str(e)}"}

    async def get_recent_vulnerabilities(self, days: int = 30, max_results: int = 10) -> Dict:
        """
        Get recent vulnerabilities.

        Args:
            days (int): Number of days to look back
            max_results (int): Maximum number of results to return

        Returns:
            Dict: Recent vulnerabilities
        """
        logger.info(f"Getting recent vulnerabilities from the last {days} days")

        # Check cache
        cache_file = self.cache_dir / f"recent_{days}_{max_results}.json"
        if cache_file.exists():
            # Check if cache is still valid
            cache_time = cache_file.stat().st_mtime
            if time.time() - cache_time < 86400:  # 1 day in seconds
                # Cache is still valid
                logger.info(f"Using cached data for recent vulnerabilities")
                try:
                    with open(cache_file, "r") as f:
                        return json.load(f)
                except Exception as e:
                    logger.warning(f"Error reading cache for recent vulnerabilities: {e}")

        # Cache is invalid or doesn't exist, fetch from API
        try:
            # Calculate date range
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)

            # Format dates for API
            start_date_str = start_date.strftime("%Y-%m-%dT00:00:00:000 UTC-00:00")
            end_date_str = end_date.strftime("%Y-%m-%dT23:59:59:999 UTC-00:00")

            # Use NVD API (updated to 2.0 as of May 2025)
            url = "https://services.nvd.nist.gov/rest/json/cves/2.0"
            params = {
                "pubStartDate": start_date_str,
                "pubEndDate": end_date_str,
                "resultsPerPage": max_results,
            }
            headers = {}

            if self.api_key:
                headers["apiKey"] = self.api_key

            async with self.session.get(url, params=params, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()

                    # Cache the result
                    try:
                        with open(cache_file, "w") as f:
                            json.dump(data, f)
                    except Exception as e:
                        logger.warning(f"Error caching recent vulnerabilities: {e}")

                    return data
                else:
                    error_text = await response.text()
                    logger.error(f"Error getting recent vulnerabilities: {response.status} - {error_text}")
                    return {"error": f"API error: {response.status}", "details": error_text}

        except Exception as e:
            logger.exception(f"Error getting recent vulnerabilities: {e}")
            return {"error": f"Failed to get recent vulnerabilities: {str(e)}"}
