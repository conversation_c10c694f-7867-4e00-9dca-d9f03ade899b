"""
Simple Email Sender

A simple script to send emails using the Gmail browser automation.
This script uses stored <NAME_EMAIL>.
"""
import sys
from agent_email_demo import EmailAgent

def main():
    """Send an email using the agent system."""
    # Default values
    from_email = "<EMAIL>"
    to_email = "<EMAIL>"
    subject = "Test Email from AI Agent System"
    body = "This is a test email sent using browser automation with stored credentials."
    
    # Get command line arguments if provided
    if len(sys.argv) > 1:
        to_email = sys.argv[1]
    if len(sys.argv) > 2:
        subject = sys.argv[2]
    if len(sys.argv) > 3:
        body = sys.argv[3]
    
    print(f"Sending email from {from_email} to {to_email}")
    print(f"Subject: {subject}")
    print(f"Body: {body}")
    
    # Create and initialize the agent
    agent = EmailAgent()
    initialized = agent.initialize()
    
    if not initialized:
        print("Failed to initialize Email Agent")
        sys.exit(1)
    
    try:
        # Send the email using stored credentials
        result = agent.send_email(
            from_email=from_email,
            to_email=to_email,
            subject=subject,
            body=body
        )
        
        if result["success"]:
            print("Email sent successfully")
            sys.exit(0)
        else:
            print(f"Failed to send email: {result['error']}")
            sys.exit(1)
    
    finally:
        # Shut down the agent
        agent.shutdown()

if __name__ == "__main__":
    main()
