"""
Browser Utilities for UI-TARS.

This module provides utility functions for browser automation with UI-TARS.
"""
import os
import sys
import json
import asyncio
import logging
import subprocess
from typing import Dict, List, Optional, Any, Union
import platform
import webbrowser
import re
import urllib.parse

from core.logger import setup_logger

# Set up logger
logger = setup_logger("ui_tars_browser_utils")

def get_installed_browsers() -> List[str]:
    """
    Get a list of installed browsers on the system.
    
    Returns:
        List[str]: List of installed browsers
    """
    browsers = []
    os_type = platform.system()
    
    if os_type == "Windows":
        # Check for common browsers on Windows
        paths = [
            (r"C:\Program Files\Google\Chrome\Application\chrome.exe", "chrome"),
            (r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe", "chrome"),
            (r"C:\Program Files\Mozilla Firefox\firefox.exe", "firefox"),
            (r"C:\Program Files (x86)\Mozilla Firefox\firefox.exe", "firefox"),
            (r"C:\Program Files\Microsoft\Edge\Application\msedge.exe", "edge"),
            (r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe", "edge"),
            (r"C:\Program Files\Opera\launcher.exe", "opera"),
            (r"C:\Program Files (x86)\Opera\launcher.exe", "opera"),
            (r"C:\Program Files\BraveSoftware\Brave-Browser\Application\brave.exe", "brave"),
            (r"C:\Program Files (x86)\BraveSoftware\Brave-Browser\Application\brave.exe", "brave"),
        ]
        
        for path, browser in paths:
            if os.path.exists(path):
                browsers.append(browser)
    
    elif os_type == "Darwin":  # macOS
        # Check for common browsers on macOS
        paths = [
            ("/Applications/Google Chrome.app", "chrome"),
            ("/Applications/Firefox.app", "firefox"),
            ("/Applications/Safari.app", "safari"),
            ("/Applications/Microsoft Edge.app", "edge"),
            ("/Applications/Opera.app", "opera"),
            ("/Applications/Brave Browser.app", "brave"),
        ]
        
        for path, browser in paths:
            if os.path.exists(path):
                browsers.append(browser)
    
    else:  # Linux
        # Check for common browsers on Linux using 'which' command
        for browser in ["google-chrome", "firefox", "chromium-browser", "opera", "brave-browser"]:
            try:
                result = subprocess.run(["which", browser], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                if result.returncode == 0:
                    browsers.append(browser.replace("google-", "").replace("-browser", ""))
            except Exception:
                pass
    
    return browsers

def format_url(url: str) -> str:
    """
    Format a URL to ensure it's valid.
    
    Args:
        url (str): URL to format
        
    Returns:
        str: Formatted URL
    """
    # Add http:// if no protocol is specified
    if not re.match(r'^https?://', url):
        url = 'http://' + url
    
    # Encode URL
    parsed = urllib.parse.urlparse(url)
    path = urllib.parse.quote(parsed.path)
    
    # Reconstruct URL
    formatted_url = urllib.parse.urlunparse((
        parsed.scheme,
        parsed.netloc,
        path,
        parsed.params,
        parsed.query,
        parsed.fragment
    ))
    
    return formatted_url

def open_browser(url: str, browser: Optional[str] = None) -> bool:
    """
    Open a URL in a browser.
    
    Args:
        url (str): URL to open
        browser (Optional[str]): Browser to use
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Format the URL
        formatted_url = format_url(url)
        
        # Open the URL in the specified browser
        if browser:
            webbrowser.get(browser).open(formatted_url)
        else:
            webbrowser.open(formatted_url)
        
        return True
    
    except Exception as e:
        logger.exception(f"Error opening browser: {e}")
        return False

def get_search_url(query: str, engine: str = "google") -> str:
    """
    Get a search URL for the given query and engine.
    
    Args:
        query (str): Search query
        engine (str): Search engine to use
        
    Returns:
        str: Search URL
    """
    # Encode the query
    encoded_query = urllib.parse.quote(query)
    
    # Get the search URL based on the engine
    if engine.lower() == "google":
        return f"https://www.google.com/search?q={encoded_query}"
    elif engine.lower() == "bing":
        return f"https://www.bing.com/search?q={encoded_query}"
    elif engine.lower() == "duckduckgo":
        return f"https://duckduckgo.com/?q={encoded_query}"
    elif engine.lower() == "yahoo":
        return f"https://search.yahoo.com/search?p={encoded_query}"
    elif engine.lower() == "baidu":
        return f"https://www.baidu.com/s?wd={encoded_query}"
    elif engine.lower() == "yandex":
        return f"https://yandex.com/search/?text={encoded_query}"
    elif engine.lower() == "sogou":
        return f"https://www.sogou.com/web?query={encoded_query}"
    else:
        # Default to Google
        return f"https://www.google.com/search?q={encoded_query}"

async def search_web(query: str, engine: str = "google", browser: Optional[str] = None) -> bool:
    """
    Search the web for the given query.
    
    Args:
        query (str): Search query
        engine (str): Search engine to use
        browser (Optional[str]): Browser to use
        
    Returns:
        bool: True if successful, False otherwise
    """
    # Get the search URL
    url = get_search_url(query, engine)
    
    # Open the URL in the browser
    return open_browser(url, browser)
