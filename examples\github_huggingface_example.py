"""
Example script demonstrating how to use GitHub and Hugging Face integrations.
"""
import sys
import asyncio
import json
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

from services.github_service import GitHubServiceFactory
from services.huggingface_service import HuggingFaceServiceFactory
from llm.huggingface_connector import HuggingfaceConnector
from core.logger import setup_logger
import config

# Set up logger
logger = setup_logger("github_huggingface_example")

async def github_example():
    """Example of using the GitHub service."""
    print("\n=== GitHub Integration Example ===\n")
    
    # Create GitHub service
    github_service = GitHubServiceFactory.create_service()
    
    if not github_service or not github_service.is_enabled():
        print("GitHub service is not enabled. Please check your .env file.")
        return
    
    # Search for repositories
    print("Searching for multi-agent repositories...")
    search_results = await github_service.search_repositories("multi-agent system", "python", "stars")
    
    if "error" in search_results:
        print(f"Error: {search_results['error']}")
        return
    
    # Print search results
    print(f"Found {search_results.get('total_count', 0)} repositories")
    
    # Get the top repository
    if search_results.get("items") and len(search_results["items"]) > 0:
        top_repo = search_results["items"][0]
        repo_name = top_repo.get("full_name")
        
        print(f"\nTop repository: {repo_name}")
        print(f"Description: {top_repo.get('description')}")
        print(f"Stars: {top_repo.get('stargazers_count')}")
        print(f"URL: {top_repo.get('html_url')}")
        
        # Get repository structure
        print("\nGetting repository structure...")
        owner, repo = repo_name.split("/")
        structure = await github_service.get_repository_structure(owner, repo)
        
        if "error" in structure:
            print(f"Error: {structure['error']}")
        else:
            print("\nRepository structure (top level):")
            for item in structure.get("items", [])[:5]:  # Show top 5 items
                print(f"- [{item.get('type')}] {item.get('name')}")
            
            # Get a Python file
            python_files = [item for item in structure.get("items", []) 
                           if item.get("type") == "file" and item.get("name", "").endswith(".py")]
            
            if python_files:
                file_path = python_files[0].get("path")
                print(f"\nGetting file content: {file_path}")
                
                file_content = await github_service.get_file_content(owner, repo, file_path)
                
                if "error" in file_content:
                    print(f"Error: {file_content['error']}")
                else:
                    content = file_content.get("content", "")
                    preview = content[:500] + "..." if len(content) > 500 else content
                    print("\nFile content preview:")
                    print(preview)

async def huggingface_example():
    """Example of using the Hugging Face service."""
    print("\n=== Hugging Face Integration Example ===\n")
    
    # Create Hugging Face service
    huggingface_service = HuggingFaceServiceFactory.create_service()
    
    if not huggingface_service or not huggingface_service.is_enabled():
        print("Hugging Face service is not enabled. Please check your .env file.")
        return
    
    # Search for models
    print("Searching for text-to-image models...")
    search_results = await huggingface_service.search_models("text-to-image", "text-to-image")
    
    if "error" in search_results:
        print(f"Error: {search_results['error']}")
        return
    
    # Print search results
    print(f"Found {len(search_results)} models")
    
    # Get the top model
    if search_results and len(search_results) > 0:
        top_model = search_results[0]
        model_id = top_model.get("id")
        
        print(f"\nTop model: {model_id}")
        print(f"Downloads: {top_model.get('downloads')}")
        print(f"Likes: {top_model.get('likes')}")
        
        # Get model card
        print("\nGetting model card...")
        model_card = await huggingface_service.get_model_card(model_id)
        
        if "error" in model_card:
            print(f"Error: {model_card['error']}")
        else:
            print(f"\nModel tags: {', '.join(model_card.get('tags', [])[:5])}")
            
            # Print README preview
            readme = model_card.get("readme", "")
            preview = readme[:500] + "..." if len(readme) > 500 else readme
            print("\nREADME preview:")
            print(preview)

async def huggingface_llm_example():
    """Example of using the Hugging Face LLM connector."""
    print("\n=== Hugging Face LLM Example ===\n")
    
    # Create Hugging Face connector
    config_data = {
        "api_key": config.HUGGINGFACE_CONFIG.get("api_key", ""),
        "models": ["mistralai/Mistral-7B-Instruct-v0.2", "meta-llama/Llama-2-7b-chat-hf"],
        "default_model": "mistralai/Mistral-7B-Instruct-v0.2",
        "enabled": config.HUGGINGFACE_CONFIG.get("enabled", False),
        "use_local": config.HUGGINGFACE_CONFIG.get("use_local", False),
        "local_url": config.HUGGINGFACE_CONFIG.get("local_url", "http://localhost:8080"),
    }
    
    huggingface_connector = HuggingfaceConnector(config_data)
    
    if not huggingface_connector.enabled:
        print("Hugging Face connector is not enabled. Please check your .env file.")
        return
    
    # Generate text
    print("Generating text with Hugging Face model...")
    
    prompt = "Explain what a multi-agent AI system is and how it can be used in business."
    
    response = await huggingface_connector.generate_text(
        prompt=prompt,
        max_tokens=500,
        temperature=0.7
    )
    
    if "error" in response:
        print(f"Error: {response['error']}")
        return
    
    # Print response
    print("\nPrompt:")
    print(prompt)
    print("\nResponse:")
    print(response.get("text", ""))
    print(f"\nModel: {response.get('model')}")
    print(f"Latency: {response.get('latency', 0):.2f} seconds")

async def main():
    """Main function."""
    try:
        # Run GitHub example
        await github_example()
        
        # Run Hugging Face example
        await huggingface_example()
        
        # Run Hugging Face LLM example
        await huggingface_llm_example()
        
    except Exception as e:
        logger.exception(f"Error in example: {e}")

if __name__ == "__main__":
    asyncio.run(main())
