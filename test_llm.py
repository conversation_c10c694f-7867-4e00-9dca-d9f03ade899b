"""
<PERSON><PERSON><PERSON> to test LLM integration.
"""
import sys
import asyncio
import argparse
from pathlib import Path

from llm.llm_router import LLMRouter
from core.logger import setup_logger
import config

# Set up logger
logger = setup_logger("test_llm")

async def test_llm(provider: str = None, model: str = None, prompt: str = None):
    """
    Test LLM integration.
    
    Args:
        provider (str, optional): LLM provider to use
        model (str, optional): Model to use
        prompt (str, optional): Prompt to send to the LLM
    """
    logger.info("Testing LLM integration")
    
    # Initialize LLM router
    llm_router = LLMRouter()
    await llm_router.initialize()
    
    # Get available providers
    available_providers = llm_router.get_available_providers()
    logger.info(f"Available LLM providers: {available_providers}")
    
    if not available_providers:
        logger.error("No LLM providers available")
        return False
    
    # Use default provider if not specified
    if not provider:
        provider = llm_router.get_default_provider()
        logger.info(f"Using default provider: {provider}")
    elif provider not in available_providers:
        logger.error(f"Provider {provider} not available")
        logger.info(f"Available providers: {available_providers}")
        return False
    
    # Get available models for the provider
    available_models = llm_router.get_available_models(provider)
    logger.info(f"Available models for {provider}: {available_models.get(provider, [])}")
    
    # Use default prompt if not specified
    if not prompt:
        prompt = "Explain what a multi-agent AI system is in one paragraph."
        logger.info(f"Using default prompt: {prompt}")
    
    # Generate text
    logger.info(f"Generating text with provider: {provider}, model: {model or 'default'}")
    response = await llm_router.generate_text(
        prompt=prompt,
        provider=provider,
        model=model,
        max_tokens=500,
        temperature=0.7
    )
    
    # Check for errors
    if "error" in response:
        logger.error(f"Error generating text: {response['error']}")
        return False
    
    # Print response
    logger.info("LLM Response:")
    print("\n" + "-" * 80)
    print(response.get("text", ""))
    print("-" * 80 + "\n")
    
    # Print metadata
    logger.info(f"Provider: {response.get('provider')}")
    logger.info(f"Model: {response.get('model')}")
    logger.info(f"Latency: {response.get('latency', 0):.2f} seconds")
    
    return True

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Test LLM integration")
    parser.add_argument("--provider", help="LLM provider to use")
    parser.add_argument("--model", help="Model to use")
    parser.add_argument("--prompt", help="Prompt to send to the LLM")
    args = parser.parse_args()
    
    # Run test
    success = asyncio.run(test_llm(args.provider, args.model, args.prompt))
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
