"""
Multi-Account Email Agent for handling email communications across multiple accounts with reasoning capabilities.
This agent can read, analyze, and respond to emails from multiple Gmail accounts with context-aware reasoning.
"""
import os
import json
import asyncio
import re
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import logging
import uuid

from core.logger import setup_logger
from core.state_manager import StateManager
from services.multi_account_gmail_service import MultiAccountGmailServiceFactory
from llm.llm_router import LLMRouter
from agents.base_agent import BaseAgent, MessagePriority

# Set up logger
logger = setup_logger("multi_account_email_agent")

class MultiAccountEmailAgent(BaseAgent):
    """
    Agent for handling email communications across multiple accounts with reasoning capabilities.
    """

    def __init__(self,
                 agent_id: str,
                 name: str = "Multi-Account Email Agent",
                 description: str = "Handles email communications across multiple accounts with reasoning capabilities",
                 config: Optional[Dict] = None,
                 message_queue: Optional[asyncio.Queue] = None,
                 state_manager: Optional[StateManager] = None):
        """
        Initialize the Multi-Account Email Agent.

        Args:
            agent_id (str): Unique identifier for the agent
            name (str): Name of the agent
            description (str): Description of the agent
            config (Optional[Dict]): Agent configuration
            message_queue (Optional[asyncio.Queue]): Message queue for communication
            state_manager (Optional[StateManager]): State manager for persistence
        """
        super().__init__(agent_id, name, description, config, message_queue, state_manager)
        self.gmail_service = None

        # Capabilities
        self.capabilities = [
            "list_accounts",
            "read_emails",
            "analyze_email",
            "draft_response",
            "send_email",
            "search_across_accounts"
        ]

    async def initialize(self):
        """Initialize the agent and load its state."""
        await super().initialize()

        # Initialize Gmail service
        self.gmail_service = MultiAccountGmailServiceFactory.create_service()

        # Check if Gmail service is enabled
        if not self.gmail_service.is_enabled():
            self.logger.warning("Multi-Account Gmail Service is not enabled. Email Agent functionality will be limited.")
        else:
            self.logger.info("Multi-Account Gmail Service initialized successfully")

            # Log available accounts
            enabled_accounts = self.gmail_service.get_enabled_accounts()
            self.logger.info(f"Enabled accounts: {', '.join(enabled_accounts)}")

        # Update agent state with capabilities
        self.state["capabilities"] = self.capabilities
        await self._update_state()

    async def handle_command(self, message: Dict):
        """
        Handle a command message.

        Args:
            message (Dict): Command message
        """
        command = message.get("content", {}).get("command")
        data = message.get("content", {}).get("data", {})

        if command == "list_accounts":
            result = await self.list_accounts(
                enabled_only=data.get("enabled_only", True)
            )
            await self.send_message(
                message.get("sender_id"),
                "response",
                {
                    "command": command,
                    "result": result
                },
                in_response_to=message.get("id")
            )

        elif command == "read_emails":
            result = await self.read_emails(
                query=data.get("query"),
                max_results=data.get("max_results", 5),
                priority_only=data.get("priority_only", True)
            )
            await self.send_message(
                message.get("sender_id"),
                "response",
                {
                    "command": command,
                    "result": result
                },
                in_response_to=message.get("id")
            )

        elif command == "analyze_email":
            result = await self.analyze_email(
                account_email=data.get("account_email"),
                email_id=data.get("email_id")
            )
            await self.send_message(
                message.get("sender_id"),
                "response",
                {
                    "command": command,
                    "result": result
                },
                in_response_to=message.get("id")
            )

        elif command == "draft_response":
            result = await self.draft_response(
                account_email=data.get("account_email"),
                email_id=data.get("email_id"),
                response_type=data.get("response_type", "professional"),
                include_reasoning=data.get("include_reasoning", False)
            )
            await self.send_message(
                message.get("sender_id"),
                "response",
                {
                    "command": command,
                    "result": result
                },
                in_response_to=message.get("id")
            )

        elif command == "send_email":
            result = await self.send_email(
                account_email=data.get("account_email"),
                to=data.get("to"),
                subject=data.get("subject"),
                body=data.get("body"),
                cc=data.get("cc"),
                bcc=data.get("bcc")
            )
            await self.send_message(
                message.get("sender_id"),
                "response",
                {
                    "command": command,
                    "result": result
                },
                in_response_to=message.get("id")
            )

        elif command == "search_across_accounts":
            result = await self.search_across_accounts(
                query=data.get("query"),
                max_results_per_account=data.get("max_results_per_account", 5),
                priority_only=data.get("priority_only", True)
            )
            await self.send_message(
                message.get("sender_id"),
                "response",
                {
                    "command": command,
                    "result": result
                },
                in_response_to=message.get("id")
            )

        else:
            await self.send_message(
                message.get("sender_id"),
                "error",
                {
                    "error": f"Unknown command: {command}",
                    "original_message": message.get("id")
                },
                in_response_to=message.get("id")
            )

    async def list_accounts(self, enabled_only: bool = True) -> Dict:
        """
        List all available email accounts.

        Args:
            enabled_only (bool): Whether to only include enabled accounts

        Returns:
            Dict: List of accounts
        """
        if not self.gmail_service or not self.gmail_service.is_enabled():
            return {"error": "Multi-Account Gmail Service is not enabled"}

        try:
            if enabled_only:
                enabled_accounts = self.gmail_service.get_enabled_accounts()
                result = {
                    "accounts": [
                        {
                            "email": email,
                            "info": self.gmail_service.accounts[email]['info'] if email in self.gmail_service.accounts else {}
                        }
                        for email in enabled_accounts
                    ]
                }
            else:
                result = {
                    "accounts": [
                        {
                            "email": email,
                            "info": account.get('info', {}),
                            "enabled": account.get('service', {}).is_enabled() if hasattr(account.get('service', {}), 'is_enabled') else False
                        }
                        for email, account in self.gmail_service.accounts.items()
                    ]
                }

            # Store in memory
            await self.memory.add_memory(
                content={
                    "action": "list_accounts",
                    "enabled_only": enabled_only,
                    "account_count": len(result.get("accounts", [])),
                    "timestamp": datetime.now().isoformat()
                },
                memory_type="episodic",
                source="multi_account_email_agent",
                importance=0.3
            )

            return result

        except Exception as e:
            error_msg = f"Error listing accounts: {str(e)}"
            self.logger.error(error_msg)
            return {"error": error_msg}

    async def read_emails(self, query: Optional[str] = None,
                         max_results: int = 5,
                         priority_only: bool = True) -> Dict:
        """
        Read and analyze emails from multiple accounts.

        Args:
            query (Optional[str]): Search query to filter emails
            max_results (int): Maximum number of emails to retrieve per account
            priority_only (bool): Whether to only include priority accounts

        Returns:
            Dict: Emails from all accounts with analysis
        """
        if not self.gmail_service or not self.gmail_service.is_enabled():
            return {"error": "Multi-Account Gmail Service is not enabled"}

        try:
            # Get LLM router from services
            llm_router = self.get_service("llm_router")
            if not llm_router:
                self.logger.warning("LLM router not available. Email summaries will not be generated.")

            # Get emails from all accounts
            emails_result = await self.gmail_service.list_messages_from_all_accounts(
                query, max_results, None, priority_only
            )

            # Add a summary for each account
            for email, account_results in emails_result.items():
                if "error" not in account_results and account_results.get("messages") and llm_router:
                    summary_prompt = f"""
                    You are an AI assistant analyzing emails for the account {email}.
                    Summarize the following {len(account_results['messages'])} emails:

                    {json.dumps(account_results['messages'], indent=2)}

                    Provide a brief summary of the emails, including:
                    1. How many are unread
                    2. Key senders and topics
                    3. Any emails that might require urgent attention
                    4. Any patterns or trends in the emails
                    """

                    summary_response = await llm_router.generate_text(summary_prompt)
                    account_results["summary"] = summary_response.get("text", "Summary generation failed")

            # Add an overall summary
            if any(["messages" in account_results for _, account_results in emails_result.items() if "error" not in account_results]) and llm_router:
                all_messages_count = sum([
                    len(account_results.get("messages", []))
                    for _, account_results in emails_result.items()
                    if "error" not in account_results
                ])

                overall_summary_prompt = f"""
                You are an AI assistant analyzing emails across multiple accounts.
                Summarize the following information:

                {json.dumps({
                    email: {
                        "message_count": len(account_results.get("messages", [])),
                        "summary": account_results.get("summary", "No summary available")
                    }
                    for email, account_results in emails_result.items()
                    if "error" not in account_results and "messages" in account_results
                }, indent=2)}

                Provide a brief overall summary, including:
                1. Total number of emails across all accounts ({all_messages_count})
                2. Which accounts have the most activity
                3. Any urgent matters that require attention
                4. Recommended actions or priorities
                """

                overall_summary_response = await llm_router.generate_text(overall_summary_prompt)
                emails_result["overall_summary"] = overall_summary_response.get("text", "Overall summary generation failed")

            # Store in memory
            await self.memory.add_memory(
                content={
                    "action": "read_emails_from_all_accounts",
                    "query": query,
                    "priority_only": priority_only,
                    "accounts_checked": list(emails_result.keys()),
                    "total_accounts": len(emails_result),
                    "timestamp": datetime.now().isoformat()
                },
                memory_type="episodic",
                source="multi_account_email_agent",
                importance=0.7
            )

            return emails_result

        except Exception as e:
            error_msg = f"Error reading emails from multiple accounts: {str(e)}"
            self.logger.error(error_msg)
            return {"error": error_msg}

    async def analyze_email(self, account_email: str, email_id: str) -> Dict:
        """
        Analyze a specific email with reasoning.

        Args:
            account_email (str): Email address of the account
            email_id (str): ID of the email to analyze

        Returns:
            Dict: Email analysis with reasoning
        """
        if not self.gmail_service or not self.gmail_service.is_enabled():
            return {"error": "Multi-Account Gmail Service is not enabled"}

        try:
            # Get email
            email_result = await self.gmail_service.get_message(account_email, email_id)

            if "error" in email_result:
                return email_result

            # Get LLM router from services
            llm_router = self.get_service("llm_router")
            if not llm_router:
                self.logger.warning("LLM router not available. Cannot analyze email.")
                email_result["analysis"] = "Analysis not available (LLM router not found)"
                return email_result

            # Analyze the email
            analysis_prompt = f"""
            You are an AI assistant analyzing an email for the account {account_email}.
            Analyze the following email:

            From: {email_result.get('from', 'Unknown')}
            To: {email_result.get('to', 'Unknown')}
            Subject: {email_result.get('subject', 'No subject')}
            Date: {email_result.get('date', 'Unknown')}

            Body:
            {email_result.get('body', 'No body')}

            Provide a detailed analysis of this email, including:
            1. The main purpose or intent of the email
            2. Key points or requests made in the email
            3. The tone and sentiment of the email
            4. Any action items or follow-ups required
            5. Any potential concerns or issues to be aware of
            6. Recommended next steps

            Structure your analysis with clear headings and bullet points where appropriate.
            """

            analysis_response = await llm_router.generate_text(analysis_prompt)

            # Add the analysis to the email result
            email_result["analysis"] = analysis_response.get("text", "Analysis generation failed")

            # Store in memory
            await self.memory.add_memory(
                content={
                    "action": "analyze_email",
                    "account_email": account_email,
                    "email_id": email_id,
                    "subject": email_result.get('subject', 'No subject'),
                    "from": email_result.get('from', 'Unknown'),
                    "timestamp": datetime.now().isoformat()
                },
                memory_type="episodic",
                source="multi_account_email_agent",
                importance=0.6
            )

            return email_result

        except Exception as e:
            error_msg = f"Error analyzing email: {str(e)}"
            self.logger.error(error_msg)
            return {"error": error_msg}

    async def draft_response(self, account_email: str, email_id: str,
                            response_type: str = "professional",
                            include_reasoning: bool = False) -> Dict:
        """
        Draft a response to an email with reasoning.

        Args:
            account_email (str): Email address of the account
            email_id (str): ID of the email to respond to
            response_type (str): Type of response
            include_reasoning (bool): Whether to include reasoning

        Returns:
            Dict: Draft response with reasoning
        """
        if not self.gmail_service or not self.gmail_service.is_enabled():
            return {"error": "Multi-Account Gmail Service is not enabled"}

        try:
            # Get email
            email_result = await self.gmail_service.get_message(account_email, email_id)

            if "error" in email_result:
                return email_result

            # Get LLM router from services
            llm_router = self.get_service("llm_router")
            if not llm_router:
                self.logger.warning("LLM router not available. Cannot draft email response.")
                return {
                    "error": "LLM router not available",
                    "account_email": account_email,
                    "original_email": {
                        "id": email_result.get('id', ''),
                        "from": email_result.get('from', 'Unknown'),
                        "subject": email_result.get('subject', 'No subject')
                    }
                }

            # Draft a response
            response_prompt = f"""
            You are an AI assistant drafting an email response for the account {account_email}.
            Respond to the following email:

            From: {email_result.get('from', 'Unknown')}
            To: {email_result.get('to', 'Unknown')}
            Subject: {email_result.get('subject', 'No subject')}
            Date: {email_result.get('date', 'Unknown')}

            Body:
            {email_result.get('body', 'No body')}

            Draft a {response_type} response to this email. The response should be:
            - Clear and concise
            - Address all points or questions raised in the original email
            - Maintain a {response_type} tone
            - Include appropriate greeting and sign-off
            - Be written as if it's coming from {account_email}

            {"Also include your reasoning for how you crafted this response, explaining your thought process." if include_reasoning else ""}
            """

            response_result = await llm_router.generate_text(response_prompt)
            response_text = response_result.get("text", "Failed to generate response")

            # Extract the response and reasoning if included
            response = response_text
            reasoning = None

            if include_reasoning:
                # Try to separate the response from the reasoning
                # This is a simple approach and might need refinement
                response_pattern = r"(.*?)(?:Reasoning:|Thought process:|My reasoning:|Here's my reasoning:)(.*)"
                match = re.search(response_pattern, response_text, re.DOTALL | re.IGNORECASE)

                if match:
                    response = match.group(1).strip()
                    reasoning = match.group(2).strip()

            # Store in memory
            await self.memory.add_memory(
                content={
                    "action": "draft_response",
                    "account_email": account_email,
                    "email_id": email_id,
                    "response_type": response_type,
                    "include_reasoning": include_reasoning,
                    "subject": email_result.get('subject', 'No subject'),
                    "from": email_result.get('from', 'Unknown'),
                    "timestamp": datetime.now().isoformat()
                },
                memory_type="episodic",
                source="multi_account_email_agent",
                importance=0.7
            )

            return {
                "account_email": account_email,
                "original_email": {
                    "id": email_result.get('id', ''),
                    "from": email_result.get('from', 'Unknown'),
                    "subject": email_result.get('subject', 'No subject')
                },
                "draft_response": response,
                "reasoning": reasoning if include_reasoning else None,
                "response_type": response_type
            }

        except Exception as e:
            error_msg = f"Error drafting email response: {str(e)}"
            self.logger.error(error_msg)
            return {"error": error_msg}

    async def send_email(self, account_email: str, to: str, subject: str, body: str,
                        cc: Optional[str] = None,
                        bcc: Optional[str] = None) -> Dict:
        """
        Send an email from a specific account.

        Args:
            account_email (str): Email address to send from
            to (str): Recipient email address
            subject (str): Email subject
            body (str): Email body
            cc (Optional[str]): CC recipients
            bcc (Optional[str]): BCC recipients

        Returns:
            Dict: Send status
        """
        if not self.gmail_service or not self.gmail_service.is_enabled():
            return {"error": "Multi-Account Gmail Service is not enabled"}

        try:
            # Validate inputs
            if not account_email:
                return {"error": "Sender email address is required"}

            if not to:
                return {"error": "Recipient email address is required"}

            if not subject:
                return {"error": "Email subject is required"}

            if not body:
                return {"error": "Email body is required"}

            # Log the email being sent
            self.logger.info(f"Sending email from {account_email} to {to} with subject: {subject}")

            # Send the email
            result = await self.gmail_service.send_message(account_email, to, subject, body, cc, bcc)

            # Store in memory
            await self.memory.add_memory(
                content={
                    "action": "send_email",
                    "account_email": account_email,
                    "to": to,
                    "subject": subject,
                    "body_preview": body[:100] + "..." if len(body) > 100 else body,
                    "cc": cc,
                    "bcc": bcc,
                    "result": result,
                    "timestamp": datetime.now().isoformat()
                },
                memory_type="episodic",
                source="multi_account_email_agent",
                importance=0.8
            )

            return result

        except Exception as e:
            error_msg = f"Error sending email: {str(e)}"
            self.logger.error(error_msg)
            return {"error": error_msg}

    async def search_across_accounts(self, query: str, max_results_per_account: int = 5,
                                    priority_only: bool = True) -> Dict:
        """
        Search for emails across all accounts.

        Args:
            query (str): Search query
            max_results_per_account (int): Maximum number of results per account
            priority_only (bool): Whether to only include priority accounts

        Returns:
            Dict: Search results from all accounts
        """
        if not self.gmail_service or not self.gmail_service.is_enabled():
            return {"error": "Multi-Account Gmail Service is not enabled"}

        try:
            # Validate inputs
            if not query:
                return {"error": "Search query is required"}

            # Search across accounts
            results = await self.gmail_service.search_across_accounts(query, max_results_per_account, priority_only)

            # Add a summary of the search results
            all_messages = []
            for email, account_results in results.items():
                if "error" not in account_results and "messages" in account_results:
                    for message in account_results["messages"]:
                        message["account"] = email
                        all_messages.append(message)

            # Get LLM router from services
            llm_router = self.get_service("llm_router")

            if all_messages and llm_router:
                summary_prompt = f"""
                You are an AI assistant analyzing search results across multiple email accounts.
                The search query was: "{query}"

                Summarize the following {len(all_messages)} search results:

                {json.dumps(all_messages, indent=2)}

                Provide a brief summary of the search results, including:
                1. Which accounts had the most relevant results
                2. Key senders and topics related to the search query
                3. Any particularly relevant or important emails
                4. Any patterns or trends in the search results
                """

                summary_response = await llm_router.generate_text(summary_prompt)
                results["summary"] = summary_response.get("text", "Summary generation failed")

            # Store in memory
            await self.memory.add_memory(
                content={
                    "action": "search_across_accounts",
                    "query": query,
                    "priority_only": priority_only,
                    "max_results_per_account": max_results_per_account,
                    "accounts_searched": list(results.keys()),
                    "total_results": len(all_messages),
                    "timestamp": datetime.now().isoformat()
                },
                memory_type="episodic",
                source="multi_account_email_agent",
                importance=0.5
            )

            return results

        except Exception as e:
            error_msg = f"Error searching across accounts: {str(e)}"
            self.logger.error(error_msg)
            return {"error": error_msg}

    async def execute_cycle(self):
        """
        Execute a single agent cycle.

        This method is called periodically by the agent manager to allow the agent
        to perform background tasks or periodic checks.
        """
        # Check for new emails if Gmail service is enabled
        if self.gmail_service and self.gmail_service.is_enabled():
            try:
                # Get priority accounts
                priority_accounts = self.gmail_service.get_priority_accounts()

                if not priority_accounts:
                    self.logger.warning("No priority accounts found")
                    return {"status": "completed", "message": "No priority accounts found"}

                # Check for unread emails in priority accounts
                unread_emails_result = await self.read_emails(
                    query="is:unread",
                    max_results=3,
                    priority_only=True
                )

                # Process results
                accounts_with_emails = 0
                total_unread = 0

                for account, account_data in unread_emails_result.get("accounts", {}).items():
                    if "error" not in account_data and "messages" in account_data and account_data["messages"]:
                        accounts_with_emails += 1
                        total_unread += len(account_data["messages"])

                        # Process the first unread email in this account
                        first_email = account_data["messages"][0]
                        self.logger.info(f"Processing email in {account} from {first_email.get('from', 'Unknown')}: {first_email.get('subject', 'No subject')}")

                        # Analyze the email
                        analysis = await self.analyze_email(account, first_email["id"])

                        # Store the analysis in the agent's state
                        if "account_emails" not in self.state:
                            self.state["account_emails"] = {}

                        if account not in self.state["account_emails"]:
                            self.state["account_emails"][account] = {}

                        self.state["account_emails"][account]["last_analyzed_email"] = {
                            "id": first_email["id"],
                            "from": first_email.get("from", "Unknown"),
                            "subject": first_email.get("subject", "No subject"),
                            "analysis": analysis.get("analysis", "No analysis available")
                        }

                        # Update agent state
                        await self._update_state()

                        # Emit an event that can be picked up by workflows
                        await self.emit_event("new_email_analyzed", {
                            "account": account,
                            "email_id": first_email["id"],
                            "from": first_email.get("from", "Unknown"),
                            "subject": first_email.get("subject", "No subject"),
                            "timestamp": datetime.now().isoformat()
                        })

                self.logger.info(f"Found {total_unread} unread emails across {accounts_with_emails} accounts")
                return {
                    "status": "completed",
                    "accounts_with_emails": accounts_with_emails,
                    "total_unread": total_unread
                }

            except Exception as e:
                self.logger.error(f"Error in execute_cycle: {str(e)}")
                return {"status": "error", "error": str(e)}

        return {"status": "completed"}