"""
Test script for Instagram integration.

This script tests the Instagram integration for the Insurance Lead Agent.
"""
import os
import sys
import json
import asyncio
import argparse
from typing import Dict, List, Optional, Any
from datetime import datetime
import aiohttp

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.social_media_service import SocialMediaService
from core.logger import setup_logger

# Set up logger
logger = setup_logger("test_instagram_integration")

async def test_instagram_connection(app_id: str, app_secret: str, access_token: str, business_account_id: str = None):
    """
    Test connection to Instagram API.
    
    Args:
        app_id (str): Instagram App ID (same as Facebook App ID)
        app_secret (str): Instagram App Secret (same as Facebook App Secret)
        access_token (str): Instagram Access Token
        business_account_id (str, optional): Instagram Business Account ID
        
    Returns:
        bool: Success status
    """
    logger.info("Testing Instagram connection...")
    
    # Create configuration
    config = {
        "enabled": True,
        "app_id": app_id,
        "app_secret": app_secret,
        "access_token": access_token,
        "business_account_id": business_account_id
    }
    
    # Create social media service
    social_media_service = SocialMediaService("instagram", config)
    
    try:
        # Check Instagram status
        status = await social_media_service.check_status()
        
        if status.get("status") == "available":
            logger.info("Instagram connection successful!")
            
            # Save credentials
            await save_credentials(app_id, app_secret, access_token, business_account_id)
            
            return True
        else:
            logger.error(f"Instagram connection failed: {status.get('error', 'Unknown error')}")
            return False
    
    except Exception as e:
        logger.exception(f"Error testing Instagram connection: {e}")
        return False

async def get_instagram_business_accounts(access_token: str):
    """
    Get Instagram business accounts.
    
    Args:
        access_token (str): Instagram Access Token
        
    Returns:
        List[Dict]: List of business accounts
    """
    logger.info("Getting Instagram business accounts...")
    
    try:
        async with aiohttp.ClientSession() as session:
            # First, get Facebook pages
            async with session.get(
                f"https://graph.facebook.com/v18.0/me/accounts?access_token={access_token}"
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    pages = data.get("data", [])
                    
                    logger.info(f"Found {len(pages)} Facebook pages")
                    
                    # For each page, get Instagram business account
                    business_accounts = []
                    
                    for page in pages:
                        page_id = page.get("id", "")
                        page_name = page.get("name", "Unknown")
                        page_access_token = page.get("access_token", "")
                        
                        logger.info(f"Checking Instagram business account for page: {page_name}")
                        
                        # Get Instagram business account
                        async with session.get(
                            f"https://graph.facebook.com/v18.0/{page_id}?fields=instagram_business_account&access_token={page_access_token}"
                        ) as ig_response:
                            if ig_response.status == 200:
                                ig_data = await ig_response.json()
                                ig_account = ig_data.get("instagram_business_account", {})
                                
                                if ig_account:
                                    ig_account_id = ig_account.get("id", "")
                                    
                                    logger.info(f"Found Instagram business account: {ig_account_id}")
                                    
                                    # Get account details
                                    async with session.get(
                                        f"https://graph.facebook.com/v18.0/{ig_account_id}?fields=name,username,profile_picture_url&access_token={page_access_token}"
                                    ) as account_response:
                                        if account_response.status == 200:
                                            account_data = await account_response.json()
                                            
                                            business_accounts.append({
                                                "id": ig_account_id,
                                                "name": account_data.get("name", "Unknown"),
                                                "username": account_data.get("username", "Unknown"),
                                                "profile_picture_url": account_data.get("profile_picture_url", ""),
                                                "page_id": page_id,
                                                "page_name": page_name,
                                                "page_access_token": page_access_token
                                            })
                                            
                                            logger.info(f"Instagram account: {account_data.get('username', 'Unknown')}")
                                else:
                                    logger.info(f"No Instagram business account found for page: {page_name}")
                            else:
                                error_data = await ig_response.text()
                                logger.error(f"Failed to get Instagram business account: {error_data}")
                    
                    return business_accounts
                else:
                    error_data = await response.text()
                    logger.error(f"Failed to get Facebook pages: {error_data}")
                    return []
    
    except Exception as e:
        logger.exception(f"Error getting Instagram business accounts: {e}")
        return []

async def get_instagram_messages(business_account_id: str, access_token: str):
    """
    Get Instagram direct messages.
    
    Args:
        business_account_id (str): Instagram Business Account ID
        access_token (str): Instagram Access Token
        
    Returns:
        List[Dict]: List of messages
    """
    logger.info(f"Getting messages for Instagram account {business_account_id}...")
    
    try:
        async with aiohttp.ClientSession() as session:
            # Get conversations
            async with session.get(
                f"https://graph.facebook.com/v18.0/{business_account_id}/conversations?access_token={access_token}"
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    conversations = data.get("data", [])
                    
                    logger.info(f"Found {len(conversations)} conversations")
                    
                    # Get messages for each conversation
                    all_messages = []
                    
                    for conversation in conversations[:3]:  # Limit to 3 conversations
                        conversation_id = conversation.get("id", "")
                        
                        logger.info(f"Getting messages for conversation: {conversation_id}")
                        
                        # Get messages
                        async with session.get(
                            f"https://graph.facebook.com/v18.0/{conversation_id}/messages?access_token={access_token}"
                        ) as msg_response:
                            if msg_response.status == 200:
                                msg_data = await msg_response.json()
                                messages = msg_data.get("data", [])
                                
                                logger.info(f"Found {len(messages)} messages")
                                
                                # Print messages
                                for i, message in enumerate(messages[:5]):  # Limit to 5 messages
                                    message_id = message.get("id", "")
                                    message_text = message.get("message", "")
                                    from_id = message.get("from", {}).get("id", "")
                                    
                                    logger.info(f"{i+1}. Message: {message_text[:50]}...")
                                    logger.info(f"   From: {from_id}, ID: {message_id}")
                                
                                all_messages.extend(messages)
                            else:
                                error_data = await msg_response.text()
                                logger.error(f"Failed to get messages: {error_data}")
                    
                    return all_messages
                else:
                    error_data = await response.text()
                    logger.error(f"Failed to get conversations: {error_data}")
                    return []
    
    except Exception as e:
        logger.exception(f"Error getting Instagram messages: {e}")
        return []

async def send_test_message(business_account_id: str, recipient_id: str, access_token: str):
    """
    Send a test message.
    
    Args:
        business_account_id (str): Instagram Business Account ID
        recipient_id (str): Recipient ID
        access_token (str): Instagram Access Token
        
    Returns:
        Dict: Response
    """
    logger.info(f"Sending test message to {recipient_id}...")
    
    try:
        async with aiohttp.ClientSession() as session:
            # Send message
            async with session.post(
                f"https://graph.facebook.com/v18.0/{business_account_id}/messages",
                json={
                    "recipient": {"id": recipient_id},
                    "message": {"text": "This is a test message from the Insurance Lead Agent."}
                },
                params={"access_token": access_token}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info("Message sent successfully!")
                    logger.info(f"Response: {data}")
                    return data
                else:
                    error_data = await response.text()
                    logger.error(f"Failed to send message: {error_data}")
                    return {"error": error_data}
    
    except Exception as e:
        logger.exception(f"Error sending Instagram message: {e}")
        return {"error": str(e)}

async def save_credentials(app_id: str, app_secret: str, access_token: str, business_account_id: str = None):
    """
    Save Instagram credentials to file.
    
    Args:
        app_id (str): Instagram App ID
        app_secret (str): Instagram App Secret
        access_token (str): Instagram Access Token
        business_account_id (str, optional): Instagram Business Account ID
    """
    try:
        # Load existing credentials
        credentials_path = "credentials/social_media/instagram.json"
        
        with open(credentials_path, "r") as f:
            credentials = json.load(f)
        
        # Update credentials
        credentials["app_id"] = app_id
        credentials["app_secret"] = app_secret
        credentials["access_token"] = access_token
        
        if business_account_id:
            credentials["business_account_id"] = business_account_id
        
        credentials["last_updated"] = datetime.now().isoformat()
        
        # Save credentials
        with open(credentials_path, "w") as f:
            json.dump(credentials, f, indent=4)
        
        logger.info(f"Saved credentials to {credentials_path}")
    
    except Exception as e:
        logger.exception(f"Error saving credentials: {e}")

async def main():
    """Run the Instagram integration test."""
    parser = argparse.ArgumentParser(description="Instagram Integration Test")
    parser.add_argument("--app-id", type=str, help="Instagram App ID (same as Facebook App ID)")
    parser.add_argument("--app-secret", type=str, help="Instagram App Secret (same as Facebook App Secret)")
    parser.add_argument("--access-token", type=str, help="Instagram Access Token")
    parser.add_argument("--business-account-id", type=str, help="Instagram Business Account ID")
    parser.add_argument("--recipient-id", type=str, help="Recipient ID for test message")
    args = parser.parse_args()
    
    # If credentials are not provided, try to load from file
    app_id = args.app_id
    app_secret = args.app_secret
    access_token = args.access_token
    business_account_id = args.business_account_id
    recipient_id = args.recipient_id
    
    if not app_id or not app_secret or not access_token:
        try:
            with open("credentials/social_media/instagram.json", "r") as f:
                credentials = json.load(f)
                app_id = app_id or credentials.get("app_id", "")
                app_secret = app_secret or credentials.get("app_secret", "")
                access_token = access_token or credentials.get("access_token", "")
                business_account_id = business_account_id or credentials.get("business_account_id", "")
        except Exception as e:
            logger.exception(f"Error loading credentials: {e}")
    
    if not app_id or not app_secret or not access_token:
        logger.error("Instagram App ID, App Secret, and Access Token are required")
        print("Please provide Instagram credentials using the --app-id, --app-secret, and --access-token arguments")
        print("You can find these credentials in your Facebook Developer account")
        return
    
    # Test Instagram connection
    connection_success = await test_instagram_connection(app_id, app_secret, access_token, business_account_id)
    
    if not connection_success:
        return
    
    # If business_account_id is not provided, get Instagram business accounts
    if not business_account_id:
        business_accounts = await get_instagram_business_accounts(access_token)
        
        if business_accounts:
            business_account_id = business_accounts[0].get("id", "")
            
            # Save business account ID
            await save_credentials(app_id, app_secret, access_token, business_account_id)
    
    if not business_account_id:
        logger.error("Instagram Business Account ID is required for further tests")
        return
    
    # Get Instagram messages
    messages = await get_instagram_messages(business_account_id, access_token)
    
    # Send test message if recipient ID is provided
    if recipient_id:
        await send_test_message(business_account_id, recipient_id, access_token)

if __name__ == "__main__":
    asyncio.run(main())
