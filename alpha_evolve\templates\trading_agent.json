{
  "name": "Trading Agent Enhancement",
  "description": "Template for enhancing trading agent capabilities",
  "template": "You are tasked with enhancing the {capability} capability of a trading agent.

The goal is to optimize for {optimization_metric}.

The trading agent handles various financial instruments including stocks, options, futures, cryptocurrencies, and forex.

Requirements:
1. The implementation must be efficient and handle real-time market data
2. It must incorporate proper risk management techniques
3. It should be robust against market volatility and unexpected events
4. It must follow regulatory compliance requirements
5. It should leverage advanced techniques like machine learning and quantum computing when appropriate

Your solution should be implemented as a Python function that follows this interface:
{interface}

Focus on creating a solution that maximizes {optimization_metric} while maintaining robustness and reliability.",
  "variables": ["capability", "optimization_metric", "interface"]
}
