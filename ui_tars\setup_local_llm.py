"""
UI-TARS Local LLM Setup Script.

This script helps set up UI-TARS to use locally downloaded LLMs.
It scans for available models and updates the configuration accordingly.
"""
import os
import sys
import yaml
import json
import argparse
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("ui_tars_setup.log")
    ]
)
logger = logging.getLogger("ui_tars_setup")

# Common model directories to scan
COMMON_MODEL_DIRS = [
    os.path.expanduser("~/models"),
    os.path.expanduser("~/Documents/models"),
    os.path.expanduser("~/Downloads/models"),
    "C:/models",
    "C:/Users/<USER>/models",
    "C:/Users/<USER>/Documents/models",
    "C:/Users/<USER>/Downloads/models",
    "D:/models",
]

# Model types to look for
MODEL_TYPES = {
    "ui-tars": ["UI-TARS", "ui-tars", "UI-TARS-1.5", "ui-tars-1.5", "UI-TARS-1.5-7B", "ui-tars-1.5-7b"],
    "qwen": ["Qwen", "qwen", "Qwen2.5-VL", "qwen2.5-vl", "Qwen2.5-VL-7B", "qwen2.5-vl-7b"],
    "llama": ["Llama", "llama", "Llama-3", "llama-3", "Llama-3-8B", "llama-3-8b"],
    "mistral": ["Mistral", "mistral", "Mistral-7B", "mistral-7b"],
    "phi": ["Phi", "phi", "Phi-3", "phi-3", "Phi-3-mini", "phi-3-mini"],
    "gemma": ["Gemma", "gemma", "Gemma-7B", "gemma-7b"],
    "falcon": ["Falcon", "falcon", "Falcon-7B", "falcon-7b"],
    "gpt-j": ["GPT-J", "gpt-j", "GPT-J-6B", "gpt-j-6b"],
    "gpt-neo": ["GPT-Neo", "gpt-neo", "GPT-Neo-2.7B", "gpt-neo-2.7b"],
    "gpt-neox": ["GPT-NeoX", "gpt-neox", "GPT-NeoX-20B", "gpt-neox-20b"],
    "bloom": ["BLOOM", "bloom", "BLOOM-7B", "bloom-7b"],
    "opt": ["OPT", "opt", "OPT-6.7B", "opt-6.7b"],
}

def scan_for_models():
    """
    Scan for locally downloaded models.
    
    Returns:
        dict: Dictionary of model types and paths
    """
    logger.info("Scanning for locally downloaded models")
    
    models = {}
    
    # Scan common model directories
    for base_dir in COMMON_MODEL_DIRS:
        if not os.path.exists(base_dir):
            continue
        
        logger.info(f"Scanning directory: {base_dir}")
        
        # Scan for model types
        for model_type, model_names in MODEL_TYPES.items():
            for model_name in model_names:
                model_path = os.path.join(base_dir, model_name)
                if os.path.exists(model_path):
                    logger.info(f"Found {model_type} model: {model_path}")
                    models[model_type] = model_path
                    break
    
    return models

def update_config(config_path, models):
    """
    Update the UI-TARS configuration with the found models.
    
    Args:
        config_path (str): Path to the configuration file
        models (dict): Dictionary of model types and paths
    """
    logger.info(f"Updating configuration: {config_path}")
    
    # Load the configuration
    with open(config_path, "r") as f:
        config = yaml.safe_load(f)
    
    # Update the model paths
    if "local_llm" not in config:
        config["local_llm"] = {}
    
    if "models" not in config["local_llm"]:
        config["local_llm"]["models"] = {}
    
    # Update the model paths
    for model_type, model_path in models.items():
        config["local_llm"]["models"][model_type] = model_path
    
    # Save the configuration
    with open(config_path, "w") as f:
        yaml.dump(config, f, default_flow_style=False)
    
    logger.info(f"Configuration updated with {len(models)} models")

def create_ui_tars_config(models, output_path):
    """
    Create a UI-TARS configuration file for importing into UI-TARS.
    
    Args:
        models (dict): Dictionary of model types and paths
        output_path (str): Path to save the configuration file
    """
    logger.info(f"Creating UI-TARS configuration: {output_path}")
    
    # Create the configuration
    config = {
        "name": "UI-TARS Local LLM Configuration",
        "version": "1.0",
        "models": {},
        "settings": {
            "use_local_model": True,
            "api_key": None,
            "autonomous_mode": True,
            "voice_commands_enabled": True,
            "browser": {
                "type": "chrome",
                "automation": {
                    "enabled": True
                }
            },
            "nvidia": {
                "enabled": True,
                "cuda": {
                    "enabled": True
                }
            }
        }
    }
    
    # Add the models
    for model_type, model_path in models.items():
        config["models"][model_type] = {
            "path": model_path,
            "type": model_type,
            "quantization": "4bit",
            "enabled": True
        }
    
    # Set the default model
    if "ui-tars" in models:
        config["settings"]["default_model"] = "ui-tars"
    elif len(models) > 0:
        config["settings"]["default_model"] = list(models.keys())[0]
    
    # Save the configuration
    with open(output_path, "w") as f:
        json.dump(config, f, indent=4)
    
    logger.info(f"UI-TARS configuration created with {len(models)} models")

def main():
    """Main entry point for the setup script."""
    logger.info("Starting UI-TARS Local LLM Setup")
    
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="UI-TARS Local LLM Setup")
    parser.add_argument("--config", type=str, default="ui_tars_config.yaml", help="Path to the configuration file")
    parser.add_argument("--output", type=str, default="ui_tars_import.json", help="Path to save the UI-TARS import configuration")
    parser.add_argument("--scan-dir", type=str, help="Additional directory to scan for models")
    args = parser.parse_args()
    
    # Add additional scan directory if provided
    if args.scan_dir:
        COMMON_MODEL_DIRS.append(args.scan_dir)
    
    # Scan for models
    models = scan_for_models()
    
    if not models:
        logger.warning("No models found. Please download models or specify the model directory.")
        return False
    
    # Update the configuration
    update_config(args.config, models)
    
    # Create UI-TARS import configuration
    create_ui_tars_config(models, args.output)
    
    logger.info("UI-TARS Local LLM Setup completed")
    logger.info(f"Found {len(models)} models:")
    for model_type, model_path in models.items():
        logger.info(f"  - {model_type}: {model_path}")
    
    logger.info(f"Configuration updated: {args.config}")
    logger.info(f"UI-TARS import configuration created: {args.output}")
    logger.info("You can now import the configuration into UI-TARS.")
    
    return True

if __name__ == "__main__":
    main()
