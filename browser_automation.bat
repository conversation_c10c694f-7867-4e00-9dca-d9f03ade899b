@echo off
echo Browser Automation CLI
echo ====================
echo.

python cli/browser_automation_cli.py %*

if "%1"=="" (
    echo.
    echo Usage:
    echo   browser_automation.bat --interactive
    echo   browser_automation.bat execute "Browse to https://www.google.com"
    echo   browser_automation.<NAME_EMAIL> "Subject" "Body"
    echo   browser_automation.bat text ********** "Message"
    echo   browser_automation.bat call **********
    echo   browser_automation.bat health --repair
    echo.
)

pause
