"""
Test Jarvis UI-TARS Integration.

This script tests the Jarvis UI-TARS integration by executing commands.
"""
import os
import sys
import asyncio
import argparse
import logging
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent))

try:
    from core.logger import setup_logger
    from ui_tars.integration.jarvis_ui_tars_integration import JarvisUITarsIntegration
except ImportError as e:
    print(f"Error importing required modules: {e}")
    print("Make sure you're running this script from the project root directory.")
    sys.exit(1)

# Set up logger
logger = setup_logger("test_jarvis_ui_tars_integration")

class MockJarvisInterface:
    """Mock Jarvis interface for testing."""
    
    def __init__(self):
        """Initialize the mock Jarvis interface."""
        self.commands = {}
        self.logger = logger
    
    def register_command(self, command_name, handler, description, usage, parameters):
        """
        Register a command with the Jarvis interface.
        
        Args:
            command_name (str): Command name
            handler (callable): Command handler
            description (str): Command description
            usage (str): Command usage
            parameters (list): Command parameters
        """
        self.logger.info(f"Registered command: {command_name}")
        self.commands[command_name] = {
            "handler": handler,
            "description": description,
            "usage": usage,
            "parameters": parameters
        }
    
    async def execute_command(self, command_name, args):
        """
        Execute a command.
        
        Args:
            command_name (str): Command name
            args (list): Command arguments
            
        Returns:
            str: Command output
        """
        if command_name not in self.commands:
            return f"Error: Unknown command '{command_name}'"
        
        handler = self.commands[command_name]["handler"]
        return await handler(args)
    
    async def start_interactive_mode(self):
        """Start interactive mode."""
        self.logger.info("Starting interactive mode")
        
        print("Jarvis Interactive Mode")
        print("======================")
        print("Type 'help' for a list of commands, or 'exit' to quit")
        print()
        
        while True:
            try:
                # Get user input
                user_input = input("Jarvis> ")
                
                # Check if user wants to exit
                if user_input.lower() in ["exit", "quit", "q"]:
                    break
                
                # Check if user wants help
                if user_input.lower() == "help":
                    print("\nAvailable commands:")
                    for command_name, command_info in self.commands.items():
                        print(f"  {command_name}: {command_info['description']}")
                        print(f"    Usage: {command_info['usage']}")
                    print()
                    continue
                
                # Parse command and arguments
                parts = user_input.split()
                if not parts:
                    continue
                
                command_name = parts[0]
                args = parts[1:]
                
                # Execute command
                result = await self.execute_command(command_name, args)
                print(result)
                print()
            
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"Error: {e}")
        
        print("Exiting interactive mode")
    
    async def initialize(self):
        """Initialize the mock Jarvis interface."""
        self.logger.info("Initializing mock Jarvis interface")
        return True
    
    async def shutdown(self):
        """Shutdown the mock Jarvis interface."""
        self.logger.info("Shutting down mock Jarvis interface")
        return True

async def load_config():
    """
    Load configuration from file.
    
    Returns:
        dict: Configuration dictionary
    """
    config_path = Path("ui_tars/config.json")
    if not config_path.exists():
        logger.error(f"Configuration file not found: {config_path}")
        return {}
    
    try:
        import json
        with open(config_path, "r") as f:
            config = json.load(f)
        
        logger.info(f"Configuration loaded from {config_path}")
        return config
    except Exception as e:
        logger.error(f"Error loading configuration: {e}")
        return {}

async def test_jarvis_ui_tars_integration():
    """
    Test Jarvis UI-TARS integration.
    
    Returns:
        bool: True if successful, False otherwise
    """
    logger.info("Testing Jarvis UI-TARS integration")
    
    # Load configuration
    config = await load_config()
    if not config:
        logger.error("Failed to load configuration")
        return False
    
    # Create mock Jarvis interface
    jarvis_interface = MockJarvisInterface()
    await jarvis_interface.initialize()
    
    # Create Jarvis UI-TARS integration
    integration = JarvisUITarsIntegration(
        jarvis_interface=jarvis_interface,
        config=config
    )
    
    # Initialize integration
    logger.info("Initializing Jarvis UI-TARS integration")
    success = await integration.initialize()
    if not success:
        logger.error("Failed to initialize Jarvis UI-TARS integration")
        return False
    
    try:
        # Start interactive mode
        await jarvis_interface.start_interactive_mode()
        
        # Shutdown integration
        logger.info("Shutting down Jarvis UI-TARS integration")
        await integration.shutdown()
        
        # Shutdown Jarvis interface
        await jarvis_interface.shutdown()
        
        return True
    
    except Exception as e:
        logger.exception(f"Error testing Jarvis UI-TARS integration: {e}")
        
        # Try to shutdown integration
        try:
            await integration.shutdown()
        except:
            pass
        
        # Try to shutdown Jarvis interface
        try:
            await jarvis_interface.shutdown()
        except:
            pass
        
        return False

async def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Test Jarvis UI-TARS Integration")
    
    args = parser.parse_args()
    
    print("Jarvis UI-TARS Integration Test")
    print("==============================")
    print()
    
    try:
        # Test Jarvis UI-TARS integration
        success = await test_jarvis_ui_tars_integration()
        
        if success:
            print("\nJarvis UI-TARS integration test completed")
            return 0
        else:
            print("\nJarvis UI-TARS integration test failed")
            return 1
    
    except Exception as e:
        logger.exception(f"Error in main: {e}")
        print(f"Error: {e}")
        return 1

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nTest cancelled")
        sys.exit(0)
