"""
Main Dashboard for the Unified Agent System.

This module provides a comprehensive GUI dashboard for monitoring and controlling
all agents in the Multi-Agent AI System.
"""

__version__ = "1.0.0"
import os
import sys
import json
import asyncio
import logging
import tkinter as tk
from tkinter import ttk, scrolledtext, filedialog, messagebox, simpledialog
from typing import Dict, List, Optional, Any, Union
import threading
import queue
import time
from datetime import datetime
import webbrowser
from PIL import Image, ImageTk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import networkx as nx

# Add the project root to the Python path
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent.parent))

try:
    from core.logger import setup_logger
    from core.state_manager import StateManager
    from core.agent_manager import AgentManager
    from ui_tars.connector.ui_tars_connector import UITarsConnector
    from ui_tars.connector.enhanced_ui_tars_connector import EnhancedUITarsConnector
    from ui_tars.agent.enhanced_ui_tars_agent import Enhanced<PERSON><PERSON>arsAgent
    from borg_cluster.jarvis_interface import JarvisInter<PERSON>
    from alpha_evolve.alpha_evolve_engine import AlphaEvolveEngine
except ImportError as e:
    print(f"Error importing required modules: {e}")
    # Fallback imports for standalone usage
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler("unified_agent_dashboard.log")
        ]
    )

    def setup_logger(name):
        return logging.getLogger(name)

    StateManager = object
    AgentManager = object
    UITarsConnector = object
    EnhancedUITarsConnector = object
    EnhancedUITarsAgent = object
    JarvisInterface = object
    AlphaEvolveEngine = object

# Set up logger
logger = setup_logger("unified_agent_dashboard")

class UnifiedAgentDashboard:
    """
    Unified Agent Dashboard for the Multi-Agent AI System.

    This class provides a comprehensive GUI dashboard for monitoring and controlling
    all agents in the Multi-Agent AI System, including UI-TARS, CrewAI, AlphaEvolve,
    and Jarvis.
    """

    def __init__(self,
                 state_manager: Optional[StateManager] = None,
                 agent_manager: Optional[AgentManager] = None,
                 ui_tars_connector: Optional[Union[UITarsConnector, EnhancedUITarsConnector]] = None,
                 jarvis_interface: Optional[JarvisInterface] = None,
                 alpha_evolve_engine: Optional[AlphaEvolveEngine] = None):
        """
        Initialize the Unified Agent Dashboard.

        Args:
            state_manager (Optional[StateManager]): State manager
            agent_manager (Optional[AgentManager]): Agent manager
            ui_tars_connector (Optional[Union[UITarsConnector, EnhancedUITarsConnector]]): UI-TARS connector
            jarvis_interface (Optional[JarvisInterface]): Jarvis interface
            alpha_evolve_engine (Optional[AlphaEvolveEngine]): AlphaEvolve engine
        """
        self.state_manager = state_manager
        self.agent_manager = agent_manager
        self.ui_tars_connector = ui_tars_connector
        self.jarvis_interface = jarvis_interface
        self.alpha_evolve_engine = alpha_evolve_engine

        # Initialize GUI components
        self.root = None
        self.notebook = None
        self.status_var = None
        self.command_entry = None
        self.output_text = None
        self.agent_listbox = None
        self.agent_details_text = None
        self.workflow_canvas = None
        self.browser_frame = None
        self.screenshot_label = None

        # Initialize message queue for thread-safe GUI updates
        self.message_queue = queue.Queue()
        self.is_running = False

        # Initialize agent data
        self.agents = {}
        self.workflows = {}
        self.agent_statuses = {}

        # Initialize command history
        self.command_history = []
        self.command_index = 0

        # Initialize theme
        self.theme_var = tk.StringVar(value="light")

        # Initialize refresh rate
        self.refresh_rate = 5  # seconds

    def initialize(self):
        """Initialize the Unified Agent Dashboard."""
        logger.info("Initializing Unified Agent Dashboard")

        # Create the main window
        self.root = tk.Tk()
        self.root.title("Unified Agent Dashboard")
        self.root.geometry("1280x800")
        self.root.minsize(1024, 768)

        # Set up the main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create a notebook (tabbed interface)
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # Create tabs
        overview_tab = ttk.Frame(self.notebook)
        agents_tab = ttk.Frame(self.notebook)
        workflows_tab = ttk.Frame(self.notebook)
        browser_tab = ttk.Frame(self.notebook)
        command_tab = ttk.Frame(self.notebook)
        settings_tab = ttk.Frame(self.notebook)

        self.notebook.add(overview_tab, text="Overview")
        self.notebook.add(agents_tab, text="Agents")
        self.notebook.add(workflows_tab, text="Workflows")
        self.notebook.add(browser_tab, text="Browser Automation")
        self.notebook.add(command_tab, text="Command Center")
        self.notebook.add(settings_tab, text="Settings")

        # Set up tabs
        self._setup_overview_tab(overview_tab)
        self._setup_agents_tab(agents_tab)
        self._setup_workflows_tab(workflows_tab)
        self._setup_browser_tab(browser_tab)
        self._setup_command_tab(command_tab)
        self._setup_settings_tab(settings_tab)

        # Set up the status bar
        self._setup_status_bar(main_frame)

        # Start the message processing thread
        self.is_running = True
        threading.Thread(target=self._process_messages, daemon=True).start()

        # Start the refresh thread
        threading.Thread(target=self._refresh_data, daemon=True).start()

        logger.info("Unified Agent Dashboard initialized")

    def _setup_overview_tab(self, parent):
        """
        Set up the overview tab.

        Args:
            parent: Parent widget
        """
        # Create frames
        top_frame = ttk.Frame(parent)
        top_frame.pack(fill=tk.X, padx=5, pady=5)

        middle_frame = ttk.Frame(parent)
        middle_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Add system status to top frame
        ttk.Label(top_frame, text="System Status:", font=("TkDefaultFont", 12, "bold")).pack(side=tk.LEFT, padx=5)

        system_status_var = tk.StringVar(value="Running")
        ttk.Label(top_frame, textvariable=system_status_var, foreground="green").pack(side=tk.LEFT, padx=5)

        ttk.Button(top_frame, text="Refresh", command=self._refresh_overview).pack(side=tk.RIGHT, padx=5)

        # Add paned window to middle frame
        paned_window = ttk.PanedWindow(middle_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)

        # Left pane for agent summary
        left_frame = ttk.Frame(paned_window)
        paned_window.add(left_frame, weight=1)

        ttk.Label(left_frame, text="Agent Summary", font=("TkDefaultFont", 11, "bold")).pack(anchor=tk.W, padx=5, pady=2)

        # Create a treeview for agent summary
        agent_tree = ttk.Treeview(left_frame, columns=("status", "type", "tasks"), show="headings")
        agent_tree.heading("status", text="Status")
        agent_tree.heading("type", text="Type")
        agent_tree.heading("tasks", text="Tasks")
        agent_tree.column("status", width=100)
        agent_tree.column("type", width=150)
        agent_tree.column("tasks", width=100)
        agent_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Add scrollbar to treeview
        agent_tree_scrollbar = ttk.Scrollbar(left_frame, orient=tk.VERTICAL, command=agent_tree.yview)
        agent_tree.configure(yscrollcommand=agent_tree_scrollbar.set)
        agent_tree_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Right pane for system metrics
        right_frame = ttk.Frame(paned_window)
        paned_window.add(right_frame, weight=1)

        ttk.Label(right_frame, text="System Metrics", font=("TkDefaultFont", 11, "bold")).pack(anchor=tk.W, padx=5, pady=2)

        # Create a figure for system metrics
        figure = plt.Figure(figsize=(6, 4), dpi=100)
        ax = figure.add_subplot(111)
        ax.set_title("CPU & Memory Usage")
        ax.set_xlabel("Time")
        ax.set_ylabel("Usage (%)")
        ax.grid(True)

        # Create a canvas for the figure
        canvas = FigureCanvasTkAgg(figure, right_frame)
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Store references
        self.agent_tree = agent_tree
        self.system_status_var = system_status_var
        self.metrics_figure = figure
        self.metrics_canvas = canvas

    def _setup_agents_tab(self, parent):
        """
        Set up the agents tab.

        Args:
            parent: Parent widget
        """
        # Create frames
        top_frame = ttk.Frame(parent)
        top_frame.pack(fill=tk.X, padx=5, pady=5)

        middle_frame = ttk.Frame(parent)
        middle_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        bottom_frame = ttk.Frame(parent)
        bottom_frame.pack(fill=tk.X, padx=5, pady=5)

        # Add controls to top frame
        ttk.Label(top_frame, text="Agent Management", font=("TkDefaultFont", 12, "bold")).pack(side=tk.LEFT, padx=5)

        ttk.Button(top_frame, text="Start Agent", command=self._start_agent).pack(side=tk.RIGHT, padx=5)
        ttk.Button(top_frame, text="Stop Agent", command=self._stop_agent).pack(side=tk.RIGHT, padx=5)
        ttk.Button(top_frame, text="Refresh", command=self._refresh_agents).pack(side=tk.RIGHT, padx=5)

        # Add paned window to middle frame
        paned_window = ttk.PanedWindow(middle_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)

        # Left pane for agent list
        left_frame = ttk.Frame(paned_window)
        paned_window.add(left_frame, weight=1)

        ttk.Label(left_frame, text="Available Agents", font=("TkDefaultFont", 11, "bold")).pack(anchor=tk.W, padx=5, pady=2)

        # Create a listbox for agents
        agent_listbox = tk.Listbox(left_frame)
        agent_listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Add scrollbar to listbox
        agent_listbox_scrollbar = ttk.Scrollbar(left_frame, orient=tk.VERTICAL, command=agent_listbox.yview)
        agent_listbox.configure(yscrollcommand=agent_listbox_scrollbar.set)
        agent_listbox_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Bind selection event
        agent_listbox.bind('<<ListboxSelect>>', self._on_agent_selected)

        # Right pane for agent details
        right_frame = ttk.Frame(paned_window)
        paned_window.add(right_frame, weight=2)

        ttk.Label(right_frame, text="Agent Details", font=("TkDefaultFont", 11, "bold")).pack(anchor=tk.W, padx=5, pady=2)

        # Create a text widget for agent details
        agent_details_text = scrolledtext.ScrolledText(right_frame, wrap=tk.WORD)
        agent_details_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        agent_details_text.config(state=tk.DISABLED)

        # Add controls to bottom frame
        ttk.Button(bottom_frame, text="Send Command", command=self._send_agent_command).pack(side=tk.RIGHT, padx=5)

        agent_command_entry = ttk.Entry(bottom_frame, width=50)
        agent_command_entry.pack(side=tk.RIGHT, padx=5, fill=tk.X, expand=True)

        ttk.Label(bottom_frame, text="Command:").pack(side=tk.RIGHT, padx=5)

        # Store references
        self.agent_listbox = agent_listbox
        self.agent_details_text = agent_details_text
        self.agent_command_entry = agent_command_entry

    def _setup_workflows_tab(self, parent):
        """
        Set up the workflows tab.

        Args:
            parent: Parent widget
        """
        # Create frames
        top_frame = ttk.Frame(parent)
        top_frame.pack(fill=tk.X, padx=5, pady=5)

        middle_frame = ttk.Frame(parent)
        middle_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        bottom_frame = ttk.Frame(parent)
        bottom_frame.pack(fill=tk.X, padx=5, pady=5)

        # Add controls to top frame
        ttk.Label(top_frame, text="Workflow Management", font=("TkDefaultFont", 12, "bold")).pack(side=tk.LEFT, padx=5)

        ttk.Button(top_frame, text="Create Workflow", command=self._create_workflow).pack(side=tk.RIGHT, padx=5)
        ttk.Button(top_frame, text="Run Workflow", command=self._run_workflow).pack(side=tk.RIGHT, padx=5)
        ttk.Button(top_frame, text="Refresh", command=self._refresh_workflows).pack(side=tk.RIGHT, padx=5)

        # Add paned window to middle frame
        paned_window = ttk.PanedWindow(middle_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)

        # Left pane for workflow list
        left_frame = ttk.Frame(paned_window)
        paned_window.add(left_frame, weight=1)

        ttk.Label(left_frame, text="Available Workflows", font=("TkDefaultFont", 11, "bold")).pack(anchor=tk.W, padx=5, pady=2)

        # Create a listbox for workflows
        workflow_listbox = tk.Listbox(left_frame)
        workflow_listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Add scrollbar to listbox
        workflow_listbox_scrollbar = ttk.Scrollbar(left_frame, orient=tk.VERTICAL, command=workflow_listbox.yview)
        workflow_listbox.configure(yscrollcommand=workflow_listbox_scrollbar.set)
        workflow_listbox_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Bind selection event
        workflow_listbox.bind('<<ListboxSelect>>', self._on_workflow_selected)

        # Right pane for workflow visualization
        right_frame = ttk.Frame(paned_window)
        paned_window.add(right_frame, weight=2)

        ttk.Label(right_frame, text="Workflow Visualization", font=("TkDefaultFont", 11, "bold")).pack(anchor=tk.W, padx=5, pady=2)

        # Create a canvas for workflow visualization
        workflow_canvas = tk.Canvas(right_frame, bg="white")
        workflow_canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Add controls to bottom frame
        ttk.Button(bottom_frame, text="Export Workflow", command=self._export_workflow).pack(side=tk.RIGHT, padx=5)
        ttk.Button(bottom_frame, text="Import Workflow", command=self._import_workflow).pack(side=tk.RIGHT, padx=5)

        # Store references
        self.workflow_listbox = workflow_listbox
        self.workflow_canvas = workflow_canvas

    def _setup_browser_tab(self, parent):
        """
        Set up the browser automation tab.

        Args:
            parent: Parent widget
        """
        # Create frames
        top_frame = ttk.Frame(parent)
        top_frame.pack(fill=tk.X, padx=5, pady=5)

        middle_frame = ttk.Frame(parent)
        middle_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        bottom_frame = ttk.Frame(parent)
        bottom_frame.pack(fill=tk.X, padx=5, pady=5)

        # Add controls to top frame
        ttk.Label(top_frame, text="Browser Automation", font=("TkDefaultFont", 12, "bold")).pack(side=tk.LEFT, padx=5)

        ttk.Button(top_frame, text="Start Browser", command=self._start_browser).pack(side=tk.RIGHT, padx=5)
        ttk.Button(top_frame, text="Take Screenshot", command=self._take_screenshot).pack(side=tk.RIGHT, padx=5)
        ttk.Button(top_frame, text="Refresh", command=self._refresh_browser).pack(side=tk.RIGHT, padx=5)

        # Add browser frame to middle frame
        browser_frame = ttk.Frame(middle_frame, relief=tk.SUNKEN, borderwidth=1)
        browser_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Add screenshot label to browser frame
        screenshot_label = ttk.Label(browser_frame)
        screenshot_label.pack(fill=tk.BOTH, expand=True)

        # Add controls to bottom frame
        ttk.Button(bottom_frame, text="Execute", command=self._execute_browser_command).pack(side=tk.RIGHT, padx=5)

        browser_command_entry = ttk.Entry(bottom_frame, width=50)
        browser_command_entry.pack(side=tk.RIGHT, padx=5, fill=tk.X, expand=True)

        ttk.Label(bottom_frame, text="Command:").pack(side=tk.RIGHT, padx=5)

        # Store references
        self.browser_frame = browser_frame
        self.screenshot_label = screenshot_label
        self.browser_command_entry = browser_command_entry

    def _setup_command_tab(self, parent):
        """
        Set up the command center tab.

        Args:
            parent: Parent widget
        """
        # Create frames
        top_frame = ttk.Frame(parent)
        top_frame.pack(fill=tk.X, padx=5, pady=5)

        middle_frame = ttk.Frame(parent)
        middle_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        bottom_frame = ttk.Frame(parent)
        bottom_frame.pack(fill=tk.X, padx=5, pady=5)

        # Add controls to top frame
        ttk.Label(top_frame, text="Command Center", font=("TkDefaultFont", 12, "bold")).pack(side=tk.LEFT, padx=5)

        ttk.Button(top_frame, text="Clear Output", command=self._clear_command_output).pack(side=tk.RIGHT, padx=5)

        # Add output text to middle frame
        output_text = scrolledtext.ScrolledText(middle_frame, wrap=tk.WORD)
        output_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        output_text.config(state=tk.DISABLED)

        # Add controls to bottom frame
        ttk.Button(bottom_frame, text="Execute", command=self._execute_command).pack(side=tk.RIGHT, padx=5)

        command_entry = ttk.Entry(bottom_frame, width=50)
        command_entry.pack(side=tk.RIGHT, padx=5, fill=tk.X, expand=True)

        ttk.Label(bottom_frame, text="Command:").pack(side=tk.RIGHT, padx=5)

        # Store references
        self.output_text = output_text
        self.command_entry = command_entry

    def _setup_settings_tab(self, parent):
        """
        Set up the settings tab.

        Args:
            parent: Parent widget
        """
        # Create frames
        settings_frame = ttk.Frame(parent)
        settings_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create notebook for settings categories
        settings_notebook = ttk.Notebook(settings_frame)
        settings_notebook.pack(fill=tk.BOTH, expand=True)

        # Create tabs for different settings categories
        general_tab = ttk.Frame(settings_notebook)
        ui_tars_tab = ttk.Frame(settings_notebook)
        crewai_tab = ttk.Frame(settings_notebook)
        alpha_evolve_tab = ttk.Frame(settings_notebook)
        jarvis_tab = ttk.Frame(settings_notebook)
        google_tab = ttk.Frame(settings_notebook)

        settings_notebook.add(general_tab, text="General")
        settings_notebook.add(ui_tars_tab, text="UI-TARS")
        settings_notebook.add(crewai_tab, text="CrewAI")
        settings_notebook.add(alpha_evolve_tab, text="AlphaEvolve")
        settings_notebook.add(jarvis_tab, text="Jarvis")
        settings_notebook.add(google_tab, text="Google ADK")

        # Set up general settings tab
        self._setup_general_settings(general_tab)

        # Set up UI-TARS settings tab
        self._setup_ui_tars_settings(ui_tars_tab)

        # Set up CrewAI settings tab
        self._setup_crewai_settings(crewai_tab)

        # Set up AlphaEvolve settings tab
        self._setup_alpha_evolve_settings(alpha_evolve_tab)

        # Set up Jarvis settings tab
        self._setup_jarvis_settings(jarvis_tab)

        # Set up Google ADK settings tab
        self._setup_google_settings(google_tab)

        # Add save and cancel buttons
        button_frame = ttk.Frame(settings_frame)
        button_frame.pack(fill=tk.X, pady=10)

        ttk.Button(button_frame, text="Save Settings", command=self._save_settings).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="Cancel", command=self._cancel_settings).pack(side=tk.RIGHT, padx=5)

    def _setup_general_settings(self, parent):
        """
        Set up the general settings tab.

        Args:
            parent: Parent widget
        """
        # Create frame
        frame = ttk.Frame(parent)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Add settings
        ttk.Label(frame, text="Theme:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        theme_combobox = ttk.Combobox(frame, textvariable=self.theme_var, values=["light", "dark"])
        theme_combobox.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(frame, text="Refresh Rate (seconds):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        refresh_spinbox = ttk.Spinbox(frame, from_=1, to=60, increment=1, width=5)
        refresh_spinbox.set(self.refresh_rate)
        refresh_spinbox.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        # Store references
        self.theme_combobox = theme_combobox
        self.refresh_spinbox = refresh_spinbox

    def _setup_ui_tars_settings(self, parent):
        """
        Set up the UI-TARS settings tab.

        Args:
            parent: Parent widget
        """
        # Create frame
        frame = ttk.Frame(parent)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Add settings
        ttk.Label(frame, text="API URL:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        api_url_entry = ttk.Entry(frame, width=40)
        api_url_entry.insert(0, "http://localhost:8080")
        api_url_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(frame, text="API Key:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        api_key_entry = ttk.Entry(frame, width=40)
        api_key_entry.insert(0, "hf_dummy_key")
        api_key_entry.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(frame, text="Model Name:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        model_name_entry = ttk.Entry(frame, width=40)
        model_name_entry.insert(0, "UI-TARS-1.5-7B")
        model_name_entry.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(frame, text="Browser Type:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        browser_type_combobox = ttk.Combobox(frame, values=["chrome", "edge", "firefox", "brave"])
        browser_type_combobox.set("chrome")
        browser_type_combobox.grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(frame, text="Auto Start:").grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
        auto_start_var = tk.BooleanVar(value=True)
        auto_start_check = ttk.Checkbutton(frame, variable=auto_start_var)
        auto_start_check.grid(row=4, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(frame, text="Auto Restart:").grid(row=5, column=0, sticky=tk.W, padx=5, pady=5)
        auto_restart_var = tk.BooleanVar(value=True)
        auto_restart_check = ttk.Checkbutton(frame, variable=auto_restart_var)
        auto_restart_check.grid(row=5, column=1, sticky=tk.W, padx=5, pady=5)

        # Store references
        self.ui_tars_settings = {
            "api_url_entry": api_url_entry,
            "api_key_entry": api_key_entry,
            "model_name_entry": model_name_entry,
            "browser_type_combobox": browser_type_combobox,
            "auto_start_var": auto_start_var,
            "auto_restart_var": auto_restart_var
        }

    def _setup_crewai_settings(self, parent):
        """
        Set up the CrewAI settings tab.

        Args:
            parent: Parent widget
        """
        # Create frame
        frame = ttk.Frame(parent)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Add settings
        ttk.Label(frame, text="LLM Provider:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        llm_provider_combobox = ttk.Combobox(frame, values=["OpenAI", "Anthropic", "Local"])
        llm_provider_combobox.set("Local")
        llm_provider_combobox.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(frame, text="API Key:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        api_key_entry = ttk.Entry(frame, width=40)
        api_key_entry.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(frame, text="Process:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        process_combobox = ttk.Combobox(frame, values=["sequential", "hierarchical"])
        process_combobox.set("sequential")
        process_combobox.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(frame, text="Verbose:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        verbose_var = tk.BooleanVar(value=True)
        verbose_check = ttk.Checkbutton(frame, variable=verbose_var)
        verbose_check.grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)

        # Store references
        self.crewai_settings = {
            "llm_provider_combobox": llm_provider_combobox,
            "api_key_entry": api_key_entry,
            "process_combobox": process_combobox,
            "verbose_var": verbose_var
        }

    def _setup_alpha_evolve_settings(self, parent):
        """
        Set up the AlphaEvolve settings tab.

        Args:
            parent: Parent widget
        """
        # Create frame
        frame = ttk.Frame(parent)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Add settings
        ttk.Label(frame, text="Population Size:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        population_size_spinbox = ttk.Spinbox(frame, from_=10, to=100, increment=10, width=5)
        population_size_spinbox.set(50)
        population_size_spinbox.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(frame, text="Generations:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        generations_spinbox = ttk.Spinbox(frame, from_=10, to=1000, increment=10, width=5)
        generations_spinbox.set(100)
        generations_spinbox.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(frame, text="Fitness Threshold:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        fitness_threshold_spinbox = ttk.Spinbox(frame, from_=0.1, to=1.0, increment=0.05, width=5)
        fitness_threshold_spinbox.set(0.95)
        fitness_threshold_spinbox.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(frame, text="Timeout (seconds):").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        timeout_spinbox = ttk.Spinbox(frame, from_=60, to=7200, increment=60, width=5)
        timeout_spinbox.set(3600)
        timeout_spinbox.grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)

        # Store references
        self.alpha_evolve_settings = {
            "population_size_spinbox": population_size_spinbox,
            "generations_spinbox": generations_spinbox,
            "fitness_threshold_spinbox": fitness_threshold_spinbox,
            "timeout_spinbox": timeout_spinbox
        }

    def _setup_jarvis_settings(self, parent):
        """
        Set up the Jarvis settings tab.

        Args:
            parent: Parent widget
        """
        # Create frame
        frame = ttk.Frame(parent)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Add settings
        ttk.Label(frame, text="Voice Enabled:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        voice_enabled_var = tk.BooleanVar(value=False)
        voice_enabled_check = ttk.Checkbutton(frame, variable=voice_enabled_var)
        voice_enabled_check.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(frame, text="Activation Keyword:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        activation_keyword_entry = ttk.Entry(frame, width=20)
        activation_keyword_entry.insert(0, "jarvis")
        activation_keyword_entry.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(frame, text="Auto Start:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        auto_start_var = tk.BooleanVar(value=True)
        auto_start_check = ttk.Checkbutton(frame, variable=auto_start_var)
        auto_start_check.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(frame, text="System Monitoring:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        system_monitoring_var = tk.BooleanVar(value=True)
        system_monitoring_check = ttk.Checkbutton(frame, variable=system_monitoring_var)
        system_monitoring_check.grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)

        # Store references
        self.jarvis_settings = {
            "voice_enabled_var": voice_enabled_var,
            "activation_keyword_entry": activation_keyword_entry,
            "auto_start_var": auto_start_var,
            "system_monitoring_var": system_monitoring_var
        }

    def _setup_google_settings(self, parent):
        """
        Set up the Google ADK settings tab.

        Args:
            parent: Parent widget
        """
        # Create frame
        frame = ttk.Frame(parent)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Add settings
        ttk.Label(frame, text="API Key:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        api_key_entry = ttk.Entry(frame, width=40)
        api_key_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(frame, text="Project ID:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        project_id_entry = ttk.Entry(frame, width=40)
        project_id_entry.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(frame, text="Region:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        region_combobox = ttk.Combobox(frame, values=["us-central1", "us-east1", "us-west1", "europe-west1", "asia-east1"])
        region_combobox.set("us-central1")
        region_combobox.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(frame, text="Enable A2A Protocol:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        a2a_protocol_var = tk.BooleanVar(value=True)
        a2a_protocol_check = ttk.Checkbutton(frame, variable=a2a_protocol_var)
        a2a_protocol_check.grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)

        # Store references
        self.google_settings = {
            "api_key_entry": api_key_entry,
            "project_id_entry": project_id_entry,
            "region_combobox": region_combobox,
            "a2a_protocol_var": a2a_protocol_var
        }

    def _setup_status_bar(self, parent):
        """
        Set up the status bar.

        Args:
            parent: Parent widget
        """
        # Create status bar
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM, padx=5, pady=2)

        # Add status label
        self.status_var = tk.StringVar(value="Ready")
        status_label = ttk.Label(status_frame, textvariable=self.status_var)
        status_label.pack(side=tk.LEFT)

        # Add version label
        version_label = ttk.Label(status_frame, text=f"v{__version__}")
        version_label.pack(side=tk.RIGHT)

    def _refresh_data(self):
        """Refresh data periodically."""
        while self.is_running:
            try:
                # Refresh overview
                self._refresh_overview()

                # Refresh agents
                self._refresh_agents()

                # Refresh workflows
                self._refresh_workflows()

                # Sleep for refresh rate
                time.sleep(self.refresh_rate)

            except Exception as e:
                logger.exception(f"Error refreshing data: {e}")

    def _process_messages(self):
        """Process messages from the message queue."""
        while self.is_running:
            try:
                # Get a message from the queue
                message = self.message_queue.get(timeout=0.1)

                # Process the message
                if message.get("type") == "log":
                    self.root.after(0, lambda m=message: self._append_to_output(f"{m.get('text', '')}\n", m.get("tag")))
                elif message.get("type") == "status":
                    self.root.after(0, lambda m=message: self.status_var.set(m.get("text", "")))
                elif message.get("type") == "screenshot":
                    self.root.after(0, lambda m=message: self._update_screenshot(m.get("data")))

                # Mark the message as processed
                self.message_queue.task_done()

            except queue.Empty:
                # No messages in the queue
                pass

            except Exception as e:
                logger.exception(f"Error processing message: {e}")

    def _append_to_output(self, text, tag=None):
        """
        Append text to the output text widget.

        Args:
            text (str): Text to append
            tag (str, optional): Tag for the text
        """
        # Enable editing
        self.output_text.config(state=tk.NORMAL)

        # Insert text
        if tag:
            self.output_text.insert(tk.END, text, tag)
        else:
            self.output_text.insert(tk.END, text)

        # Scroll to end
        self.output_text.see(tk.END)

        # Disable editing
        self.output_text.config(state=tk.DISABLED)

    def _update_screenshot(self, data):
        """
        Update the screenshot label with new image data.

        Args:
            data: Image data
        """
        try:
            # Create image from data
            image = Image.open(data)

            # Resize image to fit the label
            width, height = self.screenshot_label.winfo_width(), self.screenshot_label.winfo_height()
            if width > 1 and height > 1:
                image = image.resize((width, height), Image.LANCZOS)

            # Convert to PhotoImage
            photo = ImageTk.PhotoImage(image)

            # Update label
            self.screenshot_label.configure(image=photo)
            self.screenshot_label.image = photo  # Keep a reference

        except Exception as e:
            logger.exception(f"Error updating screenshot: {e}")

    def _refresh_overview(self):
        """Refresh the overview tab."""
        try:
            # Update system status
            if self.state_manager:
                system_state = asyncio.run_coroutine_threadsafe(
                    self.state_manager.get_state("system"), asyncio.get_event_loop()
                ).result()

                if system_state and "status" in system_state:
                    self.system_status_var.set(system_state["status"])

            # Update agent tree
            if self.agent_manager:
                agents = asyncio.run_coroutine_threadsafe(
                    self.agent_manager.get_all_agents(), asyncio.get_event_loop()
                ).result()

                # Clear existing items
                for item in self.agent_tree.get_children():
                    self.agent_tree.delete(item)

                # Add agents to tree
                for agent_id, agent_data in agents.items():
                    self.agent_tree.insert(
                        "", tk.END, text=agent_id,
                        values=(
                            agent_data.get("status", "unknown"),
                            agent_data.get("type", "unknown"),
                            len(agent_data.get("tasks", []))
                        )
                    )

            # Update metrics figure
            if self.state_manager:
                metrics = asyncio.run_coroutine_threadsafe(
                    self.state_manager.get_state("system", "metrics"), asyncio.get_event_loop()
                ).result()

                if metrics:
                    # Extract data
                    timestamps = [datetime.fromisoformat(metric["timestamp"]) for metric in metrics]
                    cpu_percent = [metric["cpu_percent"] for metric in metrics]
                    memory_percent = [metric["memory_percent"] for metric in metrics]

                    # Clear figure
                    self.metrics_figure.clear()

                    # Create new plot
                    ax = self.metrics_figure.add_subplot(111)
                    ax.set_title("CPU & Memory Usage")
                    ax.set_xlabel("Time")
                    ax.set_ylabel("Usage (%)")
                    ax.grid(True)

                    # Plot data
                    ax.plot(timestamps, cpu_percent, "b-", label="CPU")
                    ax.plot(timestamps, memory_percent, "r-", label="Memory")

                    # Add legend
                    ax.legend()

                    # Redraw canvas
                    self.metrics_canvas.draw()

        except Exception as e:
            logger.exception(f"Error refreshing overview: {e}")

    def _refresh_agents(self):
        """Refresh the agents tab."""
        try:
            # Get agents from agent manager
            if self.agent_manager:
                agents = asyncio.run_coroutine_threadsafe(
                    self.agent_manager.get_all_agents(), asyncio.get_event_loop()
                ).result()

                # Update agents dictionary
                self.agents = agents

                # Clear listbox
                self.agent_listbox.delete(0, tk.END)

                # Add agents to listbox
                for agent_id in agents.keys():
                    self.agent_listbox.insert(tk.END, agent_id)

        except Exception as e:
            logger.exception(f"Error refreshing agents: {e}")

    def _on_agent_selected(self, event):
        """
        Handle agent selection event.

        Args:
            event: Event object
        """
        try:
            # Get selected agent
            selection = self.agent_listbox.curselection()
            if not selection:
                return

            agent_id = self.agent_listbox.get(selection[0])

            # Get agent data
            agent_data = self.agents.get(agent_id)
            if not agent_data:
                return

            # Update agent details text
            self.agent_details_text.config(state=tk.NORMAL)
            self.agent_details_text.delete(1.0, tk.END)

            # Format agent details
            details = f"Agent ID: {agent_id}\n"
            details += f"Type: {agent_data.get('type', 'unknown')}\n"
            details += f"Status: {agent_data.get('status', 'unknown')}\n"
            details += f"Created: {agent_data.get('created_at', 'unknown')}\n"
            details += "\nCapabilities:\n"

            for capability in agent_data.get("capabilities", []):
                details += f"- {capability}\n"

            details += "\nTasks:\n"

            for task in agent_data.get("tasks", []):
                details += f"- {task.get('id')}: {task.get('status')}\n"

            # Insert details
            self.agent_details_text.insert(tk.END, details)
            self.agent_details_text.config(state=tk.DISABLED)

        except Exception as e:
            logger.exception(f"Error handling agent selection: {e}")

    def _start_agent(self):
        """Start the selected agent."""
        try:
            # Get selected agent
            selection = self.agent_listbox.curselection()
            if not selection:
                return

            agent_id = self.agent_listbox.get(selection[0])

            # Start agent
            if self.agent_manager:
                asyncio.run_coroutine_threadsafe(
                    self.agent_manager.start_agent(agent_id), asyncio.get_event_loop()
                )

                # Update status
                self.status_var.set(f"Starting agent: {agent_id}")

        except Exception as e:
            logger.exception(f"Error starting agent: {e}")

    def _stop_agent(self):
        """Stop the selected agent."""
        try:
            # Get selected agent
            selection = self.agent_listbox.curselection()
            if not selection:
                return

            agent_id = self.agent_listbox.get(selection[0])

            # Stop agent
            if self.agent_manager:
                asyncio.run_coroutine_threadsafe(
                    self.agent_manager.stop_agent(agent_id), asyncio.get_event_loop()
                )

                # Update status
                self.status_var.set(f"Stopping agent: {agent_id}")

        except Exception as e:
            logger.exception(f"Error stopping agent: {e}")

    def _send_agent_command(self):
        """Send a command to the selected agent."""
        try:
            # Get selected agent
            selection = self.agent_listbox.curselection()
            if not selection:
                return

            agent_id = self.agent_listbox.get(selection[0])

            # Get command
            command = self.agent_command_entry.get()
            if not command:
                return

            # Send command
            if self.agent_manager:
                asyncio.run_coroutine_threadsafe(
                    self.agent_manager.send_command(agent_id, command), asyncio.get_event_loop()
                )

                # Update status
                self.status_var.set(f"Sent command to agent: {agent_id}")

                # Clear command entry
                self.agent_command_entry.delete(0, tk.END)

        except Exception as e:
            logger.exception(f"Error sending agent command: {e}")

    def _refresh_workflows(self):
        """Refresh the workflows tab."""
        try:
            # Get workflows from state manager
            if self.state_manager:
                workflows = asyncio.run_coroutine_threadsafe(
                    self.state_manager.get_state("coordinator", "workflows"), asyncio.get_event_loop()
                ).result()

                if workflows:
                    # Update workflows dictionary
                    self.workflows = workflows

                    # Clear listbox
                    self.workflow_listbox.delete(0, tk.END)

                    # Add workflows to listbox
                    for workflow_id, workflow_data in workflows.items():
                        self.workflow_listbox.insert(tk.END, workflow_id)

        except Exception as e:
            logger.exception(f"Error refreshing workflows: {e}")

    def _on_workflow_selected(self, event):
        """
        Handle workflow selection event.

        Args:
            event: Event object
        """
        try:
            # Get selected workflow
            selection = self.workflow_listbox.curselection()
            if not selection:
                return

            workflow_id = self.workflow_listbox.get(selection[0])

            # Get workflow data
            workflow_data = self.workflows.get(workflow_id)
            if not workflow_data:
                return

            # Visualize workflow
            self._visualize_workflow(workflow_data)

        except Exception as e:
            logger.exception(f"Error handling workflow selection: {e}")

    def _visualize_workflow(self, workflow_data):
        """
        Visualize a workflow.

        Args:
            workflow_data: Workflow data
        """
        try:
            # Clear canvas
            self.workflow_canvas.delete("all")

            # Create graph
            G = nx.DiGraph()

            # Add nodes and edges
            tasks = workflow_data.get("tasks", {})
            for task_id, task_data in tasks.items():
                G.add_node(task_id, **task_data)

                # Add edges for dependencies
                for dep_id in task_data.get("dependencies", []):
                    G.add_edge(dep_id, task_id)

            # Get node positions
            pos = nx.spring_layout(G)

            # Draw nodes
            for node, (x, y) in pos.items():
                # Scale coordinates to canvas size
                canvas_x = x * 400 + 200
                canvas_y = y * 300 + 150

                # Get node status
                status = G.nodes[node].get("status", "pending")

                # Set color based on status
                if status == "completed":
                    color = "green"
                elif status == "running":
                    color = "yellow"
                elif status == "failed":
                    color = "red"
                else:
                    color = "lightgray"

                # Draw node
                self.workflow_canvas.create_oval(
                    canvas_x - 20, canvas_y - 20,
                    canvas_x + 20, canvas_y + 20,
                    fill=color, outline="black"
                )

                # Draw label
                self.workflow_canvas.create_text(
                    canvas_x, canvas_y,
                    text=node
                )

            # Draw edges
            for u, v in G.edges():
                # Get coordinates
                x1, y1 = pos[u]
                x2, y2 = pos[v]

                # Scale coordinates to canvas size
                canvas_x1 = x1 * 400 + 200
                canvas_y1 = y1 * 300 + 150
                canvas_x2 = x2 * 400 + 200
                canvas_y2 = y2 * 300 + 150

                # Draw edge
                self.workflow_canvas.create_line(
                    canvas_x1, canvas_y1,
                    canvas_x2, canvas_y2,
                    arrow=tk.LAST
                )

        except Exception as e:
            logger.exception(f"Error visualizing workflow: {e}")

    def _create_workflow(self):
        """Create a new workflow."""
        # This would open a dialog to create a new workflow
        pass

    def _run_workflow(self):
        """Run the selected workflow."""
        try:
            # Get selected workflow
            selection = self.workflow_listbox.curselection()
            if not selection:
                return

            workflow_id = self.workflow_listbox.get(selection[0])

            # Run workflow
            if self.agent_manager:
                asyncio.run_coroutine_threadsafe(
                    self.agent_manager.run_workflow(workflow_id), asyncio.get_event_loop()
                )

                # Update status
                self.status_var.set(f"Running workflow: {workflow_id}")

        except Exception as e:
            logger.exception(f"Error running workflow: {e}")

    def _export_workflow(self):
        """Export the selected workflow."""
        # This would export the selected workflow to a file
        pass

    def _import_workflow(self):
        """Import a workflow from a file."""
        # This would import a workflow from a file
        pass

    def _refresh_browser(self):
        """Refresh the browser tab."""
        try:
            # Take a screenshot
            self._take_screenshot()

        except Exception as e:
            logger.exception(f"Error refreshing browser: {e}")

    def _start_browser(self):
        """Start the browser."""
        try:
            # Start browser
            if self.ui_tars_connector:
                asyncio.run_coroutine_threadsafe(
                    self.ui_tars_connector.start_browser(), asyncio.get_event_loop()
                )

                # Update status
                self.status_var.set("Starting browser")

        except Exception as e:
            logger.exception(f"Error starting browser: {e}")

    def _take_screenshot(self):
        """Take a screenshot."""
        try:
            # Take screenshot
            if self.ui_tars_connector:
                asyncio.run_coroutine_threadsafe(
                    self.ui_tars_connector.take_screenshot(), asyncio.get_event_loop()
                )

                # Update status
                self.status_var.set("Taking screenshot")

        except Exception as e:
            logger.exception(f"Error taking screenshot: {e}")

    def _execute_browser_command(self):
        """Execute a browser command."""
        try:
            # Get command
            command = self.browser_command_entry.get()
            if not command:
                return

            # Execute command
            if self.ui_tars_connector:
                asyncio.run_coroutine_threadsafe(
                    self.ui_tars_connector.execute_command(command), asyncio.get_event_loop()
                )

                # Update status
                self.status_var.set(f"Executing command: {command}")

                # Clear command entry
                self.browser_command_entry.delete(0, tk.END)

                # Take screenshot after a delay
                self.root.after(2000, self._take_screenshot)

        except Exception as e:
            logger.exception(f"Error executing browser command: {e}")

    def _execute_command(self):
        """Execute a command."""
        try:
            # Get command
            command = self.command_entry.get()
            if not command:
                return

            # Add command to history
            self.command_history.append(command)
            self.command_index = len(self.command_history)

            # Clear command entry
            self.command_entry.delete(0, tk.END)

            # Update status
            self.status_var.set(f"Executing command: {command}")

            # Add command to output
            self._append_to_output(f">> {command}\n", "command")

            # Execute command
            if command.startswith("jarvis ") and self.jarvis_interface:
                # Execute Jarvis command
                jarvis_command = command[7:]
                asyncio.run_coroutine_threadsafe(
                    self.jarvis_interface.execute_command(jarvis_command), asyncio.get_event_loop()
                )

            elif command.startswith("evolve ") and self.alpha_evolve_engine:
                # Execute AlphaEvolve command
                evolve_command = command[7:]
                asyncio.run_coroutine_threadsafe(
                    self.alpha_evolve_engine.execute_command(evolve_command), asyncio.get_event_loop()
                )

            elif command.startswith("ui-tars ") and self.ui_tars_connector:
                # Execute UI-TARS command
                ui_tars_command = command[8:]
                asyncio.run_coroutine_threadsafe(
                    self.ui_tars_connector.execute_command(ui_tars_command), asyncio.get_event_loop()
                )

            else:
                # Unknown command
                self._append_to_output(f"Unknown command: {command}\n", "error")

        except Exception as e:
            logger.exception(f"Error executing command: {e}")
            self._append_to_output(f"Error: {e}\n", "error")

    def _clear_command_output(self):
        """Clear the command output."""
        self.output_text.config(state=tk.NORMAL)
        self.output_text.delete(1.0, tk.END)
        self.output_text.config(state=tk.DISABLED)

    def _save_settings(self):
        """Save settings."""
        try:
            # Get settings
            settings = {
                "general": {
                    "theme": self.theme_var.get(),
                    "refresh_rate": int(self.refresh_spinbox.get())
                },
                "ui_tars": {
                    "api_url": self.ui_tars_settings["api_url_entry"].get(),
                    "api_key": self.ui_tars_settings["api_key_entry"].get(),
                    "model_name": self.ui_tars_settings["model_name_entry"].get(),
                    "browser_type": self.ui_tars_settings["browser_type_combobox"].get(),
                    "auto_start": self.ui_tars_settings["auto_start_var"].get(),
                    "auto_restart": self.ui_tars_settings["auto_restart_var"].get()
                },
                "crewai": {
                    "llm_provider": self.crewai_settings["llm_provider_combobox"].get(),
                    "api_key": self.crewai_settings["api_key_entry"].get(),
                    "process": self.crewai_settings["process_combobox"].get(),
                    "verbose": self.crewai_settings["verbose_var"].get()
                },
                "alpha_evolve": {
                    "population_size": int(self.alpha_evolve_settings["population_size_spinbox"].get()),
                    "generations": int(self.alpha_evolve_settings["generations_spinbox"].get()),
                    "fitness_threshold": float(self.alpha_evolve_settings["fitness_threshold_spinbox"].get()),
                    "timeout": int(self.alpha_evolve_settings["timeout_spinbox"].get())
                },
                "jarvis": {
                    "voice_enabled": self.jarvis_settings["voice_enabled_var"].get(),
                    "activation_keyword": self.jarvis_settings["activation_keyword_entry"].get(),
                    "auto_start": self.jarvis_settings["auto_start_var"].get(),
                    "system_monitoring": self.jarvis_settings["system_monitoring_var"].get()
                },
                "google": {
                    "api_key": self.google_settings["api_key_entry"].get(),
                    "project_id": self.google_settings["project_id_entry"].get(),
                    "region": self.google_settings["region_combobox"].get(),
                    "a2a_protocol": self.google_settings["a2a_protocol_var"].get()
                }
            }

            # Save settings to file
            with open("config/unified_dashboard_settings.json", "w") as f:
                json.dump(settings, f, indent=4)

            # Update status
            self.status_var.set("Settings saved")

            # Apply settings
            self.refresh_rate = int(self.refresh_spinbox.get())

        except Exception as e:
            logger.exception(f"Error saving settings: {e}")
            self.status_var.set(f"Error saving settings: {e}")

    def _cancel_settings(self):
        """Cancel settings changes."""
        # Load settings from file
        self._load_settings_from_file()

        # Update status
        self.status_var.set("Settings changes cancelled")

    def _load_settings_from_file(self):
        """Load settings from file."""
        try:
            # Check if settings file exists
            if not os.path.exists("config/unified_dashboard_settings.json"):
                return

            # Load settings from file
            with open("config/unified_dashboard_settings.json", "r") as f:
                settings = json.load(f)

            # Apply general settings
            if "general" in settings:
                self.theme_var.set(settings["general"].get("theme", "light"))
                self.refresh_rate = settings["general"].get("refresh_rate", 5)
                self.refresh_spinbox.set(self.refresh_rate)

            # Apply UI-TARS settings
            if "ui_tars" in settings:
                self.ui_tars_settings["api_url_entry"].delete(0, tk.END)
                self.ui_tars_settings["api_url_entry"].insert(0, settings["ui_tars"].get("api_url", "http://localhost:8080"))

                self.ui_tars_settings["api_key_entry"].delete(0, tk.END)
                self.ui_tars_settings["api_key_entry"].insert(0, settings["ui_tars"].get("api_key", "hf_dummy_key"))

                self.ui_tars_settings["model_name_entry"].delete(0, tk.END)
                self.ui_tars_settings["model_name_entry"].insert(0, settings["ui_tars"].get("model_name", "UI-TARS-1.5-7B"))

                self.ui_tars_settings["browser_type_combobox"].set(settings["ui_tars"].get("browser_type", "chrome"))
                self.ui_tars_settings["auto_start_var"].set(settings["ui_tars"].get("auto_start", True))
                self.ui_tars_settings["auto_restart_var"].set(settings["ui_tars"].get("auto_restart", True))

            # Apply CrewAI settings
            if "crewai" in settings:
                self.crewai_settings["llm_provider_combobox"].set(settings["crewai"].get("llm_provider", "Local"))

                self.crewai_settings["api_key_entry"].delete(0, tk.END)
                self.crewai_settings["api_key_entry"].insert(0, settings["crewai"].get("api_key", ""))

                self.crewai_settings["process_combobox"].set(settings["crewai"].get("process", "sequential"))
                self.crewai_settings["verbose_var"].set(settings["crewai"].get("verbose", True))

            # Apply AlphaEvolve settings
            if "alpha_evolve" in settings:
                self.alpha_evolve_settings["population_size_spinbox"].set(settings["alpha_evolve"].get("population_size", 50))
                self.alpha_evolve_settings["generations_spinbox"].set(settings["alpha_evolve"].get("generations", 100))
                self.alpha_evolve_settings["fitness_threshold_spinbox"].set(settings["alpha_evolve"].get("fitness_threshold", 0.95))
                self.alpha_evolve_settings["timeout_spinbox"].set(settings["alpha_evolve"].get("timeout", 3600))

            # Apply Jarvis settings
            if "jarvis" in settings:
                self.jarvis_settings["voice_enabled_var"].set(settings["jarvis"].get("voice_enabled", False))

                self.jarvis_settings["activation_keyword_entry"].delete(0, tk.END)
                self.jarvis_settings["activation_keyword_entry"].insert(0, settings["jarvis"].get("activation_keyword", "jarvis"))

                self.jarvis_settings["auto_start_var"].set(settings["jarvis"].get("auto_start", True))
                self.jarvis_settings["system_monitoring_var"].set(settings["jarvis"].get("system_monitoring", True))

            # Apply Google settings
            if "google" in settings:
                self.google_settings["api_key_entry"].delete(0, tk.END)
                self.google_settings["api_key_entry"].insert(0, settings["google"].get("api_key", ""))

                self.google_settings["project_id_entry"].delete(0, tk.END)
                self.google_settings["project_id_entry"].insert(0, settings["google"].get("project_id", ""))

                self.google_settings["region_combobox"].set(settings["google"].get("region", "us-central1"))
                self.google_settings["a2a_protocol_var"].set(settings["google"].get("a2a_protocol", True))

        except Exception as e:
            logger.exception(f"Error loading settings: {e}")

    def run(self):
        """Run the Unified Agent Dashboard."""
        logger.info("Running Unified Agent Dashboard")

        # Initialize the dashboard
        self.initialize()

        # Load settings from file
        self._load_settings_from_file()

        # Configure text tags
        self.output_text.tag_configure("command", foreground="blue")
        self.output_text.tag_configure("result", foreground="green")
        self.output_text.tag_configure("error", foreground="red")
        self.output_text.tag_configure("info", foreground="black")

        # Start the main loop
        self.root.mainloop()

        # Clean up
        self.is_running = False

        logger.info("Unified Agent Dashboard stopped")
