"""
Interactive Gmail setup script.
This script provides a more interactive way to set up Gmail integration.
"""
import os
import sys
import json
import time
import subprocess
import webbrowser
from pathlib import Path

def clear_screen():
    """Clear the terminal screen."""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_header():
    """Print the script header."""
    clear_screen()
    print("=" * 80)
    print("                     INTERACTIVE GMAIL SETUP WIZARD")
    print("=" * 80)
    print("\nThis wizard will help you set up Gmail integration for your AI Agent System.")
    print("You'll be guided through each step with clear instructions and visual feedback.")
    print("\n")

def create_credentials_directory():
    """Create the credentials directory if it doesn't exist."""
    os.makedirs('credentials', exist_ok=True)
    print("✓ Credentials directory is ready.")

def get_configured_accounts():
    """
    Get a list of configured Gmail accounts.
    
    Returns:
        list: List of configured Gmail accounts
    """
    accounts = []
    credentials_dir = 'credentials'
    
    if not os.path.exists(credentials_dir):
        return accounts
    
    for filename in os.listdir(credentials_dir):
        if filename.startswith('gmail_') and filename.endswith('_credentials.json'):
            # Extract email from filename
            email_part = filename[6:-16]  # Remove 'gmail_' prefix and '_credentials.json' suffix
            email = email_part.replace('_at_', '@').replace('_dot_', '.')
            accounts.append({
                'email': email,
                'credentials_path': os.path.join(credentials_dir, filename),
                'token_path': os.path.join(credentials_dir, filename.replace('_credentials.json', '_token.pickle'))
            })
    
    return accounts

def open_browser_with_confirmation(url, description):
    """
    Open a URL in the browser and confirm with the user.
    
    Args:
        url (str): URL to open
        description (str): Description of what the URL is for
    """
    print(f"\nOpening {description} in your browser...")
    print(f"URL: {url}")
    
    # Try to open the browser
    try:
        # First attempt with the default browser
        webbrowser.open(url)
        print("✓ Browser should be opening now.")
    except Exception as e:
        print(f"Error opening browser: {e}")
        print("\nPlease manually open this URL in your browser:")
        print(url)
    
    # Ask for confirmation
    confirmation = input("\nDid the browser open correctly? (y/n): ").lower()
    
    if confirmation != 'y':
        print("\nLet's try again with a different method.")
        
        try:
            # Try with a specific browser
            if os.name == 'nt':  # Windows
                os.startfile(url)
            else:
                # Try common browsers on other platforms
                browsers = ['google-chrome', 'chrome', 'firefox', 'safari']
                for browser in browsers:
                    try:
                        subprocess.Popen([browser, url])
                        break
                    except:
                        continue
            
            print("✓ Browser should be opening now with an alternative method.")
        except Exception as e:
            print(f"Error opening browser with alternative method: {e}")
            print("\nPlease manually open this URL in your browser:")
            print(url)
        
        # Final confirmation
        input("\nPress Enter when you have the page open in your browser...")
    
    return

def setup_oauth_settings():
    """Set up OAuth settings for Gmail authentication."""
    print_header()
    print("STEP 1: CONFIGURE OAUTH SETTINGS")
    print("-" * 80)
    
    print("\nTo use Gmail with your AI agents, you need to configure OAuth settings.")
    print("This involves setting up the OAuth consent screen and authorized redirect URIs.")
    
    # Step 1: Configure OAuth consent screen
    print("\n1. Configure OAuth Consent Screen")
    print("--------------------------------")
    print("You need to configure the OAuth consent screen with the following settings:")
    print("- App name: 'AI Agent System' (or your preferred name)")
    print("- User support email: Your email address")
    print("- Developer contact information: Your email address")
    print("- Scopes:")
    print("  - https://www.googleapis.com/auth/gmail.readonly")
    print("  - https://www.googleapis.com/auth/gmail.send")
    print("  - https://www.googleapis.com/auth/gmail.compose")
    print("  - https://www.googleapis.com/auth/gmail.modify")
    print("- Test users: Add all your Gmail accounts")
    
    # Open OAuth consent screen
    open_browser_with_confirmation(
        "https://console.cloud.google.com/apis/credentials/consent",
        "OAuth consent screen"
    )
    
    # Step 2: Update redirect URIs
    print("\n2. Update Authorized Redirect URIs")
    print("--------------------------------")
    print("You need to add the following redirect URIs to your OAuth client:")
    print("- http://localhost:55253/")
    print("- http://localhost:0/")
    
    # Open Credentials page
    open_browser_with_confirmation(
        "https://console.cloud.google.com/apis/credentials",
        "Credentials page"
    )
    
    # Step 3: Enable Gmail API
    print("\n3. Enable Gmail API")
    print("----------------")
    print("You need to enable the Gmail API for your project.")
    
    # Open API Library
    open_browser_with_confirmation(
        "https://console.cloud.google.com/apis/library/gmail.googleapis.com",
        "Gmail API page"
    )
    
    print("\n✓ OAuth settings have been configured.")
    input("\nPress Enter to continue to the next step...")

def copy_credentials_file(email):
    """
    Copy credentials file for a specific email.
    
    Args:
        email (str): Email address
    
    Returns:
        bool: True if successful, False otherwise
    """
    print_header()
    print(f"COPYING CREDENTIALS FOR {email}")
    print("-" * 80)
    
    # Create a safe filename from the email address
    safe_email = email.replace("@", "_at_").replace(".", "_dot_")
    credentials_path = f'credentials/gmail_{safe_email}_credentials.json'
    
    print(f"\nWe need to copy the OAuth 2.0 credentials file for {email}.")
    print("This file should have been downloaded from the Google Cloud Console.")
    
    # Ask for the path to the downloaded file
    downloaded_file = input("\nEnter the path to the downloaded credentials file: ")
    
    # Check if the file exists
    if not os.path.exists(downloaded_file):
        print(f"\nError: File not found at {downloaded_file}")
        retry = input("Do you want to try again? (y/n): ").lower()
        if retry == 'y':
            return copy_credentials_file(email)
        return False
    
    # Copy the file
    try:
        import shutil
        shutil.copy(downloaded_file, credentials_path)
        print(f"\n✓ Credentials file copied to {credentials_path}")
        return True
    except Exception as e:
        print(f"\nError copying file: {e}")
        retry = input("Do you want to try again? (y/n): ").lower()
        if retry == 'y':
            return copy_credentials_file(email)
        return False

def test_gmail_auth(account):
    """
    Test Gmail authentication for a specific account.
    
    Args:
        account (dict): Account information
        
    Returns:
        bool: True if authentication was successful, False otherwise
    """
    email = account['email']
    credentials_path = account['credentials_path']
    token_path = account['token_path']
    
    print_header()
    print(f"TESTING GMAIL AUTHENTICATION FOR {email}")
    print("-" * 80)
    
    # Gmail API scopes
    SCOPES = [
        'https://www.googleapis.com/auth/gmail.readonly',
        'https://www.googleapis.com/auth/gmail.send',
        'https://www.googleapis.com/auth/gmail.compose',
        'https://www.googleapis.com/auth/gmail.modify'
    ]
    
    # Check if credentials file exists
    if not os.path.exists(credentials_path):
        print(f"\nError: Credentials file not found at {credentials_path}")
        return False
    
    # Remove token file if it exists (to force re-authentication)
    if os.path.exists(token_path):
        os.remove(token_path)
        print(f"\n✓ Removed existing token file: {token_path}")
    
    try:
        # Install required packages if not already installed
        try:
            from google.auth.transport.requests import Request
            from google.oauth2.credentials import Credentials
            from google_auth_oauthlib.flow import InstalledAppFlow
            from googleapiclient.discovery import build
            from googleapiclient.errors import HttpError
        except ImportError:
            print("\nInstalling required packages...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", 
                                  "google-auth", "google-auth-oauthlib", 
                                  "google-auth-httplib2", "google-api-python-client"])
            
            from google.auth.transport.requests import Request
            from google.oauth2.credentials import Credentials
            from google_auth_oauthlib.flow import InstalledAppFlow
            from googleapiclient.discovery import build
            from googleapiclient.errors import HttpError
        
        print("\nStarting authentication process...")
        print("A browser window will open for you to sign in to your Google account.")
        print("Please follow the instructions in the browser.")
        
        # Get new credentials
        flow = InstalledAppFlow.from_client_secrets_file(credentials_path, SCOPES)
        creds = flow.run_local_server(port=0)
        
        # Save the credentials for the next run
        with open(token_path, 'wb') as token:
            pickle.dump(creds, token)
        
        print("\n✓ Authentication successful!")
        print("✓ Token saved for future use.")
        
        # Build the service
        print("\nConnecting to Gmail API...")
        service = build('gmail', 'v1', credentials=creds)
        
        # Get user profile
        profile = service.users().getProfile(userId='me').execute()
        user_email = profile.get('emailAddress')
        
        print(f"\n✓ Successfully authenticated as {user_email}")
        
        # List a few messages to test the connection
        print("\nTesting connection by retrieving messages...")
        results = service.users().messages().list(userId='me', maxResults=5).execute()
        messages = results.get('messages', [])
        
        if not messages:
            print("No messages found.")
        else:
            print(f"✓ Found {len(messages)} messages.")
            
            # Get the first message details
            msg = service.users().messages().get(userId='me', id=messages[0]['id']).execute()
            headers = msg['payload']['headers']
            subject = next((header['value'] for header in headers if header['name'] == 'Subject'), 'No subject')
            sender = next((header['value'] for header in headers if header['name'] == 'From'), 'Unknown sender')
            
            print(f"✓ Latest message: '{subject}' from {sender}")
        
        print("\n✓ Gmail authentication test completed successfully!")
        input("\nPress Enter to continue...")
        return True
    
    except Exception as e:
        print(f"\nError testing Gmail authentication: {e}")
        
        # Provide troubleshooting guidance
        print("\nTroubleshooting steps:")
        print("1. Make sure your OAuth consent screen is properly configured")
        print("2. Add all required scopes to your OAuth consent screen")
        print(f"3. Add {email} as a test user")
        print("4. Add http://localhost:0/ to the authorized redirect URIs")
        print("5. Make sure the Gmail API is enabled for your project")
        
        retry = input("\nDo you want to try again? (y/n): ").lower()
        if retry == 'y':
            return test_gmail_auth(account)
        
        return False

def main():
    """Main entry point."""
    print_header()
    
    # Create credentials directory
    create_credentials_directory()
    
    # Get configured accounts
    accounts = get_configured_accounts()
    
    print("\nMAIN MENU")
    print("---------")
    print("1. Set up OAuth settings")
    print("2. Copy credentials file for a new account")
    print("3. Test Gmail authentication")
    print("4. Exit")
    
    choice = input("\nEnter your choice (1-4): ")
    
    if choice == '1':
        # Set up OAuth settings
        setup_oauth_settings()
        main()
    
    elif choice == '2':
        # Copy credentials file for a new account
        print_header()
        print("COPY CREDENTIALS FILE")
        print("-" * 80)
        
        email = input("\nEnter the email address: ")
        if copy_credentials_file(email):
            print("\n✓ Credentials file copied successfully!")
        else:
            print("\nFailed to copy credentials file.")
        
        input("\nPress Enter to return to the main menu...")
        main()
    
    elif choice == '3':
        # Test Gmail authentication
        print_header()
        print("TEST GMAIL AUTHENTICATION")
        print("-" * 80)
        
        # Refresh the list of configured accounts
        accounts = get_configured_accounts()
        
        if not accounts:
            print("\nNo Gmail accounts configured.")
            print("Please copy credentials files for your accounts first.")
            input("\nPress Enter to return to the main menu...")
            main()
            return
        
        print("\nConfigured Gmail accounts:")
        for i, account in enumerate(accounts):
            print(f"{i+1}. {account['email']}")
        
        print(f"{len(accounts)+1}. All accounts")
        print(f"{len(accounts)+2}. Return to main menu")
        
        try:
            index = int(input("\nSelect an account to test: ")) - 1
            
            if index == len(accounts):
                # Test all accounts
                results = {}
                for account in accounts:
                    results[account['email']] = test_gmail_auth(account)
                
                # Print summary
                print_header()
                print("AUTHENTICATION SUMMARY")
                print("-" * 80)
                
                for email, success in results.items():
                    status = "SUCCESS" if success else "FAILED"
                    print(f"{email}: {status}")
                
                input("\nPress Enter to return to the main menu...")
                main()
            
            elif index == len(accounts) + 1:
                # Return to main menu
                main()
            
            elif 0 <= index < len(accounts):
                # Test specific account
                test_gmail_auth(accounts[index])
                main()
            
            else:
                print("\nInvalid selection.")
                input("\nPress Enter to try again...")
                main()
        
        except ValueError:
            print("\nInvalid selection.")
            input("\nPress Enter to try again...")
            main()
    
    elif choice == '4':
        # Exit
        print("\nExiting...")
        sys.exit(0)
    
    else:
        print("\nInvalid choice.")
        input("\nPress Enter to try again...")
        main()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nExiting...")
        sys.exit(0)
