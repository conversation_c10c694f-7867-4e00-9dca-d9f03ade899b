#!/usr/bin/env python3
"""
Setup Gmail Token

This script sets up the Gmail token for the AI Agent System.
"""

import os
import sys
import pickle
import json
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials

# Define the scopes
SCOPES = [
    'https://www.googleapis.com/auth/gmail.readonly',
    'https://www.googleapis.com/auth/gmail.send',
    'https://www.googleapis.com/auth/gmail.compose',
    'https://www.googleapis.com/auth/gmail.modify'
]

def setup_gmail_token(credentials_path: str = 'credentials/gmail_credentials.json', 
                     token_path: str = 'tokens/gmail_token.json'):
    """
    Set up the Gmail token.
    
    Args:
        credentials_path (str): Path to the credentials JSON file
        token_path (str): Path to save the token JSON file
    """
    creds = None
    
    # Load token if it exists
    if os.path.exists(token_path):
        with open(token_path, 'r') as token:
            creds = Credentials.from_authorized_user_info(json.load(token), SCOPES)
    
    # Refresh token if expired
    if creds and creds.expired and creds.refresh_token:
        creds.refresh(Request())
    # Otherwise, get new credentials
    elif not creds:
        if not os.path.exists(credentials_path):
            print(f"Credentials file not found: {credentials_path}")
            return
        
        flow = InstalledAppFlow.from_client_secrets_file(credentials_path, SCOPES)
        creds = flow.run_local_server(port=0)
        
        # Save the credentials for the next run
        with open(token_path, 'w') as token:
            token.write(creds.to_json())
    
    print(f"Gmail token saved to {token_path}")
    print(f"Email: {creds.client_id}")

if __name__ == "__main__":
    # Ensure directories exist
    os.makedirs("credentials", exist_ok=True)
    os.makedirs("tokens", exist_ok=True)
    
    # Set up Gmail token
    setup_gmail_token()
