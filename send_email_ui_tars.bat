@echo off
echo UI-TARS Email Automation
echo ========================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Get email details
set EMAIL=<EMAIL>
set /p PASSWORD="Enter password for %EMAIL%: "
set TO=<EMAIL>
set /p SUBJECT="Enter subject (default: Test Email from UI-TARS): "
if "%SUBJECT%"=="" set SUBJECT=Test Email from UI-TARS
set /p BODY="Enter body (default: This is a test email sent using UI-TARS browser automation.): "
if "%BODY%"=="" set BODY=This is a test email sent using UI-TARS browser automation.

REM Run the UI-TARS Gmail automation script
echo.
echo Sending email from %EMAIL% to %TO%...
echo.

python ui_tars_gmail_automation.py --email "%EMAIL%" --password "%PASSWORD%" --to "%TO%" --subject "%SUBJECT%" --body "%BODY%"

echo.
echo Done.
pause
