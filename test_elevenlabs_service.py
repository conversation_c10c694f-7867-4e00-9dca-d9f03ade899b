#!/usr/bin/env python3
"""
Test ElevenLabs Service

This script tests the ElevenLabs service by generating a test audio file.
"""

import os
import sys
import asyncio
import argparse
import json
from typing import Dict, Any

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import required modules
from services.voice_calling_service import VoiceCallingService
from core.logger import setup_logger

# Set up logger
logger = setup_logger("test_elevenlabs_service")

async def test_elevenlabs_service(text: str, voice_id: str = None) -> Dict[str, Any]:
    """
    Test the ElevenLabs service by generating a test audio file.
    
    Args:
        text (str): Text to convert to speech
        voice_id (str, optional): Voice ID to use
        
    Returns:
        Dict[str, Any]: Result of the operation
    """
    # Load configuration
    config_path = os.path.join("config", "communication_services.json")
    with open(config_path, "r") as f:
        config = json.load(f)
    
    # Get voice calling service configuration
    voice_config = config.get("voice_calling_service", {})
    
    # Create a simplified config for the voice calling service
    simplified_config = {
        "enabled": True,
        "default_voice_id": voice_config.get("default_voice_id", ""),
        "elevenlabs_api_key": voice_config.get("elevenlabs_api_key", "")
    }
    
    # Initialize voice calling service
    voice_calling_service = VoiceCallingService(simplified_config)
    await voice_calling_service.initialize()
    
    if not voice_calling_service.enabled:
        return {"success": False, "error": "Voice calling service is not enabled"}
    
    # Check ElevenLabs status
    elevenlabs_status = voice_calling_service.service_status.get("elevenlabs", {}).get("status")
    if elevenlabs_status != "available":
        return {"success": False, "error": f"ElevenLabs service is not available: {elevenlabs_status}"}
    
    # Use the provided voice ID or the default one
    if not voice_id:
        voice_id = voice_calling_service.default_voice_id
    
    # Generate the audio
    result = await voice_calling_service._generate_elevenlabs_audio(text, voice_id)
    
    if "error" in result:
        return {"success": False, "error": result["error"]}
    
    return {
        "success": True, 
        "message": "Audio generated successfully", 
        "audio_file": result.get("audio_file"),
        "audio_url": result.get("audio_url")
    }

async def main():
    """Main function to run the script."""
    parser = argparse.ArgumentParser(description="Test ElevenLabs Service")
    parser.add_argument("--text", default="Hello, this is a test of the ElevenLabs text-to-speech service.", help="Text to convert to speech")
    parser.add_argument("--voice-id", help="Voice ID to use")
    
    args = parser.parse_args()
    
    logger.info("Testing ElevenLabs service by generating an audio file")
    
    # Test ElevenLabs service
    result = await test_elevenlabs_service(args.text, args.voice_id)
    
    if result["success"]:
        logger.info("ElevenLabs service test successful!")
        logger.info(f"Audio file generated: {result['audio_file']}")
    else:
        logger.error(f"ElevenLabs service test failed: {result['error']}")
    
    return result

if __name__ == "__main__":
    asyncio.run(main())
