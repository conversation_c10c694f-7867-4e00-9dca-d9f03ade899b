"""
<PERSON><PERSON><PERSON> to install cybersecurity tools for the Cybersecurity Agent.

This script automates the installation of various cybersecurity tools
required by the Cybersecurity Agent.
"""
import sys
import os
import argparse
import subprocess
import platform
import shutil
from pathlib import Path
import logging
import tempfile
import zipfile
import tarfile
import urllib.request
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger("install_security_tools")

# Tool definitions
TOOLS = {
    "nmap": {
        "name": "Nmap",
        "description": "Network mapper for network discovery and security auditing",
        "windows": {
            "url": "https://nmap.org/dist/nmap-7.94-setup.exe",
            "installer_type": "exe",
            "install_args": ["/S"],
            "check_cmd": "nmap --version",
        },
        "linux": {
            "package": "nmap",
            "apt": "apt-get install -y nmap",
            "yum": "yum install -y nmap",
            "check_cmd": "nmap --version",
        },
        "darwin": {
            "brew": "brew install nmap",
            "check_cmd": "nmap --version",
        },
    },
    "john": {
        "name": "John the Ripper",
        "description": "Password cracker",
        "windows": {
            "url": "https://www.openwall.com/john/k/john-1.9.0-jumbo-1-win64.zip",
            "installer_type": "zip",
            "bin_path": "john-1.9.0-jumbo-1-win64/run/john.exe",
            "check_cmd": "john --version",
        },
        "linux": {
            "package": "john",
            "apt": "apt-get install -y john",
            "yum": "yum install -y john",
            "check_cmd": "john --version",
        },
        "darwin": {
            "brew": "brew install john",
            "check_cmd": "john --version",
        },
    },
    "sqlmap": {
        "name": "SQLMap",
        "description": "Automatic SQL injection tool",
        "windows": {
            "url": "https://github.com/sqlmapproject/sqlmap/archive/master.zip",
            "installer_type": "zip",
            "bin_path": "sqlmap-master/sqlmap.py",
            "check_cmd": "python sqlmap.py --version",
        },
        "linux": {
            "package": "sqlmap",
            "apt": "apt-get install -y sqlmap",
            "yum": "yum install -y sqlmap",
            "check_cmd": "sqlmap --version",
        },
        "darwin": {
            "brew": "brew install sqlmap",
            "check_cmd": "sqlmap --version",
        },
        "pip": "pip install sqlmap",
    },
    "nikto": {
        "name": "Nikto",
        "description": "Web server scanner",
        "windows": {
            "url": "https://github.com/sullo/nikto/archive/master.zip",
            "installer_type": "zip",
            "bin_path": "nikto-master/program/nikto.pl",
            "check_cmd": "perl nikto.pl -Version",
        },
        "linux": {
            "package": "nikto",
            "apt": "apt-get install -y nikto",
            "yum": "yum install -y nikto",
            "check_cmd": "nikto -Version",
        },
        "darwin": {
            "brew": "brew install nikto",
            "check_cmd": "nikto -Version",
        },
    },
    "pentestgpt": {
        "name": "PentestGPT",
        "description": "AI-powered penetration testing tool",
        "pip": "pip install pentestgpt",
        "check_cmd": "python -c \"import pentestgpt; print('PentestGPT installed')\"",
    },
}

def check_tool_installed(tool_name):
    """
    Check if a tool is installed.
    
    Args:
        tool_name (str): Name of the tool
        
    Returns:
        bool: True if the tool is installed, False otherwise
    """
    tool = TOOLS.get(tool_name)
    if not tool:
        logger.error(f"Unknown tool: {tool_name}")
        return False
    
    # Get check command
    os_name = platform.system().lower()
    if os_name == "windows":
        check_cmd = tool.get("windows", {}).get("check_cmd")
    elif os_name == "linux":
        check_cmd = tool.get("linux", {}).get("check_cmd")
    elif os_name == "darwin":
        check_cmd = tool.get("darwin", {}).get("check_cmd")
    else:
        check_cmd = None
    
    # If no check command, try pip
    if not check_cmd and "pip" in tool:
        check_cmd = tool.get("check_cmd")
    
    if not check_cmd:
        logger.warning(f"No check command for {tool_name} on {os_name}")
        return False
    
    # Run check command
    try:
        result = subprocess.run(
            check_cmd,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
        )
        return result.returncode == 0
    except Exception as e:
        logger.debug(f"Error checking if {tool_name} is installed: {e}")
        return False

def install_tool(tool_name, tools_dir=None):
    """
    Install a tool.
    
    Args:
        tool_name (str): Name of the tool
        tools_dir (str): Directory to install tools to
        
    Returns:
        bool: True if the tool was installed successfully, False otherwise
    """
    tool = TOOLS.get(tool_name)
    if not tool:
        logger.error(f"Unknown tool: {tool_name}")
        return False
    
    # Check if tool is already installed
    if check_tool_installed(tool_name):
        logger.info(f"{tool['name']} is already installed")
        return True
    
    # Get installation method
    os_name = platform.system().lower()
    
    # Try pip installation first if available
    if "pip" in tool:
        logger.info(f"Installing {tool['name']} using pip")
        try:
            result = subprocess.run(
                tool["pip"],
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
            )
            if result.returncode == 0:
                logger.info(f"{tool['name']} installed successfully using pip")
                return True
            else:
                logger.warning(f"Failed to install {tool['name']} using pip: {result.stderr}")
        except Exception as e:
            logger.warning(f"Error installing {tool['name']} using pip: {e}")
    
    # OS-specific installation
    if os_name == "windows":
        return install_tool_windows(tool_name, tool, tools_dir)
    elif os_name == "linux":
        return install_tool_linux(tool_name, tool)
    elif os_name == "darwin":
        return install_tool_darwin(tool_name, tool)
    else:
        logger.error(f"Unsupported operating system: {os_name}")
        return False

def install_tool_windows(tool_name, tool, tools_dir=None):
    """
    Install a tool on Windows.
    
    Args:
        tool_name (str): Name of the tool
        tool (dict): Tool definition
        tools_dir (str): Directory to install tools to
        
    Returns:
        bool: True if the tool was installed successfully, False otherwise
    """
    windows_info = tool.get("windows")
    if not windows_info:
        logger.error(f"No Windows installation information for {tool['name']}")
        return False
    
    url = windows_info.get("url")
    if not url:
        logger.error(f"No download URL for {tool['name']} on Windows")
        return False
    
    installer_type = windows_info.get("installer_type")
    if not installer_type:
        logger.error(f"No installer type for {tool['name']} on Windows")
        return False
    
    # Create temp directory
    with tempfile.TemporaryDirectory() as temp_dir:
        # Download installer
        installer_path = os.path.join(temp_dir, os.path.basename(url))
        logger.info(f"Downloading {tool['name']} from {url}")
        try:
            urllib.request.urlretrieve(url, installer_path)
        except Exception as e:
            logger.error(f"Error downloading {tool['name']}: {e}")
            return False
        
        # Install based on installer type
        if installer_type == "exe":
            # Run installer
            install_args = windows_info.get("install_args", [])
            cmd = [installer_path] + install_args
            logger.info(f"Running installer: {' '.join(cmd)}")
            try:
                result = subprocess.run(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                )
                if result.returncode != 0:
                    logger.error(f"Error installing {tool['name']}: {result.stderr}")
                    return False
            except Exception as e:
                logger.error(f"Error running installer for {tool['name']}: {e}")
                return False
        
        elif installer_type == "zip":
            # Extract zip
            logger.info(f"Extracting {tool['name']} zip file")
            try:
                with zipfile.ZipFile(installer_path, "r") as zip_ref:
                    zip_ref.extractall(temp_dir)
            except Exception as e:
                logger.error(f"Error extracting {tool['name']} zip file: {e}")
                return False
            
            # Copy binary to tools directory
            bin_path = windows_info.get("bin_path")
            if bin_path and tools_dir:
                src_path = os.path.join(temp_dir, bin_path)
                dst_dir = os.path.join(tools_dir, tool_name)
                os.makedirs(dst_dir, exist_ok=True)
                dst_path = os.path.join(dst_dir, os.path.basename(bin_path))
                logger.info(f"Copying {tool['name']} binary to {dst_path}")
                try:
                    shutil.copy2(src_path, dst_path)
                except Exception as e:
                    logger.error(f"Error copying {tool['name']} binary: {e}")
                    return False
        
        else:
            logger.error(f"Unsupported installer type: {installer_type}")
            return False
    
    # Check if installation was successful
    if check_tool_installed(tool_name):
        logger.info(f"{tool['name']} installed successfully")
        return True
    else:
        logger.error(f"Failed to install {tool['name']}")
        return False

def install_tool_linux(tool_name, tool):
    """
    Install a tool on Linux.
    
    Args:
        tool_name (str): Name of the tool
        tool (dict): Tool definition
        
    Returns:
        bool: True if the tool was installed successfully, False otherwise
    """
    linux_info = tool.get("linux")
    if not linux_info:
        logger.error(f"No Linux installation information for {tool['name']}")
        return False
    
    # Check if apt is available
    if linux_info.get("apt") and shutil.which("apt-get"):
        logger.info(f"Installing {tool['name']} using apt")
        try:
            result = subprocess.run(
                linux_info["apt"],
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
            )
            if result.returncode == 0:
                logger.info(f"{tool['name']} installed successfully using apt")
                return True
            else:
                logger.warning(f"Failed to install {tool['name']} using apt: {result.stderr}")
        except Exception as e:
            logger.warning(f"Error installing {tool['name']} using apt: {e}")
    
    # Check if yum is available
    if linux_info.get("yum") and shutil.which("yum"):
        logger.info(f"Installing {tool['name']} using yum")
        try:
            result = subprocess.run(
                linux_info["yum"],
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
            )
            if result.returncode == 0:
                logger.info(f"{tool['name']} installed successfully using yum")
                return True
            else:
                logger.warning(f"Failed to install {tool['name']} using yum: {result.stderr}")
        except Exception as e:
            logger.warning(f"Error installing {tool['name']} using yum: {e}")
    
    logger.error(f"Failed to install {tool['name']} on Linux")
    return False

def install_tool_darwin(tool_name, tool):
    """
    Install a tool on macOS.
    
    Args:
        tool_name (str): Name of the tool
        tool (dict): Tool definition
        
    Returns:
        bool: True if the tool was installed successfully, False otherwise
    """
    darwin_info = tool.get("darwin")
    if not darwin_info:
        logger.error(f"No macOS installation information for {tool['name']}")
        return False
    
    # Check if brew is available
    if darwin_info.get("brew") and shutil.which("brew"):
        logger.info(f"Installing {tool['name']} using Homebrew")
        try:
            result = subprocess.run(
                darwin_info["brew"],
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
            )
            if result.returncode == 0:
                logger.info(f"{tool['name']} installed successfully using Homebrew")
                return True
            else:
                logger.warning(f"Failed to install {tool['name']} using Homebrew: {result.stderr}")
        except Exception as e:
            logger.warning(f"Error installing {tool['name']} using Homebrew: {e}")
    
    logger.error(f"Failed to install {tool['name']} on macOS")
    return False

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Install cybersecurity tools")
    parser.add_argument("--tools", nargs="+", help="Tools to install (default: all)")
    parser.add_argument("--tools-dir", help="Directory to install tools to")
    parser.add_argument("--check", action="store_true", help="Check if tools are installed")
    args = parser.parse_args()
    
    # Get tools to install
    tools_to_install = args.tools or list(TOOLS.keys())
    
    # Get tools directory
    tools_dir = args.tools_dir
    if tools_dir:
        os.makedirs(tools_dir, exist_ok=True)
    
    # Check or install tools
    success = True
    for tool_name in tools_to_install:
        if tool_name not in TOOLS:
            logger.error(f"Unknown tool: {tool_name}")
            success = False
            continue
        
        if args.check:
            # Check if tool is installed
            installed = check_tool_installed(tool_name)
            logger.info(f"{TOOLS[tool_name]['name']}: {'Installed' if installed else 'Not installed'}")
        else:
            # Install tool
            logger.info(f"Installing {TOOLS[tool_name]['name']}...")
            if not install_tool(tool_name, tools_dir):
                success = False
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
