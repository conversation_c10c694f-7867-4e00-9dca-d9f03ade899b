# Gmail Integration Options

This document provides different options for integrating Gmail with your AI Agent System.

## Option 1: OAuth 2.0 Authentication (Desktop Client)

This is the recommended approach for personal use. It uses a Desktop OAuth client that doesn't require verification.

### Setup Instructions

1. Run the `create_desktop_oauth_client.py` script:
   ```
   "C:\Program Files\Python310\python.exe" create_desktop_oauth_client.py
   ```

2. Follow the instructions to create a Desktop OAuth client and download the client secret JSON file.

3. Run the `simple_gmail_auth.py` script to authenticate with your Gmail accounts:
   ```
   "C:\Program Files\Python310\python.exe" simple_gmail_auth.py
   ```

4. After successful authentication, you can send test emails using the `send_gmail_test.py` script:
   ```
   "C:\Program Files\Python310\python.exe" send_gmail_test.py
   ```

### Advantages
- Doesn't require verification from Google
- Works with personal Gmail accounts
- Full access to Gmail API

### Disadvantages
- Requires user interaction for authentication
- Tokens expire and need to be refreshed

## Option 2: Service Account

This approach uses a service account to access Gmail. It's more suitable for automated systems.

### Setup Instructions

1. Run the `gmail_service_account_setup.py` script:
   ```
   "C:\Program Files\Python310\python.exe" gmail_service_account_setup.py
   ```

2. Follow the instructions to create a service account, download the key file, and enable domain-wide delegation.

3. Run the `gmail_service_account_test.py` script to test the service account:
   ```
   "C:\Program Files\Python310\python.exe" gmail_service_account_test.py
   ```

### Advantages
- No user interaction required after setup
- Tokens don't expire
- Can impersonate multiple users

### Disadvantages
- Requires domain-wide delegation (works best with Google Workspace)
- More complex setup
- May require verification for some scopes

## Option 3: Direct Gmail Access (SMTP/IMAP)

This approach uses SMTP and IMAP protocols to directly access Gmail. It's the simplest approach but has limitations.

### Setup Instructions

1. Run the `direct_gmail_access.py` script:
   ```
   "C:\Program Files\Python310\python.exe" direct_gmail_access.py
   ```

2. Enter your Gmail address and password (or app password if you have 2-factor authentication enabled).

3. Choose whether to send or read emails.

### Advantages
- Simple setup
- Works with any Gmail account
- No OAuth or service account required

### Disadvantages
- Limited functionality compared to Gmail API
- Requires storing email password or app password
- Less secure than OAuth

## Creating App Passwords for Gmail

If you're using the direct access approach and have 2-factor authentication enabled, you'll need to create an app password:

1. Go to your Google Account settings: https://myaccount.google.com/
2. Click on "Security" in the left sidebar
3. Under "Signing in to Google", click on "App passwords"
4. Select "Mail" as the app and "Windows Computer" as the device
5. Click "Generate"
6. Use the generated password in the `direct_gmail_access.py` script

## Enabling "Less Secure App Access" (Not Recommended)

If you're using the direct access approach and don't have 2-factor authentication enabled, you may need to enable "Less secure app access":

1. Go to your Google Account settings: https://myaccount.google.com/
2. Click on "Security" in the left sidebar
3. Under "Less secure app access", click on "Turn on access"

**Note:** This is not recommended as it makes your account less secure.

## Troubleshooting

### OAuth Authentication Issues

If you encounter "Access blocked" or "App not verified" errors:
- Make sure you're using a Desktop OAuth client
- Add all your Gmail accounts as test users in the OAuth consent screen
- Consider publishing your app to production

### Service Account Issues

If you encounter issues with the service account:
- Make sure you've enabled domain-wide delegation
- Make sure you've enabled the Gmail API for your project
- Make sure you've entered the correct email address to impersonate

### Direct Access Issues

If you encounter issues with direct access:
- Make sure you've entered the correct email address and password
- If you're using 2-factor authentication, you need to use an app password
- Make sure "Less secure app access" is enabled in your Google account
- Make sure IMAP is enabled in your Gmail settings

## Using Gmail in Your AI Agent System

Once you've set up Gmail integration, you can use it in your AI Agent System:

```python
# Using OAuth authentication
from services.gmail_service import GmailServiceFactory

# Create the Gmail service
gmail_service = GmailServiceFactory.create_service()

# Send an email
result = await gmail_service.send_message(
    to="<EMAIL>",
    subject="Test Email",
    body="This is a test email sent from the AI Agent System."
)

# Using service account
from services.gmail_service_account import GmailServiceAccount

# Create the Gmail service account
gmail_service = GmailServiceAccount.create_service("<EMAIL>")

# Send an email
result = await gmail_service.send_message(
    to="<EMAIL>",
    subject="Test Email",
    body="This is a test email sent from the AI Agent System."
)

# Using direct access
from services.gmail_direct import GmailDirect

# Create the Gmail direct access
gmail_direct = GmailDirect("<EMAIL>", "your-password")

# Send an email
result = await gmail_direct.send_message(
    to="<EMAIL>",
    subject="Test Email",
    body="This is a test email sent from the AI Agent System."
)
```
