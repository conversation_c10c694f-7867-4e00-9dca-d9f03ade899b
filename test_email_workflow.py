"""
Test script for the email workflow.
"""
import asyncio
import logging
import signal
import sys
import uuid
from datetime import datetime
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("test_email_workflow")

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent))

# Import necessary modules
from core.state_manager import StateManager
from core.agent_manager import AgentManager
from core.workflow_engine import WorkflowEngine
from llm.llm_router import LLMRouter
from agents.email_agent import EmailAgent
from agents.multi_account_email_agent import MultiAccountEmailAgent

# Global flag to control system shutdown
shutdown_event = asyncio.Event()

def handle_shutdown(sig, frame):
    """Handle shutdown signals gracefully."""
    logger.info(f"Received shutdown signal {sig}")
    shutdown_event.set()

class MockLLMRouter:
    """Mock LLM router for testing."""

    def __init__(self):
        self.logger = logger

    async def generate_text(self, prompt, **kwargs):
        """Generate mock text."""
        self.logger.info(f"Generating text for prompt: {prompt[:100]}...")
        return {
            "text": f"Mock response for: {prompt[:50]}...",
            "model": "mock-model",
            "tokens": len(prompt) // 4
        }

class MockMemory:
    """Mock memory for testing."""

    def __init__(self):
        self.memories = []
        self.logger = logger

    async def add_memory(self, content, memory_type="episodic", source="test", importance=0.5):
        """Add a mock memory."""
        memory = {
            "id": str(uuid.uuid4()),
            "content": content,
            "type": memory_type,
            "source": source,
            "importance": importance,
            "timestamp": datetime.now().isoformat()
        }
        self.memories.append(memory)
        self.logger.info(f"Added memory: {memory['id']}")
        return memory

async def test_email_workflow():
    """Test the email workflow."""
    logger.info("Starting email workflow test")

    try:
        # Initialize state manager
        state_manager = StateManager()
        await state_manager.initialize()
        logger.info("State manager initialized")

        # Initialize agent manager
        agent_manager = AgentManager(state_manager, shutdown_event)
        logger.info("Agent manager initialized")

        # Create mock LLM router
        llm_router = MockLLMRouter()

        # Register LLM router with agent manager
        agent_manager.register_service("llm_router", llm_router)
        logger.info("LLM router registered with agent manager")

        # Create email agent
        email_agent = EmailAgent(
            agent_id="email_agent",
            name="Email Agent",
            description="Handles email communications with reasoning capabilities"
        )

        # Create multi-account email agent
        multi_account_email_agent = MultiAccountEmailAgent(
            agent_id="multi_account_email_agent",
            name="Multi-Account Email Agent",
            description="Handles email communications across multiple accounts with reasoning capabilities"
        )

        # Register email agents with agent manager
        agent_manager.register_agent("email_agent", email_agent)
        agent_manager.register_agent("multi_account_email_agent", multi_account_email_agent)
        logger.info("Email agents registered with agent manager")

        # Initialize workflow engine
        workflow_engine = WorkflowEngine(agent_manager, state_manager)
        logger.info("Workflow engine initialized")

        # Register workflow engine with agent manager
        agent_manager.register_service("workflow_engine", workflow_engine)
        logger.info("Workflow engine registered with agent manager")

        # Initialize workflow engine
        await workflow_engine.initialize()
        logger.info("Workflow engine initialized")

        # Check if workflows were loaded
        logger.info(f"Loaded workflows: {list(workflow_engine.workflows.keys())}")

        # Trigger a new email event
        logger.info("Triggering new email event")
        await workflow_engine.trigger_event("new_email", {
            "account": "<EMAIL>",
            "email_id": "test_email_123",
            "from": "<EMAIL>",
            "subject": "Test Email",
            "body": "This is a test email"
        })

        # Wait for a while to let workflows run
        logger.info("Waiting for workflows to run")
        await asyncio.sleep(10)

        # Check active workflows
        logger.info(f"Active workflows: {list(workflow_engine.active_workflows.keys())}")

        # Shutdown workflow engine
        logger.info("Shutting down workflow engine")
        await workflow_engine.shutdown()

        # Shutdown agent manager
        logger.info("Shutting down agent manager")
        await agent_manager.stop()

        # Close state manager
        logger.info("Closing state manager")
        await state_manager.close()

        logger.info("Email workflow test completed successfully")

    except Exception as e:
        logger.exception(f"Error in email workflow test: {e}")

if __name__ == "__main__":
    # Register signal handlers
    signal.signal(signal.SIGINT, handle_shutdown)
    signal.signal(signal.SIGTERM, handle_shutdown)

    # Run the test
    asyncio.run(test_email_workflow())
