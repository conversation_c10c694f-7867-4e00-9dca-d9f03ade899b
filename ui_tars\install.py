"""
UI-TARS Installation Script.

This script downloads and installs UI-TARS 1.5 and its dependencies.
"""
import os
import sys
import json
import argparse
import subprocess
import platform
import tempfile
import shutil
import zipfile
import tarfile
import requests
from pathlib import Path
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("ui_tars_install.log")
    ]
)
logger = logging.getLogger("ui_tars_install")

# Constants
UITARS_DESKTOP_URL = "https://github.com/bytedance/UI-TARS-desktop/releases/download/v0.1.0/UI-TARS-desktop-0.1.0"
MIDSCENE_VERSION = "0.16.0"
UITARS_MODEL_URL = "https://huggingface.co/ByteDance-Seed/UI-TARS-1.5-7B"

def check_python_version():
    """Check if the Python version is compatible."""
    logger.info("Checking Python version")
    
    if sys.version_info < (3, 8):
        logger.error("Python 3.8 or higher is required")
        return False
    
    logger.info(f"Python version: {platform.python_version()}")
    return True

def check_pip():
    """Check if pip is installed."""
    logger.info("Checking pip")
    
    try:
        subprocess.run(
            [sys.executable, "-m", "pip", "--version"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            check=True
        )
        logger.info("pip is installed")
        return True
    
    except subprocess.CalledProcessError:
        logger.error("pip is not installed")
        return False

def install_requirements():
    """Install the required packages."""
    logger.info("Installing requirements")
    
    try:
        subprocess.run(
            [sys.executable, "-m", "pip", "install", "-r", "requirements.txt"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            check=True
        )
        logger.info("Requirements installed successfully")
        return True
    
    except subprocess.CalledProcessError as e:
        logger.error(f"Error installing requirements: {e}")
        return False

def install_midscene():
    """Install Midscene."""
    logger.info("Installing Midscene")
    
    try:
        # Check if npm is installed
        npm_check = subprocess.run(
            ["npm", "--version"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        if npm_check.returncode != 0:
            logger.error("npm is not installed. Please install Node.js and npm first.")
            return False
        
        # Install Midscene packages
        packages = [
            "@midscene/web",
            "@midscene/android",
            "@midscene/core",
            "@midscene/cli"
        ]
        
        for package in packages:
            logger.info(f"Installing {package}...")
            result = subprocess.run(
                ["npm", "install", "-g", f"{package}@{MIDSCENE_VERSION}"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            if result.returncode != 0:
                logger.error(f"Failed to install {package}: {result.stderr}")
                return False
            
            logger.info(f"Successfully installed {package}")
        
        logger.info("Midscene installed successfully")
        return True
    
    except Exception as e:
        logger.error(f"Error installing Midscene: {e}")
        return False

def download_uitars_desktop():
    """Download UI-TARS Desktop."""
    logger.info("Downloading UI-TARS Desktop")
    
    try:
        # Create a temporary directory
        temp_dir = tempfile.mkdtemp()
        
        # Determine the file extension based on the OS
        os_type = platform.system()
        if os_type == "Windows":
            file_ext = ".exe"
            file_name = f"{UITARS_DESKTOP_URL}-win.exe"
        elif os_type == "Darwin":  # macOS
            file_ext = ".dmg"
            file_name = f"{UITARS_DESKTOP_URL}-mac.dmg"
        else:  # Linux
            file_ext = ".AppImage"
            file_name = f"{UITARS_DESKTOP_URL}-linux.AppImage"
        
        # Download the file
        logger.info(f"Downloading from {file_name}")
        response = requests.get(file_name, stream=True)
        
        if response.status_code != 200:
            logger.error(f"Failed to download UI-TARS Desktop: {response.status_code}")
            return None
        
        # Save the file
        file_path = os.path.join(temp_dir, f"UI-TARS-desktop{file_ext}")
        with open(file_path, "wb") as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        logger.info(f"UI-TARS Desktop downloaded to {file_path}")
        return file_path
    
    except Exception as e:
        logger.error(f"Error downloading UI-TARS Desktop: {e}")
        return None

def install_uitars_desktop(file_path):
    """Install UI-TARS Desktop."""
    logger.info("Installing UI-TARS Desktop")
    
    try:
        # Determine the installation method based on the OS
        os_type = platform.system()
        if os_type == "Windows":
            # Run the installer
            subprocess.run(
                [file_path, "/S"],  # Silent install
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                check=True
            )
            
            # Default installation path
            install_path = os.path.join(os.environ.get("PROGRAMFILES", "C:\\Program Files"), "UI-TARS")
        
        elif os_type == "Darwin":  # macOS
            # Mount the DMG
            subprocess.run(
                ["hdiutil", "attach", file_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                check=True
            )
            
            # Copy the app to Applications
            subprocess.run(
                ["cp", "-R", "/Volumes/UI-TARS-desktop/UI-TARS.app", "/Applications"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                check=True
            )
            
            # Unmount the DMG
            subprocess.run(
                ["hdiutil", "detach", "/Volumes/UI-TARS-desktop"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                check=True
            )
            
            # Default installation path
            install_path = "/Applications/UI-TARS.app"
        
        else:  # Linux
            # Make the AppImage executable
            os.chmod(file_path, 0o755)
            
            # Create a directory for the app
            install_path = os.path.expanduser("~/.local/bin/ui-tars")
            os.makedirs(os.path.dirname(install_path), exist_ok=True)
            
            # Copy the AppImage
            shutil.copy(file_path, install_path)
        
        logger.info(f"UI-TARS Desktop installed to {install_path}")
        
        # Update the configuration
        update_config("ui_tars", "installation_path", install_path)
        
        return install_path
    
    except Exception as e:
        logger.error(f"Error installing UI-TARS Desktop: {e}")
        return None

def download_uitars_model():
    """Download UI-TARS model."""
    logger.info("Downloading UI-TARS model")
    
    try:
        # Create a directory for the model
        model_dir = os.path.expanduser("~/models/UI-TARS-1.5-7B")
        os.makedirs(model_dir, exist_ok=True)
        
        # Use Hugging Face CLI to download the model
        subprocess.run(
            ["huggingface-cli", "download", "ByteDance-Seed/UI-TARS-1.5-7B", "--local-dir", model_dir],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            check=True
        )
        
        logger.info(f"UI-TARS model downloaded to {model_dir}")
        
        # Update the configuration
        update_config("local_llm", "model_path", model_dir)
        
        return model_dir
    
    except Exception as e:
        logger.error(f"Error downloading UI-TARS model: {e}")
        return None

def update_config(section, key, value):
    """
    Update the configuration file.
    
    Args:
        section (str): Section to update
        key (str): Key to update
        value: Value to set
    """
    try:
        # Load the configuration
        config_path = "config.json"
        if os.path.exists(config_path):
            with open(config_path, "r") as f:
                config = json.load(f)
        else:
            config = {}
        
        # Update the configuration
        if section not in config:
            config[section] = {}
        
        config[section][key] = value
        
        # Save the configuration
        with open(config_path, "w") as f:
            json.dump(config, f, indent=4)
        
        logger.info(f"Configuration updated: {section}.{key} = {value}")
    
    except Exception as e:
        logger.error(f"Error updating configuration: {e}")

def main():
    """Main entry point for the installation script."""
    logger.info("Starting UI-TARS installation")
    
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="UI-TARS Installation Script")
    parser.add_argument("--skip-requirements", action="store_true", help="Skip installing requirements")
    parser.add_argument("--skip-midscene", action="store_true", help="Skip installing Midscene")
    parser.add_argument("--skip-uitars-desktop", action="store_true", help="Skip installing UI-TARS Desktop")
    parser.add_argument("--skip-uitars-model", action="store_true", help="Skip downloading UI-TARS model")
    args = parser.parse_args()
    
    # Check Python version
    if not check_python_version():
        logger.error("Installation failed: Python version check failed")
        return False
    
    # Check pip
    if not check_pip():
        logger.error("Installation failed: pip check failed")
        return False
    
    # Install requirements
    if not args.skip_requirements:
        if not install_requirements():
            logger.error("Installation failed: Failed to install requirements")
            return False
    
    # Install Midscene
    if not args.skip_midscene:
        if not install_midscene():
            logger.warning("Failed to install Midscene, continuing with installation")
    
    # Download and install UI-TARS Desktop
    if not args.skip_uitars_desktop:
        file_path = download_uitars_desktop()
        if file_path:
            install_path = install_uitars_desktop(file_path)
            if not install_path:
                logger.warning("Failed to install UI-TARS Desktop, continuing with installation")
        else:
            logger.warning("Failed to download UI-TARS Desktop, continuing with installation")
    
    # Download UI-TARS model
    if not args.skip_uitars_model:
        model_dir = download_uitars_model()
        if not model_dir:
            logger.warning("Failed to download UI-TARS model, continuing with installation")
    
    logger.info("UI-TARS installation completed")
    return True

if __name__ == "__main__":
    main()
