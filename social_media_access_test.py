"""
<PERSON><PERSON><PERSON> to test social media account access.

This script tests access to social media accounts using stored credentials.
"""
import sys
import os
import argparse
import getpass
import requests
from pathlib import Path
import json
import logging
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent))

from config.secure_credentials import CredentialManager

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger("social_media_access_test")

# Social media configurations
SOCIAL_MEDIA = {
    "facebook": {
        "login_url": "https://www.facebook.com/login",
        "username_field": "email",
        "password_field": "pass",
        "success_indicator": "//div[@aria-label='Your profile']",
        "success_url_contains": "facebook.com/home",
    },
    "twitter": {
        "login_url": "https://twitter.com/i/flow/login",
        "username_field": "text",  # This might need adjustment
        "password_field": "password",
        "success_indicator": "//a[@aria-label='Profile']",
        "success_url_contains": "twitter.com/home",
    },
    "instagram": {
        "login_url": "https://www.instagram.com/accounts/login/",
        "username_field": "username",
        "password_field": "password",
        "success_indicator": "//a[contains(@href, '/direct/inbox/')]",
        "success_url_contains": "instagram.com/",
    },
    "linkedin": {
        "login_url": "https://www.linkedin.com/login",
        "username_field": "session_key",
        "password_field": "session_password",
        "success_indicator": "//div[contains(@class, 'feed-identity-module')]",
        "success_url_contains": "linkedin.com/feed",
    },
}

def test_social_media_access(platform, username, password, headless=True):
    """
    Test access to a social media account using Selenium.
    
    Args:
        platform (str): Social media platform (facebook, twitter, instagram, linkedin)
        username (str): Account username
        password (str): Account password
        headless (bool): Whether to run browser in headless mode
        
    Returns:
        dict: Test results
    """
    logger.info(f"Testing {platform} access for {username}")
    
    # Get platform configuration
    platform_config = SOCIAL_MEDIA.get(platform.lower())
    if not platform_config:
        return {
            "success": False,
            "error": f"Unsupported social media platform: {platform}",
        }
    
    # Set up Selenium
    options = webdriver.ChromeOptions()
    if headless:
        options.add_argument("--headless")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    options.add_argument("--disable-gpu")
    options.add_argument("--window-size=1920,1080")
    options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36")
    
    driver = None
    try:
        # Initialize driver
        driver = webdriver.Chrome(options=options)
        driver.implicitly_wait(10)
        
        # Navigate to login page
        driver.get(platform_config["login_url"])
        logger.info(f"Navigated to {platform_config['login_url']}")
        
        # Wait for page to load
        time.sleep(5)
        
        # Handle different login flows
        if platform.lower() == "twitter":
            # Twitter has a multi-step login process
            username_input = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.NAME, platform_config["username_field"]))
            )
            username_input.send_keys(username)
            username_input.send_keys(Keys.RETURN)
            
            # Wait for password field
            time.sleep(2)
            password_input = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.NAME, platform_config["password_field"]))
            )
            password_input.send_keys(password)
            password_input.send_keys(Keys.RETURN)
        else:
            # Standard login flow
            username_input = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.NAME, platform_config["username_field"]))
            )
            password_input = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.NAME, platform_config["password_field"]))
            )
            
            username_input.send_keys(username)
            password_input.send_keys(password)
            password_input.send_keys(Keys.RETURN)
        
        # Wait for login to complete
        time.sleep(5)
        
        # Check if login was successful
        success = False
        try:
            # Check for success indicator
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.XPATH, platform_config["success_indicator"]))
            )
            success = True
        except TimeoutException:
            # Check URL as fallback
            if platform_config["success_url_contains"] in driver.current_url:
                success = True
        
        # Take screenshot
        screenshot_path = f"results/{platform}_access_test.png"
        driver.save_screenshot(screenshot_path)
        
        # Get page title and URL
        page_title = driver.title
        current_url = driver.current_url
        
        return {
            "success": success,
            "page_title": page_title,
            "current_url": current_url,
            "screenshot_path": screenshot_path,
        }
    
    except Exception as e:
        logger.exception(f"Error testing {platform} access: {e}")
        if driver:
            # Take screenshot of error
            screenshot_path = f"results/{platform}_access_error.png"
            driver.save_screenshot(screenshot_path)
            
            return {
                "success": False,
                "error": str(e),
                "screenshot_path": screenshot_path,
            }
        else:
            return {
                "success": False,
                "error": str(e),
            }
    
    finally:
        # Close driver
        if driver:
            driver.quit()

def run_social_media_test(service, master_password, headless=True):
    """
    Run social media access test.
    
    Args:
        service (str): Service name in credential manager
        master_password (str): Master password for credential decryption
        headless (bool): Whether to run browser in headless mode
    """
    logger.info(f"Testing social media access for {service}")
    
    # Load credentials
    credential_manager = CredentialManager(master_password)
    
    # Check if credentials exist for the service
    credential = credential_manager.get_credential(service)
    if not credential:
        logger.error(f"No credentials found for {service}")
        return
    
    # Get username and password
    username = credential["username"]
    password = credential["password"]
    
    # Get platform from additional info or service name
    platform = credential.get("additional_info", {}).get("platform", service)
    
    # Test access
    result = test_social_media_access(platform, username, password, headless)
    
    # Add service info to result
    result["service"] = service
    result["username"] = username
    result["platform"] = platform
    
    # Save result to file
    result_file = Path(f"results/social_media_access_{service}.json")
    result_file.parent.mkdir(exist_ok=True)
    
    # Remove password from result
    if "password" in result:
        del result["password"]
    
    with open(result_file, "w") as f:
        json.dump(result, f, indent=2)
    
    logger.info(f"Results saved to {result_file}")
    
    # Print summary
    print("\nSocial Media Access Test Summary:")
    print(f"Service: {service}")
    print(f"Username: {username}")
    print(f"Platform: {platform}")
    
    if result["success"]:
        print("Status: Success")
        print(f"Page title: {result.get('page_title', 'N/A')}")
        print(f"Current URL: {result.get('current_url', 'N/A')}")
        print(f"Screenshot saved to: {result.get('screenshot_path', 'N/A')}")
    else:
        print("Status: Failed")
        print(f"Error: {result.get('error', 'Unknown error')}")
        if "screenshot_path" in result:
            print(f"Error screenshot saved to: {result['screenshot_path']}")

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Test social media account access")
    parser.add_argument("--service", help="Service name in credential manager")
    parser.add_argument("--master-password", help="Master password for credential decryption")
    parser.add_argument("--headless", action="store_true", help="Run browser in headless mode")
    parser.add_argument("--visible", action="store_true", help="Run browser in visible mode")
    args = parser.parse_args()
    
    # Interactive mode if arguments are missing
    service = args.service
    master_password = args.master_password
    headless = not args.visible  # Default to headless unless --visible is specified
    
    if not service:
        service = input("Enter service name (as stored in credential manager): ")
    
    if not master_password:
        master_password = getpass.getpass("Enter master password for credential decryption: ")
    
    # Run test
    run_social_media_test(service, master_password, headless)

if __name__ == "__main__":
    main()
