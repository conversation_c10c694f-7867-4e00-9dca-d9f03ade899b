@echo off
REM Start Facebook Lead Integration
REM This script starts the Facebook lead integration to automatically process leads from Facebook campaigns

echo.
echo ===================================
echo    Start Facebook Lead Integration
echo ===================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Set configuration file paths
set CONFIG_FILE=config/facebook_lead_config.json
set CREDENTIALS_FILE=credentials/social_media/facebook.json
set DRIP_CAMPAIGN_CONFIG=config/drip_campaign_config.json

REM Check if credentials file exists
if not exist "%CREDENTIALS_FILE%" (
    echo Facebook API credentials file not found: %CREDENTIALS_FILE%
    echo Creating directory structure...
    
    REM Create directory if it doesn't exist
    if not exist "credentials\social_media" mkdir credentials\social_media
    
    echo Creating template credentials file...
    
    REM Create template credentials file
    echo {> "%CREDENTIALS_FILE%"
    echo     "app_id": "",>> "%CREDENTIALS_FILE%"
    echo     "app_secret": "",>> "%CREDENTIALS_FILE%"
    echo     "access_token": "",>> "%CREDENTIALS_FILE%"
    echo     "page_id": "",>> "%CREDENTIALS_FILE%"
    echo     "webhook_secret": "",>> "%CREDENTIALS_FILE%"
    echo     "enabled": true,>> "%CREDENTIALS_FILE%"
    echo     "last_updated": "%DATE% %TIME%",>> "%CREDENTIALS_FILE%"
    echo     "notes": "Facebook API credentials for Flo Faction Insurance">> "%CREDENTIALS_FILE%"
    echo }>> "%CREDENTIALS_FILE%"
    
    echo Template credentials file created: %CREDENTIALS_FILE%
    echo Please edit this file to add your Facebook API credentials.
    echo.
    
    set /p CONTINUE="Do you want to continue without credentials? (y/n): "
    if /i not "%CONTINUE%"=="y" (
        echo Facebook lead integration cancelled.
        goto end
    )
)

REM Set check interval
set /p CHECK_INTERVAL="Enter check interval in seconds (default: 300): "
if "%CHECK_INTERVAL%"=="" set CHECK_INTERVAL=300

REM Start integration
echo.
echo Starting Facebook lead integration with interval %CHECK_INTERVAL% seconds...
echo.

start "Facebook Lead Integration" cmd /k python facebook_lead_integration.py --config "%CONFIG_FILE%" --credentials "%CREDENTIALS_FILE%" --drip-campaign-config "%DRIP_CAMPAIGN_CONFIG%" --interval %CHECK_INTERVAL%

echo.
echo Facebook lead integration started in a new window.
echo.
echo To stop integration, close the integration window or press Ctrl+C in that window.
echo.

:end
pause
