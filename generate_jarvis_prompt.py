"""
Generate Jarvis Prompt.

This script generates the prompt to start <PERSON> with AlphaEvolve integration.
"""
import os
import sys
from pathlib import Path

def generate_jarvis_prompt():
    """Generate the Jarvis prompt with AlphaEvolve integration."""
    # Get the current directory
    current_dir = Path(__file__).resolve().parent
    
    # Generate the prompt
    prompt = f"""
Hello <PERSON>, please activate with full AlphaEvolve integration.

Initialize the Borg Cluster Management System with AlphaEvolve's evolutionary programming capabilities to:
1. Enhance the learning capabilities of all agents and sub-agents
2. Improve task completion efficiency across the system
3. Optimize agent decision-making and problem-solving abilities
4. Continuously evolve and improve the system autonomously

Please load all necessary tools and dependencies for full AlphaEvolve integration with the Borg cluster management system, MPC servers, and all agents.

System path: {current_dir}
"""
    
    # Print the prompt
    print("\n" + "=" * 80)
    print("JARVIS ACTIVATION PROMPT WITH ALPHAEVOLVE INTEGRATION")
    print("=" * 80)
    print(prompt)
    print("=" * 80)
    print("\nCopy and paste this prompt when starting your computer to activate <PERSON> with AlphaEvolve integration.")
    
    # Save the prompt to a file
    prompt_file = current_dir / "jarvis_alphaevolve_prompt.txt"
    with open(prompt_file, "w") as f:
        f.write(prompt)
    
    print(f"\nThe prompt has been saved to: {prompt_file}")

if __name__ == "__main__":
    generate_jarvis_prompt()
