"""
Setup ngrok for Calendly webhook testing.

This script sets up ngrok to tunnel connections for local webhook testing.
"""
import os
import sys
import json
import time
import argparse
import subprocess
import requests
from typing import Dict, Any, Optional

def start_ngrok(port: int) -> Optional[str]:
    """
    Start ngrok tunnel.
    
    Args:
        port (int): Port to tunnel
        
    Returns:
        Optional[str]: Public URL
    """
    try:
        # Check if ngrok is installed
        try:
            subprocess.run(["ngrok", "--version"], check=True, capture_output=True)
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("ngrok is not installed or not in PATH")
            print("Please install ngrok from https://ngrok.com/download")
            return None
        
        # Start ngrok
        ngrok_process = subprocess.Popen(
            ["ngrok", "http", str(port)],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # Wait for ngrok to start
        print("Starting ngrok...")
        time.sleep(3)
        
        # Get public URL
        try:
            response = requests.get("http://localhost:4040/api/tunnels")
            tunnels = response.json()["tunnels"]
            
            if tunnels:
                public_url = tunnels[0]["public_url"]
                print(f"ngrok tunnel established: {public_url}")
                return public_url
            else:
                print("No ngrok tunnels found")
                return None
        except Exception as e:
            print(f"Error getting ngrok tunnels: {e}")
            return None
    
    except Exception as e:
        print(f"Error starting ngrok: {e}")
        return None

def setup_calendly_webhook(token: str, public_url: str) -> bool:
    """
    Set up Calendly webhook.
    
    Args:
        token (str): Calendly OAuth token
        public_url (str): Public URL for webhook
        
    Returns:
        bool: Success status
    """
    try:
        # Get user information
        user_info_response = requests.get(
            "https://api.calendly.com/users/me",
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}"
            }
        )
        
        if user_info_response.status_code != 200:
            print(f"Error getting user information: {user_info_response.status_code}")
            print(user_info_response.text)
            return False
        
        user_info = user_info_response.json()
        user_uri = user_info["resource"]["uri"]
        
        # Get organization memberships
        org_memberships_response = requests.get(
            "https://api.calendly.com/organization_memberships",
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}"
            }
        )
        
        if org_memberships_response.status_code != 200:
            print(f"Error getting organization memberships: {org_memberships_response.status_code}")
            print(org_memberships_response.text)
            return False
        
        org_memberships = org_memberships_response.json()
        
        if not org_memberships["collection"]:
            print("No organization memberships found")
            return False
        
        org_uri = org_memberships["collection"][0]["organization"]
        
        # Create webhook subscription
        webhook_url = f"{public_url}/webhook"
        
        webhook_response = requests.post(
            "https://api.calendly.com/webhook_subscriptions",
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}"
            },
            json={
                "url": webhook_url,
                "events": ["invitee.created", "invitee.canceled"],
                "organization": org_uri,
                "user": user_uri,
                "scope": "user"
            }
        )
        
        if webhook_response.status_code == 201:
            print("Webhook subscription created successfully!")
            print(f"Webhook URL: {webhook_url}")
            
            # Save webhook information
            webhook_info = webhook_response.json()
            
            try:
                with open("credentials/calendly/webhook_info.json", "w") as f:
                    json.dump(webhook_info, f, indent=4)
                
                print("Webhook information saved to credentials/calendly/webhook_info.json")
            except Exception as e:
                print(f"Error saving webhook information: {e}")
            
            return True
        else:
            print(f"Error creating webhook subscription: {webhook_response.status_code}")
            print(webhook_response.text)
            return False
    
    except Exception as e:
        print(f"Error setting up Calendly webhook: {e}")
        return False

def main():
    """Set up ngrok for Calendly webhook testing."""
    parser = argparse.ArgumentParser(description="Setup ngrok for Calendly webhook testing")
    parser.add_argument("--port", type=int, default=8000, help="Port to tunnel")
    parser.add_argument("--token", type=str, help="Calendly OAuth token")
    args = parser.parse_args()
    
    # If token is not provided, try to load from credentials file
    token = args.token
    if not token:
        try:
            with open("credentials/calendly/calendly.json", "r") as f:
                credentials = json.load(f)
                token = credentials.get("api_key", "")
        except Exception as e:
            print(f"Error loading credentials: {e}")
    
    if not token:
        print("Calendly OAuth token is required")
        print("Please provide a token using the --token argument")
        print("You can find your token in your Calendly account settings")
        return
    
    # Start ngrok
    public_url = start_ngrok(args.port)
    
    if not public_url:
        return
    
    # Set up Calendly webhook
    setup_calendly_webhook(token, public_url)
    
    # Keep ngrok running
    try:
        print("Press Ctrl+C to stop")
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("Shutting down...")

if __name__ == "__main__":
    main()
