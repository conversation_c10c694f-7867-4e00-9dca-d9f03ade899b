# PowerShell script to send an email to <PERSON><PERSON> C. using direct COM automation
# This script demonstrates browser automation using PowerShell COM objects

# Email content
$recipientEmail = "<EMAIL>" # Replace with actual email
$emailSubject = "URGENT: Your Insurance Options - Coverage Available Within Your $100 Monthly Budget"
$emailBody = @"
Hi Alyssa,

I hope this message finds you well. We've been trying to reach you through multiple channels (email, phone calls, voicemails, and texts) regarding your insurance needs, and I wanted to follow up personally as this is time-sensitive.

Based on your specific situation and $100 monthly budget, we have options ready for you that provide excellent coverage:

For your IUL policy (approximately $65/month):
- Cash value growth potential tied to market performance without the downside risk
- Death benefit protection for your loved ones
- Tax-free access to your cash value for future needs
- Living benefits that allow access to your death benefit if you become critically ill

For your health/dental/vision package (approximately $35/month):
- Comprehensive health coverage with our top-tier carriers that offer exceptional benefits
- Dental coverage including preventive care, basic procedures, and major work
- Vision benefits covering exams, frames, and contacts

What makes us the best agency to handle your insurance needs:
1. Our carriers offer some of the most comprehensive health benefits in the industry, with lower deductibles and better coverage than you'll find elsewhere
2. We have flexible IUL, whole life, and term policy options that can be customized to your exact needs
3. Our mortgage protection extends for the entire life of your loan, unlike competitors who offer limited coverage periods
4. For qualified applicants like yourself, we can secure over $1 million in coverage

We need to speak with you as soon as possible to secure this coverage before rates change. I have the following time slots available tomorrow (Monday):
- 10:00 AM - 10:30 AM
- 1:00 PM - 1:30 PM
- 4:00 PM - 4:30 PM

Or Tuesday:
- 9:00 AM - 9:30 AM
- 2:00 PM - 2:30 PM

Please let me know which time works best for you, or you can schedule directly through our Calendly link:
https://calendly.com/flofaction/insurance-consultation

It's critical that we connect in the next 24-48 hours to ensure we can lock in these rates for you.

Looking forward to speaking with you soon,

Paul Edwards
Flo Faction Insurance
(772) 208-9646
"@

# Function to log messages with timestamps
function Write-Log {
    param (
        [Parameter(Mandatory=$true)]
        [string]$Message,
        
        [Parameter(Mandatory=$false)]
        [ValidateSet("INFO", "WARNING", "ERROR")]
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "$timestamp - $Level - $Message"
    
    # Output to console with color based on level
    switch ($Level) {
        "INFO" { Write-Host $logMessage -ForegroundColor Cyan }
        "WARNING" { Write-Host $logMessage -ForegroundColor Yellow }
        "ERROR" { Write-Host $logMessage -ForegroundColor Red }
    }
}

# Function to send an email using Outlook COM automation
function Send-EmailWithOutlook {
    try {
        Write-Log "Starting email automation to Alyssa C. using Outlook COM..."
        
        # Create Outlook COM object
        $outlook = New-Object -ComObject Outlook.Application
        $mail = $outlook.CreateItem(0) # 0 = olMailItem
        
        # Set email properties
        $mail.To = $recipientEmail
        $mail.Subject = $emailSubject
        $mail.Body = $emailBody
        
        # Display the email (optional - for verification)
        $mail.Display()
        
        Write-Log "Email composed successfully. Please review and click Send manually."
        Write-Host "Press Enter after sending the email..." -ForegroundColor Yellow
        Read-Host
        
        Write-Log "Email sent successfully to $recipientEmail!"
        
        # Release COM objects
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($mail) | Out-Null
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($outlook) | Out-Null
        
        return $true
    } catch {
        Write-Log "Error sending email: $_" -Level "ERROR"
        
        # Try to release COM objects if they exist
        if ($mail) {
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($mail) | Out-Null
        }
        if ($outlook) {
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($outlook) | Out-Null
        }
        
        return $false
    }
}

# Main execution
$success = Send-EmailWithOutlook

if ($success) {
    Write-Log "Email automation completed successfully!"
    exit 0
} else {
    Write-Log "Email automation failed!" -Level "ERROR"
    exit 1
}
