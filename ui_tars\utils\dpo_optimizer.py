"""
Direct Preference Optimization (DPO) for UI-TARS.

This module provides DPO capabilities for UI-TARS 1.5,
allowing it to learn from user preferences and improve over time.
"""
import os
import sys
import json
import asyncio
import logging
import numpy as np
import time
import random
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Union, Callable

from core.logger import setup_logger

# Set up logger
logger = setup_logger("ui_tars_dpo_optimizer")

class DPOOptimizer:
    """
    Direct Preference Optimization for UI-TARS 1.5.
    
    This class provides DPO capabilities for UI-TARS 1.5,
    allowing it to learn from user preferences and improve over time.
    """
    
    def __init__(self, 
                 model_name: str = "UI-TARS-1.5-7B",
                 preference_data_path: Optional[str] = None,
                 learning_rate: float = 0.0001,
                 beta: float = 0.1,
                 batch_size: int = 4,
                 max_history: int = 1000):
        """
        Initialize the DPO optimizer.
        
        Args:
            model_name (str): Name of the model to optimize
            preference_data_path (Optional[str]): Path to preference data
            learning_rate (float): Learning rate for optimization
            beta (float): Temperature parameter for DPO
            batch_size (int): Batch size for optimization
            max_history (int): Maximum number of preference pairs to store
        """
        self.model_name = model_name
        self.preference_data_path = preference_data_path
        self.learning_rate = learning_rate
        self.beta = beta
        self.batch_size = batch_size
        self.max_history = max_history
        self.preference_data = []
        self.optimization_history = []
        self.is_optimizing = False
        self.optimization_task = None
        
        # Create default preference data path if not provided
        if not self.preference_data_path:
            self.preference_data_path = os.path.join(
                os.path.expanduser("~"),
                ".ui_tars",
                "dpo",
                f"{self.model_name.replace('/', '_')}_preferences.json"
            )
            
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(self.preference_data_path), exist_ok=True)
            
        # Load preference data if it exists
        self._load_preference_data()
        
    def _load_preference_data(self) -> None:
        """Load preference data from file."""
        if os.path.exists(self.preference_data_path):
            try:
                with open(self.preference_data_path, "r") as f:
                    data = json.load(f)
                    
                    if isinstance(data, list):
                        self.preference_data = data
                        logger.info(f"Loaded {len(self.preference_data)} preference pairs from {self.preference_data_path}")
                    else:
                        logger.warning(f"Invalid preference data format in {self.preference_data_path}")
                        
            except Exception as e:
                logger.error(f"Error loading preference data: {e}")
        else:
            logger.info(f"No preference data found at {self.preference_data_path}")
            
    def _save_preference_data(self) -> None:
        """Save preference data to file."""
        try:
            with open(self.preference_data_path, "w") as f:
                json.dump(self.preference_data, f, indent=2)
                
            logger.info(f"Saved {len(self.preference_data)} preference pairs to {self.preference_data_path}")
            
        except Exception as e:
            logger.error(f"Error saving preference data: {e}")
            
    def add_preference_pair(self, 
                           prompt: str,
                           chosen_response: str,
                           rejected_response: str,
                           metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        Add a preference pair to the dataset.
        
        Args:
            prompt (str): The input prompt
            chosen_response (str): The preferred response
            rejected_response (str): The non-preferred response
            metadata (Optional[Dict[str, Any]]): Additional metadata
        """
        # Create preference pair
        preference_pair = {
            "prompt": prompt,
            "chosen": chosen_response,
            "rejected": rejected_response,
            "timestamp": time.time(),
            "metadata": metadata or {}
        }
        
        # Add to preference data
        self.preference_data.append(preference_pair)
        
        # Trim if exceeding max history
        if len(self.preference_data) > self.max_history:
            self.preference_data = self.preference_data[-self.max_history:]
            
        # Save to file
        self._save_preference_data()
        
        logger.info(f"Added preference pair (total: {len(self.preference_data)})")
        
    def get_preference_data(self) -> List[Dict[str, Any]]:
        """Get the current preference dataset."""
        return self.preference_data
        
    def clear_preference_data(self) -> None:
        """Clear all preference data."""
        self.preference_data = []
        self._save_preference_data()
        logger.info("Cleared all preference data")
        
    async def optimize(self, 
                      optimization_function: Callable[[List[Dict[str, Any]], float, float, int], Dict[str, Any]],
                      num_steps: int = 10,
                      callback: Optional[Callable[[Dict[str, Any]], None]] = None) -> Dict[str, Any]:
        """
        Optimize the model using DPO.
        
        Args:
            optimization_function (Callable): Function to perform the optimization
            num_steps (int): Number of optimization steps
            callback (Optional[Callable]): Callback function for progress updates
            
        Returns:
            Dict[str, Any]: Optimization results
        """
        if self.is_optimizing:
            logger.warning("Optimization is already running")
            return {"success": False, "error": "Optimization is already running"}
            
        if not self.preference_data:
            logger.warning("No preference data available for optimization")
            return {"success": False, "error": "No preference data available"}
            
        logger.info(f"Starting DPO optimization with {len(self.preference_data)} preference pairs")
        
        self.is_optimizing = True
        self.optimization_history = []
        
        try:
            # Run optimization
            for step in range(num_steps):
                logger.info(f"Optimization step {step+1}/{num_steps}")
                
                # Perform optimization step
                result = optimization_function(
                    self.preference_data,
                    self.learning_rate,
                    self.beta,
                    self.batch_size
                )
                
                # Record history
                self.optimization_history.append({
                    "step": step + 1,
                    "loss": result.get("loss"),
                    "accuracy": result.get("accuracy"),
                    "timestamp": time.time()
                })
                
                # Call callback if provided
                if callback:
                    callback({
                        "step": step + 1,
                        "total_steps": num_steps,
                        "loss": result.get("loss"),
                        "accuracy": result.get("accuracy")
                    })
                    
                # Simulate optimization step time
                await asyncio.sleep(0.1)
                
            logger.info("DPO optimization completed successfully")
            
            # Return results
            return {
                "success": True,
                "model_name": self.model_name,
                "num_steps": num_steps,
                "num_preference_pairs": len(self.preference_data),
                "final_loss": self.optimization_history[-1].get("loss") if self.optimization_history else None,
                "final_accuracy": self.optimization_history[-1].get("accuracy") if self.optimization_history else None,
                "history": self.optimization_history
            }
            
        except Exception as e:
            logger.exception(f"Error during DPO optimization: {e}")
            return {"success": False, "error": str(e)}
            
        finally:
            self.is_optimizing = False
            
    def get_optimization_history(self) -> List[Dict[str, Any]]:
        """Get the optimization history."""
        return self.optimization_history
        
    def get_optimization_status(self) -> Dict[str, Any]:
        """Get the current optimization status."""
        return {
            "is_optimizing": self.is_optimizing,
            "model_name": self.model_name,
            "num_preference_pairs": len(self.preference_data),
            "num_optimization_steps": len(self.optimization_history),
            "last_step": self.optimization_history[-1] if self.optimization_history else None
        }
        
    async def start_background_optimization(self, 
                                          optimization_function: Callable[[List[Dict[str, Any]], float, float, int], Dict[str, Any]],
                                          num_steps: int = 10,
                                          callback: Optional[Callable[[Dict[str, Any]], None]] = None) -> bool:
        """
        Start optimization in the background.
        
        Args:
            optimization_function (Callable): Function to perform the optimization
            num_steps (int): Number of optimization steps
            callback (Optional[Callable]): Callback function for progress updates
            
        Returns:
            bool: True if optimization started, False otherwise
        """
        if self.is_optimizing:
            logger.warning("Optimization is already running")
            return False
            
        if not self.preference_data:
            logger.warning("No preference data available for optimization")
            return False
            
        # Start optimization task
        self.optimization_task = asyncio.create_task(
            self.optimize(optimization_function, num_steps, callback)
        )
        
        logger.info("Started background optimization")
        return True
        
    async def stop_background_optimization(self) -> bool:
        """
        Stop background optimization.
        
        Returns:
            bool: True if optimization was stopped, False otherwise
        """
        if not self.is_optimizing or not self.optimization_task:
            logger.warning("No optimization is running")
            return False
            
        # Cancel the task
        self.optimization_task.cancel()
        
        try:
            await self.optimization_task
        except asyncio.CancelledError:
            pass
            
        self.is_optimizing = False
        self.optimization_task = None
        
        logger.info("Stopped background optimization")
        return True
        
    def simulate_optimization_function(self, 
                                     preference_data: List[Dict[str, Any]],
                                     learning_rate: float,
                                     beta: float,
                                     batch_size: int) -> Dict[str, Any]:
        """
        Simulate an optimization step (for testing).
        
        Args:
            preference_data (List[Dict[str, Any]]): Preference data
            learning_rate (float): Learning rate
            beta (float): Temperature parameter
            batch_size (int): Batch size
            
        Returns:
            Dict[str, Any]: Simulated optimization results
        """
        # Simulate optimization
        time.sleep(0.5)  # Simulate computation time
        
        # Generate random loss that decreases over time
        loss = max(0.1, 1.0 - len(self.optimization_history) * 0.05 + random.uniform(-0.02, 0.02))
        
        # Generate random accuracy that increases over time
        accuracy = min(0.95, 0.5 + len(self.optimization_history) * 0.03 + random.uniform(-0.01, 0.01))
        
        return {
            "loss": loss,
            "accuracy": accuracy,
            "learning_rate": learning_rate,
            "beta": beta,
            "batch_size": batch_size,
            "num_samples": len(preference_data)
        }
