"""
Gmail Service Account Test
This script tests the service account for Gmail integration.
"""
import os
import sys
import base64
import subprocess
from email.mime.text import MIMEText
from pathlib import Path

def clear_screen():
    """Clear the terminal screen."""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_header():
    """Print the script header."""
    clear_screen()
    print("=" * 80)
    print("                     GMAIL SERVICE ACCOUNT TEST")
    print("=" * 80)
    print("\nThis script will test the service account for Gmail integration.")
    print("It will send a test email using the service account.")
    print("\n")

def test_service_account():
    """Test the service account for Gmail integration."""
    print_header()
    
    # Check if service account key file exists
    service_account_path = 'credentials/gmail_service_account.json'
    if not os.path.exists(service_account_path):
        print(f"\nError: Service account key file not found at {service_account_path}")
        print("Please run gmail_service_account_setup.py to set up the service account.")
        return
    
    # Install required packages if not already installed
    try:
        from google.oauth2 import service_account
        from googleapiclient.discovery import build
        from googleapiclient.errors import HttpError
    except ImportError:
        print("\nInstalling required packages...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", 
                              "google-auth", "google-auth-oauthlib", 
                              "google-auth-httplib2", "google-api-python-client"])
        
        from google.oauth2 import service_account
        from googleapiclient.discovery import build
        from googleapiclient.errors import HttpError
    
    # Gmail API scopes
    SCOPES = [
        'https://www.googleapis.com/auth/gmail.readonly',
        'https://www.googleapis.com/auth/gmail.send',
        'https://www.googleapis.com/auth/gmail.compose',
        'https://www.googleapis.com/auth/gmail.modify'
    ]
    
    # Ask for the email address to impersonate
    print("\nYou need to specify which email address the service account should impersonate.")
    print("This should be one of your Gmail accounts that you want to send emails from.")
    
    email = input("\nEnter the email address to impersonate: ")
    
    try:
        print("\nLoading service account credentials...")
        
        # Load service account credentials
        credentials = service_account.Credentials.from_service_account_file(
            service_account_path,
            scopes=SCOPES
        )
        
        # Create delegated credentials
        delegated_credentials = credentials.with_subject(email)
        
        # Build the Gmail service
        print("Building Gmail service...")
        service = build('gmail', 'v1', credentials=delegated_credentials)
        
        # Get user profile to confirm authentication
        profile = service.users().getProfile(userId='me').execute()
        authenticated_email = profile.get('emailAddress')
        
        print(f"✓ Successfully authenticated as {authenticated_email}")
        
        # Get email details from user
        print("\nEnter the details for your test email:")
        to_email = input("To: ")
        subject = input("Subject: ")
        body = input("Body: ")
        
        # Create the email message
        print("\nCreating email message...")
        message = MIMEText(body)
        message['to'] = to_email
        message['from'] = authenticated_email
        message['subject'] = subject
        
        # Encode the message
        raw_message = base64.urlsafe_b64encode(message.as_bytes()).decode()
        
        # Send the email
        print("\nSending email...")
        send_message = service.users().messages().send(
            userId='me',
            body={'raw': raw_message}
        ).execute()
        
        print(f"\n✓ Email sent successfully! Message ID: {send_message['id']}")
        return True
    
    except Exception as e:
        print(f"\nError testing service account: {e}")
        
        print("\nTroubleshooting steps:")
        print("1. Make sure you've enabled domain-wide delegation for the service account")
        print("2. Make sure you've enabled the Gmail API for your project")
        print("3. Make sure you've entered the correct email address to impersonate")
        
        retry = input("\nDo you want to try again? (y/n): ").lower()
        if retry == 'y':
            return test_service_account()
        
        return False

if __name__ == "__main__":
    try:
        test_service_account()
    except KeyboardInterrupt:
        print("\n\nExiting...")
        sys.exit(0)
