# UI-TARS Local Setup Guide

This guide will help you set up UI-TARS 1.5 to work with your local LLMs without requiring a paid Hugging Face subscription.

## Overview

Since UI-TARS 1.5 only supports Hugging Face and VoiceArk as VLM providers, we've created a local API server that mimics the Hugging Face API but uses your local models. This allows UI-TARS to work with your local models without requiring a paid Hugging Face subscription.

## Setup Instructions

### Step 1: Start the Local API Server

1. Open a command prompt
2. Navigate to the directory containing the server scripts
3. Run one of the following commands:

   - For a simple server with dummy responses:
     ```
     start_local_hf_server.bat
     ```

   - For an advanced server that actually uses your local models:
     ```
     start_advanced_local_hf_server.bat
     ```

4. Keep the server running in the background

### Step 2: Configure UI-TARS

1. Open UI-TARS 1.5
2. Go to Settings
3. Configure the VLM settings:
   - **VLM Provider**: Select "Hugging Face"
   - **VLM Base URL**: Enter `http://127.0.0.1:8000`
   - **VLM API Key**: Enter `dummy_key`
   - **VLM Model Name**: Enter `UI-TARS-1.5-7B`

4. Configure the Chat settings:
   - **Max Tokens**: Enter `2048`
   - **Context Window**: Enter `4096`

5. Leave the Report settings blank

6. Click "Save" or "Apply" to apply the configuration

### Step 3: Test the Configuration

1. Try sending a message to UI-TARS
2. If you're using the simple server, you'll get a dummy response
3. If you're using the advanced server and have configured it to use your local models, you'll get a response generated by your local model

## Troubleshooting

### Server Won't Start

- Make sure you have installed all the required packages:
  ```
  pip install fastapi uvicorn transformers torch
  ```

- Check if port 8000 is already in use. If it is, you can change the port in the server script.

### UI-TARS Can't Connect to the Server

- Make sure the server is running
- Check if the Base URL is correct: `http://127.0.0.1:8000`
- Try restarting UI-TARS

### Model Not Found

- Make sure the model path is correct
- Check if the model directory contains the necessary files
- Try using the dummy responses option to test the connection

## Advanced Configuration

### Using a Different Model

If you want to use a different local model:

1. Update the model path in the server script or provide it as a command-line argument:
   ```
   python advanced_local_hf_server.py --model-path "C:/path/to/your/model" --model-name "Your-Model-Name"
   ```

2. Update the model name in UI-TARS to match the name you provided to the server

### Running the Server on a Different Port

If port 8000 is already in use, you can run the server on a different port:

```
python advanced_local_hf_server.py --port 8080
```

Then update the Base URL in UI-TARS to use the new port: `http://127.0.0.1:8080`

## Conclusion

You've now set up UI-TARS 1.5 to work with your local LLMs without requiring a paid Hugging Face subscription. The local API server will handle all requests from UI-TARS and either provide dummy responses or use your local models to generate responses.

Remember to keep the server running whenever you're using UI-TARS. If you close the server, UI-TARS will no longer be able to generate responses.
