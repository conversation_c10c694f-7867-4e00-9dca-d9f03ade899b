"""
Check UI-TARS Installation

This script checks if UI-TARS is installed and if Chrome is available.
"""
import os
import sys
import platform

def main():
    """Main function."""
    print("Checking for UI-TARS installation and Chrome browser...")
    
    # Check OS
    os_type = platform.system()
    print(f"Operating System: {os_type}")
    
    # Check for Chrome
    chrome_paths = []
    if os_type == "Windows":
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
        ]
    
    chrome_found = False
    for path in chrome_paths:
        if os.path.exists(path):
            print(f"Chrome found at: {path}")
            chrome_found = True
            break
    
    if not chrome_found:
        print("Chrome not found")
    
    # Check for UI-TARS
    ui_tars_paths = []
    if os_type == "Windows":
        ui_tars_paths = [
            os.path.join(os.environ.get("PROGRAMFILES", "C:\\Program Files"), "UI-TARS", "UI-TARS.exe"),
            os.path.join(os.environ.get("PROGRAMFILES(X86)", "C:\\Program Files (x86)"), "UI-TARS", "UI-TARS.exe"),
            os.path.join(os.environ.get("LOCALAPPDATA", "C:\\Users\\<USER>\\AppData\\Local".format(os.getlogin())), "UI-TARS", "UI-TARS.exe")
        ]
    
    ui_tars_found = False
    for path in ui_tars_paths:
        if os.path.exists(path):
            print(f"UI-TARS found at: {path}")
            ui_tars_found = True
            break
    
    if not ui_tars_found:
        print("UI-TARS not found")
    
    print("Check completed")

if __name__ == "__main__":
    main()
