# Midscene Email to Alyssa

This package provides a script to send an email to <PERSON><PERSON> with information about insurance options using Midscene browser automation.

## Prerequisites

Before using this package, make sure you have:

1. Python 3.8 or higher installed
2. Midscene installed (`npm install -g @midscene/cli @midscene/web @midscene/core`)
3. Google Chrome installed
4. Gmail account credentials (email and password)

## Quick Start

The easiest way to send an email to Alyssa using Midscene is to run the batch file:

```
run_midscene_email_to_alyssa.bat
```

This will:
1. Start Midscene
2. Open Chrome
3. Navigate to Gmail
4. Log <NAME_EMAIL>
5. Compose and send an email to <PERSON><PERSON>

## Configuration

You can modify the email content by editing the `midscene_email_to_alyssa.py` file:

- `EMAIL_ACCOUNT`: The Gmail account to send from (default: <EMAIL>)
- `EMAIL_PASSWORD`: The password for the Gmail account (default: GodisSoGood!777)
- `RECIPIENT_EMAIL`: The recipient's email address (default: <EMAIL> for testing)
- `EMAIL_SUBJECT`: The subject of the email
- `EMAIL_BODY`: The body of the email

## Troubleshooting

### Midscene Not Starting

If Midscene fails to start:

1. Make sure Midscene is installed: `npm list -g @midscene/cli`
2. Try reinstalling Midscene: `npm install -g @midscene/cli @midscene/web @midscene/core`

### Browser Not Opening

If the browser doesn't open:

1. Make sure Chrome is installed
2. Check if Chrome is in one of the standard installation locations
3. Try running Midscene manually: `npx @midscene/cli start --browser chrome`

### Gmail Login Issues

If there are issues logging into Gmail:

1. Make sure the Gmail credentials are correct
2. Check if 2-factor authentication is enabled (you may need to use an app password)
3. Try logging in manually first, then run the script

## Advanced Usage

### Using a Different Browser

To use a different browser, modify the `browser_type` parameter in the `MidsceneConnector` initialization:

```python
midscene = MidsceneConnector(
    api_url="http://localhost:8081",
    browser_type="edge"  # Change to "edge", "firefox", etc.
)
```

### Customizing the Email Content

To customize the email content, modify the `EMAIL_SUBJECT` and `EMAIL_BODY` variables in the script.

### Sending to Multiple Recipients

To send to multiple recipients, modify the script to loop through a list of recipients and call the `send_email_with_midscene` function for each one.

## Next Steps

After sending the email to Alyssa, you can:

1. Monitor for replies
2. Schedule follow-up communications
3. Add more clients to the campaign

## License

This package is licensed under the Apache 2.0 License.
