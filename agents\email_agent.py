"""
Email Agent for handling email communications with reasoning capabilities.
This agent can read, analyze, and respond to emails with context-aware reasoning.
"""
import os
import json
import asyncio
import re
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import logging
import uuid

from core.logger import setup_logger
from core.state_manager import StateManager
from services.gmail_service import GmailServiceFactory
from llm.llm_router import LL<PERSON>outer
from agents.base_agent import BaseAgent, MessagePriority

# Set up logger
logger = setup_logger("email_agent")

class EmailAgent(BaseAgent):
    """
    Agent for handling email communications with reasoning capabilities.
    """

    def __init__(self,
                 agent_id: str,
                 name: str = "Email Agent",
                 description: str = "Handles email communications with reasoning capabilities",
                 config: Optional[Dict] = None,
                 message_queue: Optional[asyncio.Queue] = None,
                 state_manager: Optional[StateManager] = None):
        """
        Initialize the Email Agent.

        Args:
            agent_id (str): Unique identifier for the agent
            name (str): Name of the agent
            description (str): Description of the agent
            config (Optional[Dict]): Agent configuration
            message_queue (Optional[asyncio.Queue]): Message queue for communication
            state_manager (Optional[StateManager]): State manager for persistence
        """
        super().__init__(agent_id, name, description, config, message_queue, state_manager)
        self.gmail_service = None

        # Capabilities
        self.capabilities = [
            "read_emails",
            "analyze_email",
            "draft_response",
            "send_email"
        ]

    async def initialize(self):
        """Initialize the agent and load its state."""
        await super().initialize()

        # Initialize Gmail service
        self.gmail_service = GmailServiceFactory.create_service()

        # Check if Gmail service is enabled
        if not self.gmail_service.is_enabled():
            self.logger.warning("Gmail service is not enabled. Email Agent functionality will be limited.")
        else:
            self.logger.info("Gmail service initialized successfully")

        # Update agent state with capabilities
        self.state["capabilities"] = self.capabilities
        await self._update_state()

    async def handle_command(self, message: Dict):
        """
        Handle a command message.

        Args:
            message (Dict): Command message
        """
        command = message.get("content", {}).get("command")
        data = message.get("content", {}).get("data", {})

        if command == "read_emails":
            result = await self.read_emails(
                query=data.get("query"),
                max_results=data.get("max_results", 10),
                label_ids=data.get("label_ids")
            )
            await self.send_message(
                message.get("sender_id"),
                "response",
                {
                    "command": command,
                    "result": result
                },
                in_response_to=message.get("id")
            )

        elif command == "analyze_email":
            result = await self.analyze_email(data.get("email_id"))
            await self.send_message(
                message.get("sender_id"),
                "response",
                {
                    "command": command,
                    "result": result
                },
                in_response_to=message.get("id")
            )

        elif command == "draft_response":
            result = await self.draft_response(
                email_id=data.get("email_id"),
                response_type=data.get("response_type", "professional"),
                include_reasoning=data.get("include_reasoning", False)
            )
            await self.send_message(
                message.get("sender_id"),
                "response",
                {
                    "command": command,
                    "result": result
                },
                in_response_to=message.get("id")
            )

        elif command == "send_email":
            result = await self.send_email(
                to=data.get("to"),
                subject=data.get("subject"),
                body=data.get("body"),
                cc=data.get("cc"),
                bcc=data.get("bcc")
            )
            await self.send_message(
                message.get("sender_id"),
                "response",
                {
                    "command": command,
                    "result": result
                },
                in_response_to=message.get("id")
            )

        else:
            await self.send_message(
                message.get("sender_id"),
                "error",
                {
                    "error": f"Unknown command: {command}",
                    "original_message": message.get("id")
                },
                in_response_to=message.get("id")
            )

    async def read_emails(self, query: Optional[str] = None,
                         max_results: int = 10,
                         label_ids: Optional[List[str]] = None) -> Dict:
        """
        Read and analyze emails.

        Args:
            query (Optional[str]): Search query to filter emails
            max_results (int): Maximum number of emails to retrieve
            label_ids (Optional[List[str]]): List of label IDs to filter by

        Returns:
            Dict: List of emails with analysis
        """
        if not self.gmail_service or not self.gmail_service.is_enabled():
            return {"error": "Gmail service is not enabled"}

        # Get emails
        emails_result = await self.gmail_service.list_messages(query, max_results, label_ids)

        if "error" in emails_result:
            return emails_result

        # Add a summary of the emails
        if emails_result.get("messages"):
            # Get LLM router from services
            llm_router = self.get_service("llm_router")
            if not llm_router:
                self.logger.warning("LLM router not available. Cannot generate email summary.")
                emails_result["summary"] = "Summary not available (LLM router not found)"
                return emails_result

            summary_prompt = f"""
            You are an AI assistant analyzing emails. Summarize the following {len(emails_result['messages'])} emails:

            {json.dumps(emails_result['messages'], indent=2)}

            Provide a brief summary of the emails, including:
            1. How many are unread
            2. Key senders and topics
            3. Any emails that might require urgent attention
            4. Any patterns or trends in the emails
            """

            summary_response = await llm_router.generate_text(summary_prompt)
            emails_result["summary"] = summary_response.get("text", "Summary generation failed")

        return emails_result

    async def analyze_email(self, email_id: str) -> Dict:
        """
        Analyze a specific email with reasoning.

        Args:
            email_id (str): ID of the email to analyze

        Returns:
            Dict: Email analysis with reasoning
        """
        if not self.gmail_service or not self.gmail_service.is_enabled():
            return {"error": "Gmail service is not enabled"}

        # Get email
        email_result = await self.gmail_service.get_message(email_id)

        if "error" in email_result:
            return email_result

        # Get LLM router from services
        llm_router = self.get_service("llm_router")
        if not llm_router:
            self.logger.warning("LLM router not available. Cannot analyze email.")
            email_result["analysis"] = "Analysis not available (LLM router not found)"
            return email_result

        # Analyze the email
        analysis_prompt = f"""
        You are an AI assistant analyzing an email. Analyze the following email:

        From: {email_result.get('from', 'Unknown')}
        To: {email_result.get('to', 'Unknown')}
        Subject: {email_result.get('subject', 'No subject')}
        Date: {email_result.get('date', 'Unknown')}

        Body:
        {email_result.get('body', 'No body')}

        Provide a detailed analysis of this email, including:
        1. The main purpose or intent of the email
        2. Key points or requests made in the email
        3. The tone and sentiment of the email
        4. Any action items or follow-ups required
        5. Any potential concerns or issues to be aware of
        6. Recommended next steps

        Structure your analysis with clear headings and bullet points where appropriate.
        """

        analysis_response = await llm_router.generate_text(analysis_prompt)

        # Add the analysis to the email result
        email_result["analysis"] = analysis_response.get("text", "Analysis generation failed")

        return email_result

    async def draft_response(self, email_id: str,
                            response_type: str = "professional",
                            include_reasoning: bool = False) -> Dict:
        """
        Draft a response to an email with reasoning.

        Args:
            email_id (str): ID of the email to respond to
            response_type (str): Type of response
            include_reasoning (bool): Whether to include reasoning

        Returns:
            Dict: Draft response with reasoning
        """
        if not self.gmail_service or not self.gmail_service.is_enabled():
            return {"error": "Gmail service is not enabled"}

        # Get email
        email_result = await self.gmail_service.get_message(email_id)

        if "error" in email_result:
            return email_result

        # Get LLM router from services
        llm_router = self.get_service("llm_router")
        if not llm_router:
            self.logger.warning("LLM router not available. Cannot draft email response.")
            return {
                "error": "LLM router not available",
                "original_email": {
                    "id": email_result.get('id', ''),
                    "from": email_result.get('from', 'Unknown'),
                    "subject": email_result.get('subject', 'No subject')
                }
            }

        # Draft a response
        response_prompt = f"""
        You are an AI assistant drafting an email response. Respond to the following email:

        From: {email_result.get('from', 'Unknown')}
        To: {email_result.get('to', 'Unknown')}
        Subject: {email_result.get('subject', 'No subject')}
        Date: {email_result.get('date', 'Unknown')}

        Body:
        {email_result.get('body', 'No body')}

        Draft a {response_type} response to this email. The response should be:
        - Clear and concise
        - Address all points or questions raised in the original email
        - Maintain a {response_type} tone
        - Include appropriate greeting and sign-off

        {"Also include your reasoning for how you crafted this response, explaining your thought process." if include_reasoning else ""}
        """

        response_result = await llm_router.generate_text(response_prompt)
        response_text = response_result.get("text", "Failed to generate response")

        # Extract the response and reasoning if included
        response = response_text
        reasoning = None

        if include_reasoning:
            # Try to separate the response from the reasoning
            # This is a simple approach and might need refinement
            response_pattern = r"(.*?)(?:Reasoning:|Thought process:|My reasoning:|Here's my reasoning:)(.*)"
            match = re.search(response_pattern, response_text, re.DOTALL | re.IGNORECASE)

            if match:
                response = match.group(1).strip()
                reasoning = match.group(2).strip()

        return {
            "original_email": {
                "id": email_result.get('id', ''),
                "from": email_result.get('from', 'Unknown'),
                "subject": email_result.get('subject', 'No subject')
            },
            "draft_response": response,
            "reasoning": reasoning if include_reasoning else None,
            "response_type": response_type
        }

    async def send_email(self, to: str, subject: str, body: str,
                        cc: Optional[str] = None,
                        bcc: Optional[str] = None) -> Dict:
        """
        Send an email.

        Args:
            to (str): Recipient email address
            subject (str): Email subject
            body (str): Email body
            cc (Optional[str]): CC recipients
            bcc (Optional[str]): BCC recipients

        Returns:
            Dict: Send status
        """
        if not self.gmail_service or not self.gmail_service.is_enabled():
            return {"error": "Gmail service is not enabled"}

        # Validate inputs
        if not to:
            return {"error": "Recipient email address is required"}

        if not subject:
            return {"error": "Email subject is required"}

        if not body:
            return {"error": "Email body is required"}

        # Log the email being sent
        self.logger.info(f"Sending email to {to} with subject: {subject}")

        try:
            # Send the email
            result = await self.gmail_service.send_message(to, subject, body, cc, bcc)

            # Store in memory
            await self.memory.add_memory(
                content={
                    "action": "send_email",
                    "to": to,
                    "subject": subject,
                    "body_preview": body[:100] + "..." if len(body) > 100 else body,
                    "result": result
                },
                memory_type="episodic",
                source="email_agent",
                importance=0.7
            )

            return result
        except Exception as e:
            error_msg = f"Error sending email: {str(e)}"
            self.logger.error(error_msg)
            return {"error": error_msg}

    async def execute_cycle(self):
        """
        Execute a single agent cycle.

        This method is called periodically by the agent manager to allow the agent
        to perform background tasks or periodic checks.
        """
        # Check for new emails if Gmail service is enabled
        if self.gmail_service and self.gmail_service.is_enabled():
            try:
                # Check for unread emails
                unread_emails = await self.read_emails(query="is:unread", max_results=5)

                if unread_emails and "messages" in unread_emails and unread_emails["messages"]:
                    self.logger.info(f"Found {len(unread_emails['messages'])} unread emails")

                    # Process the first unread email
                    first_email = unread_emails["messages"][0]
                    self.logger.info(f"Processing email from {first_email.get('from', 'Unknown')}: {first_email.get('subject', 'No subject')}")

                    # Analyze the email
                    analysis = await self.analyze_email(first_email["id"])

                    # Store the analysis in the agent's state
                    self.state["last_analyzed_email"] = {
                        "id": first_email["id"],
                        "from": first_email.get("from", "Unknown"),
                        "subject": first_email.get("subject", "No subject"),
                        "analysis": analysis.get("analysis", "No analysis available")
                    }

                    # Update agent state
                    await self._update_state()

                    # Emit an event that can be picked up by workflows
                    await self.emit_event("new_email_analyzed", {
                        "email_id": first_email["id"],
                        "from": first_email.get("from", "Unknown"),
                        "subject": first_email.get("subject", "No subject"),
                        "timestamp": datetime.now().isoformat()
                    })
            except Exception as e:
                self.logger.error(f"Error in execute_cycle: {str(e)}")

        return {"status": "completed"}
