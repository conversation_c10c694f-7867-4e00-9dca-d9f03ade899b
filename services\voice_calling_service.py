"""
Voice Calling Service for the Multi-Agent AI System.

This module provides a comprehensive voice calling service that integrates
with Bland AI, Air AI, ElevenLabs, and Twilio to enable agents to make calls,
send texts, leave voicemails, and set appointments.
"""
import asyncio
import json
import logging
import os
import sys
import time
import uuid
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
import aiohttp
import base64

# Add parent directory to path to import from core
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.logger import setup_logger

# Set up logger
logger = setup_logger("voice_calling_service")

class VoiceCallingService:
    """
    Voice Calling Service for the Multi-Agent AI System.

    This class provides a comprehensive voice calling service that integrates
    with Bland AI, Air AI, ElevenLabs, and Twilio to enable agents to make calls,
    send texts, leave voicemails, and set appointments.
    """

    def __init__(self, config: Dict):
        """
        Initialize the voice calling service.

        Args:
            config (Dict): Service configuration
        """
        self.config = config
        self.enabled = config.get("enabled", True)

        # API keys
        self.bland_api_key = config.get("bland_api_key", "")
        self.air_api_key = config.get("air_api_key", "")
        self.elevenlabs_api_key = config.get("elevenlabs_api_key", "")
        self.twilio_account_sid = config.get("twilio_account_sid", "")
        self.twilio_auth_token = config.get("twilio_auth_token", "")

        # Default phone numbers
        self.default_from_number = config.get("default_from_number", "")

        # Default voice settings
        self.default_voice_provider = config.get("default_voice_provider", "elevenlabs")
        self.default_voice_id = config.get("default_voice_id", "")

        # Call history
        self.call_history = []

        # Active calls
        self.active_calls = {}

        # Service status
        self.service_status = {
            "bland_ai": {"status": "unknown", "last_checked": None},
            "air_ai": {"status": "unknown", "last_checked": None},
            "elevenlabs": {"status": "unknown", "last_checked": None},
            "twilio": {"status": "unknown", "last_checked": None}
        }

        logger.info("Voice calling service initialized")

    async def initialize(self):
        """Initialize the voice calling service."""
        if not self.enabled:
            logger.warning("Voice calling service is disabled")
            return

        try:
            # Check service status
            await self._check_service_status()

            logger.info("Voice calling service initialized")

        except Exception as e:
            logger.exception(f"Error initializing voice calling service: {e}")
            self.enabled = False

    async def _check_service_status(self):
        """Check the status of all voice services."""
        # Check Bland AI
        if self.bland_api_key:
            bland_status = await self._check_bland_ai_status()
            self.service_status["bland_ai"] = bland_status

        # Check Air AI
        if self.air_api_key:
            air_status = await self._check_air_ai_status()
            self.service_status["air_ai"] = air_status

        # Check ElevenLabs
        if self.elevenlabs_api_key:
            elevenlabs_status = await self._check_elevenlabs_status()
            self.service_status["elevenlabs"] = elevenlabs_status

        # Check Twilio
        if self.twilio_account_sid and self.twilio_auth_token:
            twilio_status = await self._check_twilio_status()
            self.service_status["twilio"] = twilio_status

    async def _check_bland_ai_status(self) -> Dict:
        """
        Check the status of Bland AI service.

        Returns:
            Dict: Service status
        """
        try:
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Bearer {self.bland_api_key}",
                    "Content-Type": "application/json"
                }

                async with session.get("https://api.bland.ai/v1/account", headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            "status": "available",
                            "last_checked": datetime.now().isoformat(),
                            "details": data
                        }
                    else:
                        return {
                            "status": "unavailable",
                            "last_checked": datetime.now().isoformat(),
                            "error": f"Status code: {response.status}"
                        }

        except Exception as e:
            logger.exception(f"Error checking Bland AI status: {e}")
            return {
                "status": "error",
                "last_checked": datetime.now().isoformat(),
                "error": str(e)
            }

    async def _check_air_ai_status(self) -> Dict:
        """
        Check the status of Air AI service.

        Returns:
            Dict: Service status
        """
        try:
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Bearer {self.air_api_key}",
                    "Content-Type": "application/json"
                }

                # Air AI doesn't have a specific status endpoint, so we'll check the API key validity
                async with session.get("https://api.air.ai/v1/account", headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            "status": "available",
                            "last_checked": datetime.now().isoformat(),
                            "details": data
                        }
                    else:
                        return {
                            "status": "unavailable",
                            "last_checked": datetime.now().isoformat(),
                            "error": f"Status code: {response.status}"
                        }

        except Exception as e:
            logger.exception(f"Error checking Air AI status: {e}")
            return {
                "status": "error",
                "last_checked": datetime.now().isoformat(),
                "error": str(e)
            }

    async def _check_elevenlabs_status(self) -> Dict:
        """
        Check the status of ElevenLabs service.

        Returns:
            Dict: Service status
        """
        try:
            async with aiohttp.ClientSession() as session:
                headers = {
                    "xi-api-key": self.elevenlabs_api_key,
                    "Content-Type": "application/json"
                }

                async with session.get("https://api.elevenlabs.io/v1/user", headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            "status": "available",
                            "last_checked": datetime.now().isoformat(),
                            "details": data
                        }
                    else:
                        return {
                            "status": "unavailable",
                            "last_checked": datetime.now().isoformat(),
                            "error": f"Status code: {response.status}"
                        }

        except Exception as e:
            logger.exception(f"Error checking ElevenLabs status: {e}")
            return {
                "status": "error",
                "last_checked": datetime.now().isoformat(),
                "error": str(e)
            }

    async def _check_twilio_status(self) -> Dict:
        """
        Check the status of Twilio service.

        Returns:
            Dict: Service status
        """
        try:
            async with aiohttp.ClientSession() as session:
                auth = aiohttp.BasicAuth(self.twilio_account_sid, self.twilio_auth_token)

                async with session.get(f"https://api.twilio.com/2010-04-01/Accounts/{self.twilio_account_sid}.json", auth=auth) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            "status": "available",
                            "last_checked": datetime.now().isoformat(),
                            "details": data
                        }
                    else:
                        return {
                            "status": "unavailable",
                            "last_checked": datetime.now().isoformat(),
                            "error": f"Status code: {response.status}"
                        }

        except Exception as e:
            logger.exception(f"Error checking Twilio status: {e}")
            return {
                "status": "error",
                "last_checked": datetime.now().isoformat(),
                "error": str(e)
            }

    async def make_call(self, phone_number: str, script: str, options: Dict = None) -> Dict:
        """
        Make a phone call.

        Args:
            phone_number (str): Phone number to call
            script (str): Call script
            options (Dict, optional): Call options
                - provider (str): Voice provider (bland_ai, air_ai, twilio)
                - from_number (str): From phone number
                - voice_id (str): Voice ID
                - callback_url (str): Callback URL
                - max_duration (int): Maximum call duration in seconds
                - record (bool): Whether to record the call

        Returns:
            Dict: Call result
        """
        if not self.enabled:
            return {"error": "Voice calling service is disabled"}

        # Get options
        options = options or {}
        provider = options.get("provider", self.default_voice_provider)
        from_number = options.get("from_number", self.default_from_number)
        voice_id = options.get("voice_id", self.default_voice_id)
        callback_url = options.get("callback_url", "")
        max_duration = options.get("max_duration", 300)
        record = options.get("record", True)

        # Validate phone number
        if not self._validate_phone_number(phone_number):
            return {"error": "Invalid phone number"}

        # Make call based on provider
        if provider == "bland_ai":
            return await self._make_bland_ai_call(phone_number, script, from_number, voice_id, callback_url, max_duration, record)
        elif provider == "air_ai":
            return await self._make_air_ai_call(phone_number, script, from_number, voice_id, callback_url, max_duration, record)
        elif provider == "twilio":
            return await self._make_twilio_call(phone_number, script, from_number, voice_id, callback_url, max_duration, record)
        else:
            return {"error": f"Unknown provider: {provider}"}

    async def _make_bland_ai_call(self, phone_number: str, script: str, from_number: str, voice_id: str, callback_url: str, max_duration: int, record: bool) -> Dict:
        """
        Make a call using Bland AI.

        Args:
            phone_number (str): Phone number to call
            script (str): Call script
            from_number (str): From phone number
            voice_id (str): Voice ID
            callback_url (str): Callback URL
            max_duration (int): Maximum call duration in seconds
            record (bool): Whether to record the call

        Returns:
            Dict: Call result
        """
        try:
            # Check if Bland AI is available
            if self.service_status["bland_ai"]["status"] != "available":
                return {"error": "Bland AI service is not available"}

            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Bearer {self.bland_api_key}",
                    "Content-Type": "application/json"
                }

                payload = {
                    "phone_number": phone_number,
                    "task": script,
                    "from": from_number,
                    "voice_id": voice_id,
                    "reduce_latency": True,
                    "wait_for_greeting": True,
                    "record": record,
                    "max_duration": max_duration
                }

                if callback_url:
                    payload["webhook_url"] = callback_url

                async with session.post("https://api.bland.ai/v1/calls", headers=headers, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()

                        # Add to call history
                        call_record = {
                            "id": data.get("id"),
                            "provider": "bland_ai",
                            "phone_number": phone_number,
                            "from_number": from_number,
                            "script": script,
                            "status": data.get("status"),
                            "created_at": datetime.now().isoformat(),
                            "details": data
                        }
                        self.call_history.append(call_record)

                        # Add to active calls
                        self.active_calls[data.get("id")] = call_record

                        return {
                            "success": True,
                            "call_id": data.get("id"),
                            "status": data.get("status"),
                            "provider": "bland_ai",
                            "details": data
                        }
                    else:
                        error_data = await response.text()
                        return {
                            "error": f"Bland AI call failed: {error_data}",
                            "status_code": response.status
                        }

        except Exception as e:
            logger.exception(f"Error making Bland AI call: {e}")
            return {"error": str(e)}

    async def _make_air_ai_call(self, phone_number: str, script: str, from_number: str, voice_id: str, callback_url: str, max_duration: int, record: bool) -> Dict:
        """
        Make a call using Air AI.

        Args:
            phone_number (str): Phone number to call
            script (str): Call script
            from_number (str): From phone number
            voice_id (str): Voice ID
            callback_url (str): Callback URL
            max_duration (int): Maximum call duration in seconds
            record (bool): Whether to record the call

        Returns:
            Dict: Call result
        """
        try:
            # Check if Air AI is available
            if self.service_status["air_ai"]["status"] != "available":
                return {"error": "Air AI service is not available"}

            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Bearer {self.air_api_key}",
                    "Content-Type": "application/json"
                }

                payload = {
                    "phone_number": phone_number,
                    "prompt": script,
                    "caller_id": from_number,
                    "voice": voice_id,
                    "record": record,
                    "max_duration": max_duration
                }

                if callback_url:
                    payload["webhook_url"] = callback_url

                async with session.post("https://api.air.ai/v1/calls", headers=headers, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()

                        # Add to call history
                        call_record = {
                            "id": data.get("id"),
                            "provider": "air_ai",
                            "phone_number": phone_number,
                            "from_number": from_number,
                            "script": script,
                            "status": data.get("status"),
                            "created_at": datetime.now().isoformat(),
                            "details": data
                        }
                        self.call_history.append(call_record)

                        # Add to active calls
                        self.active_calls[data.get("id")] = call_record

                        return {
                            "success": True,
                            "call_id": data.get("id"),
                            "status": data.get("status"),
                            "provider": "air_ai",
                            "details": data
                        }
                    else:
                        error_data = await response.text()
                        return {
                            "error": f"Air AI call failed: {error_data}",
                            "status_code": response.status
                        }

        except Exception as e:
            logger.exception(f"Error making Air AI call: {e}")
            return {"error": str(e)}

    async def _make_twilio_call(self, phone_number: str, script: str, from_number: str, voice_id: str, callback_url: str, max_duration: int, record: bool) -> Dict:
        """
        Make a call using Twilio.

        Args:
            phone_number (str): Phone number to call
            script (str): Call script
            from_number (str): From phone number
            voice_id (str): Voice ID (not used for Twilio)
            callback_url (str): Callback URL
            max_duration (int): Maximum call duration in seconds
            record (bool): Whether to record the call

        Returns:
            Dict: Call result
        """
        try:
            # Check if Twilio is available
            if self.service_status["twilio"]["status"] != "available":
                return {"error": "Twilio service is not available"}

            # Generate TwiML for the call
            twiml = f"""
            <Response>
                <Say>{script}</Say>
                <Pause length="1"/>
                <Say>Please press any key to speak with a representative, or hang up to end the call.</Say>
                <Gather numDigits="1" timeout="10" action="{callback_url}"/>
            </Response>
            """

            async with aiohttp.ClientSession() as session:
                auth = aiohttp.BasicAuth(self.twilio_account_sid, self.twilio_auth_token)

                payload = {
                    "To": phone_number,
                    "From": from_number,
                    "Twiml": twiml,
                    "Record": str(record).lower(),
                    "TimeLimit": max_duration
                }

                if callback_url:
                    payload["StatusCallback"] = callback_url

                async with session.post(
                    f"https://api.twilio.com/2010-04-01/Accounts/{self.twilio_account_sid}/Calls.json",
                    auth=auth,
                    data=payload
                ) as response:
                    if response.status == 201:
                        data = await response.json()

                        # Add to call history
                        call_record = {
                            "id": data.get("sid"),
                            "provider": "twilio",
                            "phone_number": phone_number,
                            "from_number": from_number,
                            "script": script,
                            "status": data.get("status"),
                            "created_at": datetime.now().isoformat(),
                            "details": data
                        }
                        self.call_history.append(call_record)

                        # Add to active calls
                        self.active_calls[data.get("sid")] = call_record

                        return {
                            "success": True,
                            "call_id": data.get("sid"),
                            "status": data.get("status"),
                            "provider": "twilio",
                            "details": data
                        }
                    else:
                        error_data = await response.text()
                        return {
                            "error": f"Twilio call failed: {error_data}",
                            "status_code": response.status
                        }

        except Exception as e:
            logger.exception(f"Error making Twilio call: {e}")
            return {"error": str(e)}

    async def send_text_message(self, phone_number: str, message: str, options: Dict = None) -> Dict:
        """
        Send a text message.

        Args:
            phone_number (str): Phone number to send message to
            message (str): Message content
            options (Dict, optional): Message options
                - from_number (str): From phone number
                - media_urls (List[str]): Media URLs to attach

        Returns:
            Dict: Message result
        """
        if not self.enabled:
            return {"error": "Voice calling service is disabled"}

        # Get options
        options = options or {}
        from_number = options.get("from_number", self.default_from_number)
        media_urls = options.get("media_urls", [])

        # Validate phone number
        if not self._validate_phone_number(phone_number):
            return {"error": "Invalid phone number"}

        try:
            # Check if Twilio is available
            if self.service_status["twilio"]["status"] != "available":
                return {"error": "Twilio service is not available"}

            async with aiohttp.ClientSession() as session:
                auth = aiohttp.BasicAuth(self.twilio_account_sid, self.twilio_auth_token)

                payload = {
                    "To": phone_number,
                    "From": from_number,
                    "Body": message
                }

                # Add media URLs if provided
                for i, media_url in enumerate(media_urls):
                    payload[f"MediaUrl{i}"] = media_url

                async with session.post(
                    f"https://api.twilio.com/2010-04-01/Accounts/{self.twilio_account_sid}/Messages.json",
                    auth=auth,
                    data=payload
                ) as response:
                    if response.status == 201:
                        data = await response.json()

                        return {
                            "success": True,
                            "message_id": data.get("sid"),
                            "status": data.get("status"),
                            "details": data
                        }
                    else:
                        error_data = await response.text()
                        return {
                            "error": f"Text message failed: {error_data}",
                            "status_code": response.status
                        }

        except Exception as e:
            logger.exception(f"Error sending text message: {e}")
            return {"error": str(e)}

    async def leave_voicemail(self, phone_number: str, message: str, options: Dict = None) -> Dict:
        """
        Leave a voicemail.

        Args:
            phone_number (str): Phone number to leave voicemail for
            message (str): Voicemail message
            options (Dict, optional): Voicemail options
                - provider (str): Voice provider (elevenlabs, twilio)
                - from_number (str): From phone number
                - voice_id (str): Voice ID

        Returns:
            Dict: Voicemail result
        """
        if not self.enabled:
            return {"error": "Voice calling service is disabled"}

        # Get options
        options = options or {}
        provider = options.get("provider", self.default_voice_provider)
        from_number = options.get("from_number", self.default_from_number)
        voice_id = options.get("voice_id", self.default_voice_id)

        # Validate phone number
        if not self._validate_phone_number(phone_number):
            return {"error": "Invalid phone number"}

        # Generate audio for voicemail
        if provider == "elevenlabs":
            audio_result = await self._generate_elevenlabs_audio(message, voice_id)
            if "error" in audio_result:
                return audio_result

            audio_url = audio_result.get("audio_url")
        else:
            # Use Twilio's text-to-speech
            audio_url = None

        # Leave voicemail using Twilio
        try:
            # Check if Twilio is available
            if self.service_status["twilio"]["status"] != "available":
                return {"error": "Twilio service is not available"}

            async with aiohttp.ClientSession() as session:
                auth = aiohttp.BasicAuth(self.twilio_account_sid, self.twilio_auth_token)

                # Generate TwiML for the voicemail
                if audio_url:
                    twiml = f"""
                    <Response>
                        <Play>{audio_url}</Play>
                    </Response>
                    """
                else:
                    twiml = f"""
                    <Response>
                        <Say>{message}</Say>
                    </Response>
                    """

                payload = {
                    "To": phone_number,
                    "From": from_number,
                    "Twiml": twiml,
                    "SendDigits": "wwwwwwwwww1",  # Wait and then press 1 to skip greeting
                    "Record": "false"
                }

                async with session.post(
                    f"https://api.twilio.com/2010-04-01/Accounts/{self.twilio_account_sid}/Calls.json",
                    auth=auth,
                    data=payload
                ) as response:
                    if response.status == 201:
                        data = await response.json()

                        return {
                            "success": True,
                            "voicemail_id": data.get("sid"),
                            "status": data.get("status"),
                            "details": data
                        }
                    else:
                        error_data = await response.text()
                        return {
                            "error": f"Voicemail failed: {error_data}",
                            "status_code": response.status
                        }

        except Exception as e:
            logger.exception(f"Error leaving voicemail: {e}")
            return {"error": str(e)}

    async def _generate_elevenlabs_audio(self, text: str, voice_id: str) -> Dict:
        """
        Generate audio using ElevenLabs.

        Args:
            text (str): Text to convert to speech
            voice_id (str): Voice ID

        Returns:
            Dict: Audio generation result
        """
        try:
            # Check if ElevenLabs is available
            if self.service_status["elevenlabs"]["status"] != "available":
                return {"error": "ElevenLabs service is not available"}

            # Use default voice ID if not provided
            if not voice_id:
                voice_id = self.default_voice_id

            async with aiohttp.ClientSession() as session:
                headers = {
                    "xi-api-key": self.elevenlabs_api_key,
                    "Content-Type": "application/json"
                }

                payload = {
                    "text": text,
                    "model_id": "eleven_monolingual_v1",
                    "voice_settings": {
                        "stability": 0.5,
                        "similarity_boost": 0.5
                    }
                }

                async with session.post(
                    f"https://api.elevenlabs.io/v1/text-to-speech/{voice_id}",
                    headers=headers,
                    json=payload
                ) as response:
                    if response.status == 200:
                        # Save audio to a temporary file
                        audio_data = await response.read()
                        file_id = str(uuid.uuid4())
                        file_path = f"temp_audio_{file_id}.mp3"

                        with open(file_path, "wb") as f:
                            f.write(audio_data)

                        # In a real implementation, you would upload this file to a cloud storage
                        # and return the URL. For now, we'll just return the file path.
                        return {
                            "success": True,
                            "audio_file": file_path,
                            "audio_url": f"file://{os.path.abspath(file_path)}"
                        }
                    else:
                        error_data = await response.text()
                        return {
                            "error": f"ElevenLabs audio generation failed: {error_data}",
                            "status_code": response.status
                        }

        except Exception as e:
            logger.exception(f"Error generating ElevenLabs audio: {e}")
            return {"error": str(e)}

    async def get_call_status(self, call_id: str, provider: str = None) -> Dict:
        """
        Get the status of a call.

        Args:
            call_id (str): Call ID
            provider (str, optional): Provider (bland_ai, air_ai, twilio)

        Returns:
            Dict: Call status
        """
        if not self.enabled:
            return {"error": "Voice calling service is disabled"}

        # Check if call is in active calls
        if call_id in self.active_calls:
            call_record = self.active_calls[call_id]
            provider = call_record.get("provider")
        elif not provider:
            return {"error": "Provider must be specified for unknown call ID"}

        # Get call status based on provider
        if provider == "bland_ai":
            return await self._get_bland_ai_call_status(call_id)
        elif provider == "air_ai":
            return await self._get_air_ai_call_status(call_id)
        elif provider == "twilio":
            return await self._get_twilio_call_status(call_id)
        else:
            return {"error": f"Unknown provider: {provider}"}

    async def _get_bland_ai_call_status(self, call_id: str) -> Dict:
        """
        Get the status of a Bland AI call.

        Args:
            call_id (str): Call ID

        Returns:
            Dict: Call status
        """
        try:
            # Check if Bland AI is available
            if self.service_status["bland_ai"]["status"] != "available":
                return {"error": "Bland AI service is not available"}

            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Bearer {self.bland_api_key}",
                    "Content-Type": "application/json"
                }

                async with session.get(f"https://api.bland.ai/v1/calls/{call_id}", headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()

                        # Update call record if in active calls
                        if call_id in self.active_calls:
                            self.active_calls[call_id]["status"] = data.get("status")
                            self.active_calls[call_id]["details"] = data

                        return {
                            "success": True,
                            "call_id": call_id,
                            "status": data.get("status"),
                            "provider": "bland_ai",
                            "details": data
                        }
                    else:
                        error_data = await response.text()
                        return {
                            "error": f"Failed to get Bland AI call status: {error_data}",
                            "status_code": response.status
                        }

        except Exception as e:
            logger.exception(f"Error getting Bland AI call status: {e}")
            return {"error": str(e)}

    async def _get_air_ai_call_status(self, call_id: str) -> Dict:
        """
        Get the status of an Air AI call.

        Args:
            call_id (str): Call ID

        Returns:
            Dict: Call status
        """
        try:
            # Check if Air AI is available
            if self.service_status["air_ai"]["status"] != "available":
                return {"error": "Air AI service is not available"}

            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Bearer {self.air_api_key}",
                    "Content-Type": "application/json"
                }

                async with session.get(f"https://api.air.ai/v1/calls/{call_id}", headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()

                        # Update call record if in active calls
                        if call_id in self.active_calls:
                            self.active_calls[call_id]["status"] = data.get("status")
                            self.active_calls[call_id]["details"] = data

                        return {
                            "success": True,
                            "call_id": call_id,
                            "status": data.get("status"),
                            "provider": "air_ai",
                            "details": data
                        }
                    else:
                        error_data = await response.text()
                        return {
                            "error": f"Failed to get Air AI call status: {error_data}",
                            "status_code": response.status
                        }

        except Exception as e:
            logger.exception(f"Error getting Air AI call status: {e}")
            return {"error": str(e)}

    async def _get_twilio_call_status(self, call_id: str) -> Dict:
        """
        Get the status of a Twilio call.

        Args:
            call_id (str): Call ID

        Returns:
            Dict: Call status
        """
        try:
            # Check if Twilio is available
            if self.service_status["twilio"]["status"] != "available":
                return {"error": "Twilio service is not available"}

            async with aiohttp.ClientSession() as session:
                auth = aiohttp.BasicAuth(self.twilio_account_sid, self.twilio_auth_token)

                async with session.get(
                    f"https://api.twilio.com/2010-04-01/Accounts/{self.twilio_account_sid}/Calls/{call_id}.json",
                    auth=auth
                ) as response:
                    if response.status == 200:
                        data = await response.json()

                        # Update call record if in active calls
                        if call_id in self.active_calls:
                            self.active_calls[call_id]["status"] = data.get("status")
                            self.active_calls[call_id]["details"] = data

                        return {
                            "success": True,
                            "call_id": call_id,
                            "status": data.get("status"),
                            "provider": "twilio",
                            "details": data
                        }
                    else:
                        error_data = await response.text()
                        return {
                            "error": f"Failed to get Twilio call status: {error_data}",
                            "status_code": response.status
                        }

        except Exception as e:
            logger.exception(f"Error getting Twilio call status: {e}")
            return {"error": str(e)}

    def _validate_phone_number(self, phone_number: str) -> bool:
        """
        Validate a phone number.

        Args:
            phone_number (str): Phone number to validate

        Returns:
            bool: True if valid, False otherwise
        """
        # Remove any non-digit characters
        digits_only = ''.join(filter(str.isdigit, phone_number))

        # Check if it's a valid length (10-15 digits)
        return 10 <= len(digits_only) <= 15
