@echo off
echo.
echo ===================================
echo    Messages for Alyssa Chirinos
echo ===================================
echo.

REM Set client information
set "CLIENT_NAME=Alyssa Chirinos"
set "CLIENT_EMAIL=<EMAIL>"
set "CLIENT_PHONE=**********"
set "CLIENT_DOB=8/16/97"
set "CLIENT_ADDRESS=Bradenton, Florida"
set "INSURANCE_TYPE=IUL with Dental, Vision, and Basic Health"
set "PREMIUM=$100/month"
set "AGENT_NAME=Sandra"
set "ADDITIONAL_NOTES=Primary interest is IUL. Also interested in dental, vision, and basic private health coverage for checkups, physicals, and bloodwork. TOTAL BUDGET IS $100/MONTH. Need to check all carriers for best solution within budget."

echo Client: %CLIENT_NAME%
echo Email: %CLIENT_EMAIL%
echo Phone: %CLIENT_PHONE%
echo DOB: %CLIENT_DOB%
echo Address: %CLIENT_ADDRESS%
echo Insurance Type: %INSURANCE_TYPE%
echo Estimated Premium: %PREMIUM%
echo Agent: %AGENT_NAME%
echo.
echo Additional Notes:
echo %ADDITIONAL_NOTES%
echo.
echo.

REM Run the command to show what would be sent
python client_outreach.py --name "%CLIENT_NAME%" --email "%CLIENT_EMAIL%" --phone "%CLIENT_PHONE%" --dob "%CLIENT_DOB%" --address "%CLIENT_ADDRESS%" --insurance-type "%INSURANCE_TYPE%" --premium "%PREMIUM%" --agent "%AGENT_NAME%" --notes "%ADDITIONAL_NOTES%" --quote --mock

echo.
echo ===================================
echo.
pause
