"""
Test script for sending an email using UI-TARS.

This script demonstrates how to send an <NAME_EMAIL>
to <EMAIL> using UI-TARS browser automation.
"""
import os
import sys
import asyncio
import logging
import getpass
from typing import Dict, Optional, Any

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("test_ui_tars_email")

# Import UI-TARS Gmail automation
try:
    from ui_tars_gmail_automation import GmailUITarsAutomation
except ImportError:
    logger.error("UI-TARS Gmail automation not found. Make sure ui_tars_gmail_automation.py is in the current directory.")
    sys.exit(1)

async def test_ui_tars_email():
    """Test sending an email using UI-TARS."""
    # Email details
    from_email = "<EMAIL>"
    to_email = "<EMAIL>"
    subject = "Test Email from UI-TARS"
    body = "This is a test email sent using UI-TARS browser automation."

    # Use stored password
    password = "GodisSoGood!777"  # Password is stored in config

    # Create Gmail UI-TARS automation
    gmail_automation = GmailUITarsAutomation()

    # Initialize
    logger.info("Initializing UI-TARS Gmail automation")
    initialized = await gmail_automation.initialize()
    if not initialized:
        logger.error("Failed to initialize UI-TARS Gmail automation")
        return

    try:
        # Send email
        logger.info(f"Sending email from {from_email} to {to_email}")
        result = await gmail_automation.send_email(
            email_account=from_email,
            password=password,
            to_email=to_email,
            subject=subject,
            body=body
        )

        if result["success"]:
            logger.info("Email sent successfully")
        else:
            logger.error(f"Failed to send email: {result['error']}")

    finally:
        # Shut down
        logger.info("Shutting down UI-TARS Gmail automation")
        await gmail_automation.shutdown()

if __name__ == "__main__":
    asyncio.run(test_ui_tars_email())
