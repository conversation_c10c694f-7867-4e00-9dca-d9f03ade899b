"""
Configuration settings for the Multi-Agent AI System.
"""
import os
from pathlib import Path
from typing import Dict, List, Optional, Union
from dotenv import load_dotenv

# Load environment variables from .env file
env_path = Path('.') / '.env'
load_dotenv(dotenv_path=env_path)

# System settings
DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'
LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
SYSTEM_NAME = "Multi-Agent AI System"
VERSION = "0.1.0"

# Paths
BASE_DIR = Path(__file__).resolve().parent
DATA_DIR = BASE_DIR / "data"
LOG_DIR = BASE_DIR / "logs"
MODELS_DIR = BASE_DIR / "models"
CREDENTIALS_DIR = BASE_DIR / "credentials"

# Create directories if they don't exist
for directory in [DATA_DIR, LOG_DIR, MODELS_DIR, CREDENTIALS_DIR]:
    directory.mkdir(exist_ok=True)

# Database settings
DATABASE_URL = os.getenv('DATABASE_URL', f"sqlite:///{BASE_DIR}/data/system.db")

# LLM API settings
LLM_CONFIG = {
    "anthropic": {
        "api_key": os.getenv("ANTHROPIC_API_KEY", ""),
        "models": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307"],
        "default_model": "claude-3-sonnet-20240229",
        "enabled": os.getenv("ENABLE_ANTHROPIC", "True").lower() == "true",
    },
    "openai": {
        "api_key": os.getenv("OPENAI_API_KEY", ""),
        "models": ["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo"],
        "default_model": "gpt-4",
        "enabled": os.getenv("ENABLE_OPENAI", "True").lower() == "true",
    },
    "grok": {
        "api_key": os.getenv("GROK_API_KEY", ""),
        "models": ["grok-1"],
        "default_model": "grok-1",
        "enabled": os.getenv("ENABLE_GROK", "False").lower() == "true",
    },
    # Add other LLM providers as needed
}

# Default LLM to use when not specified
DEFAULT_LLM_PROVIDER = os.getenv("DEFAULT_LLM_PROVIDER", "anthropic")

# Agent settings
AGENT_CONFIG = {
    "insurance_agent": {
        "name": "Insurance Assistant",
        "description": "Manages Flo Faction Insurance business operations",
        "llm_provider": os.getenv("INSURANCE_AGENT_LLM", DEFAULT_LLM_PROVIDER),
        "enabled": os.getenv("ENABLE_INSURANCE_AGENT", "True").lower() == "true",
        "polling_interval": int(os.getenv("INSURANCE_AGENT_POLLING_INTERVAL", "300")),  # seconds
    },
    "trading_agent": {
        "name": "Trading Assistant",
        "description": "Manages trading and investment operations",
        "llm_provider": os.getenv("TRADING_AGENT_LLM", DEFAULT_LLM_PROVIDER),
        "enabled": os.getenv("ENABLE_TRADING_AGENT", "True").lower() == "true",
        "polling_interval": int(os.getenv("TRADING_AGENT_POLLING_INTERVAL", "60")),  # seconds
    },
    "social_media_agent": {
        "name": "Social Media Assistant",
        "description": "Manages social media marketing and content creation",
        "llm_provider": os.getenv("SOCIAL_MEDIA_AGENT_LLM", DEFAULT_LLM_PROVIDER),
        "enabled": os.getenv("ENABLE_SOCIAL_MEDIA_AGENT", "True").lower() == "true",
        "polling_interval": int(os.getenv("SOCIAL_MEDIA_AGENT_POLLING_INTERVAL", "600")),  # seconds
    },
    "music_agent": {
        "name": "Music Industry Assistant",
        "description": "Manages music industry operations and promotion",
        "llm_provider": os.getenv("MUSIC_AGENT_LLM", DEFAULT_LLM_PROVIDER),
        "enabled": os.getenv("ENABLE_MUSIC_AGENT", "True").lower() == "true",
        "polling_interval": int(os.getenv("MUSIC_AGENT_POLLING_INTERVAL", "1800")),  # seconds
    },
    "communication_agent": {
        "name": "Communication Assistant",
        "description": "Handles calls, emails, and text messages",
        "llm_provider": os.getenv("COMMUNICATION_AGENT_LLM", DEFAULT_LLM_PROVIDER),
        "enabled": os.getenv("ENABLE_COMMUNICATION_AGENT", "True").lower() == "true",
        "polling_interval": int(os.getenv("COMMUNICATION_AGENT_POLLING_INTERVAL", "120")),  # seconds
    },
    "research_agent": {
        "name": "Research Assistant",
        "description": "Performs web scraping and information gathering",
        "llm_provider": os.getenv("RESEARCH_AGENT_LLM", DEFAULT_LLM_PROVIDER),
        "enabled": os.getenv("ENABLE_RESEARCH_AGENT", "True").lower() == "true",
        "polling_interval": int(os.getenv("RESEARCH_AGENT_POLLING_INTERVAL", "300")),  # seconds
    },
    "cybersecurity_agent": {
        "name": "Cybersecurity Assistant",
        "description": "Performs security testing and vulnerability analysis",
        "llm_provider": os.getenv("CYBERSECURITY_AGENT_LLM", DEFAULT_LLM_PROVIDER),
        "enabled": os.getenv("ENABLE_CYBERSECURITY_AGENT", "True").lower() == "true",
        "polling_interval": int(os.getenv("CYBERSECURITY_AGENT_POLLING_INTERVAL", "300")),  # seconds
    },
    "email_agent": {
        "name": "Email Agent",
        "description": "Handles email communications with reasoning capabilities",
        "llm_provider": os.getenv("EMAIL_AGENT_LLM", DEFAULT_LLM_PROVIDER),
        "enabled": os.getenv("ENABLE_EMAIL_AGENT", "True").lower() == "true",
        "polling_interval": int(os.getenv("EMAIL_AGENT_POLLING_INTERVAL", "300")),  # seconds
    },
    "multi_account_email_agent": {
        "name": "Multi-Account Email Agent",
        "description": "Handles email communications across multiple accounts with reasoning capabilities",
        "llm_provider": os.getenv("MULTI_ACCOUNT_EMAIL_AGENT_LLM", DEFAULT_LLM_PROVIDER),
        "enabled": os.getenv("ENABLE_MULTI_ACCOUNT_EMAIL_AGENT", "True").lower() == "true",
        "polling_interval": int(os.getenv("MULTI_ACCOUNT_EMAIL_AGENT_POLLING_INTERVAL", "300")),  # seconds
    },
    "insurance_lead_agent": {
        "name": "Insurance Lead Agent",
        "description": "Handles leads from multiple channels (Facebook, Instagram, TikTok, website)",
        "llm_provider": os.getenv("INSURANCE_LEAD_AGENT_LLM", DEFAULT_LLM_PROVIDER),
        "enabled": os.getenv("ENABLE_INSURANCE_LEAD_AGENT", "True").lower() == "true",
        "polling_interval": int(os.getenv("INSURANCE_LEAD_AGENT_POLLING_INTERVAL", "60")),  # seconds
    },
}

# Communication service settings
COMMUNICATION_CONFIG = {
    "email": {
        "service": os.getenv("EMAIL_SERVICE", "sendgrid"),
        "api_key": os.getenv("EMAIL_API_KEY", ""),
        "sender_email": os.getenv("SENDER_EMAIL", ""),
        "enabled": os.getenv("ENABLE_EMAIL", "False").lower() == "true",
    },
    "sms": {
        "service": os.getenv("SMS_SERVICE", "twilio"),
        "account_sid": os.getenv("TWILIO_ACCOUNT_SID", ""),
        "auth_token": os.getenv("TWILIO_AUTH_TOKEN", ""),
        "phone_number": os.getenv("TWILIO_PHONE_NUMBER", ""),
        "enabled": os.getenv("ENABLE_SMS", "False").lower() == "true",
    },
    "voice": {
        "service": os.getenv("VOICE_SERVICE", "twilio"),
        "account_sid": os.getenv("TWILIO_ACCOUNT_SID", ""),
        "auth_token": os.getenv("TWILIO_AUTH_TOKEN", ""),
        "phone_number": os.getenv("TWILIO_PHONE_NUMBER", ""),
        "enabled": os.getenv("ENABLE_VOICE", "False").lower() == "true",
    },
}

# Trading platform settings
TRADING_CONFIG = {
    "alpaca": {
        "api_key": os.getenv("ALPACA_API_KEY", ""),
        "api_secret": os.getenv("ALPACA_API_SECRET", ""),
        "base_url": os.getenv("ALPACA_BASE_URL", "https://paper-api.alpaca.markets"),  # Paper trading by default
        "enabled": os.getenv("ENABLE_ALPACA", "False").lower() == "true",
    },
    "crypto": {
        "exchange": os.getenv("CRYPTO_EXCHANGE", "binance"),
        "api_key": os.getenv("CRYPTO_API_KEY", ""),
        "api_secret": os.getenv("CRYPTO_API_SECRET", ""),
        "enabled": os.getenv("ENABLE_CRYPTO", "False").lower() == "true",
    },
}

# Social media platform settings
SOCIAL_MEDIA_CONFIG = {
    "twitter": {
        "api_key": os.getenv("TWITTER_API_KEY", ""),
        "api_secret": os.getenv("TWITTER_API_SECRET", ""),
        "access_token": os.getenv("TWITTER_ACCESS_TOKEN", ""),
        "access_token_secret": os.getenv("TWITTER_ACCESS_TOKEN_SECRET", ""),
        "enabled": os.getenv("ENABLE_TWITTER", "False").lower() == "true",
    },
    "facebook": {
        "app_id": os.getenv("FACEBOOK_APP_ID", ""),
        "app_secret": os.getenv("FACEBOOK_APP_SECRET", ""),
        "access_token": os.getenv("FACEBOOK_ACCESS_TOKEN", ""),
        "enabled": os.getenv("ENABLE_FACEBOOK", "False").lower() == "true",
    },
    "instagram": {
        "username": os.getenv("INSTAGRAM_USERNAME", ""),
        "password": os.getenv("INSTAGRAM_PASSWORD", ""),
        "enabled": os.getenv("ENABLE_INSTAGRAM", "False").lower() == "true",
    },
    "youtube": {
        "api_key": os.getenv("YOUTUBE_API_KEY", ""),
        "channel_id": os.getenv("YOUTUBE_CHANNEL_ID", ""),
        "enabled": os.getenv("ENABLE_YOUTUBE", "False").lower() == "true",
    },
}

# Music platform settings
MUSIC_CONFIG = {
    "spotify": {
        "client_id": os.getenv("SPOTIFY_CLIENT_ID", ""),
        "client_secret": os.getenv("SPOTIFY_CLIENT_SECRET", ""),
        "redirect_uri": os.getenv("SPOTIFY_REDIRECT_URI", ""),
        "enabled": os.getenv("ENABLE_SPOTIFY", "False").lower() == "true",
    },
    "soundcloud": {
        "client_id": os.getenv("SOUNDCLOUD_CLIENT_ID", ""),
        "client_secret": os.getenv("SOUNDCLOUD_CLIENT_SECRET", ""),
        "enabled": os.getenv("ENABLE_SOUNDCLOUD", "False").lower() == "true",
    },
}

# Web interface settings
WEB_INTERFACE = {
    "enabled": os.getenv("ENABLE_WEB_INTERFACE", "True").lower() == "true",
    "host": os.getenv("WEB_HOST", "0.0.0.0"),
    "port": int(os.getenv("WEB_PORT", "8000")),
    "debug": DEBUG,
}

# GitHub settings
GITHUB_CONFIG = {
    "api_key": os.getenv("GITHUB_API_KEY", ""),
    "username": os.getenv("GITHUB_USERNAME", ""),
    "cache_dir": os.path.join(DATA_DIR, "github_cache"),
    "enabled": os.getenv("ENABLE_GITHUB", "False").lower() == "true",
}

# Hugging Face settings
HUGGINGFACE_CONFIG = {
    "api_key": os.getenv("HUGGINGFACE_API_KEY", ""),
    "use_local": os.getenv("HUGGINGFACE_USE_LOCAL", "False").lower() == "true",
    "local_url": os.getenv("HUGGINGFACE_LOCAL_URL", "http://localhost:8080"),
    "cache_dir": os.path.join(DATA_DIR, "huggingface_cache"),
    "enabled": os.getenv("ENABLE_HUGGINGFACE", "False").lower() == "true",
    "inference_endpoints": {
        "text-generation": os.getenv("HUGGINGFACE_TEXT_GENERATION_ENDPOINT", ""),
        "embeddings": os.getenv("HUGGINGFACE_EMBEDDINGS_ENDPOINT", ""),
    },
}

# Security settings
SECURITY_CONFIG = {
    "api_key": os.getenv("SYSTEM_API_KEY", ""),
    "jwt_secret": os.getenv("JWT_SECRET", ""),
    "jwt_algorithm": os.getenv("JWT_ALGORITHM", "HS256"),
    "jwt_expiration": int(os.getenv("JWT_EXPIRATION", "86400")),  # 24 hours in seconds
}

# Tool service settings
TOOL_CONFIG = {
    "enabled": os.getenv("ENABLE_TOOL_SERVICE", "True").lower() == "true",
    "tools_dir": os.path.join(BASE_DIR, "tools"),
    "tool_registry": {
        "nmap": {
            "description": "Network mapper for network discovery and security auditing",
            "category": "network",
            "install_method": "auto",
            "enabled": os.getenv("ENABLE_NMAP", "True").lower() == "true",
        },
        "john": {
            "description": "John the Ripper password cracker",
            "category": "password",
            "install_method": "auto",
            "enabled": os.getenv("ENABLE_JOHN", "True").lower() == "true",
        },
        "wireshark": {
            "description": "Network protocol analyzer",
            "category": "network",
            "install_method": "auto",
            "enabled": os.getenv("ENABLE_WIRESHARK", "True").lower() == "true",
        },
        "metasploit-framework": {
            "description": "Penetration testing framework",
            "category": "exploitation",
            "install_method": "auto",
            "enabled": os.getenv("ENABLE_METASPLOIT", "True").lower() == "true",
        },
        "burpsuite": {
            "description": "Web vulnerability scanner",
            "category": "web",
            "install_method": "auto",
            "enabled": os.getenv("ENABLE_BURPSUITE", "True").lower() == "true",
        },
        "aircrack-ng": {
            "description": "WiFi security assessment tools",
            "category": "wifi",
            "install_method": "auto",
            "enabled": os.getenv("ENABLE_AIRCRACK", "True").lower() == "true",
        },
        "sqlmap": {
            "description": "Automatic SQL injection tool",
            "category": "web",
            "install_method": "auto",
            "enabled": os.getenv("ENABLE_SQLMAP", "True").lower() == "true",
        },
        "zaproxy": {
            "description": "OWASP Zed Attack Proxy",
            "category": "web",
            "install_method": "auto",
            "enabled": os.getenv("ENABLE_ZAPROXY", "True").lower() == "true",
        },
        "theharvester": {
            "description": "E-mail, subdomain and name harvester",
            "category": "reconnaissance",
            "install_method": "auto",
            "enabled": os.getenv("ENABLE_THEHARVESTER", "True").lower() == "true",
        },
        "nikto": {
            "description": "Web server scanner",
            "category": "web",
            "install_method": "auto",
            "enabled": os.getenv("ENABLE_NIKTO", "True").lower() == "true",
        },
        "pentestgpt": {
            "description": "AI-powered penetration testing tool",
            "category": "ai",
            "install_method": "pip",
            "enabled": os.getenv("ENABLE_PENTESTGPT", "True").lower() == "true",
        },
    }
}

# Vulnerability database settings
VULNERABILITY_DATABASE_CONFIG = {
    "enabled": os.getenv("ENABLE_VULNERABILITY_DATABASE", "True").lower() == "true",
    "api_key": os.getenv("NVD_API_KEY", ""),
    "cache_dir": os.path.join(DATA_DIR, "vulnerability_cache"),
    "cache_ttl": int(os.getenv("VULNERABILITY_CACHE_TTL", "86400")),  # 24 hours in seconds
}

# MPC (Multi-Party Computation) server settings
MPC_CONFIG = {
    "enabled": os.getenv("ENABLE_MPC", "True").lower() == "true",
    "servers": {
        "local": {
            "host": os.getenv("MPC_LOCAL_HOST", "localhost"),
            "port": int(os.getenv("MPC_LOCAL_PORT", "8765")),
            "use_ssl": os.getenv("MPC_LOCAL_USE_SSL", "False").lower() == "true",
            "cert_file": os.getenv("MPC_LOCAL_CERT_FILE", ""),
            "key_file": os.getenv("MPC_LOCAL_KEY_FILE", ""),
            "enabled": os.getenv("ENABLE_MPC_LOCAL", "True").lower() == "true",
        },
        "remote": {
            "host": os.getenv("MPC_REMOTE_HOST", ""),
            "port": int(os.getenv("MPC_REMOTE_PORT", "8765")),
            "use_ssl": os.getenv("MPC_REMOTE_USE_SSL", "True").lower() == "true",
            "ca_file": os.getenv("MPC_REMOTE_CA_FILE", ""),
            "enabled": os.getenv("ENABLE_MPC_REMOTE", "False").lower() == "true",
        },
    },
}

# Quantum computing settings
QUANTUM_CONFIG = {
    "enabled": os.getenv("ENABLE_QUANTUM", "True").lower() == "true",
    "provider": os.getenv("QUANTUM_PROVIDER", "simulator"),
    "api_key": os.getenv("QUANTUM_API_KEY", ""),
    "use_local": os.getenv("QUANTUM_USE_LOCAL", "True").lower() == "true",
    "local_url": os.getenv("QUANTUM_LOCAL_URL", "http://localhost:8081"),
}

# Audio processing settings
AUDIO_CONFIG = {
    "enabled": os.getenv("ENABLE_AUDIO", "True").lower() == "true",
    "api_key": os.getenv("AUDIO_API_KEY", ""),
    "api_url": os.getenv("AUDIO_API_URL", ""),
    "local_mode": os.getenv("AUDIO_LOCAL_MODE", "True").lower() == "true",
    "audio_dir": os.path.join(BASE_DIR, "audio"),
    "speech_recognition_engine": os.getenv("SPEECH_RECOGNITION_ENGINE", "whisper"),
    "text_to_speech_engine": os.getenv("TEXT_TO_SPEECH_ENGINE", "pyttsx3"),
}

# Machine learning settings
MACHINE_LEARNING_CONFIG = {
    "enabled": os.getenv("ENABLE_MACHINE_LEARNING", "True").lower() == "true",
    "models_dir": os.path.join(MODELS_DIR, "machine_learning"),
    "use_local": os.getenv("MACHINE_LEARNING_USE_LOCAL", "True").lower() == "true",
    "api_key": os.getenv("MACHINE_LEARNING_API_KEY", ""),
    "api_url": os.getenv("MACHINE_LEARNING_API_URL", ""),
}
