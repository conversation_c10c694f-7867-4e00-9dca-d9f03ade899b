# UI-TARS Gmail Integration

This package provides integration between UI-TARS 1.5 and Gmail browser automation for your AI Agent System. It leverages UI-TARS's screen understanding and reasoning capabilities to enhance the reliability of Gmail automation.

## Overview

The integration uses Selenium WebDriver for browser automation and UI-TARS 1.5 for screen analysis and decision-making. This approach combines the best of both worlds:

- **Browser Automation**: Reliable interaction with Gmail's interface
- **UI-TARS Analysis**: Advanced screen understanding and reasoning
- **Adaptive Behavior**: Better handling of unexpected situations
- **Visual Feedback**: See the automation happening in real-time

## Components

The package includes the following components:

- `ui_tars_gmail_integration.py`: Core module integrating UI-TARS with Gmail automation
- `gmail_browser_automation.py`: Standalone Gmail automation without UI-TARS
- `send_gmail_with_ui_tars.bat`: User-friendly batch script for sending emails with UI-TARS
- `send_gmail.bat`: Batch script for sending emails without UI-TARS
- `email_automation_requirements.txt`: Required Python packages

## Prerequisites

Before using this integration, make sure you have:

1. Python 3.8 or higher installed
2. A web browser installed (Chrome, Firefox, or Edge)
3. Gmail account credentials (email and password)
4. UI-TARS 1.5 installed (optional, but recommended)

## Quick Start

### 1. Install Required Packages

```bash
pip install -r email_automation_requirements.txt
```

This will install:
- Selenium: For browser automation
- WebDriver Manager: For automatic browser driver management

### 2. Send an Email with UI-TARS

The easiest way to send an email with UI-TARS integration is to run the batch script:

```bash
send_gmail_with_ui_tars.bat
```

This will:
1. Check and install required packages
2. Ask you to choose a browser
3. Ask for the path to UI-TARS installation
4. Prompt for email details
5. Send the email using browser automation with UI-TARS assistance

### 3. Send an Email without UI-TARS

If you don't have UI-TARS installed, you can still use the standalone Gmail automation:

```bash
send_gmail.bat
```

## How It Works

### UI-TARS Integration

The UI-TARS Gmail integration works by:

1. **Taking Screenshots**: At key points in the automation process
2. **Analyzing Screens**: Using UI-TARS to understand what's on the screen
3. **Getting Next Actions**: Asking UI-TARS what to do next
4. **Handling Challenges**: Using UI-TARS to detect and respond to verification challenges

This approach makes the automation more robust and adaptable to changes in Gmail's interface.

### Key Features

- **Screen Analysis**: UI-TARS analyzes screenshots to understand the current state
- **Decision Making**: UI-TARS suggests the next action based on the current screen
- **Error Handling**: Better detection and handling of errors and challenges
- **Verification Support**: Detects verification challenges and waits for manual intervention
- **Fallback Mechanisms**: Multiple approaches to handle different scenarios

## Programmatic Usage

### With UI-TARS Integration

```python
import asyncio
from ui_tars_gmail_integration import UITarsGmailIntegration

async def send_email():
    # Create UI-TARS Gmail Integration
    integration = UITarsGmailIntegration(
        browser_type="chrome",
        ui_tars_installation_path="C:/Path/To/UI-TARS"
    )
    
    # Initialize
    await integration.initialize()
    
    try:
        # Send email
        result = await integration.send_email(
            email_account="<EMAIL>",
            password="your_password",
            to_email="<EMAIL>",
            subject="Test Email",
            body="This is a test email sent using UI-TARS integration."
        )
        
        if result["success"]:
            print("Email sent successfully")
        else:
            print(f"Failed to send email: {result['error']}")
    
    finally:
        # Shut down
        await integration.shutdown()

if __name__ == "__main__":
    asyncio.run(send_email())
```

### Without UI-TARS Integration

```python
from gmail_browser_automation import GmailBrowserAutomation

# Create Gmail Browser Automation
gmail_automation = GmailBrowserAutomation(browser_type="chrome")

# Initialize
gmail_automation.initialize()

try:
    # Send email
    result = gmail_automation.send_email(
        email_account="<EMAIL>",
        password="your_password",
        to_email="<EMAIL>",
        subject="Test Email",
        body="This is a test email sent using browser automation."
    )
    
    if result["success"]:
        print("Email sent successfully")
    else:
        print(f"Failed to send email: {result['error']}")

finally:
    # Shut down
    gmail_automation.shutdown()
```

## Integration with Agent System

To integrate with your agent system:

```python
import asyncio
from ui_tars_gmail_integration import UITarsGmailIntegration

class EmailAgent:
    def __init__(self):
        self.integration = None
    
    async def initialize(self):
        self.integration = UITarsGmailIntegration(
            browser_type="chrome",
            ui_tars_installation_path="C:/Path/To/UI-TARS"
        )
        return await self.integration.initialize()
    
    async def send_email(self, from_email, password, to_email, subject, body):
        return await self.integration.send_email(
            email_account=from_email,
            password=password,
            to_email=to_email,
            subject=subject,
            body=body
        )
    
    async def shutdown(self):
        if self.integration:
            await self.integration.shutdown()
```

## Troubleshooting

If you encounter issues:

1. **UI-TARS not found**: Make sure you've provided the correct path to UI-TARS installation
2. **Login issues**: If Gmail detects suspicious activity, it might block the login. Try:
   - Logging in manually first
   - Using an app password instead of your regular password
   - Temporarily lowering your Google account security settings
3. **Verification challenges**: The script will detect verification challenges and wait for manual intervention. You'll have 60 seconds to complete the verification.
4. **Screenshots**: The script saves screenshots at key points for debugging. Check these if something goes wrong.

## Security Considerations

This automation requires your Gmail password. To enhance security:

1. Consider using an app password instead of your main password
2. Don't hardcode passwords in your scripts
3. Use environment variables or a secure credential manager
4. Only run the automation on trusted machines

## Next Steps

After setting up this integration, you can:

1. Extend it to read and process emails
2. Add support for attachments
3. Implement more complex email workflows
4. Integrate with other parts of your agent system
