Write-Host "Checking environment for UI-TARS and browser integration..."
Write-Host ""

# Check current directory
$currentDir = Get-Location
Write-Host "Current directory: $currentDir"
Write-Host ""

# List directories
Write-Host "Directories in current location:"
Get-ChildItem -Directory | ForEach-Object { Write-Host "- $_" }
Write-Host ""

# Check for Chrome
Write-Host "Checking for Chrome browser..."
$chromePaths = @(
    "C:\Program Files\Google\Chrome\Application\chrome.exe",
    "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
)

$chromeFound = $false
foreach ($path in $chromePaths) {
    if (Test-Path $path) {
        Write-Host "Chrome found at: $path"
        $chromeFound = $true
        break
    }
}

if (-not $chromeFound) {
    Write-Host "Chrome not found"
}
Write-Host ""

# Check for UI-TARS
Write-Host "Checking for UI-TARS installation..."
$uiTarsPaths = @(
    "$env:PROGRAMFILES\UI-TARS\UI-TARS.exe",
    "${env:PROGRAMFILES(X86)}\UI-TARS\UI-TARS.exe",
    "$env:LOCALAPPDATA\UI-TARS\UI-TARS.exe"
)

$uiTarsFound = $false
foreach ($path in $uiTarsPaths) {
    if (Test-Path $path) {
        Write-Host "UI-TARS found at: $path"
        $uiTarsFound = $true
        break
    }
}

if (-not $uiTarsFound) {
    Write-Host "UI-TARS not found"
}
Write-Host ""

# Check Python installation
Write-Host "Checking Python installation..."
try {
    $pythonVersion = python --version
    Write-Host "Python installed: $pythonVersion"
} catch {
    Write-Host "Python not found or not in PATH"
}
Write-Host ""

# Check for required modules
Write-Host "Checking for required Python modules..."
try {
    $pipList = pip list
    Write-Host "Installed Python modules:"
    $pipList | Select-Object -First 10 | ForEach-Object { Write-Host "- $_" }
    if ($pipList.Count -gt 10) {
        Write-Host "  ... and $($pipList.Count - 10) more"
    }
} catch {
    Write-Host "Could not get list of installed Python modules"
}
Write-Host ""

Write-Host "Environment check completed"
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
