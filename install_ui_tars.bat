@echo off
echo Installing UI-TARS 1.5...

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Check if pip is installed
python -m pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo pip is not installed. Please install pip.
    exit /b 1
)

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Node.js is not installed. Please install Node.js and npm.
    echo Midscene installation will be skipped.
    set SKIP_MIDSCENE=--skip-midscene
) else (
    set SKIP_MIDSCENE=
)

REM Install UI-TARS
cd ui_tars
python install.py %SKIP_MIDSCENE%

if %errorlevel% neq 0 (
    echo Failed to install UI-TARS.
    exit /b 1
)

echo UI-TARS 1.5 installed successfully.
echo To start the UI-TARS dashboard, run: python ui_tars\main.py --gui
echo To start UI-TARS from the command line, run: python ui_tars\main.py --start

exit /b 0
