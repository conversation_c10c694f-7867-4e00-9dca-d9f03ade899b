"""
Multi-Party Computation (MPC) Client for secure distributed computation.

This module implements a client for connecting to MPC servers and participating
in secure multi-party computations.
"""
import asyncio
import json
import logging
import os
import secrets
import ssl
import time
from typing import Dict, List, Optional, Any, Callable, Union
import uuid

from core.logger import setup_logger

# Set up logger
logger = setup_logger("mpc_client")

class MPCClient:
    """
    Multi-Party Computation Client for secure distributed computation.
    
    This class implements a client for connecting to MPC servers and participating
    in secure multi-party computations.
    """
    
    def __init__(
        self,
        client_id: str,
        server_host: str,
        server_port: int,
        use_ssl: bool = True,
        ca_file: Optional[str] = None,
    ):
        """
        Initialize the MPC client.
        
        Args:
            client_id (str): Unique identifier for this client
            server_host (str): MPC server host
            server_port (int): MPC server port
            use_ssl (bool): Whether to use SSL
            ca_file (Optional[str]): Path to CA certificate file
        """
        self.client_id = client_id
        self.server_host = server_host
        self.server_port = server_port
        self.use_ssl = use_ssl
        self.ca_file = ca_file
        
        # Client state
        self.connected = False
        self.reader = None
        self.writer = None
        self.session_key = None
        
        # Computation state
        self.active_computations = {}
        
        logger.info(f"MPC Client {client_id} initialized")
    
    async def connect(self):
        """Connect to the MPC server."""
        if self.connected:
            logger.warning("Already connected to MPC server")
            return
        
        try:
            # Create SSL context if needed
            ssl_context = None
            if self.use_ssl:
                ssl_context = self._create_ssl_context()
            
            # Connect to server
            self.reader, self.writer = await asyncio.open_connection(
                self.server_host,
                self.server_port,
                ssl=ssl_context,
            )
            
            self.connected = True
            logger.info(f"Connected to MPC server at {self.server_host}:{self.server_port}")
            
            # Send hello message
            await self._send_hello()
            
        except Exception as e:
            logger.exception(f"Error connecting to MPC server: {e}")
            self.connected = False
            raise
    
    async def disconnect(self):
        """Disconnect from the MPC server."""
        if not self.connected:
            logger.warning("Not connected to MPC server")
            return
        
        try:
            # Close connection
            if self.writer:
                self.writer.close()
                try:
                    await self.writer.wait_closed()
                except:
                    pass
                
            self.connected = False
            self.reader = None
            self.writer = None
            self.session_key = None
            
            logger.info("Disconnected from MPC server")
            
        except Exception as e:
            logger.exception(f"Error disconnecting from MPC server: {e}")
    
    def _create_ssl_context(self) -> ssl.SSLContext:
        """
        Create SSL context for secure connections.
        
        Returns:
            ssl.SSLContext: SSL context
        """
        ssl_context = ssl.create_default_context(ssl.Purpose.SERVER_AUTH)
        
        if self.ca_file:
            ssl_context.load_verify_locations(self.ca_file)
        
        return ssl_context
    
    async def _send_message(self, message: Dict) -> Dict:
        """
        Send a message to the MPC server and receive a response.
        
        Args:
            message (Dict): Message to send
            
        Returns:
            Dict: Response from server
        """
        if not self.connected:
            raise RuntimeError("Not connected to MPC server")
        
        try:
            # Send message
            data = json.dumps(message).encode()
            self.writer.write(data)
            await self.writer.drain()
            
            # Receive response
            response_data = await self.reader.read(4096)
            if not response_data:
                raise RuntimeError("Connection closed by server")
            
            # Parse response
            response = json.loads(response_data.decode())
            
            return response
            
        except Exception as e:
            logger.exception(f"Error sending message to MPC server: {e}")
            raise
    
    async def _send_hello(self):
        """
        Send hello message to the MPC server.
        
        Returns:
            Dict: Response from server
        """
        message = {
            "type": "hello",
            "client_id": self.client_id,
            "timestamp": time.time(),
        }
        
        response = await self._send_message(message)
        
        if "error" in response:
            raise RuntimeError(f"Error in hello response: {response['error']}")
        
        # Store session key
        self.session_key = response.get("session_key")
        
        return response
    
    async def create_computation(self, computation_type: str, parameters: Dict) -> str:
        """
        Create a new computation on the MPC server.
        
        Args:
            computation_type (str): Type of computation
            parameters (Dict): Computation parameters
            
        Returns:
            str: Computation ID
        """
        message = {
            "type": "create_computation",
            "client_id": self.client_id,
            "computation_type": computation_type,
            "parameters": parameters,
            "timestamp": time.time(),
        }
        
        response = await self._send_message(message)
        
        if "error" in response:
            raise RuntimeError(f"Error creating computation: {response['error']}")
        
        computation_id = response.get("computation_id")
        
        # Store computation
        self.active_computations[computation_id] = {
            "type": computation_type,
            "parameters": parameters,
            "created_at": time.time(),
            "status": "created",
        }
        
        return computation_id
