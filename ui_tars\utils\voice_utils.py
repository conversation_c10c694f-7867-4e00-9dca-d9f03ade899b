"""
Voice Command Utilities for UI-TARS.

This module provides utility functions for voice commands with UI-TARS.
"""
import os
import sys
import json
import asyncio
import logging
import subprocess
from typing import Dict, List, Optional, Any, Union, Callable
import platform
import threading
import queue
import time

from core.logger import setup_logger

# Set up logger
logger = setup_logger("ui_tars_voice_utils")

try:
    import speech_recognition as sr
    SPEECH_RECOGNITION_AVAILABLE = True
except ImportError:
    logger.warning("speech_recognition module not available, voice commands will not work")
    SPEECH_RECOGNITION_AVAILABLE = False

try:
    import pyttsx3
    TEXT_TO_SPEECH_AVAILABLE = True
except ImportError:
    logger.warning("pyttsx3 module not available, text-to-speech will not work")
    TEXT_TO_SPEECH_AVAILABLE = False

class VoiceCommandListener:
    """
    Voice Command Listener for UI-TARS.
    
    This class provides functionality to listen for voice commands
    and execute callbacks when commands are recognized.
    """
    
    def __init__(self, 
                 commands: Dict[str, Callable] = None,
                 language: str = "en-US",
                 activation_keyword: str = "tars"):
        """
        Initialize the Voice Command Listener.
        
        Args:
            commands (Dict[str, Callable]): Dictionary of commands and callbacks
            language (str): Language code for speech recognition
            activation_keyword (str): Keyword to activate voice commands
        """
        self.commands = commands or {}
        self.language = language
        self.activation_keyword = activation_keyword.lower()
        self.recognizer = None
        self.microphone = None
        self.is_listening = False
        self.listen_thread = None
        self.command_queue = queue.Queue()
        
        # Check if speech recognition is available
        if not SPEECH_RECOGNITION_AVAILABLE:
            logger.warning("Speech recognition not available, voice commands will not work")
            return
        
        # Initialize speech recognition
        self.recognizer = sr.Recognizer()
        
        # Adjust for ambient noise
        try:
            with sr.Microphone() as source:
                self.recognizer.adjust_for_ambient_noise(source)
                logger.info("Adjusted for ambient noise")
        except Exception as e:
            logger.exception(f"Error adjusting for ambient noise: {e}")
    
    def add_command(self, command: str, callback: Callable):
        """
        Add a command to the listener.
        
        Args:
            command (str): Command to listen for
            callback (Callable): Callback to execute when the command is recognized
        """
        self.commands[command.lower()] = callback
        logger.info(f"Added command: {command}")
    
    def remove_command(self, command: str):
        """
        Remove a command from the listener.
        
        Args:
            command (str): Command to remove
        """
        if command.lower() in self.commands:
            del self.commands[command.lower()]
            logger.info(f"Removed command: {command}")
    
    def start_listening(self):
        """Start listening for voice commands."""
        if not SPEECH_RECOGNITION_AVAILABLE:
            logger.warning("Speech recognition not available, cannot start listening")
            return False
        
        if self.is_listening:
            logger.info("Already listening for voice commands")
            return True
        
        logger.info("Starting to listen for voice commands")
        self.is_listening = True
        
        # Start the listen thread
        self.listen_thread = threading.Thread(target=self._listen_loop)
        self.listen_thread.daemon = True
        self.listen_thread.start()
        
        return True
    
    def stop_listening(self):
        """Stop listening for voice commands."""
        if not self.is_listening:
            logger.info("Not listening for voice commands")
            return True
        
        logger.info("Stopping listening for voice commands")
        self.is_listening = False
        
        # Wait for the listen thread to stop
        if self.listen_thread:
            self.listen_thread.join(timeout=2)
            self.listen_thread = None
        
        return True
    
    def _listen_loop(self):
        """Listen for voice commands in a loop."""
        logger.info("Voice command listener started")
        
        while self.is_listening:
            try:
                with sr.Microphone() as source:
                    logger.debug("Listening for voice commands...")
                    audio = self.recognizer.listen(source, timeout=5, phrase_time_limit=5)
                
                try:
                    # Recognize speech using Google Speech Recognition
                    text = self.recognizer.recognize_google(audio, language=self.language)
                    logger.debug(f"Recognized: {text}")
                    
                    # Check if the activation keyword is in the text
                    if self.activation_keyword in text.lower():
                        # Remove the activation keyword
                        command_text = text.lower().replace(self.activation_keyword, "").strip()
                        
                        # Add the command to the queue
                        self.command_queue.put(command_text)
                        
                        # Process the command
                        self._process_command(command_text)
                
                except sr.UnknownValueError:
                    logger.debug("Google Speech Recognition could not understand audio")
                except sr.RequestError as e:
                    logger.warning(f"Could not request results from Google Speech Recognition service: {e}")
                except Exception as e:
                    logger.exception(f"Error processing voice command: {e}")
            
            except Exception as e:
                logger.exception(f"Error in voice command listener: {e}")
                time.sleep(1)
        
        logger.info("Voice command listener stopped")
    
    def _process_command(self, command_text: str):
        """
        Process a voice command.
        
        Args:
            command_text (str): Command text to process
        """
        logger.info(f"Processing voice command: {command_text}")
        
        # Check if the command matches any registered commands
        for command, callback in self.commands.items():
            if command in command_text:
                logger.info(f"Executing callback for command: {command}")
                try:
                    callback(command_text)
                except Exception as e:
                    logger.exception(f"Error executing callback for command {command}: {e}")
                return
        
        logger.info(f"No matching command found for: {command_text}")
    
    def get_next_command(self) -> Optional[str]:
        """
        Get the next command from the queue.
        
        Returns:
            Optional[str]: Next command or None if the queue is empty
        """
        try:
            return self.command_queue.get_nowait()
        except queue.Empty:
            return None

class TextToSpeech:
    """
    Text-to-Speech for UI-TARS.
    
    This class provides functionality to convert text to speech.
    """
    
    def __init__(self, voice: Optional[str] = None, rate: int = 150):
        """
        Initialize the Text-to-Speech.
        
        Args:
            voice (Optional[str]): Voice to use
            rate (int): Speech rate
        """
        self.voice = voice
        self.rate = rate
        self.engine = None
        
        # Check if text-to-speech is available
        if not TEXT_TO_SPEECH_AVAILABLE:
            logger.warning("Text-to-speech not available")
            return
        
        # Initialize text-to-speech
        try:
            self.engine = pyttsx3.init()
            
            # Set properties
            self.engine.setProperty("rate", self.rate)
            
            # Set voice if specified
            if self.voice:
                voices = self.engine.getProperty("voices")
                for v in voices:
                    if self.voice.lower() in v.name.lower():
                        self.engine.setProperty("voice", v.id)
                        logger.info(f"Using voice: {v.name}")
                        break
            
            logger.info("Text-to-speech initialized")
        
        except Exception as e:
            logger.exception(f"Error initializing text-to-speech: {e}")
    
    def speak(self, text: str):
        """
        Speak the given text.
        
        Args:
            text (str): Text to speak
        """
        if not self.engine:
            logger.warning("Text-to-speech not available")
            return False
        
        try:
            logger.info(f"Speaking: {text}")
            self.engine.say(text)
            self.engine.runAndWait()
            return True
        
        except Exception as e:
            logger.exception(f"Error speaking text: {e}")
            return False
    
    def get_available_voices(self) -> List[Dict]:
        """
        Get a list of available voices.
        
        Returns:
            List[Dict]: List of available voices
        """
        if not self.engine:
            logger.warning("Text-to-speech not available")
            return []
        
        try:
            voices = self.engine.getProperty("voices")
            return [{"id": v.id, "name": v.name, "languages": v.languages} for v in voices]
        
        except Exception as e:
            logger.exception(f"Error getting available voices: {e}")
            return []
