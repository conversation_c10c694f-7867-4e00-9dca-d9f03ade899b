"""
Run all tests for the Insurance Lead Agent.

This script runs all the tests for the Insurance Lead Agent.
"""
import os
import sys
import asyncio
import argparse
import subprocess
from typing import Dict, List, Optional, Any

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.logger import setup_logger

# Set up logger
logger = setup_logger("run_all_tests")

async def run_test(test_script: str, args: List[str] = None):
    """
    Run a test script.
    
    Args:
        test_script (str): Test script to run
        args (List[str], optional): Arguments to pass to the script
        
    Returns:
        int: Return code
    """
    logger.info(f"Running test: {test_script}")
    
    # Build command
    command = [sys.executable, test_script]
    
    if args:
        command.extend(args)
    
    # Run command
    process = subprocess.Popen(
        command,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        universal_newlines=True
    )
    
    # Get output
    stdout, stderr = process.communicate()
    
    # Print output
    if stdout:
        logger.info(f"Output:\n{stdout}")
    
    if stderr:
        logger.error(f"Error:\n{stderr}")
    
    # Return code
    return process.returncode

async def run_calendly_test(api_key: str = None, user_uri: str = None, event_type_uri: str = None):
    """
    Run Calendly integration test.
    
    Args:
        api_key (str, optional): Calendly API key
        user_uri (str, optional): Calendly user URI
        event_type_uri (str, optional): Event type URI
        
    Returns:
        int: Return code
    """
    logger.info("Running Calendly integration test...")
    
    # Build arguments
    args = []
    
    if api_key:
        args.extend(["--api-key", api_key])
    
    if user_uri:
        args.extend(["--user-uri", user_uri])
    
    if event_type_uri:
        args.extend(["--event-type-uri", event_type_uri])
    
    # Run test
    return await run_test("tests/test_calendly_integration.py", args)

async def run_facebook_test(app_id: str = None, app_secret: str = None, access_token: str = None, page_id: str = None, recipient_id: str = None):
    """
    Run Facebook integration test.
    
    Args:
        app_id (str, optional): Facebook App ID
        app_secret (str, optional): Facebook App Secret
        access_token (str, optional): Facebook Access Token
        page_id (str, optional): Facebook Page ID
        recipient_id (str, optional): Recipient ID
        
    Returns:
        int: Return code
    """
    logger.info("Running Facebook integration test...")
    
    # Build arguments
    args = []
    
    if app_id:
        args.extend(["--app-id", app_id])
    
    if app_secret:
        args.extend(["--app-secret", app_secret])
    
    if access_token:
        args.extend(["--access-token", access_token])
    
    if page_id:
        args.extend(["--page-id", page_id])
    
    if recipient_id:
        args.extend(["--recipient-id", recipient_id])
    
    # Run test
    return await run_test("tests/test_facebook_integration.py", args)

async def run_instagram_test(app_id: str = None, app_secret: str = None, access_token: str = None, business_account_id: str = None, recipient_id: str = None):
    """
    Run Instagram integration test.
    
    Args:
        app_id (str, optional): Instagram App ID
        app_secret (str, optional): Instagram App Secret
        access_token (str, optional): Instagram Access Token
        business_account_id (str, optional): Instagram Business Account ID
        recipient_id (str, optional): Recipient ID
        
    Returns:
        int: Return code
    """
    logger.info("Running Instagram integration test...")
    
    # Build arguments
    args = []
    
    if app_id:
        args.extend(["--app-id", app_id])
    
    if app_secret:
        args.extend(["--app-secret", app_secret])
    
    if access_token:
        args.extend(["--access-token", access_token])
    
    if business_account_id:
        args.extend(["--business-account-id", business_account_id])
    
    if recipient_id:
        args.extend(["--recipient-id", recipient_id])
    
    # Run test
    return await run_test("tests/test_instagram_integration.py", args)

async def run_tiktok_test(app_id: str = None, app_secret: str = None, access_token: str = None, recipient_id: str = None):
    """
    Run TikTok integration test.
    
    Args:
        app_id (str, optional): TikTok App ID
        app_secret (str, optional): TikTok App Secret
        access_token (str, optional): TikTok Access Token
        recipient_id (str, optional): Recipient ID
        
    Returns:
        int: Return code
    """
    logger.info("Running TikTok integration test...")
    
    # Build arguments
    args = []
    
    if app_id:
        args.extend(["--app-id", app_id])
    
    if app_secret:
        args.extend(["--app-secret", app_secret])
    
    if access_token:
        args.extend(["--access-token", access_token])
    
    if recipient_id:
        args.extend(["--recipient-id", recipient_id])
    
    # Run test
    return await run_test("tests/test_tiktok_integration.py", args)

async def run_website_test(name: str = None, email: str = None, phone: str = None, message: str = None, insurance_type: str = None):
    """
    Run website lead handling test.
    
    Args:
        name (str, optional): Lead name
        email (str, optional): Lead email
        phone (str, optional): Lead phone
        message (str, optional): Lead message
        insurance_type (str, optional): Insurance type
        
    Returns:
        int: Return code
    """
    logger.info("Running website lead handling test...")
    
    # Build arguments
    args = []
    
    if name:
        args.extend(["--name", name])
    
    if email:
        args.extend(["--email", email])
    
    if phone:
        args.extend(["--phone", phone])
    
    if message:
        args.extend(["--message", message])
    
    if insurance_type:
        args.extend(["--insurance-type", insurance_type])
    
    # Run test
    return await run_test("tests/test_website_lead_handling.py", args)

async def run_communication_test(email_recipient: str = None, email_subject: str = None, email_message: str = None, sms_recipient: str = None, sms_message: str = None):
    """
    Run communication test.
    
    Args:
        email_recipient (str, optional): Email recipient
        email_subject (str, optional): Email subject
        email_message (str, optional): Email message
        sms_recipient (str, optional): SMS recipient
        sms_message (str, optional): SMS message
        
    Returns:
        int: Return code
    """
    logger.info("Running communication test...")
    
    # Build arguments
    args = []
    
    if email_recipient:
        args.extend(["--email-recipient", email_recipient])
    
    if email_subject:
        args.extend(["--email-subject", email_subject])
    
    if email_message:
        args.extend(["--email-message", email_message])
    
    if sms_recipient:
        args.extend(["--sms-recipient", sms_recipient])
    
    if sms_message:
        args.extend(["--sms-message", sms_message])
    
    # Run test
    return await run_test("tests/test_communication.py", args)

async def run_monitoring_test(generate_data: bool = False, num_leads: int = 10, days: int = 7):
    """
    Run monitoring and analytics test.
    
    Args:
        generate_data (bool, optional): Generate test data
        num_leads (int, optional): Number of leads to generate per day
        days (int, optional): Number of days to generate data for
        
    Returns:
        int: Return code
    """
    logger.info("Running monitoring and analytics test...")
    
    # Build arguments
    args = []
    
    if generate_data:
        args.append("--generate-data")
    
    args.extend(["--num-leads", str(num_leads)])
    args.extend(["--days", str(days)])
    
    # Run test
    return await run_test("tests/test_monitoring.py", args)

async def main():
    """Run all tests."""
    parser = argparse.ArgumentParser(description="Run all tests for the Insurance Lead Agent")
    parser.add_argument("--test", type=str, choices=["calendly", "facebook", "instagram", "tiktok", "website", "communication", "monitoring", "all"], default="all", help="Test to run")
    parser.add_argument("--generate-data", action="store_true", help="Generate test data for monitoring test")
    args = parser.parse_args()
    
    # Run tests
    if args.test == "calendly" or args.test == "all":
        await run_calendly_test()
    
    if args.test == "facebook" or args.test == "all":
        await run_facebook_test()
    
    if args.test == "instagram" or args.test == "all":
        await run_instagram_test()
    
    if args.test == "tiktok" or args.test == "all":
        await run_tiktok_test()
    
    if args.test == "website" or args.test == "all":
        await run_website_test()
    
    if args.test == "communication" or args.test == "all":
        await run_communication_test()
    
    if args.test == "monitoring" or args.test == "all":
        await run_monitoring_test(args.generate_data)
    
    logger.info("All tests complete")

if __name__ == "__main__":
    asyncio.run(main())
