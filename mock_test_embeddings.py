"""
Mock test for the embeddings functionality.
This script simulates the behavior of the embeddings endpoint without requiring a running server.
This version doesn't require any external dependencies.
"""
import json
import math
import random
from typing import List, Dict, Any

# Mock numpy functionality
class MockNumpy:
    @staticmethod
    def random_normal(mean, std, size):
        return [random.gauss(mean, std) for _ in range(size)]

    @staticmethod
    def array(lst):
        return lst

    @staticmethod
    def linalg_norm(vec):
        return math.sqrt(sum(x*x for x in vec))

    @staticmethod
    def dot(vec1, vec2):
        return sum(a*b for a, b in zip(vec1, vec2))

    @staticmethod
    def mean(vec):
        return sum(vec) / len(vec)

    @staticmethod
    def std(vec):
        mean = sum(vec) / len(vec)
        return math.sqrt(sum((x - mean) ** 2 for x in vec) / len(vec))

    @staticmethod
    def min(vec):
        return min(vec)

    @staticmethod
    def max(vec):
        return max(vec)

# Use our mock numpy
np = MockNumpy()

# Mock data
EMBEDDING_DIMENSIONS = 384  # Typical for sentence-transformers/all-MiniLM-L6-v2

def mock_generate_embedding(text: str) -> List[float]:
    """
    Generate a mock embedding for a text.
    In a real scenario, this would use the actual model.

    Args:
        text (str): Text to embed

    Returns:
        List[float]: Mock embedding vector
    """
    # Use hash of text to generate a deterministic but unique embedding
    # This is just for simulation purposes
    random.seed(hash(text) % 2**32)

    # Generate a random embedding vector
    embedding = np.random_normal(0, 1, EMBEDDING_DIMENSIONS)

    # Normalize the embedding (common practice for embeddings)
    norm = np.linalg_norm(embedding)
    embedding = [x / norm for x in embedding]

    return embedding

def mock_embeddings_endpoint(request: Dict[str, Any]) -> List[List[float]]:
    """
    Mock implementation of the embeddings endpoint.

    Args:
        request (Dict[str, Any]): Request payload

    Returns:
        List[List[float]]: List of embedding vectors
    """
    # Extract inputs
    inputs = request.get("inputs", [])
    if not inputs:
        raise ValueError("No inputs provided")

    # Ensure inputs is a list
    if isinstance(inputs, str):
        inputs = [inputs]

    # Generate embeddings for each input
    results = []
    for text in inputs:
        embedding = mock_generate_embedding(text)
        results.append(embedding)

    return results

def analyze_embeddings(embeddings: List[List[float]], texts: List[str]):
    """
    Analyze the embeddings.

    Args:
        embeddings (List[List[float]]): List of embedding vectors
        texts (List[str]): List of input texts
    """
    print(f"Received {len(embeddings)} embeddings")

    for i, embedding in enumerate(embeddings):
        print(f"Embedding {i+1}:")
        print(f"  Text: {texts[i][:50]}...")
        print(f"  Dimensions: {len(embedding)}")
        print(f"  Mean: {np.mean(embedding):.6f}")
        print(f"  Std: {np.std(embedding):.6f}")
        print(f"  Min: {np.min(embedding):.6f}")
        print(f"  Max: {np.max(embedding):.6f}")
        print()

    # If we have multiple embeddings, calculate similarity
    if len(embeddings) > 1:
        # Embeddings should already be normalized, but just to be sure
        normalized_embeddings = []
        for embedding in embeddings:
            norm = np.linalg_norm(embedding)
            normalized_embeddings.append([x / norm for x in embedding])

        # Calculate cosine similarity
        print("Cosine Similarity Matrix:")
        for i, emb1 in enumerate(normalized_embeddings):
            for j, emb2 in enumerate(normalized_embeddings):
                if i <= j:  # Only print upper triangle
                    similarity = np.dot(emb1, emb2)
                    print(f"  Texts {i+1} and {j+1}: {similarity:.6f}")

def main():
    """Main entry point."""
    # Test texts
    texts = [
        "This is a test sentence about artificial intelligence.",
        "Another completely different text about cats and dogs.",
        "This text is similar to the first one and talks about AI and machine learning."
    ]

    # Create request payload
    request = {
        "inputs": texts
    }

    # Generate mock embeddings
    try:
        embeddings = mock_embeddings_endpoint(request)

        # Analyze embeddings
        analyze_embeddings(embeddings, texts)

        print("\nMock test completed successfully!")
        print("The implementation should work correctly when the environment is properly set up.")

        return True

    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    main()
