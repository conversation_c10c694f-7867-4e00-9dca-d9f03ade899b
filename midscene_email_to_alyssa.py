"""
Midscene Email to Alyssa

This script uses Midscene to control the browser and send an email to <PERSON><PERSON>
with information about insurance options.
"""
import os
import sys
import json
import asyncio
import logging
from typing import Dict, Optional, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("midscene_email_to_alyssa.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("midscene_email_to_alyssa")

# Add parent directory to path to import from core
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Try to import Midscene connector
try:
    from ui_tars.connector.midscene_connector import MidsceneConnector
except ImportError:
    logger.error("Midscene connector not found. Make sure it's installed.")
    sys.exit(1)

# Email configuration
EMAIL_ACCOUNT = "<EMAIL>"
EMAIL_PASSWORD = "GodisSoGood!777"
RECIPIENT_EMAIL = "<EMAIL>"  # Test recipient (change to <PERSON><PERSON>'s email when ready)
EMAIL_SUBJECT = "IUL Policy and Health Insurance Options - Flo Faction Insurance"

# Email body
EMAIL_BODY = """Hello Alyssa,

I hope this email finds you well. My name is Sandra from Flo Faction Insurance, and I'm reaching out regarding your interest in an IUL (Indexed Universal Life) policy structured for maximum cash value growth, plus basic health, dental and vision plans.

With your budget of $100/month, I've put together some options that I believe will work well for you:

1. IUL Policy ($60-70/month): This would be structured for optimal cash value growth while maintaining the life insurance benefit.

2. Basic Health Coverage ($20-25/month): This would cover your essential needs like checkups, physicals, and bloodwork.

3. Dental and Vision ($10-15/month): Combined plan for regular dental cleanings and eye exams.

I'd be happy to walk you through these options in more detail and answer any questions you might have. Please feel free to call or text me at ************, or you can reply to this email.

You can also schedule a time to talk using my calendar link: https://calendly.com/flofaction-insurance/30min

Looking forward to helping you secure the coverage you need!

Best regards,
Sandra
Flo Faction Insurance
************
<EMAIL>
"""

async def send_email_with_midscene() -> Dict[str, Any]:
    """
    Send an email to Alyssa using Midscene browser automation.
    
    Returns:
        Dict[str, Any]: Result of the operation
    """
    logger.info("Starting Midscene email automation")
    
    # Create Midscene connector
    midscene = MidsceneConnector(
        api_url="http://localhost:8081",
        browser_type="chrome"
    )
    
    # Initialize Midscene
    initialized = await midscene.initialize()
    if not initialized:
        logger.error("Failed to initialize Midscene")
        return {"success": False, "error": "Failed to initialize Midscene"}
    
    try:
        # Start browser automation
        result = await midscene.start_browser_automation("https://mail.google.com")
        if not result.get("success", False):
            logger.error(f"Failed to start browser automation: {result.get('error')}")
            return {"success": False, "error": f"Failed to start browser automation: {result.get('error')}"}
        
        logger.info("Browser automation started")
        
        # Check if already logged in
        logger.info("Checking login status")
        result = await midscene.execute_command(
            "Check if I'm already logged in to Gmail. If not, click 'Sign in'"
        )
        
        # Enter email
        logger.info(f"Entering email: {EMAIL_ACCOUNT}")
        result = await midscene.execute_command(
            f"Enter '{EMAIL_ACCOUNT}' in the email field and click Next"
        )
        
        # Enter password
        logger.info("Entering password")
        result = await midscene.execute_command(
            f"Enter '{EMAIL_PASSWORD}' in the password field and click Next"
        )
        
        # Wait for Gmail to load
        logger.info("Waiting for Gmail to load")
        await asyncio.sleep(5)
        
        # Click Compose
        logger.info("Clicking Compose button")
        result = await midscene.execute_command(
            "Click the Compose button"
        )
        
        # Fill in recipient
        logger.info(f"Entering recipient: {RECIPIENT_EMAIL}")
        result = await midscene.execute_command(
            f"Enter '{RECIPIENT_EMAIL}' in the To field"
        )
        
        # Fill in subject
        logger.info(f"Entering subject: {EMAIL_SUBJECT}")
        result = await midscene.execute_command(
            f"Enter '{EMAIL_SUBJECT}' in the Subject field"
        )
        
        # Fill in body
        logger.info("Entering email body")
        result = await midscene.execute_command(
            f"Enter the following in the email body: {EMAIL_BODY}"
        )
        
        # Send the email
        logger.info("Sending email")
        result = await midscene.execute_command(
            "Click the Send button"
        )
        
        # Verify the email was sent
        logger.info("Verifying email was sent")
        result = await midscene.execute_command(
            "Check if the email was sent successfully"
        )
        
        logger.info("Email sent successfully")
        return {"success": True, "message": "Email sent successfully"}
        
    except Exception as e:
        logger.exception(f"Error sending email: {e}")
        return {"success": False, "error": str(e)}
    
    finally:
        # Stop Midscene
        logger.info("Stopping Midscene")
        await midscene.stop()

async def main():
    """Main function."""
    print("Starting Midscene Email to Alyssa")
    print("=================================")
    print()
    
    result = await send_email_with_midscene()
    
    if result.get("success"):
        print("✅ Email sent successfully!")
    else:
        print(f"❌ Failed to send email: {result.get('error')}")
    
    print()
    print("Process completed")

if __name__ == "__main__":
    asyncio.run(main())
