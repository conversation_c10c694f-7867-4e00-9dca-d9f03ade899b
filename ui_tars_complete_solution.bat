@echo off
echo UI-TARS Complete Solution
echo =======================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Check if UI-TARS is installed
if not exist "C:\Users\<USER>\AppData\Local\UI-TARS\UI-TARS.exe" (
    echo UI-TARS is not installed. Please install UI-TARS 1.5.
    exit /b 1
)

echo Step 1: Killing any existing UI-TARS and browser processes...
taskkill /F /IM UI-TARS.exe 2>nul
taskkill /F /IM chrome.exe 2>nul
taskkill /F /IM msedge.exe 2>nul
timeout /t 2 >nul

echo Step 2: Creating UI-TARS configuration directory...
mkdir config 2>nul

echo Step 3: Creating UI-TARS browser data directory...
mkdir "C:\Users\<USER>\AppData\Local\UI-TARS\browser_data" 2>nul

echo Step 4: Creating optimized UI-TARS configuration...
echo {> config\ui_tars_config.json
echo   "ui_tars": {>> config\ui_tars_config.json
echo     "version": "1.5",>> config\ui_tars_config.json
echo     "enabled": true,>> config\ui_tars_config.json
echo     "browser": {>> config\ui_tars_config.json
echo       "type": "chrome",>> config\ui_tars_config.json
echo       "executable_path": "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",>> config\ui_tars_config.json
echo       "user_data_dir": "C:\\Users\\<USER>\\AppData\\Local\\UI-TARS\\browser_data",>> config\ui_tars_config.json
echo       "profile_directory": "Default",>> config\ui_tars_config.json
echo       "remote_debugging_port": 9222,>> config\ui_tars_config.json
echo       "detection": {>> config\ui_tars_config.json
echo         "auto_detect": true,>> config\ui_tars_config.json
echo         "fallback_types": ["chrome", "edge", "firefox", "brave"]>> config\ui_tars_config.json
echo       }>> config\ui_tars_config.json
echo     },>> config\ui_tars_config.json
echo     "api": {>> config\ui_tars_config.json
echo       "host": "localhost",>> config\ui_tars_config.json
echo       "port": 8080,>> config\ui_tars_config.json
echo       "timeout": 30,>> config\ui_tars_config.json
echo       "retry_attempts": 3>> config\ui_tars_config.json
echo     },>> config\ui_tars_config.json
echo     "debug": {>> config\ui_tars_config.json
echo       "enabled": true,>> config\ui_tars_config.json
echo       "log_level": "debug",>> config\ui_tars_config.json
echo       "log_file": "ui_tars_debug.log">> config\ui_tars_config.json
echo     },>> config\ui_tars_config.json
echo     "sandbox": {>> config\ui_tars_config.json
echo       "enabled": true,>> config\ui_tars_config.json
echo       "isolation_level": "high">> config\ui_tars_config.json
echo     },>> config\ui_tars_config.json
echo     "virtual_pc": {>> config\ui_tars_config.json
echo       "enabled": true,>> config\ui_tars_config.json
echo       "memory_mb": 2048,>> config\ui_tars_config.json
echo       "cpu_cores": 2>> config\ui_tars_config.json
echo     },>> config\ui_tars_config.json
echo     "dpo": {>> config\ui_tars_config.json
echo       "enabled": true,>> config\ui_tars_config.json
echo       "preference_model": "default">> config\ui_tars_config.json
echo     }>> config\ui_tars_config.json
echo   }>> config\ui_tars_config.json
echo }>> config\ui_tars_config.json

echo Step 5: Starting Chrome with remote debugging...
start "" "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222 --user-data-dir="C:\Users\<USER>\AppData\Local\UI-TARS\browser_data" --no-first-run --no-default-browser-check --disable-extensions --disable-component-extensions-with-background-pages --disable-background-networking --disable-client-side-phishing-detection --disable-sync --metrics-recording-only --disable-default-apps --no-default-browser-check --no-first-run --disable-backgrounding-occluded-windows --disable-renderer-backgrounding --disable-background-timer-throttling about:blank

echo Waiting for Chrome to start...
timeout /t 5 >nul

echo Step 6: Starting UI-TARS with enhanced configuration...
start "" "C:\Users\<USER>\AppData\Local\UI-TARS\UI-TARS.exe" --config "%CD%\config\ui_tars_config.json" --debug

echo Waiting for UI-TARS to start...
timeout /t 5 >nul

echo.
echo UI-TARS has been started with enhanced configuration.
echo.
echo Important Notes:
echo - Keep the Chrome window open while using UI-TARS
echo - If UI-TARS doesn't connect to the browser, try restarting this script
echo - Check ui_tars_debug.log for detailed error information
echo.
echo Press any key to exit...
pause >nul
