"""
Trading Agent for managing trading and investment operations.
"""
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import json
import re
import random

from agents.base_agent import BaseAgent
from core.logger import setup_logger
from llm.llm_router import LLMRouter
from services.trading_service import TradingServiceFactory

class TradingAgent(BaseAgent):
    """
    Agent specialized for trading and investment operations.

    This agent handles tasks related to trading stocks, cryptocurrencies,
    and other assets, including market analysis, trade execution, and
    portfolio management.
    """

    def __init__(
        self,
        agent_id: str,
        config: Dict,
        state_manager,
        message_queue,
        shutdown_event
    ):
        """Initialize the trading agent."""
        super().__init__(agent_id, config, state_manager, message_queue, shutdown_event)

        # Trading-specific configuration
        self.llm_provider = config.get("llm_provider", "anthropic")
        self.llm_router = None

        # Trading services
        self.stock_service = None
        self.crypto_service = None

        # Trading data
        self.watchlist = {}
        self.portfolio = {}
        self.trade_history = {}
        self.strategies = {}

        # Agent capabilities
        self.capabilities = [
            "market_analysis",
            "trade_execution",
            "portfolio_management",
            "strategy_development",
            "risk_management",
            "algorithmic_trading",
            "sentiment_analysis",
            "technical_analysis",
            "fundamental_analysis",
            "options_trading",
            "futures_trading",
            "crypto_trading",
            "forex_trading",
            "high_frequency_trading",
            "quantitative_analysis",
            "machine_learning_predictions",
            "quantum_computing_optimization",
            "secure_multi_party_computation",
            "advanced_risk_modeling",
            "market_simulation",
            "backtesting",
            "portfolio_optimization",
            "automated_trading",
            "trading_bot_management",
            "regulatory_compliance",
            "tax_optimization",
            "trading_report_generation",
            "alert_management",
            "trading_api_integration",
            "exchange_connectivity",
        ]

    async def initialize(self):
        """Initialize the trading agent."""
        await super().initialize()

        # Initialize LLM router
        self.llm_router = LLMRouter()
        await self.llm_router.initialize()

        # Initialize trading services
        self.stock_service = TradingServiceFactory.create_service("alpaca")
        self.crypto_service = TradingServiceFactory.create_service("crypto")

        # Load trading data
        await self._load_trading_data()

        self.logger.info(f"Trading agent initialized with provider: {self.llm_provider}")

    async def _load_trading_data(self):
        """Load trading data from state manager."""
        # Load watchlist
        watchlist_data = await self.state_manager.get_state("trading", "watchlist")
        if watchlist_data:
            self.watchlist = watchlist_data
            self.logger.info(f"Loaded watchlist with {len(self.watchlist)} symbols")

        # Load portfolio
        portfolio_data = await self.state_manager.get_state("trading", "portfolio")
        if portfolio_data:
            self.portfolio = portfolio_data
            self.logger.info(f"Loaded portfolio with {len(self.portfolio)} positions")

        # Load trade history
        trade_history_data = await self.state_manager.get_state("trading", "trade_history")
        if trade_history_data:
            self.trade_history = trade_history_data
            self.logger.info(f"Loaded trade history with {len(self.trade_history)} trades")

        # Load strategies
        strategies_data = await self.state_manager.get_state("trading", "strategies")
        if strategies_data:
            self.strategies = strategies_data
            self.logger.info(f"Loaded {len(self.strategies)} trading strategies")

    async def execute_cycle(self):
        """Execute one cycle of the trading agent's logic."""
        self.logger.debug("Executing trading agent cycle")

        try:
            # Check for pending tasks
            pending_tasks = await self.state_manager.get_state("trading", "pending_tasks")
            if pending_tasks:
                for task_id, task in pending_tasks.items():
                    if task.get("status") == "pending":
                        await self._process_task(task_id, task)

            # Update market data for watchlist
            await self._update_watchlist()

            # Check for trading signals
            await self._check_trading_signals()

            # Update portfolio status
            await self._update_portfolio()

            # Update state with any changes
            await self._save_trading_data()

        except Exception as e:
            self.logger.exception(f"Error in trading agent cycle: {e}")

    async def _process_task(self, task_id: str, task: Dict):
        """
        Process a pending task.

        Args:
            task_id (str): Task identifier
            task (Dict): Task data
        """
        task_type = task.get("type")
        self.logger.info(f"Processing task: {task_id} ({task_type})")

        try:
            if task_type == "market_analysis":
                await self._handle_market_analysis(task)
            elif task_type == "trade_execution":
                await self._handle_trade_execution(task)
            elif task_type == "portfolio_update":
                await self._handle_portfolio_update(task)
            elif task_type == "strategy_development":
                await self._handle_strategy_development(task)
            else:
                self.logger.warning(f"Unknown task type: {task_type}")
                return

            # Update task status
            task["status"] = "completed"
            task["completed_at"] = datetime.now().isoformat()

            # Update pending tasks
            pending_tasks = await self.state_manager.get_state("trading", "pending_tasks") or {}
            pending_tasks[task_id] = task
            await self.state_manager.update_state("trading", "pending_tasks", pending_tasks)

        except Exception as e:
            self.logger.exception(f"Error processing task {task_id}: {e}")

            # Update task status
            task["status"] = "error"
            task["error"] = str(e)

            # Update pending tasks
            pending_tasks = await self.state_manager.get_state("trading", "pending_tasks") or {}
            pending_tasks[task_id] = task
            await self.state_manager.update_state("trading", "pending_tasks", pending_tasks)

    async def _handle_market_analysis(self, task: Dict):
        """
        Handle a market analysis task.

        Args:
            task (Dict): Task data
        """
        symbol = task.get("symbol")
        asset_type = task.get("asset_type", "stock")

        # Get market data
        if asset_type == "stock":
            if self.stock_service and self.stock_service.is_enabled():
                market_data = await self.stock_service.get_market_data(symbol)
            else:
                raise ValueError("Stock trading service not available")
        elif asset_type == "crypto":
            if self.crypto_service and self.crypto_service.is_enabled():
                market_data = await self.crypto_service.get_market_data(symbol)
            else:
                raise ValueError("Crypto trading service not available")
        else:
            raise ValueError(f"Unsupported asset type: {asset_type}")

        # Generate analysis using LLM
        prompt = f"""
        You are a trading assistant. Please analyze the following market data for {symbol}:

        {json.dumps(market_data, indent=2)}

        Provide a concise analysis including:
        1. Current price and recent price action
        2. Key support and resistance levels
        3. Technical indicators (if available)
        4. Market sentiment
        5. Trading recommendation (buy, sell, or hold)

        Keep your analysis factual and data-driven.
        """

        response = await self.llm_router.generate_text(
            prompt=prompt,
            provider=self.llm_provider,
            max_tokens=800,
            temperature=0.7
        )

        # Store analysis in task
        task["analysis"] = response.get("text")
        task["market_data"] = market_data

        # Update watchlist with latest data
        self.watchlist[symbol] = {
            "symbol": symbol,
            "asset_type": asset_type,
            "last_price": self._extract_price(market_data),
            "last_updated": datetime.now().isoformat(),
            "analysis": response.get("text"),
        }

    async def _handle_trade_execution(self, task: Dict):
        """
        Handle a trade execution task.

        Args:
            task (Dict): Task data
        """
        symbol = task.get("symbol")
        side = task.get("side")  # buy or sell
        quantity = task.get("quantity")
        order_type = task.get("order_type", "market")
        price = task.get("price")  # for limit orders
        asset_type = task.get("asset_type", "stock")

        # Validate parameters
        if not symbol or not side or not quantity:
            raise ValueError("Missing required parameters for trade execution")

        if side not in ["buy", "sell"]:
            raise ValueError(f"Invalid order side: {side}")

        # Execute trade
        if asset_type == "stock":
            if self.stock_service and self.stock_service.is_enabled():
                order_result = await self.stock_service.place_order(
                    symbol=symbol,
                    side=side,
                    quantity=quantity,
                    order_type=order_type,
                    price=price,
                )
            else:
                raise ValueError("Stock trading service not available")
        elif asset_type == "crypto":
            if self.crypto_service and self.crypto_service.is_enabled():
                order_result = await self.crypto_service.place_order(
                    symbol=symbol,
                    side=side,
                    quantity=quantity,
                    order_type=order_type,
                    price=price,
                )
            else:
                raise ValueError("Crypto trading service not available")
        else:
            raise ValueError(f"Unsupported asset type: {asset_type}")

        # Store order result in task
        task["order_result"] = order_result

        # Add to trade history
        trade_id = f"TRADE-{datetime.now().strftime('%Y%m%d')}-{len(self.trade_history) + 1:04d}"

        self.trade_history[trade_id] = {
            "trade_id": trade_id,
            "symbol": symbol,
            "asset_type": asset_type,
            "side": side,
            "quantity": quantity,
            "order_type": order_type,
            "price": price,
            "executed_at": datetime.now().isoformat(),
            "order_id": order_result.get("id"),
            "status": order_result.get("status", "unknown"),
        }

        # Update portfolio
        await self._update_portfolio_after_trade(symbol, asset_type, side, quantity, price)

    async def _handle_portfolio_update(self, task: Dict):
        """
        Handle a portfolio update task.

        Args:
            task (Dict): Task data
        """
        # Get account information
        if self.stock_service and self.stock_service.is_enabled():
            stock_account = await self.stock_service.get_account_info()
        else:
            stock_account = {"error": "Stock trading service not available"}

        if self.crypto_service and self.crypto_service.is_enabled():
            crypto_account = await self.crypto_service.get_account_info()
        else:
            crypto_account = {"error": "Crypto trading service not available"}

        # Update portfolio with account information
        self.portfolio["last_updated"] = datetime.now().isoformat()
        self.portfolio["stock_account"] = stock_account
        self.portfolio["crypto_account"] = crypto_account

        # Store account information in task
        task["stock_account"] = stock_account
        task["crypto_account"] = crypto_account

    async def _handle_strategy_development(self, task: Dict):
        """
        Handle a strategy development task.

        Args:
            task (Dict): Task data
        """
        strategy_name = task.get("strategy_name")
        strategy_type = task.get("strategy_type")
        parameters = task.get("parameters", {})
        description = task.get("description", "")

        # Create or update strategy
        self.strategies[strategy_name] = {
            "name": strategy_name,
            "type": strategy_type,
            "parameters": parameters,
            "description": description,
            "created_at": datetime.now().isoformat(),
            "status": "active",
        }

        # Generate strategy documentation using LLM
        prompt = f"""
        You are a trading strategy developer. Please create documentation for the following trading strategy:

        Name: {strategy_name}
        Type: {strategy_type}
        Parameters: {json.dumps(parameters, indent=2)}
        Description: {description}

        Include the following sections:
        1. Overview
        2. Strategy Logic
        3. Entry and Exit Criteria
        4. Risk Management
        5. Expected Performance
        6. Implementation Notes

        Keep the documentation clear, concise, and actionable.
        """

        response = await self.llm_router.generate_text(
            prompt=prompt,
            provider=self.llm_provider,
            max_tokens=1000,
            temperature=0.7
        )

        # Store documentation in strategy
        self.strategies[strategy_name]["documentation"] = response.get("text")

        # Store documentation in task
        task["documentation"] = response.get("text")

    async def _update_watchlist(self):
        """Update market data for symbols in the watchlist."""
        for symbol, data in self.watchlist.items():
            # Skip if updated recently (within last 15 minutes)
            if data.get("last_updated"):
                last_updated = datetime.fromisoformat(data["last_updated"])
                if (datetime.now() - last_updated) < timedelta(minutes=15):
                    continue

            try:
                asset_type = data.get("asset_type", "stock")

                # Get market data
                if asset_type == "stock" and self.stock_service and self.stock_service.is_enabled():
                    market_data = await self.stock_service.get_market_data(symbol)
                elif asset_type == "crypto" and self.crypto_service and self.crypto_service.is_enabled():
                    market_data = await self.crypto_service.get_market_data(symbol)
                else:
                    continue

                # Update watchlist entry
                self.watchlist[symbol].update({
                    "last_price": self._extract_price(market_data),
                    "last_updated": datetime.now().isoformat(),
                    "market_data": market_data,
                })

                self.logger.debug(f"Updated market data for {symbol}")

            except Exception as e:
                self.logger.error(f"Error updating market data for {symbol}: {e}")

    async def _check_trading_signals(self):
        """Check for trading signals based on strategies and market data."""
        for strategy_name, strategy in self.strategies.items():
            if strategy.get("status") != "active":
                continue

            try:
                # Apply strategy to watchlist
                for symbol, data in self.watchlist.items():
                    signal = await self._apply_strategy(strategy, symbol, data)

                    if signal:
                        self.logger.info(f"Trading signal: {signal['action']} {symbol} ({strategy_name})")

                        # Create trade execution task
                        await self._create_trade_task(symbol, signal)

            except Exception as e:
                self.logger.error(f"Error applying strategy {strategy_name}: {e}")

    async def _apply_strategy(self, strategy: Dict, symbol: str, data: Dict) -> Optional[Dict]:
        """
        Apply a trading strategy to a symbol.

        Args:
            strategy (Dict): Strategy data
            symbol (str): Trading symbol
            data (Dict): Symbol data from watchlist

        Returns:
            Optional[Dict]: Trading signal if generated, None otherwise
        """
        # This is a simplified implementation
        # In a real system, this would involve more complex logic

        # For demonstration purposes, generate random signals
        if random.random() < 0.05:  # 5% chance of generating a signal
            action = random.choice(["buy", "sell"])
            quantity = random.uniform(0.1, 1.0)
            price = data.get("last_price")

            return {
                "action": action,
                "quantity": quantity,
                "price": price,
                "strategy": strategy["name"],
                "timestamp": datetime.now().isoformat(),
            }

        return None

    async def _create_trade_task(self, symbol: str, signal: Dict):
        """
        Create a trade execution task based on a signal.

        Args:
            symbol (str): Trading symbol
            signal (Dict): Trading signal
        """
        task_id = f"TASK-{datetime.now().strftime('%Y%m%d%H%M%S')}"

        task = {
            "task_id": task_id,
            "type": "trade_execution",
            "symbol": symbol,
            "side": signal["action"],
            "quantity": signal["quantity"],
            "price": signal["price"],
            "order_type": "market",
            "asset_type": self.watchlist[symbol].get("asset_type", "stock"),
            "strategy": signal["strategy"],
            "created_at": datetime.now().isoformat(),
            "status": "pending",
        }

        # Add task to pending tasks
        pending_tasks = await self.state_manager.get_state("trading", "pending_tasks") or {}
        pending_tasks[task_id] = task
        await self.state_manager.update_state("trading", "pending_tasks", pending_tasks)

    async def _update_portfolio(self):
        """Update portfolio status."""
        # Check if portfolio was updated recently (within last hour)
        if self.portfolio.get("last_updated"):
            last_updated = datetime.fromisoformat(self.portfolio["last_updated"])
            if (datetime.now() - last_updated) < timedelta(hours=1):
                return

        # Create portfolio update task
        task_id = f"TASK-PORTFOLIO-{datetime.now().strftime('%Y%m%d%H%M%S')}"

        task = {
            "task_id": task_id,
            "type": "portfolio_update",
            "created_at": datetime.now().isoformat(),
            "status": "pending",
        }

        # Add task to pending tasks
        pending_tasks = await self.state_manager.get_state("trading", "pending_tasks") or {}
        pending_tasks[task_id] = task
        await self.state_manager.update_state("trading", "pending_tasks", pending_tasks)

    async def _update_portfolio_after_trade(self, symbol: str, asset_type: str, side: str, quantity: float, price: Optional[float]):
        """
        Update portfolio after a trade.

        Args:
            symbol (str): Trading symbol
            asset_type (str): Asset type (stock/crypto)
            side (str): Order side (buy/sell)
            quantity (float): Order quantity
            price (Optional[float]): Order price
        """
        # This is a simplified implementation
        # In a real system, this would involve more complex logic

        # Initialize positions if not exists
        if "positions" not in self.portfolio:
            self.portfolio["positions"] = {}

        # Get current position
        position_key = f"{asset_type}:{symbol}"
        position = self.portfolio["positions"].get(position_key, {
            "symbol": symbol,
            "asset_type": asset_type,
            "quantity": 0,
            "cost_basis": 0,
        })

        # Update position
        if side == "buy":
            # Calculate new cost basis
            current_value = position["quantity"] * position["cost_basis"] if position["quantity"] > 0 else 0
            new_value = quantity * (price or 0)
            new_quantity = position["quantity"] + quantity
            new_cost_basis = (current_value + new_value) / new_quantity if new_quantity > 0 else 0

            position["quantity"] = new_quantity
            position["cost_basis"] = new_cost_basis
            position["last_updated"] = datetime.now().isoformat()

        elif side == "sell":
            # Update quantity
            position["quantity"] = max(0, position["quantity"] - quantity)
            position["last_updated"] = datetime.now().isoformat()

            # Remove position if quantity is zero
            if position["quantity"] == 0:
                if position_key in self.portfolio["positions"]:
                    del self.portfolio["positions"][position_key]
                return

        # Update position in portfolio
        self.portfolio["positions"][position_key] = position

    async def _save_trading_data(self):
        """Save trading data to state manager."""
        # Save watchlist
        await self.state_manager.update_state("trading", "watchlist", self.watchlist)

        # Save portfolio
        await self.state_manager.update_state("trading", "portfolio", self.portfolio)

        # Save trade history
        await self.state_manager.update_state("trading", "trade_history", self.trade_history)

        # Save strategies
        await self.state_manager.update_state("trading", "strategies", self.strategies)

    async def run_algorithmic_trading(self, strategy_id: str, parameters: Dict = None) -> Dict:
        """
        Run algorithmic trading using a specific strategy.

        Args:
            strategy_id (str): Strategy identifier
            parameters (Dict, optional): Strategy parameters

        Returns:
            Dict: Trading results
        """
        self.logger.info(f"Running algorithmic trading with strategy: {strategy_id}")

        # Get strategy
        if strategy_id not in self.strategies:
            raise ValueError(f"Strategy not found: {strategy_id}")

        strategy = self.strategies[strategy_id]

        # Merge parameters
        if parameters:
            strategy_params = strategy.get("parameters", {}).copy()
            strategy_params.update(parameters)
        else:
            strategy_params = strategy.get("parameters", {})

        # Get symbols to trade
        symbols = strategy_params.get("symbols", [])
        if not symbols:
            # Use watchlist if no symbols specified
            symbols = list(self.watchlist.keys())

        # Initialize results
        results = {
            "strategy_id": strategy_id,
            "timestamp": datetime.now().isoformat(),
            "trades": [],
            "summary": {
                "total_trades": 0,
                "successful_trades": 0,
                "failed_trades": 0,
                "total_profit_loss": 0.0,
            }
        }

        # Apply strategy to each symbol
        for symbol in symbols:
            try:
                # Get market data
                if symbol in self.watchlist:
                    data = self.watchlist[symbol]
                    asset_type = data.get("asset_type", "stock")
                else:
                    # Skip if not in watchlist
                    continue

                # Apply strategy
                signal = await self._apply_strategy(strategy, symbol, data)

                if signal:
                    # Execute trade
                    trade_result = await self._execute_algorithmic_trade(symbol, asset_type, signal)

                    # Add to results
                    results["trades"].append(trade_result)
                    results["summary"]["total_trades"] += 1

                    if trade_result.get("status") == "executed":
                        results["summary"]["successful_trades"] += 1
                        results["summary"]["total_profit_loss"] += trade_result.get("profit_loss", 0.0)
                    else:
                        results["summary"]["failed_trades"] += 1

            except Exception as e:
                self.logger.error(f"Error applying strategy to {symbol}: {e}")

        return results

    async def _execute_algorithmic_trade(self, symbol: str, asset_type: str, signal: Dict) -> Dict:
        """
        Execute a trade based on algorithmic signal.

        Args:
            symbol (str): Trading symbol
            asset_type (str): Asset type (stock, crypto, etc.)
            signal (Dict): Trading signal

        Returns:
            Dict: Trade result
        """
        try:
            # Get trading service
            if asset_type == "stock":
                if self.stock_service and self.stock_service.is_enabled():
                    trading_service = self.stock_service
                else:
                    raise ValueError("Stock trading service not available")
            elif asset_type == "crypto":
                if self.crypto_service and self.crypto_service.is_enabled():
                    trading_service = self.crypto_service
                else:
                    raise ValueError("Crypto trading service not available")
            else:
                raise ValueError(f"Unsupported asset type: {asset_type}")

            # Extract trade parameters
            side = signal.get("action")  # buy or sell
            quantity = signal.get("quantity")
            price = signal.get("price")
            order_type = signal.get("order_type", "market")

            # Execute trade
            order_result = await trading_service.place_order(
                symbol=symbol,
                side=side,
                quantity=quantity,
                order_type=order_type,
                price=price,
            )

            # Create trade record
            trade_id = f"ALGO-{datetime.now().strftime('%Y%m%d%H%M%S')}"

            trade_record = {
                "trade_id": trade_id,
                "symbol": symbol,
                "asset_type": asset_type,
                "side": side,
                "quantity": quantity,
                "order_type": order_type,
                "price": price,
                "executed_at": datetime.now().isoformat(),
                "order_id": order_result.get("id"),
                "status": "executed",
                "strategy": signal.get("strategy"),
                "signal": signal,
            }

            # Add to trade history
            self.trade_history[trade_id] = trade_record

            # Update portfolio
            await self._update_portfolio_after_trade(symbol, asset_type, side, quantity, price)

            return trade_record

        except Exception as e:
            self.logger.exception(f"Error executing algorithmic trade: {e}")

            # Create failed trade record
            trade_id = f"ALGO-FAILED-{datetime.now().strftime('%Y%m%d%H%M%S')}"

            trade_record = {
                "trade_id": trade_id,
                "symbol": symbol,
                "asset_type": asset_type,
                "side": signal.get("action"),
                "quantity": signal.get("quantity"),
                "price": signal.get("price"),
                "executed_at": datetime.now().isoformat(),
                "status": "failed",
                "error": str(e),
                "strategy": signal.get("strategy"),
                "signal": signal,
            }

            # Add to trade history
            self.trade_history[trade_id] = trade_record

            return trade_record

    async def run_sentiment_analysis(self, symbols: List[str] = None) -> Dict:
        """
        Run sentiment analysis on market data and news.

        Args:
            symbols (List[str], optional): Symbols to analyze

        Returns:
            Dict: Sentiment analysis results
        """
        self.logger.info("Running sentiment analysis")

        # Use all symbols in watchlist if none specified
        if not symbols:
            symbols = list(self.watchlist.keys())

        # Get advanced reasoning service
        advanced_reasoning = self.get_service("advanced_reasoning")
        if not advanced_reasoning:
            raise ValueError("Advanced reasoning service not available")

        # Initialize results
        results = {
            "timestamp": datetime.now().isoformat(),
            "symbols": {},
            "market_sentiment": None,
        }

        # Analyze each symbol
        for symbol in symbols:
            try:
                # Get market data
                if symbol in self.watchlist:
                    data = self.watchlist[symbol]

                    # Get news and social media data (placeholder)
                    news_data = f"Recent news for {symbol}: [This would be actual news data in a real implementation]"
                    social_data = f"Social media sentiment for {symbol}: [This would be actual social media data in a real implementation]"

                    # Combine data for analysis
                    context = f"""
                    Symbol: {symbol}
                    Current Price: {data.get('last_price')}
                    Last Updated: {data.get('last_updated')}

                    Market Data:
                    {json.dumps(data.get('market_data', {}), indent=2)}

                    News Data:
                    {news_data}

                    Social Media Data:
                    {social_data}
                    """

                    # Perform causal reasoning for sentiment analysis
                    sentiment_result = await advanced_reasoning.causal_reasoning(
                        context=context,
                        question=f"What is the current market sentiment for {symbol} based on price action, news, and social media?",
                        variables=["price action", "news sentiment", "social media sentiment", "trading volume", "market conditions"]
                    )

                    # Extract sentiment
                    sentiment = self._extract_sentiment(sentiment_result.get("reasoning", ""))

                    # Store result
                    results["symbols"][symbol] = {
                        "sentiment": sentiment,
                        "reasoning": sentiment_result.get("reasoning"),
                        "confidence": sentiment.get("confidence", 0.0),
                    }

                    # Update watchlist with sentiment
                    self.watchlist[symbol]["sentiment"] = sentiment

            except Exception as e:
                self.logger.error(f"Error analyzing sentiment for {symbol}: {e}")
                results["symbols"][symbol] = {
                    "error": str(e)
                }

        # Calculate overall market sentiment
        if results["symbols"]:
            bullish_count = sum(1 for s in results["symbols"].values() if s.get("sentiment", {}).get("direction") == "bullish")
            bearish_count = sum(1 for s in results["symbols"].values() if s.get("sentiment", {}).get("direction") == "bearish")
            neutral_count = sum(1 for s in results["symbols"].values() if s.get("sentiment", {}).get("direction") == "neutral")

            total = len(results["symbols"])

            if bullish_count > bearish_count and bullish_count > neutral_count:
                market_direction = "bullish"
                strength = bullish_count / total
            elif bearish_count > bullish_count and bearish_count > neutral_count:
                market_direction = "bearish"
                strength = bearish_count / total
            else:
                market_direction = "neutral"
                strength = neutral_count / total

            results["market_sentiment"] = {
                "direction": market_direction,
                "strength": strength,
                "bullish_count": bullish_count,
                "bearish_count": bearish_count,
                "neutral_count": neutral_count,
                "total_symbols": total,
            }

        return results

    def _extract_sentiment(self, reasoning: str) -> Dict:
        """
        Extract sentiment from reasoning text.

        Args:
            reasoning (str): Reasoning text

        Returns:
            Dict: Sentiment information
        """
        # This is a simplified implementation
        # In a real system, this would use NLP to extract sentiment

        # Count sentiment words
        bullish_words = ["bullish", "positive", "uptrend", "buy", "growth", "increasing", "optimistic"]
        bearish_words = ["bearish", "negative", "downtrend", "sell", "decline", "decreasing", "pessimistic"]

        bullish_count = sum(reasoning.lower().count(word) for word in bullish_words)
        bearish_count = sum(reasoning.lower().count(word) for word in bearish_words)

        # Determine sentiment direction
        if bullish_count > bearish_count:
            direction = "bullish"
            strength = min(1.0, bullish_count / (bullish_count + bearish_count + 1))
        elif bearish_count > bullish_count:
            direction = "bearish"
            strength = min(1.0, bearish_count / (bullish_count + bearish_count + 1))
        else:
            direction = "neutral"
            strength = 0.5

        return {
            "direction": direction,
            "strength": strength,
            "confidence": 0.7,  # Placeholder
            "bullish_count": bullish_count,
            "bearish_count": bearish_count,
        }

    async def run_quantum_optimization(self, portfolio_id: str = None) -> Dict:
        """
        Run quantum computing optimization for portfolio allocation.

        Args:
            portfolio_id (str, optional): Portfolio identifier

        Returns:
            Dict: Optimization results
        """
        self.logger.info(f"Running quantum portfolio optimization for portfolio: {portfolio_id}")

        # Get quantum connector
        quantum_connector = self.get_service("quantum_connector")
        if not quantum_connector:
            raise ValueError("Quantum connector not available")

        # Get portfolio data
        if portfolio_id:
            # Get specific portfolio
            portfolio_data = self.portfolio.get(portfolio_id)
            if not portfolio_data:
                raise ValueError(f"Portfolio not found: {portfolio_id}")

            portfolios = {portfolio_id: portfolio_data}
        else:
            # Use all portfolios
            portfolios = self.portfolio

        # Initialize results
        results = {
            "timestamp": datetime.now().isoformat(),
            "portfolios": {},
        }

        # Optimize each portfolio
        for p_id, p_data in portfolios.items():
            try:
                # Extract assets and weights
                assets = []
                weights = []

                for asset, asset_data in p_data.get("assets", {}).items():
                    assets.append(asset)
                    weights.append(asset_data.get("weight", 0.0))

                # Skip if no assets
                if not assets:
                    continue

                # Prepare optimization parameters
                parameters = {
                    "assets": assets,
                    "current_weights": weights,
                    "risk_tolerance": p_data.get("risk_tolerance", 0.5),
                    "expected_returns": [self._get_expected_return(asset) for asset in assets],
                    "covariance_matrix": self._generate_covariance_matrix(assets),
                }

                # Run quantum optimization algorithm
                optimization_result = await quantum_connector.run_quantum_algorithm(
                    algorithm="portfolio_optimization",
                    parameters=parameters,
                    shots=1024
                )

                # Store result
                results["portfolios"][p_id] = {
                    "original_weights": weights,
                    "optimized_weights": optimization_result.get("optimized_weights", []),
                    "expected_return": optimization_result.get("expected_return", 0.0),
                    "expected_risk": optimization_result.get("expected_risk", 0.0),
                    "sharpe_ratio": optimization_result.get("sharpe_ratio", 0.0),
                }

                # Update portfolio with optimized weights
                if "optimized_weights" in optimization_result:
                    for i, asset in enumerate(assets):
                        if i < len(optimization_result["optimized_weights"]):
                            p_data["assets"][asset]["weight"] = optimization_result["optimized_weights"][i]
                            p_data["assets"][asset]["optimized"] = True

                # Update portfolio metadata
                p_data["last_optimized"] = datetime.now().isoformat()
                p_data["optimization_method"] = "quantum"

                # Save updated portfolio
                self.portfolio[p_id] = p_data

            except Exception as e:
                self.logger.error(f"Error optimizing portfolio {p_id}: {e}")
                results["portfolios"][p_id] = {
                    "error": str(e)
                }

        return results

    def _get_expected_return(self, asset: str) -> float:
        """
        Get expected return for an asset.

        Args:
            asset (str): Asset symbol

        Returns:
            float: Expected return
        """
        # This is a simplified implementation
        # In a real system, this would use historical data and models

        if asset in self.watchlist:
            # Use sentiment if available
            sentiment = self.watchlist[asset].get("sentiment", {})
            if sentiment:
                direction = sentiment.get("direction")
                strength = sentiment.get("strength", 0.5)

                if direction == "bullish":
                    return 0.05 + (strength * 0.1)  # 5-15% return
                elif direction == "bearish":
                    return -0.05 - (strength * 0.1)  # -5 to -15% return
                else:
                    return 0.02  # 2% return for neutral

        # Default expected return
        return 0.05  # 5% return

    def _generate_covariance_matrix(self, assets: List[str]) -> List[List[float]]:
        """
        Generate covariance matrix for assets.

        Args:
            assets (List[str]): List of asset symbols

        Returns:
            List[List[float]]: Covariance matrix
        """
        # This is a simplified implementation
        # In a real system, this would use historical price data

        n = len(assets)
        matrix = [[0.0 for _ in range(n)] for _ in range(n)]

        for i in range(n):
            for j in range(n):
                if i == j:
                    # Variance on diagonal (simplified)
                    matrix[i][j] = 0.04  # 20% volatility squared
                else:
                    # Covariance (simplified)
                    # Assume 0.3 correlation between assets
                    matrix[i][j] = 0.3 * 0.04

        return matrix

    async def run_secure_computation(self, computation_type: str, parameters: Dict) -> Dict:
        """
        Run secure multi-party computation for sensitive trading data.

        Args:
            computation_type (str): Type of computation
            parameters (Dict): Computation parameters

        Returns:
            Dict: Computation results
        """
        self.logger.info(f"Running secure computation: {computation_type}")

        # Get MPC client
        mpc_client = self.get_service("mpc_client")
        if not mpc_client:
            raise ValueError("MPC client not available")

        try:
            # Create computation
            computation_id = await mpc_client.create_computation(
                computation_type=computation_type,
                parameters=parameters
            )

            # Submit input
            await mpc_client.submit_input(
                computation_id=computation_id,
                input_data=parameters.get("input_data")
            )

            # Get result
            result = await mpc_client.get_result(computation_id)

            return {
                "computation_id": computation_id,
                "computation_type": computation_type,
                "timestamp": datetime.now().isoformat(),
                "result": result,
            }

        except Exception as e:
            self.logger.exception(f"Error in secure computation: {e}")
            return {
                "error": str(e),
                "computation_type": computation_type,
                "timestamp": datetime.now().isoformat(),
            }

    def _extract_price(self, market_data: Dict) -> float:
        """
        Extract price from market data.

        Args:
            market_data (Dict): Market data

        Returns:
            float: Price
        """
        # This is a simplified implementation
        # In a real system, this would involve more complex logic

        # Try different paths to find price
        if "trade" in market_data and "p" in market_data["trade"]:
            return float(market_data["trade"]["p"])

        if "quote" in market_data and "ap" in market_data["quote"]:
            return float(market_data["quote"]["ap"])

        if "bars" in market_data and market_data["bars"] and "c" in market_data["bars"][0]:
            return float(market_data["bars"][0]["c"])

        # Default to a placeholder value
        return 0.0
