@echo off
echo Starting Advanced Local Hugging Face API Server for UI-TARS...

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Ask if the user wants to use dummy responses
set /p USE_DUMMY="Use dummy responses? (y/n, default: y): "
if /i "%USE_DUMMY%"=="n" (
    set DUMMY_FLAG=
    echo Using actual model for responses.
) else (
    set DUMMY_FLAG=--dummy
    echo Using dummy responses.
)

REM Ask for the model path
set /p MODEL_PATH="Enter path to your model (default: C:/Users/<USER>/models/UI-TARS-1.5-7B): "
if "%MODEL_PATH%"=="" (
    set MODEL_PATH=C:/Users/<USER>/models/UI-TARS-1.5-7B
)

REM Start the server
echo.
echo Server is starting at http://127.0.0.1:8000
echo.
echo Configure UI-TARS with these settings:
echo - VLM Provider: Hugging Face
echo - VLM Base URL: http://127.0.0.1:8000
echo - VLM API Key: dummy_key
echo - VLM Model Name: UI-TARS-1.5-7B
echo.
echo Press Ctrl+C to stop the server when you're done.
echo.

python advanced_local_hf_server.py --model-path "%MODEL_PATH%" %DUMMY_FLAG%

exit /b 0
