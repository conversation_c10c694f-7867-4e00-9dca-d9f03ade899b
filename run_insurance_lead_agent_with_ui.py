"""
Run the Insurance Lead Agent with a web interface.

This script runs the Insurance Lead Agent with a web interface for monitoring.
"""
import os
import sys
import json
import asyncio
import argparse
import signal
import threading
from typing import Dict, List, Optional, Any
from datetime import datetime
import webbrowser
from aiohttp import web
import aiohttp_jinja2
import jinja2

from agents.insurance_lead_agent import InsuranceLeadAgent
from core.state_manager import StateManager
from core.logger import setup_logger
from llm.llm_router import LLMRouter

# Set up logger
logger = setup_logger("run_insurance_lead_agent_with_ui")

# Global variables
agent = None
shutdown_event = None
app = web.Application()

async def initialize_agent(config_path: str):
    """
    Initialize the Insurance Lead Agent.
    
    Args:
        config_path (str): Path to lead agent configuration file
        
    Returns:
        InsuranceLeadAgent: Initialized agent
    """
    logger.info("Initializing Insurance Lead Agent...")
    
    # Load configuration
    try:
        with open(config_path, "r") as f:
            lead_config = json.load(f)
    except Exception as e:
        logger.error(f"Error loading configuration: {e}")
        return None
    
    # Create state manager
    state_manager = StateManager()
    await state_manager.initialize()
    
    # Create LLM router
    llm_router = LLMRouter()
    await llm_router.initialize()
    
    # Create message queue and shutdown event
    message_queue = asyncio.Queue()
    global shutdown_event
    shutdown_event = asyncio.Event()
    
    # Create agent configuration
    agent_config = {
        "name": "Insurance Lead Agent",
        "description": "Handles leads from multiple channels",
        "llm_provider": "anthropic",
        "lead_agent_config": lead_config
    }
    
    # Create and initialize the agent
    global agent
    agent = InsuranceLeadAgent(
        agent_id="insurance_lead_agent_1",
        config=agent_config,
        state_manager=state_manager,
        message_queue=message_queue,
        shutdown_event=shutdown_event
    )
    
    # Set LLM router
    agent.llm_router = llm_router
    
    # Initialize the agent
    await agent.initialize()
    
    logger.info("Insurance Lead Agent initialized")
    
    return agent

async def run_agent(interval: int = 60):
    """
    Run the Insurance Lead Agent.
    
    Args:
        interval (int): Interval between agent cycles in seconds
    """
    logger.info(f"Running Insurance Lead Agent with interval {interval} seconds")
    
    global agent
    global shutdown_event
    
    if not agent:
        logger.error("Agent not initialized")
        return
    
    try:
        # Run until shutdown
        while not shutdown_event.is_set():
            # Execute agent cycle
            logger.info("Executing agent cycle...")
            await agent.execute_cycle()
            
            # Wait for next cycle
            try:
                await asyncio.wait_for(shutdown_event.wait(), timeout=interval)
            except asyncio.TimeoutError:
                pass
    
    except KeyboardInterrupt:
        logger.info("Keyboard interrupt received")
    
    except Exception as e:
        logger.exception(f"Error running agent: {e}")
    
    finally:
        # Shutdown
        logger.info("Shutting down...")
        shutdown_event.set()
        
        # Close state manager
        await agent.state_manager.close()
        
        logger.info("Shutdown complete")

def signal_handler(sig, frame):
    """
    Handle signals.
    
    Args:
        sig: Signal number
        frame: Current stack frame
    """
    logger.info(f"Signal {sig} received")
    
    global shutdown_event
    if shutdown_event:
        shutdown_event.set()

# Web interface routes

@aiohttp_jinja2.template('index.html')
async def index(request):
    """
    Handle index page request.
    
    Args:
        request: Request object
        
    Returns:
        Dict: Template context
    """
    global agent
    
    if not agent:
        return {
            "agent_initialized": False,
            "leads": [],
            "interactions": [],
            "channels": {}
        }
    
    # Get leads
    leads = list(agent.leads.values())
    
    # Sort leads by last contact
    leads.sort(key=lambda x: x.get("last_contact", ""), reverse=True)
    
    # Get interactions
    interactions = list(agent.interactions.values())
    
    # Sort interactions by timestamp
    interactions.sort(key=lambda x: x.get("timestamp", ""), reverse=True)
    
    # Limit to 100 interactions
    interactions = interactions[:100]
    
    return {
        "agent_initialized": True,
        "leads": leads,
        "interactions": interactions,
        "channels": agent.channels
    }

async def api_leads(request):
    """
    Handle API request for leads.
    
    Args:
        request: Request object
        
    Returns:
        Response: JSON response
    """
    global agent
    
    if not agent:
        return web.json_response({"error": "Agent not initialized"}, status=500)
    
    # Get leads
    leads = list(agent.leads.values())
    
    # Sort leads by last contact
    leads.sort(key=lambda x: x.get("last_contact", ""), reverse=True)
    
    return web.json_response({"leads": leads})

async def api_interactions(request):
    """
    Handle API request for interactions.
    
    Args:
        request: Request object
        
    Returns:
        Response: JSON response
    """
    global agent
    
    if not agent:
        return web.json_response({"error": "Agent not initialized"}, status=500)
    
    # Get interactions
    interactions = list(agent.interactions.values())
    
    # Sort interactions by timestamp
    interactions.sort(key=lambda x: x.get("timestamp", ""), reverse=True)
    
    # Limit to 100 interactions
    interactions = interactions[:100]
    
    return web.json_response({"interactions": interactions})

async def api_channels(request):
    """
    Handle API request for channels.
    
    Args:
        request: Request object
        
    Returns:
        Response: JSON response
    """
    global agent
    
    if not agent:
        return web.json_response({"error": "Agent not initialized"}, status=500)
    
    return web.json_response({"channels": agent.channels})

async def api_lead(request):
    """
    Handle API request for a specific lead.
    
    Args:
        request: Request object
        
    Returns:
        Response: JSON response
    """
    global agent
    
    if not agent:
        return web.json_response({"error": "Agent not initialized"}, status=500)
    
    # Get lead ID
    lead_id = request.match_info.get("lead_id")
    
    if not lead_id:
        return web.json_response({"error": "Lead ID not provided"}, status=400)
    
    # Get lead
    lead = agent.leads.get(lead_id)
    
    if not lead:
        return web.json_response({"error": "Lead not found"}, status=404)
    
    # Get interactions for this lead
    interactions = []
    for interaction_id, interaction in agent.interactions.items():
        if interaction.get("lead_id") == lead_id:
            interactions.append(interaction)
    
    # Sort interactions by timestamp
    interactions.sort(key=lambda x: x.get("timestamp", ""))
    
    return web.json_response({
        "lead": lead,
        "interactions": interactions
    })

async def api_simulate_lead(request):
    """
    Handle API request to simulate a lead.
    
    Args:
        request: Request object
        
    Returns:
        Response: JSON response
    """
    global agent
    
    if not agent:
        return web.json_response({"error": "Agent not initialized"}, status=500)
    
    # Get request data
    try:
        data = await request.json()
    except Exception:
        return web.json_response({"error": "Invalid JSON"}, status=400)
    
    # Get required fields
    channel = data.get("channel")
    user_handle = data.get("user_handle")
    message = data.get("message")
    
    if not channel or not user_handle or not message:
        return web.json_response({"error": "Missing required fields"}, status=400)
    
    # Create lead data
    lead_data = {
        "lead_id": f"{channel}-{datetime.now().strftime('%Y%m%d%H%M%S')}",
        "user_handle": user_handle,
        "message": message
    }
    
    # Add optional fields
    if "email" in data:
        lead_data["email"] = data["email"]
    
    if "phone" in data:
        lead_data["phone"] = data["phone"]
    
    # Handle lead
    try:
        result = await agent.handle_lead(channel, lead_data)
        return web.json_response(result)
    except Exception as e:
        logger.exception(f"Error handling lead: {e}")
        return web.json_response({"error": str(e)}, status=500)

def setup_web_app(host: str, port: int):
    """
    Set up the web application.
    
    Args:
        host (str): Host to bind to
        port (int): Port to bind to
    """
    global app
    
    # Set up Jinja2 templates
    templates_dir = os.path.join(os.path.dirname(__file__), "templates")
    os.makedirs(templates_dir, exist_ok=True)
    
    # Create index.html template if it doesn't exist
    index_template_path = os.path.join(templates_dir, "index.html")
    if not os.path.exists(index_template_path):
        with open(index_template_path, "w") as f:
            f.write("""
<!DOCTYPE html>
<html>
<head>
    <title>Insurance Lead Agent</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .container { margin-top: 20px; }
        .card { margin-bottom: 20px; }
        .badge { margin-right: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Insurance Lead Agent</h1>
        
        {% if not agent_initialized %}
            <div class="alert alert-warning">Agent not initialized</div>
        {% else %}
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>Channel Status</h5>
                        </div>
                        <div class="card-body">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Channel</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for channel, config in channels.items() %}
                                        <tr>
                                            <td>{{ channel }}</td>
                                            <td>
                                                {% if config.status == "available" %}
                                                    <span class="badge bg-success">Available</span>
                                                {% elif config.status == "unavailable" %}
                                                    <span class="badge bg-warning">Unavailable</span>
                                                {% elif config.status == "error" %}
                                                    <span class="badge bg-danger">Error</span>
                                                    {% if config.error %}
                                                        <small class="text-muted">{{ config.error }}</small>
                                                    {% endif %}
                                                {% else %}
                                                    <span class="badge bg-secondary">Unknown</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h5>Simulate Lead</h5>
                        </div>
                        <div class="card-body">
                            <form id="simulate-lead-form">
                                <div class="mb-3">
                                    <label for="channel" class="form-label">Channel</label>
                                    <select class="form-select" id="channel" name="channel" required>
                                        <option value="facebook">Facebook</option>
                                        <option value="instagram">Instagram</option>
                                        <option value="tiktok">TikTok</option>
                                        <option value="website">Website</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="user_handle" class="form-label">Name</label>
                                    <input type="text" class="form-control" id="user_handle" name="user_handle" required>
                                </div>
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email (optional)</label>
                                    <input type="email" class="form-control" id="email" name="email">
                                </div>
                                <div class="mb-3">
                                    <label for="phone" class="form-label">Phone (optional)</label>
                                    <input type="text" class="form-control" id="phone" name="phone">
                                </div>
                                <div class="mb-3">
                                    <label for="message" class="form-label">Message</label>
                                    <textarea class="form-control" id="message" name="message" rows="3" required></textarea>
                                </div>
                                <button type="submit" class="btn btn-primary">Submit</button>
                            </form>
                            <div id="simulate-lead-result" class="mt-3"></div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>Recent Leads</h5>
                        </div>
                        <div class="card-body">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Channel</th>
                                        <th>Status</th>
                                        <th>Last Contact</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for lead in leads[:10] %}
                                        <tr>
                                            <td>{{ lead.user_handle }}</td>
                                            <td>{{ lead.channel }}</td>
                                            <td>
                                                {% if lead.status == "new" %}
                                                    <span class="badge bg-info">New</span>
                                                {% elif lead.status == "greeted" %}
                                                    <span class="badge bg-primary">Greeted</span>
                                                {% elif lead.status == "qualifying" %}
                                                    <span class="badge bg-warning">Qualifying</span>
                                                {% elif lead.status == "qualified" %}
                                                    <span class="badge bg-success">Qualified</span>
                                                {% elif lead.status == "booked" %}
                                                    <span class="badge bg-success">Booked</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">{{ lead.status }}</span>
                                                {% endif %}
                                                
                                                {% if lead.insurance_type %}
                                                    <span class="badge bg-secondary">{{ lead.insurance_type }}</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ lead.last_contact }}</td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h5>Recent Interactions</h5>
                        </div>
                        <div class="card-body">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Channel</th>
                                        <th>Direction</th>
                                        <th>Content</th>
                                        <th>Status</th>
                                        <th>Timestamp</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for interaction in interactions[:10] %}
                                        <tr>
                                            <td>{{ interaction.channel }}</td>
                                            <td>
                                                {% if interaction.direction == "incoming" %}
                                                    <span class="badge bg-primary">Incoming</span>
                                                {% else %}
                                                    <span class="badge bg-success">Outgoing</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ interaction.content[:50] }}{% if interaction.content|length > 50 %}...{% endif %}</td>
                                            <td>
                                                {% if interaction.status == "received" %}
                                                    <span class="badge bg-info">Received</span>
                                                {% elif interaction.status == "success" %}
                                                    <span class="badge bg-success">Success</span>
                                                {% elif interaction.status == "failure" %}
                                                    <span class="badge bg-danger">Failure</span>
                                                {% elif interaction.status == "escalated" %}
                                                    <span class="badge bg-warning">Escalated</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">{{ interaction.status }}</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ interaction.timestamp }}</td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const simulateLeadForm = document.getElementById('simulate-lead-form');
            const simulateLeadResult = document.getElementById('simulate-lead-result');
            
            if (simulateLeadForm) {
                simulateLeadForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    const formData = new FormData(simulateLeadForm);
                    const data = {};
                    
                    for (const [key, value] of formData.entries()) {
                        if (value) {
                            data[key] = value;
                        }
                    }
                    
                    fetch('/api/simulate-lead', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(data)
                    })
                    .then(response => response.json())
                    .then(result => {
                        if (result.error) {
                            simulateLeadResult.innerHTML = `<div class="alert alert-danger">${result.error}</div>`;
                        } else {
                            simulateLeadResult.innerHTML = `<div class="alert alert-success">Lead submitted successfully!</div>`;
                            simulateLeadForm.reset();
                            
                            // Reload page after 2 seconds
                            setTimeout(() => {
                                window.location.reload();
                            }, 2000);
                        }
                    })
                    .catch(error => {
                        simulateLeadResult.innerHTML = `<div class="alert alert-danger">${error.message}</div>`;
                    });
                });
            }
        });
    </script>
</body>
</html>
            """)
    
    aiohttp_jinja2.setup(app, loader=jinja2.FileSystemLoader(templates_dir))
    
    # Set up routes
    app.router.add_get('/', index)
    app.router.add_get('/api/leads', api_leads)
    app.router.add_get('/api/interactions', api_interactions)
    app.router.add_get('/api/channels', api_channels)
    app.router.add_get('/api/lead/{lead_id}', api_lead)
    app.router.add_post('/api/simulate-lead', api_simulate_lead)
    
    # Start web server
    runner = web.AppRunner(app)
    return runner, host, port

async def start_web_server(runner, host, port):
    """
    Start the web server.
    
    Args:
        runner: Application runner
        host (str): Host to bind to
        port (int): Port to bind to
    """
    await runner.setup()
    site = web.TCPSite(runner, host, port)
    await site.start()
    
    logger.info(f"Web server started at http://{host}:{port}")
    
    # Open browser
    webbrowser.open(f"http://{host}:{port}")

async def main():
    """Run the Insurance Lead Agent with web interface."""
    parser = argparse.ArgumentParser(description="Run the Insurance Lead Agent with web interface")
    parser.add_argument("--config", type=str, default="config/lead_agent_config.json", help="Path to lead agent configuration file")
    parser.add_argument("--interval", type=int, default=60, help="Interval between agent cycles in seconds")
    parser.add_argument("--host", type=str, default="localhost", help="Host to bind web server to")
    parser.add_argument("--port", type=int, default=8080, help="Port to bind web server to")
    args = parser.parse_args()
    
    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Initialize agent
    agent = await initialize_agent(args.config)
    
    if not agent:
        return
    
    # Set up web app
    runner, host, port = setup_web_app(args.host, args.port)
    
    # Start web server
    await start_web_server(runner, host, port)
    
    # Run agent
    await run_agent(args.interval)
    
    # Cleanup
    await runner.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
