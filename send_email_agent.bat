@echo off
echo AI Agent Email Sender
echo ===================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Install required packages if not already installed
echo Checking and installing required packages...
pip install -r email_automation_requirements.txt
if %errorlevel% neq 0 (
    echo Failed to install required packages. Please check your internet connection.
    exit /b 1
)

REM Install cryptography if not already installed
pip install cryptography >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing cryptography package...
    pip install cryptography
    if %errorlevel% neq 0 (
        echo Failed to install cryptography package. Please check your internet connection.
        exit /b 1
    )
)

REM Ask for email details
echo.
set FROM_EMAIL=<EMAIL>
set /p TO_EMAIL="Enter recipient email (default: <EMAIL>): "
if "%TO_EMAIL%"=="" set TO_EMAIL=<EMAIL>

set /p SUBJECT="Enter subject (default: Message from AI Agent System): "
if "%SUBJECT%"=="" set SUBJECT=Message from AI Agent System

echo.
echo Enter body (type your message and press Enter twice when done):
echo -------------------------------------------------------------
set BODY=
:body_loop
set /p LINE=""
if "%LINE%"=="" goto body_done
set BODY=%BODY%%LINE%^

goto body_loop
:body_done

if "%BODY%"=="" set BODY=This is a message sent using the AI Agent System.

REM Create a temporary Python script
echo import sys > temp_email_script.py
echo from agent_email_demo import EmailAgent >> temp_email_script.py
echo agent = EmailAgent() >> temp_email_script.py
echo agent.initialize() >> temp_email_script.py
echo result = agent.send_email('%FROM_EMAIL%', '%TO_EMAIL%', '%SUBJECT%', r"""%BODY%""") >> temp_email_script.py
echo agent.shutdown() >> temp_email_script.py
echo sys.exit(0 if result['success'] else 1) >> temp_email_script.py

REM Run the temporary script
echo.
echo Sending email from %FROM_EMAIL% to %TO_EMAIL%...
echo.

python temp_email_script.py

echo.
if %errorlevel% equ 0 (
    echo Email sent successfully!
) else (
    echo There was an issue sending the email. Please check the logs.
)

REM Clean up temporary script
del temp_email_script.py

echo.
pause
