"""
State management for the Multi-Agent AI System.
"""
import json
import asyncio
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
import aiofiles
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

import config
from core.logger import setup_logger

# Set up logger
logger = setup_logger("state_manager")

# SQLAlchemy setup
Base = declarative_base()

class StateManager:
    """
    Manages the state of the system, including persistence and recovery.
    """

    def __init__(self, use_database: bool = True):
        """
        Initialize the state manager.

        Args:
            use_database (bool): Whether to use the database for state storage
        """
        self.use_database = use_database

        if self.use_database:
            self.engine = create_engine(config.DATABASE_URL)
            self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        else:
            self.engine = None
            self.SessionLocal = None

        self.state_file = config.DATA_DIR / "system_state.json"
        self.state_lock = asyncio.Lock()
        self.state = {
            "system": {
                "start_time": datetime.now().isoformat(),
                "last_update": datetime.now().isoformat(),
                "version": config.VERSION,
            },
            "agents": {},
            "tasks": {},
            "metrics": {},
        }

    async def initialize(self):
        """Initialize the state manager and load state if available."""
        # Create database tables if using database
        if self.use_database and self.engine:
            Base.metadata.create_all(bind=self.engine)

        # Ensure data directory exists
        config.DATA_DIR.mkdir(exist_ok=True)

        # Load state from file if it exists
        if self.state_file.exists():
            try:
                await self.load_state()
                logger.info("System state loaded successfully")
            except Exception as e:
                logger.error(f"Failed to load system state: {e}")
                # Create a backup of the corrupted state file
                backup_file = self.state_file.with_suffix(f".bak.{datetime.now().strftime('%Y%m%d%H%M%S')}")
                self.state_file.rename(backup_file)
                logger.info(f"Corrupted state file backed up to {backup_file}")
        else:
            logger.info("No existing state file found, starting with fresh state")
            await self.save_state()

    async def load_state(self):
        """Load system state from file."""
        async with self.state_lock:
            async with aiofiles.open(self.state_file, 'r') as f:
                content = await f.read()
                loaded_state = json.loads(content)
                self.state.update(loaded_state)

    async def save_state(self):
        """Save current system state to file."""
        async with self.state_lock:
            # Update last update timestamp
            self.state["system"]["last_update"] = datetime.now().isoformat()

            # Ensure data directory exists
            config.DATA_DIR.mkdir(exist_ok=True)

            # Write state to file
            async with aiofiles.open(self.state_file, 'w') as f:
                await f.write(json.dumps(self.state, indent=2))

    async def get_state(self, section: Optional[str] = None, key: Optional[str] = None) -> Any:
        """
        Get state data.

        Args:
            section (str, optional): State section to retrieve
            key (str, optional): Specific key within section

        Returns:
            Any: Requested state data
        """
        async with self.state_lock:
            if section is None:
                return self.state

            if section not in self.state:
                return None

            if key is None:
                return self.state[section]

            return self.state[section].get(key)

    async def update_state(self, section: str, key: str, value: Any):
        """
        Update state data.

        Args:
            section (str): State section to update
            key (str): Key within section
            value (Any): New value
        """
        async with self.state_lock:
            if section not in self.state:
                self.state[section] = {}

            self.state[section][key] = value
            await self.save_state()

    async def close(self):
        """Clean up resources and save final state."""
        await self.save_state()
        logger.info("State manager closed")
