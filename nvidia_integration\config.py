"""
Configuration for NVIDIA integration components.
"""
import os
from typing import Dict, Any, List

# Default configuration for NVIDIA integration
NVIDIA_CONFIG = {
    "enabled": os.getenv("ENABLE_NVIDIA_INTEGRATION", "True").lower() == "true",
    
    # CUDA and TensorRT acceleration
    "gpu_acceleration": {
        "enabled": True,
        "devices": [0],  # List of GPU device IDs to use
        "precision": "mixed",  # Options: fp32, fp16, mixed, int8
        "memory_efficient_attention": True,
        "compile_models": True,  # Use torch.compile for PyTorch 2.0+ acceleration
    },
    
    # Riva Speech AI
    "riva": {
        "enabled": os.getenv("ENABLE_NVIDIA_RIVA", "False").lower() == "true",
        "server_url": os.getenv("NVIDIA_RIVA_SERVER", "localhost:50051"),
        "language_code": "en-US",
        "voice_name": "English-US.Female-1",
        "sample_rate_hz": 16000,
        "asr_boosted_lm_words": [],
        "asr_boosted_lm_score": 10.0,
        "tts_pitch": 0.0,
        "tts_speaking_rate": 1.0,
    },
    
    # Clara Healthcare AI
    "clara": {
        "enabled": os.getenv("ENABLE_NVIDIA_CLARA", "False").lower() == "true",
        "api_key": os.getenv("NVIDIA_CLARA_API_KEY", ""),
        "server_url": os.getenv("NVIDIA_CLARA_SERVER", ""),
        "models": {
            "medical_nlp": "clara-medical-nlp",
            "medical_imaging": "clara-medical-imaging",
        }
    },
    
    # Isaac Robotics
    "isaac": {
        "enabled": os.getenv("ENABLE_NVIDIA_ISAAC", "False").lower() == "true",
        "sim_server_url": os.getenv("NVIDIA_ISAAC_SIM_SERVER", ""),
        "models": {
            "navigation": "isaac_nav_latest",
            "manipulation": "isaac_manip_latest",
        }
    },
    
    # Metropolis Vision AI
    "metropolis": {
        "enabled": os.getenv("ENABLE_NVIDIA_METROPOLIS", "False").lower() == "true",
        "server_url": os.getenv("NVIDIA_METROPOLIS_SERVER", ""),
        "api_key": os.getenv("NVIDIA_METROPOLIS_API_KEY", ""),
        "models": {
            "object_detection": "metropolis-objdet",
            "tracking": "metropolis-tracking",
            "action_recognition": "metropolis-action-recog",
        }
    },
    
    # Jetson Edge AI
    "jetson": {
        "enabled": os.getenv("ENABLE_NVIDIA_JETSON", "False").lower() == "true",
        "device_ip": os.getenv("NVIDIA_JETSON_IP", ""),
        "username": os.getenv("NVIDIA_JETSON_USER", ""),
        "password": os.getenv("NVIDIA_JETSON_PASSWORD", ""),
        "ssh_port": int(os.getenv("NVIDIA_JETSON_SSH_PORT", "22")),
    },
    
    # Optimizations
    "optimizers": {
        "enabled": True,
        "use_triton": True,  # Use NVIDIA Triton Inference Server
        "enable_flash_attention": True,
        "enable_fused_kernels": True,
        "cudnn_benchmark": True,
    },
    
    # Logging
    "logging": {
        "level": "info",
        "log_gpu_stats": True,
        "log_interval_seconds": 60,
    }
}