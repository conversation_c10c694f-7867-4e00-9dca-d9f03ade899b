"""
Agent management for the Multi-Agent AI System.

This module provides management capabilities for all agents in the system,
including lifecycle management, message routing, and service registration.
It implements enhanced message handling with priority-based routing.
"""
import asyncio
from typing import Dict, List, Optional, Set, Type, Any, Tuple
import importlib
import inspect
from datetime import datetime
import heapq
import uuid

import config
from core.logger import setup_logger
from core.state_manager import StateManager
from agents.base_agent import BaseAgent, MessagePriority

# Set up logger
logger = setup_logger("agent_manager")

class AgentManager:
    """
    Manages the lifecycle and coordination of all agents in the system.

    This class is responsible for:
    - Loading and initializing agents
    - Managing agent lifecycle (start, stop)
    - Routing messages between agents
    - Providing access to shared services
    """

    def __init__(self, state_manager: StateManager, shutdown_event: asyncio.Event):
        """
        Initialize the agent manager.

        Args:
            state_manager (StateManager): System state manager
            shutdown_event (asyncio.Event): Event to signal system shutdown
        """
        self.state_manager = state_manager
        self.shutdown_event = shutdown_event
        self.agents: Dict[str, BaseAgent] = {}
        self.agent_tasks: Dict[str, asyncio.Task] = {}

        # Priority-based message queue
        self.message_queue = asyncio.PriorityQueue()
        self.message_processor_task = None

        # Message tracking
        self.message_stats = {
            "processed": 0,
            "errors": 0,
            "broadcasts": 0,
        }

        # Message history (limited size)
        self.message_history = {}
        self.max_history_size = 1000

        # Services registry for shared services
        self.services: Dict[str, Any] = {}

    async def start(self):
        """Start all enabled agents and the message processor."""
        logger.info("Starting agent manager")

        # Start message processor
        self.message_processor_task = asyncio.create_task(self._process_messages())

        # Load and start all enabled agents
        await self._load_agents()

        # Initialize workflow engine if available
        if "workflow_engine" in self.services:
            try:
                workflow_engine = self.services["workflow_engine"]
                await workflow_engine.initialize()
                logger.info("Workflow engine initialized")
            except Exception as e:
                logger.error(f"Error initializing workflow engine: {str(e)}")

        logger.info(f"Agent manager started with {len(self.agents)} agents")

    async def stop(self):
        """Stop all agents and the message processor."""
        logger.info("Stopping agent manager")

        # Cancel all agent tasks
        for agent_id, task in self.agent_tasks.items():
            logger.info(f"Stopping agent: {agent_id}")
            task.cancel()

        # Wait for all agent tasks to complete
        if self.agent_tasks:
            await asyncio.gather(*self.agent_tasks.values(), return_exceptions=True)

        # Stop message processor
        if self.message_processor_task:
            self.message_processor_task.cancel()
            try:
                await self.message_processor_task
            except asyncio.CancelledError:
                pass

        logger.info("Agent manager stopped")

    async def _load_agents(self):
        """Load and initialize all enabled agents."""
        for agent_id, agent_config in config.AGENT_CONFIG.items():
            if agent_config.get("enabled", False):
                try:
                    # Import the agent module
                    module_name = f"agents.{agent_id}"
                    class_name = "".join(word.capitalize() for word in agent_id.split("_"))

                    try:
                        module = importlib.import_module(module_name)
                        agent_class = getattr(module, class_name)
                    except (ImportError, AttributeError) as e:
                        logger.error(f"Failed to import agent {agent_id}: {e}")
                        continue

                    # Create agent instance
                    agent = agent_class(
                        agent_id=agent_id,
                        config=agent_config,
                        state_manager=self.state_manager,
                        message_queue=self.message_queue,
                        shutdown_event=self.shutdown_event
                    )

                    # Store agent instance
                    self.agents[agent_id] = agent

                    # Start agent task
                    self.agent_tasks[agent_id] = asyncio.create_task(
                        self._run_agent(agent_id, agent)
                    )

                    logger.info(f"Agent loaded: {agent_id}")

                except Exception as e:
                    logger.exception(f"Error loading agent {agent_id}: {e}")

    async def _run_agent(self, agent_id: str, agent: BaseAgent):
        """
        Run an agent in a separate task.

        Args:
            agent_id (str): Agent identifier
            agent (BaseAgent): Agent instance
        """
        try:
            logger.info(f"Starting agent: {agent_id}")
            await agent.initialize()
            await agent.run()
        except asyncio.CancelledError:
            logger.info(f"Agent {agent_id} task cancelled")
            await agent.shutdown()
        except Exception as e:
            logger.exception(f"Error in agent {agent_id}: {e}")
            await agent.shutdown()

    async def _process_messages(self):
        """Process messages from the message queue."""
        try:
            while not self.shutdown_event.is_set():
                # Get message from queue with timeout
                try:
                    priority, message = await asyncio.wait_for(self.message_queue.get(), timeout=1.0)
                except asyncio.TimeoutError:
                    continue

                try:
                    # Process message
                    message_id = message.get("id")
                    sender_id = message.get("sender_id")
                    recipient_id = message.get("recipient_id")
                    message_type = message.get("type")

                    # Store in message history (with size limit)
                    if len(self.message_history) >= self.max_history_size:
                        # Remove oldest message
                        oldest_id = next(iter(self.message_history))
                        del self.message_history[oldest_id]

                    self.message_history[message_id] = message

                    logger.debug(f"Processing message: {sender_id} -> {recipient_id} ({message_type}) [priority={priority}]")

                    # Update stats
                    self.message_stats["processed"] += 1

                    # Broadcast message to all agents if recipient is None
                    if recipient_id is None:
                        self.message_stats["broadcasts"] += 1
                        for agent_id, agent in self.agents.items():
                            if agent_id != sender_id:  # Don't send to sender
                                await agent.receive_message(message)
                    # Send to specific agent
                    elif recipient_id in self.agents:
                        await self.agents[recipient_id].receive_message(message)
                    # Send to agent coordinator if recipient is "coordinator"
                    elif recipient_id == "coordinator" and self.get_service("agent_coordinator"):
                        coordinator = self.get_service("agent_coordinator")
                        await coordinator.receive_message(message)
                    else:
                        logger.warning(f"Message recipient not found: {recipient_id}")

                        # If sender exists, send error response
                        if sender_id and sender_id in self.agents:
                            error_message = {
                                "id": str(uuid.uuid4()),
                                "sender_id": "agent_manager",
                                "recipient_id": sender_id,
                                "type": "error",
                                "content": {
                                    "error": f"Recipient not found: {recipient_id}",
                                    "original_message": message_id,
                                },
                                "timestamp": datetime.now().isoformat(),
                                "priority": MessagePriority.HIGH.value,
                            }
                            await self.message_queue.put((MessagePriority.HIGH.value, error_message))

                    # Mark message as processed
                    self.message_queue.task_done()

                except Exception as e:
                    logger.exception(f"Error processing message: {e}")
                    self.message_stats["errors"] += 1

                    # Try to send error response to sender
                    try:
                        if sender_id and sender_id in self.agents:
                            error_message = {
                                "id": str(uuid.uuid4()),
                                "sender_id": "agent_manager",
                                "recipient_id": sender_id,
                                "type": "error",
                                "content": {
                                    "error": f"Error processing message: {str(e)}",
                                    "original_message": message.get("id"),
                                },
                                "timestamp": datetime.now().isoformat(),
                                "priority": MessagePriority.HIGH.value,
                            }
                            await self.message_queue.put((MessagePriority.HIGH.value, error_message))
                    except Exception as e2:
                        logger.exception(f"Error sending error response: {e2}")

                    # Mark message as processed
                    self.message_queue.task_done()

        except asyncio.CancelledError:
            logger.info("Message processor task cancelled")
        except Exception as e:
            logger.exception(f"Error in message processor: {e}")

    async def send_message(self, message: Dict):
        """
        Send a message to the message queue.

        Args:
            message (Dict): Message to send
        """
        # Extract or set default priority
        priority = message.get("priority", MessagePriority.NORMAL.value)

        # Add to queue with priority
        await self.message_queue.put((priority, message))

        # Store in message history (with size limit)
        message_id = message.get("id")
        if message_id:
            if len(self.message_history) >= self.max_history_size:
                # Remove oldest message
                oldest_id = next(iter(self.message_history))
                del self.message_history[oldest_id]

            self.message_history[message_id] = message

    def get_agent(self, agent_id: str) -> Optional[BaseAgent]:
        """
        Get an agent by ID.

        Args:
            agent_id (str): Agent identifier

        Returns:
            Optional[BaseAgent]: Agent instance if found, None otherwise
        """
        return self.agents.get(agent_id)

    def get_all_agents(self) -> Dict[str, BaseAgent]:
        """
        Get all agents.

        Returns:
            Dict[str, BaseAgent]: Dictionary of all agents
        """
        return self.agents

    def register_agent(self, agent_id: str, agent: BaseAgent) -> None:
        """
        Register an agent with the agent manager.

        Args:
            agent_id (str): Agent identifier
            agent (BaseAgent): Agent instance
        """
        # Store agent instance
        self.agents[agent_id] = agent

        # Start agent task
        self.agent_tasks[agent_id] = asyncio.create_task(
            self._run_agent(agent_id, agent)
        )

        logger.info(f"Agent registered: {agent_id}")

    def register_service(self, service_id: str, service: Any) -> None:
        """
        Register a shared service that can be used by agents.

        Args:
            service_id (str): Service identifier
            service (Any): Service instance
        """
        self.services[service_id] = service
        logger.info(f"Registered service: {service_id}")

    def get_service(self, service_id: str) -> Optional[Any]:
        """
        Get a shared service by ID.

        Args:
            service_id (str): Service identifier

        Returns:
            Optional[Any]: Service instance if found, None otherwise
        """
        return self.services.get(service_id)

    def get_all_services(self) -> Dict[str, Any]:
        """
        Get all shared services.

        Returns:
            Dict[str, Any]: Dictionary of all services
        """
        return self.services
