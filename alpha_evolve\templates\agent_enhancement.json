{"name": "Agent Enhancement", "description": "Template for agent enhancement problems", "template": "\nYou are tasked with enhancing the {capability} capability of the {agent_id} agent.\n\nThe goal is to optimize for {optimization_metric}.\n\nYour solution should be implemented as a Python function that follows this interface:\n{interface}\n\nMake sure your implementation is:\n1. Efficient\n2. Robust\n3. Well-integrated with the agent's existing capabilities\n4. Optimized for {optimization_metric}\n", "variables": ["agent_id", "capability", "optimization_metric", "interface"]}