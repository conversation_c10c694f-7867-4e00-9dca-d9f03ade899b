"""
UI-TARS Integration Module.

This module provides integration between UI-TARS and the AI Agent System.
"""
import os
import sys
import json
import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
import threading
import queue
import time
from datetime import datetime

from core.logger import setup_logger
from ui_tars.connector.ui_tars_connector import <PERSON>ITarsConnector
from ui_tars.connector.midscene_connector import MidsceneConnector
from ui_tars.connector.local_llm_connector import LocalLLMConnector
from ui_tars.agent.ui_tars_agent import UITarsAgent

# Set up logger
logger = setup_logger("ui_tars_integration")

class UITarsIntegration:
    """
    UI-TARS Integration for the AI Agent System.
    
    This class provides integration between UI-TARS and the AI Agent System,
    allowing agents to use UI-TARS capabilities.
    """
    
    def __init__(self, 
                 config: Dict,
                 agent_manager: Any = None,
                 state_manager: Any = None,
                 message_bus: Any = None):
        """
        Initialize the UI-TARS Integration.
        
        Args:
            config (Dict): Configuration for the integration
            agent_manager (Any): Agent manager for the AI Agent System
            state_manager (Any): State manager for the AI Agent System
            message_bus (Any): Message bus for the AI Agent System
        """
        self.config = config
        self.agent_manager = agent_manager
        self.state_manager = state_manager
        self.message_bus = message_bus
        self.ui_tars_connector = None
        self.midscene_connector = None
        self.local_llm_connector = None
        self.ui_tars_agent = None
        self.is_initialized = False
        self.is_running = False
        self.message_queue = queue.Queue()
        self.shutdown_event = asyncio.Event()
    
    async def initialize(self):
        """Initialize the UI-TARS Integration."""
        logger.info("Initializing UI-TARS Integration")
        
        # Initialize connectors
        self.ui_tars_connector = UITarsConnector(
            api_url=self.config.get("ui_tars", {}).get("api_url"),
            api_key=self.config.get("ui_tars", {}).get("api_key"),
            model_name=self.config.get("ui_tars", {}).get("model_name"),
            installation_path=self.config.get("ui_tars", {}).get("installation_path")
        )
        
        self.midscene_connector = MidsceneConnector(
            api_url=self.config.get("midscene", {}).get("api_url"),
            api_key=self.config.get("midscene", {}).get("api_key"),
            model_name=self.config.get("midscene", {}).get("model_name"),
            installation_path=self.config.get("midscene", {}).get("installation_path"),
            browser_type=self.config.get("midscene", {}).get("browser_type", "chrome"),
            android_enabled=self.config.get("midscene", {}).get("android_enabled", False)
        )
        
        self.local_llm_connector = LocalLLMConnector(
            model_path=self.config.get("local_llm", {}).get("model_path"),
            model_type=self.config.get("local_llm", {}).get("model_type", "ui-tars"),
            host=self.config.get("local_llm", {}).get("host", "localhost"),
            port=self.config.get("local_llm", {}).get("port", 8000),
            api_base=self.config.get("local_llm", {}).get("api_base"),
            quantization=self.config.get("local_llm", {}).get("quantization", "4bit")
        )
        
        # Initialize connectors
        await self.ui_tars_connector.initialize()
        await self.midscene_connector.initialize()
        await self.local_llm_connector.initialize()
        
        # Create UI-TARS agent
        self.ui_tars_agent = UITarsAgent(
            agent_id="ui_tars_agent",
            config=self.config.get("agent", {}),
            state_manager=self.state_manager,
            message_queue=self.message_queue,
            shutdown_event=self.shutdown_event
        )
        
        # Initialize UI-TARS agent
        await self.ui_tars_agent.initialize()
        
        # Register UI-TARS agent with agent manager
        if self.agent_manager:
            self.agent_manager.register_agent(self.ui_tars_agent)
        
        self.is_initialized = True
        logger.info("UI-TARS Integration initialized")
        
        return True
    
    async def start(self):
        """Start the UI-TARS Integration."""
        if not self.is_initialized:
            logger.warning("UI-TARS Integration not initialized")
            return False
        
        logger.info("Starting UI-TARS Integration")
        
        # Start UI-TARS if auto-start is enabled
        if self.config.get("ui_tars", {}).get("auto_start", False):
            logger.info("Auto-starting UI-TARS")
            await self.ui_tars_connector.start()
        
        # Start local LLM server if auto-start is enabled
        if self.config.get("local_llm", {}).get("auto_start", False):
            logger.info("Auto-starting local LLM server")
            await self.local_llm_connector.start_server()
        
        # Start browser automation if auto-start is enabled
        if self.config.get("midscene", {}).get("auto_start_browser", False):
            logger.info("Auto-starting browser automation")
            url = self.config.get("midscene", {}).get("default_url")
            await self.midscene_connector.start_browser_automation(url)
        
        # Start Android automation if auto-start is enabled
        if self.config.get("midscene", {}).get("auto_start_android", False) and self.midscene_connector.android_enabled:
            logger.info("Auto-starting Android automation")
            device_id = self.config.get("midscene", {}).get("default_device_id")
            await self.midscene_connector.start_android_automation(device_id)
        
        # Start UI-TARS agent
        self.is_running = True
        
        logger.info("UI-TARS Integration started")
        
        return True
    
    async def stop(self):
        """Stop the UI-TARS Integration."""
        if not self.is_running:
            logger.warning("UI-TARS Integration not running")
            return False
        
        logger.info("Stopping UI-TARS Integration")
        
        # Stop UI-TARS agent
        if self.ui_tars_agent:
            await self.ui_tars_agent.shutdown()
        
        # Stop UI-TARS if running
        if self.ui_tars_connector and self.ui_tars_connector.is_running:
            await self.ui_tars_connector.stop()
        
        # Stop Midscene if running
        if self.midscene_connector and self.midscene_connector.is_running:
            await self.midscene_connector.stop()
        
        # Stop local LLM server if running
        if self.local_llm_connector and self.local_llm_connector.is_running:
            await self.local_llm_connector.stop_server()
        
        self.is_running = False
        logger.info("UI-TARS Integration stopped")
        
        return True
    
    async def execute_command(self, command: str, screenshot: bool = True) -> Dict:
        """
        Execute a command in UI-TARS.
        
        Args:
            command (str): Command to execute
            screenshot (bool): Whether to include a screenshot in the response
            
        Returns:
            Dict: Result of the operation
        """
        if not self.is_running:
            logger.warning("UI-TARS Integration not running")
            return {"error": "UI-TARS Integration not running"}
        
        if not self.ui_tars_connector:
            logger.warning("UI-TARS connector not initialized")
            return {"error": "UI-TARS connector not initialized"}
        
        logger.info(f"Executing command: {command}")
        
        result = await self.ui_tars_connector.execute_command(command, screenshot)
        
        return result
    
    async def browse_website(self, url: str, task: str = "browse") -> Dict:
        """
        Browse a website using UI-TARS.
        
        Args:
            url (str): URL to browse
            task (str): Task to perform on the website
            
        Returns:
            Dict: Result of the operation
        """
        if not self.is_running:
            logger.warning("UI-TARS Integration not running")
            return {"error": "UI-TARS Integration not running"}
        
        if not self.ui_tars_connector:
            logger.warning("UI-TARS connector not initialized")
            return {"error": "UI-TARS connector not initialized"}
        
        logger.info(f"Browsing website: {url}, task: {task}")
        
        # Construct the command
        command = f"Browse to {url} and {task}"
        
        result = await self.ui_tars_connector.execute_command(command)
        
        return result
    
    async def search_web(self, query: str, engine: str = "google") -> Dict:
        """
        Search the web using UI-TARS.
        
        Args:
            query (str): Search query
            engine (str): Search engine to use
            
        Returns:
            Dict: Result of the operation
        """
        if not self.is_running:
            logger.warning("UI-TARS Integration not running")
            return {"error": "UI-TARS Integration not running"}
        
        if not self.ui_tars_connector:
            logger.warning("UI-TARS connector not initialized")
            return {"error": "UI-TARS connector not initialized"}
        
        logger.info(f"Searching web: {query}, engine: {engine}")
        
        # Construct the command
        command = f"Search for {query} using {engine}"
        
        result = await self.ui_tars_connector.execute_command(command)
        
        return result
    
    async def start_autonomous_mode(self, task: str) -> Dict:
        """
        Start autonomous mode.
        
        Args:
            task (str): Task to perform autonomously
            
        Returns:
            Dict: Result of the operation
        """
        if not self.is_running:
            logger.warning("UI-TARS Integration not running")
            return {"error": "UI-TARS Integration not running"}
        
        if not self.ui_tars_agent:
            logger.warning("UI-TARS agent not initialized")
            return {"error": "UI-TARS agent not initialized"}
        
        logger.info(f"Starting autonomous mode: {task}")
        
        result = await self.ui_tars_agent.start_autonomous_mode(task)
        
        return result
    
    async def stop_autonomous_mode(self) -> Dict:
        """
        Stop autonomous mode.
        
        Returns:
            Dict: Result of the operation
        """
        if not self.is_running:
            logger.warning("UI-TARS Integration not running")
            return {"error": "UI-TARS Integration not running"}
        
        if not self.ui_tars_agent:
            logger.warning("UI-TARS agent not initialized")
            return {"error": "UI-TARS agent not initialized"}
        
        logger.info("Stopping autonomous mode")
        
        result = await self.ui_tars_agent.stop_autonomous_mode()
        
        return result
