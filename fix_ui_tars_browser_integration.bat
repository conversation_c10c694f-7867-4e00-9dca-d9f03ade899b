@echo off
echo Fix UI-TARS Browser Integration
echo ==============================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Ask for UI-TARS path
echo Enter the path to UI-TARS executable (leave empty to auto-detect):
set /p UI_TARS_PATH=""

REM Ask for configuration path
echo.
echo Enter the path to UI-TARS configuration file (leave empty to use default):
set /p CONFIG_PATH=""

REM Ask for browser type
echo.
echo Select browser type:
echo 1. Auto-detect (default)
echo 2. Chrome
echo 3. Edge
echo 4. Firefox
echo 5. Brave
echo.
set /p BROWSER_CHOICE="Enter choice (1-5): "

if "%BROWSER_CHOICE%"=="2" (
    set BROWSER_TYPE=chrome
) else if "%BROWSER_CHOICE%"=="3" (
    set BROWSER_TYPE=edge
) else if "%BROWSER_CHOICE%"=="4" (
    set BROWSER_TYPE=firefox
) else if "%BROWSER_CHOICE%"=="5" (
    set BROWSER_TYPE=brave
) else (
    set BROWSER_TYPE=
)

REM Run the browser integration fix script
echo.
echo Fixing UI-TARS browser integration...
echo.

set COMMAND=python fix_ui_tars_browser_integration.py

if not "%UI_TARS_PATH%"=="" (
    set COMMAND=%COMMAND% --path "%UI_TARS_PATH%"
)

if not "%CONFIG_PATH%"=="" (
    set COMMAND=%COMMAND% --config "%CONFIG_PATH%"
)

if not "%BROWSER_TYPE%"=="" (
    set COMMAND=%COMMAND% --browser %BROWSER_TYPE%
)

set COMMAND=%COMMAND% --debug

echo Executing: %COMMAND%
echo.

%COMMAND%

echo.
if %errorlevel% equ 0 (
    echo Browser integration has been fixed successfully!
    echo.
    echo Would you like to start UI-TARS now? (Y/N)
    set /p START_UI_TARS=""
    
    if /i "%START_UI_TARS%"=="Y" (
        echo.
        echo Starting UI-TARS...
        
        if not "%UI_TARS_PATH%"=="" (
            if not "%CONFIG_PATH%"=="" (
                start "" "%UI_TARS_PATH%" --config "%CONFIG_PATH%"
            ) else (
                start "" "%UI_TARS_PATH%" --config "config/ui_tars_config.json"
            )
        ) else (
            echo Please start UI-TARS manually.
        )
    )
) else (
    echo Failed to fix browser integration. Please check the error messages above.
)

echo.
pause
