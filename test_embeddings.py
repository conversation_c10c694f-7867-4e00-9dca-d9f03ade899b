"""
<PERSON><PERSON><PERSON> to test the embeddings functionality of the local model server.
"""
import sys
import asyncio
import argparse
import aiohttp
import numpy as np
from pathlib import Path

from core.logger import setup_logger

# Set up logger
logger = setup_logger("test_embeddings")

async def test_local_embeddings(url: str, texts: list):
    """
    Test embeddings from the local model server.
    
    Args:
        url (str): URL of the local model server
        texts (list): List of texts to embed
    """
    logger.info(f"Testing embeddings from local server at {url}")
    
    # Prepare request payload
    payload = {
        "inputs": texts
    }
    
    # Make API request
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{url}/embeddings",
                json=payload,
                timeout=60
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    logger.error(f"Error: {response.status} - {error_text}")
                    return False
                
                # Get embeddings
                embeddings = await response.json()
                
                # Check if we got embeddings
                if not embeddings or not isinstance(embeddings, list):
                    logger.error(f"Invalid response: {embeddings}")
                    return False
                
                # Print embeddings info
                print(f"Received {len(embeddings)} embeddings")
                
                for i, embedding in enumerate(embeddings):
                    # Convert to numpy array for easier analysis
                    embedding_array = np.array(embedding)
                    
                    print(f"Embedding {i+1}:")
                    print(f"  Text: {texts[i][:50]}...")
                    print(f"  Dimensions: {embedding_array.shape}")
                    print(f"  Mean: {embedding_array.mean():.6f}")
                    print(f"  Std: {embedding_array.std():.6f}")
                    print(f"  Min: {embedding_array.min():.6f}")
                    print(f"  Max: {embedding_array.max():.6f}")
                    print()
                
                # If we have multiple embeddings, calculate similarity
                if len(embeddings) > 1:
                    # Normalize embeddings
                    normalized_embeddings = []
                    for embedding in embeddings:
                        embedding_array = np.array(embedding)
                        norm = np.linalg.norm(embedding_array)
                        normalized_embeddings.append(embedding_array / norm)
                    
                    # Calculate cosine similarity
                    print("Cosine Similarity Matrix:")
                    for i, emb1 in enumerate(normalized_embeddings):
                        for j, emb2 in enumerate(normalized_embeddings):
                            if i <= j:  # Only print upper triangle
                                similarity = np.dot(emb1, emb2)
                                print(f"  Texts {i+1} and {j+1}: {similarity:.6f}")
                
                return True
    
    except Exception as e:
        logger.exception(f"Error testing embeddings: {e}")
        return False

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Test embeddings from the local model server")
    parser.add_argument("--url", default="http://localhost:8080", help="URL of the local model server")
    parser.add_argument("--texts", nargs="+", default=["This is a test sentence.", "Another completely different text."], help="Texts to embed")
    args = parser.parse_args()
    
    # Run test
    success = asyncio.run(test_local_embeddings(args.url, args.texts))
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
