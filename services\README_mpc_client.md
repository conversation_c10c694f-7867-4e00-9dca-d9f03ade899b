# Multi-Party Computation (MPC) Client

This module provides integration with MPC servers for secure computation on sensitive data without revealing the underlying data to any party.

## Overview

The Multi-Party Computation (MPC) Client enables the Multi-Agent AI System to perform secure computations on sensitive data across multiple parties without revealing the underlying data to any party. This is particularly useful for financial, insurance, and trading applications where privacy and confidentiality are critical.

## Features

### Secure Computations

- **Secure Average**: Compute the average of values from multiple parties without revealing individual values
- **Secure Sum**: Compute the sum of values from multiple parties without revealing individual values
- **Secure Comparison**: Compare values without revealing them
- **Secure Set Intersection**: Compute the intersection of sets without revealing the sets
- **Secure Linear Regression**: Compute linear regression without revealing the data

### Server Management

- **Multi-Server Support**: Connect to multiple MPC servers for redundancy and load balancing
- **Server Health Monitoring**: Monitor the health and status of MPC servers
- **Automatic Failover**: Automatically switch to backup servers if primary servers fail

### Security and Privacy

- **Zero-Knowledge Proofs**: Verify computation correctness without revealing inputs
- **Secure Credential Management**: Secure handling of API keys and credentials
- **Computation History**: Tracking and management of MPC computations
- **Input Validation**: Validate inputs before submitting to MPC servers

## Usage

### Secure Average Computation

```python
from services.mpc_client import MPCClient

# Initialize MPC client
mpc_client = MPCClient(state_manager)
await mpc_client.initialize()

# Create a secure average computation
computation_id = await mpc_client.create_computation(
    computation_type="secure_average",
    parameters={
        "description": "Average customer age calculation"
    }
)

# Submit input data
await mpc_client.submit_input(
    computation_id=computation_id,
    input_data={
        "values": [32, 45, 28, 39, 52]
    },
    party_id="insurance_agent"
)

# Get result
result = await mpc_client.get_result(computation_id)
print(f"Secure average result: {result['result']}")
```

### Secure Comparison

```python
# Create a secure comparison computation
computation_id = await mpc_client.create_computation(
    computation_type="secure_comparison",
    parameters={
        "description": "Compare premium values"
    }
)

# Submit input data
await mpc_client.submit_input(
    computation_id=computation_id,
    input_data={
        "value_a": 1250.75,
        "value_b": 1100.50,
        "operator": "gt"
    },
    party_id="insurance_agent"
)

# Get result
result = await mpc_client.get_result(computation_id)
print(f"Comparison result: {result['result']}")
```

### Secure Set Intersection

```python
# Create a secure set intersection computation
computation_id = await mpc_client.create_computation(
    computation_type="secure_set_intersection",
    parameters={
        "description": "Find common customer IDs"
    }
)

# Submit input data
await mpc_client.submit_input(
    computation_id=computation_id,
    input_data={
        "sets": [
            ["CUST-001", "CUST-002", "CUST-003", "CUST-004"],
            ["CUST-002", "CUST-004", "CUST-005", "CUST-006"]
        ]
    },
    party_id="insurance_agent"
)

# Get result
result = await mpc_client.get_result(computation_id)
print(f"Set intersection result: {result['result']}")
```

### Secure Linear Regression

```python
# Create a secure linear regression computation
computation_id = await mpc_client.create_computation(
    computation_type="secure_linear_regression",
    parameters={
        "description": "Predict claim amounts based on customer data"
    }
)

# Submit input data
await mpc_client.submit_input(
    computation_id=computation_id,
    input_data={
        "x_values": [[32, 5], [45, 10], [28, 2], [39, 7], [52, 15]],  # [age, years_as_customer]
        "y_values": [1200, 2500, 800, 1800, 3200]  # claim_amount
    },
    party_id="insurance_agent"
)

# Get result
result = await mpc_client.get_result(computation_id)
print(f"Coefficients: {result['coefficients']}")
print(f"Intercept: {result['intercept']}")
print(f"R-squared: {result['r_squared']}")
```

## Computation Configurations

The MPC Client supports the following computation configurations:

### Secure Average

```json
{
  "type": "arithmetic",
  "description": "Compute average of values without revealing individual values",
  "input_schema": {
    "type": "object",
    "properties": {
      "values": {
        "type": "array",
        "items": {"type": "number"}
      }
    }
  },
  "output_schema": {
    "type": "object",
    "properties": {
      "result": {"type": "number"}
    }
  }
}
```

### Secure Sum

```json
{
  "type": "arithmetic",
  "description": "Compute sum of values without revealing individual values",
  "input_schema": {
    "type": "object",
    "properties": {
      "values": {
        "type": "array",
        "items": {"type": "number"}
      }
    }
  },
  "output_schema": {
    "type": "object",
    "properties": {
      "result": {"type": "number"}
    }
  }
}
```

### Secure Comparison

```json
{
  "type": "boolean",
  "description": "Compare values without revealing them",
  "input_schema": {
    "type": "object",
    "properties": {
      "value_a": {"type": "number"},
      "value_b": {"type": "number"},
      "operator": {
        "type": "string",
        "enum": ["eq", "ne", "gt", "ge", "lt", "le"]
      }
    }
  },
  "output_schema": {
    "type": "object",
    "properties": {
      "result": {"type": "boolean"}
    }
  }
}
```

## Server Configuration

The MPC Client can be configured to use multiple MPC servers:

```python
# Configure MPC servers
config = {
    "servers": [
        {
            "id": "mpc-server-1",
            "url": "https://mpc-server-1.example.com",
            "region": "us-east"
        },
        {
            "id": "mpc-server-2",
            "url": "https://mpc-server-2.example.com",
            "region": "us-west"
        }
    ],
    "default_server": "mpc-server-1",
    "api_keys": {
        "mpc-server-1": "your_api_key_1",
        "mpc-server-2": "your_api_key_2"
    }
}

# Initialize with configuration
mpc_client = MPCClient(state_manager, config)
```

## Integration with Agents

Agents can access the MPC Client through the service registry:

```python
# In an agent's execute_cycle method
async def execute_cycle(self):
    # Get MPC client from services
    mpc_client = self.get_service("mpc_client")
    
    if mpc_client:
        # Create a secure computation
        computation_id = await mpc_client.create_computation(
            computation_type="secure_average",
            parameters={
                "description": "Average premium calculation"
            }
        )
        
        # Submit input data
        await mpc_client.submit_input(
            computation_id=computation_id,
            input_data={
                "values": self.premium_values
            }
        )
        
        # Get result
        result = await mpc_client.get_result(computation_id)
        
        # Process result
        await self._process_average_premium(result["result"])
    else:
        self.logger.error("MPC client not available")
```

## Technical Details

### MPC Protocols

The MPC Client supports various MPC protocols, including:

- **Secret Sharing**: Divide sensitive data into shares that reveal nothing individually
- **Garbled Circuits**: Encrypt computation circuits for secure evaluation
- **Homomorphic Encryption**: Perform computations on encrypted data without decrypting it
- **Oblivious Transfer**: Securely transfer data without revealing what was transferred

### Security Guarantees

The MPC Client provides the following security guarantees:

- **Input Privacy**: Input data is not revealed to any party
- **Computation Correctness**: Results are correct even if some parties are malicious
- **Output Privacy**: Results are only revealed to authorized parties

### Error Handling

The MPC Client includes robust error handling for various MPC-related issues:

- **Connection Failures**: Handle connection failures to MPC servers
- **Protocol Errors**: Handle errors in MPC protocols
- **Malicious Behavior**: Detect and handle malicious behavior by parties
- **Timeout Handling**: Handle timeouts in MPC computations

## Configuration

The MPC Client can be configured in the `.env` file:

```
# MPC client settings
MPC_DEFAULT_SERVER=mpc-server-1
MPC_API_KEY_1=your_api_key_1
MPC_API_KEY_2=your_api_key_2
```

## Future Enhancements

- **Additional Computation Types**: Support for more complex secure computations
- **Quantum-Resistant MPC**: Integration with post-quantum cryptography
- **Federated Learning**: Secure machine learning across multiple parties
- **Threshold Signatures**: Secure multi-party digital signatures
- **Secure Multi-Party Machine Learning**: Train machine learning models without revealing training data
