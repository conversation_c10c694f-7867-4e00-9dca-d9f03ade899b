# Calendly OAuth Integration Guide

This guide explains how to set up OAuth authentication for Calendly integration with the Insurance Lead Agent.

## Overview

Calendly uses OAuth 2.0 for API authentication, which is more secure than using a Personal Access Token. This guide will walk you through the process of setting up OAuth authentication for Calendly.

## Step 1: Create a Calendly OAuth Application

1. Log in to your Calendly account at https://calendly.com/
2. Go to your account settings (click on your profile icon in the top-right corner)
3. Navigate to "Integrations" > "API & Webhooks"
4. Click on "Create New App" or "Register a New App"
5. Fill in the application details:
   - **App Name**: Insurance Lead Agent
   - **Description**: AI agent for handling insurance leads
   - **Redirect URI**: http://localhost:8000/callback
   - **Scopes**: Select all scopes you need (at minimum: `read_events`, `read_user`, `webhook_subscriptions:manage`)
6. Click "Create App" to create your OAuth application
7. Note down your **Client ID** and **Client Secret** - you'll need these later

## Step 2: Set Up the OAuth Flow

Run the `setup_calendly_oauth.py` script to start the OAuth flow:

```bash
python setup_calendly_oauth.py
```

This script will:
1. Ask for your Client ID
2. Open your browser to the Calendly authorization page
3. Start a local server to receive the authorization callback
4. Save the authorization code to a file

## Step 3: Exchange the Authorization Code for an Access Token

Run the `get_calendly_token.py` script to exchange the authorization code for an access token:

```bash
python get_calendly_token.py
```

This script will:
1. Ask for your Client ID and Client Secret
2. Ask for the authorization code (which should be saved from the previous step)
3. Exchange the code for an access token
4. Save the access token to the credentials file
5. Test the token to make sure it works

## Step 4: Test the Calendly API

Now that you have an access token, you can test the Calendly API:

```bash
python test_calendly_api.py --event-types --save-config
```

This will:
1. Test if your access token works
2. Get your event types from Calendly
3. Save the event types to the configuration file

## Step 5: Set Up Webhooks

To receive real-time notifications when leads schedule or cancel appointments, you need to set up webhooks:

```bash
python setup_ngrok.py
```

This script will:
1. Start ngrok to create a tunnel to your local server
2. Create a webhook subscription in Calendly pointing to your ngrok URL
3. Save the webhook information to a file

## Step 6: Start the Webhook Receiver

In a separate terminal, start the webhook receiver:

```bash
python calendly_webhook_receiver.py
```

This will start a local server that listens for webhook events from Calendly.

## Understanding the OAuth Flow

The OAuth flow consists of the following steps:

1. **Authorization Request**: The user is redirected to Calendly's authorization page
2. **User Consent**: The user grants permission to the application
3. **Authorization Code**: Calendly redirects back to the application with an authorization code
4. **Token Exchange**: The application exchanges the authorization code for an access token
5. **API Access**: The application uses the access token to make API calls

## Refreshing the Access Token

Access tokens expire after a certain period (typically 1 hour). To continue using the API, you need to refresh the token:

```bash
python refresh_calendly_token.py
```

This script will:
1. Check if the access token is expired
2. If it is, use the refresh token to get a new access token
3. Save the new access token to the credentials file

## Troubleshooting

### Authorization Error

If you see an error like "Error 403: access_denied", it could be because:
- The application hasn't been approved by Calendly
- The user hasn't granted permission to the application
- The scopes requested are not allowed for the application

### Invalid Client Error

If you see an error like "invalid_client", it could be because:
- The Client ID or Client Secret is incorrect
- The application hasn't been registered with Calendly

### Invalid Grant Error

If you see an error like "invalid_grant", it could be because:
- The authorization code has expired (they typically expire after 10 minutes)
- The authorization code has already been used
- The redirect URI doesn't match the one registered with the application

## API Reference

### setup_calendly_oauth.py

```
usage: setup_calendly_oauth.py

This script helps you set up OAuth for Calendly.
```

### get_calendly_token.py

```
usage: get_calendly_token.py

This script helps you exchange an authorization code for an OAuth token.
```

### test_calendly_api.py

```
usage: test_calendly_api.py [--token TOKEN] [--event-types] [--scheduled-events] [--save-config]

Test Calendly API

options:
  --token TOKEN         Calendly API key
  --event-types         Get event types
  --scheduled-events    Get scheduled events
  --save-config         Save event types to config
```

### refresh_calendly_token.py

```
usage: refresh_calendly_token.py

This script refreshes your Calendly OAuth token.
```
