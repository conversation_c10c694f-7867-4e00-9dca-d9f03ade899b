"""
Start UI-TARS with Jarvis Integration.

This script starts UI-TARS with full <PERSON> integration, initializing all necessary
components and establishing connections between them.
"""
import asyncio
import argparse
import logging
import os
import sys
import signal
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent))

try:
    from core.logger import setup_logger
    from core.state_manager import StateManager
    from borg_cluster.borg_resource_manager import BorgResourceManager
    from borg_cluster.borg_load_balancer import BorgLoadBalancer
    from borg_cluster.jarvis_interface import JarvisInterface
    from alpha_evolve.alpha_evolve_engine import AlphaEvolveEngine
    from alpha_evolve.integration.borg_integration import BorgIntegration
    from alpha_evolve.integration.jarvis_integration import JarvisAlphaEvolveCommands
    from alpha_evolve.integration.agent_integration import AgentIntegration
    from core.agent_manager import AgentManager
    from ui_tars.integration.jarvis_ui_tars_integration import JarvisUITarsIntegration
except ImportError as e:
    print(f"Error importing required modules: {e}")
    print("Make sure you're running this script from the project root directory.")
    sys.exit(1)

# Set up logger
logger = setup_logger("start_ui_tars_with_jarvis")

# Global flag to control system shutdown
shutdown_event = asyncio.Event()

async def initialize_components():
    """Initialize all components required for UI-TARS with Jarvis integration."""
    logger.info("Initializing components for UI-TARS with Jarvis integration")
    
    try:
        # Initialize state manager
        logger.info("Initializing State Manager")
        state_manager = StateManager()
        await state_manager.initialize()
        
        # Initialize agent manager
        logger.info("Initializing Agent Manager")
        agent_manager = AgentManager(state_manager=state_manager)
        await agent_manager.initialize()
        
        # Initialize Borg resource manager
        logger.info("Initializing Borg Resource Manager")
        resource_manager = BorgResourceManager(state_manager=state_manager)
        await resource_manager.initialize()
        
        # Initialize Borg load balancer
        logger.info("Initializing Borg Load Balancer")
        load_balancer = BorgLoadBalancer(
            resource_manager=resource_manager,
            state_manager=state_manager
        )
        await load_balancer.initialize()
        
        # Initialize AlphaEvolve engine
        logger.info("Initializing AlphaEvolve Engine")
        alpha_evolve_engine = AlphaEvolveEngine(state_manager=state_manager)
        await alpha_evolve_engine.initialize()
        
        # Initialize Borg integration
        logger.info("Initializing Borg Integration")
        borg_integration = BorgIntegration(
            alpha_evolve_engine=alpha_evolve_engine,
            resource_manager=resource_manager,
            load_balancer=load_balancer
        )
        await borg_integration.initialize()
        
        # Initialize agent integration
        logger.info("Initializing Agent Integration")
        agent_integration = AgentIntegration(
            alpha_evolve_engine=alpha_evolve_engine,
            agent_manager=agent_manager
        )
        await agent_integration.initialize()
        
        # Initialize Jarvis interface
        logger.info("Initializing Jarvis Interface")
        jarvis_interface = JarvisInterface(
            resource_manager=resource_manager,
            load_balancer=load_balancer,
            agent_manager=agent_manager,
            state_manager=state_manager
        )
        await jarvis_interface.initialize()
        
        # Initialize Jarvis AlphaEvolve commands
        logger.info("Initializing Jarvis AlphaEvolve Commands")
        jarvis_commands = JarvisAlphaEvolveCommands(
            jarvis_interface=jarvis_interface,
            alpha_evolve_engine=alpha_evolve_engine,
            borg_integration=borg_integration,
            agent_integration=agent_integration
        )
        await jarvis_commands.initialize()
        
        # Initialize Jarvis UI-TARS integration
        logger.info("Initializing Jarvis UI-TARS Integration")
        
        # Load UI-TARS configuration
        ui_tars_config_path = os.path.join("ui_tars", "config.json")
        ui_tars_config = {}
        
        if os.path.exists(ui_tars_config_path):
            import json
            with open(ui_tars_config_path, "r") as f:
                ui_tars_config = json.load(f)
        
        # Create Jarvis UI-TARS integration
        jarvis_ui_tars_integration = JarvisUITarsIntegration(
            jarvis_interface=jarvis_interface,
            config=ui_tars_config
        )
        await jarvis_ui_tars_integration.initialize()
        
        logger.info("All components initialized successfully")
        
        return {
            "state_manager": state_manager,
            "agent_manager": agent_manager,
            "resource_manager": resource_manager,
            "load_balancer": load_balancer,
            "alpha_evolve_engine": alpha_evolve_engine,
            "borg_integration": borg_integration,
            "agent_integration": agent_integration,
            "jarvis_interface": jarvis_interface,
            "jarvis_commands": jarvis_commands,
            "jarvis_ui_tars_integration": jarvis_ui_tars_integration
        }
    
    except Exception as e:
        logger.exception(f"Error initializing components: {e}")
        raise

async def start_ui_tars_with_jarvis(args):
    """
    Start UI-TARS with Jarvis integration.
    
    Args:
        args: Command-line arguments
    """
    logger.info("Starting UI-TARS with Jarvis integration")
    
    try:
        # Initialize components
        components = await initialize_components()
        
        # Display welcome message
        jarvis_interface = components["jarvis_interface"]
        
        welcome_message = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║                  UI-TARS WITH JARVIS INTEGRATION ACTIVE                      ║
║                                                                              ║
║  UI-TARS 1.5's browser automation capabilities are now integrated with       ║
║  the Jarvis interface to:                                                    ║
║                                                                              ║
║  1. Enable reliable browser automation for all agents                        ║
║  2. Provide Gmail and Google Voice integration                               ║
║  3. Support both Paul Edwards and Sandra Garcia personas                     ║
║  4. Allow autonomous operation with minimal human intervention               ║
║                                                                              ║
║  Type 'help' to see available commands.                                      ║
║  Type 'ui-tars-browse <url>' to browse a website.                            ║
║  Type 'gmail-send <to> <subject> <body>' to send an email.                   ║
║  Type 'voice-text <phone_number> <message>' to send a text message.          ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
"""
        print(welcome_message)
        
        # Start Jarvis interface
        if args.interactive:
            await jarvis_interface.start_interactive_mode()
        else:
            # Wait for shutdown signal
            await shutdown_event.wait()
        
        # Shutdown components
        await shutdown_components(components)
    
    except Exception as e:
        logger.exception(f"Error starting UI-TARS with Jarvis: {e}")
        sys.exit(1)

async def shutdown_components(components):
    """
    Shutdown all components.
    
    Args:
        components: Dictionary of components
    """
    logger.info("Shutting down components")
    
    try:
        # Shutdown in reverse order of initialization
        if "jarvis_ui_tars_integration" in components:
            logger.info("Shutting down Jarvis UI-TARS Integration")
            await components["jarvis_ui_tars_integration"].shutdown()
        
        if "jarvis_commands" in components:
            logger.info("Shutting down Jarvis AlphaEvolve Commands")
            # No shutdown method for jarvis_commands
        
        if "jarvis_interface" in components:
            logger.info("Shutting down Jarvis Interface")
            await components["jarvis_interface"].shutdown()
        
        if "agent_integration" in components:
            logger.info("Shutting down Agent Integration")
            await components["agent_integration"].shutdown()
        
        if "borg_integration" in components:
            logger.info("Shutting down Borg Integration")
            await components["borg_integration"].shutdown()
        
        if "alpha_evolve_engine" in components:
            logger.info("Shutting down AlphaEvolve Engine")
            await components["alpha_evolve_engine"].shutdown()
        
        if "load_balancer" in components:
            logger.info("Shutting down Borg Load Balancer")
            await components["load_balancer"].shutdown()
        
        if "resource_manager" in components:
            logger.info("Shutting down Borg Resource Manager")
            await components["resource_manager"].shutdown()
        
        if "agent_manager" in components:
            logger.info("Shutting down Agent Manager")
            await components["agent_manager"].shutdown()
        
        if "state_manager" in components:
            logger.info("Shutting down State Manager")
            await components["state_manager"].shutdown()
        
        logger.info("All components shut down successfully")
    
    except Exception as e:
        logger.exception(f"Error shutting down components: {e}")

def signal_handler():
    """Handle termination signals."""
    logger.info("Received termination signal")
    shutdown_event.set()

def main():
    """Main entry point."""
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="Start UI-TARS with Jarvis Integration")
    parser.add_argument("--interactive", action="store_true", help="Start in interactive mode")
    parser.add_argument("--log-level", type=str, default="INFO", help="Logging level")
    args = parser.parse_args()
    
    # Set up signal handlers
    for sig in (signal.SIGINT, signal.SIGTERM):
        signal.signal(sig, lambda signum, frame: signal_handler())
    
    # Start UI-TARS with Jarvis
    try:
        asyncio.run(start_ui_tars_with_jarvis(args))
    except KeyboardInterrupt:
        print("\nShutting down UI-TARS with Jarvis...")
        sys.exit(0)

if __name__ == "__main__":
    main()
