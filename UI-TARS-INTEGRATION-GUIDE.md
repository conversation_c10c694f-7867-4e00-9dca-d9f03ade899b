# UI-TARS Integration Guide

This guide provides comprehensive instructions for integrating UI-TARS with your AI agent system.

## Overview

UI-TARS is a powerful tool for autonomous browser control and desktop automation. By integrating it with your AI agent system, you can enable your agents to:

- Browse websites and extract information
- Fill out forms and submit them
- Search the web and summarize results
- Take screenshots and analyze visual content
- Automate desktop applications
- Perform complex multi-step tasks

This integration uses a local API server to connect UI-TARS with your AI agent system, allowing UI-TARS to work with your local LLMs without requiring internet access or paid subscriptions.

## Components

The integration consists of the following components:

1. **Local API Server**: A server that mimics the Hugging Face API but uses your local models
2. **UI-TARS Agent**: A Python class that provides an interface for your agents to use UI-TARS capabilities
3. **Task Examples**: Example scripts that demonstrate how to use UI-TARS to complete specific tasks

## Setup Instructions

### Step 1: Start the Local API Server

1. Open a command prompt
2. Navigate to the directory containing the server scripts
3. Run the following command:
   ```
   .\start_simple_server.bat
   ```
4. Keep the server running in the background

### Step 2: Configure UI-TARS

1. Open UI-TARS 1.5
2. Go to Settings
3. Configure the VLM settings:
   - **VLM Provider**: Hugging Face
   - **VLM Base URL**: http://127.0.0.1:8000
   - **VLM API Key**: dummy_key
   - **VLM Model Name**: UI-TARS-1.5-7B

4. Configure the Chat settings:
   - **Max Tokens**: 2048
   - **Context Window**: 4096

5. Leave the Report settings blank

6. Click "Save" or "Apply" to apply the configuration

### Step 3: Import the UI-TARS Agent Module

In your AI agent system code, import the UI-TARS Agent module:

```python
from ui_tars_agent_integration import UITarsAgent
```

### Step 4: Create and Initialize a UI-TARS Agent

```python
import asyncio

async def main():
    # Create a UI-TARS Agent
    agent = UITarsAgent()
    
    # Initialize the agent
    await agent.initialize()
    
    # Use the agent to perform tasks
    # ...
    
    # Shut down the agent when done
    await agent.shutdown()

if __name__ == "__main__":
    asyncio.run(main())
```

## Using UI-TARS in Your AI Agent System

### Browsing Websites

```python
# Browse a website and perform a task
result = await agent.browse_website("https://www.example.com", "find the contact information")
```

### Searching the Web

```python
# Search the web for information
result = await agent.search_web("UI-TARS documentation")
```

### Filling Forms

```python
# Fill out a form on a website
form_data = {
    "name": "John Doe",
    "email": "<EMAIL>",
    "subject": "Inquiry",
    "message": "Hello, I would like more information."
}
result = await agent.fill_form("https://www.example.com/contact", form_data)
```

### Taking Screenshots

```python
# Take a screenshot
result = await agent.take_screenshot()
```

### Extracting Data

```python
# Extract data from a website
result = await agent.extract_data("https://www.example.com", "product prices")
```

### Executing Custom Commands

```python
# Execute a custom command
result = await agent.execute_command("Open the calculator app and perform a calculation")
```

## Example Tasks

### Search and Summarize

This task demonstrates how to search the web for information and summarize the results:

```bash
python ui_tars_task_example.py --task search --query "UI-TARS documentation"
```

### Fill Contact Form

This task demonstrates how to fill out a contact form on a website:

```bash
python ui_tars_task_example.py --task form --website "https://www.example.com"
```

### Extract Product Information

This task demonstrates how to extract product information from a website:

```bash
python ui_tars_task_example.py --task extract --website "https://www.example.com" --product "electronics"
```

## Integrating with Your Existing Agents

To integrate UI-TARS with your existing agents, you can:

1. Create a UI-TARS Agent for each of your existing agents
2. Add UI-TARS capabilities to your agent's skill set
3. Use the UI-TARS Agent to perform tasks that require browser control or desktop automation

For example:

```python
class MyAgent:
    def __init__(self):
        self.ui_tars_agent = UITarsAgent()
        
    async def initialize(self):
        await self.ui_tars_agent.initialize()
        
    async def perform_task(self, task):
        if task.requires_browser():
            # Use UI-TARS for browser tasks
            result = await self.ui_tars_agent.browse_website(task.url, task.description)
        elif task.requires_desktop():
            # Use UI-TARS for desktop tasks
            result = await self.ui_tars_agent.execute_command(task.command)
        else:
            # Use other capabilities for non-UI tasks
            result = await self.perform_non_ui_task(task)
        
        return result
        
    async def shutdown(self):
        await self.ui_tars_agent.shutdown()
```

## Offline Operation

This integration allows UI-TARS to work completely offline:

1. The local API server runs on your machine and doesn't require internet access
2. UI-TARS communicates with the local server instead of the Hugging Face API
3. Your local LLMs run on your machine and don't require internet access

The only time internet access is required is when you specifically ask UI-TARS to perform a task that requires internet access, such as browsing a website or searching for information.

## Troubleshooting

### Server Issues

If the local API server is not responding:

1. Check if the server is running
2. Restart the server using `.\start_simple_server.bat`
3. Check the server logs for any error messages

### UI-TARS Issues

If UI-TARS is not responding:

1. Check if UI-TARS is running
2. Restart UI-TARS
3. Verify that the settings are correct

### Integration Issues

If the integration is not working:

1. Check if the UI-TARS Agent is properly initialized
2. Verify that the API URL and key are correct
3. Check the logs for any error messages

## Next Steps

After integrating UI-TARS with your AI agent system, you can:

1. Create more complex tasks that combine multiple UI-TARS capabilities
2. Train your agents to use UI-TARS effectively
3. Develop custom workflows for specific use cases
4. Extend the UI-TARS Agent with additional capabilities

Remember to keep the local API server running whenever you're using UI-TARS with your AI agent system.
