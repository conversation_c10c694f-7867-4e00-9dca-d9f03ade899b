"""
Insurance Agent Communication Module for the Multi-Agent AI System.

This module extends the InsuranceAgent class with communication capabilities,
including making calls, sending texts, setting appointments, and more.
"""
import asyncio
import json
import logging
import os
import sys
import time
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta

# Add parent directory to path to import from core
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.logger import setup_logger
from agents.insurance_agent import InsuranceAgent
from services.voice_calling_service import VoiceCallingService
from services.calendar_integration import CalendarIntegration
from services.agent_security_tools_provider import AgentSecurityToolsProvider

# Set up logger
logger = setup_logger("insurance_agent_communication")

class InsuranceAgentCommunication(InsuranceAgent):
    """
    Insurance Agent with Communication Capabilities.

    This class extends the InsuranceAgent class with communication capabilities,
    including making calls, sending texts, setting appointments, and more.
    """

    def __init__(
        self,
        agent_id: str,
        config: Dict,
        state_manager,
        message_queue,
        shutdown_event
    ):
        """Initialize the insurance agent with communication capabilities."""
        super().__init__(agent_id, config, state_manager, message_queue, shutdown_event)

        # Communication services
        self.voice_calling_service = None
        self.calendar_integration = None

        # Communication templates
        self.call_templates = config.get("call_templates", {})
        self.text_templates = config.get("text_templates", {})
        self.voicemail_templates = config.get("voicemail_templates", {})
        self.email_templates = config.get("email_templates", {})

        # Appointment types
        self.appointment_types = config.get("appointment_types", {})

        # Communication history
        self.call_history = []
        self.text_history = []
        self.voicemail_history = []
        self.email_history = []
        self.appointment_history = []

        # Add communication capabilities
        self.capabilities.extend([
            "make_calls",
            "send_texts",
            "leave_voicemails",
            "schedule_appointments",
            "send_emails",
            "appointment_reminders",
            "follow_up_calls",
            "lead_generation",
            "customer_outreach",
            "appointment_setting",
            "calendly_integration",
            "google_calendar_integration",
            "twilio_integration",
            "elevenlabs_integration",
            "bland_ai_integration",
            "air_ai_integration",
        ])

        logger.info(f"Insurance agent communication module initialized for agent {agent_id}")

    async def initialize(self):
        """Initialize the insurance agent with communication capabilities."""
        await super().initialize()

        try:
            # Initialize communication services
            self.voice_calling_service = self.get_service("voice_calling_service")
            self.calendar_integration = self.get_service("calendar_integration")

            # Load communication history from state
            await self._load_communication_history()

            logger.info(f"Insurance agent communication module initialized for agent {self.agent_id}")

        except Exception as e:
            logger.exception(f"Error initializing insurance agent communication module: {e}")

    async def _load_communication_history(self):
        """Load communication history from state manager."""
        # Load call history
        call_history = await self.state_manager.get_state("insurance", f"call_history_{self.agent_id}")
        if call_history:
            self.call_history = call_history
            logger.info(f"Loaded {len(self.call_history)} call records")

        # Load text history
        text_history = await self.state_manager.get_state("insurance", f"text_history_{self.agent_id}")
        if text_history:
            self.text_history = text_history
            logger.info(f"Loaded {len(self.text_history)} text records")

        # Load voicemail history
        voicemail_history = await self.state_manager.get_state("insurance", f"voicemail_history_{self.agent_id}")
        if voicemail_history:
            self.voicemail_history = voicemail_history
            logger.info(f"Loaded {len(self.voicemail_history)} voicemail records")

        # Load email history
        email_history = await self.state_manager.get_state("insurance", f"email_history_{self.agent_id}")
        if email_history:
            self.email_history = email_history
            logger.info(f"Loaded {len(self.email_history)} email records")

        # Load appointment history
        appointment_history = await self.state_manager.get_state("insurance", f"appointment_history_{self.agent_id}")
        if appointment_history:
            self.appointment_history = appointment_history
            logger.info(f"Loaded {len(self.appointment_history)} appointment records")

    async def _save_communication_history(self):
        """Save communication history to state manager."""
        # Save call history
        await self.state_manager.update_state("insurance", f"call_history_{self.agent_id}", self.call_history)

        # Save text history
        await self.state_manager.update_state("insurance", f"text_history_{self.agent_id}", self.text_history)

        # Save voicemail history
        await self.state_manager.update_state("insurance", f"voicemail_history_{self.agent_id}", self.voicemail_history)

        # Save email history
        await self.state_manager.update_state("insurance", f"email_history_{self.agent_id}", self.email_history)

        # Save appointment history
        await self.state_manager.update_state("insurance", f"appointment_history_{self.agent_id}", self.appointment_history)

    async def execute_cycle(self):
        """Execute one cycle of the insurance agent's logic."""
        await super().execute_cycle()

        try:
            # Check for appointment reminders
            await self._check_appointment_reminders()

            # Check for follow-up tasks
            await self._check_follow_up_tasks()

            # Save communication history
            await self._save_communication_history()

        except Exception as e:
            logger.exception(f"Error in insurance agent communication cycle: {e}")

    async def make_call(self, phone_number: str, template_name: str, template_vars: Dict = None, options: Dict = None) -> Dict:
        """
        Make a phone call using a template.

        Args:
            phone_number (str): Phone number to call
            template_name (str): Template name
            template_vars (Dict, optional): Template variables
            options (Dict, optional): Call options

        Returns:
            Dict: Call result
        """
        if not self.voice_calling_service:
            return {"error": "Voice calling service not available"}

        try:
            # Get call template
            if template_name not in self.call_templates:
                return {"error": f"Unknown call template: {template_name}"}

            template = self.call_templates[template_name]

            # Prepare template variables
            template_vars = template_vars or {}
            template_vars["agent_name"] = template_vars.get("agent_name", self.config.get("agent_name", "Insurance Agent"))

            # Format script
            script = template["script"].format(**template_vars)

            # Prepare call options
            options = options or {}

            # Get voice settings
            voice_type = template.get("voice_type", "female")
            voice_style = template.get("voice_style", "professional")

            # Get voice ID based on provider
            provider = options.get("provider", self.voice_calling_service.default_voice_provider)
            voice_settings = self.voice_calling_service.config.get("voice_settings", {}).get(provider, {})
            voices = voice_settings.get("voices", {}).get(voice_type, {})
            voice_id = voices.get(voice_style)

            if voice_id:
                options["voice_id"] = voice_id

            # Set other options from template
            options["max_duration"] = options.get("max_duration", template.get("max_duration", 300))
            options["record"] = options.get("record", template.get("record", True))

            # Make call
            result = await self.voice_calling_service.make_call(phone_number, script, options)

            # Store call in history if successful
            if result.get("success") and result.get("call_id"):
                call_record = {
                    "call_id": result["call_id"],
                    "phone_number": phone_number,
                    "template_name": template_name,
                    "template_vars": template_vars,
                    "options": options,
                    "status": result.get("status"),
                    "created_at": datetime.now().isoformat(),
                    "details": result
                }

                self.call_history.append(call_record)

            return result

        except Exception as e:
            logger.exception(f"Error making call: {e}")
            return {"error": str(e)}

    async def send_text_message(self, phone_number: str, template_name: str, template_vars: Dict = None, options: Dict = None) -> Dict:
        """
        Send a text message using a template.

        Args:
            phone_number (str): Phone number to send message to
            template_name (str): Template name
            template_vars (Dict, optional): Template variables
            options (Dict, optional): Message options

        Returns:
            Dict: Message result
        """
        if not self.voice_calling_service:
            return {"error": "Voice calling service not available"}

        try:
            # Get text template
            if template_name not in self.text_templates:
                return {"error": f"Unknown text template: {template_name}"}

            template = self.text_templates[template_name]

            # Prepare template variables
            template_vars = template_vars or {}
            template_vars["agent_name"] = template_vars.get("agent_name", self.config.get("agent_name", "Insurance Agent"))

            # Format message
            message = template.format(**template_vars)

            # Send text message
            result = await self.voice_calling_service.send_text_message(phone_number, message, options)

            # Store text in history if successful
            if result.get("success") and result.get("message_id"):
                text_record = {
                    "message_id": result["message_id"],
                    "phone_number": phone_number,
                    "template_name": template_name,
                    "template_vars": template_vars,
                    "options": options,
                    "status": result.get("status"),
                    "created_at": datetime.now().isoformat(),
                    "details": result
                }

                self.text_history.append(text_record)

            return result

        except Exception as e:
            logger.exception(f"Error sending text message: {e}")
            return {"error": str(e)}

    async def leave_voicemail(self, phone_number: str, template_name: str, template_vars: Dict = None, options: Dict = None) -> Dict:
        """
        Leave a voicemail using a template.

        Args:
            phone_number (str): Phone number to leave voicemail for
            template_name (str): Template name
            template_vars (Dict, optional): Template variables
            options (Dict, optional): Voicemail options

        Returns:
            Dict: Voicemail result
        """
        if not self.voice_calling_service:
            return {"error": "Voice calling service not available"}

        try:
            # Get voicemail template
            if template_name not in self.voicemail_templates:
                return {"error": f"Unknown voicemail template: {template_name}"}

            template = self.voicemail_templates[template_name]

            # Prepare template variables
            template_vars = template_vars or {}
            template_vars["agent_name"] = template_vars.get("agent_name", self.config.get("agent_name", "Insurance Agent"))

            # Format message
            message = template.format(**template_vars)

            # Leave voicemail
            result = await self.voice_calling_service.leave_voicemail(phone_number, message, options)

            # Store voicemail in history if successful
            if result.get("success") and result.get("voicemail_id"):
                voicemail_record = {
                    "voicemail_id": result["voicemail_id"],
                    "phone_number": phone_number,
                    "template_name": template_name,
                    "template_vars": template_vars,
                    "options": options,
                    "status": result.get("status"),
                    "created_at": datetime.now().isoformat(),
                    "details": result
                }

                self.voicemail_history.append(voicemail_record)

            return result

        except Exception as e:
            logger.exception(f"Error leaving voicemail: {e}")
            return {"error": str(e)}

    async def schedule_appointment(self, appointment_data: Dict) -> Dict:
        """
        Schedule an appointment.

        Args:
            appointment_data (Dict): Appointment data
                - client_name (str): Client name
                - client_email (str, optional): Client email
                - client_phone (str, optional): Client phone
                - appointment_type (str): Appointment type
                - appointment_date (str): Appointment date
                - appointment_time (str): Appointment time
                - duration (int, optional): Duration in minutes
                - notes (str, optional): Appointment notes

        Returns:
            Dict: Appointment result
        """
        if not self.calendar_integration:
            return {"error": "Calendar integration not available"}

        try:
            # Get appointment data
            client_name = appointment_data.get("client_name")
            client_email = appointment_data.get("client_email")
            client_phone = appointment_data.get("client_phone")
            appointment_type = appointment_data.get("appointment_type")
            appointment_date = appointment_data.get("appointment_date")
            appointment_time = appointment_data.get("appointment_time")
            duration = appointment_data.get("duration", 30)
            notes = appointment_data.get("notes", "")

            # Validate required fields
            if not client_name or not appointment_type or not appointment_date or not appointment_time:
                return {"error": "Missing required fields: client_name, appointment_type, appointment_date, appointment_time"}

            # Get appointment type details
            if appointment_type not in self.appointment_types:
                return {"error": f"Unknown appointment type: {appointment_type}"}

            appointment_type_details = self.appointment_types[appointment_type]

            # Parse appointment date and time
            try:
                # Combine date and time
                appointment_datetime = f"{appointment_date} {appointment_time}"

                # Parse datetime
                start_time = datetime.strptime(appointment_datetime, "%Y-%m-%d %H:%M")

                # Calculate end time
                end_time = start_time + timedelta(minutes=duration)

                # Format as ISO 8601
                start_time_iso = start_time.isoformat()
                end_time_iso = end_time.isoformat()

            except ValueError:
                return {"error": f"Invalid date or time format: {appointment_date} {appointment_time}"}

            # Prepare event data
            event_data = {
                "summary": f"{appointment_type_details['title']} - {client_name}",
                "description": f"{appointment_type_details['description']}\n\nClient: {client_name}\nPhone: {client_phone}\nEmail: {client_email}\n\nNotes: {notes}",
                "start_time": start_time_iso,
                "end_time": end_time_iso,
                "location": appointment_type_details.get("location", "Phone Call"),
                "calendar_id": appointment_type_details.get("calendar_id", "primary")
            }

            # Add attendees if email is provided
            if client_email:
                event_data["attendees"] = [client_email]

            # Create event in Google Calendar
            result = await self.calendar_integration.create_google_calendar_event(event_data)

            # Store appointment in history if successful
            if result.get("success") and result.get("event_id"):
                appointment_record = {
                    "event_id": result["event_id"],
                    "client_name": client_name,
                    "client_email": client_email,
                    "client_phone": client_phone,
                    "appointment_type": appointment_type,
                    "start_time": start_time_iso,
                    "end_time": end_time_iso,
                    "notes": notes,
                    "created_at": datetime.now().isoformat(),
                    "details": result
                }

                self.appointment_history.append(appointment_record)

                # Send confirmation if phone number is provided
                if client_phone and self.voice_calling_service:
                    template_vars = {
                        "client_name": client_name,
                        "appointment_date": appointment_date,
                        "appointment_time": appointment_time,
                        "phone_number": self.voice_calling_service.default_from_number
                    }

                    await self.send_text_message(client_phone, "appointment_confirmation", template_vars)

            return result

        except Exception as e:
            logger.exception(f"Error scheduling appointment: {e}")
            return {"error": str(e)}

    async def parse_appointment_request(self, text: str) -> Dict:
        """
        Parse an appointment request from text.

        Args:
            text (str): Text containing appointment request

        Returns:
            Dict: Parsed appointment data
        """
        if not self.calendar_integration:
            return {"error": "Calendar integration not available"}

        try:
            # Use calendar integration to parse appointment request
            result = await self.calendar_integration.parse_appointment_request(text)

            return result

        except Exception as e:
            logger.exception(f"Error parsing appointment request: {e}")
            return {"error": str(e)}

    async def get_available_appointment_slots(self, start_date: str = None, end_date: str = None, appointment_type: str = None) -> Dict:
        """
        Get available appointment slots.

        Args:
            start_date (str, optional): Start date in YYYY-MM-DD format
            end_date (str, optional): End date in YYYY-MM-DD format
            appointment_type (str, optional): Appointment type

        Returns:
            Dict: Available appointment slots
        """
        if not self.calendar_integration:
            return {"error": "Calendar integration not available"}

        try:
            # Set default dates if not provided
            if not start_date:
                start_date = datetime.now().strftime("%Y-%m-%d")

            if not end_date:
                end_date = (datetime.now() + timedelta(days=7)).strftime("%Y-%m-%d")

            # Parse dates
            start_datetime = datetime.strptime(start_date, "%Y-%m-%d")
            end_datetime = datetime.strptime(end_date, "%Y-%m-%d")

            # Format as ISO 8601
            start_time_iso = start_datetime.isoformat() + "Z"
            end_time_iso = end_datetime.isoformat() + "Z"

            # Get calendar ID based on appointment type
            calendar_id = "primary"
            if appointment_type and appointment_type in self.appointment_types:
                calendar_id = self.appointment_types[appointment_type].get("calendar_id", "primary")

            # Get events from Google Calendar
            events_result = await self.calendar_integration.get_google_calendar_events({
                "calendar_id": calendar_id,
                "time_min": start_time_iso,
                "time_max": end_time_iso,
                "max_results": 100
            })

            if not events_result.get("success"):
                return {"error": "Failed to get events from Google Calendar"}

            # Extract busy times
            busy_times = []
            for event in events_result.get("events", []):
                start = event.get("start", {}).get("dateTime")
                end = event.get("end", {}).get("dateTime")

                if start and end:
                    busy_times.append({
                        "start": start,
                        "end": end
                    })

            # Generate available slots
            available_slots = self._generate_available_slots(start_datetime, end_datetime, busy_times, appointment_type)

            return {
                "success": True,
                "start_date": start_date,
                "end_date": end_date,
                "appointment_type": appointment_type,
                "available_slots": available_slots
            }

        except Exception as e:
            logger.exception(f"Error getting available appointment slots: {e}")
            return {"error": str(e)}

    def _generate_available_slots(self, start_date: datetime, end_date: datetime, busy_times: List[Dict], appointment_type: str = None) -> List[Dict]:
        """
        Generate available appointment slots.

        Args:
            start_date (datetime): Start date
            end_date (datetime): End date
            busy_times (List[Dict]): List of busy time slots
            appointment_type (str, optional): Appointment type

        Returns:
            List[Dict]: Available appointment slots
        """
        # Get appointment duration
        duration = 30  # Default duration in minutes
        if appointment_type and appointment_type in self.appointment_types:
            duration = self.appointment_types[appointment_type].get("duration", 30)

        # Define business hours
        business_hours = {
            0: [],  # Monday (0 = Monday in Python's datetime)
            1: [],  # Tuesday
            2: [],  # Wednesday
            3: [],  # Thursday
            4: [],  # Friday
            5: [],  # Saturday
            6: []   # Sunday
        }

        # Set default business hours (9 AM to 5 PM, Monday to Friday)
        for day in range(5):  # Monday to Friday
            business_hours[day] = [{"start": "09:00", "end": "17:00"}]

        # Override with appointment type specific hours if available
        if appointment_type and appointment_type in self.appointment_types:
            type_hours = self.appointment_types[appointment_type].get("business_hours")
            if type_hours:
                business_hours = type_hours

        # Generate all possible slots
        all_slots = []
        current_date = start_date

        while current_date <= end_date:
            # Get day of week (0 = Monday in Python's datetime)
            day_of_week = current_date.weekday()

            # Check if there are business hours for this day
            day_hours = business_hours.get(day_of_week, [])

            for hours in day_hours:
                # Parse business hours
                start_hour, start_minute = map(int, hours["start"].split(":"))
                end_hour, end_minute = map(int, hours["end"].split(":"))

                # Create slots at regular intervals
                slot_start = current_date.replace(hour=start_hour, minute=start_minute, second=0, microsecond=0)
                slot_end = current_date.replace(hour=end_hour, minute=end_minute, second=0, microsecond=0)

                while slot_start + timedelta(minutes=duration) <= slot_end:
                    # Check if slot overlaps with any busy time
                    is_available = True
                    slot_end_time = slot_start + timedelta(minutes=duration)

                    for busy in busy_times:
                        busy_start = datetime.fromisoformat(busy["start"].replace("Z", "+00:00"))
                        busy_end = datetime.fromisoformat(busy["end"].replace("Z", "+00:00"))

                        # Check for overlap
                        if (slot_start < busy_end and slot_end_time > busy_start):
                            is_available = False
                            break

                    if is_available:
                        all_slots.append({
                            "date": slot_start.strftime("%Y-%m-%d"),
                            "start_time": slot_start.strftime("%H:%M"),
                            "end_time": slot_end_time.strftime("%H:%M"),
                            "duration": duration
                        })

                    # Move to next slot
                    slot_start += timedelta(minutes=30)  # 30-minute intervals

            # Move to next day
            current_date += timedelta(days=1)

        return all_slots

    async def _check_appointment_reminders(self):
        """Check for appointments that need reminders."""
        current_time = datetime.now()

        for appointment in self.appointment_history:
            # Skip if reminder already sent
            if appointment.get("reminder_sent"):
                continue

            # Parse appointment time
            start_time = datetime.fromisoformat(appointment.get("start_time"))

            # Check if appointment is within 24 hours
            time_to_appointment = start_time - current_time
            hours_to_appointment = time_to_appointment.total_seconds() / 3600

            if 0 < hours_to_appointment <= 24:
                # Send reminder
                client_phone = appointment.get("client_phone")
                if client_phone and self.voice_calling_service:
                    # Format appointment date and time
                    appointment_date = start_time.strftime("%Y-%m-%d")
                    appointment_time = start_time.strftime("%H:%M")

                    template_vars = {
                        "client_name": appointment.get("client_name"),
                        "appointment_date": appointment_date,
                        "appointment_time": appointment_time,
                        "phone_number": self.voice_calling_service.default_from_number
                    }

                    # Send reminder text
                    await self.send_text_message(client_phone, "appointment_reminder", template_vars)

                    # Mark reminder as sent
                    appointment["reminder_sent"] = True

    async def _check_follow_up_tasks(self):
        """Check for follow-up tasks that need to be performed."""
        current_time = datetime.now()

        # Check for follow-up on quotes
        quotes = self.quotes or {}
        for quote_id, quote in quotes.items():
            # Skip if follow-up already sent
            if quote.get("follow_up_sent"):
                continue

            # Check if quote was created more than 3 days ago
            created_at = datetime.fromisoformat(quote.get("created_at"))
            days_since_creation = (current_time - created_at).days

            if days_since_creation >= 3:
                # Send follow-up
                customer_info = quote.get("customer_info", {})
                client_phone = customer_info.get("phone")

                if client_phone and self.voice_calling_service:
                    template_vars = {
                        "client_name": customer_info.get("name"),
                        "quote_id": quote_id,
                        "phone_number": self.voice_calling_service.default_from_number
                    }

                    # Send follow-up text
                    await self.send_text_message(client_phone, "quote_follow_up", template_vars)

                    # Mark follow-up as sent
                    quote["follow_up_sent"] = True
