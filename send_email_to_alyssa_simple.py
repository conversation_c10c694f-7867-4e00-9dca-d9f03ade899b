"""
Send Email to Alyssa - Simple Version

This script sends an email to Alyssa using yagmail, which is a simple way to send emails through Gmail.
"""
import os
import sys
import logging
import argparse
import yagmail

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("send_email_to_alyssa_simple.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("send_email_to_alyssa_simple")

# Constants
EMAIL_SUBJECT = "IUL Policy and Health Insurance Options"
EMAIL_RECIPIENT = "<EMAIL>"  # Replace with Al<PERSON>'s actual email if different

# Email template for Alyssa
EMAIL_TEMPLATE = """
Dear <PERSON><PERSON>,

Thank you for your interest in our insurance products. Based on your $100/month budget, I'd like to discuss some options for an Indexed Universal Life (IUL) policy structured for maximum cash value growth, along with basic health, dental, and vision plans.

Here's what I'm thinking:

1. IUL Policy: We can structure this for optimal cash value growth while maintaining the life insurance benefit. This would be approximately $60-70 of your monthly budget.

2. Health Insurance: For the remaining $30-40, we can look at basic health plans that cover essential services.

3. Dental & Vision: We have some affordable options that can be added if your budget allows, or we can discuss slightly exceeding your budget if these are priorities for you.

Would you be available for a quick call to discuss these options in more detail? I can answer any questions you might have and provide specific policy recommendations based on your needs.

Please let me know what days and times work best for you.

Best regards,
<PERSON> Faction Insurance
Phone: (*************
Email: <EMAIL>
"""

def send_email(sender_email, app_password, recipient_email, subject, body):
    """Send an email using yagmail."""
    logger.info(f"Sending email from {sender_email} to {recipient_email}...")
    
    try:
        # Initialize yagmail SMTP client
        yag = yagmail.SMTP(sender_email, app_password)
        
        # Send email
        yag.send(
            to=recipient_email,
            subject=subject,
            contents=body
        )
        
        logger.info("Email sent successfully")
        return True
    except Exception as e:
        logger.error(f"Error sending email: {e}")
        return False

def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Send Email to Alyssa - Simple Version")
    parser.add_argument("--email", type=str, default="<EMAIL>", help="Gmail email address")
    parser.add_argument("--password", type=str, help="Gmail app password")
    parser.add_argument("--recipient", type=str, default=EMAIL_RECIPIENT, help="Recipient email address")
    parser.add_argument("--subject", type=str, default=EMAIL_SUBJECT, help="Email subject")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    
    args = parser.parse_args()
    
    # Set log level
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        
    print("Send Email to Alyssa - Simple Version")
    print("===================================")
    print()
    
    # Check if password is provided
    if not args.password:
        print("❌ Gmail app password is required")
        print("Please generate an app password at https://myaccount.google.com/apppasswords")
        return 1
    
    # Print email details
    print("Email Details:")
    print(f"- From: {args.email}")
    print(f"- To: {args.recipient}")
    print(f"- Subject: {args.subject}")
    print(f"- Content: {EMAIL_TEMPLATE[:50]}...")
    print()
    
    # Send email
    print("Sending email...")
    success = send_email(args.email, args.password, args.recipient, args.subject, EMAIL_TEMPLATE)
    
    if success:
        print("✅ Email sent successfully to Alyssa!")
    else:
        print("❌ Failed to send email")
        return 1
        
    return 0

if __name__ == "__main__":
    sys.exit(main())
