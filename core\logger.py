"""
Logging system for the Multi-Agent AI System.
"""
import sys
import logging
from pathlib import Path
from logging.handlers import RotatingFileHandler
import config

def setup_logger(name="system", level=None):
    """
    Set up and configure the logger.

    Args:
        name (str): Logger name
        level (int, optional): Logging level. If None, uses the level from config.

    Returns:
        logging.Logger: Configured logger instance
    """
    # Create logs directory if it doesn't exist
    log_dir = config.LOG_DIR
    log_dir.mkdir(exist_ok=True)

    # Configure log file path
    log_file = log_dir / f"{name}.log"

    # Create logger
    logger = logging.getLogger(name)

    # Set log level from parameter or config
    if level is not None:
        log_level = level
    else:
        log_level = getattr(logging, config.LOG_LEVEL.upper(), logging.INFO)
    logger.setLevel(log_level)

    # Clear existing handlers if any
    if logger.handlers:
        logger.handlers.clear()

    # Create handlers
    console_handler = logging.StreamHandler(sys.stdout)
    file_handler = RotatingFileHandler(
        log_file, maxBytes=10*1024*1024, backupCount=5
    )

    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Set formatter for handlers
    console_handler.setFormatter(formatter)
    file_handler.setFormatter(formatter)

    # Add handlers to logger
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)

    return logger
