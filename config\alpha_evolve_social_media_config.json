{"code_generation": {"models": ["gemini-pro", "claude-3-sonnet"], "temperature": 0.75, "max_tokens": 2000, "top_p": 0.95, "retry_attempts": 3, "retry_delay": 1.0}, "code_evaluation": {"timeout": 10, "max_memory": "1GB", "metrics": ["correctness", "efficiency", "complexity", "engagement"], "sandbox_enabled": true, "cache_results": true, "max_cache_size": 1000}, "evolutionary_optimization": {"population_size": 45, "tournament_size": 5, "crossover_rate": 0.85, "mutation_rate": 0.15, "elitism": 2, "max_generations": 100, "fitness_threshold": 0.95, "stagnation_limit": 10, "diversity_weight": 0.2}, "prompt_engineering": {"templates_dir": "alpha_evolve/templates", "variation_strategies": ["efficiency", "readability", "novel_approach", "simplicity", "robustness"], "prompt_cache_size": 1000}, "integration": {"borg_cluster": {"enabled": true, "resource_optimization_interval": 3600, "task_scheduling_optimization_interval": 1800, "apply_optimizations_automatically": false}, "agent_system": {"enabled": true, "capability_enhancement_interval": 86400, "apply_enhancements_automatically": false, "capability_types": ["market_analysis", "portfolio_optimization", "lead_qualification", "vulnerability_scanning", "content_generation", "data_analysis"]}, "mpc_servers": {"enabled": true, "protocol_optimization_interval": 86400, "apply_optimizations_automatically": false}}, "performance_monitoring": {"enabled": true, "metrics_collection_interval": 60, "metrics_retention_period": 604800, "alert_thresholds": {"evolution_time": 3600, "fitness_improvement": 0.05, "resource_usage": 0.9}}, "self_improvement": {"enabled": true, "improvement_interval": 604800, "target_components": ["prompt_templates", "evolutionary_operators", "evaluation_metrics", "code_generation"], "max_improvement_generations": 20}, "domain": "social_media"}