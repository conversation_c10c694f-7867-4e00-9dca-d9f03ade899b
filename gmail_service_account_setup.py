"""
Gmail Service Account Setup
This script helps you set up a service account for Gmail integration.
"""
import os
import sys
import webbrowser
import subprocess
from pathlib import Path

def clear_screen():
    """Clear the terminal screen."""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_header():
    """Print the script header."""
    clear_screen()
    print("=" * 80)
    print("                     GMAIL SERVICE ACCOUNT SETUP")
    print("=" * 80)
    print("\nThis script will help you set up a service account for Gmail integration.")
    print("A service account is a special type of Google account that belongs to your application.")
    print("\n")

def open_browser_with_confirmation(url, description):
    """
    Open a URL in the browser and confirm with the user.
    
    Args:
        url (str): URL to open
        description (str): Description of what the URL is for
    """
    print(f"\nOpening {description} in your browser...")
    print(f"URL: {url}")
    
    # Try to open the browser
    try:
        # First attempt with the default browser
        webbrowser.open(url)
        print("✓ Browser should be opening now.")
    except Exception as e:
        print(f"Error opening browser: {e}")
        print("\nPlease manually open this URL in your browser:")
        print(url)
    
    # Ask for confirmation
    confirmation = input("\nDid the browser open correctly? (y/n): ").lower()
    
    if confirmation != 'y':
        print("\nLet's try again with a different method.")
        
        try:
            # Try with a specific browser
            if os.name == 'nt':  # Windows
                os.startfile(url)
            else:
                # Try common browsers on other platforms
                browsers = ['google-chrome', 'chrome', 'firefox', 'safari']
                for browser in browsers:
                    try:
                        subprocess.Popen([browser, url])
                        break
                    except:
                        continue
            
            print("✓ Browser should be opening now with an alternative method.")
        except Exception as e:
            print(f"Error opening browser with alternative method: {e}")
            print("\nPlease manually open this URL in your browser:")
            print(url)
        
        # Final confirmation
        input("\nPress Enter when you have the page open in your browser...")
    
    return

def setup_service_account():
    """Set up a service account for Gmail integration."""
    print_header()
    
    print("STEP 1: CREATE A SERVICE ACCOUNT")
    print("-" * 80)
    
    # Open Google Cloud Console
    open_browser_with_confirmation(
        "https://console.cloud.google.com/iam-admin/serviceaccounts",
        "Google Cloud Console Service Accounts page"
    )
    
    print("\nFollow these steps to create a service account:")
    print("1. Click on '+ CREATE SERVICE ACCOUNT' at the top of the page")
    print("2. Enter a name for your service account (e.g., 'gmail-integration')")
    print("3. Enter a description (e.g., 'Service account for Gmail integration')")
    print("4. Click 'CREATE AND CONTINUE'")
    print("5. For 'Grant this service account access to project', select 'Editor' role")
    print("6. Click 'CONTINUE'")
    print("7. Click 'DONE'")
    
    input("\nPress Enter when you've created the service account...")
    
    print("\nSTEP 2: CREATE A SERVICE ACCOUNT KEY")
    print("-" * 80)
    
    print("\nFollow these steps to create a service account key:")
    print("1. Click on the service account you just created")
    print("2. Click on the 'KEYS' tab")
    print("3. Click on 'ADD KEY' and select 'Create new key'")
    print("4. Select 'JSON' as the key type")
    print("5. Click 'CREATE'")
    print("6. The key file will be downloaded to your computer")
    
    input("\nPress Enter when you've downloaded the key file...")
    
    print("\nSTEP 3: ENABLE DOMAIN-WIDE DELEGATION")
    print("-" * 80)
    
    print("\nFollow these steps to enable domain-wide delegation:")
    print("1. Click on the service account you just created")
    print("2. Click on 'EDIT' at the top of the page")
    print("3. Check the box for 'Enable Google Workspace Domain-wide Delegation'")
    print("4. Enter a product name for the consent screen (e.g., 'Gmail Integration')")
    print("5. Click 'SAVE'")
    
    input("\nPress Enter when you've enabled domain-wide delegation...")
    
    print("\nSTEP 4: ENABLE THE GMAIL API")
    print("-" * 80)
    
    # Open API Library
    open_browser_with_confirmation(
        "https://console.cloud.google.com/apis/library/gmail.googleapis.com",
        "Gmail API page"
    )
    
    print("\nMake sure the Gmail API is enabled for your project.")
    
    input("\nPress Enter when you've enabled the Gmail API...")
    
    print("\nSTEP 5: SAVE THE SERVICE ACCOUNT KEY")
    print("-" * 80)
    
    # Create credentials directory if it doesn't exist
    os.makedirs('credentials', exist_ok=True)
    
    # Ask for the path to the downloaded file
    downloaded_file = input("\nEnter the path to the downloaded service account key file: ")
    
    if not os.path.exists(downloaded_file):
        print(f"\nError: File not found at {downloaded_file}")
        retry = input("Do you want to try again? (y/n): ").lower()
        if retry == 'y':
            return setup_service_account()
        return
    
    # Copy the file
    try:
        import shutil
        service_account_path = 'credentials/gmail_service_account.json'
        shutil.copy(downloaded_file, service_account_path)
        print(f"\n✓ Service account key file copied to {service_account_path}")
    except Exception as e:
        print(f"\nError copying file: {e}")
        return
    
    print("\n✓ Service account set up successfully!")
    print("\nNext steps:")
    print("1. Run the gmail_service_account_test.py script to test the service account")
    print("2. If you encounter issues, make sure you've enabled domain-wide delegation")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    try:
        setup_service_account()
    except KeyboardInterrupt:
        print("\n\nExiting...")
        sys.exit(0)
