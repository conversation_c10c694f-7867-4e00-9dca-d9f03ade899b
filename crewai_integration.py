"""
CrewAI Integration for the AI Agent System.

This module provides integration with the CrewAI framework,
enabling the creation and management of agent crews for complex tasks.
"""
import asyncio
import json
import logging
import os
from typing import Dict, List, Optional, Any, Union
import uuid
from datetime import datetime
from pathlib import Path

# Add the project root to the Python path
import sys
sys.path.insert(0, str(Path(__file__).resolve().parent))

try:
    from crewai import Agent, Task, Crew, Process
    from crewai.agent import CrewAgentConfig
except ImportError:
    print("CrewAI not installed. Please install it with: pip install crewai>=0.28.0")
    sys.exit(1)

from core.logger import setup_logger
from core.state_manager import StateManager
from core.agent_manager import AgentManager
from borg_cluster.borg_load_balancer import BorgLoadBalancer

# Set up logger
logger = setup_logger("crewai_integration")

class CrewAIIntegration:
    """
    CrewAI Integration for the AI Agent System.

    This class provides integration with the CrewAI framework,
    enabling the creation and management of agent crews for complex tasks.
    """

    def __init__(
        self,
        state_manager: <PERSON><PERSON>anager,
        agent_manager: Agent<PERSON>anager,
        load_balancer: <PERSON>rgLoadBalancer,
        config: Dict = None,
    ):
        """
        Initialize the CrewAI Integration.

        Args:
            state_manager (StateManager): System state manager
            agent_manager (AgentManager): Agent manager
            load_balancer (BorgLoadBalancer): Borg load balancer
            config (Dict, optional): Configuration options
        """
        self.state_manager = state_manager
        self.agent_manager = agent_manager
        self.load_balancer = load_balancer
        self.config = config or {}

        # CrewAI components
        self.crews = {}
        self.agents = {}
        self.tasks = {}

        # Initialization status
        self.initialized = False

        logger.info("CrewAI Integration initialized")

    async def initialize(self):
        """Initialize the CrewAI Integration."""
        try:
            # Load existing state if available
            crewai_state = await self.state_manager.get_state("crewai", "integration")
            if crewai_state:
                # Restore crews
                if "crews" in crewai_state:
                    self.crews = crewai_state["crews"]

                # Restore agents
                if "agents" in crewai_state:
                    self.agents = crewai_state["agents"]

                # Restore tasks
                if "tasks" in crewai_state:
                    self.tasks = crewai_state["tasks"]

                logger.info("Restored CrewAI Integration state")

            # Initialize default crews
            await self._initialize_default_crews()

            self.initialized = True
            logger.info("CrewAI Integration initialized successfully")

        except Exception as e:
            logger.exception(f"Error initializing CrewAI Integration: {e}")
            raise

    async def _initialize_default_crews(self):
        """Initialize default crews."""
        # Check if we already have crews
        if self.crews:
            logger.info(f"Using {len(self.crews)} existing crews")
            return

        # Create default crews based on configuration
        try:
            # Insurance business crew
            if self.config.get("enable_insurance_crew", True):
                await self.create_insurance_crew()

            # Trading crew
            if self.config.get("enable_trading_crew", True):
                await self.create_trading_crew()

            # Social media crew
            if self.config.get("enable_social_media_crew", True):
                await self.create_social_media_crew()

            # Music management crew
            if self.config.get("enable_music_crew", True):
                await self.create_music_crew()

            # Cybersecurity crew
            if self.config.get("enable_cybersecurity_crew", True):
                await self.create_cybersecurity_crew()

            logger.info(f"Initialized {len(self.crews)} default crews")

        except Exception as e:
            logger.exception(f"Error initializing default crews: {e}")
            raise

    async def create_insurance_crew(self):
        """Create an insurance business crew."""
        try:
            # Create insurance agents
            lead_qualifier = Agent(
                role="Lead Qualifier",
                goal="Qualify insurance leads and determine their needs",
                backstory="You are an expert at qualifying insurance leads and determining their needs.",
                verbose=True,
                allow_delegation=True,
                config=CrewAgentConfig(
                    temperature=0.7,
                    max_tokens=2000,
                )
            )

            policy_advisor = Agent(
                role="Policy Advisor",
                goal="Recommend the best insurance policies for qualified leads",
                backstory="You are an expert at recommending insurance policies based on client needs.",
                verbose=True,
                allow_delegation=True,
                config=CrewAgentConfig(
                    temperature=0.7,
                    max_tokens=2000,
                )
            )

            drip_campaign_manager = Agent(
                role="Drip Campaign Manager",
                goal="Manage drip campaigns for insurance leads",
                backstory="You are an expert at managing drip campaigns for insurance leads.",
                verbose=True,
                allow_delegation=True,
                config=CrewAgentConfig(
                    temperature=0.7,
                    max_tokens=2000,
                )
            )

            # Create insurance tasks
            qualify_lead_task = Task(
                description="Qualify an insurance lead and determine their needs",
                expected_output="A detailed qualification report with lead needs",
                agent=lead_qualifier
            )

            recommend_policy_task = Task(
                description="Recommend insurance policies based on lead needs",
                expected_output="A list of recommended policies with justifications",
                agent=policy_advisor
            )

            setup_drip_campaign_task = Task(
                description="Set up a drip campaign for a qualified lead",
                expected_output="A configured drip campaign with schedule",
                agent=drip_campaign_manager
            )

            # Create insurance crew
            insurance_crew = Crew(
                agents=[lead_qualifier, policy_advisor, drip_campaign_manager],
                tasks=[qualify_lead_task, recommend_policy_task, setup_drip_campaign_task],
                verbose=True,
                process=Process.sequential
            )

            # Store crew
            crew_id = str(uuid.uuid4())
            self.crews[crew_id] = {
                "id": crew_id,
                "name": "Insurance Business Crew",
                "description": "A crew for managing insurance business operations",
                "crew": insurance_crew,
                "created_at": datetime.now().isoformat(),
            }

            # Store agents
            self.agents[str(uuid.uuid4())] = {
                "agent": lead_qualifier,
                "name": "Lead Qualifier",
                "crew_id": crew_id,
            }
            self.agents[str(uuid.uuid4())] = {
                "agent": policy_advisor,
                "name": "Policy Advisor",
                "crew_id": crew_id,
            }
            self.agents[str(uuid.uuid4())] = {
                "agent": drip_campaign_manager,
                "name": "Drip Campaign Manager",
                "crew_id": crew_id,
            }

            # Store tasks
            self.tasks[str(uuid.uuid4())] = {
                "task": qualify_lead_task,
                "name": "Qualify Lead",
                "crew_id": crew_id,
            }
            self.tasks[str(uuid.uuid4())] = {
                "task": recommend_policy_task,
                "name": "Recommend Policy",
                "crew_id": crew_id,
            }
            self.tasks[str(uuid.uuid4())] = {
                "task": setup_drip_campaign_task,
                "name": "Setup Drip Campaign",
                "crew_id": crew_id,
            }

            logger.info(f"Created insurance crew with ID: {crew_id}")
            return crew_id

        except Exception as e:
            logger.exception(f"Error creating insurance crew: {e}")
            raise

    async def create_trading_crew(self):
        """Create a trading crew."""
        # Implementation similar to create_insurance_crew
        # This is a placeholder for the trading crew implementation
        logger.info("Trading crew creation is not yet implemented")
        return None

    async def create_social_media_crew(self):
        """Create a social media crew."""
        # Implementation similar to create_insurance_crew
        # This is a placeholder for the social media crew implementation
        logger.info("Social media crew creation is not yet implemented")
        return None

    async def create_music_crew(self):
        """Create a music management crew."""
        # Implementation similar to create_insurance_crew
        # This is a placeholder for the music crew implementation
        logger.info("Music crew creation is not yet implemented")
        return None

    async def create_cybersecurity_crew(self):
        """Create a cybersecurity crew."""
        # Implementation similar to create_insurance_crew
        # This is a placeholder for the cybersecurity crew implementation
        logger.info("Cybersecurity crew creation is not yet implemented")
        return None

    async def run_crew(self, crew_id: str, inputs: Dict = None):
        """
        Run a crew with the given inputs.

        Args:
            crew_id (str): Crew ID
            inputs (Dict, optional): Inputs for the crew

        Returns:
            Dict: Crew results
        """
        if not self.initialized:
            logger.error("CrewAI Integration not initialized")
            return {"error": "CrewAI Integration not initialized"}

        if crew_id not in self.crews:
            logger.error(f"Crew not found: {crew_id}")
            return {"error": f"Crew not found: {crew_id}"}

        try:
            # Get crew
            crew_info = self.crews[crew_id]
            crew = crew_info["crew"]

            # Run crew
            logger.info(f"Running crew: {crew_info['name']} ({crew_id})")
            result = crew.kickoff(inputs=inputs or {})

            # Store result
            crew_info["last_run"] = {
                "timestamp": datetime.now().isoformat(),
                "inputs": inputs,
                "result": result,
            }

            # Save state
            await self._save_state()

            return {
                "crew_id": crew_id,
                "name": crew_info["name"],
                "result": result,
            }

        except Exception as e:
            logger.exception(f"Error running crew: {e}")
            return {"error": str(e)}

    async def _save_state(self):
        """Save the current state to the state manager."""
        try:
            # Create state
            state = {
                "crews": self.crews,
                "agents": self.agents,
                "tasks": self.tasks,
                "last_updated": datetime.now().isoformat(),
            }

            # Save state
            await self.state_manager.update_state("crewai", "integration", state)

        except Exception as e:
            logger.exception(f"Error saving CrewAI Integration state: {e}")

    async def shutdown(self):
        """Shutdown the CrewAI Integration."""
        try:
            # Save final state
            await self._save_state()

            logger.info("CrewAI Integration shut down")

        except Exception as e:
            logger.exception(f"Error shutting down CrewAI Integration: {e}")
            raise
