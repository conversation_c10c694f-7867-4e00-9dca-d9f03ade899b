"""
Test script for contacting a client using the Multi-Agent AI System.

This script tests the system by creating a workflow to contact a client
named <PERSON>yssa <PERSON> using the insurance agent.
"""
import asyncio
import sys
import json
import uuid
from datetime import datetime
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent))

from core.agent_manager import Agent<PERSON>anager
from core.agent_coordinator import AgentCoordinator, MessageIntent, MessagePriority
from core.state_manager import StateManager
from core.logger import setup_logger
from llm.llm_router import LL<PERSON>outer
from ui_tars_gmail_automation import GmailUITarsAutomation

# Set up logging
logger = setup_logger("test_contact_client")

async def test_contact_client():
    """Test contacting a client using the system."""
    logger.info("Starting test: Contact client Alyssa C.")
    
    # Initialize components
    shutdown_event = asyncio.Event()
    state_manager = StateManager()
    await state_manager.initialize()
    
    llm_router = LLMRouter()
    await llm_router.initialize()
    
    agent_coordinator = AgentCoordinator(state_manager, llm_router, shutdown_event)
    await agent_coordinator.initialize()
    
    agent_manager = AgentManager(state_manager, shutdown_event)
    
    # Register services
    agent_manager.register_service("llm_router", llm_router)
    agent_manager.register_service("agent_coordinator", agent_coordinator)
    
    # Start agent manager
    await agent_manager.start()
    
    try:
        # Create a workflow for contacting Alyssa C.
        workflow_id = await agent_coordinator.create_workflow(
            name="Contact Alyssa C.",
            description="Contact client Alyssa C. about insurance options",
            steps=[
                {
                    "type": "agent_task",
                    "agent_id": "insurance_agent",
                    "action": "prepare_client_email",
                    "parameters": {
                        "client_name": "Alyssa Chirinos",
                        "client_email": "<EMAIL>",  # Test email (replace with actual client email)
                        "insurance_type": "IUL",
                        "budget": "$100/month",
                        "additional_needs": "basic health, dental and vision plans"
                    }
                },
                {
                    "type": "reasoning",
                    "reasoning_type": "causal",
                    "context": "Client Alyssa C. has requested an IUL policy with maximum cash value growth, plus basic health, dental and vision plans. Her budget is $100/month with some flexibility if slightly over budget.",
                    "question": "What insurance options should we recommend to Alyssa?",
                    "variables": ["budget constraints", "IUL options", "health plan options", "dental plan options", "vision plan options"]
                },
                {
                    "type": "agent_task",
                    "agent_id": "insurance_agent",
                    "action": "send_client_email",
                    "parameters": {
                        "client_name": "Alyssa Chirinos",
                        "client_email": "<EMAIL>",  # Test email (replace with actual client email)
                        "subject": "Your Insurance Options - Flo Faction Insurance",
                        "use_ui_tars": True
                    }
                }
            ]
        )
        
        logger.info(f"Created workflow: {workflow_id}")
        
        # Execute the workflow
        result = await agent_coordinator.execute_workflow(
            workflow_id=workflow_id,
            input_data={
                "client_info": {
                    "name": "Alyssa Chirinos",
                    "email": "<EMAIL>",
                    "phone": "N/A",
                    "insurance_needs": "IUL policy with maximum cash value growth, plus basic health, dental and vision plans",
                    "budget": "$100/month",
                    "notes": "Some flexibility if slightly over budget"
                }
            }
        )
        
        logger.info(f"Workflow execution result: {json.dumps(result, indent=2)}")
        
        # If UI-TARS is not available, use direct Gmail API
        if result.get("status") == "failed" and "UI-TARS" in str(result.get("error", "")):
            logger.info("UI-TARS failed, attempting direct email using Gmail API")
            
            # Send email directly
            email_content = await _generate_email_content(
                llm_router,
                "Alyssa Chirinos",
                "IUL policy with maximum cash value growth, plus basic health, dental and vision plans",
                "$100/month"
            )
            
            # Initialize Gmail automation
            gmail = GmailUITarsAutomation()
            
            # Send email
            await gmail.send_email(
                email_account="<EMAIL>",
                password="GodisSoGood!777",
                to_email="<EMAIL>",
                subject="Your Insurance Options - Flo Faction Insurance",
                body=email_content
            )
            
            logger.info("Email sent successfully using direct Gmail automation")
        
        # Test Google Voice integration if needed
        # This would require additional setup with UI-TARS to automate Google Voice
        # await _test_google_voice(agent_coordinator)
        
    except Exception as e:
        logger.exception(f"Error in test: {e}")
    finally:
        # Shutdown
        await agent_coordinator.shutdown()
        await agent_manager.stop()
        await state_manager.close()
        
        logger.info("Test completed")

async def _generate_email_content(llm_router, client_name, insurance_needs, budget):
    """Generate email content using LLM."""
    prompt = f"""
    Write a professional but friendly email to a potential insurance client named {client_name}.
    
    The client is interested in:
    - {insurance_needs}
    - Budget: {budget}
    
    The email should:
    1. Introduce yourself as an insurance agent from Flo Faction Insurance
    2. Thank them for their interest
    3. Briefly explain the insurance options that would meet their needs
    4. Offer to schedule a call to discuss options in more detail
    5. Include a Calendly link (use placeholder: [CALENDLY_LINK])
    6. End with a friendly sign-off
    
    Keep the tone conversational and not too formal. Address the client by their first name only.
    """
    
    response = await llm_router.generate_text(
        prompt=prompt,
        max_tokens=1000,
        temperature=0.7
    )
    
    return response.get("text", "")

async def _test_google_voice(agent_coordinator):
    """Test Google Voice integration."""
    logger.info("Testing Google Voice integration")
    
    # Create a message to the communication agent
    message_id = await agent_coordinator.send_message(
        recipient_id="communication_agent",
        message_type=MessageIntent.COMMAND.value,
        content={
            "command": "send_text",
            "data": {
                "recipient": "7722089646",  # Test number (replace with actual client number)
                "message": "Hello from Flo Faction Insurance! This is a test message. Please disregard.",
                "service": "google_voice"
            }
        },
        priority=MessagePriority.HIGH
    )
    
    # Wait for response
    for _ in range(30):  # Wait up to 30 seconds
        if message_id in agent_coordinator.pending_responses:
            response = agent_coordinator.pending_responses[message_id]
            logger.info(f"Google Voice test response: {response}")
            break
        await asyncio.sleep(1)
    else:
        logger.warning("No response received for Google Voice test")

if __name__ == "__main__":
    try:
        asyncio.run(test_contact_client())
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)
