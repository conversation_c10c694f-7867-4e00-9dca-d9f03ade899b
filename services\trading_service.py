"""
Trading services for the Multi-Agent AI System.
"""
import asyncio
from typing import Dict, List, Optional, Any, Union
from abc import ABC, abstractmethod
import json
from datetime import datetime
import aiohttp
import hmac
import hashlib
import time
import base64
from urllib.parse import urlencode

import config
from core.logger import setup_logger

# Set up logger
logger = setup_logger("trading_service")

class TradingService(ABC):
    """
    Base class for trading services.
    """
    
    def __init__(self, service_type: str, config: Dict):
        """
        Initialize the trading service.
        
        Args:
            service_type (str): Type of trading service
            config (Dict): Service configuration
        """
        self.service_type = service_type
        self.config = config
        self.enabled = config.get("enabled", False)
        
        # Set up logger
        self.logger = setup_logger(f"trading.{service_type}")
    
    @abstractmethod
    async def get_account_info(self) -> Dict:
        """
        Get account information.
        
        Returns:
            Dict: Account information
        """
        pass
    
    @abstractmethod
    async def get_market_data(self, symbol: str) -> Dict:
        """
        Get market data for a symbol.
        
        Args:
            symbol (str): Trading symbol
            
        Returns:
            Dict: Market data
        """
        pass
    
    @abstractmethod
    async def place_order(
        self,
        symbol: str,
        side: str,
        quantity: float,
        order_type: str = "market",
        price: Optional[float] = None,
        **kwargs
    ) -> Dict:
        """
        Place a trading order.
        
        Args:
            symbol (str): Trading symbol
            side (str): Order side (buy/sell)
            quantity (float): Order quantity
            order_type (str): Order type (market/limit/etc.)
            price (Optional[float]): Order price (for limit orders)
            **kwargs: Additional parameters
            
        Returns:
            Dict: Order response
        """
        pass
    
    @abstractmethod
    async def get_order_status(self, order_id: str) -> Dict:
        """
        Get order status.
        
        Args:
            order_id (str): Order ID
            
        Returns:
            Dict: Order status
        """
        pass
    
    @abstractmethod
    async def cancel_order(self, order_id: str) -> Dict:
        """
        Cancel an order.
        
        Args:
            order_id (str): Order ID
            
        Returns:
            Dict: Cancellation response
        """
        pass
    
    def is_enabled(self) -> bool:
        """
        Check if the service is enabled.
        
        Returns:
            bool: True if enabled, False otherwise
        """
        return self.enabled

class AlpacaTradingService(TradingService):
    """
    Alpaca trading service for stocks and ETFs.
    """
    
    def __init__(self, config: Dict):
        """
        Initialize the Alpaca trading service.
        
        Args:
            config (Dict): Service configuration
        """
        super().__init__("alpaca", config)
        
        self.api_key = config.get("api_key", "")
        self.api_secret = config.get("api_secret", "")
        self.base_url = config.get("base_url", "https://paper-api.alpaca.markets")
        
        # Validate configuration
        if not self.api_key or not self.api_secret:
            self.logger.warning("Alpaca API credentials not provided")
            self.enabled = False
    
    async def get_account_info(self) -> Dict:
        """
        Get Alpaca account information.
        
        Returns:
            Dict: Account information
        """
        if not self.enabled:
            return {"error": "Alpaca trading service is not enabled"}
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.base_url}/v2/account",
                    headers=self._get_headers(),
                ) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        error_text = await response.text()
                        self.logger.error(f"Failed to get account info: {response.status} - {error_text}")
                        return {
                            "error": f"API error: {response.status}",
                            "details": error_text,
                        }
        except Exception as e:
            self.logger.exception(f"Error getting account info: {e}")
            return {"error": f"Failed to get account info: {str(e)}"}
    
    async def get_market_data(self, symbol: str) -> Dict:
        """
        Get market data for a symbol from Alpaca.
        
        Args:
            symbol (str): Trading symbol
            
        Returns:
            Dict: Market data
        """
        if not self.enabled:
            return {"error": "Alpaca trading service is not enabled"}
        
        try:
            async with aiohttp.ClientSession() as session:
                # Get latest trade
                async with session.get(
                    f"{self.base_url}/v2/stocks/{symbol}/trades/latest",
                    headers=self._get_headers(),
                ) as response:
                    if response.status == 200:
                        trade_data = await response.json()
                    else:
                        error_text = await response.text()
                        self.logger.error(f"Failed to get trade data: {response.status} - {error_text}")
                        return {
                            "error": f"API error: {response.status}",
                            "details": error_text,
                        }
                
                # Get latest quote
                async with session.get(
                    f"{self.base_url}/v2/stocks/{symbol}/quotes/latest",
                    headers=self._get_headers(),
                ) as response:
                    if response.status == 200:
                        quote_data = await response.json()
                    else:
                        error_text = await response.text()
                        self.logger.error(f"Failed to get quote data: {response.status} - {error_text}")
                        return {
                            "error": f"API error: {response.status}",
                            "details": error_text,
                        }
                
                # Get bars (OHLC)
                async with session.get(
                    f"{self.base_url}/v2/stocks/{symbol}/bars",
                    params={"timeframe": "1D", "limit": 5},
                    headers=self._get_headers(),
                ) as response:
                    if response.status == 200:
                        bars_data = await response.json()
                    else:
                        error_text = await response.text()
                        self.logger.error(f"Failed to get bars data: {response.status} - {error_text}")
                        return {
                            "error": f"API error: {response.status}",
                            "details": error_text,
                        }
                
                # Combine data
                return {
                    "symbol": symbol,
                    "trade": trade_data.get("trade", {}),
                    "quote": quote_data.get("quote", {}),
                    "bars": bars_data.get("bars", []),
                    "timestamp": datetime.now().isoformat(),
                }
        
        except Exception as e:
            self.logger.exception(f"Error getting market data: {e}")
            return {"error": f"Failed to get market data: {str(e)}"}
    
    async def place_order(
        self,
        symbol: str,
        side: str,
        quantity: float,
        order_type: str = "market",
        price: Optional[float] = None,
        **kwargs
    ) -> Dict:
        """
        Place a trading order with Alpaca.
        
        Args:
            symbol (str): Trading symbol
            side (str): Order side (buy/sell)
            quantity (float): Order quantity
            order_type (str): Order type (market/limit/etc.)
            price (Optional[float]): Order price (for limit orders)
            **kwargs: Additional parameters
            
        Returns:
            Dict: Order response
        """
        if not self.enabled:
            return {"error": "Alpaca trading service is not enabled"}
        
        # Validate parameters
        if side not in ["buy", "sell"]:
            return {"error": f"Invalid order side: {side}"}
        
        if order_type not in ["market", "limit", "stop", "stop_limit"]:
            return {"error": f"Invalid order type: {order_type}"}
        
        if order_type in ["limit", "stop_limit"] and price is None:
            return {"error": f"Price is required for {order_type} orders"}
        
        # Prepare order payload
        payload = {
            "symbol": symbol,
            "qty": str(quantity),
            "side": side,
            "type": order_type,
            "time_in_force": kwargs.get("time_in_force", "day"),
        }
        
        # Add price for limit orders
        if order_type in ["limit", "stop_limit"]:
            payload["limit_price"] = str(price)
        
        # Add stop price for stop orders
        if order_type in ["stop", "stop_limit"]:
            payload["stop_price"] = str(kwargs.get("stop_price", 0))
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/v2/orders",
                    headers=self._get_headers(),
                    json=payload,
                ) as response:
                    if response.status == 200:
                        order_data = await response.json()
                        self.logger.info(f"Order placed: {order_data.get('id')}")
                        return order_data
                    else:
                        error_text = await response.text()
                        self.logger.error(f"Failed to place order: {response.status} - {error_text}")
                        return {
                            "error": f"API error: {response.status}",
                            "details": error_text,
                        }
        except Exception as e:
            self.logger.exception(f"Error placing order: {e}")
            return {"error": f"Failed to place order: {str(e)}"}
    
    async def get_order_status(self, order_id: str) -> Dict:
        """
        Get order status from Alpaca.
        
        Args:
            order_id (str): Order ID
            
        Returns:
            Dict: Order status
        """
        if not self.enabled:
            return {"error": "Alpaca trading service is not enabled"}
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.base_url}/v2/orders/{order_id}",
                    headers=self._get_headers(),
                ) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        error_text = await response.text()
                        self.logger.error(f"Failed to get order status: {response.status} - {error_text}")
                        return {
                            "error": f"API error: {response.status}",
                            "details": error_text,
                        }
        except Exception as e:
            self.logger.exception(f"Error getting order status: {e}")
            return {"error": f"Failed to get order status: {str(e)}"}
    
    async def cancel_order(self, order_id: str) -> Dict:
        """
        Cancel an order with Alpaca.
        
        Args:
            order_id (str): Order ID
            
        Returns:
            Dict: Cancellation response
        """
        if not self.enabled:
            return {"error": "Alpaca trading service is not enabled"}
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.delete(
                    f"{self.base_url}/v2/orders/{order_id}",
                    headers=self._get_headers(),
                ) as response:
                    if response.status == 204:
                        self.logger.info(f"Order cancelled: {order_id}")
                        return {"status": "cancelled", "order_id": order_id}
                    else:
                        error_text = await response.text()
                        self.logger.error(f"Failed to cancel order: {response.status} - {error_text}")
                        return {
                            "error": f"API error: {response.status}",
                            "details": error_text,
                        }
        except Exception as e:
            self.logger.exception(f"Error cancelling order: {e}")
            return {"error": f"Failed to cancel order: {str(e)}"}
    
    def _get_headers(self) -> Dict[str, str]:
        """
        Get headers for Alpaca API requests.
        
        Returns:
            Dict[str, str]: Headers
        """
        return {
            "APCA-API-KEY-ID": self.api_key,
            "APCA-API-SECRET-KEY": self.api_secret,
            "Content-Type": "application/json",
        }

class CryptoTradingService(TradingService):
    """
    Cryptocurrency trading service.
    """
    
    def __init__(self, config: Dict):
        """
        Initialize the crypto trading service.
        
        Args:
            config (Dict): Service configuration
        """
        super().__init__("crypto", config)
        
        self.exchange = config.get("exchange", "binance")
        self.api_key = config.get("api_key", "")
        self.api_secret = config.get("api_secret", "")
        
        # Set API URLs based on exchange
        if self.exchange == "binance":
            self.base_url = "https://api.binance.com"
        elif self.exchange == "coinbase":
            self.base_url = "https://api.coinbase.com"
        else:
            self.logger.warning(f"Unsupported crypto exchange: {self.exchange}")
            self.enabled = False
            return
        
        # Validate configuration
        if not self.api_key or not self.api_secret:
            self.logger.warning(f"{self.exchange} API credentials not provided")
            self.enabled = False
    
    async def get_account_info(self) -> Dict:
        """
        Get account information from the crypto exchange.
        
        Returns:
            Dict: Account information
        """
        if not self.enabled:
            return {"error": f"{self.exchange} trading service is not enabled"}
        
        if self.exchange == "binance":
            return await self._binance_get_account_info()
        elif self.exchange == "coinbase":
            return await self._coinbase_get_account_info()
        else:
            return {"error": f"Unsupported crypto exchange: {self.exchange}"}
    
    async def get_market_data(self, symbol: str) -> Dict:
        """
        Get market data for a symbol from the crypto exchange.
        
        Args:
            symbol (str): Trading symbol
            
        Returns:
            Dict: Market data
        """
        if not self.enabled:
            return {"error": f"{self.exchange} trading service is not enabled"}
        
        if self.exchange == "binance":
            return await self._binance_get_market_data(symbol)
        elif self.exchange == "coinbase":
            return await self._coinbase_get_market_data(symbol)
        else:
            return {"error": f"Unsupported crypto exchange: {self.exchange}"}
    
    async def place_order(
        self,
        symbol: str,
        side: str,
        quantity: float,
        order_type: str = "market",
        price: Optional[float] = None,
        **kwargs
    ) -> Dict:
        """
        Place a trading order with the crypto exchange.
        
        Args:
            symbol (str): Trading symbol
            side (str): Order side (buy/sell)
            quantity (float): Order quantity
            order_type (str): Order type (market/limit/etc.)
            price (Optional[float]): Order price (for limit orders)
            **kwargs: Additional parameters
            
        Returns:
            Dict: Order response
        """
        if not self.enabled:
            return {"error": f"{self.exchange} trading service is not enabled"}
        
        if self.exchange == "binance":
            return await self._binance_place_order(symbol, side, quantity, order_type, price, **kwargs)
        elif self.exchange == "coinbase":
            return await self._coinbase_place_order(symbol, side, quantity, order_type, price, **kwargs)
        else:
            return {"error": f"Unsupported crypto exchange: {self.exchange}"}
    
    async def get_order_status(self, order_id: str) -> Dict:
        """
        Get order status from the crypto exchange.
        
        Args:
            order_id (str): Order ID
            
        Returns:
            Dict: Order status
        """
        if not self.enabled:
            return {"error": f"{self.exchange} trading service is not enabled"}
        
        if self.exchange == "binance":
            return await self._binance_get_order_status(order_id)
        elif self.exchange == "coinbase":
            return await self._coinbase_get_order_status(order_id)
        else:
            return {"error": f"Unsupported crypto exchange: {self.exchange}"}
    
    async def cancel_order(self, order_id: str) -> Dict:
        """
        Cancel an order with the crypto exchange.
        
        Args:
            order_id (str): Order ID
            
        Returns:
            Dict: Cancellation response
        """
        if not self.enabled:
            return {"error": f"{self.exchange} trading service is not enabled"}
        
        if self.exchange == "binance":
            return await self._binance_cancel_order(order_id)
        elif self.exchange == "coinbase":
            return await self._coinbase_cancel_order(order_id)
        else:
            return {"error": f"Unsupported crypto exchange: {self.exchange}"}
    
    # Binance-specific methods
    async def _binance_get_account_info(self) -> Dict:
        """
        Get account information from Binance.
        
        Returns:
            Dict: Account information
        """
        endpoint = "/api/v3/account"
        timestamp = int(time.time() * 1000)
        params = f"timestamp={timestamp}"
        signature = self._binance_sign(params)
        
        url = f"{self.base_url}{endpoint}?{params}&signature={signature}"
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    url,
                    headers={"X-MBX-APIKEY": self.api_key},
                ) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        error_text = await response.text()
                        self.logger.error(f"Failed to get Binance account info: {response.status} - {error_text}")
                        return {
                            "error": f"API error: {response.status}",
                            "details": error_text,
                        }
        except Exception as e:
            self.logger.exception(f"Error getting Binance account info: {e}")
            return {"error": f"Failed to get account info: {str(e)}"}
    
    # Helper methods for Binance
    def _binance_sign(self, data: str) -> str:
        """
        Generate signature for Binance API requests.
        
        Args:
            data (str): Data to sign
            
        Returns:
            str: Signature
        """
        return hmac.new(
            self.api_secret.encode("utf-8"),
            data.encode("utf-8"),
            hashlib.sha256
        ).hexdigest()
    
    # Placeholder methods for other exchange-specific implementations
    async def _binance_get_market_data(self, symbol: str) -> Dict:
        # Implementation for Binance market data
        return {"error": "Method not implemented"}
    
    async def _binance_place_order(self, symbol: str, side: str, quantity: float, order_type: str, price: Optional[float], **kwargs) -> Dict:
        # Implementation for Binance order placement
        return {"error": "Method not implemented"}
    
    async def _binance_get_order_status(self, order_id: str) -> Dict:
        # Implementation for Binance order status
        return {"error": "Method not implemented"}
    
    async def _binance_cancel_order(self, order_id: str) -> Dict:
        # Implementation for Binance order cancellation
        return {"error": "Method not implemented"}
    
    async def _coinbase_get_account_info(self) -> Dict:
        # Implementation for Coinbase account info
        return {"error": "Method not implemented"}
    
    async def _coinbase_get_market_data(self, symbol: str) -> Dict:
        # Implementation for Coinbase market data
        return {"error": "Method not implemented"}
    
    async def _coinbase_place_order(self, symbol: str, side: str, quantity: float, order_type: str, price: Optional[float], **kwargs) -> Dict:
        # Implementation for Coinbase order placement
        return {"error": "Method not implemented"}
    
    async def _coinbase_get_order_status(self, order_id: str) -> Dict:
        # Implementation for Coinbase order status
        return {"error": "Method not implemented"}
    
    async def _coinbase_cancel_order(self, order_id: str) -> Dict:
        # Implementation for Coinbase order cancellation
        return {"error": "Method not implemented"}

class TradingServiceFactory:
    """
    Factory for creating trading services.
    """
    
    @staticmethod
    def create_service(service_type: str) -> Optional[TradingService]:
        """
        Create a trading service.
        
        Args:
            service_type (str): Type of trading service
            
        Returns:
            Optional[TradingService]: Trading service instance
        """
        if service_type == "alpaca":
            return AlpacaTradingService(config.TRADING_CONFIG["alpaca"])
        elif service_type == "crypto":
            return CryptoTradingService(config.TRADING_CONFIG["crypto"])
        else:
            logger.warning(f"Unsupported trading service type: {service_type}")
            return None
