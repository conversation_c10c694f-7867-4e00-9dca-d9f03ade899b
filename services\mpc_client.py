"""
Multi-Party Computation (MPC) Client for the Multi-Agent AI System.

This module provides integration with MPC servers for secure computation
on sensitive data without revealing the underlying data to any party.
"""
import asyncio
import json
import logging
import os
import random
import time
from datetime import datetime
from typing import Dict, List, Optional, Any, Union

from core.logger import setup_logger
from core.state_manager import StateManager

# Set up logger
logger = setup_logger("mpc_client")

class MPCClient:
    """
    Client for Multi-Party Computation (MPC) servers.
    
    This class provides integration with MPC servers for secure computation
    on sensitive data without revealing the underlying data to any party.
    """
    
    def __init__(self, state_manager: StateManager, config: Dict = None):
        """
        Initialize the MPC client.
        
        Args:
            state_manager (StateManager): System state manager
            config (Dict, optional): Configuration options
        """
        self.state_manager = state_manager
        self.config = config or {}
        
        # MPC server configuration
        self.servers = self.config.get("servers", [])
        self.default_server = self.config.get("default_server")
        self.api_keys = self.config.get("api_keys", {})
        
        # Computation configurations
        self.computation_configs = {}
        
        # Computation history
        self.computation_history = {}
        
        # Connection status
        self.connected_servers = set()
        self.last_connection_attempt = {}
        
        logger.info(f"MPC client initialized with {len(self.servers)} servers")
    
    async def initialize(self):
        """Initialize the MPC client."""
        try:
            # Load computation configurations
            await self._load_computation_configs()
            
            # Load computation history
            await self._load_computation_history()
            
            # Connect to MPC servers
            await self.connect_to_servers()
            
            logger.info("MPC client initialized successfully")
            
        except Exception as e:
            logger.exception(f"Error initializing MPC client: {e}")
            raise
    
    async def _load_computation_configs(self):
        """Load computation configurations from state manager."""
        try:
            configs = await self.state_manager.get_state("mpc", "computation_configs")
            
            if configs:
                self.computation_configs = configs
                logger.info(f"Loaded configurations for {len(self.computation_configs)} MPC computations")
            else:
                # Initialize with default configurations
                self.computation_configs = {
                    "secure_average": {
                        "type": "arithmetic",
                        "description": "Compute average of values without revealing individual values",
                        "input_schema": {
                            "type": "object",
                            "properties": {
                                "values": {
                                    "type": "array",
                                    "items": {"type": "number"}
                                }
                            }
                        },
                        "output_schema": {
                            "type": "object",
                            "properties": {
                                "result": {"type": "number"}
                            }
                        }
                    },
                    "secure_sum": {
                        "type": "arithmetic",
                        "description": "Compute sum of values without revealing individual values",
                        "input_schema": {
                            "type": "object",
                            "properties": {
                                "values": {
                                    "type": "array",
                                    "items": {"type": "number"}
                                }
                            }
                        },
                        "output_schema": {
                            "type": "object",
                            "properties": {
                                "result": {"type": "number"}
                            }
                        }
                    },
                    "secure_comparison": {
                        "type": "boolean",
                        "description": "Compare values without revealing them",
                        "input_schema": {
                            "type": "object",
                            "properties": {
                                "value_a": {"type": "number"},
                                "value_b": {"type": "number"},
                                "operator": {
                                    "type": "string",
                                    "enum": ["eq", "ne", "gt", "ge", "lt", "le"]
                                }
                            }
                        },
                        "output_schema": {
                            "type": "object",
                            "properties": {
                                "result": {"type": "boolean"}
                            }
                        }
                    },
                    "secure_set_intersection": {
                        "type": "set",
                        "description": "Compute intersection of sets without revealing the sets",
                        "input_schema": {
                            "type": "object",
                            "properties": {
                                "sets": {
                                    "type": "array",
                                    "items": {
                                        "type": "array",
                                        "items": {"type": "string"}
                                    }
                                }
                            }
                        },
                        "output_schema": {
                            "type": "object",
                            "properties": {
                                "result": {
                                    "type": "array",
                                    "items": {"type": "string"}
                                }
                            }
                        }
                    },
                    "secure_linear_regression": {
                        "type": "machine_learning",
                        "description": "Compute linear regression without revealing the data",
                        "input_schema": {
                            "type": "object",
                            "properties": {
                                "x_values": {
                                    "type": "array",
                                    "items": {
                                        "type": "array",
                                        "items": {"type": "number"}
                                    }
                                },
                                "y_values": {
                                    "type": "array",
                                    "items": {"type": "number"}
                                }
                            }
                        },
                        "output_schema": {
                            "type": "object",
                            "properties": {
                                "coefficients": {
                                    "type": "array",
                                    "items": {"type": "number"}
                                },
                                "intercept": {"type": "number"},
                                "r_squared": {"type": "number"}
                            }
                        }
                    }
                }
                
                # Save default configurations
                await self.state_manager.update_state("mpc", "computation_configs", self.computation_configs)
                logger.info("Initialized default MPC computation configurations")
            
        except Exception as e:
            logger.exception(f"Error loading MPC computation configurations: {e}")
            raise
    
    async def _load_computation_history(self):
        """Load computation history from state manager."""
        try:
            history = await self.state_manager.get_state("mpc", "computation_history")
            
            if history:
                self.computation_history = history
                logger.info(f"Loaded {len(self.computation_history)} MPC computation records")
            else:
                logger.info("No MPC computation history found")
            
        except Exception as e:
            logger.exception(f"Error loading MPC computation history: {e}")
    
    async def connect_to_servers(self) -> Dict[str, bool]:
        """
        Connect to MPC servers.
        
        Returns:
            Dict[str, bool]: Connection status for each server
        """
        connection_status = {}
        
        for server in self.servers:
            server_id = server.get("id")
            server_url = server.get("url")
            
            if not server_id or not server_url:
                logger.warning(f"Invalid server configuration: {server}")
                continue
            
            try:
                self.last_connection_attempt[server_id] = datetime.now().isoformat()
                
                # Simulate connection to server
                # In a real implementation, this would establish a connection
                await asyncio.sleep(0.5)  # Simulate connection delay
                
                # Randomly succeed or fail for simulation
                success = random.random() > 0.2  # 80% success rate
                
                if success:
                    self.connected_servers.add(server_id)
                    connection_status[server_id] = True
                    logger.info(f"Connected to MPC server: {server_id}")
                else:
                    if server_id in self.connected_servers:
                        self.connected_servers.remove(server_id)
                    connection_status[server_id] = False
                    logger.warning(f"Failed to connect to MPC server: {server_id}")
                
            except Exception as e:
                logger.exception(f"Error connecting to MPC server {server_id}: {e}")
                connection_status[server_id] = False
                if server_id in self.connected_servers:
                    self.connected_servers.remove(server_id)
        
        return connection_status
    
    async def create_computation(
        self,
        computation_type: str,
        parameters: Dict,
        server_id: str = None,
    ) -> str:
        """
        Create a new MPC computation.
        
        Args:
            computation_type (str): Type of computation
            parameters (Dict): Computation parameters
            server_id (str, optional): Server ID to use
            
        Returns:
            str: Computation ID
        """
        try:
            # Check if computation type exists
            if computation_type not in self.computation_configs:
                raise ValueError(f"Unknown computation type: {computation_type}")
            
            # Get computation configuration
            comp_config = self.computation_configs[computation_type]
            
            # Determine server to use
            if not server_id:
                server_id = self.default_server
                
                if not server_id or server_id not in self.connected_servers:
                    # Find a connected server
                    if not self.connected_servers:
                        await self.connect_to_servers()
                        if not self.connected_servers:
                            raise ValueError("No connected MPC servers available")
                    
                    server_id = next(iter(self.connected_servers))
            
            # Check if server is connected
            if server_id not in self.connected_servers:
                # Try to connect
                connection_status = await self.connect_to_servers()
                if not connection_status.get(server_id, False):
                    raise ValueError(f"MPC server not connected: {server_id}")
            
            # Create computation ID
            computation_id = f"{computation_type}_{datetime.now().strftime('%Y%m%d%H%M%S')}_{random.randint(1000, 9999)}"
            
            # Create computation record
            computation_record = {
                "id": computation_id,
                "type": computation_type,
                "parameters": parameters,
                "server_id": server_id,
                "status": "created",
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
            }
            
            # Store computation record
            self.computation_history[computation_id] = computation_record
            await self.state_manager.update_state("mpc", "computation_history", self.computation_history)
            
            logger.info(f"Created MPC computation: {computation_id} of type {computation_type}")
            
            return computation_id
            
        except Exception as e:
            logger.exception(f"Error creating MPC computation: {e}")
            raise
    
    async def submit_input(
        self,
        computation_id: str,
        input_data: Dict,
        party_id: str = "default",
    ) -> bool:
        """
        Submit input for an MPC computation.
        
        Args:
            computation_id (str): Computation ID
            input_data (Dict): Input data
            party_id (str, optional): Party ID
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Check if computation exists
            if computation_id not in self.computation_history:
                raise ValueError(f"Computation not found: {computation_id}")
            
            # Get computation record
            computation = self.computation_history[computation_id]
            
            # Check computation status
            if computation["status"] not in ["created", "input_collection"]:
                raise ValueError(f"Cannot submit input for computation in state: {computation['status']}")
            
            # Update computation status
            computation["status"] = "input_collection"
            computation["updated_at"] = datetime.now().isoformat()
            
            # Store input data
            if "inputs" not in computation:
                computation["inputs"] = {}
            
            computation["inputs"][party_id] = input_data
            
            # Store updated computation record
            self.computation_history[computation_id] = computation
            await self.state_manager.update_state("mpc", "computation_history", self.computation_history)
            
            logger.info(f"Submitted input for MPC computation: {computation_id} from party: {party_id}")
            
            # Simulate automatic computation if all inputs are available
            # In a real implementation, this would depend on the MPC protocol
            await self._simulate_computation_progress(computation_id)
            
            return True
            
        except Exception as e:
            logger.exception(f"Error submitting input for MPC computation: {e}")
            raise
    
    async def _simulate_computation_progress(self, computation_id: str):
        """
        Simulate progress of an MPC computation.
        
        Args:
            computation_id (str): Computation ID
        """
        try:
            # Get computation record
            computation = self.computation_history[computation_id]
            
            # Check if inputs are available
            if "inputs" not in computation or not computation["inputs"]:
                return
            
            # Simulate computation delay
            await asyncio.sleep(1.0)
            
            # Update computation status
            computation["status"] = "computing"
            computation["updated_at"] = datetime.now().isoformat()
            
            # Store updated computation record
            self.computation_history[computation_id] = computation
            await self.state_manager.update_state("mpc", "computation_history", self.computation_history)
            
            # Simulate computation time
            await asyncio.sleep(2.0)
            
            # Generate result based on computation type
            computation_type = computation["type"]
            
            if computation_type == "secure_average":
                result = self._compute_average(computation["inputs"])
            elif computation_type == "secure_sum":
                result = self._compute_sum(computation["inputs"])
            elif computation_type == "secure_comparison":
                result = self._compute_comparison(computation["inputs"])
            elif computation_type == "secure_set_intersection":
                result = self._compute_set_intersection(computation["inputs"])
            elif computation_type == "secure_linear_regression":
                result = self._compute_linear_regression(computation["inputs"])
            else:
                result = {"error": f"Unsupported computation type: {computation_type}"}
            
            # Update computation with result
            computation["status"] = "completed"
            computation["result"] = result
            computation["completed_at"] = datetime.now().isoformat()
            computation["updated_at"] = datetime.now().isoformat()
            
            # Store updated computation record
            self.computation_history[computation_id] = computation
            await self.state_manager.update_state("mpc", "computation_history", self.computation_history)
            
            logger.info(f"Completed MPC computation: {computation_id}")
            
        except Exception as e:
            logger.exception(f"Error simulating MPC computation progress: {e}")
            
            # Update computation status
            computation = self.computation_history.get(computation_id)
            if computation:
                computation["status"] = "failed"
                computation["error"] = str(e)
                computation["updated_at"] = datetime.now().isoformat()
                
                # Store updated computation record
                self.computation_history[computation_id] = computation
                await self.state_manager.update_state("mpc", "computation_history", self.computation_history)
    
    def _compute_average(self, inputs: Dict) -> Dict:
        """
        Compute average of values.
        
        Args:
            inputs (Dict): Input data from parties
            
        Returns:
            Dict: Computation result
        """
        all_values = []
        
        for party_id, input_data in inputs.items():
            values = input_data.get("values", [])
            all_values.extend(values)
        
        if not all_values:
            return {"result": 0}
        
        average = sum(all_values) / len(all_values)
        
        return {"result": average}
    
    def _compute_sum(self, inputs: Dict) -> Dict:
        """
        Compute sum of values.
        
        Args:
            inputs (Dict): Input data from parties
            
        Returns:
            Dict: Computation result
        """
        all_values = []
        
        for party_id, input_data in inputs.items():
            values = input_data.get("values", [])
            all_values.extend(values)
        
        total_sum = sum(all_values)
        
        return {"result": total_sum}
    
    def _compute_comparison(self, inputs: Dict) -> Dict:
        """
        Compute comparison of values.
        
        Args:
            inputs (Dict): Input data from parties
            
        Returns:
            Dict: Computation result
        """
        # Get first input (in a real MPC system, this would be done securely)
        first_input = next(iter(inputs.values()))
        
        value_a = first_input.get("value_a", 0)
        value_b = first_input.get("value_b", 0)
        operator = first_input.get("operator", "eq")
        
        if operator == "eq":
            result = value_a == value_b
        elif operator == "ne":
            result = value_a != value_b
        elif operator == "gt":
            result = value_a > value_b
        elif operator == "ge":
            result = value_a >= value_b
        elif operator == "lt":
            result = value_a < value_b
        elif operator == "le":
            result = value_a <= value_b
        else:
            result = False
        
        return {"result": result}
    
    def _compute_set_intersection(self, inputs: Dict) -> Dict:
        """
        Compute intersection of sets.
        
        Args:
            inputs (Dict): Input data from parties
            
        Returns:
            Dict: Computation result
        """
        all_sets = []
        
        for party_id, input_data in inputs.items():
            sets = input_data.get("sets", [])
            all_sets.extend(sets)
        
        if not all_sets:
            return {"result": []}
        
        # Convert lists to sets
        set_objects = [set(s) for s in all_sets]
        
        # Compute intersection
        if set_objects:
            intersection = set.intersection(*set_objects)
            result = list(intersection)
        else:
            result = []
        
        return {"result": result}
    
    def _compute_linear_regression(self, inputs: Dict) -> Dict:
        """
        Compute linear regression.
        
        Args:
            inputs (Dict): Input data from parties
            
        Returns:
            Dict: Computation result
        """
        # Get first input (in a real MPC system, this would be done securely)
        first_input = next(iter(inputs.values()))
        
        x_values = first_input.get("x_values", [])
        y_values = first_input.get("y_values", [])
        
        if not x_values or not y_values or len(x_values) != len(y_values):
            return {
                "coefficients": [],
                "intercept": 0,
                "r_squared": 0
            }
        
        # Simplified linear regression for demonstration
        # In a real implementation, this would use a proper algorithm
        
        # For simplicity, assume x_values are 1D
        x = [x[0] if isinstance(x, list) and len(x) > 0 else x for x in x_values]
        y = y_values
        
        n = len(x)
        sum_x = sum(x)
        sum_y = sum(y)
        sum_xy = sum(x_i * y_i for x_i, y_i in zip(x, y))
        sum_xx = sum(x_i * x_i for x_i in x)
        
        # Calculate slope and intercept
        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_xx - sum_x * sum_x)
        intercept = (sum_y - slope * sum_x) / n
        
        # Calculate R-squared
        y_mean = sum_y / n
        ss_total = sum((y_i - y_mean) ** 2 for y_i in y)
        ss_residual = sum((y_i - (slope * x_i + intercept)) ** 2 for x_i, y_i in zip(x, y))
        r_squared = 1 - (ss_residual / ss_total) if ss_total != 0 else 0
        
        return {
            "coefficients": [slope],
            "intercept": intercept,
            "r_squared": r_squared
        }
    
    async def get_result(self, computation_id: str) -> Dict:
        """
        Get result of an MPC computation.
        
        Args:
            computation_id (str): Computation ID
            
        Returns:
            Dict: Computation result
        """
        try:
            # Check if computation exists
            if computation_id not in self.computation_history:
                raise ValueError(f"Computation not found: {computation_id}")
            
            # Get computation record
            computation = self.computation_history[computation_id]
            
            # Check if computation is completed
            if computation["status"] != "completed":
                # If still computing, wait a bit
                if computation["status"] == "computing":
                    await asyncio.sleep(1.0)
                    
                    # Refresh computation record
                    computation = self.computation_history[computation_id]
                    
                    if computation["status"] != "completed":
                        return {"status": computation["status"], "message": "Computation in progress"}
                else:
                    return {"status": computation["status"], "message": f"Computation in state: {computation['status']}"}
            
            # Return result
            return computation.get("result", {})
            
        except Exception as e:
            logger.exception(f"Error getting MPC computation result: {e}")
            raise
    
    async def get_computation_status(self, computation_id: str) -> Dict:
        """
        Get status of an MPC computation.
        
        Args:
            computation_id (str): Computation ID
            
        Returns:
            Dict: Computation status
        """
        if computation_id not in self.computation_history:
            raise ValueError(f"Computation not found: {computation_id}")
        
        computation = self.computation_history[computation_id]
        
        return {
            "id": computation_id,
            "type": computation.get("type"),
            "status": computation.get("status"),
            "server_id": computation.get("server_id"),
            "created_at": computation.get("created_at"),
            "updated_at": computation.get("updated_at"),
            "completed_at": computation.get("completed_at"),
            "error": computation.get("error"),
        }
    
    async def get_available_computations(self) -> Dict:
        """
        Get available MPC computations.
        
        Returns:
            Dict: Available computations and their configurations
        """
        return self.computation_configs
    
    async def get_server_status(self) -> Dict:
        """
        Get status of MPC servers.
        
        Returns:
            Dict: Server status
        """
        server_status = {}
        
        for server in self.servers:
            server_id = server.get("id")
            
            if server_id:
                server_status[server_id] = {
                    "connected": server_id in self.connected_servers,
                    "last_connection_attempt": self.last_connection_attempt.get(server_id),
                    "url": server.get("url"),
                }
        
        return server_status
