@echo off
REM Start Alyssa's Insurance Drip Campaign
REM This script starts a drip campaign for Alyssa Chirinos

echo.
echo ===================================
echo    Start Alyssa's Drip Campaign
echo ===================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Alyssa's information
set CLIENT_NAME=Alyssa Chirinos
set PHONE_NUMBER=5555555555
set EMAIL=<EMAIL>
set INSURANCE_TYPE=IUL
set BUDGET=$100/month

REM Confirm information
echo Client Information:
echo Name: %CLIENT_NAME%
echo Phone: %PHONE_NUMBER%
echo Email: %EMAIL%
echo Insurance Type: %INSURANCE_TYPE%
echo Budget: %BUDGET%
echo.

set /p CONFIRM="Start drip campaign with this information? (y/n): "
if /i not "%CONFIRM%"=="y" (
    echo Drip campaign creation cancelled.
    goto end
)

REM Start drip campaign
echo.
echo Starting drip campaign for %CLIENT_NAME%...
echo.

python run_drip_campaign.py start --name "%CLIENT_NAME%" --phone "%PHONE_NUMBER%" --email "%EMAIL%" --insurance-type "%INSURANCE_TYPE%" --budget "%BUDGET%"

if %errorlevel% neq 0 (
    echo Failed to start drip campaign.
    goto end
)

echo.
echo Drip campaign started successfully.
echo.

REM Ask if user wants to run the agent
set /p RUN_AGENT="Do you want to run the drip campaign agent now? (y/n): "
if /i not "%RUN_AGENT%"=="y" (
    goto end
)

REM Run agent
echo.
echo Running drip campaign agent...
echo.

python run_drip_campaign.py run

:end
echo.
pause
