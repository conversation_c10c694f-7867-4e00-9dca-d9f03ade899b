"""
Hugging Face connector for the Multi-Agent AI System.
"""
from typing import Dict, List, Optional, Any, Union
import asyncio
import json
from datetime import datetime
import aiohttp
from tenacity import retry, stop_after_attempt, wait_exponential

from llm.llm_connector import LLMConnector
from core.logger import setup_logger

class HuggingfaceConnector(LLMConnector):
    """
    Connector for Hugging Face Inference API.
    
    This connector allows the system to use any model available on the
    Hugging Face Hub, including open-source models like Llama, Mistral,
    Falcon, and many others.
    """
    
    def __init__(self, config: Dict):
        """
        Initialize the Hugging Face connector.
        
        Args:
            config (Dict): LLM configuration
        """
        self.provider = "huggingface"
        super().__init__(config)
        
        # Hugging Face-specific configuration
        self.api_url = "https://api-inference.huggingface.co/models"
        self.use_local = config.get("use_local", False)
        self.local_url = config.get("local_url", "http://localhost:8080")
        
        # For local inference, we might need different parameters
        if self.use_local:
            self.logger.info(f"Using local Hugging Face models at {self.local_url}")
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=1, max=10))
    async def generate_text(
        self,
        prompt: str,
        model: Optional[str] = None,
        max_tokens: int = 1000,
        temperature: float = 0.7,
        stop_sequences: Optional[List[str]] = None,
        **kwargs
    ) -> Dict:
        """
        Generate text using Hugging Face models.
        
        Args:
            prompt (str): Input prompt
            model (Optional[str]): Model to use, defaults to default_model
            max_tokens (int): Maximum number of tokens to generate
            temperature (float): Sampling temperature
            stop_sequences (Optional[List[str]]): Sequences that stop generation
            **kwargs: Additional model-specific parameters
            
        Returns:
            Dict: Response containing generated text and metadata
        """
        if not self.enabled:
            return {"error": f"{self.provider} is not enabled"}
        
        if not model:
            model = self.default_model
        
        if model not in self.models:
            self.logger.warning(f"Model {model} not available, using {self.default_model}")
            model = self.default_model
        
        # Prepare request payload
        payload = {
            "inputs": prompt,
            "parameters": {
                "max_new_tokens": max_tokens,
                "temperature": temperature,
                "return_full_text": kwargs.get("return_full_text", False),
                "do_sample": True,
            }
        }
        
        # Add stop sequences if provided
        if stop_sequences:
            payload["parameters"]["stop"] = stop_sequences
        
        # Add additional parameters
        for key, value in kwargs.items():
            if key not in ["return_full_text"]:
                payload["parameters"][key] = value
        
        # Make API request
        start_time = datetime.now()
        try:
            async with aiohttp.ClientSession() as session:
                # Determine URL based on local or remote
                url = f"{self.local_url}/generate" if self.use_local else f"{self.api_url}/{model}"
                
                # Add model to payload for local inference
                if self.use_local:
                    payload["model"] = model
                
                headers = {
                    "Content-Type": "application/json"
                }
                
                # Add authorization for remote API
                if not self.use_local:
                    headers["Authorization"] = f"Bearer {self.api_key}"
                
                async with session.post(
                    url,
                    headers=headers,
                    json=payload,
                    timeout=60  # Longer timeout for model inference
                ) as response:
                    response_json = await response.json()
                    
                    if response.status != 200:
                        self.logger.error(f"Hugging Face API error: {response.status} - {response_json}")
                        return {
                            "error": f"API error: {response.status}",
                            "details": response_json,
                        }
                    
                    # Process response
                    end_time = datetime.now()
                    latency = (end_time - start_time).total_seconds()
                    
                    # Extract text from response (format varies by model)
                    text = ""
                    if isinstance(response_json, list) and len(response_json) > 0:
                        if "generated_text" in response_json[0]:
                            text = response_json[0]["generated_text"]
                        else:
                            text = str(response_json[0])
                    elif isinstance(response_json, dict):
                        if "generated_text" in response_json:
                            text = response_json["generated_text"]
                        elif "text" in response_json:
                            text = response_json["text"]
                    
                    return {
                        "text": text,
                        "model": model,
                        "provider": self.provider,
                        "latency": latency,
                        "raw_response": response_json,
                    }
        
        except Exception as e:
            self.logger.exception(f"Error calling Hugging Face API: {e}")
            return {
                "error": f"API request failed: {str(e)}",
                "model": model,
                "provider": self.provider,
            }
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=1, max=10))
    async def generate_chat(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        max_tokens: int = 1000,
        temperature: float = 0.7,
        stop_sequences: Optional[List[str]] = None,
        **kwargs
    ) -> Dict:
        """
        Generate a chat response using Hugging Face models.
        
        Args:
            messages (List[Dict[str, str]]): List of message dictionaries
            model (Optional[str]): Model to use, defaults to default_model
            max_tokens (int): Maximum number of tokens to generate
            temperature (float): Sampling temperature
            stop_sequences (Optional[List[str]]): Sequences that stop generation
            **kwargs: Additional model-specific parameters
            
        Returns:
            Dict: Response containing generated text and metadata
        """
        if not self.enabled:
            return {"error": f"{self.provider} is not enabled"}
        
        if not model:
            model = self.default_model
        
        if model not in self.models:
            self.logger.warning(f"Model {model} not available, using {self.default_model}")
            model = self.default_model
        
        # Convert messages to a format suitable for the model
        # This varies by model family, so we'll use a common format and adapt as needed
        conversation = []
        for message in messages:
            role = message.get("role", "user")
            content = message.get("content", "")
            
            if role == "system":
                conversation.append({"role": "system", "content": content})
            elif role == "user":
                conversation.append({"role": "user", "content": content})
            elif role == "assistant":
                conversation.append({"role": "assistant", "content": content})
        
        # Prepare request payload
        payload = {
            "inputs": conversation,
            "parameters": {
                "max_new_tokens": max_tokens,
                "temperature": temperature,
                "do_sample": True,
            }
        }
        
        # Add stop sequences if provided
        if stop_sequences:
            payload["parameters"]["stop"] = stop_sequences
        
        # Add additional parameters
        for key, value in kwargs.items():
            payload["parameters"][key] = value
        
        # Make API request
        start_time = datetime.now()
        try:
            async with aiohttp.ClientSession() as session:
                # Determine URL based on local or remote
                url = f"{self.local_url}/chat" if self.use_local else f"{self.api_url}/{model}"
                
                # Add model to payload for local inference
                if self.use_local:
                    payload["model"] = model
                
                headers = {
                    "Content-Type": "application/json"
                }
                
                # Add authorization for remote API
                if not self.use_local:
                    headers["Authorization"] = f"Bearer {self.api_key}"
                
                async with session.post(
                    url,
                    headers=headers,
                    json=payload,
                    timeout=60  # Longer timeout for model inference
                ) as response:
                    response_json = await response.json()
                    
                    if response.status != 200:
                        self.logger.error(f"Hugging Face API error: {response.status} - {response_json}")
                        return {
                            "error": f"API error: {response.status}",
                            "details": response_json,
                        }
                    
                    # Process response
                    end_time = datetime.now()
                    latency = (end_time - start_time).total_seconds()
                    
                    # Extract text from response (format varies by model)
                    text = ""
                    if isinstance(response_json, dict):
                        if "generated_text" in response_json:
                            text = response_json["generated_text"]
                        elif "response" in response_json:
                            text = response_json["response"]
                    elif isinstance(response_json, list) and len(response_json) > 0:
                        if isinstance(response_json[0], dict) and "generated_text" in response_json[0]:
                            text = response_json[0]["generated_text"]
                    
                    return {
                        "text": text,
                        "model": model,
                        "provider": self.provider,
                        "latency": latency,
                        "raw_response": response_json,
                    }
        
        except Exception as e:
            self.logger.exception(f"Error calling Hugging Face API: {e}")
            return {
                "error": f"API request failed: {str(e)}",
                "model": model,
                "provider": self.provider,
            }
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=1, max=10))
    async def embed_text(
        self,
        text: Union[str, List[str]],
        model: Optional[str] = None,
        **kwargs
    ) -> Dict:
        """
        Generate embeddings for text using Hugging Face models.
        
        Args:
            text (Union[str, List[str]]): Text to embed
            model (Optional[str]): Model to use, defaults to default_model
            **kwargs: Additional model-specific parameters
            
        Returns:
            Dict: Response containing embeddings and metadata
        """
        if not self.enabled:
            return {"error": f"{self.provider} is not enabled"}
        
        # Use a specific embedding model if not specified
        if not model:
            # Check if we have a specific embedding model configured
            embedding_model = kwargs.get("embedding_model")
            if embedding_model and embedding_model in self.models:
                model = embedding_model
            else:
                model = self.default_model
        
        if model not in self.models:
            self.logger.warning(f"Model {model} not available, using {self.default_model}")
            model = self.default_model
        
        # Prepare request payload
        payload = {
            "inputs": text if isinstance(text, list) else [text],
        }
        
        # Add additional parameters
        for key, value in kwargs.items():
            if key != "embedding_model":
                payload[key] = value
        
        # Make API request
        start_time = datetime.now()
        try:
            async with aiohttp.ClientSession() as session:
                # Determine URL based on local or remote
                url = f"{self.local_url}/embeddings" if self.use_local else f"{self.api_url}/{model}"
                
                # Add model to payload for local inference
                if self.use_local:
                    payload["model"] = model
                
                headers = {
                    "Content-Type": "application/json"
                }
                
                # Add authorization for remote API
                if not self.use_local:
                    headers["Authorization"] = f"Bearer {self.api_key}"
                
                async with session.post(
                    url,
                    headers=headers,
                    json=payload,
                    timeout=60  # Longer timeout for model inference
                ) as response:
                    response_json = await response.json()
                    
                    if response.status != 200:
                        self.logger.error(f"Hugging Face API error: {response.status} - {response_json}")
                        return {
                            "error": f"API error: {response.status}",
                            "details": response_json,
                        }
                    
                    # Process response
                    end_time = datetime.now()
                    latency = (end_time - start_time).total_seconds()
                    
                    # Extract embeddings from response
                    embeddings = []
                    if isinstance(response_json, list):
                        embeddings = response_json
                    
                    return {
                        "embeddings": embeddings,
                        "model": model,
                        "provider": self.provider,
                        "latency": latency,
                        "raw_response": response_json,
                    }
        
        except Exception as e:
            self.logger.exception(f"Error calling Hugging Face API: {e}")
            return {
                "error": f"API request failed: {str(e)}",
                "model": model,
                "provider": self.provider,
            }
