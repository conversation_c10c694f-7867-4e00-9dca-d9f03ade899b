@echo off
echo UI-TARS Diagnostics
echo ==================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Check if required packages are installed
python -c "import requests, psutil" >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing required packages...
    python -m pip install requests psutil
)

REM Ask for UI-TARS path
echo Enter the path to UI-TARS executable (leave empty to auto-detect):
set /p UI_TARS_PATH=""

REM Ask for browser type
echo.
echo Select browser to use:
echo 1. Chrome (recommended)
echo 2. Edge
echo 3. Firefox
echo 4. Brave
echo 5. Auto-detect (default)
echo.
set /p BROWSER_CHOICE="Enter choice (1-5): "

if "%BROWSER_CHOICE%"=="1" (
    set BROWSER_TYPE=chrome
) else if "%BROWSER_CHOICE%"=="2" (
    set BROWSER_TYPE=edge
) else if "%BROWSER_CHOICE%"=="3" (
    set BROWSER_TYPE=firefox
) else if "%BROWSER_CHOICE%"=="4" (
    set BROWSER_TYPE=brave
) else (
    set BROWSER_TYPE=
)

REM Ask for browser path if browser type is selected
if not "%BROWSER_TYPE%"=="" (
    echo.
    echo Enter the path to %BROWSER_TYPE% executable (leave empty to auto-detect):
    set /p BROWSER_PATH=""
)

REM Ask if user wants to fix issues
echo.
echo Do you want to attempt to fix common issues? (Y/N, default: Y)
set /p FIX_ISSUES="Fix issues? "

if /i "%FIX_ISSUES%"=="N" (
    set FIX_FLAG=
) else (
    set FIX_FLAG=--fix
)

REM Ask if user wants to start UI-TARS
echo.
echo Do you want to start UI-TARS if it's not running? (Y/N, default: Y)
set /p START_UI_TARS="Start UI-TARS? "

if /i "%START_UI_TARS%"=="N" (
    set START_FLAG=
) else (
    set START_FLAG=--start
)

REM Ask if user wants to use enhanced diagnostics
echo.
echo Do you want to use enhanced diagnostics? (Y/N, default: Y)
set /p ENHANCED_DIAG="Use enhanced diagnostics? "

if /i "%ENHANCED_DIAG%"=="N" (
    set ENHANCED_FLAG=
) else (
    set ENHANCED_FLAG=--enhanced
)

REM Run the diagnostic script
echo.
echo Running UI-TARS diagnostics...
echo.

set COMMAND=python ui_tars_diagnostic.py

if not "%UI_TARS_PATH%"=="" (
    set COMMAND=%COMMAND% --path "%UI_TARS_PATH%"
)

if not "%BROWSER_TYPE%"=="" (
    set COMMAND=%COMMAND% --browser %BROWSER_TYPE%
)

if not "%BROWSER_PATH%"=="" (
    set COMMAND=%COMMAND% --browser-path "%BROWSER_PATH%"
)

if not "%FIX_FLAG%"=="" (
    set COMMAND=%COMMAND% %FIX_FLAG%
)

if not "%START_FLAG%"=="" (
    set COMMAND=%COMMAND% %START_FLAG%
)

if not "%ENHANCED_FLAG%"=="" (
    set COMMAND=%COMMAND% %ENHANCED_FLAG%
)

echo Executing: %COMMAND%
echo.

%COMMAND%

echo.
echo Diagnostics completed. Please check the recommendations above.
echo.
pause
