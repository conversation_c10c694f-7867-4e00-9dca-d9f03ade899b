"""
Test script for the enhanced UI-TARS integration.

This script tests the enhanced UI-TARS integration by:
1. Running the diagnostic tool
2. Initializing the enhanced UI-TARS connector
3. Executing a simple browser automation task
4. Testing Gmail and Google Voice integration
"""
import os
import sys
import asyncio
import argparse
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("test_ui_tars_integration.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("test_ui_tars_integration")

async def run_diagnostic():
    """Run the UI-TARS diagnostic tool."""
    logger.info("Running UI-TARS diagnostic tool")
    
    try:
        import subprocess
        result = subprocess.run(["python", "ui_tars_diagnostic.py", "--fix", "--start"], capture_output=True, text=True)
        
        logger.info(f"Diagnostic tool output:\n{result.stdout}")
        
        if result.returncode != 0:
            logger.error(f"Diagnostic tool failed with return code {result.returncode}")
            logger.error(f"Error output:\n{result.stderr}")
            return False
        
        return True
    
    except Exception as e:
        logger.exception(f"Error running diagnostic tool: {e}")
        return False

async def test_ui_tars_connector():
    """Test the enhanced UI-TARS connector."""
    logger.info("Testing enhanced UI-TARS connector")
    
    try:
        from ui_tars.connector.enhanced_ui_tars_connector import EnhancedUITarsConnector
        
        # Initialize connector
        connector = EnhancedUITarsConnector(
            api_url="http://localhost:8080",
            api_key="hf_dummy_key",
            model_name="UI-TARS-1.5-7B",
            browser_type="chrome",
            auto_start=True,
            auto_restart=True
        )
        
        # Initialize connector
        logger.info("Initializing connector")
        success = await connector.initialize()
        
        if not success:
            logger.error("Failed to initialize connector")
            return False
        
        # Execute a simple command
        logger.info("Executing command")
        result = await connector.execute_command("Browse to https://www.google.com")
        
        # Wait a moment
        await asyncio.sleep(5)
        
        # Take a screenshot
        logger.info("Taking screenshot")
        screenshot_command = "Take a screenshot"
        screenshot_result = await connector.execute_command(screenshot_command)
        
        # Stop connector
        logger.info("Stopping connector")
        await connector.stop()
        
        return True
    
    except Exception as e:
        logger.exception(f"Error testing connector: {e}")
        return False

async def test_gmail_agent():
    """Test the Gmail automation agent."""
    logger.info("Testing Gmail automation agent")
    
    try:
        from ui_tars.agent.gmail_automation_agent import GmailAutomationAgent
        
        # Initialize agent
        agent = GmailAutomationAgent(
            api_url="http://localhost:8080",
            api_key="hf_dummy_key",
            model_name="UI-TARS-1.5-7B",
            browser_type="chrome",
            auto_start=True,
            auto_restart=True,
            default_email="<EMAIL>",
            default_password="GodisSoGood!777"
        )
        
        # Initialize agent
        logger.info("Initializing Gmail agent")
        success = await agent.initialize()
        
        if not success:
            logger.error("Failed to initialize Gmail agent")
            return False
        
        # Log in to Gmail
        logger.info("Logging in to Gmail")
        login_result = await agent.login_to_gmail()
        
        if not login_result["success"]:
            logger.error(f"Failed to log in to Gmail: {login_result.get('error', 'Unknown error')}")
            return False
        
        # Check inbox
        logger.info("Checking inbox")
        inbox_result = await agent.check_inbox()
        
        # Logout
        logger.info("Logging out from Gmail")
        await agent.logout_from_gmail()
        
        # Shutdown agent
        logger.info("Shutting down Gmail agent")
        await agent.shutdown()
        
        return True
    
    except Exception as e:
        logger.exception(f"Error testing Gmail agent: {e}")
        return False

async def test_google_voice_agent():
    """Test the Google Voice automation agent."""
    logger.info("Testing Google Voice automation agent")
    
    try:
        from ui_tars.agent.google_voice_automation_agent import GoogleVoiceAutomationAgent
        
        # Initialize agent
        agent = GoogleVoiceAutomationAgent(
            api_url="http://localhost:8080",
            api_key="hf_dummy_key",
            model_name="UI-TARS-1.5-7B",
            browser_type="chrome",
            auto_start=True,
            auto_restart=True,
            default_email="<EMAIL>",
            default_password="GodisSoGood!777",
            default_phone_number="7722089646"
        )
        
        # Initialize agent
        logger.info("Initializing Google Voice agent")
        success = await agent.initialize()
        
        if not success:
            logger.error("Failed to initialize Google Voice agent")
            return False
        
        # Log in to Google Voice
        logger.info("Logging in to Google Voice")
        login_result = await agent.login_to_google_voice()
        
        if not login_result["success"]:
            logger.error(f"Failed to log in to Google Voice: {login_result.get('error', 'Unknown error')}")
            return False
        
        # Check messages
        logger.info("Checking messages")
        messages_result = await agent.check_text_messages()
        
        # Logout
        logger.info("Logging out from Google Voice")
        await agent.logout_from_google_voice()
        
        # Shutdown agent
        logger.info("Shutting down Google Voice agent")
        await agent.shutdown()
        
        return True
    
    except Exception as e:
        logger.exception(f"Error testing Google Voice agent: {e}")
        return False

async def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Test UI-TARS Integration")
    parser.add_argument("--skip-diagnostic", action="store_true", help="Skip running the diagnostic tool")
    parser.add_argument("--skip-connector", action="store_true", help="Skip testing the connector")
    parser.add_argument("--skip-gmail", action="store_true", help="Skip testing the Gmail agent")
    parser.add_argument("--skip-voice", action="store_true", help="Skip testing the Google Voice agent")
    
    args = parser.parse_args()
    
    print("UI-TARS Integration Test")
    print("=======================")
    print()
    
    # Run diagnostic tool
    if not args.skip_diagnostic:
        print("Running diagnostic tool...")
        diagnostic_success = await run_diagnostic()
        print(f"Diagnostic tool: {'Success' if diagnostic_success else 'Failed'}")
        print()
    
    # Test UI-TARS connector
    if not args.skip_connector:
        print("Testing UI-TARS connector...")
        connector_success = await test_ui_tars_connector()
        print(f"UI-TARS connector: {'Success' if connector_success else 'Failed'}")
        print()
    
    # Test Gmail agent
    if not args.skip_gmail:
        print("Testing Gmail agent...")
        gmail_success = await test_gmail_agent()
        print(f"Gmail agent: {'Success' if gmail_success else 'Failed'}")
        print()
    
    # Test Google Voice agent
    if not args.skip_voice:
        print("Testing Google Voice agent...")
        voice_success = await test_google_voice_agent()
        print(f"Google Voice agent: {'Success' if voice_success else 'Failed'}")
        print()
    
    print("Test completed. See test_ui_tars_integration.log for details.")
    
    return 0

if __name__ == "__main__":
    asyncio.run(main())
