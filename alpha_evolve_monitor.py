"""
AlphaEvolve Performance Monitor.

This script monitors the performance of AlphaEvolve and tracks improvements
in agent capabilities and system efficiency.
"""
import asyncio
import argparse
import json
import logging
import os
import sys
import time
from pathlib import Path
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import numpy as np

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent))

from core.logger import setup_logger
from core.state_manager import StateManager
from alpha_evolve.alpha_evolve_engine import AlphaEvolveEngine

# Set up logger
logger = setup_logger("alpha_evolve_monitor")

class AlphaEvolveMonitor:
    """
    AlphaEvolve Performance Monitor.

    This class monitors the performance of AlphaEvolve and tracks improvements
    in agent capabilities and system efficiency.
    """

    def __init__(self, state_manager: StateManager = None):
        """
        Initialize the AlphaEvolve Monitor.

        Args:
            state_manager (StateManager, optional): State manager
        """
        self.state_manager = state_manager
        self.initialized = False
        
        # Performance metrics
        self.evolution_metrics = {}
        self.agent_metrics = {}
        self.system_metrics = {}
        
        # Baseline metrics
        self.baselines = {}
        
        # Output directory
        self.output_dir = Path("alpha_evolve_metrics")
        
    async def initialize(self):
        """Initialize the AlphaEvolve Monitor."""
        logger.info("Initializing AlphaEvolve Monitor")
        
        # Initialize state manager if not provided
        if not self.state_manager:
            from core.state_manager import StateManager
            self.state_manager = StateManager()
            await self.state_manager.initialize()
        
        # Create output directory
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Load existing metrics
        await self._load_metrics()
        
        self.initialized = True
        logger.info("AlphaEvolve Monitor initialized")
    
    async def _load_metrics(self):
        """Load existing metrics from state manager."""
        try:
            # Load evolution metrics
            evolution_metrics = await self.state_manager.get_state("alpha_evolve", "evolution_metrics")
            if evolution_metrics:
                self.evolution_metrics = evolution_metrics
                logger.info(f"Loaded {len(evolution_metrics)} evolution metrics")
            
            # Load agent metrics
            agent_metrics = await self.state_manager.get_state("alpha_evolve", "agent_metrics")
            if agent_metrics:
                self.agent_metrics = agent_metrics
                logger.info(f"Loaded metrics for {len(agent_metrics)} agents")
            
            # Load system metrics
            system_metrics = await self.state_manager.get_state("alpha_evolve", "system_metrics")
            if system_metrics:
                self.system_metrics = system_metrics
                logger.info(f"Loaded system metrics")
            
            # Load baselines
            baselines = await self.state_manager.get_state("alpha_evolve", "baselines")
            if baselines:
                self.baselines = baselines
                logger.info(f"Loaded baselines")
        
        except Exception as e:
            logger.exception(f"Error loading metrics: {e}")
    
    async def _save_metrics(self):
        """Save metrics to state manager."""
        try:
            # Save evolution metrics
            await self.state_manager.update_state("alpha_evolve", "evolution_metrics", self.evolution_metrics)
            
            # Save agent metrics
            await self.state_manager.update_state("alpha_evolve", "agent_metrics", self.agent_metrics)
            
            # Save system metrics
            await self.state_manager.update_state("alpha_evolve", "system_metrics", self.system_metrics)
            
            # Save baselines
            await self.state_manager.update_state("alpha_evolve", "baselines", self.baselines)
            
            logger.info("Saved metrics")
        
        except Exception as e:
            logger.exception(f"Error saving metrics: {e}")
    
    async def collect_evolution_metrics(self, alpha_evolve_engine: AlphaEvolveEngine):
        """
        Collect evolution metrics from AlphaEvolve engine.
        
        Args:
            alpha_evolve_engine (AlphaEvolveEngine): AlphaEvolve engine
        """
        if not self.initialized:
            await self.initialize()
        
        logger.info("Collecting evolution metrics")
        
        try:
            # Get evolution history
            evolution_history = alpha_evolve_engine.evolution_history
            
            # Process each evolution
            for evolution_id, evolution in evolution_history.items():
                # Skip if already processed
                if evolution_id in self.evolution_metrics:
                    continue
                
                # Extract metrics
                metrics = {
                    "id": evolution_id,
                    "problem_type": evolution.get("problem", {}).get("type", "unknown"),
                    "start_time": evolution.get("start_time"),
                    "end_time": evolution.get("end_time"),
                    "status": evolution.get("status"),
                    "generations": evolution.get("current_generation", 0),
                    "best_fitness": evolution.get("best_fitness", 0.0),
                    "fitness_history": evolution.get("fitness_history", []),
                }
                
                # Calculate duration
                if metrics["start_time"] and metrics["end_time"]:
                    start_time = datetime.fromisoformat(metrics["start_time"])
                    end_time = datetime.fromisoformat(metrics["end_time"])
                    duration = (end_time - start_time).total_seconds()
                    metrics["duration"] = duration
                
                # Store metrics
                self.evolution_metrics[evolution_id] = metrics
            
            logger.info(f"Collected metrics for {len(self.evolution_metrics)} evolutions")
            
            # Save metrics
            await self._save_metrics()
        
        except Exception as e:
            logger.exception(f"Error collecting evolution metrics: {e}")
    
    async def collect_agent_metrics(self, agent_integration):
        """
        Collect agent metrics from agent integration.
        
        Args:
            agent_integration: Agent integration
        """
        if not self.initialized:
            await self.initialize()
        
        logger.info("Collecting agent metrics")
        
        try:
            # Get enhancement tasks
            enhancement_tasks = agent_integration.enhancement_tasks
            
            # Process each task
            for task_id, task in enhancement_tasks.items():
                # Skip if already processed
                if task_id in self.agent_metrics:
                    continue
                
                # Extract metrics
                metrics = {
                    "id": task_id,
                    "agent_id": task.get("agent_id"),
                    "capability": task.get("capability"),
                    "optimization_metric": task.get("optimization_metric"),
                    "start_time": task.get("start_time"),
                    "end_time": task.get("end_time"),
                    "status": task.get("status"),
                    "model_id": task.get("model_id"),
                }
                
                # Add result metrics if available
                if task.get("result"):
                    result = task["result"]
                    metrics["evolution_id"] = result.get("evolution_id")
                    metrics["generations"] = result.get("generations", 0)
                    metrics["best_fitness"] = result.get("best_fitness", 0.0)
                
                # Calculate duration
                if metrics["start_time"] and metrics["end_time"]:
                    start_time = datetime.fromisoformat(metrics["start_time"])
                    end_time = datetime.fromisoformat(metrics["end_time"])
                    duration = (end_time - start_time).total_seconds()
                    metrics["duration"] = duration
                
                # Store metrics
                self.agent_metrics[task_id] = metrics
            
            logger.info(f"Collected metrics for {len(self.agent_metrics)} agent enhancements")
            
            # Save metrics
            await self._save_metrics()
        
        except Exception as e:
            logger.exception(f"Error collecting agent metrics: {e}")
    
    async def collect_system_metrics(self):
        """Collect system-wide metrics."""
        if not self.initialized:
            await self.initialize()
        
        logger.info("Collecting system metrics")
        
        try:
            # Get current timestamp
            timestamp = datetime.now().isoformat()
            
            # Calculate evolution success rate
            total_evolutions = len(self.evolution_metrics)
            successful_evolutions = sum(1 for m in self.evolution_metrics.values() if m.get("status") == "completed")
            evolution_success_rate = successful_evolutions / total_evolutions if total_evolutions > 0 else 0
            
            # Calculate average fitness improvement
            fitness_improvements = []
            for m in self.evolution_metrics.values():
                fitness_history = m.get("fitness_history", [])
                if fitness_history:
                    initial_fitness = fitness_history[0].get("best_fitness", 0)
                    final_fitness = fitness_history[-1].get("best_fitness", 0)
                    improvement = final_fitness - initial_fitness
                    fitness_improvements.append(improvement)
            
            avg_fitness_improvement = sum(fitness_improvements) / len(fitness_improvements) if fitness_improvements else 0
            
            # Calculate agent enhancement success rate
            total_enhancements = len(self.agent_metrics)
            successful_enhancements = sum(1 for m in self.agent_metrics.values() if m.get("status") == "completed")
            enhancement_success_rate = successful_enhancements / total_enhancements if total_enhancements > 0 else 0
            
            # Store system metrics
            self.system_metrics[timestamp] = {
                "timestamp": timestamp,
                "total_evolutions": total_evolutions,
                "successful_evolutions": successful_evolutions,
                "evolution_success_rate": evolution_success_rate,
                "avg_fitness_improvement": avg_fitness_improvement,
                "total_enhancements": total_enhancements,
                "successful_enhancements": successful_enhancements,
                "enhancement_success_rate": enhancement_success_rate,
            }
            
            logger.info("Collected system metrics")
            
            # Save metrics
            await self._save_metrics()
        
        except Exception as e:
            logger.exception(f"Error collecting system metrics: {e}")
    
    async def establish_baseline(self, agent_id: str, capability: str, baseline_value: float):
        """
        Establish a baseline for an agent capability.
        
        Args:
            agent_id (str): Agent ID
            capability (str): Capability
            baseline_value (float): Baseline value
        """
        if not self.initialized:
            await self.initialize()
        
        logger.info(f"Establishing baseline for {agent_id}.{capability}: {baseline_value}")
        
        try:
            # Create baseline key
            key = f"{agent_id}.{capability}"
            
            # Store baseline
            self.baselines[key] = {
                "agent_id": agent_id,
                "capability": capability,
                "value": baseline_value,
                "timestamp": datetime.now().isoformat(),
            }
            
            # Save metrics
            await self._save_metrics()
        
        except Exception as e:
            logger.exception(f"Error establishing baseline: {e}")
    
    async def calculate_improvement(self, agent_id: str, capability: str, current_value: float) -> float:
        """
        Calculate improvement over baseline.
        
        Args:
            agent_id (str): Agent ID
            capability (str): Capability
            current_value (float): Current value
            
        Returns:
            float: Improvement percentage
        """
        if not self.initialized:
            await self.initialize()
        
        try:
            # Create baseline key
            key = f"{agent_id}.{capability}"
            
            # Get baseline
            baseline = self.baselines.get(key)
            
            if baseline:
                baseline_value = baseline.get("value", 0)
                
                # Calculate improvement
                if baseline_value > 0:
                    improvement = (current_value - baseline_value) / baseline_value * 100
                else:
                    improvement = 0 if current_value == 0 else 100
                
                return improvement
            else:
                logger.warning(f"No baseline found for {agent_id}.{capability}")
                return 0
        
        except Exception as e:
            logger.exception(f"Error calculating improvement: {e}")
            return 0
    
    async def generate_reports(self):
        """Generate performance reports."""
        if not self.initialized:
            await self.initialize()
        
        logger.info("Generating performance reports")
        
        try:
            # Generate evolution report
            await self._generate_evolution_report()
            
            # Generate agent report
            await self._generate_agent_report()
            
            # Generate system report
            await self._generate_system_report()
            
            logger.info("Generated performance reports")
        
        except Exception as e:
            logger.exception(f"Error generating reports: {e}")
    
    async def _generate_evolution_report(self):
        """Generate evolution performance report."""
        try:
            # Create report file
            report_path = self.output_dir / "evolution_report.json"
            
            # Generate report data
            report = {
                "timestamp": datetime.now().isoformat(),
                "total_evolutions": len(self.evolution_metrics),
                "evolutions_by_status": {},
                "evolutions_by_problem_type": {},
                "average_duration": 0,
                "average_generations": 0,
                "average_best_fitness": 0,
            }
            
            # Calculate metrics
            durations = []
            generations = []
            fitness_scores = []
            
            for metrics in self.evolution_metrics.values():
                # Count by status
                status = metrics.get("status", "unknown")
                report["evolutions_by_status"][status] = report["evolutions_by_status"].get(status, 0) + 1
                
                # Count by problem type
                problem_type = metrics.get("problem_type", "unknown")
                report["evolutions_by_problem_type"][problem_type] = report["evolutions_by_problem_type"].get(problem_type, 0) + 1
                
                # Collect metrics
                if metrics.get("duration"):
                    durations.append(metrics["duration"])
                
                generations.append(metrics.get("generations", 0))
                fitness_scores.append(metrics.get("best_fitness", 0))
            
            # Calculate averages
            report["average_duration"] = sum(durations) / len(durations) if durations else 0
            report["average_generations"] = sum(generations) / len(generations) if generations else 0
            report["average_best_fitness"] = sum(fitness_scores) / len(fitness_scores) if fitness_scores else 0
            
            # Save report
            with open(report_path, "w") as f:
                json.dump(report, f, indent=2)
            
            logger.info(f"Evolution report saved to {report_path}")
            
            # Generate fitness history chart
            self._generate_fitness_history_chart()
        
        except Exception as e:
            logger.exception(f"Error generating evolution report: {e}")
    
    def _generate_fitness_history_chart(self):
        """Generate fitness history chart."""
        try:
            # Create chart file
            chart_path = self.output_dir / "fitness_history.png"
            
            # Collect fitness histories
            histories = []
            
            for metrics in self.evolution_metrics.values():
                fitness_history = metrics.get("fitness_history", [])
                if fitness_history:
                    # Extract best fitness values
                    generations = [h.get("generation", 0) for h in fitness_history]
                    fitness_values = [h.get("best_fitness", 0) for h in fitness_history]
                    
                    histories.append((generations, fitness_values, metrics.get("problem_type", "unknown")))
            
            if not histories:
                logger.warning("No fitness histories found")
                return
            
            # Create chart
            plt.figure(figsize=(10, 6))
            
            for generations, fitness_values, problem_type in histories[:10]:  # Limit to 10 histories
                plt.plot(generations, fitness_values, label=f"{problem_type[:15]}...")
            
            plt.xlabel("Generation")
            plt.ylabel("Best Fitness")
            plt.title("Fitness History")
            plt.legend()
            plt.grid(True)
            
            # Save chart
            plt.savefig(chart_path)
            plt.close()
            
            logger.info(f"Fitness history chart saved to {chart_path}")
        
        except Exception as e:
            logger.exception(f"Error generating fitness history chart: {e}")
    
    async def _generate_agent_report(self):
        """Generate agent performance report."""
        try:
            # Create report file
            report_path = self.output_dir / "agent_report.json"
            
            # Generate report data
            report = {
                "timestamp": datetime.now().isoformat(),
                "total_enhancements": len(self.agent_metrics),
                "enhancements_by_status": {},
                "enhancements_by_agent": {},
                "enhancements_by_capability": {},
                "average_duration": 0,
                "average_best_fitness": 0,
            }
            
            # Calculate metrics
            durations = []
            fitness_scores = []
            
            for metrics in self.agent_metrics.values():
                # Count by status
                status = metrics.get("status", "unknown")
                report["enhancements_by_status"][status] = report["enhancements_by_status"].get(status, 0) + 1
                
                # Count by agent
                agent_id = metrics.get("agent_id", "unknown")
                report["enhancements_by_agent"][agent_id] = report["enhancements_by_agent"].get(agent_id, 0) + 1
                
                # Count by capability
                capability = metrics.get("capability", "unknown")
                report["enhancements_by_capability"][capability] = report["enhancements_by_capability"].get(capability, 0) + 1
                
                # Collect metrics
                if metrics.get("duration"):
                    durations.append(metrics["duration"])
                
                fitness_scores.append(metrics.get("best_fitness", 0))
            
            # Calculate averages
            report["average_duration"] = sum(durations) / len(durations) if durations else 0
            report["average_best_fitness"] = sum(fitness_scores) / len(fitness_scores) if fitness_scores else 0
            
            # Save report
            with open(report_path, "w") as f:
                json.dump(report, f, indent=2)
            
            logger.info(f"Agent report saved to {report_path}")
            
            # Generate agent improvement chart
            self._generate_agent_improvement_chart()
        
        except Exception as e:
            logger.exception(f"Error generating agent report: {e}")
    
    def _generate_agent_improvement_chart(self):
        """Generate agent improvement chart."""
        try:
            # Create chart file
            chart_path = self.output_dir / "agent_improvement.png"
            
            # Collect agent improvements
            agent_improvements = {}
            
            for metrics in self.agent_metrics.values():
                if metrics.get("status") == "completed":
                    agent_id = metrics.get("agent_id", "unknown")
                    
                    if agent_id not in agent_improvements:
                        agent_improvements[agent_id] = []
                    
                    agent_improvements[agent_id].append(metrics.get("best_fitness", 0))
            
            if not agent_improvements:
                logger.warning("No agent improvements found")
                return
            
            # Create chart
            plt.figure(figsize=(10, 6))
            
            agents = list(agent_improvements.keys())
            avg_improvements = [sum(improvements) / len(improvements) for improvements in agent_improvements.values()]
            
            plt.bar(agents, avg_improvements)
            
            plt.xlabel("Agent")
            plt.ylabel("Average Fitness")
            plt.title("Agent Improvements")
            plt.xticks(rotation=45)
            plt.tight_layout()
            
            # Save chart
            plt.savefig(chart_path)
            plt.close()
            
            logger.info(f"Agent improvement chart saved to {chart_path}")
        
        except Exception as e:
            logger.exception(f"Error generating agent improvement chart: {e}")
    
    async def _generate_system_report(self):
        """Generate system performance report."""
        try:
            # Create report file
            report_path = self.output_dir / "system_report.json"
            
            # Generate report data
            report = {
                "timestamp": datetime.now().isoformat(),
                "total_metrics": len(self.system_metrics),
                "latest_metrics": None,
                "evolution_success_rate_trend": [],
                "enhancement_success_rate_trend": [],
                "avg_fitness_improvement_trend": [],
            }
            
            # Get latest metrics
            if self.system_metrics:
                latest_timestamp = max(self.system_metrics.keys())
                report["latest_metrics"] = self.system_metrics[latest_timestamp]
            
            # Calculate trends
            timestamps = sorted(self.system_metrics.keys())
            
            for timestamp in timestamps:
                metrics = self.system_metrics[timestamp]
                
                report["evolution_success_rate_trend"].append({
                    "timestamp": timestamp,
                    "value": metrics.get("evolution_success_rate", 0),
                })
                
                report["enhancement_success_rate_trend"].append({
                    "timestamp": timestamp,
                    "value": metrics.get("enhancement_success_rate", 0),
                })
                
                report["avg_fitness_improvement_trend"].append({
                    "timestamp": timestamp,
                    "value": metrics.get("avg_fitness_improvement", 0),
                })
            
            # Save report
            with open(report_path, "w") as f:
                json.dump(report, f, indent=2)
            
            logger.info(f"System report saved to {report_path}")
            
            # Generate system trends chart
            self._generate_system_trends_chart()
        
        except Exception as e:
            logger.exception(f"Error generating system report: {e}")
    
    def _generate_system_trends_chart(self):
        """Generate system trends chart."""
        try:
            # Create chart file
            chart_path = self.output_dir / "system_trends.png"
            
            # Collect trend data
            timestamps = sorted(self.system_metrics.keys())
            
            if not timestamps:
                logger.warning("No system metrics found")
                return
            
            # Convert timestamps to datetime objects
            dates = [datetime.fromisoformat(ts) for ts in timestamps]
            
            # Extract metrics
            evolution_success_rates = [self.system_metrics[ts].get("evolution_success_rate", 0) for ts in timestamps]
            enhancement_success_rates = [self.system_metrics[ts].get("enhancement_success_rate", 0) for ts in timestamps]
            fitness_improvements = [self.system_metrics[ts].get("avg_fitness_improvement", 0) for ts in timestamps]
            
            # Create chart
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 8), sharex=True)
            
            # Plot success rates
            ax1.plot(dates, evolution_success_rates, label="Evolution Success Rate")
            ax1.plot(dates, enhancement_success_rates, label="Enhancement Success Rate")
            ax1.set_ylabel("Success Rate")
            ax1.set_title("System Performance Trends")
            ax1.legend()
            ax1.grid(True)
            
            # Plot fitness improvements
            ax2.plot(dates, fitness_improvements, label="Avg Fitness Improvement", color="green")
            ax2.set_xlabel("Date")
            ax2.set_ylabel("Fitness Improvement")
            ax2.legend()
            ax2.grid(True)
            
            plt.tight_layout()
            
            # Save chart
            plt.savefig(chart_path)
            plt.close()
            
            logger.info(f"System trends chart saved to {chart_path}")
        
        except Exception as e:
            logger.exception(f"Error generating system trends chart: {e}")

async def run_monitor(args):
    """
    Run the AlphaEvolve Monitor.
    
    Args:
        args: Command-line arguments
    """
    # Initialize state manager
    state_manager = StateManager()
    await state_manager.initialize()
    
    # Initialize monitor
    monitor = AlphaEvolveMonitor(state_manager)
    await monitor.initialize()
    
    # Initialize AlphaEvolve engine if needed
    alpha_evolve_engine = None
    
    if args.collect_evolution_metrics:
        alpha_evolve_engine = AlphaEvolveEngine(state_manager=state_manager)
        await alpha_evolve_engine.initialize()
    
    # Initialize agent integration if needed
    agent_integration = None
    
    if args.collect_agent_metrics:
        from alpha_evolve.integration.agent_integration import AgentIntegration
        agent_integration = AgentIntegration(
            alpha_evolve_engine=alpha_evolve_engine,
            state_manager=state_manager
        )
        await agent_integration.initialize()
    
    # Collect metrics
    if args.collect_evolution_metrics:
        await monitor.collect_evolution_metrics(alpha_evolve_engine)
    
    if args.collect_agent_metrics:
        await monitor.collect_agent_metrics(agent_integration)
    
    if args.collect_system_metrics:
        await monitor.collect_system_metrics()
    
    # Generate reports
    if args.generate_reports:
        await monitor.generate_reports()
    
    # Establish baseline if requested
    if args.establish_baseline:
        agent_id, capability, baseline_value = args.establish_baseline.split(",")
        await monitor.establish_baseline(agent_id, capability, float(baseline_value))
    
    # Calculate improvement if requested
    if args.calculate_improvement:
        agent_id, capability, current_value = args.calculate_improvement.split(",")
        improvement = await monitor.calculate_improvement(agent_id, capability, float(current_value))
        print(f"Improvement for {agent_id}.{capability}: {improvement:.2f}%")

def main():
    """Main entry point."""
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="AlphaEvolve Performance Monitor")
    parser.add_argument("--collect-evolution-metrics", action="store_true", help="Collect evolution metrics")
    parser.add_argument("--collect-agent-metrics", action="store_true", help="Collect agent metrics")
    parser.add_argument("--collect-system-metrics", action="store_true", help="Collect system metrics")
    parser.add_argument("--generate-reports", action="store_true", help="Generate performance reports")
    parser.add_argument("--establish-baseline", type=str, help="Establish baseline (format: agent_id,capability,value)")
    parser.add_argument("--calculate-improvement", type=str, help="Calculate improvement (format: agent_id,capability,value)")
    args = parser.parse_args()
    
    # Run monitor
    asyncio.run(run_monitor(args))

if __name__ == "__main__":
    main()
