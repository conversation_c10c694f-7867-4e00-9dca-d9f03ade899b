@echo off
echo Checking for UI-TARS installation and Chrome browser...
echo.

echo Operating System: Windows

REM Check for Chrome
echo Checking for Chrome...
if exist "C:\Program Files\Google\Chrome\Application\chrome.exe" (
    echo Chrome found at: C:\Program Files\Google\Chrome\Application\chrome.exe
) else if exist "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" (
    echo Chrome found at: C:\Program Files (x86)\Google\Chrome\Application\chrome.exe
) else (
    echo Chrome not found
)

echo.

REM Check for UI-TARS
echo Checking for UI-TARS...
if exist "%PROGRAMFILES%\UI-TARS\UI-TARS.exe" (
    echo UI-TARS found at: %PROGRAMFILES%\UI-TARS\UI-TARS.exe
) else if exist "%PROGRAMFILES(X86)%\UI-TARS\UI-TARS.exe" (
    echo UI-TARS found at: %PROGRAMFILES(X86)%\UI-TARS\UI-TARS.exe
) else if exist "%LOCALAPPDATA%\UI-TARS\UI-TARS.exe" (
    echo UI-TARS found at: %LOCALAPPDATA%\UI-TARS\UI-TARS.exe
) else (
    echo UI-TARS not found
)

echo.
echo Check completed
pause
