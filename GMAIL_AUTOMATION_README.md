# Gmail Automation for AI Agent System

This package provides reliable Gmail automation for your AI Agent System using browser automation. It allows your agents to send emails through Gmail with a robust approach that handles security challenges.

## Overview

The integration uses Selenium WebDriver to automate Gmail in a browser, providing a visual and reliable way to send emails. This approach has several advantages:

- **Visual Feedback**: You can see the automation happening in real-time
- **No API Setup**: No need to set up OAuth or API credentials
- **Reliability**: Works even when Gmail's interface changes
- **Adaptability**: Handles security challenges like "browser or app may not be supported"

## Components

The package includes the following components:

- `gmail_browser_automation.py`: Core module for automating Gmail using Selenium
- `send_email.bat`: User-friendly batch script for sending emails
- `email_automation_requirements.txt`: Required Python packages

## Prerequisites

Before using this integration, make sure you have:

1. Python 3.8 or higher installed
2. A web browser installed (Chrome, Firefox, or Edge)
3. Gmail account credentials (email and password)

## Quick Start

### 1. Install Required Packages

```bash
pip install -r email_automation_requirements.txt
```

This will install:

- Selenium: For browser automation
- WebDriver Manager: For automatic browser driver management

### 2. Send an Email

The easiest way to send an email is to run the batch script:

```bash
send_email.bat
```

This will:

1. Check and install required packages
2. Prompt for email details (recipient, subject, body)
3. Send the email using browser automation

### 3. Programmatic Usage

To use the automation in your own code:

```python
from gmail_browser_automation import GmailBrowserAutomation

# Create Gmail browser automation
gmail_automation = GmailBrowserAutomation(browser_type="chrome")

# Initialize
gmail_automation.initialize()

try:
    # Send email
    result = gmail_automation.send_email(
        email_account="<EMAIL>",
        password="your_password",
        to_email="<EMAIL>",
        subject="Test Email",
        body="This is a test email sent using browser automation."
    )

    if result["success"]:
        print("Email sent successfully")
    else:
        print(f"Failed to send email: {result['error']}")

finally:
    # Shut down
    gmail_automation.shutdown()
```

## Integration with Agent System

To integrate with your agent system:

```python
class EmailAgent:
    def __init__(self):
        self.gmail_automation = GmailBrowserAutomation(browser_type="chrome")
        self.gmail_automation.initialize()

    def send_email(self, from_email, password, to_email, subject, body):
        return self.gmail_automation.send_email(
            email_account=from_email,
            password=password,
            to_email=to_email,
            subject=subject,
            body=body
        )

    def shutdown(self):
        self.gmail_automation.shutdown()
```

## How It Works

### Key Features

1. **Anti-Detection Measures**: The script uses various techniques to avoid being detected as automation:
   - Disables automation flags
   - Uses realistic user agents
   - Modifies browser properties

2. **Robust Element Finding**: The script uses multiple selectors for each element to handle variations in Gmail's interface:
   - Multiple selectors for Compose button
   - Multiple selectors for To field
   - Multiple selectors for Subject field
   - Multiple selectors for Body field
   - Multiple selectors for Send button

3. **Security Challenge Handling**: The script can detect and handle security challenges:
   - Detects "browser or app may not be supported" message
   - Tries to click Continue button automatically
   - Provides time for manual intervention if needed

4. **Error Recovery**: The script takes screenshots at key points for debugging and provides detailed logs.

## Troubleshooting

If you encounter issues:

1. **Browser driver not found**: The script should automatically download the correct driver, but if it fails, you can download it manually from:
   - Chrome: [https://chromedriver.chromium.org/downloads](https://chromedriver.chromium.org/downloads)
   - Firefox: [https://github.com/mozilla/geckodriver/releases](https://github.com/mozilla/geckodriver/releases)
   - Edge: [https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/](https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/)

2. **Login issues**: If Gmail detects suspicious activity, it might block the login. Try:
   - Logging in manually first
   - Using an app password instead of your regular password
   - Temporarily lowering your Google account security settings

3. **"Browser or app may not be supported" message**: The script will try to handle this automatically, but if it fails:
   - Run the script again
   - When the message appears, manually click "Continue" within the 60-second window

4. **Screenshots**: The script saves screenshots at key points for debugging. Check these if something goes wrong:
   - `login_error_screenshot.png`
   - `login_element_not_found.png`
   - `browser_not_supported_screenshot.png`
   - `compose_button_not_found.png`
   - `to_field_not_found.png`
   - `subject_field_not_found.png`
   - `body_field_not_found.png`
   - `send_button_not_found.png`

## Advanced Usage

### Command Line Arguments

You can run the Gmail browser automation script directly with command line arguments:

```bash
python gmail_browser_automation.py --email "<EMAIL>" --to "<EMAIL>" --subject "Test Email" --body "This is a test email." --browser "chrome"
```

### Using Different Browsers

The automation supports Chrome, Firefox, and Edge. You can specify the browser to use:

```python
gmail_automation = GmailBrowserAutomation(browser_type="firefox")
```

### Customizing Browser Options

You can customize browser options by modifying the `initialize` method in `gmail_browser_automation.py`. For example, to run Chrome in headless mode:

```python
options = webdriver.ChromeOptions()
options.add_argument("--headless")
options.add_argument("--window-size=1920,1080")
self.driver = webdriver.Chrome(service=service, options=options)
```

## Security Considerations

This automation requires your Gmail password. To enhance security:

1. Consider using an app password instead of your main password
2. Don't hardcode passwords in your scripts
3. Use environment variables or a secure credential manager
4. Only run the automation on trusted machines

## Next Steps

After setting up this integration, you can:

1. Extend it to read and process emails
2. Add support for attachments
3. Implement more complex email workflows
4. Integrate with other parts of your agent system
