"""
Credential Store

This module provides a secure way to store and retrieve credentials for the AI Agent System.
It uses simple encryption to protect sensitive information.
"""
import os
import json
import base64
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

class CredentialStore:
    """Secure credential storage for the AI Agent System."""
    
    def __init__(self, store_path="credentials/credential_store.enc", key_path="credentials/key.key"):
        """
        Initialize the Credential Store.
        
        Args:
            store_path (str): Path to the encrypted credential store file
            key_path (str): Path to the encryption key file
        """
        self.store_path = store_path
        self.key_path = key_path
        self.key = None
        self.credentials = {}
        
        # Create credentials directory if it doesn't exist
        os.makedirs(os.path.dirname(store_path), exist_ok=True)
        
        # Initialize or load the encryption key
        self._init_key()
        
        # Load existing credentials if available
        self._load_credentials()
    
    def _init_key(self):
        """Initialize or load the encryption key."""
        if os.path.exists(self.key_path):
            # Load existing key
            with open(self.key_path, "rb") as key_file:
                self.key = key_file.read()
        else:
            # Generate a new key
            self.key = Fernet.generate_key()
            
            # Save the key
            with open(self.key_path, "wb") as key_file:
                key_file.write(self.key)
    
    def _load_credentials(self):
        """Load credentials from the encrypted store."""
        if os.path.exists(self.store_path):
            try:
                # Read the encrypted data
                with open(self.store_path, "rb") as store_file:
                    encrypted_data = store_file.read()
                
                # Decrypt the data
                fernet = Fernet(self.key)
                decrypted_data = fernet.decrypt(encrypted_data)
                
                # Parse the JSON data
                self.credentials = json.loads(decrypted_data.decode())
            except Exception as e:
                print(f"Error loading credentials: {e}")
                self.credentials = {}
    
    def _save_credentials(self):
        """Save credentials to the encrypted store."""
        try:
            # Convert credentials to JSON
            json_data = json.dumps(self.credentials).encode()
            
            # Encrypt the data
            fernet = Fernet(self.key)
            encrypted_data = fernet.encrypt(json_data)
            
            # Write the encrypted data
            with open(self.store_path, "wb") as store_file:
                store_file.write(encrypted_data)
            
            return True
        except Exception as e:
            print(f"Error saving credentials: {e}")
            return False
    
    def store_credential(self, service, account, credential_type, value):
        """
        Store a credential.
        
        Args:
            service (str): Service name (e.g., "gmail", "twitter")
            account (str): Account identifier (e.g., email address, username)
            credential_type (str): Type of credential (e.g., "password", "api_key")
            value (str): Credential value
            
        Returns:
            bool: True if successful, False otherwise
        """
        # Create nested dictionaries if they don't exist
        if service not in self.credentials:
            self.credentials[service] = {}
        
        if account not in self.credentials[service]:
            self.credentials[service][account] = {}
        
        # Store the credential
        self.credentials[service][account][credential_type] = value
        
        # Save the updated credentials
        return self._save_credentials()
    
    def get_credential(self, service, account, credential_type):
        """
        Get a credential.
        
        Args:
            service (str): Service name (e.g., "gmail", "twitter")
            account (str): Account identifier (e.g., email address, username)
            credential_type (str): Type of credential (e.g., "password", "api_key")
            
        Returns:
            str: Credential value, or None if not found
        """
        try:
            return self.credentials[service][account][credential_type]
        except KeyError:
            return None
    
    def has_credential(self, service, account, credential_type):
        """
        Check if a credential exists.
        
        Args:
            service (str): Service name (e.g., "gmail", "twitter")
            account (str): Account identifier (e.g., email address, username)
            credential_type (str): Type of credential (e.g., "password", "api_key")
            
        Returns:
            bool: True if the credential exists, False otherwise
        """
        try:
            return self.credentials[service][account][credential_type] is not None
        except KeyError:
            return False
    
    def remove_credential(self, service, account, credential_type):
        """
        Remove a credential.
        
        Args:
            service (str): Service name (e.g., "gmail", "twitter")
            account (str): Account identifier (e.g., email address, username)
            credential_type (str): Type of credential (e.g., "password", "api_key")
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            del self.credentials[service][account][credential_type]
            
            # Clean up empty dictionaries
            if not self.credentials[service][account]:
                del self.credentials[service][account]
            
            if not self.credentials[service]:
                del self.credentials[service]
            
            # Save the updated credentials
            return self._save_credentials()
        except KeyError:
            return False
    
    def list_services(self):
        """
        List all services.
        
        Returns:
            list: List of service names
        """
        return list(self.credentials.keys())
    
    def list_accounts(self, service):
        """
        List all accounts for a service.
        
        Args:
            service (str): Service name
            
        Returns:
            list: List of account identifiers
        """
        try:
            return list(self.credentials[service].keys())
        except KeyError:
            return []
    
    def list_credential_types(self, service, account):
        """
        List all credential types for an account.
        
        Args:
            service (str): Service name
            account (str): Account identifier
            
        Returns:
            list: List of credential types
        """
        try:
            return list(self.credentials[service][account].keys())
        except KeyError:
            return []
