@echo off
echo UI-TARS Diagnostic Tool
echo =====================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Install requests if not already installed
pip install requests >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing requests package...
    pip install requests
    if %errorlevel% neq 0 (
        echo Failed to install requests package. Please check your internet connection.
        exit /b 1
    )
)

REM Ask for UI-TARS path
echo.
echo Enter the path to UI-TARS executable (leave empty to auto-detect):
set /p UI_TARS_PATH=""

REM Run the diagnostic tool
echo.
echo Running UI-TARS diagnostic...
echo.

if "%UI_TARS_PATH%"=="" (
    python ui_tars_diagnostic.py --start
) else (
    python ui_tars_diagnostic.py --path "%UI_TARS_PATH%" --start
)

echo.
if %errorlevel% equ 0 (
    echo UI-TARS is properly configured and running!
) else (
    echo There are issues with the UI-TARS configuration. Please check the recommendations above.
)

echo.
pause
