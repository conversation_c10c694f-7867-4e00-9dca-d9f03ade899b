# Audio Processing Module

This directory contains the audio processing module for the Multi-Agent AI System. It provides capabilities for speech recognition, text-to-speech, and audio analysis.

## Overview

The audio processing module allows agents to work with audio data, enabling voice-based interactions, audio content analysis, and speech synthesis. This is particularly useful for communication agents, music agents, and any agent that needs to process or generate audio.

## Components

- `audio_processor.py`: Main module for audio processing
- `speech_recognition.py`: Speech recognition capabilities
- `text_to_speech.py`: Text-to-speech capabilities
- `audio_analysis.py`: Audio analysis capabilities

## Features

### Speech Recognition

The speech recognition component can transcribe audio files to text, supporting multiple languages and different audio formats.

Example:

```python
from audio.audio_processor import AudioProcessor
import asyncio

async def transcribe_audio():
    # Create audio processor
    audio_config = {
        "enabled": True,
        "local_mode": True,
        "speech_recognition_engine": "whisper"
    }
    processor = AudioProcessor(audio_config)
    await processor.initialize()
    
    # Transcribe audio
    result = await processor.transcribe_audio(
        audio_path="path/to/audio.wav",
        language="en"
    )
    
    print(f"Transcription: {result['text']}")

asyncio.run(transcribe_audio())
```

### Text-to-Speech

The text-to-speech component can convert text to speech, supporting multiple voices and languages.

Example:

```python
async def text_to_speech():
    # Create audio processor
    audio_config = {
        "enabled": True,
        "local_mode": True,
        "text_to_speech_engine": "pyttsx3"
    }
    processor = AudioProcessor(audio_config)
    await processor.initialize()
    
    # Convert text to speech
    result = await processor.text_to_speech(
        text="Hello, this is a test message.",
        voice="default",
        output_path="path/to/output.wav"
    )
    
    print(f"Audio saved to: {result['audio_path']}")

asyncio.run(text_to_speech())
```

### Audio Analysis

The audio analysis component can analyze audio files to extract features such as tempo, pitch, and emotion.

Example:

```python
async def analyze_audio():
    # Create audio processor
    audio_config = {
        "enabled": True,
        "local_mode": True
    }
    processor = AudioProcessor(audio_config)
    await processor.initialize()
    
    # Analyze audio
    result = await processor.analyze_audio(
        audio_path="path/to/audio.wav",
        analysis_type="music"
    )
    
    print(f"Tempo: {result['tempo']} BPM")
    print(f"Key: {result['key']}")
    print(f"Mood: {result['mood']}")

asyncio.run(analyze_audio())
```

## Integration with Agents

Agents can use audio processing capabilities for various tasks:

```python
# In a communication agent's execute_cycle method
async def execute_cycle(self):
    # Get audio processor from services
    audio_processor = self.get_service("audio_processor")
    
    if audio_processor:
        # Check for new voice messages
        new_messages = await self._check_for_new_voice_messages()
        
        for message in new_messages:
            # Transcribe voice message
            transcription = await audio_processor.transcribe_audio(
                audio_path=message["audio_path"],
                language=message["language"]
            )
            
            # Process the transcribed text
            response_text = await self._process_message(transcription["text"])
            
            # Convert response to speech
            speech_result = await audio_processor.text_to_speech(
                text=response_text,
                voice="default"
            )
            
            # Send voice response
            await self._send_voice_response(
                recipient=message["sender"],
                audio_path=speech_result["audio_path"]
            )
```

## Supported Engines

### Speech Recognition Engines

1. **Whisper**: OpenAI's Whisper model for speech recognition
2. **Google Speech-to-Text**: Google's speech recognition API
3. **Mozilla DeepSpeech**: Open-source speech recognition

### Text-to-Speech Engines

1. **pyttsx3**: Offline text-to-speech library
2. **ElevenLabs**: High-quality voice synthesis
3. **Google Text-to-Speech**: Google's text-to-speech API

## Audio Formats

The audio processor supports various audio formats:

- WAV
- MP3
- FLAC
- OGG
- M4A

## Configuration

Audio processing can be configured in the `.env` file:

```
# Audio processing settings
ENABLE_AUDIO=True
AUDIO_API_KEY=your_api_key
AUDIO_API_URL=http://api.example.com
AUDIO_LOCAL_MODE=True
SPEECH_RECOGNITION_ENGINE=whisper
TEXT_TO_SPEECH_ENGINE=pyttsx3
```

## Requirements

To use the audio processing module, you may need to install additional packages:

```bash
# For speech recognition
pip install whisper

# For text-to-speech
pip install pyttsx3

# For audio analysis
pip install librosa

# For all features
pip install whisper pyttsx3 librosa
```

## API Mode

In addition to local mode, the audio processor can also use external APIs for speech recognition and text-to-speech:

```python
# Create audio processor with API mode
audio_config = {
    "enabled": True,
    "local_mode": False,
    "api_key": "your_api_key",
    "api_url": "https://api.example.com"
}
processor = AudioProcessor(audio_config)
await processor.initialize()

# Use API for speech recognition
result = await processor.transcribe_audio(
    audio_path="path/to/audio.wav",
    language="en"
)
```
