"""
Calendly Webhook Receiver

This script receives and processes webhook events from Calendly.
"""
import os
import sys
import json
import asyncio
import argparse
from datetime import datetime
from typing import Dict, Any, Optional
from aiohttp import web

# Add parent directory to path to import from core
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.logger import setup_logger
from core.state_manager import StateManager

# Set up logger
logger = setup_logger("calendly_webhook_receiver")

# Global variables
state_manager = None
event_type_filter = None

async def handle_webhook(request):
    """
    Handle incoming webhook events from Calendly.
    
    Args:
        request: HTTP request
        
    Returns:
        Response: HTTP response
    """
    global state_manager, event_type_filter
    
    try:
        # Parse request body
        payload = await request.json()
        
        # Log the webhook event
        logger.info(f"Received webhook event from Calendly: {payload.get('event')}")
        
        # Check if we should filter by event type
        if event_type_filter:
            event_type = payload.get('scheduled_event', {}).get('event_type', '')
            if event_type != event_type_filter:
                logger.info(f"Skipping webhook event (event type doesn't match filter)")
                return web.Response(status=200)
        
        # Process the webhook event
        event_type = payload.get('event')
        
        if event_type == 'invitee.created':
            await handle_invitee_created(payload)
        elif event_type == 'invitee.canceled':
            await handle_invitee_canceled(payload)
        else:
            logger.info(f"Unhandled event type: {event_type}")
        
        return web.Response(status=200)
    
    except Exception as e:
        logger.exception(f"Error handling webhook: {e}")
        return web.Response(status=500)

async def handle_invitee_created(payload: Dict[str, Any]):
    """
    Handle invitee.created webhook event.
    
    Args:
        payload (Dict[str, Any]): Webhook payload
    """
    global state_manager
    
    try:
        # Extract relevant information
        invitee = payload.get('payload', {}).get('invitee', {})
        scheduled_event = payload.get('payload', {}).get('scheduled_event', {})
        
        invitee_uuid = invitee.get('uri', '').split('/')[-1]
        event_uuid = scheduled_event.get('uri', '').split('/')[-1]
        
        # Extract invitee information
        name = invitee.get('name', '')
        email = invitee.get('email', '')
        
        # Extract event information
        event_type = scheduled_event.get('event_type_name', '')
        start_time = scheduled_event.get('start_time', '')
        end_time = scheduled_event.get('end_time', '')
        
        # Log the appointment
        logger.info(f"New appointment scheduled: {name} ({email}) - {event_type} at {start_time}")
        
        # Find lead by email
        leads = await state_manager.get_state("insurance_lead", "leads") or {}
        
        lead_id = None
        for lid, lead in leads.items():
            if lead.get("email") == email:
                lead_id = lid
                break
        
        if lead_id:
            # Update lead with appointment information
            lead = leads.get(lead_id, {})
            lead["appointment_booked"] = True
            lead["appointment_id"] = invitee_uuid
            lead["status"] = "booked"
            lead["last_contact"] = datetime.now().isoformat()
            
            # Add appointment details to notes
            note = f"Appointment scheduled: {event_type} at {start_time}"
            if "notes" not in lead:
                lead["notes"] = []
            lead["notes"].append(note)
            
            # Update lead
            leads[lead_id] = lead
            await state_manager.update_state("insurance_lead", "leads", leads)
            
            # Log interaction
            interactions = await state_manager.get_state("insurance_lead", "interactions") or {}
            
            interaction_id = f"{lead_id}-{datetime.now().strftime('%Y%m%d%H%M%S')}"
            interaction = {
                "interaction_id": interaction_id,
                "lead_id": lead_id,
                "channel": "calendly",
                "direction": "incoming",
                "content": f"Appointment scheduled: {event_type} at {start_time}",
                "status": "received",
                "timestamp": datetime.now().isoformat()
            }
            
            interactions[interaction_id] = interaction
            await state_manager.update_state("insurance_lead", "interactions", interactions)
            
            logger.info(f"Updated lead {lead_id} with appointment information")
        else:
            logger.warning(f"No lead found with email {email}")
    
    except Exception as e:
        logger.exception(f"Error handling invitee.created event: {e}")

async def handle_invitee_canceled(payload: Dict[str, Any]):
    """
    Handle invitee.canceled webhook event.
    
    Args:
        payload (Dict[str, Any]): Webhook payload
    """
    global state_manager
    
    try:
        # Extract relevant information
        invitee = payload.get('payload', {}).get('invitee', {})
        scheduled_event = payload.get('payload', {}).get('scheduled_event', {})
        
        invitee_uuid = invitee.get('uri', '').split('/')[-1]
        event_uuid = scheduled_event.get('uri', '').split('/')[-1]
        
        # Extract invitee information
        name = invitee.get('name', '')
        email = invitee.get('email', '')
        
        # Extract event information
        event_type = scheduled_event.get('event_type_name', '')
        start_time = scheduled_event.get('start_time', '')
        
        # Log the cancellation
        logger.info(f"Appointment canceled: {name} ({email}) - {event_type} at {start_time}")
        
        # Find lead by appointment ID
        leads = await state_manager.get_state("insurance_lead", "leads") or {}
        
        lead_id = None
        for lid, lead in leads.items():
            if lead.get("appointment_id") == invitee_uuid:
                lead_id = lid
                break
        
        if lead_id:
            # Update lead with cancellation information
            lead = leads.get(lead_id, {})
            lead["appointment_booked"] = False
            lead["appointment_id"] = None
            lead["status"] = "qualified"  # Revert to qualified status
            lead["last_contact"] = datetime.now().isoformat()
            
            # Add cancellation details to notes
            note = f"Appointment canceled: {event_type} at {start_time}"
            if "notes" not in lead:
                lead["notes"] = []
            lead["notes"].append(note)
            
            # Update lead
            leads[lead_id] = lead
            await state_manager.update_state("insurance_lead", "leads", leads)
            
            # Log interaction
            interactions = await state_manager.get_state("insurance_lead", "interactions") or {}
            
            interaction_id = f"{lead_id}-{datetime.now().strftime('%Y%m%d%H%M%S')}"
            interaction = {
                "interaction_id": interaction_id,
                "lead_id": lead_id,
                "channel": "calendly",
                "direction": "incoming",
                "content": f"Appointment canceled: {event_type} at {start_time}",
                "status": "received",
                "timestamp": datetime.now().isoformat()
            }
            
            interactions[interaction_id] = interaction
            await state_manager.update_state("insurance_lead", "interactions", interactions)
            
            logger.info(f"Updated lead {lead_id} with cancellation information")
        else:
            logger.warning(f"No lead found with appointment ID {invitee_uuid}")
    
    except Exception as e:
        logger.exception(f"Error handling invitee.canceled event: {e}")

async def main():
    """Run the Calendly webhook receiver."""
    global state_manager, event_type_filter
    
    parser = argparse.ArgumentParser(description="Calendly Webhook Receiver")
    parser.add_argument("--host", type=str, default="localhost", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    parser.add_argument("--event-type", type=str, help="Filter by event type URI")
    args = parser.parse_args()
    
    # Set event type filter
    event_type_filter = args.event_type
    
    # Initialize state manager
    state_manager = StateManager(use_database=False)
    await state_manager.initialize()
    
    # Create web application
    app = web.Application()
    app.router.add_post('/webhook', handle_webhook)
    
    # Start web server
    runner = web.AppRunner(app)
    await runner.setup()
    site = web.TCPSite(runner, args.host, args.port)
    
    print(f"Starting webhook receiver on http://{args.host}:{args.port}")
    print("Press Ctrl+C to stop")
    
    if event_type_filter:
        print(f"Filtering events by event type: {event_type_filter}")
    
    await site.start()
    
    # Keep the server running
    try:
        while True:
            await asyncio.sleep(3600)  # Sleep for an hour
    except KeyboardInterrupt:
        print("Shutting down...")
    finally:
        await runner.cleanup()
        await state_manager.close()

if __name__ == "__main__":
    asyncio.run(main())
