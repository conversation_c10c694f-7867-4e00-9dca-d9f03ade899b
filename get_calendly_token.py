"""
Get Calendly OAuth Token

This script helps you exchange an authorization code for an OAuth token.
"""
import requests
import json
import os

# Create credentials directory if it doesn't exist
os.makedirs("credentials/calendly", exist_ok=True)

# Ask for client ID and client secret
client_id = input("Enter your Calendly client ID: ")
client_secret = input("Enter your Calendly client secret: ")
auth_code = input("Enter the authorization code from the redirect URL: ")
redirect_uri = input("Enter your redirect URI (default: http://localhost:8000/callback): ") or "http://localhost:8000/callback"

# Exchange authorization code for access token
try:
    response = requests.post(
        "https://auth.calendly.com/oauth/token",
        data={
            "client_id": client_id,
            "client_secret": client_secret,
            "code": auth_code,
            "redirect_uri": redirect_uri,
            "grant_type": "authorization_code"
        }
    )
    
    if response.status_code == 200:
        token_data = response.json()
        print("\nOAuth token received successfully!")
        print(f"Access Token: {token_data['access_token']}")
        print(f"Refresh Token: {token_data['refresh_token']}")
        print(f"Expires In: {token_data['expires_in']} seconds")
        
        # Save token to credentials file
        with open("credentials/calendly/calendly.json", "w") as f:
            json.dump({
                "api_key": token_data['access_token'],
                "refresh_token": token_data['refresh_token'],
                "expires_in": token_data['expires_in'],
                "token_type": token_data['token_type'],
                "created_at": token_data.get('created_at', '')
            }, f, indent=4)
        
        print("\nToken saved to credentials/calendly/calendly.json")
        
        # Test the token
        print("\nTesting the token...")
        user_response = requests.get(
            "https://api.calendly.com/users/me",
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token_data['access_token']}"
            }
        )
        
        if user_response.status_code == 200:
            user_info = user_response.json()
            print("Token is valid!")
            print(f"User: {user_info['resource']['name']} ({user_info['resource']['email']})")
            
            # Update credentials file with user info
            with open("credentials/calendly/calendly.json", "r") as f:
                credentials = json.load(f)
            
            credentials["user_uri"] = user_info['resource']['uri']
            credentials["user_uuid"] = user_info['resource']['uri'].split('/')[-1]
            
            with open("credentials/calendly/calendly.json", "w") as f:
                json.dump(credentials, f, indent=4)
            
            print("User information saved to credentials file")
        else:
            print(f"Error testing token: {user_response.status_code}")
            print(user_response.text)
    else:
        print(f"Error exchanging authorization code: {response.status_code}")
        print(response.text)
except Exception as e:
    print(f"Error: {e}")
