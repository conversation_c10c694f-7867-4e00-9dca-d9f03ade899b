# Setup AI System Autostart
# This script sets up the AI agent system to start automatically on Windows startup

# Check if running as administrator
$currentPrincipal = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
$isAdmin = $currentPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

if (-not $isAdmin) {
    Write-Host "This script must be run as Administrator. Please restart PowerShell as Administrator and try again." -ForegroundColor Red
    
    # Pause to see the message
    Write-Host "Press any key to exit..." -ForegroundColor Yellow
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    exit
}

# Set the current directory to the script directory
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location -Path $scriptPath

Write-Host "Setting up AI System Autostart..." -ForegroundColor Cyan

# Create a task to start the AI system at login
Write-Host "Creating a task to start the AI system at login..." -ForegroundColor Cyan

$taskName = "StartAIAgentSystem"
$taskExists = Get-ScheduledTask -TaskName $taskName -ErrorAction SilentlyContinue

if ($taskExists) {
    Write-Host "Task '$taskName' already exists. Removing it..." -ForegroundColor Yellow
    Unregister-ScheduledTask -TaskName $taskName -Confirm:$false
}

$action = New-ScheduledTaskAction -Execute "powershell.exe" -Argument "-ExecutionPolicy Bypass -WindowStyle Normal -File `"$scriptPath\auto_start_ai_system.ps1`"" -WorkingDirectory $scriptPath
$trigger = New-ScheduledTaskTrigger -AtLogOn
$settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable

Register-ScheduledTask -TaskName $taskName -Action $action -Trigger $trigger -Settings $settings -Description "Start AI Agent System at login" -RunLevel Highest

Write-Host "Task created successfully. The AI system will start automatically when you log in." -ForegroundColor Green

# Create a shortcut on the desktop
Write-Host "Creating a shortcut on the desktop..." -ForegroundColor Cyan

$desktopPath = [Environment]::GetFolderPath("Desktop")
$shortcutPath = Join-Path -Path $desktopPath -ChildPath "AI Agent System.lnk"

# Create a WScript.Shell object
$shell = New-Object -ComObject WScript.Shell

# Create the shortcut
$shortcut = $shell.CreateShortcut($shortcutPath)
$shortcut.TargetPath = "powershell.exe"
$shortcut.Arguments = "-ExecutionPolicy Bypass -WindowStyle Normal -File `"$scriptPath\auto_start_ai_system.ps1`""
$shortcut.WorkingDirectory = $scriptPath
$shortcut.Description = "Start AI Agent System"
$shortcut.IconLocation = "powershell.exe,0"
$shortcut.Save()

Write-Host "Shortcut created on your desktop: 'AI Agent System.lnk'" -ForegroundColor Green

# Create a README file with instructions
$readmePath = Join-Path -Path $scriptPath -ChildPath "AUTOSTART_README.md"
$readmeContent = @"
# AI Agent System Autostart Setup

The AI Agent System has been set up to run automatically when your computer starts. Here's what has been configured:

1. **Windows Task Scheduler**: A task has been created to start the AI Agent System at login.
   - Task Name: StartAIAgentSystem
   - Status: Ready
   - Trigger: At log on of any user
   - Action: Run PowerShell script auto_start_ai_system.ps1

2. **Desktop Shortcut**: A shortcut has been created on your desktop to manually start the system if needed.
   - Shortcut Name: AI Agent System.lnk

## Components Started Automatically

When the system starts, the following components are initialized:

1. **AlphaEvolve Service**: Runs in the background for evolutionary optimization
2. **MPC Servers**: Provides multi-party computation capabilities
3. **UI-TARS 1.5**: Provides browser automation and UI interaction
4. **Jarvis AI Interface**: Provides natural language interaction with the system
5. **VS Code**: Opens with the project workspace for monitoring and development

## Using the System

Once started, the Jarvis AI Interface will be immediately ready to accept natural language commands.

To exit Jarvis and return to the regular terminal, press **Ctrl+Alt+X**.

## Troubleshooting

If the system doesn't start automatically:

1. Check the Task Scheduler to ensure the task is enabled
2. Try running the desktop shortcut manually
3. Check the logs in the 'logs' directory for any errors
4. Run setup_ai_system_autostart.ps1 again as Administrator

## Uninstalling

To remove the autostart configuration:

1. Open Task Scheduler and delete the 'StartAIAgentSystem' task
2. Delete the desktop shortcut
"@

Set-Content -Path $readmePath -Value $readmeContent
Write-Host "Created README file with instructions: $readmePath" -ForegroundColor Green

# Display completion message
Write-Host @"

╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║                  AI AGENT SYSTEM AUTOSTART SETUP COMPLETE                    ║
║                                                                              ║
║  The AI Agent System will now run automatically when your computer starts.   ║
║  You can also start it manually using the desktop shortcut.                  ║
║                                                                              ║
║  To test the setup now, run:                                                 ║
║  .\auto_start_ai_system.ps1                                                  ║
║                                                                              ║
║  For more information, see AUTOSTART_README.md                               ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
"@ -ForegroundColor Cyan

# Pause to see the message
Write-Host "Press any key to exit..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
