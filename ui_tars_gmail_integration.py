"""
UI-TARS Gmail Integration

This script integrates UI-TARS 1.5 with Gmail browser automation to provide
better screen understanding and reasoning capabilities.
"""
import os
import sys
import time
import json
import asyncio
import logging
import getpass
import argparse
import subprocess
from typing import Dict, Optional, Any, List, Tuple

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ui_tars_gmail_integration")

# Check if required packages are installed
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.service import Service as ChromeService
    from selenium.webdriver.firefox.service import Service as FirefoxService
    from selenium.webdriver.edge.service import Service as EdgeService
    from selenium.webdriver.common.by import By
    from selenium.webdriver.common.keys import Keys
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    from webdriver_manager.chrome import ChromeDriverManager
    from webdriver_manager.firefox import GeckoDriverManager
    from webdriver_manager.microsoft import EdgeChromiumDriverManager
except ImportError as e:
    logger.error(f"Required package not installed: {e}")
    logger.error("Please install required packages with: pip install -r email_automation_requirements.txt")
    sys.exit(1)

# Try to import UI-TARS connector
try:
    from ui_tars.connector.ui_tars_connector import UITarsConnector
    UI_TARS_AVAILABLE = True
except ImportError:
    logger.warning("UI-TARS connector not found. Running without UI-TARS integration.")
    UI_TARS_AVAILABLE = False
    UITarsConnector = None

class UITarsGmailIntegration:
    """Class to integrate UI-TARS with Gmail browser automation."""
    
    def __init__(self, 
                 browser_type: str = "chrome",
                 ui_tars_api_url: str = "http://localhost:8080",
                 ui_tars_model_name: str = "UI-TARS-1.5-7B",
                 ui_tars_installation_path: Optional[str] = None):
        """
        Initialize the UI-TARS Gmail Integration.
        
        Args:
            browser_type (str): Type of browser to use (chrome, firefox, edge)
            ui_tars_api_url (str): URL of the UI-TARS API
            ui_tars_model_name (str): Name of the model to use
            ui_tars_installation_path (Optional[str]): Path to UI-TARS installation
        """
        self.browser_type = browser_type.lower()
        self.ui_tars_api_url = ui_tars_api_url
        self.ui_tars_model_name = ui_tars_model_name
        self.ui_tars_installation_path = ui_tars_installation_path
        
        self.driver = None
        self.ui_tars_connector = None
        self.ui_tars_enabled = UI_TARS_AVAILABLE
    
    async def initialize(self) -> bool:
        """
        Initialize the browser and UI-TARS.
        
        Returns:
            bool: True if initialization was successful, False otherwise
        """
        logger.info(f"Initializing {self.browser_type} browser")
        
        try:
            # Initialize browser
            if self.browser_type == "chrome":
                service = ChromeService(ChromeDriverManager().install())
                options = webdriver.ChromeOptions()
                options.add_argument("--start-maximized")
                self.driver = webdriver.Chrome(service=service, options=options)
            
            elif self.browser_type == "firefox":
                service = FirefoxService(GeckoDriverManager().install())
                options = webdriver.FirefoxOptions()
                self.driver = webdriver.Firefox(service=service, options=options)
            
            elif self.browser_type == "edge":
                service = EdgeService(EdgeChromiumDriverManager().install())
                options = webdriver.EdgeOptions()
                options.add_argument("--start-maximized")
                self.driver = webdriver.Edge(service=service, options=options)
            
            else:
                logger.error(f"Unsupported browser type: {self.browser_type}")
                return False
            
            # Set implicit wait time
            self.driver.implicitly_wait(10)
            
            logger.info(f"{self.browser_type} browser initialized successfully")
            
            # Initialize UI-TARS if available
            if self.ui_tars_enabled:
                logger.info("Initializing UI-TARS connector")
                
                # Create UI-TARS connector
                self.ui_tars_connector = UITarsConnector(
                    api_url=self.ui_tars_api_url,
                    api_key=None,
                    model_name=self.ui_tars_model_name,
                    installation_path=self.ui_tars_installation_path
                )
                
                # Initialize connector
                success = await self.ui_tars_connector.initialize()
                if not success:
                    logger.warning("Failed to initialize UI-TARS connector. Running without UI-TARS integration.")
                    self.ui_tars_enabled = False
                else:
                    logger.info("UI-TARS connector initialized successfully")
            
            return True
            
        except Exception as e:
            logger.exception(f"Error initializing: {e}")
            return False
    
    async def analyze_screen(self) -> Dict[str, Any]:
        """
        Analyze the current screen using UI-TARS.
        
        Returns:
            Dict[str, Any]: Analysis results
        """
        if not self.ui_tars_enabled or not self.ui_tars_connector:
            logger.warning("UI-TARS not enabled or connector not initialized")
            return {"success": False, "error": "UI-TARS not enabled"}
        
        try:
            # Take a screenshot
            screenshot_path = "current_screen.png"
            self.driver.save_screenshot(screenshot_path)
            
            # Ask UI-TARS to analyze the screenshot
            command = f"Analyze this screenshot and tell me what's happening on the screen. What elements are visible? Is there a login form? Is there an error message? What should I do next?"
            
            result = await self.ui_tars_connector.execute_command(
                command=command,
                screenshot=screenshot_path
            )
            
            return {"success": True, "analysis": result}
            
        except Exception as e:
            logger.exception(f"Error analyzing screen: {e}")
            return {"success": False, "error": str(e)}
    
    async def get_next_action(self, current_task: str) -> Dict[str, Any]:
        """
        Get the next action to take using UI-TARS.
        
        Args:
            current_task (str): Current task being performed
            
        Returns:
            Dict[str, Any]: Next action to take
        """
        if not self.ui_tars_enabled or not self.ui_tars_connector:
            logger.warning("UI-TARS not enabled or connector not initialized")
            return {"success": False, "error": "UI-TARS not enabled"}
        
        try:
            # Take a screenshot
            screenshot_path = "current_screen.png"
            self.driver.save_screenshot(screenshot_path)
            
            # Ask UI-TARS for the next action
            command = f"I'm trying to {current_task}. Based on this screenshot, what should I do next? Give me a specific action to take."
            
            result = await self.ui_tars_connector.execute_command(
                command=command,
                screenshot=screenshot_path
            )
            
            return {"success": True, "next_action": result}
            
        except Exception as e:
            logger.exception(f"Error getting next action: {e}")
            return {"success": False, "error": str(e)}
    
    async def send_email(self, 
                        email_account: str,
                        password: str,
                        to_email: str, 
                        subject: str, 
                        body: str) -> Dict[str, Any]:
        """
        Send an email using Gmail through browser automation with UI-TARS assistance.
        
        Args:
            email_account (str): Gmail account to send from
            password (str): Password for the Gmail account
            to_email (str): Recipient email address
            subject (str): Email subject
            body (str): Email body
            
        Returns:
            Dict[str, Any]: Result of the operation
        """
        if not self.driver:
            logger.error("Browser not initialized")
            return {"success": False, "error": "Browser not initialized"}
        
        try:
            # Step 1: Navigate to Gmail
            logger.info("Navigating to Gmail")
            self.driver.get("https://mail.google.com")
            time.sleep(2)  # Give the page time to load
            
            # If UI-TARS is enabled, analyze the screen
            if self.ui_tars_enabled:
                logger.info("Analyzing login screen with UI-TARS")
                analysis = await self.analyze_screen()
                if analysis["success"]:
                    logger.info(f"UI-TARS analysis: {analysis['analysis']}")
            
            # Step 2: Check if already logged in
            logger.info("Checking login status")
            
            # Wait for either the email input field or the Gmail interface to load
            try:
                # Look for the email input field
                try:
                    email_input = WebDriverWait(self.driver, 10).until(
                        EC.presence_of_element_located((By.ID, "identifierId"))
                    )
                    
                    # Need to log in
                    logger.info("Login page detected")
                    
                    # Step 3: Enter email
                    logger.info(f"Entering email: {email_account}")
                    email_input.clear()
                    email_input.send_keys(email_account)
                    time.sleep(1)  # Brief pause before clicking next
                    
                    # If UI-TARS is enabled, get next action
                    if self.ui_tars_enabled:
                        next_action = await self.get_next_action("log in to Gmail after entering email")
                        if next_action["success"]:
                            logger.info(f"UI-TARS suggested next action: {next_action['next_action']}")
                    
                    # Try to find and click the Next button
                    try:
                        next_button = self.driver.find_element(By.XPATH, "//span[text()='Next']/parent::*")
                        next_button.click()
                        logger.info("Clicked Next button after entering email")
                    except NoSuchElementException:
                        try:
                            # Try alternative selector
                            next_button = self.driver.find_element(By.CSS_SELECTOR, "button[jsname='LgbsSe']")
                            next_button.click()
                            logger.info("Clicked Next button using alternative selector")
                        except NoSuchElementException:
                            # Just press Enter as last resort
                            email_input.send_keys(Keys.RETURN)
                            logger.info("Pressed Enter key after entering email")
                    
                    # Wait for password field
                    logger.info("Waiting for password field...")
                    password_input = WebDriverWait(self.driver, 15).until(
                        EC.element_to_be_clickable((By.NAME, "Passwd"))
                    )
                    
                    # Step 4: Enter password
                    logger.info("Entering password")
                    time.sleep(2)  # Brief pause before entering password
                    password_input.clear()
                    password_input.send_keys(password)
                    time.sleep(1)  # Brief pause before clicking next
                    
                    # If UI-TARS is enabled, get next action
                    if self.ui_tars_enabled:
                        next_action = await self.get_next_action("log in to Gmail after entering password")
                        if next_action["success"]:
                            logger.info(f"UI-TARS suggested next action: {next_action['next_action']}")
                    
                    # Try to find and click the Next button for password
                    try:
                        next_button = self.driver.find_element(By.XPATH, "//span[text()='Next']/parent::*")
                        next_button.click()
                        logger.info("Clicked Next button after entering password")
                    except NoSuchElementException:
                        try:
                            # Try alternative selector
                            next_button = self.driver.find_element(By.CSS_SELECTOR, "button[jsname='LgbsSe']")
                            next_button.click()
                            logger.info("Clicked Next button using alternative selector")
                        except NoSuchElementException:
                            # Just press Enter as last resort
                            password_input.send_keys(Keys.RETURN)
                            logger.info("Pressed Enter key after entering password")
                    
                    # Wait for Gmail to load
                    logger.info("Waiting for Gmail to load...")
                    WebDriverWait(self.driver, 30).until(
                        EC.presence_of_element_located((By.XPATH, "//div[text()='Compose']"))
                    )
                    logger.info("Gmail loaded successfully")
                    
                except Exception as e:
                    logger.warning(f"Exception during login process: {str(e)}")
                    # Take a screenshot for debugging
                    screenshot_path = "login_process_screenshot.png"
                    self.driver.save_screenshot(screenshot_path)
                    logger.info(f"Screenshot saved to {screenshot_path}")
                    
                    # If UI-TARS is enabled, analyze the screen
                    if self.ui_tars_enabled:
                        logger.info("Analyzing screen with UI-TARS after login exception")
                        analysis = await self.analyze_screen()
                        if analysis["success"]:
                            logger.info(f"UI-TARS analysis: {analysis['analysis']}")
                    
                    # Check if we're on a verification page or other challenge
                    page_source = self.driver.page_source.lower()
                    if "verify" in page_source or "challenge" in page_source or "security" in page_source:
                        logger.warning("Detected a verification challenge. User interaction may be required.")
                        # Give user time to manually complete verification if needed
                        logger.info("Waiting 60 seconds for manual verification...")
                        time.sleep(60)
                        
                        # Check if we made it to Gmail after manual verification
                        try:
                            WebDriverWait(self.driver, 10).until(
                                EC.presence_of_element_located((By.XPATH, "//div[text()='Compose']"))
                            )
                            logger.info("Successfully logged in after verification")
                        except TimeoutException:
                            logger.error("Still unable to access Gmail after verification wait")
                            return {"success": False, "error": "Failed to access Gmail after verification"}
                    else:
                        # Try to continue with the flow, maybe we're already logged in
                        pass
                
            except TimeoutException:
                # Check if already logged in
                try:
                    logger.info("Checking if already logged in...")
                    WebDriverWait(self.driver, 10).until(
                        EC.presence_of_element_located((By.XPATH, "//div[text()='Compose']"))
                    )
                    logger.info("Already logged in")
                except TimeoutException:
                    # Take a screenshot for debugging
                    screenshot_path = "login_error_screenshot.png"
                    self.driver.save_screenshot(screenshot_path)
                    logger.error(f"Failed to log in or detect Gmail interface. Screenshot saved to {screenshot_path}")
                    
                    # If UI-TARS is enabled, analyze the screen
                    if self.ui_tars_enabled:
                        logger.info("Analyzing error screen with UI-TARS")
                        analysis = await self.analyze_screen()
                        if analysis["success"]:
                            logger.info(f"UI-TARS analysis: {analysis['analysis']}")
                    
                    return {"success": False, "error": "Failed to log in or detect Gmail interface"}
            
            # Step 5: Click Compose
            logger.info("Clicking Compose button")
            time.sleep(2)  # Give the page time to fully load
            
            # If UI-TARS is enabled, get next action
            if self.ui_tars_enabled:
                next_action = await self.get_next_action("compose a new email in Gmail")
                if next_action["success"]:
                    logger.info(f"UI-TARS suggested next action: {next_action['next_action']}")
            
            compose_button = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//div[text()='Compose']"))
            )
            compose_button.click()
            
            # Wait for compose window
            to_input = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//textarea[@name='to']"))
            )
            
            # Step 6: Fill in recipient
            logger.info(f"Entering recipient: {to_email}")
            to_input.clear()
            to_input.send_keys(to_email)
            
            # Step 7: Fill in subject
            logger.info(f"Entering subject: {subject}")
            subject_input = self.driver.find_element(By.XPATH, "//input[@name='subjectbox']")
            subject_input.clear()
            subject_input.send_keys(subject)
            
            # Step 8: Fill in body
            logger.info("Entering email body")
            body_input = self.driver.find_element(By.XPATH, "//div[@role='textbox']")
            body_input.clear()
            body_input.send_keys(body)
            
            # If UI-TARS is enabled, get next action
            if self.ui_tars_enabled:
                next_action = await self.get_next_action("send the email after filling in recipient, subject, and body")
                if next_action["success"]:
                    logger.info(f"UI-TARS suggested next action: {next_action['next_action']}")
            
            # Step 9: Send the email
            logger.info("Sending email")
            send_button = self.driver.find_element(By.XPATH, "//div[text()='Send']")
            send_button.click()
            
            # Step 10: Wait for confirmation
            time.sleep(3)
            
            # If UI-TARS is enabled, analyze the screen
            if self.ui_tars_enabled:
                logger.info("Analyzing screen with UI-TARS after sending email")
                analysis = await self.analyze_screen()
                if analysis["success"]:
                    logger.info(f"UI-TARS analysis: {analysis['analysis']}")
            
            logger.info("Email sent successfully")
            return {"success": True, "message": "Email sent successfully"}
            
        except Exception as e:
            logger.exception(f"Error sending email: {e}")
            return {"success": False, "error": str(e)}
    
    async def shutdown(self) -> None:
        """Shut down the browser and UI-TARS."""
        if self.driver:
            self.driver.quit()
            logger.info("Browser shut down")
        
        if self.ui_tars_enabled and self.ui_tars_connector:
            await self.ui_tars_connector.stop()
            logger.info("UI-TARS connector shut down")

async def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="UI-TARS Gmail Integration")
    parser.add_argument("--email", type=str, default="<EMAIL>", help="Gmail account to send from")
    parser.add_argument("--to", type=str, default="<EMAIL>", help="Recipient email address")
    parser.add_argument("--subject", type=str, default="Test Email from AI Agent System with UI-TARS", help="Email subject")
    parser.add_argument("--body", type=str, default="This is a test email sent using browser automation with UI-TARS integration.", help="Email body")
    parser.add_argument("--browser", type=str, choices=["chrome", "firefox", "edge"], default="chrome", help="Browser to use")
    parser.add_argument("--ui-tars-path", type=str, help="Path to UI-TARS installation")
    
    args = parser.parse_args()
    
    # Get password
    password = getpass.getpass(f"Enter password for {args.email}: ")
    
    # Create UI-TARS Gmail Integration
    integration = UITarsGmailIntegration(
        browser_type=args.browser,
        ui_tars_installation_path=args.ui_tars_path
    )
    
    # Initialize
    initialized = await integration.initialize()
    if not initialized:
        logger.error("Failed to initialize UI-TARS Gmail Integration")
        return
    
    try:
        # Send email
        result = await integration.send_email(
            email_account=args.email,
            password=password,
            to_email=args.to,
            subject=args.subject,
            body=args.body
        )
        
        if result["success"]:
            logger.info("Email sent successfully")
        else:
            logger.error(f"Failed to send email: {result['error']}")
    
    finally:
        # Shut down
        await integration.shutdown()

if __name__ == "__main__":
    asyncio.run(main())
