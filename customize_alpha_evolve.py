"""
Customize AlphaEvolve for Specific Domains.

This script customizes the AlphaEvolve configuration for specific domains,
optimizing its performance for different types of agents and tasks.
"""
import asyncio
import argparse
import json
import logging
import os
import sys
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent))

from core.logger import setup_logger
from alpha_evolve.prompt_engineering import PromptManager

# Set up logger
logger = setup_logger("customize_alpha_evolve")

# Domain-specific configurations
DOMAIN_CONFIGS = {
    "trading": {
        "code_generation": {
            "temperature": 0.6,  # Lower temperature for more precise code
            "max_tokens": 2500,  # Larger context for complex algorithms
        },
        "code_evaluation": {
            "timeout": 15,  # Longer timeout for complex calculations
            "metrics": ["correctness", "efficiency", "complexity", "risk_management"],
        },
        "evolutionary_optimization": {
            "population_size": 60,  # Larger population for more diverse solutions
            "tournament_size": 6,
            "crossover_rate": 0.85,
            "mutation_rate": 0.15,
            "elitism": 3,
        },
    },
    "insurance": {
        "code_generation": {
            "temperature": 0.65,
            "max_tokens": 2200,
        },
        "code_evaluation": {
            "timeout": 12,
            "metrics": ["correctness", "efficiency", "complexity", "compliance"],
        },
        "evolutionary_optimization": {
            "population_size": 50,
            "tournament_size": 5,
            "crossover_rate": 0.8,
            "mutation_rate": 0.2,
            "elitism": 2,
        },
    },
    "cybersecurity": {
        "code_generation": {
            "temperature": 0.5,  # Lower temperature for more precise security code
            "max_tokens": 2800,  # Larger context for comprehensive security implementations
        },
        "code_evaluation": {
            "timeout": 20,  # Longer timeout for thorough security testing
            "metrics": ["correctness", "efficiency", "complexity", "security"],
        },
        "evolutionary_optimization": {
            "population_size": 70,  # Larger population for more diverse security solutions
            "tournament_size": 7,
            "crossover_rate": 0.75,
            "mutation_rate": 0.25,  # Higher mutation rate for more exploration
            "elitism": 3,
        },
    },
    "social_media": {
        "code_generation": {
            "temperature": 0.75,  # Higher temperature for more creative content
            "max_tokens": 2000,
        },
        "code_evaluation": {
            "timeout": 10,
            "metrics": ["correctness", "efficiency", "complexity", "engagement"],
        },
        "evolutionary_optimization": {
            "population_size": 45,
            "tournament_size": 5,
            "crossover_rate": 0.85,
            "mutation_rate": 0.15,
            "elitism": 2,
        },
    },
    "music": {
        "code_generation": {
            "temperature": 0.7,
            "max_tokens": 2200,
        },
        "code_evaluation": {
            "timeout": 12,
            "metrics": ["correctness", "efficiency", "complexity", "creativity"],
        },
        "evolutionary_optimization": {
            "population_size": 50,
            "tournament_size": 5,
            "crossover_rate": 0.8,
            "mutation_rate": 0.2,
            "elitism": 2,
        },
    },
    "research": {
        "code_generation": {
            "temperature": 0.6,
            "max_tokens": 2500,
        },
        "code_evaluation": {
            "timeout": 15,
            "metrics": ["correctness", "efficiency", "complexity", "thoroughness"],
        },
        "evolutionary_optimization": {
            "population_size": 55,
            "tournament_size": 6,
            "crossover_rate": 0.8,
            "mutation_rate": 0.2,
            "elitism": 2,
        },
    }
}

async def customize_config(domain: str, output_path: str = None):
    """
    Customize AlphaEvolve configuration for a specific domain.
    
    Args:
        domain (str): Domain to customize for
        output_path (str, optional): Output path for customized configuration
    """
    logger.info(f"Customizing AlphaEvolve configuration for domain: {domain}")
    
    # Load base configuration
    config_path = "config/alpha_evolve_config.json"
    
    try:
        with open(config_path, "r") as f:
            config = json.load(f)
    except Exception as e:
        logger.error(f"Error loading configuration: {e}")
        return False
    
    # Apply domain-specific configuration
    if domain in DOMAIN_CONFIGS:
        domain_config = DOMAIN_CONFIGS[domain]
        
        # Update code generation configuration
        if "code_generation" in domain_config:
            config["code_generation"].update(domain_config["code_generation"])
        
        # Update code evaluation configuration
        if "code_evaluation" in domain_config:
            config["code_evaluation"].update(domain_config["code_evaluation"])
        
        # Update evolutionary optimization configuration
        if "evolutionary_optimization" in domain_config:
            config["evolutionary_optimization"].update(domain_config["evolutionary_optimization"])
        
        # Update domain-specific settings
        config["domain"] = domain
        
        # Determine output path
        if not output_path:
            output_path = f"config/alpha_evolve_{domain}_config.json"
        
        # Save customized configuration
        try:
            with open(output_path, "w") as f:
                json.dump(config, f, indent=2)
            
            logger.info(f"Customized configuration saved to {output_path}")
            return True
        except Exception as e:
            logger.error(f"Error saving configuration: {e}")
            return False
    else:
        logger.error(f"Unknown domain: {domain}")
        return False

async def verify_templates():
    """Verify that domain-specific templates exist."""
    logger.info("Verifying domain-specific templates")
    
    # Initialize prompt manager
    prompt_manager = PromptManager()
    await prompt_manager.initialize()
    
    # Check for domain-specific templates
    domains = list(DOMAIN_CONFIGS.keys())
    missing_templates = []
    
    for domain in domains:
        template = await prompt_manager.get_template(domain + "_agent")
        if not template:
            missing_templates.append(domain)
    
    # Check for workflow optimization template
    workflow_template = await prompt_manager.get_template("workflow_optimization")
    if not workflow_template:
        missing_templates.append("workflow_optimization")
    
    if missing_templates:
        logger.warning(f"Missing templates for: {', '.join(missing_templates)}")
        return False
    else:
        logger.info("All domain-specific templates verified")
        return True

async def customize_all_domains():
    """Customize AlphaEvolve configuration for all domains."""
    logger.info("Customizing AlphaEvolve configuration for all domains")
    
    # Verify templates
    templates_verified = await verify_templates()
    if not templates_verified:
        logger.warning("Some templates are missing, but continuing with configuration customization")
    
    # Customize configuration for each domain
    results = {}
    
    for domain in DOMAIN_CONFIGS.keys():
        result = await customize_config(domain)
        results[domain] = result
    
    # Print summary
    logger.info("Customization summary:")
    for domain, result in results.items():
        status = "Success" if result else "Failed"
        logger.info(f"  {domain}: {status}")
    
    return all(results.values())

def main():
    """Main entry point."""
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="Customize AlphaEvolve for specific domains")
    parser.add_argument("--domain", type=str, help="Domain to customize for")
    parser.add_argument("--output", type=str, help="Output path for customized configuration")
    parser.add_argument("--all", action="store_true", help="Customize for all domains")
    args = parser.parse_args()
    
    # Run customization
    if args.all:
        asyncio.run(customize_all_domains())
    elif args.domain:
        asyncio.run(customize_config(args.domain, args.output))
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
