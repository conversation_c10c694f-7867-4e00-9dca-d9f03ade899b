"""
Agent Gmail Integration

This script integrates the Gmail browser automation with the agent system.
It provides a way for agents to send emails using browser automation.
"""
import os
import sys
import asyncio
import logging
import argparse
from typing import Dict, Optional, Any

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("agent_gmail_integration")

# Import Gmail browser automation
try:
    from gmail_browser_automation import GmailBrowserAutomation
except ImportError:
    logger.error("Gmail browser automation not found. Make sure gmail_browser_automation.py is in the current directory.")
    sys.exit(1)

# Try to import agent system components
try:
    from agents.base_agent import BaseAgent
    from services.gmail_service import GmailService
    from core.logger import setup_logger
    AGENT_SYSTEM_AVAILABLE = True
except ImportError:
    logger.warning("Agent system components not found. Running in standalone mode.")
    AGENT_SYSTEM_AVAILABLE = False
    BaseAgent = object
    GmailService = object
    setup_logger = lambda x: logging.getLogger(x)

class EmailAgent(BaseAgent):
    """Agent for sending emails using browser automation."""
    
    def __init__(self, 
                 name: str = "email_agent",
                 browser_type: str = "chrome",
                 gmail_service: Optional[GmailService] = None):
        """
        Initialize the Email Agent.
        
        Args:
            name (str): Name of the agent
            browser_type (str): Type of browser to use
            gmail_service (Optional[GmailService]): Gmail service for API-based email sending
        """
        if AGENT_SYSTEM_AVAILABLE:
            super().__init__(name=name)
        
        self.browser_type = browser_type
        self.gmail_service = gmail_service
        
        self.browser_automation = None
        self.logger = setup_logger(f"agent.{name}")
    
    async def initialize(self) -> bool:
        """
        Initialize the Email Agent.
        
        Returns:
            bool: True if initialization was successful, False otherwise
        """
        self.logger.info("Initializing Email Agent")
        
        if AGENT_SYSTEM_AVAILABLE:
            # Initialize base agent
            await super().initialize()
        
        # Create Gmail browser automation
        self.browser_automation = GmailBrowserAutomation(
            browser_type=self.browser_type
        )
        
        # Initialize browser automation
        success = self.browser_automation.initialize()
        if not success:
            self.logger.error("Failed to initialize Gmail browser automation")
            return False
        
        self.logger.info("Email Agent initialized successfully")
        return True
    
    async def send_email_browser(self, 
                               email_account: str,
                               password: str,
                               to_email: str, 
                               subject: str, 
                               body: str) -> Dict[str, Any]:
        """
        Send an email using browser automation.
        
        Args:
            email_account (str): Gmail account to send from
            password (str): Password for the Gmail account
            to_email (str): Recipient email address
            subject (str): Email subject
            body (str): Email body
            
        Returns:
            Dict[str, Any]: Result of the operation
        """
        self.logger.info(f"Sending email from {email_account} to {to_email} using browser automation")
        
        if not self.browser_automation:
            self.logger.error("Gmail browser automation not initialized")
            return {"success": False, "error": "Gmail browser automation not initialized"}
        
        result = self.browser_automation.send_email(
            email_account=email_account,
            password=password,
            to_email=to_email,
            subject=subject,
            body=body
        )
        
        return result
    
    async def send_email_api(self, 
                           to_email: str, 
                           subject: str, 
                           body: str,
                           cc: Optional[str] = None,
                           bcc: Optional[str] = None) -> Dict[str, Any]:
        """
        Send an email using the Gmail API.
        
        Args:
            to_email (str): Recipient email address
            subject (str): Email subject
            body (str): Email body
            cc (Optional[str]): CC recipients
            bcc (Optional[str]): BCC recipients
            
        Returns:
            Dict[str, Any]: Result of the operation
        """
        self.logger.info(f"Sending email to {to_email} using Gmail API")
        
        if not self.gmail_service:
            self.logger.error("Gmail service not initialized")
            return {"success": False, "error": "Gmail service not initialized"}
        
        if not self.gmail_service.is_enabled():
            self.logger.error("Gmail service not enabled")
            return {"success": False, "error": "Gmail service not enabled"}
        
        result = await self.gmail_service.send_message(
            to=to_email,
            subject=subject,
            body=body,
            cc=cc,
            bcc=bcc
        )
        
        if "error" in result:
            return {"success": False, "error": result["error"]}
        
        return {"success": True, "message": "Email sent successfully", "message_id": result.get("message_id")}
    
    async def shutdown(self) -> None:
        """Shut down the Email Agent."""
        if self.browser_automation:
            self.browser_automation.shutdown()
        self.logger.info("Email Agent shut down")

async def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Agent Gmail Integration")
    parser.add_argument("--email", type=str, default="<EMAIL>", help="Gmail account to send from")
    parser.add_argument("--password", type=str, required=True, help="Password for the Gmail account")
    parser.add_argument("--to", type=str, default="<EMAIL>", help="Recipient email address")
    parser.add_argument("--subject", type=str, default="Test Email from AI Agent System", help="Email subject")
    parser.add_argument("--body", type=str, default="This is a test email sent using browser automation integrated with the AI Agent System.", help="Email body")
    parser.add_argument("--browser", type=str, choices=["chrome", "firefox", "edge"], default="chrome", help="Browser to use")
    parser.add_argument("--method", type=str, choices=["browser", "api"], default="browser", help="Method to use for sending email")
    
    args = parser.parse_args()
    
    # Create Email Agent
    agent = EmailAgent(
        browser_type=args.browser
    )
    
    # Initialize
    initialized = await agent.initialize()
    if not initialized:
        logger.error("Failed to initialize Email Agent")
        return
    
    try:
        # Send email
        if args.method == "browser":
            result = await agent.send_email_browser(
                email_account=args.email,
                password=args.password,
                to_email=args.to,
                subject=args.subject,
                body=args.body
            )
        else:
            result = await agent.send_email_api(
                to_email=args.to,
                subject=args.subject,
                body=args.body
            )
        
        if result["success"]:
            logger.info("Email sent successfully")
        else:
            logger.error(f"Failed to send email: {result['error']}")
    
    finally:
        # Shut down
        await agent.shutdown()

if __name__ == "__main__":
    asyncio.run(main())
