"""
Agent Coordinator for the Multi-Agent AI System.

This module provides coordination capabilities for multiple agents,
allowing them to collaborate effectively on complex tasks.
It implements advanced communication protocols, message routing,
and error handling for robust agent interactions.
"""
import asyncio
import json
import logging
import os
from typing import Dict, List, Optional, Any, Union, Set, Tuple
import uuid
from datetime import datetime, timedelta
import heapq
import enum

from core.logger import setup_logger
from core.state_manager import StateManager
from llm.llm_router import <PERSON><PERSON><PERSON><PERSON>
from machine_learning.advanced_reasoning import AdvancedReasoning
from core.advanced_memory import AdvancedMemory

# Set up logger
logger = setup_logger("agent_coordinator")

class MessagePriority(enum.IntEnum):
    """Message priority levels."""
    LOW = 0
    NORMAL = 1
    HIGH = 2
    CRITICAL = 3

class MessageIntent(enum.Enum):
    """Message intent classification."""
    COMMAND = "command"           # Direct command to an agent
    QUERY = "query"               # Information request
    RESPONSE = "response"         # Response to a query or command
    NOTIFICATION = "notification" # Informational update
    ERROR = "error"               # Error notification
    STATUS = "status"             # Status update
    BROADCAST = "broadcast"       # Broadcast message to all agents
    COLLABORATION = "collaboration" # Collaboration request
    DELEGATION = "delegation"     # Task delegation
    FEEDBACK = "feedback"         # Feedback on a task or action

class AgentCoordinator:
    """
    Coordinator for multiple agents.

    This class provides coordination capabilities for multiple agents,
    allowing them to collaborate effectively on complex tasks.
    It implements advanced communication protocols, message routing,
    and error handling for robust agent interactions.
    """

    def __init__(
        self,
        state_manager: StateManager,
        llm_router: LLMRouter,
        shutdown_event: asyncio.Event,
    ):
        """
        Initialize the agent coordinator.

        Args:
            state_manager (StateManager): System state manager
            llm_router (LLMRouter): LLM router for generating responses
            shutdown_event (asyncio.Event): Event to signal system shutdown
        """
        self.state_manager = state_manager
        self.llm_router = llm_router
        self.shutdown_event = shutdown_event

        # Coordinator state
        self.workflows = {}
        self.tasks = {}
        self.agent_capabilities = {}
        self.agent_relationships = {}
        self.conversation_history = []

        # Message queues (priority-based)
        self.message_queue = asyncio.PriorityQueue()
        self.pending_responses = {}
        self.message_handlers = {}

        # Message tracking
        self.message_history = {}
        self.message_stats = {
            "sent": 0,
            "received": 0,
            "errors": 0,
            "retries": 0,
        }

        # Advanced reasoning
        self.advanced_reasoning = None

        # Advanced memory
        self.memory = None

        # Message processing task
        self.message_processor_task = None

        logger.info("Agent coordinator initialized")

    async def initialize(self):
        """Initialize the agent coordinator."""
        try:
            # Initialize advanced reasoning
            self.advanced_reasoning = AdvancedReasoning(self.llm_router)
            await self.advanced_reasoning.initialize()

            # Initialize advanced memory
            self.memory = AdvancedMemory("coordinator", self.state_manager)
            await self.memory.initialize()

            # Load workflows
            await self._load_workflows()

            # Load agent capabilities
            await self._load_agent_capabilities()

            # Load agent relationships
            await self._load_agent_relationships()

            # Register message handlers
            self._register_message_handlers()

            # Start message processor
            self.message_processor_task = asyncio.create_task(self._process_messages())

            logger.info("Agent coordinator initialized")

        except Exception as e:
            logger.exception(f"Error initializing agent coordinator: {e}")

    async def shutdown(self):
        """Shutdown the agent coordinator."""
        logger.info("Shutting down agent coordinator")

        # Cancel message processor task
        if self.message_processor_task:
            self.message_processor_task.cancel()
            try:
                await self.message_processor_task
            except asyncio.CancelledError:
                pass

        # Save state
        await self._save_state()

        logger.info("Agent coordinator shutdown complete")

    async def _save_state(self):
        """Save coordinator state."""
        # Save workflows
        await self.state_manager.update_state("coordinator", "workflows", self.workflows)

        # Save agent capabilities
        await self.state_manager.update_state("coordinator", "agent_capabilities", self.agent_capabilities)

        # Save agent relationships
        await self.state_manager.update_state("coordinator", "agent_relationships", self.agent_relationships)

        # Save message stats
        await self.state_manager.update_state("coordinator", "message_stats", self.message_stats)

    def _register_message_handlers(self):
        """Register message handlers."""
        # Register handlers for different message intents
        self.message_handlers = {
            MessageIntent.COMMAND.value: self._handle_command_message,
            MessageIntent.QUERY.value: self._handle_query_message,
            MessageIntent.RESPONSE.value: self._handle_response_message,
            MessageIntent.NOTIFICATION.value: self._handle_notification_message,
            MessageIntent.ERROR.value: self._handle_error_message,
            MessageIntent.STATUS.value: self._handle_status_message,
            MessageIntent.BROADCAST.value: self._handle_broadcast_message,
            MessageIntent.COLLABORATION.value: self._handle_collaboration_message,
            MessageIntent.DELEGATION.value: self._handle_delegation_message,
            MessageIntent.FEEDBACK.value: self._handle_feedback_message,
        }

    async def _load_workflows(self):
        """Load workflows from workflow configuration files."""
        try:
            # Check if workflows directory exists
            if not os.path.exists("workflows"):
                logger.warning("Workflows directory not found")
                return

            # Check if workflow configuration exists
            if not os.path.exists("config/workflows.json"):
                logger.warning("Workflow configuration file not found")
                return

            # Load workflow configuration
            with open("config/workflows.json", "r") as f:
                workflow_config = json.load(f)

            if not workflow_config.get("enabled", False):
                logger.info("Workflows are disabled in configuration")
                return

            # Load workflows
            workflows = {}
            for workflow_info in workflow_config.get("workflows", []):
                if not workflow_info.get("enabled", True):
                    continue

                workflow_id = workflow_info.get("id")
                workflow_path = workflow_info.get("path")

                if not workflow_id or not workflow_path or not os.path.exists(workflow_path):
                    logger.warning(f"Invalid workflow configuration: {workflow_info}")
                    continue

                try:
                    with open(workflow_path, "r") as f:
                        workflow = json.load(f)

                    workflows[workflow_id] = {
                        "id": workflow_id,
                        "config": workflow_info,
                        "definition": workflow
                    }
                    logger.info(f"Loaded workflow '{workflow_id}' from {workflow_path}")
                except Exception as e:
                    logger.error(f"Error loading workflow '{workflow_id}': {str(e)}")

            self.workflows = workflows
            logger.info(f"Loaded {len(self.workflows)} workflows")

            # Save to state manager
            await self.state_manager.update_state("coordinator", "workflows", self.workflows)

        except Exception as e:
            logger.exception(f"Error loading workflows: {e}")

    async def _load_agent_capabilities(self):
        """Load agent capabilities from state manager."""
        try:
            # Get all agents
            agents = await self.state_manager.get_state("agents")

            # Extract capabilities
            for agent_id, agent_data in agents.items():
                if "capabilities" in agent_data:
                    self.agent_capabilities[agent_id] = agent_data["capabilities"]

            logger.info(f"Loaded capabilities for {len(self.agent_capabilities)} agents")

        except Exception as e:
            logger.exception(f"Error loading agent capabilities: {e}")

    async def _load_agent_relationships(self):
        """Load agent relationships from state manager."""
        try:
            # Get agent relationships
            relationships = await self.state_manager.get_state("coordinator", "agent_relationships")

            if relationships:
                self.agent_relationships = relationships
                logger.info(f"Loaded {len(self.agent_relationships)} agent relationships")
            else:
                # Initialize relationships
                self.agent_relationships = {}
                for agent_id in self.agent_capabilities:
                    self.agent_relationships[agent_id] = {
                        "collaborators": [],
                        "trust_scores": {},
                        "communication_stats": {},
                    }
                logger.info(f"Initialized relationships for {len(self.agent_relationships)} agents")

        except Exception as e:
            logger.exception(f"Error loading agent relationships: {e}")

    async def _process_messages(self):
        """Process messages from the queue."""
        logger.info("Starting message processor")

        try:
            while not self.shutdown_event.is_set():
                # Get message from queue (with priority)
                priority, message = await self.message_queue.get()

                try:
                    # Process message
                    await self._process_message(message)

                    # Mark task as done
                    self.message_queue.task_done()

                except Exception as e:
                    logger.exception(f"Error processing message: {e}")
                    self.message_stats["errors"] += 1

                    # Handle error
                    await self._handle_message_error(message, str(e))

                    # Mark task as done
                    self.message_queue.task_done()

        except asyncio.CancelledError:
            logger.info("Message processor cancelled")
            raise

        except Exception as e:
            logger.exception(f"Error in message processor: {e}")

    async def _process_message(self, message: Dict):
        """
        Process a message.

        Args:
            message (Dict): Message to process
        """
        # Extract message details
        message_id = message.get("id")
        sender_id = message.get("sender_id")
        recipient_id = message.get("recipient_id")
        message_type = message.get("type")
        content = message.get("content")

        # Log message
        logger.debug(f"Processing message {message_id} from {sender_id} to {recipient_id} ({message_type})")

        # Update message stats
        self.message_stats["received"] += 1

        # Store message in history
        if message_id not in self.message_history:
            self.message_history[message_id] = message

        # Store in memory
        await self.memory.add_memory(
            content=message,
            memory_type="episodic",
            source=f"message:{sender_id}",
            metadata={
                "sender_id": sender_id,
                "recipient_id": recipient_id,
                "message_type": message_type,
            }
        )

        # Handle message based on type
        if message_type in self.message_handlers:
            await self.message_handlers[message_type](message)
        else:
            logger.warning(f"Unknown message type: {message_type}")

            # Send error response
            await self.send_message(
                sender_id,
                MessageIntent.ERROR.value,
                {
                    "error": f"Unknown message type: {message_type}",
                    "original_message": message_id,
                },
                priority=MessagePriority.NORMAL
            )

    async def create_workflow(
        self,
        name: str,
        description: str,
        steps: List[Dict],
    ) -> str:
        """
        Create a new workflow.

        Args:
            name (str): Workflow name
            description (str): Workflow description
            steps (List[Dict]): Workflow steps

        Returns:
            str: Workflow ID
        """
        workflow_id = str(uuid.uuid4())

        # Create workflow
        workflow = {
            "id": workflow_id,
            "name": name,
            "description": description,
            "steps": steps,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
        }

        # Store workflow
        self.workflows[workflow_id] = workflow

        # Save to state manager
        await self.state_manager.update_state("coordinator", "workflows", self.workflows)

        logger.info(f"Created workflow: {name} ({workflow_id})")

        return workflow_id

    async def execute_workflow(
        self,
        workflow_id: str,
        input_data: Dict,
    ) -> Dict:
        """
        Execute a workflow.

        Args:
            workflow_id (str): Workflow ID
            input_data (Dict): Input data for the workflow

        Returns:
            Dict: Workflow execution results
        """
        if workflow_id not in self.workflows:
            raise ValueError(f"Workflow not found: {workflow_id}")

        workflow = self.workflows[workflow_id]
        logger.info(f"Executing workflow: {workflow['name']} ({workflow_id})")

        # Create task
        task_id = str(uuid.uuid4())
        task = {
            "id": task_id,
            "workflow_id": workflow_id,
            "input_data": input_data,
            "status": "running",
            "step_results": {},
            "current_step": 0,
            "started_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
        }

        # Store task
        self.tasks[task_id] = task

        try:
            # Execute workflow steps
            result = input_data
            for i, step in enumerate(workflow["steps"]):
                # Update task status
                task["current_step"] = i
                task["updated_at"] = datetime.now().isoformat()

                # Execute step
                step_result = await self._execute_workflow_step(step, result)

                # Store step result
                task["step_results"][str(i)] = step_result

                # Update result for next step
                result = step_result

                # Check if shutdown requested
                if self.shutdown_event.is_set():
                    task["status"] = "cancelled"
                    logger.info(f"Workflow execution cancelled: {workflow_id}")
                    break

            # Update task status
            task["status"] = "completed"
            task["updated_at"] = datetime.now().isoformat()
            task["completed_at"] = datetime.now().isoformat()

            logger.info(f"Workflow execution completed: {workflow_id}")

            return {
                "task_id": task_id,
                "workflow_id": workflow_id,
                "status": "completed",
                "result": result,
            }

        except Exception as e:
            # Update task status
            task["status"] = "failed"
            task["error"] = str(e)
            task["updated_at"] = datetime.now().isoformat()

            logger.exception(f"Error executing workflow {workflow_id}: {e}")

            return {
                "task_id": task_id,
                "workflow_id": workflow_id,
                "status": "failed",
                "error": str(e),
            }

    async def _execute_workflow_step(self, step: Dict, input_data: Dict) -> Dict:
        """
        Execute a workflow step.

        Args:
            step (Dict): Workflow step
            input_data (Dict): Input data for the step

        Returns:
            Dict: Step execution results
        """
        step_type = step.get("type")

        if step_type == "agent":
            return await self._execute_agent_step(step, input_data)
        elif step_type == "reasoning":
            return await self._execute_reasoning_step(step, input_data)
        elif step_type == "transformation":
            return await self._execute_transformation_step(step, input_data)
        else:
            raise ValueError(f"Unknown step type: {step_type}")

    async def send_message(
        self,
        recipient_id: str,
        message_type: str,
        content: Any,
        priority: MessagePriority = MessagePriority.NORMAL,
        in_response_to: Optional[str] = None,
        metadata: Optional[Dict] = None,
    ) -> str:
        """
        Send a message to an agent.

        Args:
            recipient_id (str): Recipient agent ID
            message_type (str): Message type
            content (Any): Message content
            priority (MessagePriority): Message priority
            in_response_to (Optional[str]): ID of message this is responding to
            metadata (Optional[Dict]): Additional metadata

        Returns:
            str: Message ID
        """
        # Create message
        message_id = str(uuid.uuid4())
        message = {
            "id": message_id,
            "sender_id": "coordinator",
            "recipient_id": recipient_id,
            "type": message_type,
            "content": content,
            "timestamp": datetime.now().isoformat(),
            "in_response_to": in_response_to,
            "metadata": metadata or {},
        }

        # Add to queue with priority
        await self.message_queue.put((priority.value, message))

        # Update stats
        self.message_stats["sent"] += 1

        # Store in history
        self.message_history[message_id] = message

        # Store in memory
        await self.memory.add_memory(
            content=message,
            memory_type="episodic",
            source="outgoing_message",
            metadata={
                "recipient_id": recipient_id,
                "message_type": message_type,
                "priority": priority.value,
            }
        )

        logger.debug(f"Sent message {message_id} to {recipient_id} ({message_type})")

        return message_id

    async def _handle_message_error(self, message: Dict, error: str):
        """
        Handle a message processing error.

        Args:
            message (Dict): Message that caused the error
            error (str): Error message
        """
        message_id = message.get("id")
        sender_id = message.get("sender_id")

        logger.error(f"Error processing message {message_id}: {error}")

        # Send error response
        await self.send_message(
            sender_id,
            MessageIntent.ERROR.value,
            {
                "error": error,
                "original_message": message_id,
            },
            priority=MessagePriority.HIGH
        )

    async def _handle_command_message(self, message: Dict):
        """
        Handle a command message.

        Args:
            message (Dict): Command message
        """
        # Extract message details
        message_id = message.get("id")
        sender_id = message.get("sender_id")
        content = message.get("content", {})

        # Extract command
        command = content.get("command")

        if not command:
            await self._handle_message_error(message, "No command specified")
            return

        # Handle command
        try:
            if command == "execute_workflow":
                # Execute workflow
                workflow_id = content.get("workflow_id")
                input_data = content.get("input_data", {})

                if not workflow_id:
                    await self._handle_message_error(message, "No workflow ID specified")
                    return

                # Execute workflow
                result = await self.execute_workflow(workflow_id, input_data)

                # Send response
                await self.send_message(
                    sender_id,
                    MessageIntent.RESPONSE.value,
                    {
                        "command": command,
                        "result": result,
                    },
                    in_response_to=message_id
                )

            elif command == "create_workflow":
                # Create workflow
                name = content.get("name")
                description = content.get("description", "")
                steps = content.get("steps", [])

                if not name:
                    await self._handle_message_error(message, "No workflow name specified")
                    return

                # Create workflow
                workflow_id = await self.create_workflow(name, description, steps)

                # Send response
                await self.send_message(
                    sender_id,
                    MessageIntent.RESPONSE.value,
                    {
                        "command": command,
                        "workflow_id": workflow_id,
                    },
                    in_response_to=message_id
                )

            else:
                # Unknown command
                await self._handle_message_error(message, f"Unknown command: {command}")

        except Exception as e:
            # Handle error
            await self._handle_message_error(message, str(e))

    async def _handle_query_message(self, message: Dict):
        """
        Handle a query message.

        Args:
            message (Dict): Query message
        """
        # Extract message details
        message_id = message.get("id")
        sender_id = message.get("sender_id")
        content = message.get("content", {})

        # Extract query
        query = content.get("query")

        if not query:
            await self._handle_message_error(message, "No query specified")
            return

        # Handle query
        try:
            if query == "agent_capabilities":
                # Get agent capabilities
                agent_id = content.get("agent_id")

                if agent_id:
                    # Get capabilities for specific agent
                    capabilities = self.agent_capabilities.get(agent_id, [])

                    # Send response
                    await self.send_message(
                        sender_id,
                        MessageIntent.RESPONSE.value,
                        {
                            "query": query,
                            "agent_id": agent_id,
                            "capabilities": capabilities,
                        },
                        in_response_to=message_id
                    )
                else:
                    # Get all capabilities
                    await self.send_message(
                        sender_id,
                        MessageIntent.RESPONSE.value,
                        {
                            "query": query,
                            "agent_capabilities": self.agent_capabilities,
                        },
                        in_response_to=message_id
                    )

            elif query == "workflows":
                # Get workflows
                workflow_id = content.get("workflow_id")

                if workflow_id:
                    # Get specific workflow
                    workflow = self.workflows.get(workflow_id)

                    if workflow:
                        await self.send_message(
                            sender_id,
                            MessageIntent.RESPONSE.value,
                            {
                                "query": query,
                                "workflow_id": workflow_id,
                                "workflow": workflow,
                            },
                            in_response_to=message_id
                        )
                    else:
                        await self._handle_message_error(message, f"Workflow not found: {workflow_id}")
                else:
                    # Get all workflows
                    await self.send_message(
                        sender_id,
                        MessageIntent.RESPONSE.value,
                        {
                            "query": query,
                            "workflows": self.workflows,
                        },
                        in_response_to=message_id
                    )

            else:
                # Unknown query
                await self._handle_message_error(message, f"Unknown query: {query}")

        except Exception as e:
            # Handle error
            await self._handle_message_error(message, str(e))

    async def _handle_response_message(self, message: Dict):
        """
        Handle a response message.

        Args:
            message (Dict): Response message
        """
        # Extract message details
        message_id = message.get("id")
        in_response_to = message.get("in_response_to")

        # Store response
        if in_response_to:
            self.pending_responses[in_response_to] = message

    async def _handle_notification_message(self, message: Dict):
        """
        Handle a notification message.

        Args:
            message (Dict): Notification message
        """
        # Just log the notification
        logger.info(f"Notification: {message.get('content')}")

    async def _handle_error_message(self, message: Dict):
        """
        Handle an error message.

        Args:
            message (Dict): Error message
        """
        # Extract message details
        content = message.get("content", {})
        error = content.get("error", "Unknown error")
        original_message = content.get("original_message")

        # Log error
        logger.error(f"Error message: {error} (original message: {original_message})")

    async def _handle_status_message(self, message: Dict):
        """
        Handle a status message.

        Args:
            message (Dict): Status message
        """
        # Extract message details
        sender_id = message.get("sender_id")
        content = message.get("content", {})
        status = content.get("status")

        # Update agent status
        if sender_id in self.agent_capabilities:
            # Update agent status in state manager
            await self.state_manager.update_state("agents", f"{sender_id}_status", status)

    async def _handle_broadcast_message(self, message: Dict):
        """
        Handle a broadcast message.

        Args:
            message (Dict): Broadcast message
        """
        # Extract message details
        sender_id = message.get("sender_id")
        content = message.get("content")

        # Forward to all agents except sender
        for agent_id in self.agent_capabilities:
            if agent_id != sender_id:
                await self.send_message(
                    agent_id,
                    MessageIntent.BROADCAST.value,
                    content,
                    priority=MessagePriority.NORMAL,
                    metadata={"original_sender": sender_id}
                )

    async def _handle_collaboration_message(self, message: Dict):
        """
        Handle a collaboration message.

        Args:
            message (Dict): Collaboration message
        """
        # Extract message details
        message_id = message.get("id")
        sender_id = message.get("sender_id")
        content = message.get("content", {})

        # Extract collaboration request
        collaboration_type = content.get("collaboration_type")
        target_agent_id = content.get("target_agent_id")

        if not collaboration_type:
            await self._handle_message_error(message, "No collaboration type specified")
            return

        if not target_agent_id:
            await self._handle_message_error(message, "No target agent specified")
            return

        # Update agent relationships
        if sender_id in self.agent_relationships and target_agent_id in self.agent_capabilities:
            # Add to collaborators if not already present
            if target_agent_id not in self.agent_relationships[sender_id]["collaborators"]:
                self.agent_relationships[sender_id]["collaborators"].append(target_agent_id)

            # Initialize communication stats if not present
            if target_agent_id not in self.agent_relationships[sender_id]["communication_stats"]:
                self.agent_relationships[sender_id]["communication_stats"][target_agent_id] = {
                    "messages_sent": 0,
                    "messages_received": 0,
                    "last_communication": datetime.now().isoformat(),
                }

            # Forward collaboration request to target agent
            await self.send_message(
                target_agent_id,
                MessageIntent.COLLABORATION.value,
                {
                    "collaboration_type": collaboration_type,
                    "source_agent_id": sender_id,
                    "data": content.get("data", {}),
                },
                priority=MessagePriority.HIGH
            )

            # Send acknowledgment to sender
            await self.send_message(
                sender_id,
                MessageIntent.RESPONSE.value,
                {
                    "collaboration_type": collaboration_type,
                    "target_agent_id": target_agent_id,
                    "status": "forwarded",
                },
                in_response_to=message_id
            )
        else:
            # Agent not found
            await self._handle_message_error(message, f"Agent not found: {target_agent_id}")

    async def _handle_delegation_message(self, message: Dict):
        """
        Handle a delegation message.

        Args:
            message (Dict): Delegation message
        """
        # Extract message details
        message_id = message.get("id")
        sender_id = message.get("sender_id")
        content = message.get("content", {})

        # Extract delegation request
        task_type = content.get("task_type")
        parameters = content.get("parameters", {})

        if not task_type:
            await self._handle_message_error(message, "No task type specified")
            return

        # Find agent with capability
        capable_agents = []
        for agent_id, capabilities in self.agent_capabilities.items():
            if task_type in capabilities:
                capable_agents.append(agent_id)

        if not capable_agents:
            await self._handle_message_error(message, f"No agent found with capability: {task_type}")
            return

        # Select best agent (for now, just pick the first one)
        target_agent_id = capable_agents[0]

        # Forward task to target agent
        await self.send_message(
            target_agent_id,
            MessageIntent.COMMAND.value,
            {
                "command": task_type,
                "data": parameters,
                "delegated_by": sender_id,
            },
            priority=MessagePriority.HIGH
        )

        # Send acknowledgment to sender
        await self.send_message(
            sender_id,
            MessageIntent.RESPONSE.value,
            {
                "task_type": task_type,
                "target_agent_id": target_agent_id,
                "status": "delegated",
            },
            in_response_to=message_id
        )

    async def _handle_feedback_message(self, message: Dict):
        """
        Handle a feedback message.

        Args:
            message (Dict): Feedback message
        """
        # Extract message details
        sender_id = message.get("sender_id")
        content = message.get("content", {})

        # Extract feedback
        target_agent_id = content.get("target_agent_id")
        feedback_type = content.get("feedback_type")
        feedback_value = content.get("feedback_value")

        if not target_agent_id:
            await self._handle_message_error(message, "No target agent specified")
            return

        if not feedback_type:
            await self._handle_message_error(message, "No feedback type specified")
            return

        # Update agent relationships
        if sender_id in self.agent_relationships and target_agent_id in self.agent_capabilities:
            # Update trust score
            if feedback_type == "trust":
                self.agent_relationships[sender_id]["trust_scores"][target_agent_id] = feedback_value

            # Forward feedback to target agent
            await self.send_message(
                target_agent_id,
                MessageIntent.FEEDBACK.value,
                {
                    "feedback_type": feedback_type,
                    "feedback_value": feedback_value,
                    "source_agent_id": sender_id,
                },
                priority=MessagePriority.NORMAL
            )
        else:
            # Agent not found
            await self._handle_message_error(message, f"Agent not found: {target_agent_id}")

    async def _execute_agent_step(self, step: Dict, input_data: Dict) -> Dict:
        """
        Execute an agent step.

        Args:
            step (Dict): Workflow step
            input_data (Dict): Input data for the step

        Returns:
            Dict: Step execution results
        """
        agent_id = step.get("agent_id")
        action = step.get("action")

        if not agent_id:
            raise ValueError("Agent ID not specified")

        if not action:
            raise ValueError("Action not specified")

        # Create message for agent
        command_message_id = await self.send_message(
            agent_id,
            MessageIntent.COMMAND.value,
            {
                "command": action,
                "data": input_data,
            },
            priority=MessagePriority.HIGH
        )

        # Wait for response (in a real implementation, this would use a more sophisticated approach)
        # For now, we'll just return a placeholder

        return {
            "status": "success",
            "agent_id": agent_id,
            "action": action,
            "message_id": command_message_id,
            "result": {
                "message": f"Executed {action} on {agent_id}",
                "data": input_data,
            },
        }

    async def _execute_reasoning_step(self, step: Dict, input_data: Dict) -> Dict:
        """
        Execute a reasoning step.

        Args:
            step (Dict): Workflow step
            input_data (Dict): Input data for the step

        Returns:
            Dict: Step execution results
        """
        reasoning_type = step.get("reasoning_type")

        if not reasoning_type:
            raise ValueError("Reasoning type not specified")

        if reasoning_type == "causal":
            return await self._execute_causal_reasoning(step, input_data)
        elif reasoning_type == "counterfactual":
            return await self._execute_counterfactual_reasoning(step, input_data)
        else:
            raise ValueError(f"Unknown reasoning type: {reasoning_type}")

    async def _execute_causal_reasoning(self, step: Dict, input_data: Dict) -> Dict:
        """
        Execute causal reasoning.

        Args:
            step (Dict): Workflow step
            input_data (Dict): Input data for the step

        Returns:
            Dict: Step execution results
        """
        context = input_data.get("context", "")
        question = input_data.get("question", "")
        variables = input_data.get("variables", [])

        # Perform causal reasoning
        result = await self.advanced_reasoning.causal_reasoning(
            context=context,
            question=question,
            variables=variables,
        )

        return {
            "status": "success",
            "reasoning_type": "causal",
            "result": result,
        }
