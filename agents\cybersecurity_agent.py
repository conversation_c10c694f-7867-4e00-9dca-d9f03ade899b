"""
Cybersecurity Agent for security testing and analysis.
"""
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import json
import re
import uuid
import hashlib
import os
from pathlib import Path

from agents.base_agent import BaseAgent
from core.logger import setup_logger
from llm.llm_router import <PERSON><PERSON>outer
from services.tool_service import ToolService
from services.vulnerability_database import VulnerabilityDatabase

# Import tool-specific implementations
from agents.cybersecurity_agent_tools import (
    _handle_network_scan,
    _handle_vulnerability_scan,
    _handle_password_audit,
    _handle_web_security_scan,
    _handle_wifi_security,
    _handle_sql_injection_test,
    _handle_security_report
)

# Import AI-enhanced security analysis
from agents.cybersecurity_agent_ai import (
    analyze_vulnerabilities,
    perform_threat_modeling,
    generate_security_recommendations,
    analyze_security_incident
)

# Import multi-agent system
from agents.cybersecurity_multi_agent import CybersecurityMultiAgentSystem

class CybersecurityAgent(BaseAgent):
    """
    Agent specialized for cybersecurity testing and analysis.

    This agent handles tasks related to security testing, vulnerability scanning,
    and security analysis using various cybersecurity tools.
    """

    def __init__(
        self,
        agent_id: str,
        config: Dict,
        state_manager,
        message_queue,
        shutdown_event
    ):
        """Initialize the cybersecurity agent."""
        super().__init__(agent_id, config, state_manager, message_queue, shutdown_event)

        # Cybersecurity-specific configuration
        self.llm_provider = config.get("llm_provider", "anthropic")
        self.llm_router = None

        # Services
        self.tool_service = None
        self.vuln_db_service = None

        # Multi-agent system
        self.multi_agent_system = None

        # Cybersecurity data
        self.scan_history = {}
        self.vulnerability_database = {}
        self.security_reports = {}
        self.threat_models = {}
        self.security_recommendations = {}
        self.incident_analyses = {}

        # Agent capabilities
        self.capabilities = [
            # Basic security testing
            "network_scan",
            "vulnerability_scan",
            "password_audit",
            "web_security_scan",
            "wifi_security",
            "sql_injection_test",
            "security_report",

            # AI-enhanced security analysis
            "vulnerability_analysis",
            "threat_modeling",
            "security_recommendations",
            "incident_analysis",

            # Vulnerability database
            "cve_lookup",

            # Multi-agent capabilities
            "security_assessment",
            "vulnerability_assessment",
            "collaborative_analysis",
        ]

    async def initialize(self):
        """Initialize the cybersecurity agent."""
        await super().initialize()

        # Initialize LLM router
        self.llm_router = LLMRouter()
        await self.llm_router.initialize()

        # Initialize tool service
        tool_config = await self.state_manager.get_state("system", "tool_service")
        if not tool_config:
            # Create default tool configuration
            tool_config = {
                "enabled": True,
                "tools_dir": str(Path(__file__).resolve().parent.parent / "tools"),
                "tool_registry": {
                    "nmap": {
                        "description": "Network mapper for network discovery and security auditing",
                        "category": "network",
                        "install_method": "auto",
                    },
                    "john": {
                        "description": "John the Ripper password cracker",
                        "category": "password",
                        "install_method": "auto",
                    },
                    "wireshark": {
                        "description": "Network protocol analyzer",
                        "category": "network",
                        "install_method": "auto",
                    },
                    "metasploit-framework": {
                        "description": "Penetration testing framework",
                        "category": "exploitation",
                        "install_method": "auto",
                    },
                    "burpsuite": {
                        "description": "Web vulnerability scanner",
                        "category": "web",
                        "install_method": "auto",
                    },
                    "aircrack-ng": {
                        "description": "WiFi security assessment tools",
                        "category": "wifi",
                        "install_method": "auto",
                    },
                    "sqlmap": {
                        "description": "Automatic SQL injection tool",
                        "category": "web",
                        "install_method": "auto",
                    },
                    "zaproxy": {
                        "description": "OWASP Zed Attack Proxy",
                        "category": "web",
                        "install_method": "auto",
                    },
                    "theharvester": {
                        "description": "E-mail, subdomain and name harvester",
                        "category": "reconnaissance",
                        "install_method": "auto",
                    },
                    "nikto": {
                        "description": "Web server scanner",
                        "category": "web",
                        "install_method": "auto",
                    },
                    "pentestgpt": {
                        "description": "AI-powered penetration testing tool",
                        "category": "ai",
                        "install_method": "pip",
                    },
                }
            }

            # Save tool configuration
            await self.state_manager.update_state("system", "tool_service", tool_config)

        # Initialize tool service
        self.tool_service = ToolService(tool_config)

        # Initialize vulnerability database service
        vuln_db_config = await self.state_manager.get_state("system", "vulnerability_database")
        if not vuln_db_config:
            # Create default vulnerability database configuration
            vuln_db_config = {
                "enabled": True,
                "api_key": "",  # NVD API key (optional)
                "cache_dir": str(Path(__file__).resolve().parent.parent / "data" / "vulnerability_cache"),
                "cache_ttl": 86400,  # 24 hours in seconds
            }

            # Save vulnerability database configuration
            await self.state_manager.update_state("system", "vulnerability_database", vuln_db_config)

        # Initialize vulnerability database service
        self.vuln_db_service = VulnerabilityDatabase(vuln_db_config)
        await self.vuln_db_service.initialize()

        # Initialize multi-agent system
        self.multi_agent_system = CybersecurityMultiAgentSystem(
            system_id="cybersecurity_multi_agent",
            name="Cybersecurity Multi-Agent System",
            description="A collaborative system of specialized agents for cybersecurity tasks",
            tool_service=self.tool_service,
            vuln_db_service=self.vuln_db_service,
        )
        await self.multi_agent_system.initialize(self.llm_router)

        # Load cybersecurity data
        await self._load_cybersecurity_data()

    async def _load_cybersecurity_data(self):
        """Load cybersecurity data from state manager."""
        # Load scan history
        scan_history = await self.state_manager.get_state("cybersecurity", "scan_history")
        if scan_history:
            self.scan_history = scan_history

        # Load vulnerability database
        vulnerability_database = await self.state_manager.get_state("cybersecurity", "vulnerability_database")
        if vulnerability_database:
            self.vulnerability_database = vulnerability_database

        # Load security reports
        security_reports = await self.state_manager.get_state("cybersecurity", "security_reports")
        if security_reports:
            self.security_reports = security_reports

        # Load threat models
        threat_models = await self.state_manager.get_state("cybersecurity", "threat_models")
        if threat_models:
            self.threat_models = threat_models

        # Load security recommendations
        security_recommendations = await self.state_manager.get_state("cybersecurity", "security_recommendations")
        if security_recommendations:
            self.security_recommendations = security_recommendations

        # Load incident analyses
        incident_analyses = await self.state_manager.get_state("cybersecurity", "incident_analyses")
        if incident_analyses:
            self.incident_analyses = incident_analyses

    async def _save_cybersecurity_data(self):
        """Save cybersecurity data to state manager."""
        # Save scan history
        await self.state_manager.update_state("cybersecurity", "scan_history", self.scan_history)

        # Save vulnerability database
        await self.state_manager.update_state("cybersecurity", "vulnerability_database", self.vulnerability_database)

        # Save security reports
        await self.state_manager.update_state("cybersecurity", "security_reports", self.security_reports)

        # Save threat models
        await self.state_manager.update_state("cybersecurity", "threat_models", self.threat_models)

        # Save security recommendations
        await self.state_manager.update_state("cybersecurity", "security_recommendations", self.security_recommendations)

        # Save incident analyses
        await self.state_manager.update_state("cybersecurity", "incident_analyses", self.incident_analyses)

    async def execute_cycle(self):
        """Execute one cycle of the cybersecurity agent's logic."""
        self.logger.debug("Executing cybersecurity agent cycle")

        try:
            # Check for pending tasks
            pending_tasks = await self.state_manager.get_state("cybersecurity", "pending_tasks")
            if pending_tasks:
                for task_id, task in pending_tasks.items():
                    if task.get("status") == "pending":
                        await self._process_task(task_id, task)

            # Update state with any changes
            await self._save_cybersecurity_data()

        except Exception as e:
            self.logger.exception(f"Error in cybersecurity agent cycle: {e}")

    async def _process_task(self, task_id: str, task: Dict):
        """
        Process a cybersecurity task.

        Args:
            task_id (str): Task identifier
            task (Dict): Task data
        """
        self.logger.info(f"Processing task: {task_id}")

        # Update task status
        task["status"] = "processing"
        await self.state_manager.update_state("cybersecurity", "pending_tasks", {task_id: task})

        try:
            # Get task type
            task_type = task.get("type")

            # Process task based on type
            if task_type == "network_scan":
                result = await _handle_network_scan(self, task)
            elif task_type == "vulnerability_scan":
                result = await _handle_vulnerability_scan(self, task)
            elif task_type == "password_audit":
                result = await _handle_password_audit(self, task)
            elif task_type == "web_security_scan":
                result = await _handle_web_security_scan(self, task)
            elif task_type == "wifi_security":
                result = await _handle_wifi_security(self, task)
            elif task_type == "sql_injection_test":
                result = await _handle_sql_injection_test(self, task)
            elif task_type == "security_report":
                result = await _handle_security_report(self, task)
            elif task_type == "vulnerability_analysis":
                # AI-enhanced vulnerability analysis
                scan_results = task.get("scan_results", {})
                result = await analyze_vulnerabilities(self, scan_results)

                # Store in vulnerability analyses
                self.vulnerability_database[task_id] = result
            elif task_type == "threat_modeling":
                # AI-enhanced threat modeling
                system_description = task.get("system_description", "")
                scan_results = task.get("scan_results", {})
                result = await perform_threat_modeling(self, system_description, scan_results)

                # Store in threat models
                self.threat_models[task_id] = result
            elif task_type == "security_recommendations":
                # AI-enhanced security recommendations
                security_posture = task.get("security_posture", "")
                scan_results = task.get("scan_results", {})
                result = await generate_security_recommendations(self, security_posture, scan_results)

                # Store in security recommendations
                self.security_recommendations[task_id] = result
            elif task_type == "incident_analysis":
                # AI-enhanced incident analysis
                incident_details = task.get("incident_details", "")
                result = await analyze_security_incident(self, incident_details)

                # Store in incident analyses
                self.incident_analyses[task_id] = result
            elif task_type == "cve_lookup":
                # Look up CVE details
                cve_id = task.get("cve_id")
                if not cve_id:
                    raise ValueError("Missing cve_id parameter")

                # Check if vulnerability database service is available
                if not self.vuln_db_service or not self.vuln_db_service.is_enabled():
                    raise ValueError("Vulnerability database service is not available")

                # Get CVE details
                result = await self.vuln_db_service.get_cve_details(cve_id)

            # Multi-agent tasks
            elif task_type == "security_assessment":
                # Run security assessment using multi-agent system
                target = task.get("target")
                options = task.get("options", {})

                if not target:
                    raise ValueError("Missing target parameter")

                # Check if multi-agent system is available
                if not self.multi_agent_system:
                    raise ValueError("Multi-agent system is not available")

                # Run security assessment
                result = await self.multi_agent_system.run_security_assessment(target, options)

            elif task_type == "vulnerability_assessment":
                # Run vulnerability assessment using multi-agent system
                target = task.get("target")
                options = task.get("options", {})

                if not target:
                    raise ValueError("Missing target parameter")

                # Check if multi-agent system is available
                if not self.multi_agent_system:
                    raise ValueError("Multi-agent system is not available")

                # Run vulnerability assessment
                result = await self.multi_agent_system.run_vulnerability_assessment(target, options)

            elif task_type == "collaborative_analysis":
                # Run collaborative analysis using multi-agent system
                data = task.get("data", {})
                query = task.get("query", "")

                if not data and not query:
                    raise ValueError("Missing data or query parameter")

                # Check if multi-agent system is available
                if not self.multi_agent_system:
                    raise ValueError("Multi-agent system is not available")

                # Run collaborative analysis
                input_data = {
                    "query": query,
                    "timestamp": datetime.now().isoformat(),
                    "metadata": data,
                }

                result = await self.multi_agent_system.chat(input_data)
            else:
                raise ValueError(f"Unknown task type: {task_type}")

            # Update task with result
            task["status"] = "completed"
            task["result"] = result
            task["completed_at"] = datetime.now().isoformat()

            # Save task
            await self.state_manager.update_state("cybersecurity", "pending_tasks", {task_id: task})

            # Add to scan history
            self.scan_history[task_id] = {
                "task_id": task_id,
                "type": task_type,
                "timestamp": datetime.now().isoformat(),
                "summary": result.get("summary", ""),
            }

            # Send notification
            await self.send_message(
                "agent_manager",
                "notification",
                {
                    "title": f"Cybersecurity task completed: {task_type}",
                    "message": f"Task {task_id} has been completed.",
                    "data": {
                        "task_id": task_id,
                        "task_type": task_type,
                    },
                }
            )

        except Exception as e:
            self.logger.exception(f"Error processing task {task_id}: {e}")

            # Update task status
            task["status"] = "error"
            task["error"] = str(e)
            await self.state_manager.update_state("cybersecurity", "pending_tasks", {task_id: task})

            # Send error notification
            await self.send_message(
                "agent_manager",
                "error",
                {
                    "title": f"Error in cybersecurity task: {task.get('type')}",
                    "message": f"Error processing task {task_id}: {str(e)}",
                    "data": {
                        "task_id": task_id,
                        "task_type": task.get("type"),
                        "error": str(e),
                    },
                }
            )

    async def handle_command(self, message: Dict):
        """
        Handle a command message.

        Args:
            message (Dict): Command message
        """
        command = message.get("content", {}).get("command")
        params = message.get("content", {}).get("params", {})

        if command == "list_tools":
            # List available tools
            tools = await self.tool_service.list_tools()

            await self.send_message(
                message.get("sender_id"),
                "response",
                {
                    "command": "list_tools",
                    "tools": tools,
                }
            )
            return

        elif command == "install_tool":
            # Install a tool
            tool_name = params.get("tool_name")

            if not tool_name:
                await self.send_message(
                    message.get("sender_id"),
                    "error",
                    {
                        "command": command,
                        "error": "Missing tool_name parameter",
                    }
                )
                return

            success = await self.tool_service.install_tool(tool_name)

            await self.send_message(
                message.get("sender_id"),
                "response",
                {
                    "command": "install_tool",
                    "tool_name": tool_name,
                    "success": success,
                }
            )
            return

        elif command == "run_scan":
            # Run a security scan
            scan_type = params.get("scan_type")
            target = params.get("target")

            if not scan_type:
                await self.send_message(
                    message.get("sender_id"),
                    "error",
                    {
                        "command": command,
                        "error": "Missing scan_type parameter",
                    }
                )
                return

            if not target:
                await self.send_message(
                    message.get("sender_id"),
                    "error",
                    {
                        "command": command,
                        "error": "Missing target parameter",
                    }
                )
                return

            # Create task
            task_id = f"{scan_type}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
            task = {
                "type": scan_type,
                "target": target,
                "status": "pending",
                "created_at": datetime.now().isoformat(),
                "params": params,
            }

            # Save task
            await self.state_manager.update_state("cybersecurity", "pending_tasks", {task_id: task})

            await self.send_message(
                message.get("sender_id"),
                "response",
                {
                    "command": "run_scan",
                    "task_id": task_id,
                    "status": "pending",
                }
            )
            return

        elif command == "get_task_status":
            # Get task status
            task_id = params.get("task_id")

            if not task_id:
                await self.send_message(
                    message.get("sender_id"),
                    "error",
                    {
                        "command": command,
                        "error": "Missing task_id parameter",
                    }
                )
                return

            # Get task
            task = await self.state_manager.get_state("cybersecurity", "pending_tasks", task_id)

            if not task:
                await self.send_message(
                    message.get("sender_id"),
                    "error",
                    {
                        "command": command,
                        "error": f"Task {task_id} not found",
                    }
                )
                return

            await self.send_message(
                message.get("sender_id"),
                "response",
                {
                    "command": "get_task_status",
                    "task_id": task_id,
                    "status": task.get("status"),
                    "created_at": task.get("created_at"),
                    "completed_at": task.get("completed_at"),
                }
            )
            return

        elif command == "get_task_result":
            # Get task result
            task_id = params.get("task_id")

            if not task_id:
                await self.send_message(
                    message.get("sender_id"),
                    "error",
                    {
                        "command": command,
                        "error": "Missing task_id parameter",
                    }
                )
                return

            # Get task
            task = await self.state_manager.get_state("cybersecurity", "pending_tasks", task_id)

            if not task:
                await self.send_message(
                    message.get("sender_id"),
                    "error",
                    {
                        "command": command,
                        "error": f"Task {task_id} not found",
                    }
                )
                return

            if task.get("status") != "completed":
                await self.send_message(
                    message.get("sender_id"),
                    "error",
                    {
                        "command": command,
                        "error": f"Task {task_id} is not completed",
                        "status": task.get("status"),
                    }
                )
                return

            await self.send_message(
                message.get("sender_id"),
                "response",
                {
                    "command": "get_task_result",
                    "task_id": task_id,
                    "result": task.get("result"),
                }
            )
            return

        # If command not handled, call parent method
        await super().handle_command(message)

    async def handle_query(self, message: Dict):
        """
        Handle a query message.

        Args:
            message (Dict): Query message
        """
        query_type = message.get("content", {}).get("query_type")
        query_params = message.get("content", {}).get("params", {})

        if query_type == "scan_history":
            # Return scan history
            await self.send_message(
                message.get("sender_id"),
                "response",
                {
                    "query_type": "scan_history",
                    "scan_history": self.scan_history,
                }
            )
            return

        elif query_type == "vulnerability_database":
            # Return vulnerability database
            await self.send_message(
                message.get("sender_id"),
                "response",
                {
                    "query_type": "vulnerability_database",
                    "vulnerability_database": self.vulnerability_database,
                }
            )
            return

        elif query_type == "security_reports":
            # Return security reports
            await self.send_message(
                message.get("sender_id"),
                "response",
                {
                    "query_type": "security_reports",
                    "security_reports": self.security_reports,
                }
            )
            return

        elif query_type == "threat_models":
            # Return threat models
            await self.send_message(
                message.get("sender_id"),
                "response",
                {
                    "query_type": "threat_models",
                    "threat_models": self.threat_models,
                }
            )
            return

        elif query_type == "security_recommendations":
            # Return security recommendations
            await self.send_message(
                message.get("sender_id"),
                "response",
                {
                    "query_type": "security_recommendations",
                    "security_recommendations": self.security_recommendations,
                }
            )
            return

        elif query_type == "incident_analyses":
            # Return incident analyses
            await self.send_message(
                message.get("sender_id"),
                "response",
                {
                    "query_type": "incident_analyses",
                    "incident_analyses": self.incident_analyses,
                }
            )
            return

        elif query_type == "cve_lookup":
            # Look up CVE details
            cve_id = query_params.get("cve_id")
            if not cve_id:
                await self.send_message(
                    message.get("sender_id"),
                    "error",
                    {
                        "query_type": "cve_lookup",
                        "error": "Missing cve_id parameter",
                    }
                )
                return

            # Check if vulnerability database service is available
            if not self.vuln_db_service or not self.vuln_db_service.is_enabled():
                await self.send_message(
                    message.get("sender_id"),
                    "error",
                    {
                        "query_type": "cve_lookup",
                        "error": "Vulnerability database service is not available",
                    }
                )
                return

            # Get CVE details
            cve_details = await self.vuln_db_service.get_cve_details(cve_id)

            await self.send_message(
                message.get("sender_id"),
                "response",
                {
                    "query_type": "cve_lookup",
                    "cve_id": cve_id,
                    "cve_details": cve_details,
                }
            )
            return

        elif query_type == "recent_vulnerabilities":
            # Get recent vulnerabilities
            days = query_params.get("days", 30)
            max_results = query_params.get("max_results", 10)

            # Check if vulnerability database service is available
            if not self.vuln_db_service or not self.vuln_db_service.is_enabled():
                await self.send_message(
                    message.get("sender_id"),
                    "error",
                    {
                        "query_type": "recent_vulnerabilities",
                        "error": "Vulnerability database service is not available",
                    }
                )
                return

            # Get recent vulnerabilities
            recent_vulnerabilities = await self.vuln_db_service.get_recent_vulnerabilities(days, max_results)

            await self.send_message(
                message.get("sender_id"),
                "response",
                {
                    "query_type": "recent_vulnerabilities",
                    "days": days,
                    "max_results": max_results,
                    "recent_vulnerabilities": recent_vulnerabilities,
                }
            )
            return

        # If query not handled, call parent method
        await super().handle_query(message)
