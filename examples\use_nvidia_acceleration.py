"""
Example of using NVIDIA acceleration with AI agents.

This script demonstrates how to integrate NVIDIA developer tools with
your agent system to accelerate models and add new capabilities.
"""
import asyncio
import os
import sys
import json
from typing import Dict, List, Optional

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.logger import setup_logger
from nvidia_integration.agent_accelerator import AgentAccelerator
from agents.base_agent import BaseAgent

# Import agent types you want to accelerate
try:
    from agents.ml_agent import MLAgent
    from agents.llm_agent import LLMAgent
    from agents.vision_agent import VisionAgent
    from agents.speech_agent import SpeechAgent
    ML_AGENTS_AVAILABLE = True
except ImportError:
    ML_AGENTS_AVAILABLE = False

# Set up logger
logger = setup_logger("nvidia_example")

class AcceleratedAgentExample:
    """Example class for using NVIDIA acceleration with agents."""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the example.
        
        Args:
            config_path: Path to NVIDIA configuration file
        """
        # Load configuration
        self.config = {}
        if config_path and os.path.exists(config_path):
            with open(config_path, 'r') as f:
                self.config = json.load(f)
        
        # Create accelerator
        self.accelerator = AgentAccelerator(self.config)
        self.initialized = False
        
        # Prepare agents collection
        self.agents = {}
        
    async def initialize(self):
        """Initialize the accelerator and create accelerated agents."""
        logger.info("Initializing NVIDIA accelerated agents example")
        
        # Initialize the accelerator
        await self.accelerator.initialize()
        
        # Check if initialization was successful
        if not self.accelerator.initialized:
            logger.warning("NVIDIA acceleration could not be initialized. Using non-accelerated agents.")
        else:
            logger.info("NVIDIA acceleration initialized successfully")
            
            # Get GPU information
            gpu_info = self.accelerator.get_gpu_info()
            if gpu_info.get("available", False):
                logger.info("GPU available: %s", gpu_info.get("torch_cuda_device_name", "Unknown GPU"))
                logger.info("CUDA version: %s", gpu_info.get("cuda_version", "Unknown"))
            else:
                logger.warning("No GPUs available for acceleration")
        
        self.initialized = True
    
    async def setup_accelerated_agents(self):
        """Set up accelerated agents with NVIDIA technologies."""
        if not self.initialized:
            logger.warning("Example not initialized. Please call initialize() first.")
            return
            
        # Create and accelerate LLM agent
        if ML_AGENTS_AVAILABLE:
            try:
                # Create a standard LLM agent
                llm_agent = LLMAgent(model_name="gpt-3.5-turbo")
                await llm_agent.initialize()
                
                # Accelerate the agent
                accelerated_llm_agent = self.accelerator.accelerate_agent_model(llm_agent)
                self.agents["llm_agent"] = accelerated_llm_agent
                logger.info("Created and accelerated LLM agent")
                
                # Create and accelerate ML agent with quantized models
                ml_agent = MLAgent()
                await ml_agent.initialize()
                
                # Quantize the agent's models to INT8 precision
                quantized_ml_agent = self.accelerator.quantize_agent_model(ml_agent, quantization_type="int8")
                self.agents["ml_agent"] = quantized_ml_agent
                logger.info("Created and quantized ML agent")
                
                # Create vision agent with Metropolis capabilities if available
                if self.accelerator.enable_metropolis:
                    vision_agent = VisionAgent()
                    await vision_agent.initialize()
                    
                    # Enhance with Metropolis features (without explicit acceleration)
                    self.agents["vision_agent"] = vision_agent
                    logger.info("Created vision agent with Metropolis capabilities")
                
                # Create speech agent with Riva capabilities if available
                if self.accelerator.enable_riva:
                    speech_agent = SpeechAgent()
                    await speech_agent.initialize()
                    
                    # Enhance with Riva features (without explicit acceleration)
                    self.agents["speech_agent"] = speech_agent
                    logger.info("Created speech agent with Riva capabilities")
                
            except Exception as e:
                logger.exception("Error setting up ML agents: %s", e)
        else:
            logger.warning("ML agents not available. Skipping agent setup.")
            
            # Create a simple base agent for demonstration
            base_agent = BaseAgent(name="demo_agent")
            self.agents["base_agent"] = base_agent
            logger.info("Created basic demo agent")
    
    async def run_example_tasks(self):
        """Run example tasks with the accelerated agents."""
        if not self.initialized or not self.agents:
            logger.warning("Agents not set up. Please call setup_accelerated_agents() first.")
            return
        
        logger.info("Running example tasks with accelerated agents")
        
        # Example task 1: Generate text with LLM agent
        if "llm_agent" in self.agents:
            try:
                llm_response = await self.agents["llm_agent"].generate("Explain how NVIDIA acceleration helps AI applications")
                logger.info("LLM Agent response: %s", llm_response[:100] + "..." if len(llm_response) > 100 else llm_response)
            except Exception as e:
                logger.error("Error running LLM agent: %s", e)
        
        # Example task 2: Process image with vision agent and Metropolis
        if "vision_agent" in self.agents and self.accelerator.enable_metropolis:
            try:
                # Use a sample image path or generated data for testing
                sample_image_path = "examples/sample_data/sample_image.jpg"
                
                if os.path.exists(sample_image_path):
                    # Detect objects using Metropolis
                    detection_results = await self.accelerator.detect_objects(sample_image_path)
                    logger.info("Object detection results: %s", 
                                json.dumps(detection_results, indent=2) if detection_results.get("success") else "Detection failed")
                else:
                    logger.warning("Sample image not found: %s", sample_image_path)
            except Exception as e:
                logger.error("Error running vision tasks: %s", e)
        
        # Example task 3: Speech recognition with speech agent and Riva
        if "speech_agent" in self.agents and self.accelerator.enable_riva:
            try:
                # Use a sample audio path or generated data for testing
                sample_audio_path = "examples/sample_data/sample_audio.wav"
                
                if os.path.exists(sample_audio_path):
                    # Perform speech-to-text using Riva
                    transcription = await self.accelerator.speech_to_text(sample_audio_path)
                    logger.info("Speech-to-text result: %s", 
                                transcription.get("transcript") if transcription.get("success") else "Transcription failed")
                else:
                    logger.warning("Sample audio not found: %s", sample_audio_path)
            except Exception as e:
                logger.error("Error running speech tasks: %s", e)
    
    async def get_accelerator_status(self):
        """Get the status of the NVIDIA accelerator."""
        if not self.initialized:
            return {"initialized": False}
        
        return self.accelerator.get_status()
    
    async def shutdown(self):
        """Shutdown the example and release resources."""
        if self.initialized:
            logger.info("Shutting down NVIDIA accelerated agents example")
            
            # Shutdown the accelerator
            await self.accelerator.shutdown()
            
            # Clean up agents
            for agent_name, agent in self.agents.items():
                if hasattr(agent, 'shutdown') and callable(agent.shutdown):
                    try:
                        await agent.shutdown()
                        logger.info(f"Shutdown agent: {agent_name}")
                    except Exception as e:
                        logger.error(f"Error shutting down agent {agent_name}: {e}")
            
            self.agents = {}
            self.initialized = False
            
            logger.info("Example shutdown complete")


async def main():
    """Run the NVIDIA acceleration example."""
    # Path to custom configuration (if available)
    config_path = "config/nvidia_config.json"
    
    # Create and run the example
    example = AcceleratedAgentExample(config_path if os.path.exists(config_path) else None)
    
    try:
        # Initialize the example
        await example.initialize()
        
        # Setup accelerated agents
        await example.setup_accelerated_agents()
        
        # Get and print accelerator status
        status = await example.get_accelerator_status()
        logger.info("NVIDIA Accelerator status: %s", json.dumps(status, indent=2))
        
        # Run example tasks
        await example.run_example_tasks()
        
    finally:
        # Always clean up resources
        await example.shutdown()


if __name__ == "__main__":
    asyncio.run(main())