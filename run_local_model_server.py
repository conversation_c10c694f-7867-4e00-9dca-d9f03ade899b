"""
<PERSON>ript to run a local Hugging Face model server.
"""
import sys
import os
import argparse
import subprocess
import time
from pathlib import Path

def check_requirements():
    """Check if required packages are installed."""
    try:
        import torch
        import transformers
        import accelerate
        import bitsandbytes
        import fastapi
        import uvicorn
        return True
    except ImportError as e:
        print(f"Missing required package: {e}")
        print("Please install the required packages:")
        print("pip install torch transformers accelerate bitsandbytes fastapi uvicorn")
        return <PERSON>alse

def create_server_script(model_id: str, port: int, quantize: bool, max_memory: str = None, embedding_model: str = None):
    """
    Create a server script for the model.

    Args:
        model_id (str): Hugging Face model ID
        port (int): Port to run the server on
        quantize (bool): Whether to use quantization
        max_memory (str): Maximum memory to use (e.g., "8GiB")
        embedding_model (str): Hugging Face embedding model ID

    Returns:
        str: Path to the server script
    """
    script_dir = Path("scripts")
    script_dir.mkdir(exist_ok=True)

    script_path = script_dir / "model_server.py"

    # Create server script
    with open(script_path, "w") as f:
        f.write(f"""
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, pipeline
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import uvicorn
import gc
import os

# Model configuration
MODEL_ID = "{model_id}"
EMBEDDING_MODEL_ID = {repr(embedding_model)}
QUANTIZE = {str(quantize)}
MAX_MEMORY = {repr(max_memory)}
PORT = {port}

# Initialize FastAPI app
app = FastAPI(title="Hugging Face Model Server")

# Model loading
print(f"Loading model {{MODEL_ID}}...")

# Configure device map and quantization
device_map = "auto"
load_in_8bit = False
load_in_4bit = False

if QUANTIZE:
    if torch.cuda.is_available():
        load_in_8bit = True
        print("Using 8-bit quantization")
    else:
        print("CUDA not available, quantization disabled")

# Configure max memory
kwargs = {{"device_map": device_map}}
if MAX_MEMORY:
    kwargs["max_memory"] = {{0: MAX_MEMORY}}

# Load tokenizer and model
tokenizer = AutoTokenizer.from_pretrained(MODEL_ID)
model = AutoModelForCausalLM.from_pretrained(
    MODEL_ID,
    load_in_8bit=load_in_8bit,
    load_in_4bit=load_in_4bit,
    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
    **kwargs
)

# Create pipeline
pipe = pipeline(
    "text-generation",
    model=model,
    tokenizer=tokenizer,
    max_new_tokens=512,
    do_sample=True,
    temperature=0.7,
    top_p=0.95,
)

# Load embedding model if specified
embedding_model = None
embedding_tokenizer = None
if EMBEDDING_MODEL_ID:
    try:
        print(f"Loading embedding model {{EMBEDDING_MODEL_ID}}...")
        from transformers import AutoModel

        # Load embedding model and tokenizer
        embedding_tokenizer = AutoTokenizer.from_pretrained(EMBEDDING_MODEL_ID)
        embedding_model = AutoModel.from_pretrained(
            EMBEDDING_MODEL_ID,
            torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
        )
        print(f"Embedding model loaded successfully")
    except Exception as e:
        print(f"Error loading embedding model: {{e}}")
        embedding_model = None
        embedding_tokenizer = None

# Request models
class GenerateRequest(BaseModel):
    inputs: str
    parameters: Optional[Dict[str, Any]] = None
    model: Optional[str] = None

class ChatRequest(BaseModel):
    inputs: List[Dict[str, str]]
    parameters: Optional[Dict[str, Any]] = None
    model: Optional[str] = None

@app.get("/")
def read_root():
    return {{"status": "ok", "model": MODEL_ID}}

@app.post("/generate")
def generate(request: GenerateRequest):
    try:
        # Extract parameters
        params = request.parameters or {{}}
        max_new_tokens = params.get("max_new_tokens", 512)
        temperature = params.get("temperature", 0.7)
        top_p = params.get("top_p", 0.95)
        do_sample = params.get("do_sample", True)

        # Generate text
        result = pipe(
            request.inputs,
            max_new_tokens=max_new_tokens,
            temperature=temperature,
            top_p=top_p,
            do_sample=do_sample,
        )

        # Format response
        generated_text = result[0]["generated_text"]

        # If return_full_text is False, remove the prompt
        if not params.get("return_full_text", True) and generated_text.startswith(request.inputs):
            generated_text = generated_text[len(request.inputs):].lstrip()

        return {{"generated_text": generated_text}}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/chat")
def chat(request: ChatRequest):
    try:
        # Extract parameters
        params = request.parameters or {{}}
        max_new_tokens = params.get("max_new_tokens", 512)
        temperature = params.get("temperature", 0.7)
        top_p = params.get("top_p", 0.95)
        do_sample = params.get("do_sample", True)

        # Format chat messages into a prompt
        prompt = ""
        for message in request.inputs:
            role = message.get("role", "user")
            content = message.get("content", "")

            if role == "system":
                prompt += f"<|system|>\\n{{content}}\\n"
            elif role == "user":
                prompt += f"<|user|>\\n{{content}}\\n"
            elif role == "assistant":
                prompt += f"<|assistant|>\\n{{content}}\\n"

        # Add final assistant prompt
        prompt += "<|assistant|>\\n"

        # Generate text
        result = pipe(
            prompt,
            max_new_tokens=max_new_tokens,
            temperature=temperature,
            top_p=top_p,
            do_sample=do_sample,
        )

        # Extract assistant response
        generated_text = result[0]["generated_text"]
        response = generated_text[len(prompt):].strip()

        return {{"response": response}}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/embeddings")
def embeddings(request: Dict[str, Any]):
    """Generate embeddings for text."""
    try:
        # Check if embedding model is available
        if not embedding_model or not embedding_tokenizer:
            raise HTTPException(
                status_code=501,
                detail="Embedding model not loaded. Please specify an embedding model when starting the server."
            )

        # Extract inputs
        inputs = request.get("inputs", [])
        if not inputs:
            raise HTTPException(status_code=400, detail="No inputs provided")

        # Ensure inputs is a list
        if isinstance(inputs, str):
            inputs = [inputs]

        # Generate embeddings for each input
        results = []
        for text in inputs:
            # Tokenize text
            encoded_input = embedding_tokenizer(
                text,
                padding=True,
                truncation=True,
                max_length=512,
                return_tensors='pt'
            )

            # Move to GPU if available
            if torch.cuda.is_available():
                encoded_input = {k: v.cuda() for k, v in encoded_input.items()}

            # Generate embeddings
            with torch.no_grad():
                model_output = embedding_model(**encoded_input)

                # Get the embeddings - use mean pooling of token embeddings
                attention_mask = encoded_input['attention_mask']
                token_embeddings = model_output[0]  # First element of model_output contains token embeddings

                # Mask padding tokens
                input_mask_expanded = attention_mask.unsqueeze(-1).expand(token_embeddings.size()).float()

                # Sum the masked embeddings
                sum_embeddings = torch.sum(token_embeddings * input_mask_expanded, 1)

                # Sum the number of non-padding tokens
                sum_mask = torch.clamp(input_mask_expanded.sum(1), min=1e-9)

                # Get the mean embeddings
                embeddings = sum_embeddings / sum_mask

                # Convert to list
                embedding_list = embeddings[0].cpu().numpy().tolist()
                results.append(embedding_list)

        return results

    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating embeddings: {str(e)}")

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=PORT)
""")

    return str(script_path)

def run_server(script_path: str):
    """
    Run the model server.

    Args:
        script_path (str): Path to the server script
    """
    try:
        # Run the server
        process = subprocess.Popen([sys.executable, script_path])

        # Wait for server to start
        print("Starting server...")
        time.sleep(5)

        print("\nServer is running!")
        print("Press Ctrl+C to stop the server")

        # Wait for user to stop the server
        process.wait()

    except KeyboardInterrupt:
        print("\nStopping server...")
        process.terminate()
        process.wait()
        print("Server stopped")

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Run a local Hugging Face model server")
    parser.add_argument("--model", default="mistralai/Mistral-7B-Instruct-v0.2", help="Hugging Face model ID")
    parser.add_argument("--port", type=int, default=8080, help="Port to run the server on")
    parser.add_argument("--quantize", action="store_true", help="Use quantization (8-bit)")
    parser.add_argument("--max-memory", help="Maximum memory to use (e.g., '8GiB')")
    parser.add_argument("--embedding-model", help="Hugging Face embedding model ID (e.g., 'sentence-transformers/all-MiniLM-L6-v2')")
    args = parser.parse_args()

    # Check requirements
    if not check_requirements():
        return 1

    # Create server script
    script_path = create_server_script(
        args.model,
        args.port,
        args.quantize,
        args.max_memory,
        args.embedding_model
    )

    # Run server
    run_server(script_path)

    return 0

if __name__ == "__main__":
    sys.exit(main())
