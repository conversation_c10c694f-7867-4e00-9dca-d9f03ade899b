"""
Send Email to Alyssa with App Password

This script sends an email to <PERSON><PERSON> using the provided app password.
"""
import os
import sys
import smtplib
import logging
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("send_email_to_alyssa.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("send_email_to_alyssa")

# Email configuration
SENDER_EMAIL = "<EMAIL>"
APP_PASSWORD = "fnkesylgdubokwyr"  # App password provided by user
RECIPIENT_EMAIL = "<EMAIL>"
EMAIL_SUBJECT = "IUL Policy and Health Insurance Options"

# Email template for Alyssa
EMAIL_TEMPLATE = """
Dear <PERSON><PERSON>,

Thank you for your interest in our insurance products. Based on your $100/month budget, I'd like to discuss some options for an Indexed Universal Life (IUL) policy structured for maximum cash value growth, along with basic health, dental, and vision plans.

Here's what I'm thinking:

1. IUL Policy: We can structure this for optimal cash value growth while maintaining the life insurance benefit. This would be approximately $60-70 of your monthly budget.

2. Health Insurance: For the remaining $30-40, we can look at basic health plans that cover essential services.

3. Dental & Vision: We have some affordable options that can be added if your budget allows, or we can discuss slightly exceeding your budget if these are priorities for you.

Would you be available for a quick call to discuss these options in more detail? I can answer any questions you might have and provide specific policy recommendations based on your needs.

Please let me know what days and times work best for you.

Best regards,
Paul Edwards
Flo Faction Insurance
Phone: (*************
Email: <EMAIL>
"""

def send_email():
    """Send an email to Alyssa using the app password."""
    logger.info(f"Sending email from {SENDER_EMAIL} to {RECIPIENT_EMAIL}...")
    
    try:
        # Create message
        message = MIMEMultipart()
        message["From"] = SENDER_EMAIL
        message["To"] = RECIPIENT_EMAIL
        message["Subject"] = EMAIL_SUBJECT
        
        # Add body
        message.attach(MIMEText(EMAIL_TEMPLATE, "plain"))
        
        # Connect to SMTP server
        with smtplib.SMTP("smtp.gmail.com", 587) as server:
            server.starttls()
            server.login(SENDER_EMAIL, APP_PASSWORD)
            server.send_message(message)
            
        logger.info("Email sent successfully")
        return True
    except Exception as e:
        logger.error(f"Error sending email: {e}")
        return False

def main():
    """Main entry point for the script."""
    print("Send Email to Alyssa with App Password")
    print("=====================================")
    print()
    
    # Print email details
    print("Email Details:")
    print(f"- From: {SENDER_EMAIL}")
    print(f"- To: {RECIPIENT_EMAIL}")
    print(f"- Subject: {EMAIL_SUBJECT}")
    print(f"- Content: {EMAIL_TEMPLATE[:50]}...")
    print()
    
    # Send email
    print("Sending email...")
    success = send_email()
    
    if success:
        print("✅ Email sent successfully to Alyssa!")
    else:
        print("❌ Failed to send email")
        return 1
        
    return 0

if __name__ == "__main__":
    sys.exit(main())
