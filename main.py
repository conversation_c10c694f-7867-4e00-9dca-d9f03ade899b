"""
Main entry point for the Multi-Agent AI System.

This is an advanced multi-agent system with capabilities for:
- Multiple specialized AI agents
- MPC (Multi-Party Computation) servers
- Quantum computing integration
- Advanced machine learning and reasoning
- Security tools integration
- Audio processing
- NVIDIA developer tools acceleration
"""
import asyncio
import signal
import sys
import logging
from datetime import datetime
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent))

from core.agent_manager import Agent<PERSON>anager
from core.agent_coordinator import Agent<PERSON>oordinator
from core.agent_integration import AgentIntegration
from core.logger import setup_logger
from core.state_manager import StateManager
from core.web_interface import start_web_interface
from core.advanced_memory import AdvancedMemory
from core.metrics_collector import MetricsCollector
from core.workflow_engine import WorkflowEngine
from llm.llm_router import LLMRouter
from services.security_tools_service import SecurityToolsService
from audio.audio_processor import AudioProcessor
from quantum_computing.quantum_connector import QuantumConnector
from machine_learning.advanced_reasoning import AdvancedReasoning
from nvidia_integration.integrate_with_agents import initialize_nvidia_tools, shutdown_nvidia_tools
from ui.workflow_visualizer import WorkflowVisualizer
from ui.agent_network_graph import AgentNetworkGraph
from ui.performance_dashboard import PerformanceDashboard
import config

# Set up logging with more verbose output
logger = setup_logger(level=logging.DEBUG)

# Global flag to control system shutdown
shutdown_event = asyncio.Event()

def handle_shutdown(sig, frame):
    """Handle shutdown signals gracefully."""
    logger.info(f"Received shutdown signal {sig}")
    shutdown_event.set()

# Register signal handlers
signal.signal(signal.SIGINT, handle_shutdown)
signal.signal(signal.SIGTERM, handle_shutdown)

async def main():
    """Main entry point for the system."""
    logger.info(f"Starting {config.SYSTEM_NAME} v{config.VERSION}")

    try:
        # Initialize NVIDIA tools if enabled
        nvidia_config_path = "config/nvidia_config.json"
        nvidia_initialized = False
        nvidia_config = getattr(config, "NVIDIA_CONFIG", {})
        if nvidia_config and nvidia_config.get("enabled", True):
            logger.info("Initializing NVIDIA developer tools")
            nvidia_initialized = await initialize_nvidia_tools(nvidia_config_path)
            if nvidia_initialized:
                logger.info("NVIDIA developer tools initialized successfully")
            else:
                logger.warning("NVIDIA developer tools initialization failed. Continuing without NVIDIA acceleration.")

        # Initialize state manager
        state_manager = StateManager()
        await state_manager.initialize()

        # Initialize LLM router
        llm_router = LLMRouter()
        await llm_router.initialize()

        # Initialize advanced components
        security_tools_service = None
        audio_processor = None
        quantum_connector = None
        advanced_reasoning = None
        metrics_collector = None
        workflow_visualizer = None
        agent_network_graph = None
        performance_dashboard = None

        # Initialize security tools service if enabled
        if config.TOOL_CONFIG.get("enabled", False):
            security_tools_service = SecurityToolsService(config.TOOL_CONFIG)
            await security_tools_service.initialize()

        # Initialize audio processor if enabled
        audio_config = getattr(config, "AUDIO_CONFIG", {})
        if audio_config and audio_config.get("enabled", False):
            audio_processor = AudioProcessor(audio_config)
            await audio_processor.initialize()

        # Initialize quantum connector if enabled
        quantum_config = getattr(config, "QUANTUM_CONFIG", {})
        if quantum_config and quantum_config.get("enabled", False):
            quantum_connector = QuantumConnector(quantum_config)
            await quantum_connector.initialize()

        # Initialize advanced reasoning
        advanced_reasoning = AdvancedReasoning(llm_router)
        await advanced_reasoning.initialize()

        # Initialize metrics collector
        metrics_collector = MetricsCollector(state_manager)
        await metrics_collector.start()

        # Initialize visualization components
        workflow_visualizer = WorkflowVisualizer(state_manager)
        agent_network_graph = AgentNetworkGraph(state_manager)
        performance_dashboard = PerformanceDashboard(state_manager, metrics_collector)

        # Initialize agent coordinator
        agent_coordinator = AgentCoordinator(state_manager, llm_router, shutdown_event)
        await agent_coordinator.initialize()

        # Initialize agent integration
        agent_integration = AgentIntegration(state_manager, llm_router, agent_coordinator, shutdown_event)
        await agent_integration.initialize()

        # Initialize agent manager
        agent_manager = AgentManager(state_manager, shutdown_event)

        # Initialize workflow engine
        workflow_engine = WorkflowEngine(agent_manager, state_manager)

        # Register services with agent manager
        agent_manager.register_service("llm_router", llm_router)
        agent_manager.register_service("agent_coordinator", agent_coordinator)
        agent_manager.register_service("agent_integration", agent_integration)
        agent_manager.register_service("workflow_engine", workflow_engine)

        # Register email agents
        from agents.email_agent import EmailAgent
        from agents.multi_account_email_agent import MultiAccountEmailAgent

        # Create email agent
        email_agent = EmailAgent(
            agent_id="email_agent",
            name="Email Agent",
            description="Handles email communications with reasoning capabilities"
        )

        # Create multi-account email agent
        multi_account_email_agent = MultiAccountEmailAgent(
            agent_id="multi_account_email_agent",
            name="Multi-Account Email Agent",
            description="Handles email communications across multiple accounts with reasoning capabilities"
        )

        # Register email agents with agent manager
        agent_manager.register_agent("email_agent", email_agent)
        agent_manager.register_agent("multi_account_email_agent", multi_account_email_agent)

        if security_tools_service:
            agent_manager.register_service("security_tools", security_tools_service)

        if audio_processor:
            agent_manager.register_service("audio_processor", audio_processor)

        if quantum_connector:
            agent_manager.register_service("quantum_connector", quantum_connector)

        if advanced_reasoning:
            agent_manager.register_service("advanced_reasoning", advanced_reasoning)

        if metrics_collector:
            agent_manager.register_service("metrics_collector", metrics_collector)

        if workflow_visualizer:
            agent_manager.register_service("workflow_visualizer", workflow_visualizer)

        if agent_network_graph:
            agent_manager.register_service("agent_network_graph", agent_network_graph)

        if performance_dashboard:
            agent_manager.register_service("performance_dashboard", performance_dashboard)

        # Register NVIDIA acceleration status
        agent_manager.register_metadata("nvidia_acceleration", nvidia_initialized)

        # Start web interface if enabled
        web_task = None
        if config.WEB_INTERFACE["enabled"]:
            web_task = asyncio.create_task(
                start_web_interface(
                    agent_manager,
                    state_manager,
                    host=config.WEB_INTERFACE["host"],
                    port=config.WEB_INTERFACE["port"],
                    debug=config.WEB_INTERFACE["debug"]
                )
            )

        # Start agent manager
        await agent_manager.start()

        # Trigger system startup event in workflow engine
        if 'workflow_engine' in agent_manager.services:
            logger.info("Triggering system startup event")
            workflow_engine = agent_manager.get_service("workflow_engine")
            await workflow_engine.trigger_event("system_startup", {
                "timestamp": datetime.now().isoformat()
            })

        # Wait for shutdown signal
        await shutdown_event.wait()

        # Shutdown procedures
        logger.info("Shutting down system...")

        # Shutdown metrics collector
        if metrics_collector:
            logger.info("Shutting down metrics collector")
            await metrics_collector.stop()

        # Shutdown workflow engine
        if 'workflow_engine' in agent_manager.services:
            logger.info("Shutting down workflow engine")
            workflow_engine = agent_manager.get_service("workflow_engine")
            await workflow_engine.shutdown()

        # Shutdown agent coordinator
        if agent_coordinator:
            logger.info("Shutting down agent coordinator")
            await agent_coordinator.shutdown()

        # Shutdown NVIDIA tools if initialized
        if nvidia_initialized:
            logger.info("Shutting down NVIDIA developer tools")
            await shutdown_nvidia_tools()

        # Shutdown agent manager
        logger.info("Shutting down agent manager")
        await agent_manager.stop()

        # Shutdown web interface
        if web_task:
            logger.info("Shutting down web interface")
            web_task.cancel()
            try:
                await web_task
            except asyncio.CancelledError:
                pass

        # Close state manager
        logger.info("Closing state manager")
        await state_manager.close()

    except Exception as e:
        logger.exception(f"Fatal error in main loop: {e}")
    finally:
        logger.info(f"{config.SYSTEM_NAME} shutdown complete")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nSystem shutdown by user")
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)
