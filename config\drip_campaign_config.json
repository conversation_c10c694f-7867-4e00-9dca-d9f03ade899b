{"enabled": true, "default_campaign": "insurance_lead", "campaigns": {"insurance_lead": {"name": "Insurance Lead Drip Campaign", "description": "Drip campaign for insurance leads with decreasing frequency", "initial_sequence": [{"type": "call", "delay_minutes": 0, "retry_count": 1, "retry_delay_minutes": 2, "template": "insurance_lead_call"}, {"type": "voicemail", "delay_minutes": 5, "condition": "if_no_answer", "template": "insurance_lead_voicemail"}, {"type": "text", "delay_minutes": 10, "template": "insurance_lead_text"}, {"type": "email", "delay_minutes": 15, "template": "insurance_lead_email"}], "follow_up_sequence": [{"type": "call", "delay_hours": 3, "retry_count": 1, "retry_delay_minutes": 2, "template": "insurance_lead_follow_up_call"}, {"type": "text", "delay_hours": 4, "template": "insurance_lead_follow_up_text"}, {"type": "email", "delay_hours": 6, "template": "insurance_lead_follow_up_email"}, {"type": "call", "delay_hours": 24, "retry_count": 1, "retry_delay_minutes": 2, "template": "insurance_lead_day2_call"}, {"type": "text", "delay_hours": 26, "template": "insurance_lead_day2_text"}, {"type": "email", "delay_hours": 48, "template": "insurance_lead_day3_email"}, {"type": "text", "delay_hours": 72, "template": "insurance_lead_day4_text"}, {"type": "call", "delay_hours": 96, "retry_count": 1, "retry_delay_minutes": 2, "template": "insurance_lead_day5_call"}, {"type": "email", "delay_hours": 120, "template": "insurance_lead_day6_email"}, {"type": "text", "delay_hours": 168, "template": "insurance_lead_day8_text"}, {"type": "email", "delay_hours": 240, "template": "insurance_lead_day11_email"}, {"type": "text", "delay_hours": 336, "template": "insurance_lead_day15_text_final"}], "stop_conditions": [{"type": "message_received", "content": "STOP", "case_sensitive": false}, {"type": "appointment_booked", "follow_up": "appointment_confirmation"}, {"type": "manual_stop", "by_name": true, "by_phone": true, "by_email": true}]}}, "templates": {"call": {"insurance_lead_call": {"script": "Hello {client_first_name}, this is <PERSON> from Flo Faction Insurance. I'm calling about your interest in {insurance_type} insurance. Is now a good time to talk about your insurance needs?", "voice_type": "female", "voice_name": "rachel"}, "insurance_lead_follow_up_call": {"script": "Hi {client_first_name}, it's <PERSON> from Flo Faction Insurance following up about your {insurance_type} insurance inquiry. I'd love to answer any questions you might have. Is now a good time to talk?", "voice_type": "female", "voice_name": "rachel"}, "insurance_lead_day2_call": {"script": "Hello {client_first_name}, it's <PERSON> from Flo Faction Insurance. I'm checking in about the {insurance_type} insurance options we discussed. Do you have any questions I can help with?", "voice_type": "female", "voice_name": "rachel"}, "insurance_lead_day5_call": {"script": "Hi {client_first_name}, it's <PERSON> from Flo Faction Insurance. I wanted to follow up on the {insurance_type} insurance options. I'm available to answer any questions you might have.", "voice_type": "female", "voice_name": "rachel"}}, "voicemail": {"insurance_lead_voicemail": {"script": "Hi {client_first_name}, this is <PERSON> from Flo Faction Insurance. I'm calling about your interest in {insurance_type} insurance. I'd love to discuss your options and answer any questions you might have. Please give me a call back at ************ when you have a moment. Thanks, and have a great day!", "voice_type": "female", "voice_name": "rachel"}}, "text": {"insurance_lead_text": {"message": "Hi {client_first_name}, this is <PERSON> from Flo Faction Insurance. I just tried to reach you about your {insurance_type} insurance inquiry. Please call me back at ************ or reply to this message to discuss your options. Thanks!"}, "insurance_lead_follow_up_text": {"message": "Hello {client_first_name}, it's <PERSON> from Flo Faction Insurance. I'm following up on your {insurance_type} insurance inquiry. I'd be happy to answer any questions you might have. Call me at ************ or reply to this message."}, "insurance_lead_day2_text": {"message": "Hey {client_first_name}! Just checking in to see if you had a chance to review the {insurance_type} insurance options. I'm especially excited about the plans that fit your budget of {budget}. Let me know if you have any questions! - Sandra"}, "insurance_lead_day4_text": {"message": "Hi {client_first_name}, <PERSON> from Flo Faction Insurance here. Just a friendly reminder that I'm available to discuss your {insurance_type} insurance options. Feel free to call or text me at ************."}, "insurance_lead_day8_text": {"message": "Hi {client_first_name}, I wanted to check in one more time about your {insurance_type} insurance options. I'm still available to help if you're interested. Call or text me at ************. - <PERSON>"}, "insurance_lead_day15_text_final": {"message": "Hi {client_first_name}, just wanted to check in one last time about those insurance options. I'm still available to help if you're interested. No pressure at all - just want to make sure you have what you need! - Sandra"}}, "email": {"insurance_lead_email": {"subject": "Your {insurance_type} Insurance Options - Flo Faction Insurance", "body": "Hi {client_first_name},\n\nThank you for your interest in {insurance_type} insurance. I tried to reach you by phone to discuss your options, but I wasn't able to connect with you.\n\nAt Flo Faction Insurance, we work with multiple carriers including UHC, Mutual of Omaha, Americo, Aetna, Cigna, and many others to find the best coverage for your needs and budget.\n\nBased on your budget of {budget}, I believe we can find a great {insurance_type} plan for you. I'd love to discuss your options in more detail.\n\nPlease feel free to call or text me at ************, or you can reply to this email.\n\nAlternatively, you can schedule a time to talk using my calendar link: https://calendly.com/flofaction-insurance/30min\n\nI look forward to helping you find the right insurance coverage!\n\nBest regards,\n<PERSON> Faction Insurance\n************\n<EMAIL>"}, "insurance_lead_follow_up_email": {"subject": "Following Up: Your {insurance_type} Insurance Options", "body": "Hi {client_first_name},\n\nI'm following up on your interest in {insurance_type} insurance. I wanted to make sure you received my previous message and to see if you have any questions about the insurance options available to you.\n\nWith your budget of {budget}, we can find a plan that provides the coverage you need without breaking the bank. I'd be happy to walk you through the options and help you make an informed decision.\n\nPlease feel free to call or text me at ************, or you can reply to this email.\n\nYou can also schedule a time to talk using my calendar link: https://calendly.com/flofaction-insurance/30min\n\nI'm here to help!\n\nBest regards,\n<PERSON>action Insurance\n************\n<EMAIL>"}, "insurance_lead_day3_email": {"subject": "Your {insurance_type} Insurance Quote", "body": "Hi {client_first_name},\n\nI hope this email finds you well. I wanted to follow up on your interest in {insurance_type} insurance.\n\nBased on the information you provided, I've put together some preliminary quotes that I believe would fit your needs and budget of {budget}. I'd love to discuss these options with you and answer any questions you might have.\n\nHere's a quick overview of what we can offer:\n\n1. Option A: Comprehensive coverage with [Carrier Name] - Includes [key benefits]\n2. Option B: Balanced coverage with [Carrier Name] - Includes [key benefits]\n3. Option C: Budget-friendly option with [Carrier Name] - Includes [key benefits]\n\nPlease feel free to call or text me at ************ to discuss these options in more detail, or you can schedule a time to talk using my calendar link: https://calendly.com/flofaction-insurance/30min\n\nI look forward to helping you find the right insurance coverage!\n\nBest regards,\nSandra\n<PERSON>lo Faction Insurance\n************\n<EMAIL>"}, "insurance_lead_day6_email": {"subject": "Still interested in {insurance_type} insurance?", "body": "Hi {client_first_name},\n\nI hope you're doing well. I wanted to check in one more time about your interest in {insurance_type} insurance.\n\nI understand that life gets busy, and finding the right insurance coverage can sometimes take a back seat to other priorities. However, having the right insurance protection is important, and I'd be happy to help you find a plan that fits your needs and budget.\n\nIf you're still interested, please feel free to call or text me at ************, or you can reply to this email. You can also schedule a time to talk using my calendar link: https://calendly.com/flofaction-insurance/30min\n\nIf now isn't the right time, no problem at all. Just keep my contact information handy for when you're ready to explore your options.\n\nBest regards,\n<PERSON> Faction Insurance\n************\n<EMAIL>"}, "insurance_lead_day11_email": {"subject": "Final follow-up: Your {insurance_type} insurance options", "body": "Hi {client_first_name},\n\nI hope this email finds you well. I'm reaching out one final time regarding your interest in {insurance_type} insurance.\n\nI want to assure you that Flo Faction Insurance is still here to help whenever you're ready to explore your insurance options. We work with multiple carriers to find the best coverage for your needs and budget.\n\nIf you're still interested in learning more about your options, please feel free to contact me at your convenience. You can call or text me at ************, reply to this email, or schedule a time to talk using my calendar link: https://calendly.com/flofaction-insurance/30min\n\nThank you for considering Flo Faction Insurance. I wish you all the best!\n\nBest regards,\nSandra\nFlo Faction Insurance\n************\n<EMAIL>"}}}, "settings": {"default_voice": "rachel", "default_from_email": "<EMAIL>", "default_from_phone": "7722089646", "default_email_signature": "Best regards,\nSandra\n<PERSON> Faction Insurance\n************\n<EMAIL>", "calendly_link": "https://calendly.com/flofaction-insurance/30min", "max_daily_contacts": 3, "working_hours": {"start_hour": 9, "end_hour": 18, "days": [1, 2, 3, 4, 5]}}}