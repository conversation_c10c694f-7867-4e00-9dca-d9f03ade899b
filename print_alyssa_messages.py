#!/usr/bin/env python3
"""
Print messages for <PERSON><PERSON>.
"""

import json
import os

def load_templates():
    """Load templates from the configuration file."""
    try:
        config_path = os.path.join("config", "communication_services.json")
        with open(config_path, "r", encoding="utf-8") as f:
            config = json.load(f)
            return config
    except UnicodeDecodeError:
        try:
            with open(config_path, "r", encoding="latin-1") as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading configuration with latin-1 encoding: {e}")
            return {}
    except Exception as e:
        print(f"Error loading configuration: {e}")
        return {}

def format_template(template, vars_dict):
    """Format a template with the given variables."""
    subject = template["subject"]
    body = template["body"].format(**vars_dict)
    return subject, body

def main():
    """Main function to print messages for <PERSON><PERSON>."""
    # Load templates
    config = load_templates()
    if not config:
        print("No configuration found. Exiting.")
        return

    email_templates = config.get("email_integration", {}).get("email_templates", {})
    text_templates = config.get("voice_calling_service", {}).get("text_templates", {})
    voicemail_templates = config.get("voice_calling_service", {}).get("voicemail_templates", {})

    # Client information
    client_info = {
        "client_name": "Alyssa Chirinos",
        "first_name": "Alyssa",
        "agent_name": "Sandra",
        "phone_number": "(*************",
        "dob": "8/16/97",
        "address": "Bradenton, Florida",
        "insurance_type": "IUL with Dental, Vision, and Basic Health",
        "estimated_premium": "$100/month",
        "notes": "Primary interest is IUL. Also interested in dental, vision, and basic private health coverage for checkups, physicals, and bloodwork. TOTAL BUDGET IS $100/MONTH. Need to check all carriers for best solution within budget."
    }

    # Print email messages
    print("\n===== EMAIL MESSAGES =====\n")
    
    if "new_client" in email_templates:
        subject, body = format_template(email_templates["new_client"], client_info)
        print(f"INITIAL EMAIL:")
        print(f"To: <EMAIL>")
        print(f"From: <EMAIL>")
        print(f"Subject: {subject}")
        print(f"Body:\n{body}\n")

    if "new_client_quote" in email_templates:
        subject, body = format_template(email_templates["new_client_quote"], client_info)
        print(f"QUOTE EMAIL:")
        print(f"To: <EMAIL>")
        print(f"From: <EMAIL>")
        print(f"Subject: {subject}")
        print(f"Body:\n{body}\n")

    # Print text message
    print("\n===== TEXT MESSAGE =====\n")
    
    if "new_client" in text_templates:
        message = text_templates["new_client"].format(**client_info)
        print(f"To: 9419294330")
        print(f"From: (*************")
        print(f"Message: {message}\n")

    # Print voicemail message
    print("\n===== VOICEMAIL MESSAGE =====\n")
    
    if "new_client" in voicemail_templates:
        message = voicemail_templates["new_client"].format(**client_info)
        print(f"To: 9419294330")
        print(f"From: (*************")
        print(f"Voice: ElevenLabs female voice")
        print(f"Message: {message}\n")

if __name__ == "__main__":
    main()
