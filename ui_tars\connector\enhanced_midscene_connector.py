"""
Enhanced Midscene Connector for the Multi-Agent AI System.

This module provides an improved connector to interface with Midscene.js,
with enhanced browser detection, connection reliability, and error recovery.
"""
import os
import sys
import json
import asyncio
import logging
import subprocess
import requests
import time
import platform
import psutil
import socket
import shutil
import tempfile
from typing import Dict, List, Optional, Any, Union, Tuple
from pathlib import Path

try:
    from core.logger import setup_logger
except ImportError:
    # Fallback logging setup if core.logger is not available
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler("midscene_connector.log")
        ]
    )

    def setup_logger(name):
        return logging.getLogger(name)

# Set up logger
logger = setup_logger("enhanced_midscene_connector")

class EnhancedMidsceneConnector:
    """
    Enhanced connector for Midscene.js.

    This class provides improved methods to interface with Midscene.js,
    with better browser detection, connection reliability, and error recovery.
    """

    def __init__(self,
                 api_url: Optional[str] = "http://localhost:8081",
                 api_key: Optional[str] = None,
                 model_name: Optional[str] = "UI-TARS-1.5-7B",
                 installation_path: Optional[str] = None,
                 browser_type: str = "chrome",
                 browser_path: Optional[str] = None,
                 max_retries: int = 3,
                 retry_delay: int = 5,
                 auto_start: bool = True,
                 auto_restart: bool = True,
                 android_enabled: bool = False):
        """
        Initialize the Enhanced Midscene connector.

        Args:
            api_url (Optional[str]): URL of the Midscene API
            api_key (Optional[str]): API key for Midscene
            model_name (Optional[str]): Name of the model to use
            installation_path (Optional[str]): Path to Midscene installation
            browser_type (str): Type of browser to use
            browser_path (Optional[str]): Path to browser executable
            max_retries (int): Maximum number of connection retries
            retry_delay (int): Delay between retries in seconds
            auto_start (bool): Whether to automatically start Midscene
            auto_restart (bool): Whether to automatically restart Midscene on failure
            android_enabled (bool): Whether to enable Android automation
        """
        self.api_url = api_url
        self.api_key = api_key
        self.model_name = model_name
        self.installation_path = installation_path
        self.browser_type = browser_type.lower()
        self.browser_path = browser_path
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.auto_start = auto_start
        self.auto_restart = auto_restart
        self.android_enabled = android_enabled
        self.midscene_version = "0.16.0"  # Latest version as of now

        self.session = None
        self.process = None
        self.is_running = False
        self.is_connected = False
        self.os_type = platform.system()  # 'Windows', 'Darwin' (macOS), or 'Linux'
        self.connection_lock = asyncio.Lock()
        self.last_error = None
        self.startup_attempts = 0
        self.max_startup_attempts = 3

    async def initialize(self):
        """Initialize the Enhanced Midscene connector with improved reliability."""
        logger.info("Initializing Enhanced Midscene connector")

        # Create a session for API requests
        self.session = requests.Session()
        if self.api_key:
            self.session.headers.update({"Authorization": f"Bearer {self.api_key}"})

        # Find Midscene installation if not provided
        if not self.installation_path:
            self.installation_path = await self._find_midscene_installation()
            if not self.installation_path:
                logger.warning("Midscene installation not found, attempting to install")
                install_success = await self._install_midscene()
                if not install_success:
                    logger.error("Failed to install Midscene")
                    return False

                # Try to find installation again
                self.installation_path = await self._find_midscene_installation()
                if not self.installation_path:
                    logger.error("Midscene installation not found after installation")
                    return False

        # Find browser if path not provided
        if not self.browser_path:
            self.browser_path = await self._find_browser()
            if not self.browser_path:
                logger.error(f"Browser '{self.browser_type}' not found")
                return False

        logger.info(f"Midscene installation: {self.installation_path}")
        logger.info(f"Browser: {self.browser_type} at {self.browser_path}")

        # Start Midscene if auto-start is enabled
        if self.auto_start:
            success = await self.start()
            if not success:
                logger.error("Failed to start Midscene")
                return False

        logger.info("Enhanced Midscene connector initialized successfully")
        return True

    async def _find_midscene_installation(self) -> Optional[str]:
        """Find Midscene installation path."""
        logger.info("Searching for Midscene installation")

        # Check common Node.js module locations
        if self.os_type == "Windows":
            possible_paths = [
                os.path.join(os.environ.get("APPDATA", ""), "npm", "node_modules", "@midscene"),
                os.path.join(os.environ.get("USERPROFILE", ""), "node_modules", "@midscene"),
                os.path.join(os.environ.get("PROGRAMFILES", ""), "nodejs", "node_modules", "@midscene"),
            ]
        elif self.os_type == "Darwin":  # macOS
            possible_paths = [
                os.path.expanduser("~/node_modules/@midscene"),
                "/usr/local/lib/node_modules/@midscene",
                "/opt/homebrew/lib/node_modules/@midscene",
            ]
        else:  # Linux
            possible_paths = [
                os.path.expanduser("~/node_modules/@midscene"),
                "/usr/local/lib/node_modules/@midscene",
                "/usr/lib/node_modules/@midscene",
            ]

        # Check if any of the paths exist
        for path in possible_paths:
            if os.path.exists(path):
                logger.info(f"Found Midscene at: {path}")
                return path

        # Try to find Midscene using npm
        try:
            result = subprocess.run(
                ["npm", "list", "-g", "@midscene/cli"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            if result.returncode == 0 and "@midscene/cli" in result.stdout:
                # Extract path from npm output
                for line in result.stdout.splitlines():
                    if "@midscene/cli" in line:
                        path = line.split("@midscene/cli")[0].strip()
                        if os.path.exists(path):
                            logger.info(f"Found Midscene at: {path}")
                            return path
        except:
            pass

        logger.warning("Midscene installation not found")
        return None

    async def _find_browser(self) -> Optional[str]:
        """Find browser executable path."""
        logger.info(f"Searching for {self.browser_type} browser")

        # Common browser paths
        if self.os_type == "Windows":
            browser_paths = {
                "chrome": [
                    os.path.join(os.environ.get("PROGRAMFILES", ""), "Google", "Chrome", "Application", "chrome.exe"),
                    os.path.join(os.environ.get("PROGRAMFILES(X86)", ""), "Google", "Chrome", "Application", "chrome.exe"),
                    os.path.join(os.environ.get("LOCALAPPDATA", ""), "Google", "Chrome", "Application", "chrome.exe")
                ],
                "edge": [
                    os.path.join(os.environ.get("PROGRAMFILES", ""), "Microsoft", "Edge", "Application", "msedge.exe"),
                    os.path.join(os.environ.get("PROGRAMFILES(X86)", ""), "Microsoft", "Edge", "Application", "msedge.exe")
                ],
                "firefox": [
                    os.path.join(os.environ.get("PROGRAMFILES", ""), "Mozilla Firefox", "firefox.exe"),
                    os.path.join(os.environ.get("PROGRAMFILES(X86)", ""), "Mozilla Firefox", "firefox.exe")
                ],
                "brave": [
                    os.path.join(os.environ.get("PROGRAMFILES", ""), "BraveSoftware", "Brave-Browser", "Application", "brave.exe"),
                    os.path.join(os.environ.get("LOCALAPPDATA", ""), "BraveSoftware", "Brave-Browser", "Application", "brave.exe")
                ]
            }
        elif self.os_type == "Darwin":  # macOS
            browser_paths = {
                "chrome": [
                    "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                    os.path.expanduser("~/Applications/Google Chrome.app/Contents/MacOS/Google Chrome")
                ],
                "edge": [
                    "/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge",
                    os.path.expanduser("~/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge")
                ],
                "firefox": [
                    "/Applications/Firefox.app/Contents/MacOS/firefox",
                    os.path.expanduser("~/Applications/Firefox.app/Contents/MacOS/firefox")
                ],
                "brave": [
                    "/Applications/Brave Browser.app/Contents/MacOS/Brave Browser",
                    os.path.expanduser("~/Applications/Brave Browser.app/Contents/MacOS/Brave Browser")
                ]
            }
        else:  # Linux
            browser_paths = {
                "chrome": [
                    "/usr/bin/google-chrome",
                    "/usr/bin/google-chrome-stable"
                ],
                "edge": [
                    "/usr/bin/microsoft-edge",
                    "/usr/bin/microsoft-edge-stable"
                ],
                "firefox": [
                    "/usr/bin/firefox"
                ],
                "brave": [
                    "/usr/bin/brave-browser",
                    "/usr/bin/brave"
                ]
            }

        # Check if browser type is supported
        if self.browser_type not in browser_paths:
            logger.error(f"Unsupported browser type: {self.browser_type}")
            return None

        # Check if any of the paths exist
        for path in browser_paths.get(self.browser_type, []):
            if os.path.exists(path):
                logger.info(f"Found {self.browser_type} at: {path}")
                return path

        logger.warning(f"{self.browser_type} browser not found")
        return None

    async def _install_midscene(self) -> bool:
        """Install Midscene.js with enhanced reliability."""
        logger.info("Installing Midscene.js")

        try:
            # Check if npm is installed
            npm_check = subprocess.run(
                ["npm", "--version"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            if npm_check.returncode != 0:
                logger.error("npm is not installed. Please install Node.js and npm first.")
                return False

            # Install Midscene packages
            packages = [
                "@midscene/web",
                "@midscene/android",
                "@midscene/core",
                "@midscene/cli"
            ]

            for package in packages:
                logger.info(f"Installing {package}...")
                result = subprocess.run(
                    ["npm", "install", "-g", f"{package}@{self.midscene_version}"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )

                if result.returncode != 0:
                    logger.error(f"Failed to install {package}: {result.stderr}")
                    return False

                logger.info(f"Successfully installed {package}")

            logger.info("Midscene installed successfully")
            return True

        except Exception as e:
            logger.exception(f"Error installing Midscene: {e}")
            return False

    async def start(self, url: Optional[str] = None) -> bool:
        """
        Start Midscene with enhanced reliability.

        Args:
            url (Optional[str]): URL to open in the browser

        Returns:
            bool: True if started successfully, False otherwise
        """
        async with self.connection_lock:
            if self.is_running:
                logger.info("Midscene is already running")
                return True

            logger.info("Starting Midscene with enhanced reliability")

            # Check if Midscene is already running
            if await self._check_midscene_running():
                logger.info("Midscene is already running (detected existing process)")
                self.is_running = True
                return await self._ensure_connection()

            # Start Midscene
            try:
                # Prepare the command
                command = ["npx", "@midscene/cli", "start", "--browser", self.browser_type]

                # Add URL if provided
                if url:
                    command.extend(["--url", url])

                # Add model if provided
                if self.model_name:
                    command.extend(["--model", self.model_name])

                # Start the process
                logger.info(f"Executing command: {' '.join(command)}")
                self.process = subprocess.Popen(
                    command,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )

                # Wait for the process to start
                await asyncio.sleep(5)

                # Check if process is still running
                if self.process.poll() is not None:
                    stderr = self.process.stderr.read() if self.process.stderr else ""
                    logger.error(f"Midscene process exited with code {self.process.returncode}: {stderr}")
                    return False

                self.is_running = True
                logger.info("Midscene started successfully")

                # Check connection
                connection_success = await self._ensure_connection()
                if not connection_success:
                    logger.error("Failed to connect to Midscene API")
                    if self.auto_restart and self.startup_attempts < self.max_startup_attempts:
                        self.startup_attempts += 1
                        logger.info(f"Attempting to restart Midscene (attempt {self.startup_attempts}/{self.max_startup_attempts})")
                        await self.stop()
                        await asyncio.sleep(2)
                        return await self.start(url)
                    return False

                self.startup_attempts = 0
                return True

            except Exception as e:
                logger.exception(f"Error starting Midscene: {e}")
                return False

    async def stop(self) -> bool:
        """
        Stop Midscene process.

        Returns:
            bool: True if stopped successfully, False otherwise
        """
        async with self.connection_lock:
            if not self.is_running:
                logger.info("Midscene is not running")
                return True

            logger.info("Stopping Midscene")

            try:
                if self.process:
                    logger.info("Terminating Midscene process")
                    self.process.terminate()
                    await asyncio.sleep(2)

                    # Force kill if still running
                    if self.process.poll() is None:
                        logger.info("Killing Midscene process")
                        self.process.kill()

                # Reset state
                self.is_running = False
                self.is_connected = False
                self.process = None

                logger.info("Midscene stopped")
                return True

            except Exception as e:
                logger.error(f"Error stopping Midscene: {e}")
                return False

    async def restart(self, url: Optional[str] = None) -> bool:
        """
        Restart Midscene with enhanced reliability.

        Args:
            url (Optional[str]): URL to open in the browser

        Returns:
            bool: True if restarted successfully, False otherwise
        """
        logger.info("Restarting Midscene")

        # Stop Midscene
        await self.stop()

        # Wait before restarting
        await asyncio.sleep(3)

        # Start Midscene
        return await self.start(url)

    async def execute_command(self, command: str, max_retries: int = 3) -> Dict:
        """
        Execute a command in Midscene with enhanced reliability.

        Args:
            command (str): Command to execute
            max_retries (int): Maximum number of retries

        Returns:
            Dict: Response from Midscene
        """
        # Ensure connection
        if not self.is_connected:
            connection_success = await self._ensure_connection()
            if not connection_success:
                # Try to restart Midscene if auto-restart is enabled
                if self.auto_restart:
                    logger.info("Attempting to restart Midscene")
                    restart_success = await self.restart()
                    if not restart_success:
                        return {"error": "Failed to connect to Midscene API and restart failed"}
                else:
                    return {"error": "Not connected to Midscene API"}

        logger.info(f"Executing command: {command}")

        # Track retries
        retries = 0
        last_error = None

        while retries <= max_retries:
            try:
                # Prepare the request
                data = {
                    "command": command,
                    "model": self.model_name
                }

                # Send the request
                response = self.session.post(f"{self.api_url}/execute", json=data, timeout=30)
                response.raise_for_status()

                # Parse the response
                result = response.json()

                # Check if the response indicates an error
                if "error" in result:
                    error_msg = result["error"]
                    logger.warning(f"Midscene returned an error: {error_msg}")

                    # If it's a connection error, try to reconnect
                    if "connection" in error_msg.lower() or "timeout" in error_msg.lower():
                        self.is_connected = False
                        connection_success = await self._ensure_connection()
                        if connection_success:
                            retries += 1
                            continue

                logger.info("Command executed successfully")
                return result

            except requests.exceptions.RequestException as e:
                last_error = str(e)
                logger.error(f"Error executing command (attempt {retries+1}/{max_retries+1}): {e}")

                # Try to reconnect
                self.is_connected = False
                connection_success = await self._ensure_connection()

                if not connection_success and self.auto_restart:
                    # Try to restart Midscene
                    logger.info("Attempting to restart Midscene")
                    restart_success = await self.restart()
                    if not restart_success:
                        logger.error("Failed to restart Midscene")

                retries += 1
                if retries <= max_retries:
                    # Wait before retrying
                    await asyncio.sleep(self.retry_delay)
                    logger.info(f"Retrying command (attempt {retries+1}/{max_retries+1})")

            except Exception as e:
                last_error = str(e)
                logger.exception(f"Unexpected error executing command (attempt {retries+1}/{max_retries+1}): {e}")
                retries += 1
                if retries <= max_retries:
                    # Wait before retrying
                    await asyncio.sleep(self.retry_delay)
                    logger.info(f"Retrying command (attempt {retries+1}/{max_retries+1})")

        # All retries failed
        return {"error": f"Failed to execute command after {max_retries+1} attempts: {last_error}"}

    async def _check_midscene_running(self) -> bool:
        """
        Check if Midscene is already running.

        Returns:
            bool: True if running, False otherwise
        """
        # Check if Midscene API is responding
        try:
            response = requests.get(f"{self.api_url}/health", timeout=2)
            if response.status_code == 200:
                logger.info("Midscene API is responding")
                return True
        except:
            pass

        # Check for Midscene process
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = proc.info.get('cmdline', [])
                if cmdline and '@midscene/cli' in ' '.join(cmdline):
                    logger.info(f"Found running Midscene process: {proc.info['pid']}")
                    return True
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass

        return False

    async def _ensure_connection(self) -> bool:
        """
        Ensure connection to Midscene API.

        Returns:
            bool: True if connected, False otherwise
        """
        logger.info("Ensuring connection to Midscene API")

        for attempt in range(self.max_retries):
            try:
                response = requests.get(f"{self.api_url}/health", timeout=5)
                if response.status_code == 200:
                    logger.info("Connected to Midscene API")
                    self.is_connected = True
                    return True

                logger.warning(f"Failed to connect to Midscene API (attempt {attempt+1}/{self.max_retries}): Status code {response.status_code}")
            except Exception as e:
                logger.warning(f"Failed to connect to Midscene API (attempt {attempt+1}/{self.max_retries}): {e}")

            # Wait before retrying
            await asyncio.sleep(self.retry_delay)

        logger.error(f"Failed to connect to Midscene API after {self.max_retries} attempts")
        self.is_connected = False
        return False

    async def health_check(self) -> Dict:
        """
        Perform a health check on the Midscene connector.

        Returns:
            Dict: Health check result
        """
        logger.info("Performing health check")

        result = {
            "status": "healthy",
            "issues": [],
            "components": {
                "midscene_api": False,
                "midscene_process": False,
                "browser_process": False,
                "npm_installation": False
            }
        }

        # Check Midscene API
        try:
            response = requests.get(f"{self.api_url}/health", timeout=5)
            if response.status_code == 200:
                result["components"]["midscene_api"] = True
            else:
                result["issues"].append(f"Midscene API returned status code {response.status_code}")
        except Exception as e:
            result["issues"].append(f"Midscene API not responding: {str(e)}")

        # Check Midscene process
        if self.process:
            if self.process.poll() is None:
                result["components"]["midscene_process"] = True
            else:
                result["issues"].append(f"Midscene process exited with code {self.process.returncode}")
        else:
            # Check if Midscene is running as a separate process
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = proc.info.get('cmdline', [])
                    if cmdline and '@midscene/cli' in ' '.join(cmdline):
                        result["components"]["midscene_process"] = True
                        break
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    pass

            if not result["components"]["midscene_process"]:
                result["issues"].append("Midscene process not found")

        # Check browser process
        browser_names = {
            "chrome": ["chrome", "chrome.exe"],
            "edge": ["msedge", "msedge.exe"],
            "firefox": ["firefox", "firefox.exe"],
            "brave": ["brave", "brave.exe"]
        }

        browser_name_patterns = browser_names.get(self.browser_type, [])

        for proc in psutil.process_iter(['pid', 'name']):
            try:
                for pattern in browser_name_patterns:
                    if pattern.lower() in proc.info['name'].lower():
                        result["components"]["browser_process"] = True
                        break
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass

        if not result["components"]["browser_process"]:
            result["issues"].append(f"{self.browser_type} browser process not found")

        # Check npm installation
        try:
            npm_check = subprocess.run(
                ["npm", "list", "-g", "@midscene/cli"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            if npm_check.returncode == 0 and "@midscene/cli" in npm_check.stdout:
                result["components"]["npm_installation"] = True
            else:
                result["issues"].append("Midscene npm packages not installed")
        except Exception as e:
            result["issues"].append(f"Error checking npm installation: {str(e)}")

        # Update overall status
        if result["issues"]:
            result["status"] = "unhealthy"

        return result

    async def auto_repair(self) -> Dict:
        """
        Attempt to automatically repair the Midscene connector.

        Returns:
            Dict: Repair result
        """
        logger.info("Attempting to auto-repair Midscene connector")

        # Perform health check
        health = await self.health_check()

        if health["status"] == "healthy":
            logger.info("Midscene connector is healthy, no repair needed")
            return {
                "success": True,
                "message": "Midscene connector is healthy, no repair needed",
                "actions_taken": []
            }

        actions_taken = []

        # Check npm installation
        if not health["components"]["npm_installation"]:
            logger.info("Midscene npm packages not installed, attempting to install")
            install_success = await self._install_midscene()
            if install_success:
                actions_taken.append("Installed Midscene npm packages")
            else:
                logger.error("Failed to install Midscene npm packages")
                return {
                    "success": False,
                    "message": "Failed to install Midscene npm packages",
                    "actions_taken": actions_taken
                }

        # Check Midscene process
        if not health["components"]["midscene_process"]:
            logger.info("Midscene process not running, attempting to start")

            # Stop any existing process first
            await self.stop()
            actions_taken.append("Stopped existing Midscene process")

            # Start Midscene
            start_success = await self.start()
            if start_success:
                actions_taken.append("Started Midscene process")
            else:
                logger.error("Failed to start Midscene process")
                return {
                    "success": False,
                    "message": "Failed to start Midscene process",
                    "actions_taken": actions_taken
                }

        # Check Midscene API
        if not health["components"]["midscene_api"]:
            logger.info("Midscene API not responding, attempting to restart")

            # Restart Midscene
            restart_success = await self.restart()
            if restart_success:
                actions_taken.append("Restarted Midscene")
            else:
                logger.error("Failed to restart Midscene")
                return {
                    "success": False,
                    "message": "Failed to restart Midscene",
                    "actions_taken": actions_taken
                }

        # Check browser process
        if not health["components"]["browser_process"]:
            logger.info(f"{self.browser_type} browser not running, attempting to restart Midscene")

            # Restart Midscene (which will start the browser)
            restart_success = await self.restart()
            if restart_success:
                actions_taken.append(f"Restarted Midscene with {self.browser_type} browser")
            else:
                logger.error(f"Failed to restart Midscene with {self.browser_type} browser")
                return {
                    "success": False,
                    "message": f"Failed to restart Midscene with {self.browser_type} browser",
                    "actions_taken": actions_taken
                }

        # Perform another health check
        health = await self.health_check()

        if health["status"] == "healthy":
            logger.info("Midscene connector repaired successfully")
            return {
                "success": True,
                "message": "Midscene connector repaired successfully",
                "actions_taken": actions_taken
            }
        else:
            logger.error(f"Failed to repair Midscene connector: {health['issues']}")
            return {
                "success": False,
                "message": f"Failed to repair Midscene connector: {health['issues']}",
                "actions_taken": actions_taken
            }
