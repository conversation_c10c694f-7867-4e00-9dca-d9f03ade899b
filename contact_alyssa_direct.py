"""
Contact Alyssa Directly

This script sends an email to <PERSON><PERSON> directly using SMTP,
without relying on UI-TARS or browser automation.
"""
import os
import sys
import smtplib
import logging
import argparse
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("contact_alyssa_direct.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("contact_alyssa_direct")

# Constants
EMAIL_SUBJECT = "IUL Policy and Health Insurance Options"
EMAIL_RECIPIENT = "<EMAIL>"  # Replace with <PERSON><PERSON>'s actual email if different

# Email template for Alyssa
EMAIL_TEMPLATE = """
Dear <PERSON><PERSON>,

Thank you for your interest in our insurance products. Based on your $100/month budget, I'd like to discuss some options for an Indexed Universal Life (IUL) policy structured for maximum cash value growth, along with basic health, dental, and vision plans.

Here's what I'm thinking:

1. IUL Policy: We can structure this for optimal cash value growth while maintaining the life insurance benefit. This would be approximately $60-70 of your monthly budget.

2. Health Insurance: For the remaining $30-40, we can look at basic health plans that cover essential services.

3. Dental & Vision: We have some affordable options that can be added if your budget allows, or we can discuss slightly exceeding your budget if these are priorities for you.

Would you be available for a quick call to discuss these options in more detail? I can answer any questions you might have and provide specific policy recommendations based on your needs.

Please let me know what days and times work best for you.

Best regards,
Paul Edwards
Flo Faction Insurance
Phone: (*************
Email: <EMAIL>
"""

def send_email(sender_email, sender_password, recipient_email, subject, body):
    """Send an email using SMTP."""
    logger.info(f"Sending email from {sender_email} to {recipient_email}...")
    
    try:
        # Create message
        message = MIMEMultipart()
        message["From"] = sender_email
        message["To"] = recipient_email
        message["Subject"] = subject
        
        # Add body
        message.attach(MIMEText(body, "plain"))
        
        # Connect to SMTP server
        with smtplib.SMTP("smtp.gmail.com", 587) as server:
            server.starttls()
            server.login(sender_email, sender_password)
            server.send_message(message)
            
        logger.info("Email sent successfully")
        return True
    except Exception as e:
        logger.error(f"Error sending email: {e}")
        return False

def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Contact Alyssa Directly")
    parser.add_argument("--email", type=str, default="<EMAIL>", help="Gmail email address")
    parser.add_argument("--password", type=str, default="GodisSoGood!777", help="Gmail password")
    parser.add_argument("--recipient", type=str, default=EMAIL_RECIPIENT, help="Recipient email address")
    parser.add_argument("--subject", type=str, default=EMAIL_SUBJECT, help="Email subject")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    parser.add_argument("--dry-run", action="store_true", help="Don't actually send the email")
    
    args = parser.parse_args()
    
    # Set log level
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        
    print("Contact Alyssa Directly")
    print("======================")
    print()
    
    # Print email details
    print("Email Details:")
    print(f"- From: {args.email}")
    print(f"- To: {args.recipient}")
    print(f"- Subject: {args.subject}")
    print(f"- Content: {EMAIL_TEMPLATE[:50]}...")
    print()
    
    # Send email if not dry run
    if not args.dry_run:
        print("Sending email...")
        success = send_email(args.email, args.password, args.recipient, args.subject, EMAIL_TEMPLATE)
        
        if success:
            print("✅ Email sent successfully")
        else:
            print("❌ Failed to send email")
            return 1
    else:
        print("ℹ️ Dry run - email not sent")
        
    return 0

if __name__ == "__main__":
    sys.exit(main())
