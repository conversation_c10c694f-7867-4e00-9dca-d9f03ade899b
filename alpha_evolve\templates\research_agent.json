{
  "name": "Research Agent Enhancement",
  "description": "Template for enhancing research agent capabilities",
  "template": "You are tasked with enhancing the {capability} capability of a research agent.

The goal is to optimize for {optimization_metric}.

The research agent handles various research tasks including web search, content scraping, information summarization, topic monitoring, and technical documentation.

Requirements:
1. The implementation must find relevant and accurate information efficiently
2. It must extract and structure information in a useful format
3. It should summarize complex information clearly and concisely
4. It must respect website terms of service and rate limits
5. It should evaluate the credibility and reliability of sources

Your solution should be implemented as a Python function that follows this interface:
{interface}

Focus on creating a solution that maximizes {optimization_metric} while maintaining information accuracy and ethical research practices.",
  "variables": ["capability", "optimization_metric", "interface"]
}
