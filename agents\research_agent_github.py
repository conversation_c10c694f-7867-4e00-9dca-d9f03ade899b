"""
GitHub-related methods for the Research Agent.
"""
import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any
import json
import re
import uuid
import hashlib
import os

from core.logger import setup_logger

async def _handle_github_search(self, task: Dict):
    """
    Handle a GitHub search task.
    
    Args:
        task (Dict): Task data
    """
    query = task.get("query")
    language = task.get("language")
    search_type = task.get("search_type", "code")  # code, repositories, users
    
    # Check if GitHub service is available
    if not self.github_service or not self.github_service.is_enabled():
        raise ValueError("GitHub service is not available")
    
    # Perform search based on type
    if search_type == "code":
        search_results = await self.github_service.search_code(query, language)
    elif search_type == "repositories":
        search_results = await self.github_service.search_repositories(query, language)
    else:
        raise ValueError(f"Unsupported search type: {search_type}")
    
    # Check for errors
    if "error" in search_results:
        raise ValueError(f"GitHub search error: {search_results['error']}")
    
    # Store search results in task
    task["results"] = search_results
    
    # Add to search history
    search_id = str(uuid.uuid4())
    self.search_history[search_id] = {
        "id": search_id,
        "type": "github",
        "search_type": search_type,
        "query": query,
        "language": language,
        "timestamp": datetime.now().isoformat(),
        "results": search_results,
    }
    
    # If requested, analyze top results
    if task.get("analyze_results", False):
        if search_type == "code":
            # Analyze top code results
            for item in search_results.get("items", [])[:3]:  # Limit to top 3
                repo_name = item.get("repository", {}).get("full_name")
                path = item.get("path")
                
                if repo_name and path:
                    analysis_task_id = f"TASK-GITHUB-CODE-{datetime.now().strftime('%Y%m%d%H%M%S')}"
                    
                    analysis_task = {
                        "task_id": analysis_task_id,
                        "type": "github_code_analysis",
                        "repo": repo_name,
                        "path": path,
                        "parent_task_id": task.get("task_id"),
                        "created_at": datetime.now().isoformat(),
                        "status": "pending",
                    }
                    
                    # Add task to pending tasks
                    pending_tasks = await self.state_manager.get_state("research", "pending_tasks") or {}
                    pending_tasks[analysis_task_id] = analysis_task
                    await self.state_manager.update_state("research", "pending_tasks", pending_tasks)
        
        elif search_type == "repositories":
            # Analyze top repositories
            for item in search_results.get("items", [])[:2]:  # Limit to top 2
                repo_name = item.get("full_name")
                
                if repo_name:
                    repo_task_id = f"TASK-GITHUB-REPO-{datetime.now().strftime('%Y%m%d%H%M%S')}"
                    
                    repo_task = {
                        "task_id": repo_task_id,
                        "type": "github_repository",
                        "repo": repo_name,
                        "parent_task_id": task.get("task_id"),
                        "created_at": datetime.now().isoformat(),
                        "status": "pending",
                    }
                    
                    # Add task to pending tasks
                    pending_tasks = await self.state_manager.get_state("research", "pending_tasks") or {}
                    pending_tasks[repo_task_id] = repo_task
                    await self.state_manager.update_state("research", "pending_tasks", pending_tasks)

async def _handle_github_repository(self, task: Dict):
    """
    Handle a GitHub repository task.
    
    Args:
        task (Dict): Task data
    """
    repo = task.get("repo")  # Format: "owner/repo"
    
    # Check if GitHub service is available
    if not self.github_service or not self.github_service.is_enabled():
        raise ValueError("GitHub service is not available")
    
    # Split repo into owner and name
    parts = repo.split("/")
    if len(parts) != 2:
        raise ValueError(f"Invalid repository format: {repo}")
    
    owner, repo_name = parts
    
    # Get repository information
    repo_info = await self.github_service.get_repository(owner, repo_name)
    
    # Check for errors
    if "error" in repo_info:
        raise ValueError(f"GitHub repository error: {repo_info['error']}")
    
    # Get repository structure
    structure = await self.github_service.get_repository_structure(owner, repo_name)
    
    # Store repository information in task
    task["repository_info"] = repo_info
    task["repository_structure"] = structure
    
    # Add to GitHub repositories
    repo_id = hashlib.md5(repo.encode()).hexdigest()
    self.github_repositories[repo_id] = {
        "id": repo_id,
        "full_name": repo,
        "owner": owner,
        "name": repo_name,
        "info": repo_info,
        "structure": structure,
        "analyzed_at": datetime.now().isoformat(),
    }
    
    # Generate repository summary using LLM
    prompt = f"""
    You are a technical researcher analyzing a GitHub repository. Please provide a comprehensive summary of the following repository:
    
    Repository: {repo}
    Description: {repo_info.get('description', 'No description')}
    Language: {repo_info.get('language', 'Unknown')}
    Stars: {repo_info.get('stargazers_count', 0)}
    Forks: {repo_info.get('forks_count', 0)}
    Open Issues: {repo_info.get('open_issues_count', 0)}
    Created: {repo_info.get('created_at', '')}
    Last Updated: {repo_info.get('updated_at', '')}
    
    Please include:
    1. A brief overview of the repository's purpose
    2. Key features and capabilities
    3. Technical architecture (based on the repository structure)
    4. Potential use cases
    5. Notable strengths or limitations
    
    Keep your summary factual, technical, and well-organized.
    """
    
    response = await self.llm_router.generate_text(
        prompt=prompt,
        provider=self.llm_provider,
        max_tokens=1000,
        temperature=0.7
    )
    
    # Store summary in task and repository
    summary = response.get("text", "")
    task["summary"] = summary
    self.github_repositories[repo_id]["summary"] = summary
    
    # If this is part of a parent task, update the parent
    parent_task_id = task.get("parent_task_id")
    if parent_task_id:
        pending_tasks = await self.state_manager.get_state("research", "pending_tasks") or {}
        if parent_task_id in pending_tasks:
            parent_task = pending_tasks[parent_task_id]
            if "github_repositories" not in parent_task:
                parent_task["github_repositories"] = []
            
            parent_task["github_repositories"].append({
                "id": repo_id,
                "full_name": repo,
                "summary": summary,
            })
            
            pending_tasks[parent_task_id] = parent_task
            await self.state_manager.update_state("research", "pending_tasks", pending_tasks)

async def _handle_github_code_analysis(self, task: Dict):
    """
    Handle a GitHub code analysis task.
    
    Args:
        task (Dict): Task data
    """
    repo = task.get("repo")  # Format: "owner/repo"
    path = task.get("path")
    
    # Check if GitHub service is available
    if not self.github_service or not self.github_service.is_enabled():
        raise ValueError("GitHub service is not available")
    
    # Split repo into owner and name
    parts = repo.split("/")
    if len(parts) != 2:
        raise ValueError(f"Invalid repository format: {repo}")
    
    owner, repo_name = parts
    
    # Get file content
    file_content = await self.github_service.get_file_content(owner, repo_name, path)
    
    # Check for errors
    if "error" in file_content:
        raise ValueError(f"GitHub file error: {file_content['error']}")
    
    # Store file content in task
    task["file_content"] = file_content
    
    # Generate code analysis using LLM
    prompt = f"""
    You are a code analyst reviewing a file from a GitHub repository. Please analyze the following code:
    
    Repository: {repo}
    File: {path}
    
    ```
    {file_content.get('content', '')}
    ```
    
    Please provide:
    1. A brief overview of what this code does
    2. Key functions, classes, or components
    3. Notable algorithms or techniques used
    4. Potential use cases
    5. Any potential issues, optimizations, or security concerns
    
    Keep your analysis technical, detailed, and focused on the code's functionality and quality.
    """
    
    response = await self.llm_router.generate_text(
        prompt=prompt,
        provider=self.llm_provider,
        max_tokens=1200,
        temperature=0.7
    )
    
    # Store analysis in task
    analysis = response.get("text", "")
    task["analysis"] = analysis
    
    # Add to knowledge base
    content_hash = hashlib.md5(f"{repo}:{path}".encode()).hexdigest()
    self.knowledge_base[content_hash] = {
        "id": content_hash,
        "type": "github_code",
        "repo": repo,
        "path": path,
        "content": file_content.get("content", ""),
        "analysis": analysis,
        "analyzed_at": datetime.now().isoformat(),
    }
    
    # If this is part of a parent task, update the parent
    parent_task_id = task.get("parent_task_id")
    if parent_task_id:
        pending_tasks = await self.state_manager.get_state("research", "pending_tasks") or {}
        if parent_task_id in pending_tasks:
            parent_task = pending_tasks[parent_task_id]
            if "code_analyses" not in parent_task:
                parent_task["code_analyses"] = []
            
            parent_task["code_analyses"].append({
                "id": content_hash,
                "repo": repo,
                "path": path,
                "analysis": analysis,
            })
            
            pending_tasks[parent_task_id] = parent_task
            await self.state_manager.update_state("research", "pending_tasks", pending_tasks)
