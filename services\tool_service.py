"""
Tool Service for managing external security tools.

This service provides a unified interface for installing, configuring,
and executing external security tools.
"""
import asyncio
import os
import json
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
import shutil
import logging
from abc import ABC, abstractmethod

from core.logger import setup_logger
from utils.tool_executor import ToolExecutor

# Set up logger
logger = setup_logger("services.tool_service")

class ToolService:
    """
    Service for managing external security tools.
    
    This service provides methods for installing, configuring, and
    executing external security tools.
    """
    
    def __init__(self, config: Dict):
        """
        Initialize the tool service.
        
        Args:
            config (Dict): Service configuration
        """
        self.config = config
        self.enabled = config.get("enabled", False)
        self.tools_dir = Path(config.get("tools_dir", "tools"))
        
        # Create tools directory if it doesn't exist
        self.tools_dir.mkdir(exist_ok=True)
        
        # Initialize tool executor
        self.tool_executor = ToolExecutor(self.tools_dir)
        
        # Initialize tool registry
        self.tool_registry = {}
        
        # Load tool registry
        self._load_tool_registry()
    
    def is_enabled(self) -> bool:
        """
        Check if the tool service is enabled.
        
        Returns:
            bool: True if enabled, False otherwise
        """
        return self.enabled
    
    def _load_tool_registry(self):
        """Load the tool registry from configuration."""
        # Get tool registry from config
        tool_registry = self.config.get("tool_registry", {})
        
        # Register tools
        for tool_name, tool_config in tool_registry.items():
            self.register_tool(tool_name, tool_config)
    
    def register_tool(self, tool_name: str, tool_config: Dict):
        """
        Register a tool with the service.
        
        Args:
            tool_name (str): Name of the tool
            tool_config (Dict): Tool configuration
        """
        logger.info(f"Registering tool: {tool_name}")
        
        # Add tool to registry
        self.tool_registry[tool_name] = tool_config
    
    async def install_tool(self, tool_name: str) -> bool:
        """
        Install a tool.
        
        Args:
            tool_name (str): Name of the tool
            
        Returns:
            bool: True if the tool was installed successfully, False otherwise
        """
        # Check if tool is registered
        if tool_name not in self.tool_registry:
            logger.error(f"Tool {tool_name} is not registered")
            return False
        
        # Get tool configuration
        tool_config = self.tool_registry[tool_name]
        
        # Install tool
        return await self.tool_executor.install_tool(
            tool_name, tool_config.get("install_method", "auto")
        )
    
    async def check_tool_installed(self, tool_name: str) -> bool:
        """
        Check if a tool is installed.
        
        Args:
            tool_name (str): Name of the tool
            
        Returns:
            bool: True if the tool is installed, False otherwise
        """
        # Check if tool is registered
        if tool_name not in self.tool_registry:
            logger.error(f"Tool {tool_name} is not registered")
            return False
        
        # Check if tool is installed
        return await self.tool_executor.check_tool_installed(tool_name)
    
    async def run_tool(
        self,
        tool_name: str,
        args: Optional[List[str]] = None,
        input_data: Optional[str] = None,
        timeout: Optional[float] = 300,
        parse_output: bool = True,
    ) -> Dict[str, Any]:
        """
        Run a tool.
        
        Args:
            tool_name (str): Name of the tool
            args (Optional[List[str]]): Command-line arguments
            input_data (Optional[str]): Input data to pass to the tool
            timeout (Optional[float]): Timeout in seconds
            parse_output (bool): Whether to parse the output
            
        Returns:
            Dict[str, Any]: Tool result
        """
        # Check if tool is registered
        if tool_name not in self.tool_registry:
            logger.error(f"Tool {tool_name} is not registered")
            return {"error": f"Tool {tool_name} is not registered"}
        
        # Get tool configuration
        tool_config = self.tool_registry[tool_name]
        
        # Check if tool is installed
        if not await self.check_tool_installed(tool_name):
            # Try to install the tool
            if not await self.install_tool(tool_name):
                logger.error(f"Tool {tool_name} is not installed and could not be installed")
                return {"error": f"Tool {tool_name} is not installed and could not be installed"}
        
        # Prepare command
        command = [tool_name]
        
        # Add arguments
        if args:
            command.extend(args)
        
        # Run command
        try:
            result = await self.tool_executor.run_command(
                command,
                timeout=timeout,
                check=False,
            )
            
            # Parse output if requested
            if parse_output:
                parsed_output = await self.tool_executor.parse_tool_output(
                    tool_name, result["stdout"]
                )
                result["parsed"] = parsed_output
            
            return result
        
        except Exception as e:
            logger.exception(f"Error running tool {tool_name}: {e}")
            return {"error": f"Error running tool {tool_name}: {str(e)}"}
    
    async def get_tool_help(self, tool_name: str) -> Dict[str, Any]:
        """
        Get help information for a tool.
        
        Args:
            tool_name (str): Name of the tool
            
        Returns:
            Dict[str, Any]: Tool help information
        """
        # Check if tool is registered
        if tool_name not in self.tool_registry:
            logger.error(f"Tool {tool_name} is not registered")
            return {"error": f"Tool {tool_name} is not registered"}
        
        # Get tool configuration
        tool_config = self.tool_registry[tool_name]
        
        # Check if tool is installed
        if not await self.check_tool_installed(tool_name):
            logger.warning(f"Tool {tool_name} is not installed")
            return {
                "tool": tool_name,
                "installed": False,
                "description": tool_config.get("description", ""),
            }
        
        # Run tool with help flag
        result = await self.run_tool(
            tool_name,
            args=["--help"],
            timeout=10,
            parse_output=False,
        )
        
        return {
            "tool": tool_name,
            "installed": True,
            "description": tool_config.get("description", ""),
            "help": result.get("stdout", ""),
        }
    
    async def list_tools(self) -> List[Dict[str, Any]]:
        """
        List all registered tools.
        
        Returns:
            List[Dict[str, Any]]: List of tools with their status
        """
        tools = []
        
        for tool_name, tool_config in self.tool_registry.items():
            # Check if tool is installed
            installed = await self.check_tool_installed(tool_name)
            
            tools.append({
                "name": tool_name,
                "description": tool_config.get("description", ""),
                "installed": installed,
                "category": tool_config.get("category", "general"),
            })
        
        return tools
