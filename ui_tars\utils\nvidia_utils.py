"""
NVIDIA Utilities for UI-TARS.

This module provides utility functions for NVIDIA acceleration with UI-TARS.
"""
import os
import sys
import json
import asyncio
import logging
import subprocess
from typing import Dict, List, Optional, Any, Union
import platform

from core.logger import setup_logger

# Set up logger
logger = setup_logger("ui_tars_nvidia_utils")

def check_nvidia_gpu() -> bool:
    """
    Check if an NVIDIA GPU is available.
    
    Returns:
        bool: True if an NVIDIA GPU is available, False otherwise
    """
    try:
        # Try to run nvidia-smi
        result = subprocess.run(
            ["nvidia-smi"], 
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Check if the command was successful
        if result.returncode == 0:
            logger.info("NVIDIA GPU detected")
            return True
        else:
            logger.info("No NVIDIA GPU detected")
            return False
    
    except Exception as e:
        logger.warning(f"Error checking for NVIDIA GPU: {e}")
        return False

def get_nvidia_gpu_info() -> Dict:
    """
    Get information about the NVIDIA GPU.
    
    Returns:
        Dict: Information about the NVIDIA GPU
    """
    try:
        # Try to run nvidia-smi
        result = subprocess.run(
            ["nvidia-smi", "--query-gpu=name,driver_version,memory.total,memory.used,temperature.gpu", "--format=csv,noheader"], 
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Check if the command was successful
        if result.returncode == 0:
            # Parse the output
            output = result.stdout.strip()
            parts = output.split(", ")
            
            if len(parts) >= 5:
                info = {
                    "name": parts[0],
                    "driver_version": parts[1],
                    "memory_total": parts[2],
                    "memory_used": parts[3],
                    "temperature": parts[4]
                }
                
                logger.info(f"NVIDIA GPU info: {info}")
                return info
            else:
                logger.warning(f"Unexpected output format from nvidia-smi: {output}")
                return {"error": "Unexpected output format"}
        else:
            logger.warning("Failed to get NVIDIA GPU info")
            return {"error": "No NVIDIA GPU detected"}
    
    except Exception as e:
        logger.exception(f"Error getting NVIDIA GPU info: {e}")
        return {"error": str(e)}

def setup_cuda_environment() -> bool:
    """
    Set up the CUDA environment for NVIDIA acceleration.
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Check if NVIDIA GPU is available
        if not check_nvidia_gpu():
            logger.warning("No NVIDIA GPU detected, cannot set up CUDA environment")
            return False
        
        # Set CUDA_VISIBLE_DEVICES to 0 (use the first GPU)
        os.environ["CUDA_VISIBLE_DEVICES"] = "0"
        
        # Set TF_FORCE_GPU_ALLOW_GROWTH to true (for TensorFlow)
        os.environ["TF_FORCE_GPU_ALLOW_GROWTH"] = "true"
        
        logger.info("CUDA environment set up successfully")
        return True
    
    except Exception as e:
        logger.exception(f"Error setting up CUDA environment: {e}")
        return False

def setup_tensorrt() -> bool:
    """
    Set up TensorRT for optimized inference.
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Check if NVIDIA GPU is available
        if not check_nvidia_gpu():
            logger.warning("No NVIDIA GPU detected, cannot set up TensorRT")
            return False
        
        # Check if TensorRT is installed
        try:
            import tensorrt
            logger.info(f"TensorRT version: {tensorrt.__version__}")
            return True
        except ImportError:
            logger.warning("TensorRT not installed")
            return False
    
    except Exception as e:
        logger.exception(f"Error setting up TensorRT: {e}")
        return False

def optimize_model_for_tensorrt(model_path: str, output_path: str) -> bool:
    """
    Optimize a model for TensorRT.
    
    Args:
        model_path (str): Path to the model
        output_path (str): Path to save the optimized model
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Check if TensorRT is installed
        try:
            import tensorrt
        except ImportError:
            logger.warning("TensorRT not installed, cannot optimize model")
            return False
        
        # Check if the model file exists
        if not os.path.exists(model_path):
            logger.warning(f"Model file not found: {model_path}")
            return False
        
        # Create the output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Run the TensorRT optimization command
        command = [
            "trtexec",
            f"--onnx={model_path}",
            f"--saveEngine={output_path}",
            "--fp16"
        ]
        
        result = subprocess.run(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Check if the command was successful
        if result.returncode == 0:
            logger.info(f"Model optimized successfully: {output_path}")
            return True
        else:
            logger.warning(f"Failed to optimize model: {result.stderr}")
            return False
    
    except Exception as e:
        logger.exception(f"Error optimizing model: {e}")
        return False
