# Gmail Integration Setup Instructions

This document provides step-by-step instructions for setting up Gmail integration with your AI Agent System for multiple Gmail accounts.

## Prerequisites

- Python 3.6 or higher
- Google account(s) with Gmail
- Access to Google Cloud Console

## Setup Process

### 1. Create a Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Note the project name and ID for future reference

### 2. Enable the Gmail API

1. In your Google Cloud project, go to [API Library](https://console.cloud.google.com/apis/library)
2. Search for "Gmail API"
3. Click on "Gmail API" in the results
4. Click "Enable"

### 3. Configure OAuth Consent Screen

1. Go to [OAuth consent screen](https://console.cloud.google.com/apis/credentials/consent)
2. Select "External" user type (unless you have a Google Workspace organization)
3. Fill in the required information:
   - App name: "AI Agent System" (or your preferred name)
   - User support email: Your email address
   - Developer contact information: Your email address
4. Click "Save and Continue"
5. Add the following scopes:
   - `https://www.googleapis.com/auth/gmail.readonly`
   - `https://www.googleapis.com/auth/gmail.send`
   - `https://www.googleapis.com/auth/gmail.compose`
   - `https://www.googleapis.com/auth/gmail.modify`
6. Click "Save and Continue"
7. Add your email addresses as test users (all Gmail accounts you want to integrate)
8. Click "Save and Continue"
9. Review your settings and click "Back to Dashboard"

### 4. Create OAuth 2.0 Client ID

1. Go to [Credentials](https://console.cloud.google.com/apis/credentials)
2. Click "Create Credentials" and select "OAuth client ID"
3. Application type: "Desktop application"
4. Name: "AI Agent System Desktop Client" (or your preferred name)
5. Click "Create"
6. Add the following Authorized redirect URIs:
   - `http://localhost:55253/`
   - `http://localhost:0/`
7. Click "Save"
8. Download the JSON file (this will be your credentials file)

### 5. Set Up Credentials for Each Gmail Account

Run the `setup_gmail_credentials.py` script to set up credentials for each Gmail account:

```
python setup_gmail_credentials.py
```

Follow the prompts to set up credentials for each account:
1. Select the account you want to set up (or select "All accounts")
2. Follow the instructions to save the credentials file
3. When prompted, provide the path to the downloaded credentials JSON file

### 6. Test Gmail Authentication

Run the `test_gmail_auth.py` script to test authentication for each Gmail account:

```
python test_gmail_auth.py
```

Follow the prompts to test authentication for each account:
1. Select the account you want to test (or select "All accounts")
2. The script will attempt to authenticate with Gmail
3. If successful, it will display the authenticated email address and list a few messages

### 7. Troubleshooting Authentication Issues

If you encounter authentication issues (such as 403 access_denied errors), run the `fix_gmail_auth.py` script:

```
python fix_gmail_auth.py
```

Follow the prompts to fix authentication for each account:
1. Select the account you want to fix (or select "All accounts")
2. Follow the instructions to update your OAuth 2.0 Client ID settings
3. Make sure the Gmail API is enabled for your project

## Common Issues and Solutions

### Error 403: access_denied

This error typically occurs when:
- The OAuth consent screen is not properly configured
- The requested scopes are not approved for your application
- The redirect URI is not authorized
- The user has not been added as a test user (for apps in testing mode)

Solution:
1. Make sure your OAuth consent screen is properly configured
2. Add all required scopes to your OAuth consent screen
3. Add the user's email address as a test user
4. Add `http://localhost:55253/` and `http://localhost:0/` to the authorized redirect URIs
5. Run the `fix_gmail_auth.py` script to fix authentication issues

### Token Refresh Errors

If you encounter token refresh errors, delete the token file and re-authenticate:

```
# Replace with your email address
rm credentials/gmail_your_email_at_gmail_dot_com_token.pickle
```

Then run the `test_gmail_auth.py` script again to generate a new token.

## Using Gmail in Your AI Agent System

Once you have set up and tested Gmail authentication for all your accounts, you can use the Gmail service in your AI Agent System:

- For a single Gmail account, use `GmailServiceFactory.create_service()`
- For multiple Gmail accounts, use `MultiAccountGmailServiceFactory.create_service()`

Example:

```python
from services.multi_account_gmail_service import MultiAccountGmailServiceFactory

# Create the multi-account Gmail service
gmail_service = MultiAccountGmailServiceFactory.create_service()

# Check which accounts are enabled
enabled_accounts = gmail_service.get_enabled_accounts()
print(f"Enabled accounts: {enabled_accounts}")

# Send an email from a specific account
result = await gmail_service.send_message_from_account(
    email="<EMAIL>",
    to="<EMAIL>",
    subject="Test Email",
    body="This is a test email sent from the AI Agent System."
)
```

## Additional Resources

- [Gmail API Documentation](https://developers.google.com/gmail/api/guides)
- [Google Cloud Console](https://console.cloud.google.com/)
- [OAuth 2.0 for Mobile & Desktop Apps](https://developers.google.com/identity/protocols/oauth2/native-app)
