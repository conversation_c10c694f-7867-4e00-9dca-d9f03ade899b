"""
Start Browser Automation with UI-TARS and Midscene.

This script starts the browser automation manager with both UI-TARS and Midscene.
"""
import os
import sys
import json
import asyncio
import logging
import time
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent))

try:
    from core.logger import setup_logger
    from ui_tars.browser_automation_manager import BrowserAutomationManager, AutomationProvider
except ImportError:
    # Fallback logging setup if core.logger is not available
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler("browser_automation.log")
        ]
    )

    def setup_logger(name):
        return logging.getLogger(name)

# Set up logger
logger = setup_logger("start_browser_automation")

async def load_config():
    """
    Load configuration from file.
    
    Returns:
        dict: Configuration dictionary
    """
    config_path = Path("ui_tars/config.json")
    if not config_path.exists():
        logger.error(f"Configuration file not found: {config_path}")
        return {}
    
    try:
        import json
        with open(config_path, "r") as f:
            config = json.load(f)
        
        logger.info(f"Configuration loaded from {config_path}")
        return config
    except Exception as e:
        logger.error(f"Error loading configuration: {e}")
        return {}

async def start_browser_automation(provider=AutomationProvider.AUTO):
    """
    Start browser automation with UI-TARS and Midscene.
    
    Args:
        provider (AutomationProvider): Preferred automation provider
        
    Returns:
        BrowserAutomationManager: Initialized manager
    """
    logger.info(f"Starting browser automation with provider: {provider.value}")
    
    # Load configuration
    config = await load_config()
    if not config:
        logger.error("Failed to load configuration")
        return None
    
    # Create browser automation manager
    manager = BrowserAutomationManager(
        config=config,
        provider=provider,
        auto_start=True,
        auto_restart=True,
        auto_fallback=True
    )
    
    # Initialize manager
    logger.info("Initializing browser automation manager")
    success = await manager.initialize()
    if not success:
        logger.error("Failed to initialize browser automation manager")
        return None
    
    # Check active provider
    logger.info(f"Active provider: {manager.active_provider.value}")
    
    # Perform health check
    logger.info("Performing health check")
    health = await manager.health_check()
    logger.info(f"Health check result: {health['status']}")
    
    if health["status"] == "unhealthy":
        logger.warning(f"Health check issues: {health['issues']}")
        
        # Try to repair
        logger.info("Attempting to repair")
        repair_result = await manager.auto_repair()
        logger.info(f"Repair result: {repair_result['success']}")
        
        if not repair_result["success"]:
            logger.error(f"Failed to repair: {repair_result['message']}")
            await manager.stop()
            return None
    
    logger.info("Browser automation manager started successfully")
    return manager

async def interactive_mode(manager):
    """
    Run interactive mode for browser automation.
    
    Args:
        manager (BrowserAutomationManager): Browser automation manager
    """
    print("\nBrowser Automation Interactive Mode")
    print("==================================")
    print("Type 'exit' to quit, 'help' for commands")
    print()
    
    while True:
        try:
            command = input("Command> ")
            
            if command.lower() in ["exit", "quit", "q"]:
                break
            
            if command.lower() == "help":
                print("\nAvailable commands:")
                print("  browse <url> - Browse to a URL")
                print("  screenshot - Take a screenshot")
                print("  click <text> - Click on element with text")
                print("  type <text> - Type text")
                print("  health - Perform health check")
                print("  repair - Attempt to repair")
                print("  restart - Restart browser automation")
                print("  provider - Show active provider")
                print("  switch - Switch provider")
                print("  exit - Exit interactive mode")
                print()
                continue
            
            if command.lower() == "health":
                health = await manager.health_check()
                print(f"Health check result: {health['status']}")
                if health["issues"]:
                    print("Issues:")
                    for issue in health["issues"]:
                        print(f"- {issue}")
                continue
            
            if command.lower() == "repair":
                repair_result = await manager.auto_repair()
                print(f"Repair result: {repair_result['success']}")
                print(f"Message: {repair_result['message']}")
                if repair_result["actions_taken"]:
                    print("Actions taken:")
                    for action in repair_result["actions_taken"]:
                        print(f"- {action}")
                continue
            
            if command.lower() == "restart":
                print("Restarting browser automation...")
                success = await manager.restart()
                print(f"Restart {'successful' if success else 'failed'}")
                continue
            
            if command.lower() == "provider":
                print(f"Active provider: {manager.active_provider.value}")
                continue
            
            if command.lower() == "switch":
                new_provider = AutomationProvider.MIDSCENE if manager.active_provider == AutomationProvider.UI_TARS else AutomationProvider.UI_TARS
                print(f"Switching to {new_provider.value}...")
                
                # Execute a simple command with the new provider
                result = await manager.execute_command("Browse to https://www.google.com", max_retries=1)
                
                if "error" in result:
                    print(f"Failed to switch provider: {result['error']}")
                else:
                    print(f"Switched to {manager.active_provider.value}")
                
                continue
            
            if command.lower().startswith("browse "):
                url = command[7:].strip()
                print(f"Browsing to {url}...")
                result = await manager.execute_command(f"Browse to {url}")
                print(f"Result: {result}")
                continue
            
            if command.lower() == "screenshot":
                print("Taking screenshot...")
                result = await manager.execute_command("Take a screenshot")
                print(f"Result: {result}")
                continue
            
            if command.lower().startswith("click "):
                text = command[6:].strip()
                print(f"Clicking on '{text}'...")
                result = await manager.execute_command(f"Click on '{text}'")
                print(f"Result: {result}")
                continue
            
            if command.lower().startswith("type "):
                text = command[5:].strip()
                print(f"Typing '{text}'...")
                result = await manager.execute_command(f"Type '{text}'")
                print(f"Result: {result}")
                continue
            
            # Execute command directly
            print(f"Executing command: {command}")
            result = await manager.execute_command(command)
            print(f"Result: {result}")
        
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"Error: {e}")
    
    print("Exiting interactive mode")

async def main():
    """Main entry point for the script."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Start Browser Automation")
    parser.add_argument("--provider", choices=["ui_tars", "midscene", "auto"], default="auto", help="Preferred automation provider")
    parser.add_argument("--interactive", action="store_true", help="Run in interactive mode")
    
    args = parser.parse_args()
    
    # Convert provider string to enum
    provider_map = {
        "ui_tars": AutomationProvider.UI_TARS,
        "midscene": AutomationProvider.MIDSCENE,
        "auto": AutomationProvider.AUTO
    }
    provider = provider_map[args.provider]
    
    print("Browser Automation")
    print("=================")
    print(f"Provider: {provider.value}")
    print()
    
    try:
        # Start browser automation
        manager = await start_browser_automation(provider)
        
        if not manager:
            print("Failed to start browser automation")
            return 1
        
        print(f"Browser automation started with {manager.active_provider.value}")
        
        if args.interactive:
            # Run interactive mode
            await interactive_mode(manager)
        else:
            # Wait for user to press Enter
            input("Press Enter to stop browser automation...")
        
        # Stop browser automation
        print("Stopping browser automation...")
        await manager.stop()
        print("Browser automation stopped")
        
        return 0
    
    except Exception as e:
        logger.exception(f"Error in main: {e}")
        print(f"Error: {e}")
        return 1

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nBrowser automation stopped")
        sys.exit(0)
