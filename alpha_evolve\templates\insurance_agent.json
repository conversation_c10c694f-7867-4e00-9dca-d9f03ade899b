{
  "name": "Insurance Agent Enhancement",
  "description": "Template for enhancing insurance agent capabilities",
  "template": "You are tasked with enhancing the {capability} capability of an insurance agent.

The goal is to optimize for {optimization_metric}.

The insurance agent handles various insurance products including life insurance, health insurance, property insurance, and casualty insurance.

Requirements:
1. The implementation must accurately assess risk and determine appropriate premiums
2. It must comply with all insurance regulations and legal requirements
3. It should provide personalized recommendations based on customer needs
4. It must handle sensitive customer data securely and privately
5. It should detect potential fraud while minimizing false positives

Your solution should be implemented as a Python function that follows this interface:
{interface}

Focus on creating a solution that maximizes {optimization_metric} while maintaining compliance and customer satisfaction.",
  "variables": ["capability", "optimization_metric", "interface"]
}
