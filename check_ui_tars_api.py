"""
Check if the UI-TARS API server is running.
"""
import os
import sys
import time
import logging
import requests
import subprocess
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("check_ui_tars_api")

def check_api_server(url="http://localhost:8080/v1/health", max_retries=5, retry_delay=2):
    """
    Check if the UI-TARS API server is running.
    
    Args:
        url (str): URL to check
        max_retries (int): Maximum number of retries
        retry_delay (int): Delay between retries in seconds
        
    Returns:
        bool: True if the server is running, False otherwise
    """
    logger.info(f"Checking if UI-TARS API server is running at {url}")
    
    for i in range(max_retries):
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                logger.info("UI-TARS API server is running")
                return True
            else:
                logger.warning(f"UI-TARS API server returned status code {response.status_code}")
        except requests.exceptions.RequestException as e:
            logger.warning(f"Failed to connect to UI-TARS API server: {e}")
        
        if i < max_retries - 1:
            logger.info(f"Retrying in {retry_delay} seconds...")
            time.sleep(retry_delay)
    
    logger.error("UI-TARS API server is not running")
    return False

def find_ui_tars_installation():
    """Find the UI-TARS installation directory."""
    # Check default locations
    possible_paths = [
        "C:\\Program Files\\UI-TARS",
        "C:\\Program Files (x86)\\UI-TARS",
        os.path.join(os.environ.get("LOCALAPPDATA", "C:\\Users\\<USER>\\AppData\\Local"), "UI-TARS"),
        os.path.join(os.environ.get("APPDATA", "C:\\Users\\<USER>\\AppData\\Roaming"), "UI-TARS")
    ]
    
    # Check if any of the paths exist
    for path in possible_paths:
        if os.path.exists(path):
            logger.info(f"Found UI-TARS installation at: {path}")
            return path
    
    logger.warning("Could not find UI-TARS installation path")
    return None

def start_ui_tars_api_server(installation_path):
    """
    Start the UI-TARS API server.
    
    Args:
        installation_path (str): Path to UI-TARS installation
        
    Returns:
        subprocess.Popen: Process object for the API server
    """
    if not installation_path:
        logger.error("UI-TARS installation path not provided")
        return None
    
    # Find API server executable
    api_server_exe = os.path.join(installation_path, "api-server.exe")
    if not os.path.exists(api_server_exe):
        # Try alternative names
        alternatives = [
            os.path.join(installation_path, "ui-tars-api.exe"),
            os.path.join(installation_path, "ui-tars-server.exe"),
            os.path.join(installation_path, "server.exe")
        ]
        
        for alt in alternatives:
            if os.path.exists(alt):
                api_server_exe = alt
                break
        
        if not os.path.exists(api_server_exe):
            logger.error(f"UI-TARS API server executable not found in {installation_path}")
            return None
    
    logger.info(f"Starting UI-TARS API server: {api_server_exe}")
    
    try:
        # Start API server
        process = subprocess.Popen([api_server_exe])
        
        # Wait for API server to start
        time.sleep(5)
        
        # Check if API server is running
        if check_api_server():
            logger.info("UI-TARS API server started successfully")
            return process
        else:
            logger.error("UI-TARS API server failed to start")
            process.terminate()
            return None
    
    except Exception as e:
        logger.exception(f"Error starting UI-TARS API server: {e}")
        return None

def main():
    """Main entry point."""
    logger.info("Checking UI-TARS API server")
    
    # Check if API server is already running
    if check_api_server():
        logger.info("UI-TARS API server is already running")
        return 0
    
    # Find UI-TARS installation
    installation_path = find_ui_tars_installation()
    if not installation_path:
        logger.error("UI-TARS installation not found")
        return 1
    
    # Start API server
    process = start_ui_tars_api_server(installation_path)
    if not process:
        logger.error("Failed to start UI-TARS API server")
        return 1
    
    logger.info("UI-TARS API server started successfully")
    
    # Keep the server running
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("Stopping UI-TARS API server")
        process.terminate()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
