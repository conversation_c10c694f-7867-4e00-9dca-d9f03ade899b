"""
AlphaEvolve Package.

This package provides evolutionary programming capabilities for the Multi-Agent AI System,
enabling the discovery and optimization of algorithms through evolutionary techniques
combined with large language models.
"""

from alpha_evolve.alpha_evolve_engine import AlphaEvolveEngine
from alpha_evolve.llm_code_generator import LLMCodeGenerator
from alpha_evolve.code_evaluator import CodeEvaluator
from alpha_evolve.evolutionary_optimizer import EvolutionaryOptimizer
from alpha_evolve.prompt_engineering import PromptManager

__all__ = [
    "AlphaEvolveEngine",
    "LLMCodeGenerator",
    "CodeEvaluator",
    "EvolutionaryOptimizer",
    "PromptManager",
]
