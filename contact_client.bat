@echo off
echo Contact Client using AI Agent System
echo =================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Check if required packages are installed
python -c "import requests" >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing required packages...
    pip install requests
)

REM Create directories
mkdir logs 2>nul
mkdir data 2>nul
mkdir config 2>nul
mkdir agents 2>nul

REM Ask for client name
echo.
echo Enter client name (default: Alyssa):
set /p CLIENT_NAME=""
if "%CLIENT_NAME%"=="" set CLIENT_NAME=Alyssa

REM Ask for template
echo.
echo Enter template name (default: iul_policy):
echo Available templates: initial_contact, follow_up, quote, appointment_confirmation, iul_policy
set /p TEMPLATE=""
if "%TEMPLATE%"=="" set TEMPLATE=iul_policy

REM Ask for agent name
echo.
echo Enter agent name (default: <PERSON>):
set /p AGENT_NAME=""
if "%AGENT_NAME%"=="" set AGENT_NAME=<PERSON> Edwards

REM Ask for agent email
echo.
echo Enter agent email (default: <EMAIL>):
set /p AGENT_EMAIL=""
if "%AGENT_EMAIL%"=="" set AGENT_EMAIL=<EMAIL>

REM Run the script
echo.
echo Running client contact script...
echo.

set COMMAND=python contact_client.py --client "%CLIENT_NAME%" --template "%TEMPLATE%" --agent-name "%AGENT_NAME%" --agent-email "%AGENT_EMAIL%" --debug

echo Executing: %COMMAND%
echo.

%COMMAND%

echo.
if %errorlevel% equ 0 (
    echo Client contact completed successfully!
) else (
    echo Failed to contact client. Please check the error messages above.
)

echo.
pause
