"""
Jarvis Interface Integration for AlphaEvolve.

This module provides integration between AlphaEvolve and the Jarvis Interface,
enabling command-line control of evolutionary optimization processes.
"""
import asyncio
import json
import logging
import os
import shlex
from typing import Dict, List, Optional, Any, Union, Tuple
import uuid
from datetime import datetime

# Add the project root to the Python path
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent.parent))

from core.logger import setup_logger
from borg_cluster.jarvis_interface import JarvisInterface
from alpha_evolve.alpha_evolve_engine import AlphaEvolveEngine
from alpha_evolve.integration.borg_integration import BorgIntegration
from alpha_evolve.integration.agent_integration import AgentIntegration

# Set up logger
logger = setup_logger("jarvis_integration")

class JarvisAlphaEvolveCommands:
    """
    Jarvis Interface Commands for AlphaEvolve.

    This class provides command handlers for AlphaEvolve in the Jarvis Interface.
    """

    def __init__(
        self,
        jarvis_interface: JarvisInterface,
        alpha_evolve_engine: Optional[AlphaEvolveEngine] = None,
        borg_integration: Optional[BorgIntegration] = None,
        agent_integration: Optional[AgentIntegration] = None,
    ):
        """
        Initialize the Jarvis AlphaEvolve Commands.

        Args:
            jarvis_interface (JarvisInterface): Jarvis interface
            alpha_evolve_engine (AlphaEvolveEngine, optional): AlphaEvolve engine
            borg_integration (BorgIntegration, optional): Borg integration
            agent_integration (AgentIntegration, optional): Agent integration
        """
        self.jarvis_interface = jarvis_interface
        self.alpha_evolve_engine = alpha_evolve_engine
        self.borg_integration = borg_integration
        self.agent_integration = agent_integration
        self.initialized = False
        
        # Command handlers
        self.command_handlers = {}
        
    async def initialize(self):
        """Initialize the Jarvis AlphaEvolve Commands."""
        logger.info("Initializing Jarvis AlphaEvolve Commands")
        
        # Initialize AlphaEvolve engine if not provided
        if not self.alpha_evolve_engine:
            from alpha_evolve.alpha_evolve_engine import AlphaEvolveEngine
            self.alpha_evolve_engine = AlphaEvolveEngine()
            await self.alpha_evolve_engine.initialize()
        
        # Initialize Borg integration if not provided
        if not self.borg_integration:
            from alpha_evolve.integration.borg_integration import BorgIntegration
            self.borg_integration = BorgIntegration(alpha_evolve_engine=self.alpha_evolve_engine)
            await self.borg_integration.initialize()
        
        # Initialize Agent integration if not provided
        if not self.agent_integration:
            from alpha_evolve.integration.agent_integration import AgentIntegration
            self.agent_integration = AgentIntegration(alpha_evolve_engine=self.alpha_evolve_engine)
            await self.agent_integration.initialize()
        
        # Register command handlers
        self._register_command_handlers()
        
        self.initialized = True
        logger.info("Jarvis AlphaEvolve Commands initialized")
    
    def _register_command_handlers(self):
        """Register command handlers with the Jarvis interface."""
        # Register AlphaEvolve commands
        self.jarvis_interface.register_command(
            "evolve",
            self.handle_evolve_command,
            "Run an evolutionary process",
            "evolve <problem_type> [options]",
            [
                {"name": "problem_type", "description": "Type of problem to evolve (optimization, agent_enhancement, algorithm_discovery)"},
                {"name": "--generations", "description": "Number of generations (default: 50)"},
                {"name": "--population-size", "description": "Population size (default: 20)"},
                {"name": "--fitness-threshold", "description": "Fitness threshold to stop evolution (default: 0.95)"},
                {"name": "--timeout", "description": "Timeout in seconds (default: 3600)"},
            ]
        )
        
        self.jarvis_interface.register_command(
            "optimize",
            self.handle_optimize_command,
            "Optimize an existing algorithm",
            "optimize <file_path> <problem_type> [options]",
            [
                {"name": "file_path", "description": "Path to file containing code to optimize"},
                {"name": "problem_type", "description": "Type of problem (optimization, agent_enhancement, algorithm_discovery)"},
                {"name": "--generations", "description": "Number of generations (default: 30)"},
                {"name": "--population-size", "description": "Population size (default: 15)"},
                {"name": "--fitness-threshold", "description": "Fitness threshold to stop evolution (default: 0.9)"},
            ]
        )
        
        self.jarvis_interface.register_command(
            "enhance-agent",
            self.handle_enhance_agent_command,
            "Enhance an agent's capabilities",
            "enhance-agent <agent_id> <capability> [options]",
            [
                {"name": "agent_id", "description": "Agent ID"},
                {"name": "capability", "description": "Capability to enhance"},
                {"name": "--optimization-metric", "description": "Metric to optimize (default: efficiency)"},
                {"name": "--generations", "description": "Number of generations (default: 40)"},
                {"name": "--population-size", "description": "Population size (default: 20)"},
            ]
        )
        
        self.jarvis_interface.register_command(
            "optimize-resources",
            self.handle_optimize_resources_command,
            "Optimize resource allocation",
            "optimize-resources [options]",
            [
                {"name": "--resource-types", "description": "Resource types to optimize (comma-separated)"},
                {"name": "--optimization-metric", "description": "Metric to optimize (default: utilization)"},
                {"name": "--generations", "description": "Number of generations (default: 50)"},
                {"name": "--population-size", "description": "Population size (default: 20)"},
            ]
        )
        
        self.jarvis_interface.register_command(
            "optimize-scheduling",
            self.handle_optimize_scheduling_command,
            "Optimize task scheduling",
            "optimize-scheduling [options]",
            [
                {"name": "--task-types", "description": "Task types to optimize (comma-separated)"},
                {"name": "--optimization-metric", "description": "Metric to optimize (default: throughput)"},
                {"name": "--generations", "description": "Number of generations (default: 50)"},
                {"name": "--population-size", "description": "Population size (default: 20)"},
            ]
        )
        
        self.jarvis_interface.register_command(
            "alpha-evolve-status",
            self.handle_status_command,
            "Check the status of evolutionary processes",
            "alpha-evolve-status [evolution_id]",
            [
                {"name": "evolution_id", "description": "Evolution ID (optional)"},
            ]
        )
        
        self.jarvis_interface.register_command(
            "apply-model",
            self.handle_apply_model_command,
            "Apply an evolved model",
            "apply-model <model_id> <model_type>",
            [
                {"name": "model_id", "description": "Model ID"},
                {"name": "model_type", "description": "Model type (resource_allocation, task_scheduling, agent_capability)"},
            ]
        )
        
        logger.info("Registered AlphaEvolve commands with Jarvis interface")
    
    async def handle_evolve_command(self, args: List[str]) -> str:
        """
        Handle the 'evolve' command.
        
        Args:
            args (List[str]): Command arguments
            
        Returns:
            str: Command output
        """
        if not self.initialized:
            await self.initialize()
        
        # Parse arguments
        if not args:
            return "Error: Missing problem_type argument"
        
        problem_type = args[0]
        
        # Parse options
        generations = 50
        population_size = 20
        fitness_threshold = 0.95
        timeout = 3600
        
        i = 1
        while i < len(args):
            if args[i] == "--generations" and i + 1 < len(args):
                try:
                    generations = int(args[i + 1])
                    i += 2
                except ValueError:
                    return f"Error: Invalid generations value: {args[i + 1]}"
            elif args[i] == "--population-size" and i + 1 < len(args):
                try:
                    population_size = int(args[i + 1])
                    i += 2
                except ValueError:
                    return f"Error: Invalid population-size value: {args[i + 1]}"
            elif args[i] == "--fitness-threshold" and i + 1 < len(args):
                try:
                    fitness_threshold = float(args[i + 1])
                    i += 2
                except ValueError:
                    return f"Error: Invalid fitness-threshold value: {args[i + 1]}"
            elif args[i] == "--timeout" and i + 1 < len(args):
                try:
                    timeout = int(args[i + 1])
                    i += 2
                except ValueError:
                    return f"Error: Invalid timeout value: {args[i + 1]}"
            else:
                i += 1
        
        # Create problem definition
        problem = {
            "type": problem_type,
            "objective": f"Solve a {problem_type} problem",
        }
        
        # Run evolution
        try:
            result = await self.alpha_evolve_engine.evolve(
                problem=problem,
                generations=generations,
                population_size=population_size,
                fitness_threshold=fitness_threshold,
                timeout=timeout,
            )
            
            # Format result
            output = f"Evolution completed with status: {result['status']}\n"
            output += f"Evolution ID: {result['evolution_id']}\n"
            output += f"Generations: {result['generations']}\n"
            output += f"Best fitness: {result['best_fitness']}\n"
            
            if result.get("best_solution"):
                output += f"\nBest solution found:\n"
                output += f"```python\n{result['best_solution']['code']}\n```\n"
            
            return output
        
        except Exception as e:
            logger.exception(f"Error handling evolve command: {e}")
            return f"Error: {str(e)}"
    
    async def handle_optimize_command(self, args: List[str]) -> str:
        """
        Handle the 'optimize' command.
        
        Args:
            args (List[str]): Command arguments
            
        Returns:
            str: Command output
        """
        if not self.initialized:
            await self.initialize()
        
        # Parse arguments
        if len(args) < 2:
            return "Error: Missing file_path or problem_type argument"
        
        file_path = args[0]
        problem_type = args[1]
        
        # Parse options
        generations = 30
        population_size = 15
        fitness_threshold = 0.9
        
        i = 2
        while i < len(args):
            if args[i] == "--generations" and i + 1 < len(args):
                try:
                    generations = int(args[i + 1])
                    i += 2
                except ValueError:
                    return f"Error: Invalid generations value: {args[i + 1]}"
            elif args[i] == "--population-size" and i + 1 < len(args):
                try:
                    population_size = int(args[i + 1])
                    i += 2
                except ValueError:
                    return f"Error: Invalid population-size value: {args[i + 1]}"
            elif args[i] == "--fitness-threshold" and i + 1 < len(args):
                try:
                    fitness_threshold = float(args[i + 1])
                    i += 2
                except ValueError:
                    return f"Error: Invalid fitness-threshold value: {args[i + 1]}"
            else:
                i += 1
        
        # Read code from file
        try:
            with open(file_path, "r") as f:
                code = f.read()
        except Exception as e:
            return f"Error reading file: {str(e)}"
        
        # Create problem definition
        problem = {
            "type": problem_type,
            "objective": f"Optimize code for {problem_type}",
        }
        
        # Run optimization
        try:
            result = await self.alpha_evolve_engine.optimize_existing_code(
                code=code,
                problem=problem,
                generations=generations,
                population_size=population_size,
                fitness_threshold=fitness_threshold,
            )
            
            # Format result
            output = f"Optimization completed with status: {result['status']}\n"
            output += f"Evolution ID: {result['evolution_id']}\n"
            output += f"Generations: {result['generations']}\n"
            output += f"Best fitness: {result['best_fitness']}\n"
            
            if result.get("best_solution"):
                output += f"\nOptimized solution:\n"
                output += f"```python\n{result['best_solution']['code']}\n```\n"
            
            return output
        
        except Exception as e:
            logger.exception(f"Error handling optimize command: {e}")
            return f"Error: {str(e)}"
    
    async def handle_enhance_agent_command(self, args: List[str]) -> str:
        """
        Handle the 'enhance-agent' command.
        
        Args:
            args (List[str]): Command arguments
            
        Returns:
            str: Command output
        """
        if not self.initialized:
            await self.initialize()
        
        # Parse arguments
        if len(args) < 2:
            return "Error: Missing agent_id or capability argument"
        
        agent_id = args[0]
        capability = args[1]
        
        # Parse options
        optimization_metric = "efficiency"
        generations = 40
        population_size = 20
        
        i = 2
        while i < len(args):
            if args[i] == "--optimization-metric" and i + 1 < len(args):
                optimization_metric = args[i + 1]
                i += 2
            elif args[i] == "--generations" and i + 1 < len(args):
                try:
                    generations = int(args[i + 1])
                    i += 2
                except ValueError:
                    return f"Error: Invalid generations value: {args[i + 1]}"
            elif args[i] == "--population-size" and i + 1 < len(args):
                try:
                    population_size = int(args[i + 1])
                    i += 2
                except ValueError:
                    return f"Error: Invalid population-size value: {args[i + 1]}"
            else:
                i += 1
        
        # Run agent enhancement
        try:
            result = await self.agent_integration.enhance_agent_capability(
                agent_id=agent_id,
                capability=capability,
                optimization_metric=optimization_metric,
                generations=generations,
                population_size=population_size,
            )
            
            # Format result
            output = f"Agent enhancement completed with status: {result['status']}\n"
            output += f"Agent ID: {agent_id}\n"
            output += f"Capability: {capability}\n"
            output += f"Optimization metric: {optimization_metric}\n"
            
            if result.get("evolution_result"):
                evolution_result = result["evolution_result"]
                output += f"Evolution ID: {evolution_result['evolution_id']}\n"
                output += f"Generations: {evolution_result['generations']}\n"
                output += f"Best fitness: {evolution_result['best_fitness']}\n"
                
                if evolution_result.get("best_solution"):
                    output += f"\nEnhanced capability implementation:\n"
                    output += f"```python\n{evolution_result['best_solution']['code']}\n```\n"
            
            return output
        
        except Exception as e:
            logger.exception(f"Error handling enhance-agent command: {e}")
            return f"Error: {str(e)}"
    
    async def handle_optimize_resources_command(self, args: List[str]) -> str:
        """
        Handle the 'optimize-resources' command.
        
        Args:
            args (List[str]): Command arguments
            
        Returns:
            str: Command output
        """
        if not self.initialized:
            await self.initialize()
        
        # Parse options
        resource_types = None
        optimization_metric = "utilization"
        generations = 50
        population_size = 20
        
        i = 0
        while i < len(args):
            if args[i] == "--resource-types" and i + 1 < len(args):
                resource_types = args[i + 1].split(",")
                i += 2
            elif args[i] == "--optimization-metric" and i + 1 < len(args):
                optimization_metric = args[i + 1]
                i += 2
            elif args[i] == "--generations" and i + 1 < len(args):
                try:
                    generations = int(args[i + 1])
                    i += 2
                except ValueError:
                    return f"Error: Invalid generations value: {args[i + 1]}"
            elif args[i] == "--population-size" and i + 1 < len(args):
                try:
                    population_size = int(args[i + 1])
                    i += 2
                except ValueError:
                    return f"Error: Invalid population-size value: {args[i + 1]}"
            else:
                i += 1
        
        # Run resource optimization
        try:
            result = await self.borg_integration.optimize_resource_allocation(
                resource_types=resource_types,
                optimization_metric=optimization_metric,
                generations=generations,
                population_size=population_size,
            )
            
            # Format result
            output = f"Resource optimization task created with ID: {result['task_id']}\n"
            output += f"Status: {result['status']}\n"
            
            if result['status'] == "completed" and result.get("result"):
                evolution_result = result["result"]
                output += f"Generations: {evolution_result['generations']}\n"
                output += f"Best fitness: {evolution_result['best_fitness']}\n"
                
                if evolution_result.get("best_solution"):
                    output += f"\nOptimized resource allocation model:\n"
                    output += f"```python\n{evolution_result['best_solution']['code']}\n```\n"
            
            return output
        
        except Exception as e:
            logger.exception(f"Error handling optimize-resources command: {e}")
            return f"Error: {str(e)}"
    
    async def handle_optimize_scheduling_command(self, args: List[str]) -> str:
        """
        Handle the 'optimize-scheduling' command.
        
        Args:
            args (List[str]): Command arguments
            
        Returns:
            str: Command output
        """
        if not self.initialized:
            await self.initialize()
        
        # Parse options
        task_types = None
        optimization_metric = "throughput"
        generations = 50
        population_size = 20
        
        i = 0
        while i < len(args):
            if args[i] == "--task-types" and i + 1 < len(args):
                task_types = args[i + 1].split(",")
                i += 2
            elif args[i] == "--optimization-metric" and i + 1 < len(args):
                optimization_metric = args[i + 1]
                i += 2
            elif args[i] == "--generations" and i + 1 < len(args):
                try:
                    generations = int(args[i + 1])
                    i += 2
                except ValueError:
                    return f"Error: Invalid generations value: {args[i + 1]}"
            elif args[i] == "--population-size" and i + 1 < len(args):
                try:
                    population_size = int(args[i + 1])
                    i += 2
                except ValueError:
                    return f"Error: Invalid population-size value: {args[i + 1]}"
            else:
                i += 1
        
        # Run scheduling optimization
        try:
            result = await self.borg_integration.optimize_task_scheduling(
                task_types=task_types,
                optimization_metric=optimization_metric,
                generations=generations,
                population_size=population_size,
            )
            
            # Format result
            output = f"Task scheduling optimization task created with ID: {result['task_id']}\n"
            output += f"Status: {result['status']}\n"
            
            if result['status'] == "completed" and result.get("result"):
                evolution_result = result["result"]
                output += f"Generations: {evolution_result['generations']}\n"
                output += f"Best fitness: {evolution_result['best_fitness']}\n"
                
                if evolution_result.get("best_solution"):
                    output += f"\nOptimized task scheduling model:\n"
                    output += f"```python\n{evolution_result['best_solution']['code']}\n```\n"
            
            return output
        
        except Exception as e:
            logger.exception(f"Error handling optimize-scheduling command: {e}")
            return f"Error: {str(e)}"
    
    async def handle_status_command(self, args: List[str]) -> str:
        """
        Handle the 'alpha-evolve-status' command.
        
        Args:
            args (List[str]): Command arguments
            
        Returns:
            str: Command output
        """
        if not self.initialized:
            await self.initialize()
        
        # Check if evolution ID provided
        if args:
            evolution_id = args[0]
            
            # Get evolution status
            try:
                status = await self.alpha_evolve_engine.get_evolution_status(evolution_id)
                
                if status.get("status") == "not_found":
                    return f"Evolution not found: {evolution_id}"
                
                # Format status
                output = f"Evolution ID: {evolution_id}\n"
                output += f"Status: {status['status']}\n"
                output += f"Problem type: {status.get('problem', {}).get('type', 'unknown')}\n"
                
                if status.get("start_time"):
                    output += f"Start time: {status['start_time']}\n"
                
                if status.get("end_time"):
                    output += f"End time: {status['end_time']}\n"
                
                if status.get("current_generation") is not None:
                    output += f"Current generation: {status['current_generation']}\n"
                
                if status.get("best_fitness") is not None:
                    output += f"Best fitness: {status['best_fitness']}\n"
                
                if status.get("error"):
                    output += f"Error: {status['error']}\n"
                
                return output
            
            except Exception as e:
                logger.exception(f"Error getting evolution status: {e}")
                return f"Error: {str(e)}"
        
        # List all active evolutions
        try:
            active_evolutions = self.alpha_evolve_engine.active_evolutions
            
            if not active_evolutions:
                return "No active evolutions"
            
            output = f"Active evolutions: {len(active_evolutions)}\n\n"
            
            for evolution_id, evolution in active_evolutions.items():
                output += f"Evolution ID: {evolution_id}\n"
                output += f"Problem type: {evolution.get('problem', {}).get('type', 'unknown')}\n"
                output += f"Status: {evolution['status']}\n"
                output += f"Current generation: {evolution.get('current_generation', 0)}\n"
                output += f"Best fitness: {evolution.get('best_fitness', 0.0)}\n\n"
            
            return output
        
        except Exception as e:
            logger.exception(f"Error listing active evolutions: {e}")
            return f"Error: {str(e)}"
    
    async def handle_apply_model_command(self, args: List[str]) -> str:
        """
        Handle the 'apply-model' command.
        
        Args:
            args (List[str]): Command arguments
            
        Returns:
            str: Command output
        """
        if not self.initialized:
            await self.initialize()
        
        # Parse arguments
        if len(args) < 2:
            return "Error: Missing model_id or model_type argument"
        
        model_id = args[0]
        model_type = args[1]
        
        # Apply model
        try:
            if model_type == "resource_allocation":
                result = await self.borg_integration.apply_resource_allocation_model(model_id)
            elif model_type == "task_scheduling":
                result = await self.borg_integration.apply_task_scheduling_model(model_id)
            elif model_type == "agent_capability":
                result = await self.agent_integration.apply_capability_model(model_id)
            else:
                return f"Error: Unknown model type: {model_type}"
            
            # Format result
            if result.get("status") == "success":
                output = f"Model applied successfully\n"
                output += f"Model ID: {model_id}\n"
                output += f"Model type: {model_type}\n"
                
                if model_type == "resource_allocation" and result.get("new_allocation"):
                    output += f"\nNew resource allocation applied\n"
                elif model_type == "task_scheduling" and result.get("new_scheduling"):
                    output += f"\nNew task scheduling applied\n"
                elif model_type == "agent_capability" and result.get("agent_id"):
                    output += f"\nNew capability applied to agent: {result['agent_id']}\n"
                
                return output
            else:
                return f"Error applying model: {result.get('error', 'Unknown error')}"
        
        except Exception as e:
            logger.exception(f"Error handling apply-model command: {e}")
            return f"Error: {str(e)}"
