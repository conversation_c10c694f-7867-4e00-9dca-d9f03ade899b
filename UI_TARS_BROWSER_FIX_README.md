# UI-TARS Browser Integration Fix

This package provides tools to fix browser integration issues in UI-TARS 1.5 and enhance its capabilities with:

- **Enhanced Browser Detection**: Reliable detection of browsers across platforms
- **Browser Sandbox**: Isolated browser environment for security and stability
- **Virtual PC Environment**: Virtualized environment for UI-TARS to run in
- **DPO (Direct Preference Optimization)**: Learning from user preferences to improve over time
- **Debugging Tools**: Comprehensive tools for diagnosing and fixing issues

## Quick Start

The easiest way to fix UI-TARS browser integration issues is to run the batch file:

```
fix_ui_tars_browser.bat
```

This will guide you through the process of fixing browser detection issues and starting UI-TARS with the enhanced configuration.

## Tools Included

### 1. Enhanced UI-TARS Diagnostic Tool

This tool provides comprehensive diagnostics for UI-TARS 1.5, including browser detection, API connection testing, and more.

```
python enhanced_ui_tars_diagnostic.py --start --sandbox --virtual-pc --dpo
```

Options:
- `--path PATH`: Path to UI-TARS executable
- `--browser {chrome,edge,firefox,brave}`: Browser type to use
- `--sandbox`: Enable sandbox mode
- `--virtual-pc`: Enable virtual PC mode
- `--dpo`: Enable DPO (Direct Preference Optimization)
- `--start`: Start UI-TARS after diagnostics
- `--debug`: Enable debug logging

### 2. UI-TARS Virtual PC Environment

This tool creates a virtual PC environment for UI-TARS 1.5, providing an isolated execution environment.

```
python ui_tars_virtual_pc.py --browser chrome --memory 2048 --cpu 2
```

Options:
- `--path PATH`: Path to UI-TARS executable
- `--browser {chrome,edge,firefox,brave}`: Browser type to use
- `--memory MEMORY`: Memory allocation in MB
- `--cpu CPU`: Number of CPU cores to allocate
- `--no-virtual-display`: Disable virtual display
- `--no-sandbox`: Disable sandbox mode
- `--no-dpo`: Disable DPO
- `--debug`: Enable debug mode

### 3. Browser Detection Fix

This tool specifically fixes browser detection issues in UI-TARS 1.5.

```
python fix_ui_tars_browser.py --browser chrome --start
```

Options:
- `--path PATH`: Path to UI-TARS executable
- `--config CONFIG`: Path to UI-TARS configuration file
- `--browser {chrome,edge,firefox,brave}`: Browser type to use
- `--no-sandbox`: Disable sandbox mode
- `--no-virtual-pc`: Disable virtual PC mode
- `--no-dpo`: Disable DPO
- `--debug`: Enable debug mode
- `--start`: Start UI-TARS after fixing

## Batch Files

For convenience, the following batch files are provided:

- `fix_ui_tars_browser.bat`: Fix browser detection issues and start UI-TARS
- `run_enhanced_ui_tars.bat`: Run the enhanced UI-TARS diagnostic tool
- `run_ui_tars_virtual_pc.bat`: Run UI-TARS in a virtual PC environment

## Advanced Features

### Browser Sandbox

The browser sandbox provides an isolated environment for browser automation, with features such as:

- Isolated user data directory
- Remote debugging capabilities
- Security isolation
- Headless mode support

### DPO (Direct Preference Optimization)

DPO allows UI-TARS to learn from user preferences and improve over time, with features such as:

- Preference pair collection
- Optimization based on user feedback
- Continuous learning
- Performance improvement over time

### Virtual PC Environment

The virtual PC environment provides a virtualized environment for UI-TARS to run in, with features such as:

- Resource allocation (memory, CPU)
- Virtual display (on Linux)
- Isolation from the host system
- Enhanced security

## Troubleshooting

### UI-TARS Not Starting

If UI-TARS fails to start after fixing browser detection issues:

1. Check the logs in the `ui_tars_debug.log` file
2. Make sure the UI-TARS executable path is correct
3. Try running with `--debug` for more detailed logging
4. Check if the browser is properly detected and available

### Browser Not Detected

If the browser is not detected:

1. Make sure the browser is installed
2. Try specifying the browser type explicitly with `--browser`
3. Check if the browser executable path is correct
4. Try running with `--debug` for more detailed logging

### API Connection Issues

If UI-TARS API connection fails:

1. Make sure UI-TARS is running
2. Check if the API port (default: 8080) is not used by another application
3. Try restarting UI-TARS
4. Check the logs for API-related errors

## Requirements

- Python 3.8 or higher
- UI-TARS 1.5
- One of the following browsers: Chrome, Edge, Firefox, or Brave

## Testing

To verify that UI-TARS 1.5 works properly on your computer, run the test script:

```
test_ui_tars_implementation.bat
```

This script will:
1. Check if UI-TARS is installed and configured correctly
2. Detect available browsers
3. Test the sandbox environment
4. Test DPO capabilities
5. Test virtual PC capabilities

The test results will show which components are working correctly and provide recommendations for fixing any issues.

## License

This software is licensed under the Apache License, Version 2.0. See the LICENSE file for the full license text.

```
Copyright 2023-2025 Flo Faction Insurance / Paul Edwards

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
```

## Credits

Developed for Flo Faction Insurance to enhance UI-TARS 1.5 capabilities and fix browser integration issues.
