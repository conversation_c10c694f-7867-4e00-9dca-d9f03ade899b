"""
<PERSON><PERSON><PERSON> to create a new agent.
"""
import sys
import os
import argparse
from pathlib import Path
import re

def create_agent(agent_name: str, description: str):
    """
    Create a new agent.
    
    Args:
        agent_name (str): Name of the agent (snake_case)
        description (str): Description of the agent
    """
    # Validate agent name
    if not re.match(r'^[a-z][a-z0-9_]*$', agent_name):
        print(f"Error: Invalid agent name: {agent_name}")
        print("Agent name must be in snake_case (lowercase with underscores)")
        return False
    
    # Create class name (PascalCase)
    class_name = "".join(word.capitalize() for word in agent_name.split("_")) + "Agent"
    
    # Create file path
    file_path = Path(f"agents/{agent_name}_agent.py")
    
    # Check if file already exists
    if file_path.exists():
        print(f"Error: Agent file already exists: {file_path}")
        return False
    
    # Create agent file
    print(f"Creating agent file: {file_path}")
    
    # Agent file template
    template = f'''"""
{description}
"""
import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any
import json
import uuid

from agents.base_agent import BaseAgent
from core.logger import setup_logger
from llm.llm_router import LLMRouter

class {class_name}(BaseAgent):
    """
    {description}
    """
    
    def __init__(
        self,
        agent_id: str,
        config: Dict,
        state_manager,
        message_queue,
        shutdown_event
    ):
        """Initialize the {agent_name} agent."""
        super().__init__(agent_id, config, state_manager, message_queue, shutdown_event)
        
        # Agent-specific configuration
        self.llm_provider = config.get("llm_provider", "anthropic")
        self.llm_router = None
        
        # Agent-specific data
        self.data = {{}}
        
        # Agent capabilities
        self.capabilities = [
            # Add capabilities here
        ]
    
    async def initialize(self):
        """Initialize the {agent_name} agent."""
        await super().initialize()
        
        # Initialize LLM router
        self.llm_router = LLMRouter()
        await self.llm_router.initialize()
        
        # Load agent data
        await self._load_data()
        
        self.logger.info(f"{class_name} initialized with provider: {{self.llm_provider}}")
    
    async def _load_data(self):
        """Load agent data from state manager."""
        # Load data
        data = await self.state_manager.get_state("{agent_name}", "data")
        if data:
            self.data = data
            self.logger.info(f"Loaded {agent_name} data")
    
    async def execute_cycle(self):
        """Execute one cycle of the {agent_name} agent's logic."""
        self.logger.debug("Executing {agent_name} agent cycle")
        
        try:
            # Check for pending tasks
            pending_tasks = await self.state_manager.get_state("{agent_name}", "pending_tasks")
            if pending_tasks:
                for task_id, task in pending_tasks.items():
                    if task.get("status") == "pending":
                        await self._process_task(task_id, task)
            
            # Add agent-specific logic here
            
            # Update state with any changes
            await self._save_data()
            
        except Exception as e:
            self.logger.exception(f"Error in {agent_name} agent cycle: {{e}}")
    
    async def _process_task(self, task_id: str, task: Dict):
        """
        Process a pending task.
        
        Args:
            task_id (str): Task identifier
            task (Dict): Task data
        """
        task_type = task.get("type")
        self.logger.info(f"Processing task: {{task_id}} ({{task_type}})")
        
        try:
            # Add task processing logic here
            
            # Update task status
            task["status"] = "completed"
            task["completed_at"] = datetime.now().isoformat()
            
            # Update pending tasks
            pending_tasks = await self.state_manager.get_state("{agent_name}", "pending_tasks") or {{}}
            pending_tasks[task_id] = task
            await self.state_manager.update_state("{agent_name}", "pending_tasks", pending_tasks)
            
        except Exception as e:
            self.logger.exception(f"Error processing task {{task_id}}: {{e}}")
            
            # Update task status
            task["status"] = "error"
            task["error"] = str(e)
            
            # Update pending tasks
            pending_tasks = await self.state_manager.get_state("{agent_name}", "pending_tasks") or {{}}
            pending_tasks[task_id] = task
            await self.state_manager.update_state("{agent_name}", "pending_tasks", pending_tasks)
    
    async def _save_data(self):
        """Save agent data to state manager."""
        # Save data
        await self.state_manager.update_state("{agent_name}", "data", self.data)
    
    async def handle_command(self, message: Dict):
        """
        Handle a command message.
        
        Args:
            message (Dict): Command message
        """
        command = message.get("content", {{}}).get("command")
        
        # Add command handling logic here
        
        # If command not handled, call parent method
        await super().handle_command(message)
    
    async def handle_query(self, message: Dict):
        """
        Handle a query message.
        
        Args:
            message (Dict): Query message
        """
        query = message.get("content", {{}}).get("query")
        
        # Add query handling logic here
        
        # If query not handled, call parent method
        await super().handle_query(message)
'''
    
    # Write agent file
    with open(file_path, "w") as f:
        f.write(template)
    
    print(f"Agent file created: {file_path}")
    
    # Update config.py
    print("Updating config.py with new agent")
    
    # Read config.py
    config_path = Path("config.py")
    with open(config_path, "r") as f:
        config_content = f.read()
    
    # Find AGENT_CONFIG section
    agent_config_match = re.search(r'AGENT_CONFIG\s*=\s*{([^}]*)}', config_content, re.DOTALL)
    if not agent_config_match:
        print("Error: Could not find AGENT_CONFIG section in config.py")
        return False
    
    # Extract AGENT_CONFIG content
    agent_config_content = agent_config_match.group(1)
    
    # Check if agent already exists in config
    if f'"{agent_name}"' in agent_config_content:
        print(f"Error: Agent {agent_name} already exists in config.py")
        return False
    
    # Create new agent config
    new_agent_config = f'''
    "{agent_name}": {{
        "name": "{' '.join(word.capitalize() for word in agent_name.split('_'))}",
        "description": "{description}",
        "llm_provider": os.getenv("{agent_name.upper()}_AGENT_LLM", DEFAULT_LLM_PROVIDER),
        "enabled": os.getenv("ENABLE_{agent_name.upper()}_AGENT", "True").lower() == "true",
        "polling_interval": int(os.getenv("{agent_name.upper()}_AGENT_POLLING_INTERVAL", "300")),  # seconds
    }},'''
    
    # Add new agent config to AGENT_CONFIG
    new_agent_config_content = agent_config_content + new_agent_config
    
    # Replace AGENT_CONFIG content
    new_config_content = config_content.replace(agent_config_content, new_agent_config_content)
    
    # Write updated config.py
    with open(config_path, "w") as f:
        f.write(new_config_content)
    
    print("config.py updated with new agent")
    
    # Update .env.example
    print("Updating .env.example with new agent")
    
    # Read .env.example
    env_path = Path(".env.example")
    with open(env_path, "r") as f:
        env_content = f.read()
    
    # Create new agent env vars
    new_agent_env = f'''
ENABLE_{agent_name.upper()}_AGENT=True
{agent_name.upper()}_AGENT_LLM=anthropic
{agent_name.upper()}_AGENT_POLLING_INTERVAL=300
'''
    
    # Add new agent env vars to .env.example
    new_env_content = env_content + new_agent_env
    
    # Write updated .env.example
    with open(env_path, "w") as f:
        f.write(new_env_content)
    
    print(".env.example updated with new agent")
    
    # Print next steps
    print("\nNext steps:")
    print(f"1. Edit agents/{agent_name}_agent.py to implement your agent's logic")
    print("2. Update your .env file with the new agent's configuration")
    print("3. Restart the system to load the new agent")
    
    return True

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Create a new agent")
    parser.add_argument("name", help="Name of the agent (snake_case)")
    parser.add_argument("--description", default="Agent specialized for custom tasks.", help="Description of the agent")
    args = parser.parse_args()
    
    # Create agent
    success = create_agent(args.name, args.description)
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
