"""
Advanced web scraping and crawling module for AI agent system.

This module provides advanced web intelligence capabilities including:
- Distributed web crawling with rate limiting
- JavaScript rendering support
- Bypass mechanisms for common anti-scraping measures
- Structured data extraction
- Browser fingerprint simulation
"""
import asyncio
import logging
import os
import json
import random
import re
import time
import uuid
from typing import Dict, List, Optional, Any, Union, Set, Tuple
from datetime import datetime
import hashlib

from concurrent.futures import ThreadPoolExecutor
import urllib.parse

from core.logger import setup_logger

# Optional dependencies - will gracefully degrade if not available
try:
    import aiohttp
    AIOHTTP_AVAILABLE = True
except ImportError:
    AIOHTTP_AVAILABLE = False

try:
    import requests
    from requests.adapters import HTTPAdapter
    from urllib3.util.retry import Retry
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False

try:
    from bs4 import BeautifulSoup
    BS4_AVAILABLE = True
except ImportError:
    BS4_AVAILABLE = False

try:
    import playwright.async_api as playwright
    from playwright.async_api import async_playwright
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False

try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

try:
    import scrapy
    from scrapy.crawler import CrawlerProcess
    from scrapy import signals
    SCRAPY_AVAILABLE = True
except ImportError:
    SCRAPY_AVAILABLE = False

# Set up logger
logger = setup_logger("advanced_scraper")

class BrowserFingerprint:
    """Browser fingerprint for advanced anti-detection."""
    
    # Common user agents for simulation
    USER_AGENTS = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.4 Safari/605.1.15",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/112.0",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/111.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36 Edg/112.0.1722.48",
    ]
    
    # Common screen resolutions
    SCREEN_RESOLUTIONS = [
        (1920, 1080),
        (1366, 768),
        (2560, 1440),
        (3840, 2160),
        (1280, 720),
    ]
    
    # Common color depths
    COLOR_DEPTHS = [24, 32, 16]
    
    # Common platform info
    PLATFORMS = [
        "Win32",
        "MacIntel",
        "Linux x86_64",
        "Linux armv7l",
    ]
    
    # Common language settings
    LANGUAGES = [
        "en-US,en;q=0.9",
        "en-GB,en;q=0.9",
        "es-ES,es;q=0.9,en;q=0.8",
        "fr-FR,fr;q=0.9,en;q=0.8",
        "de-DE,de;q=0.9,en;q=0.8",
    ]
    
    def __init__(self, seed: Optional[int] = None, profile_type: str = "random"):
        """
        Initialize a browser fingerprint.
        
        Args:
            seed (Optional[int]): Random seed for reproducibility
            profile_type (str): Type of profile ("random", "consistent", or browser name)
        """
        self.seed = seed if seed is not None else random.randint(1, 100000)
        self.random_engine = random.Random(self.seed)
        self.profile_type = profile_type
        
        # Generate a consistent fingerprint
        self._generate_fingerprint()
    
    def _generate_fingerprint(self):
        """Generate browser fingerprint parameters."""
        if self.profile_type == "random":
            self.user_agent = self.random_engine.choice(self.USER_AGENTS)
            self.screen_resolution = self.random_engine.choice(self.SCREEN_RESOLUTIONS)
            self.color_depth = self.random_engine.choice(self.COLOR_DEPTHS)
            self.platform = self.random_engine.choice(self.PLATFORMS)
            self.language = self.random_engine.choice(self.LANGUAGES)
            
        elif self.profile_type == "chrome":
            self.user_agent = self.USER_AGENTS[0]  # Chrome user agent
            self.screen_resolution = (1920, 1080)
            self.color_depth = 24
            self.platform = "Win32"
            self.language = "en-US,en;q=0.9"
            
        elif self.profile_type == "firefox":
            self.user_agent = self.USER_AGENTS[2]  # Firefox user agent
            self.screen_resolution = (1920, 1080)
            self.color_depth = 24
            self.platform = "Win32"
            self.language = "en-US,en;q=0.9"
            
        elif self.profile_type == "safari":
            self.user_agent = self.USER_AGENTS[1]  # Safari user agent
            self.screen_resolution = (1440, 900)
            self.color_depth = 32
            self.platform = "MacIntel"
            self.language = "en-US,en;q=0.9"
            
        else:  # Default to consistent but semi-random
            # Hash the seed to get consistent but seemingly random values
            hash_obj = hashlib.md5(str(self.seed).encode())
            hash_int = int(hash_obj.hexdigest(), 16)
            
            self.user_agent = self.USER_AGENTS[hash_int % len(self.USER_AGENTS)]
            self.screen_resolution = self.SCREEN_RESOLUTIONS[hash_int % len(self.SCREEN_RESOLUTIONS)]
            self.color_depth = self.COLOR_DEPTHS[hash_int % len(self.COLOR_DEPTHS)]
            self.platform = self.PLATFORMS[hash_int % len(self.PLATFORMS)]
            self.language = self.LANGUAGES[hash_int % len(self.LANGUAGES)]
    
    def get_headers(self) -> Dict[str, str]:
        """Get HTTP headers for the fingerprint."""
        return {
            "User-Agent": self.user_agent,
            "Accept-Language": self.language,
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "DNT": "1",  # Do Not Track
        }
    
    def apply_to_playwright(self, context_options: Dict[str, Any]) -> Dict[str, Any]:
        """Apply fingerprint to Playwright browser context options."""
        updated_options = context_options.copy()
        
        # Extract browser type from user agent
        is_chrome = "Chrome" in self.user_agent
        is_firefox = "Firefox" in self.user_agent
        is_safari = "Safari" in self.user_agent and "Chrome" not in self.user_agent
        
        # Set proper viewport
        updated_options["viewport"] = {
            "width": self.screen_resolution[0],
            "height": self.screen_resolution[1]
        }
        
        # Set user agent
        updated_options["user_agent"] = self.user_agent
        
        # Set language
        updated_options["locale"] = self.language.split(",")[0]
        
        # Set color depth
        updated_options["color_scheme"] = "no-preference"
        
        # Set platform
        updated_options["platform"] = self.platform
        
        # Set device scale factor
        updated_options["device_scale_factor"] = 1.0
        
        return updated_options
    
    def apply_to_selenium(self, options):
        """Apply fingerprint to Selenium options."""
        options.add_argument(f"user-agent={self.user_agent}")
        options.add_argument(f"--window-size={self.screen_resolution[0]},{self.screen_resolution[1]}")
        options.add_argument(f"--lang={self.language.split(',')[0]}")
        
        # Additional options to make fingerprint more realistic
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option("useAutomationExtension", False)
        
        return options


class AdvancedWebScraper:
    """
    Advanced web scraper with sophisticated capabilities for bypassing detection.
    
    Features:
    - Multiple scraping backends (simple HTTP, Selenium, Playwright)
    - Browser fingerprint simulation
    - Proxy rotation
    - Rate limiting and back-off strategies
    - Anti-detection mechanisms
    """
    
    def __init__(self, config: Dict):
        """
        Initialize the advanced web scraper.
        
        Args:
            config (Dict): Configuration for the scraper
        """
        self.config = config
        self.enabled = config.get("enabled", True)
        
        # Networking configuration
        self.default_timeout = config.get("timeout", 30)
        self.max_retries = config.get("max_retries", 3)
        self.retry_backoff = config.get("retry_backoff", 1.5)
        
        # Rate limiting configuration
        self.rate_limit = config.get("rate_limit", 10)  # requests per minute
        self.rate_limit_enabled = config.get("rate_limit_enabled", True)
        self.last_request_time = 0.0
        
        # Anti-detection configuration
        self.rotate_fingerprints = config.get("rotate_fingerprints", True)
        self.default_fingerprint_type = config.get("fingerprint_type", "random")
        
        # Proxy configuration
        self.proxies = config.get("proxies", [])
        self.rotate_proxies = config.get("rotate_proxies", len(self.proxies) > 0)
        self.current_proxy_index = 0
        
        # Browser automation config
        self.preferred_backend = config.get("preferred_backend", "auto")  # auto, playwright, selenium, simple
        self.headless = config.get("headless", True)
        self.browser_args = config.get("browser_args", [])
        
        # Create a set of fingerprints to rotate through
        self.fingerprints = []
        if self.rotate_fingerprints:
            for i in range(config.get("num_fingerprints", 5)):
                self.fingerprints.append(BrowserFingerprint(seed=i, profile_type=self.default_fingerprint_type))
        else:
            self.fingerprints.append(BrowserFingerprint(profile_type=self.default_fingerprint_type))
        
        self.current_fingerprint_index = 0
        
        # Client objects
        self.session = None
        self.browser = None
        self.context = None
        self.playwright_api = None
        self.selenium_driver = None
        
        # State
        self.initialized = False
    
    async def initialize(self):
        """Initialize the web scraper and prepare resources."""
        if not self.enabled:
            logger.info("Advanced web scraping is disabled. Skipping initialization.")
            return
        
        logger.info("Initializing advanced web scraper")
        
        try:
            # Initialize HTTP session if requests is available
            if REQUESTS_AVAILABLE:
                retry_strategy = Retry(
                    total=self.max_retries,
                    backoff_factor=self.retry_backoff,
                    status_forcelist=[429, 500, 502, 503, 504],
                )
                adapter = HTTPAdapter(max_retries=retry_strategy)
                self.session = requests.Session()
                self.session.mount("http://", adapter)
                self.session.mount("https://", adapter)
                
                # Apply initial fingerprint
                self.session.headers.update(self.get_current_fingerprint().get_headers())
                logger.info("HTTP session initialized with retry strategy")
            
            self.initialized = True
            logger.info("Advanced web scraper initialized successfully")
            
        except Exception as e:
            logger.exception(f"Error initializing advanced web scraper: {e}")
    
    def get_current_fingerprint(self) -> BrowserFingerprint:
        """Get the current browser fingerprint."""
        return self.fingerprints[self.current_fingerprint_index]
    
    def rotate_fingerprint(self):
        """Rotate to the next browser fingerprint."""
        if not self.rotate_fingerprints or len(self.fingerprints) <= 1:
            return
            
        self.current_fingerprint_index = (self.current_fingerprint_index + 1) % len(self.fingerprints)
        
        # Apply new fingerprint to session if it exists
        if self.session:
            self.session.headers.update(self.get_current_fingerprint().get_headers())
    
    def get_current_proxy(self) -> Optional[Dict[str, str]]:
        """Get the current proxy configuration."""
        if not self.proxies:
            return None
            
        return self.proxies[self.current_proxy_index]
    
    def rotate_proxy(self):
        """Rotate to the next proxy."""
        if not self.rotate_proxies or not self.proxies:
            return
            
        self.current_proxy_index = (self.current_proxy_index + 1) % len(self.proxies)
    
    async def _detect_best_backend(self, url: str) -> str:
        """Detect the best backend for a given URL."""
        if self.preferred_backend != "auto":
            return self.preferred_backend
            
        # Check for likely JavaScript-rendered content or anti-bot measures
        js_heavy_indicators = ["react", "vue", "angular", "cloudflare", "recaptcha", "captcha"]
        
        if any(indicator in url.lower() for indicator in js_heavy_indicators):
            if PLAYWRIGHT_AVAILABLE:
                return "playwright"
            elif SELENIUM_AVAILABLE:
                return "selenium"
        
        return "simple"
    
    async def respect_rate_limit(self):
        """Respect rate limits by waiting if needed."""
        if not self.rate_limit_enabled:
            return
            
        # Calculate time since last request
        current_time = time.time()
        elapsed = current_time - self.last_request_time
        
        # Calculate minimum interval between requests
        min_interval = 60.0 / self.rate_limit
        
        # Wait if we're making requests too quickly
        if elapsed < min_interval:
            wait_time = min_interval - elapsed
            logger.debug(f"Rate limiting: waiting {wait_time:.2f} seconds")
            await asyncio.sleep(wait_time)
        
        self.last_request_time = time.time()
    
    async def _get_with_simple(self, url: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """Get a URL using simple HTTP requests."""
        if not REQUESTS_AVAILABLE:
            return {"success": False, "error": "Requests library not available"}
        
        try:
            # Apply fingerprint and proxy settings
            headers = self.get_current_fingerprint().get_headers()
            proxies = self.get_current_proxy()
            
            # Add custom headers if specified
            if "headers" in options:
                headers.update(options["headers"])
            
            # Respect rate limits
            await self.respect_rate_limit()
            
            # Make the request
            response = self.session.get(
                url,
                headers=headers,
                proxies=proxies,
                timeout=options.get("timeout", self.default_timeout),
                verify=options.get("verify_ssl", True)
            )
            
            # Check if successful
            response.raise_for_status()
            
            # Parse response
            content_type = response.headers.get("Content-Type", "")
            is_json = "application/json" in content_type
            
            if is_json:
                data = response.json()
            else:
                html = response.text
                data = html
            
            return {
                "success": True,
                "status_code": response.status_code,
                "headers": dict(response.headers),
                "data": data,
                "is_json": is_json,
                "url": response.url,
                "elapsed": response.elapsed.total_seconds()
            }
            
        except requests.exceptions.RequestException as e:
            logger.warning(f"Error in simple HTTP request: {e}")
            return {"success": False, "error": str(e)}
    
    async def _get_with_playwright(self, url: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """Get a URL using Playwright for JavaScript rendering."""
        if not PLAYWRIGHT_AVAILABLE:
            return {"success": False, "error": "Playwright not available"}
        
        browser = None
        page = None
        
        try:
            # Start Playwright on demand
            self.playwright_api = await async_playwright().start()
            
            # Get browser type
            browser_type_name = options.get("browser_type", "chromium")
            if browser_type_name == "firefox":
                browser_type = self.playwright_api.firefox
            elif browser_type_name == "webkit":
                browser_type = self.playwright_api.webkit
            else:
                browser_type = self.playwright_api.chromium
            
            # Browser context options with fingerprint
            context_options = {
                "headless": options.get("headless", self.headless),
                "proxy": self.get_current_proxy() if self.rotate_proxies else None,
            }
            
            # Apply fingerprint to context options
            context_options = self.get_current_fingerprint().apply_to_playwright(context_options)
            
            # Respect rate limits
            await self.respect_rate_limit()
            
            # Launch browser
            browser = await browser_type.launch(
                headless=context_options.pop("headless", True),
                args=self.browser_args
            )
            
            # Create context and page
            context = await browser.new_context(**context_options)
            page = await context.new_page()
            
            # Set default timeout
            page.set_default_timeout(options.get("timeout", self.default_timeout) * 1000)
            
            # Handle JavaScript dialog alerts and prompts
            page.on("dialog", lambda dialog: asyncio.create_task(dialog.dismiss()))
            
            # Navigate to the URL
            timeout_ms = options.get("timeout", self.default_timeout) * 1000
            await page.goto(url, wait_until="networkidle", timeout=timeout_ms)
            
            # Wait for selector if specified
            if "wait_for_selector" in options:
                await page.wait_for_selector(options["wait_for_selector"])
            
            # Get page content
            content = await page.content()
            
            # Get any structured data if needed
            page_url = page.url
            
            # Collect links if requested
            links = []
            if options.get("collect_links", False):
                link_elements = await page.query_selector_all("a")
                for element in link_elements:
                    href = await element.get_attribute("href")
                    if href:
                        text = await element.text_content()
                        links.append({"href": href, "text": text})
            
            # Clean up
            await page.close()
            await context.close()
            await browser.close()
            
            return {
                "success": True,
                "data": content,
                "is_json": False,
                "url": page_url,
                "links": links if options.get("collect_links", False) else []
            }
            
        except Exception as e:
            logger.warning(f"Error in Playwright request: {e}")
            return {"success": False, "error": str(e)}
            
        finally:
            # Clean up resources
            if self.playwright_api:
                await self.playwright_api.stop()
                self.playwright_api = None
    
    async def _get_with_selenium(self, url: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """Get a URL using Selenium for JavaScript rendering."""
        if not SELENIUM_AVAILABLE:
            return {"success": False, "error": "Selenium not available"}
        
        driver = None
        
        try:
            # Set up browser options
            chrome_options = Options()
            if options.get("headless", self.headless):
                chrome_options.add_argument("--headless")
            
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--disable-extensions")
            
            # Apply fingerprint customizations
            chrome_options = self.get_current_fingerprint().apply_to_selenium(chrome_options)
            
            # Set proxy if available
            proxy = self.get_current_proxy()
            if proxy:
                chrome_options.add_argument(f'--proxy-server={proxy["http"]}')
            
            # Respect rate limits
            await self.respect_rate_limit()
            
            # Start browser in a separate thread to avoid blocking the event loop
            def start_selenium():
                return webdriver.Chrome(options=chrome_options)
            
            with ThreadPoolExecutor() as executor:
                driver_future = executor.submit(start_selenium)
                driver = driver_future.result()
            
            # Set page load timeout
            driver.set_page_load_timeout(options.get("timeout", self.default_timeout))
            
            # Navigate to the URL
            driver.get(url)
            
            # Wait for page to load
            if "wait_for_selector" in options:
                try:
                    element_present = EC.presence_of_element_located((By.CSS_SELECTOR, options["wait_for_selector"]))
                    WebDriverWait(driver, options.get("timeout", self.default_timeout)).until(element_present)
                except Exception as e:
                    logger.warning(f"Wait for selector timed out: {e}")
            else:
                # Default wait for page load
                time.sleep(2)
            
            # Get page source
            content = driver.page_source
            
            # Get the final URL after any redirects
            final_url = driver.current_url
            
            # Collect links if requested
            links = []
            if options.get("collect_links", False):
                link_elements = driver.find_elements(By.TAG_NAME, "a")
                for element in link_elements:
                    try:
                        href = element.get_attribute("href")
                        if href:
                            text = element.text
                            links.append({"href": href, "text": text})
                    except:
                        pass
            
            return {
                "success": True,
                "data": content,
                "is_json": False,
                "url": final_url,
                "links": links if options.get("collect_links", False) else []
            }
            
        except Exception as e:
            logger.warning(f"Error in Selenium request: {e}")
            return {"success": False, "error": str(e)}
        
        finally:
            # Clean up Selenium driver
            if driver:
                driver.quit()
    
    async def get(self, url: str, **kwargs) -> Dict[str, Any]:
        """
        Get content from a URL using the most appropriate backend.
        
        Args:
            url (str): The URL to retrieve
            **kwargs: Additional options
                - backend: Specific backend to use ("simple", "playwright", "selenium")
                - timeout: Request timeout in seconds
                - headers: Additional headers to send
                - wait_for_selector: CSS selector to wait for before returning
                - collect_links: Whether to collect links from the page
                - parse_html: Whether to parse HTML into a structured format
                - follow_redirects: Whether to follow redirects
                - verify_ssl: Whether to verify SSL certificates
                
        Returns:
            Dict[str, Any]: Response information
        """
        if not self.initialized:
            await self.initialize()
        
        # Create options dictionary from kwargs
        options = dict(kwargs)
        
        # Detect the best backend to use if not specified
        backend = options.get("backend") or await self._detect_best_backend(url)
        
        # Try the specified backend with retries
        for attempt in range(self.max_retries):
            try:
                if backend == "playwright":
                    result = await self._get_with_playwright(url, options)
                elif backend == "selenium":
                    result = await self._get_with_selenium(url, options)
                else:  # simple
                    result = await self._get_with_simple(url, options)
                
                # If successful, return result
                if result.get("success", False):
                    # Parse HTML if requested
                    if options.get("parse_html", False) and result.get("data") and not result.get("is_json", False):
                        if BS4_AVAILABLE:
                            soup = BeautifulSoup(result["data"], "html.parser")
                            result["parsed"] = soup
                    
                    # Auto-rotate fingerprint for next request
                    if self.rotate_fingerprints:
                        self.rotate_fingerprint()
                    
                    # Auto-rotate proxy for next request
                    if self.rotate_proxies:
                        self.rotate_proxy()
                    
                    return result
                
                # If failed due to captcha or anti-bot, try a different approach
                error = result.get("error", "").lower()
                if "captcha" in error or "bot" in error or "forbidden" in error:
                    logger.warning(f"Anti-bot measures detected: {error}. Trying a different approach.")
                    
                    # Try a more sophisticated method
                    if backend == "simple":
                        if PLAYWRIGHT_AVAILABLE:
                            backend = "playwright"
                        elif SELENIUM_AVAILABLE:
                            backend = "selenium"
                        else:
                            # No more sophisticated methods available, rotate fingerprint and retry
                            self.rotate_fingerprint()
                    
                    # If using playwright/selenium and hit captcha, rotate fingerprint
                    elif backend in ["playwright", "selenium"]:
                        self.rotate_fingerprint()
                        self.rotate_proxy()
                
                # Wait before retrying
                if attempt < self.max_retries - 1:
                    retry_delay = self.retry_backoff ** attempt
                    logger.info(f"Retrying after {retry_delay:.1f} seconds...")
                    await asyncio.sleep(retry_delay)
            
            except Exception as e:
                logger.exception(f"Unexpected error in get request: {e}")
                
                # Wait before retrying
                if attempt < self.max_retries - 1:
                    retry_delay = self.retry_backoff ** attempt
                    logger.info(f"Retrying after {retry_delay:.1f} seconds...")
                    await asyncio.sleep(retry_delay)
        
        # If we've exhausted all retries, return failure
        return {"success": False, "error": f"Failed after {self.max_retries} attempts"}
    
    async def extract_structured_data(self, html: str, extractors: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Extract structured data from HTML using various extractors.
        
        Args:
            html (str): HTML content to extract from
            extractors (List[Dict]): List of extractor definitions
                - type: Type of extractor ("css", "xpath", "regex", "json_ld")
                - field: Field name to store result in
                - selector: CSS or XPath selector
                - pattern: Regex pattern
                - multiple: Whether to extract multiple items
                - attr: Attribute to extract (for CSS/XPath)
                
        Returns:
            Dict[str, Any]: Extracted structured data
        """
        if not BS4_AVAILABLE:
            return {"success": False, "error": "BeautifulSoup not available"}
        
        try:
            # Parse HTML
            soup = BeautifulSoup(html, "html.parser")
            results = {}
            
            # Process each extractor
            for extractor in extractors:
                extractor_type = extractor.get("type", "css")
                field = extractor.get("field", "unknown")
                multiple = extractor.get("multiple", False)
                
                if extractor_type == "css":
                    selector = extractor.get("selector", "")
                    attr = extractor.get("attr")
                    
                    if multiple:
                        elements = soup.select(selector)
                        if attr:
                            results[field] = [elem.get(attr) for elem in elements if elem.get(attr)]
                        else:
                            results[field] = [elem.get_text(strip=True) for elem in elements]
                    else:
                        element = soup.select_one(selector)
                        if element:
                            if attr:
                                results[field] = element.get(attr)
                            else:
                                results[field] = element.get_text(strip=True)
                        else:
                            results[field] = None
                
                elif extractor_type == "xpath":
                    # Note: BeautifulSoup doesn't directly support XPath
                    # In a real implementation, we'd use lxml or similar here
                    logger.warning("XPath extraction not implemented with BeautifulSoup")
                    results[field] = None
                
                elif extractor_type == "regex":
                    pattern = extractor.get("pattern", "")
                    group = extractor.get("group", 1)
                    
                    try:
                        if multiple:
                            matches = re.findall(pattern, html)
                            results[field] = matches
                        else:
                            match = re.search(pattern, html)
                            if match:
                                if isinstance(match.groups(), tuple) and len(match.groups()) >= group:
                                    results[field] = match.group(group)
                                else:
                                    results[field] = match.group(0)
                            else:
                                results[field] = None
                    except re.error as e:
                        logger.warning(f"Regex error: {e}")
                        results[field] = None
                
                elif extractor_type == "json_ld":
                    # Extract JSON-LD structured data
                    json_ld_scripts = soup.find_all("script", type="application/ld+json")
                    json_ld_data = []
                    
                    for script in json_ld_scripts:
                        try:
                            data = json.loads(script.string)
                            json_ld_data.append(data)
                        except Exception as e:
                            logger.warning(f"JSON-LD parsing error: {e}")
                    
                    results[field] = json_ld_data if multiple else (json_ld_data[0] if json_ld_data else None)
            
            return {
                "success": True,
                "data": results
            }
            
        except Exception as e:
            logger.exception(f"Error extracting structured data: {e}")
            return {"success": False, "error": str(e)}
    
    async def crawl(self, start_urls: List[str], options: Dict[str, Any]) -> Dict[str, Any]:
        """
        Crawl multiple pages starting from the given URLs.
        
        Args:
            start_urls (List[str]): URLs to start crawling from
            options (Dict): Crawling options
                - max_pages: Maximum number of pages to crawl
                - max_depth: Maximum crawl depth
                - allowed_domains: List of domains to restrict crawling to
                - follow_links: Whether to follow links
                - link_extractor: Pattern or function to extract links to follow
                - extractors: Data extractors for each page
                
        Returns:
            Dict[str, Any]: Crawling results with extracted data
        """
        if not self.initialized:
            await self.initialize()
        
        try:
            # Process options
            max_pages = options.get("max_pages", 10)
            max_depth = options.get("max_depth", 2)
            allowed_domains = options.get("allowed_domains", [])
            follow_links = options.get("follow_links", True)
            extractors = options.get("extractors", [])
            
            # Initialize crawl state
            visited_urls = set()
            queue = [(url, 0) for url in start_urls]  # (url, depth)
            results = {
                "pages_crawled": 0,
                "data": {}
            }
            
            # Create a URL filter function
            def url_filter(url):
                parsed_url = urllib.parse.urlparse(url)
                domain = parsed_url.netloc
                
                # Check if domain is allowed
                if allowed_domains and domain not in allowed_domains:
                    return False
                
                # Filter out non-HTTP/HTTPS URLs
                if not url.startswith(("http://", "https://")):
                    return False
                
                # Filter out URLs with fragments only
                base_url = url.split("#")[0]
                if base_url in visited_urls:
                    return False
                
                return True
            
            # Process queue
            while queue and results["pages_crawled"] < max_pages:
                # Get next URL to process
                url, depth = queue.pop(0)
                
                # Skip if already visited or exceeds depth
                if url in visited_urls or depth > max_depth:
                    continue
                
                # Mark as visited
                visited_urls.add(url)
                
                logger.info(f"Crawling {url} (depth {depth}, {results['pages_crawled'] + 1}/{max_pages})")
                
                # Fetch the page, with automatic backend selection
                page_result = await self.get(
                    url, 
                    collect_links=follow_links,
                    parse_html=bool(extractors),
                    **options.get("fetch_options", {})
                )
                
                if not page_result.get("success", False):
                    logger.warning(f"Failed to crawl {url}: {page_result.get('error', 'Unknown error')}")
                    continue
                
                # Extract structured data if extractors are defined
                page_data = {}
                if extractors:
                    extract_result = await self.extract_structured_data(page_result["data"], extractors)
                    if extract_result.get("success", False):
                        page_data = extract_result["data"]
                
                # Store results
                results["data"][url] = {
                    "title": page_data.get("title", ""),  # Try to get title from extracted data
                    "final_url": page_result.get("url", url),  # Final URL after redirects
                    "extracted_data": page_data,
                }
                
                results["pages_crawled"] += 1
                
                # Follow links if enabled and not at max depth
                if follow_links and depth < max_depth:
                    links = page_result.get("links", [])
                    
                    # Apply link extractor filter if defined
                    link_filter = options.get("link_extractor")
                    if link_filter:
                        if callable(link_filter):
                            links = [link for link in links if link_filter(link)]
                        elif isinstance(link_filter, str):
                            pattern = re.compile(link_filter)
                            links = [link for link in links if pattern.search(link.get("href", ""))]
                    
                    # Process links
                    for link in links:
                        href = link.get("href", "")
                        
                        # Handle relative URLs
                        full_url = urllib.parse.urljoin(url, href)
                        
                        # Filter URL
                        if url_filter(full_url):
                            queue.append((full_url, depth + 1))
            
            return {
                "success": True,
                "pages_visited": results["pages_crawled"],
                "data": results["data"],
                "total_urls_discovered": len(visited_urls)
            }
        
        except Exception as e:
            logger.exception(f"Error during crawl: {e}")
            return {"success": False, "error": str(e)}
    
    async def shutdown(self):
        """Shutdown the scraper and release resources."""
        if self.session:
            self.session.close()
            self.session = None
        
        logger.info("Advanced web scraper shutdown complete")