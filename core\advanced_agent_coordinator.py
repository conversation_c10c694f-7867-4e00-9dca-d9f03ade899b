"""
Advanced Agent Coordinator for the Multi-Agent AI System.

This module provides sophisticated coordination capabilities for multiple agents,
enabling complex workflows, collaborative problem-solving, and dynamic task allocation.
"""
import asyncio
import json
import logging
import os
import time
from typing import Dict, List, Optional, Any, Union, Set, Tuple
import uuid
from datetime import datetime, timedelta
import random
import networkx as nx

from core.logger import setup_logger
from core.state_manager import StateManager
from core.agent_coordinator import AgentCoordinator
from llm.llm_router import LLMRouter
from machine_learning.advanced_reasoning import AdvancedReasoning

# Set up logger
logger = setup_logger("advanced_agent_coordinator")

class AdvancedAgentCoordinator:
    """
    Advanced coordinator for multiple agents.

    This class provides sophisticated coordination capabilities for multiple agents,
    enabling complex workflows, collaborative problem-solving, and dynamic task allocation.
    """

    def __init__(
        self,
        state_manager: StateManager,
        llm_router: LLMRouter,
        agent_coordinator: AgentCoordinator,
        shutdown_event: asyncio.Event,
    ):
        """
        Initialize the advanced agent coordinator.

        Args:
            state_manager (StateManager): System state manager
            llm_router (LLMRouter): LLM router for generating responses
            agent_coordinator (AgentCoordinator): Basic agent coordinator
            shutdown_event (asyncio.Event): Event to signal system shutdown
        """
        self.state_manager = state_manager
        self.llm_router = llm_router
        self.agent_coordinator = agent_coordinator
        self.shutdown_event = shutdown_event

        # Coordination state
        self.workflows = {}
        self.tasks = {}
        self.agent_capabilities = {}
        self.agent_performance = {}
        self.agent_relationships = {}

        # Task allocation
        self.task_queue = asyncio.Queue()
        self.task_processor_task = None

        # Workflow execution
        self.workflow_executions = {}
        self.workflow_processor_task = None

        # Agent communication
        self.message_history = []
        self.communication_graph = nx.DiGraph()

        # Advanced reasoning
        self.advanced_reasoning = None

        logger.info("Advanced agent coordinator initialized")

    async def initialize(self):
        """Initialize the advanced agent coordinator."""
        try:
            # Initialize advanced reasoning
            self.advanced_reasoning = AdvancedReasoning(self.llm_router)
            await self.advanced_reasoning.initialize()

            # Load workflows
            await self._load_workflows()

            # Load agent capabilities
            await self._load_agent_capabilities()

            # Load agent performance
            await self._load_agent_performance()

            # Load agent relationships
            await self._load_agent_relationships()

            # Start task processor
            self.task_processor_task = asyncio.create_task(self._process_tasks())

            # Start workflow processor
            self.workflow_processor_task = asyncio.create_task(self._process_workflows())

            logger.info("Advanced agent coordinator initialized")

        except Exception as e:
            logger.exception(f"Error initializing advanced agent coordinator: {e}")

    async def shutdown(self):
        """Shutdown the advanced agent coordinator."""
        try:
            # Cancel task processor
            if self.task_processor_task:
                self.task_processor_task.cancel()
                try:
                    await self.task_processor_task
                except asyncio.CancelledError:
                    pass

            # Cancel workflow processor
            if self.workflow_processor_task:
                self.workflow_processor_task.cancel()
                try:
                    await self.workflow_processor_task
                except asyncio.CancelledError:
                    pass

            logger.info("Advanced agent coordinator shutdown")

        except Exception as e:
            logger.exception(f"Error shutting down advanced agent coordinator: {e}")

    async def _load_workflows(self):
        """Load workflows from state manager."""
        try:
            workflows = await self.state_manager.get_state("advanced_coordinator", "workflows")
            if workflows:
                self.workflows = workflows
                logger.info(f"Loaded {len(self.workflows)} workflows")
            else:
                logger.info("No workflows found")

        except Exception as e:
            logger.exception(f"Error loading workflows: {e}")

    async def _load_agent_capabilities(self):
        """Load agent capabilities from state manager."""
        try:
            # Get all agents
            agents = await self.state_manager.get_state("agents")

            # Extract capabilities
            for agent_id, agent_data in agents.items():
                if "capabilities" in agent_data:
                    self.agent_capabilities[agent_id] = agent_data["capabilities"]

            logger.info(f"Loaded capabilities for {len(self.agent_capabilities)} agents")

        except Exception as e:
            logger.exception(f"Error loading agent capabilities: {e}")

    async def _load_agent_performance(self):
        """Load agent performance metrics from state manager."""
        try:
            performance = await self.state_manager.get_state("advanced_coordinator", "agent_performance")
            if performance:
                self.agent_performance = performance
                logger.info(f"Loaded performance metrics for {len(self.agent_performance)} agents")
            else:
                logger.info("No agent performance metrics found")

        except Exception as e:
            logger.exception(f"Error loading agent performance: {e}")

    async def _load_agent_relationships(self):
        """Load agent relationships from state manager."""
        try:
            relationships = await self.state_manager.get_state("advanced_coordinator", "agent_relationships")
            if relationships:
                self.agent_relationships = relationships
                logger.info(f"Loaded {len(self.agent_relationships)} agent relationships")

                # Build communication graph
                self._build_communication_graph()
            else:
                logger.info("No agent relationships found")

        except Exception as e:
            logger.exception(f"Error loading agent relationships: {e}")

    def _build_communication_graph(self):
        """Build communication graph from agent relationships."""
        try:
            # Clear existing graph
            self.communication_graph.clear()

            # Add nodes (agents)
            for agent_id in self.agent_capabilities.keys():
                self.communication_graph.add_node(agent_id)

            # Add edges (relationships)
            for agent_id, relationships in self.agent_relationships.items():
                for related_agent_id, relationship in relationships.items():
                    # Add edge with relationship attributes
                    self.communication_graph.add_edge(
                        agent_id,
                        related_agent_id,
                        weight=relationship.get("strength", 1.0),
                        type=relationship.get("type", "general"),
                        last_interaction=relationship.get("last_interaction"),
                    )

            logger.info(f"Built communication graph with {self.communication_graph.number_of_nodes()} nodes and {self.communication_graph.number_of_edges()} edges")

        except Exception as e:
            logger.exception(f"Error building communication graph: {e}")

    async def create_workflow(
        self,
        name: str,
        description: str,
        steps: List[Dict],
        metadata: Optional[Dict] = None,
    ) -> str:
        """
        Create a new workflow.

        Args:
            name (str): Workflow name
            description (str): Workflow description
            steps (List[Dict]): Workflow steps
            metadata (Optional[Dict]): Additional workflow metadata

        Returns:
            str: Workflow ID
        """
        workflow_id = str(uuid.uuid4())

        # Create workflow
        workflow = {
            "id": workflow_id,
            "name": name,
            "description": description,
            "steps": steps,
            "metadata": metadata or {},
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "status": "active",
        }

        # Store workflow
        self.workflows[workflow_id] = workflow

        # Save to state manager
        await self.state_manager.update_state("advanced_coordinator", "workflows", self.workflows)

        logger.info(f"Created workflow: {name} ({workflow_id})")

        return workflow_id

    async def update_workflow(
        self,
        workflow_id: str,
        name: Optional[str] = None,
        description: Optional[str] = None,
        steps: Optional[List[Dict]] = None,
        metadata: Optional[Dict] = None,
        status: Optional[str] = None,
    ) -> bool:
        """
        Update an existing workflow.

        Args:
            workflow_id (str): Workflow ID
            name (Optional[str]): New workflow name
            description (Optional[str]): New workflow description
            steps (Optional[List[Dict]]): New workflow steps
            metadata (Optional[Dict]): New workflow metadata
            status (Optional[str]): New workflow status

        Returns:
            bool: True if successful, False otherwise
        """
        if workflow_id not in self.workflows:
            logger.warning(f"Workflow not found: {workflow_id}")
            return False

        workflow = self.workflows[workflow_id]

        # Update workflow
        if name is not None:
            workflow["name"] = name

        if description is not None:
            workflow["description"] = description

        if steps is not None:
            workflow["steps"] = steps

        if metadata is not None:
            workflow["metadata"] = metadata

        if status is not None:
            workflow["status"] = status

        workflow["updated_at"] = datetime.now().isoformat()

        # Store workflow
        self.workflows[workflow_id] = workflow

        # Save to state manager
        await self.state_manager.update_state("advanced_coordinator", "workflows", self.workflows)

        logger.info(f"Updated workflow: {workflow['name']} ({workflow_id})")

        return True

    async def delete_workflow(self, workflow_id: str) -> bool:
        """
        Delete a workflow.

        Args:
            workflow_id (str): Workflow ID

        Returns:
            bool: True if successful, False otherwise
        """
        if workflow_id not in self.workflows:
            logger.warning(f"Workflow not found: {workflow_id}")
            return False

        # Delete workflow
        del self.workflows[workflow_id]

        # Save to state manager
        await self.state_manager.update_state("advanced_coordinator", "workflows", self.workflows)

        logger.info(f"Deleted workflow: {workflow_id}")

        return True

    async def execute_workflow(
        self,
        workflow_id: str,
        input_data: Dict,
        execution_options: Optional[Dict] = None,
    ) -> str:
        """
        Execute a workflow.

        Args:
            workflow_id (str): Workflow ID
            input_data (Dict): Input data for the workflow
            execution_options (Optional[Dict]): Execution options

        Returns:
            str: Execution ID
        """
        if workflow_id not in self.workflows:
            raise ValueError(f"Workflow not found: {workflow_id}")

        workflow = self.workflows[workflow_id]

        if workflow["status"] != "active":
            raise ValueError(f"Workflow is not active: {workflow_id}")

        # Create execution ID
        execution_id = str(uuid.uuid4())

        # Create execution
        execution = {
            "id": execution_id,
            "workflow_id": workflow_id,
            "input_data": input_data,
            "options": execution_options or {},
            "status": "pending",
            "current_step": 0,
            "step_results": {},
            "started_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
        }

        # Store execution
        self.workflow_executions[execution_id] = execution

        # Save to state manager
        await self.state_manager.update_state("advanced_coordinator", "workflow_executions", self.workflow_executions)

        logger.info(f"Created workflow execution: {execution_id} for workflow {workflow_id}")

        return execution_id

    async def get_workflow_execution_status(self, execution_id: str) -> Dict:
        """
        Get the status of a workflow execution.

        Args:
            execution_id (str): Execution ID

        Returns:
            Dict: Execution status
        """
        if execution_id not in self.workflow_executions:
            raise ValueError(f"Workflow execution not found: {execution_id}")

        execution = self.workflow_executions[execution_id]

        return {
            "execution_id": execution_id,
            "workflow_id": execution["workflow_id"],
            "status": execution["status"],
            "current_step": execution["current_step"],
            "started_at": execution["started_at"],
            "updated_at": execution["updated_at"],
            "completed_at": execution.get("completed_at"),
            "error": execution.get("error"),
        }

    async def _process_workflows(self):
        """Process workflow executions."""
        try:
            while not self.shutdown_event.is_set():
                # Find pending executions
                pending_executions = [
                    execution_id for execution_id, execution in self.workflow_executions.items()
                    if execution["status"] == "pending"
                ]

                # Process each pending execution
                for execution_id in pending_executions:
                    try:
                        # Get execution
                        execution = self.workflow_executions[execution_id]

                        # Update status
                        execution["status"] = "running"
                        execution["updated_at"] = datetime.now().isoformat()

                        # Get workflow
                        workflow_id = execution["workflow_id"]
                        workflow = self.workflows[workflow_id]

                        # Execute workflow steps
                        result = execution["input_data"]

                        for i, step in enumerate(workflow["steps"]):
                            # Update current step
                            execution["current_step"] = i
                            execution["updated_at"] = datetime.now().isoformat()

                            # Save execution state
                            await self.state_manager.update_state("advanced_coordinator", "workflow_executions", self.workflow_executions)

                            # Execute step
                            try:
                                step_result = await self._execute_workflow_step(step, result, execution)

                                # Store step result
                                execution["step_results"][str(i)] = step_result

                                # Update result for next step
                                result = step_result

                            except Exception as e:
                                logger.exception(f"Error executing workflow step {i}: {e}")
                                execution["status"] = "failed"
                                execution["error"] = str(e)
                                execution["updated_at"] = datetime.now().isoformat()
                                break

                            # Check if shutdown requested
                            if self.shutdown_event.is_set():
                                execution["status"] = "cancelled"
                                execution["updated_at"] = datetime.now().isoformat()
                                break

                        # Update execution status
                        if execution["status"] == "running":
                            execution["status"] = "completed"
                            execution["result"] = result
                            execution["completed_at"] = datetime.now().isoformat()

                        execution["updated_at"] = datetime.now().isoformat()

                        # Save execution state
                        await self.state_manager.update_state("advanced_coordinator", "workflow_executions", self.workflow_executions)

                        logger.info(f"Workflow execution completed: {execution_id}")

                    except Exception as e:
                        logger.exception(f"Error processing workflow execution {execution_id}: {e}")

                        # Update execution status
                        execution = self.workflow_executions.get(execution_id)
                        if execution:
                            execution["status"] = "failed"
                            execution["error"] = str(e)
                            execution["updated_at"] = datetime.now().isoformat()

                            # Save execution state
                            await self.state_manager.update_state("advanced_coordinator", "workflow_executions", self.workflow_executions)

                # Wait before checking again
                await asyncio.sleep(1)

        except asyncio.CancelledError:
            logger.info("Workflow processor task cancelled")
        except Exception as e:
            logger.exception(f"Error in workflow processor: {e}")

    async def _execute_workflow_step(
        self,
        step: Dict,
        input_data: Dict,
        execution: Dict,
    ) -> Dict:
        """
        Execute a workflow step.

        Args:
            step (Dict): Workflow step
            input_data (Dict): Input data for the step
            execution (Dict): Workflow execution

        Returns:
            Dict: Step execution results
        """
        step_type = step.get("type")

        if step_type == "agent_task":
            return await self._execute_agent_task_step(step, input_data, execution)
        elif step_type == "reasoning":
            return await self._execute_reasoning_step(step, input_data, execution)
        elif step_type == "transformation":
            return await self._execute_transformation_step(step, input_data, execution)
        elif step_type == "condition":
            return await self._execute_condition_step(step, input_data, execution)
        elif step_type == "parallel":
            return await self._execute_parallel_step(step, input_data, execution)
        else:
            raise ValueError(f"Unknown step type: {step_type}")

    async def _execute_agent_task_step(
        self,
        step: Dict,
        input_data: Dict,
        execution: Dict,
    ) -> Dict:
        """
        Execute an agent task step.

        Args:
            step (Dict): Workflow step
            input_data (Dict): Input data for the step
            execution (Dict): Workflow execution

        Returns:
            Dict: Step execution results
        """
        # Get step parameters
        agent_id = step.get("agent_id")
        task_type = step.get("task_type")
        task_parameters = step.get("parameters", {})

        if not agent_id:
            raise ValueError("Agent ID not specified")

        if not task_type:
            raise ValueError("Task type not specified")

        # Merge input data with task parameters
        merged_parameters = task_parameters.copy()
        merged_parameters.update(input_data)

        # Create task
        task_id = await self.create_task(
            agent_id=agent_id,
            task_type=task_type,
            parameters=merged_parameters,
            priority=step.get("priority", "normal"),
            deadline=step.get("deadline"),
            workflow_execution_id=execution["id"],
        )

        # Wait for task completion
        task_result = await self.wait_for_task_completion(task_id)

        return task_result

    async def _execute_reasoning_step(
        self,
        step: Dict,
        input_data: Dict,
        execution: Dict,
    ) -> Dict:
        """
        Execute a reasoning step.

        Args:
            step (Dict): Workflow step
            input_data (Dict): Input data for the step
            execution (Dict): Workflow execution

        Returns:
            Dict: Step execution results
        """
        reasoning_type = step.get("reasoning_type")

        if not reasoning_type:
            raise ValueError("Reasoning type not specified")

        if reasoning_type == "causal":
            # Get parameters
            context = step.get("context", "")
            question = step.get("question", "")
            variables = step.get("variables", [])

            # Replace variables in context and question
            context = self._replace_variables(context, input_data)
            question = self._replace_variables(question, input_data)

            # Perform causal reasoning
            result = await self.advanced_reasoning.causal_reasoning(
                context=context,
                question=question,
                variables=variables,
            )

            return {
                "reasoning_type": "causal",
                "result": result,
            }

        elif reasoning_type == "counterfactual":
            # Get parameters
            context = step.get("context", "")
            factual_outcome = step.get("factual_outcome", "")
            counterfactual_condition = step.get("counterfactual_condition", "")

            # Replace variables in parameters
            context = self._replace_variables(context, input_data)
            factual_outcome = self._replace_variables(factual_outcome, input_data)
            counterfactual_condition = self._replace_variables(counterfactual_condition, input_data)

            # Perform counterfactual reasoning
            result = await self.advanced_reasoning.counterfactual_reasoning(
                context=context,
                factual_outcome=factual_outcome,
                counterfactual_condition=counterfactual_condition,
            )

            return {
                "reasoning_type": "counterfactual",
                "result": result,
            }

        else:
            raise ValueError(f"Unknown reasoning type: {reasoning_type}")

    async def _execute_transformation_step(
        self,
        step: Dict,
        input_data: Dict,
        execution: Dict,
    ) -> Dict:
        """
        Execute a transformation step.

        Args:
            step (Dict): Workflow step
            input_data (Dict): Input data for the step
            execution (Dict): Workflow execution

        Returns:
            Dict: Step execution results
        """
        transformation_type = step.get("transformation_type")

        if not transformation_type:
            raise ValueError("Transformation type not specified")

        if transformation_type == "filter":
            # Get filter criteria
            criteria = step.get("criteria", {})

            # Apply filter
            result = self._apply_filter(input_data, criteria)

            return {
                "transformation_type": "filter",
                "result": result,
            }

        elif transformation_type == "map":
            # Get mapping function
            mapping = step.get("mapping", {})

            # Apply mapping
            result = self._apply_mapping(input_data, mapping)

            return {
                "transformation_type": "map",
                "result": result,
            }

        elif transformation_type == "aggregate":
            # Get aggregation function
            aggregation = step.get("aggregation", {})

            # Apply aggregation
            result = self._apply_aggregation(input_data, aggregation)

            return {
                "transformation_type": "aggregate",
                "result": result,
            }

        else:
            raise ValueError(f"Unknown transformation type: {transformation_type}")

    async def _execute_condition_step(
        self,
        step: Dict,
        input_data: Dict,
        execution: Dict,
    ) -> Dict:
        """
        Execute a condition step.

        Args:
            step (Dict): Workflow step
            input_data (Dict): Input data for the step
            execution (Dict): Workflow execution

        Returns:
            Dict: Step execution results
        """
        # Get condition
        condition = step.get("condition", {})

        # Evaluate condition
        condition_result = self._evaluate_condition(input_data, condition)

        # Get true and false branches
        true_branch = step.get("true_branch", [])
        false_branch = step.get("false_branch", [])

        # Execute appropriate branch
        if condition_result:
            # Execute true branch
            result = input_data

            for i, branch_step in enumerate(true_branch):
                result = await self._execute_workflow_step(branch_step, result, execution)

            return {
                "condition_result": True,
                "branch": "true",
                "result": result,
            }
        else:
            # Execute false branch
            result = input_data

            for i, branch_step in enumerate(false_branch):
                result = await self._execute_workflow_step(branch_step, result, execution)

            return {
                "condition_result": False,
                "branch": "false",
                "result": result,
            }

    async def _execute_parallel_step(
        self,
        step: Dict,
        input_data: Dict,
        execution: Dict,
    ) -> Dict:
        """
        Execute parallel steps.

        Args:
            step (Dict): Workflow step
            input_data (Dict): Input data for the step
            execution (Dict): Workflow execution

        Returns:
            Dict: Step execution results
        """
        # Get parallel branches
        branches = step.get("branches", [])

        # Execute branches in parallel
        tasks = []

        for i, branch in enumerate(branches):
            task = asyncio.create_task(self._execute_branch(branch, input_data, execution))
            tasks.append(task)

        # Wait for all branches to complete
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Process results
        branch_results = {}

        for i, result in enumerate(results):
            if isinstance(result, Exception):
                branch_results[f"branch_{i}"] = {
                    "status": "failed",
                    "error": str(result),
                }
            else:
                branch_results[f"branch_{i}"] = {
                    "status": "completed",
                    "result": result,
                }

        return {
            "parallel_branches": len(branches),
            "branch_results": branch_results,
        }

    async def _execute_branch(
        self,
        branch: List[Dict],
        input_data: Dict,
        execution: Dict,
    ) -> Dict:
        """
        Execute a branch of steps.

        Args:
            branch (List[Dict]): Branch steps
            input_data (Dict): Input data for the branch
            execution (Dict): Workflow execution

        Returns:
            Dict: Branch execution results
        """
        result = input_data

        for i, step in enumerate(branch):
            result = await self._execute_workflow_step(step, result, execution)

        return result

    def _replace_variables(self, text: str, data: Dict) -> str:
        """
        Replace variables in text with values from data.

        Args:
            text (str): Text with variables
            data (Dict): Data with variable values

        Returns:
            str: Text with variables replaced
        """
        if not text:
            return text

        # Replace ${variable} with value
        for key, value in data.items():
            if isinstance(value, (str, int, float, bool)):
                text = text.replace(f"${{{key}}}", str(value))

        return text

    def _apply_filter(self, data: Dict, criteria: Dict) -> Dict:
        """
        Apply filter criteria to data.

        Args:
            data (Dict): Data to filter
            criteria (Dict): Filter criteria

        Returns:
            Dict: Filtered data
        """
        result = {}

        # Apply criteria to each item in data
        for key, value in data.items():
            if key in criteria:
                # Get criterion
                criterion = criteria[key]

                # Check if value matches criterion
                if self._matches_criterion(value, criterion):
                    result[key] = value
            else:
                # Include items without criteria
                result[key] = value

        return result

    def _matches_criterion(self, value: Any, criterion: Dict) -> bool:
        """
        Check if value matches criterion.

        Args:
            value (Any): Value to check
            criterion (Dict): Criterion to match

        Returns:
            bool: True if value matches criterion, False otherwise
        """
        operator = criterion.get("operator", "eq")
        target = criterion.get("value")

        if operator == "eq":
            return value == target
        elif operator == "ne":
            return value != target
        elif operator == "gt":
            return value > target
        elif operator == "ge":
            return value >= target
        elif operator == "lt":
            return value < target
        elif operator == "le":
            return value <= target
        elif operator == "in":
            return value in target
        elif operator == "contains":
            return target in value
        else:
            return False

    def _apply_mapping(self, data: Dict, mapping: Dict) -> Dict:
        """
        Apply mapping to data.

        Args:
            data (Dict): Data to map
            mapping (Dict): Mapping to apply

        Returns:
            Dict: Mapped data
        """
        result = {}

        # Apply mapping to each item in data
        for target_key, source_info in mapping.items():
            if isinstance(source_info, str):
                # Simple mapping
                source_key = source_info
                if source_key in data:
                    result[target_key] = data[source_key]
            elif isinstance(source_info, dict):
                # Complex mapping
                source_key = source_info.get("source")
                transform = source_info.get("transform")

                if source_key in data:
                    value = data[source_key]

                    if transform:
                        # Apply transformation
                        value = self._apply_transform(value, transform)

                    result[target_key] = value

        return result

    def _apply_transform(self, value: Any, transform: Dict) -> Any:
        """
        Apply transformation to value.

        Args:
            value (Any): Value to transform
            transform (Dict): Transformation to apply

        Returns:
            Any: Transformed value
        """
        transform_type = transform.get("type")

        if transform_type == "string":
            return str(value)
        elif transform_type == "int":
            return int(value)
        elif transform_type == "float":
            return float(value)
        elif transform_type == "bool":
            return bool(value)
        elif transform_type == "uppercase":
            return str(value).upper()
        elif transform_type == "lowercase":
            return str(value).lower()
        else:
            return value

    def _apply_aggregation(self, data: Dict, aggregation: Dict) -> Dict:
        """
        Apply aggregation to data.

        Args:
            data (Dict): Data to aggregate
            aggregation (Dict): Aggregation to apply

        Returns:
            Dict: Aggregated data
        """
        result = {}

        # Apply aggregation to each item in aggregation
        for target_key, agg_info in aggregation.items():
            agg_type = agg_info.get("type")
            sources = agg_info.get("sources", [])

            if agg_type == "sum":
                # Sum values
                result[target_key] = sum(data.get(source, 0) for source in sources)
            elif agg_type == "avg":
                # Average values
                values = [data.get(source, 0) for source in sources]
                result[target_key] = sum(values) / len(values) if values else 0
            elif agg_type == "min":
                # Minimum value
                values = [data.get(source) for source in sources if source in data]
                result[target_key] = min(values) if values else None
            elif agg_type == "max":
                # Maximum value
                values = [data.get(source) for source in sources if source in data]
                result[target_key] = max(values) if values else None
            elif agg_type == "count":
                # Count values
                result[target_key] = len([source for source in sources if source in data])
            elif agg_type == "concat":
                # Concatenate values
                separator = agg_info.get("separator", "")
                values = [str(data.get(source, "")) for source in sources if source in data]
                result[target_key] = separator.join(values)

        return result

    def _evaluate_condition(self, data: Dict, condition: Dict) -> bool:
        """
        Evaluate condition on data.

        Args:
            data (Dict): Data to evaluate
            condition (Dict): Condition to evaluate

        Returns:
            bool: True if condition is met, False otherwise
        """
        condition_type = condition.get("type")

        if condition_type == "simple":
            # Simple condition
            field = condition.get("field")
            operator = condition.get("operator", "eq")
            value = condition.get("value")

            if field in data:
                return self._matches_criterion(data[field], {"operator": operator, "value": value})
            else:
                return False

        elif condition_type == "and":
            # AND condition
            subconditions = condition.get("conditions", [])
            return all(self._evaluate_condition(data, subcondition) for subcondition in subconditions)

        elif condition_type == "or":
            # OR condition
            subconditions = condition.get("conditions", [])
            return any(self._evaluate_condition(data, subcondition) for subcondition in subconditions)

        elif condition_type == "not":
            # NOT condition
            subcondition = condition.get("condition", {})
            return not self._evaluate_condition(data, subcondition)

        else:
            return False

    async def create_task(
        self,
        agent_id: str,
        task_type: str,
        parameters: Dict,
        priority: str = "normal",
        deadline: Optional[str] = None,
        workflow_execution_id: Optional[str] = None,
    ) -> str:
        """
        Create a task for an agent.

        Args:
            agent_id (str): Agent ID
            task_type (str): Task type
            parameters (Dict): Task parameters
            priority (str): Task priority
            deadline (Optional[str]): Task deadline
            workflow_execution_id (Optional[str]): Workflow execution ID

        Returns:
            str: Task ID
        """
        task_id = str(uuid.uuid4())

        # Create task
        task = {
            "id": task_id,
            "agent_id": agent_id,
            "type": task_type,
            "parameters": parameters,
            "priority": priority,
            "deadline": deadline,
            "workflow_execution_id": workflow_execution_id,
            "status": "pending",
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
        }

        # Store task
        self.tasks[task_id] = task

        # Add to task queue
        await self.task_queue.put(task_id)

        # Save to state manager
        await self.state_manager.update_state("advanced_coordinator", "tasks", self.tasks)

        logger.info(f"Created task: {task_id} for agent {agent_id}")

        return task_id

    async def wait_for_task_completion(self, task_id: str, timeout: Optional[float] = None) -> Dict:
        """
        Wait for a task to complete.

        Args:
            task_id (str): Task ID
            timeout (Optional[float]): Timeout in seconds

        Returns:
            Dict: Task result
        """
        if task_id not in self.tasks:
            raise ValueError(f"Task not found: {task_id}")

        start_time = time.time()

        while True:
            # Get task
            task = self.tasks[task_id]

            # Check if task is completed
            if task["status"] in ["completed", "failed", "cancelled"]:
                return task.get("result", {})

            # Check timeout
            if timeout is not None and time.time() - start_time > timeout:
                raise TimeoutError(f"Timeout waiting for task {task_id}")

            # Wait before checking again
            await asyncio.sleep(0.5)

    async def _process_tasks(self):
        """Process tasks in the task queue."""
        try:
            while not self.shutdown_event.is_set():
                # Get task from queue
                task_id = await self.task_queue.get()

                try:
                    # Get task
                    task = self.tasks[task_id]

                    # Get agent
                    agent_id = task["agent_id"]

                    # Create message for agent
                    message = {
                        "id": str(uuid.uuid4()),
                        "sender_id": "advanced_coordinator",
                        "recipient_id": agent_id,
                        "type": "task",
                        "content": {
                            "task_id": task_id,
                            "task_type": task["type"],
                            "parameters": task["parameters"],
                        },
                        "timestamp": datetime.now().isoformat(),
                    }

                    # Update task status
                    task["status"] = "assigned"
                    task["updated_at"] = datetime.now().isoformat()

                    # Save task state
                    await self.state_manager.update_state("advanced_coordinator", "tasks", self.tasks)

                    # Send message to agent
                    # In a real implementation, this would use the message queue
                    # For now, we'll just simulate the agent processing the task

                    # Simulate agent processing
                    await self._simulate_agent_processing(task)

                    # Task queue done
                    self.task_queue.task_done()

                except Exception as e:
                    logger.exception(f"Error processing task {task_id}: {e}")

                    # Update task status
                    task = self.tasks.get(task_id)
                    if task:
                        task["status"] = "failed"
                        task["error"] = str(e)
                        task["updated_at"] = datetime.now().isoformat()

                        # Save task state
                        await self.state_manager.update_state("advanced_coordinator", "tasks", self.tasks)

                    # Task queue done
                    self.task_queue.task_done()

        except asyncio.CancelledError:
            logger.info("Task processor task cancelled")
        except Exception as e:
            logger.exception(f"Error in task processor: {e}")

    async def _simulate_agent_processing(self, task: Dict):
        """
        Simulate agent processing a task.

        Args:
            task (Dict): Task to process
        """
        # In a real implementation, the agent would process the task
        # For now, we'll just simulate the agent processing

        # Simulate processing time
        await asyncio.sleep(random.uniform(0.5, 2.0))

        # Update task status
        task["status"] = "completed"
        task["result"] = {
            "success": True,
            "message": f"Task {task['id']} completed successfully",
            "data": task["parameters"],
        }
        task["updated_at"] = datetime.now().isoformat()
        task["completed_at"] = datetime.now().isoformat()

        # Save task state
        await self.state_manager.update_state("advanced_coordinator", "tasks", self.tasks)

        logger.info(f"Task {task['id']} completed")

    async def get_agent_capabilities(self, agent_id: str) -> List[str]:
        """
        Get capabilities of an agent.

        Args:
            agent_id (str): Agent ID

        Returns:
            List[str]: Agent capabilities
        """
        return self.agent_capabilities.get(agent_id, [])

    async def find_agents_with_capability(self, capability: str) -> List[str]:
        """
        Find agents with a specific capability.

        Args:
            capability (str): Capability to find

        Returns:
            List[str]: Agent IDs with the capability
        """
        return [
            agent_id for agent_id, capabilities in self.agent_capabilities.items()
            if capability in capabilities
        ]

    async def find_best_agent_for_task(self, task_type: str, parameters: Dict) -> str:
        """
        Find the best agent for a task.

        Args:
            task_type (str): Task type
            parameters (Dict): Task parameters

        Returns:
            str: Agent ID
        """
        # Find agents with required capability
        capable_agents = await self.find_agents_with_capability(task_type)

        if not capable_agents:
            raise ValueError(f"No agent found with capability: {task_type}")

        # If only one agent is capable, return it
        if len(capable_agents) == 1:
            return capable_agents[0]

        # Find agent with best performance
        best_agent = None
        best_score = -1

        for agent_id in capable_agents:
            # Get agent performance
            performance = self.agent_performance.get(agent_id, {})

            # Calculate score based on performance metrics
            score = self._calculate_agent_score(agent_id, task_type, parameters, performance)

            if score > best_score:
                best_agent = agent_id
                best_score = score

        return best_agent or capable_agents[0]

    def _calculate_agent_score(
        self,
        agent_id: str,
        task_type: str,
        parameters: Dict,
        performance: Dict,
    ) -> float:
        """
        Calculate score for an agent.

        Args:
            agent_id (str): Agent ID
            task_type (str): Task type
            parameters (Dict): Task parameters
            performance (Dict): Agent performance metrics

        Returns:
            float: Agent score
        """
        # Base score
        score = 0.5

        # Task-specific performance
        task_performance = performance.get("tasks", {}).get(task_type, {})

        # Success rate
        success_rate = task_performance.get("success_rate", 0.5)
        score += success_rate * 0.3

        # Average completion time
        avg_time = task_performance.get("avg_completion_time", 60)
        time_score = 1.0 / (1.0 + avg_time / 60.0)  # Normalize time score
        score += time_score * 0.2

        return score
