@echo off
echo UI-TARS Screenshot Analyzer
echo =========================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Install required packages
echo Installing required packages...
pip install requests pyautogui >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing required packages...
    pip install requests pyautogui
    if %errorlevel% neq 0 (
        echo Failed to install required packages. Please check your internet connection.
        exit /b 1
    )
)

REM Ask for screenshot path or take screenshot
echo.
echo Choose an option:
echo 1. Analyze an existing screenshot
echo 2. Take a new screenshot and analyze it
echo.
set /p OPTION="Enter your choice (1-2): "

if "%OPTION%"=="1" (
    echo.
    echo Enter the path to the screenshot:
    set /p SCREENSHOT_PATH=""
    
    echo.
    echo Enter a prompt (or press Enter for default):
    set /p PROMPT=""
    
    if "%PROMPT%"=="" (
        python ui_tars_screenshot_analyzer.py --screenshot "%SCREENSHOT_PATH%"
    ) else (
        python ui_tars_screenshot_analyzer.py --screenshot "%SCREENSHOT_PATH%" --prompt "%PROMPT%"
    )
) else if "%OPTION%"=="2" (
    echo.
    echo Enter a prompt (or press Enter for default):
    set /p PROMPT=""
    
    echo.
    echo Taking screenshot in 3 seconds...
    timeout /t 3 >nul
    
    if "%PROMPT%"=="" (
        python ui_tars_screenshot_analyzer.py --take-screenshot
    ) else (
        python ui_tars_screenshot_analyzer.py --take-screenshot --prompt "%PROMPT%"
    )
) else (
    echo Invalid option. Please run the script again and choose 1 or 2.
    exit /b 1
)

echo.
if %errorlevel% equ 0 (
    echo Screenshot analysis completed.
) else (
    echo There were issues with the screenshot analysis.
)

echo.
pause
