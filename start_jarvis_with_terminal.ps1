# Start Jarvis with Terminal
# This script starts <PERSON> in a terminal window with a custom exit handler

# Set the current directory to the script directory
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location -Path $scriptPath

# Clear the terminal and set window title
Clear-Host
$host.UI.RawUI.WindowTitle = "Jarvis AI Interface"

# Function to display the <PERSON> welcome banner
function Show-JarvisBanner {
    $banner = @"
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║                      JARVIS AI INTERFACE WITH ALPHAEVOLVE                    ║
║                                                                              ║
║  Welcome to the Jarvis AI Interface with AlphaEvolve integration.            ║
║  This system provides advanced AI capabilities for:                          ║
║                                                                              ║
║  1. Insurance business operations and lead management                        ║
║  2. Trading and asset management                                             ║
║  3. Social media marketing and content creation                              ║
║  4. Music production and management                                          ║
║                                                                              ║
║  The system is ready to accept natural language commands.                    ║
║  Press Ctrl+Alt+X to exit and return to regular terminal.                    ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
"@
    Write-Host $banner -ForegroundColor Cyan
}

# Display the welcome banner
Show-JarvisBanner

# Register exit key handler (Ctrl+Alt+X)
# Note: This is a placeholder as PowerShell console doesn't directly support key combinations
# The actual implementation will be in the Python script

# Start Jarvis with AlphaEvolve integration in interactive mode
try {
    # Run the Python script with the --interactive and --auto-start flags
    python start_jarvis_with_alphaevolve.py --interactive --auto-start
} catch {
    Write-Host "Error starting Jarvis: $_" -ForegroundColor Red
    Write-Host "Press any key to exit..." -ForegroundColor Yellow
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}

# This part will execute after Jarvis exits
Write-Host "Jarvis has been closed. Returning to regular terminal..." -ForegroundColor Yellow
Start-Sleep -Seconds 2

# Clear the terminal
Clear-Host
