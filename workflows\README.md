# Multi-Agent Workflows

This directory contains workflow definitions for the Multi-Agent AI System. These workflows orchestrate complex interactions between multiple agents to accomplish sophisticated tasks.

## Overview

Workflows are defined in JSON format and executed by the Advanced Agent Coordinator. Each workflow consists of a series of steps that can include agent tasks, reasoning operations, transformations, conditions, and parallel execution branches.

## Available Workflows

### Insurance Claim Processing Workflow

**File:** `insurance_claim_workflow.json`

This workflow orchestrates the processing of an insurance claim, including:

1. Claim validation
2. Fraud detection using quantum computing
3. Risk assessment
4. Financial impact analysis
5. Decision making
6. Customer notification

The workflow integrates both the Insurance Agent and Trading Agent to provide comprehensive claim processing with financial impact analysis.

**Key Features:**
- Advanced fraud detection with quantum pattern recognition
- Parallel processing of financial impacts
- Causal reasoning for optimal decision making
- Conditional branching based on fraud and risk assessments

### Trading Strategy Workflow

**File:** `trading_strategy_workflow.json`

This workflow implements a sophisticated trading strategy that includes:

1. Market analysis
2. Sentiment analysis
3. Portfolio analysis
4. Strategy selection using causal reasoning
5. Risk assessment (both trading and insurance)
6. Portfolio optimization using quantum computing
7. Insurance coverage analysis
8. Trade execution

The workflow integrates both the Trading Agent and Insurance Agent to provide comprehensive trading strategy execution with risk management and insurance protection.

**Key Features:**
- Quantum portfolio optimization
- Parallel risk assessment from multiple perspectives
- Insurance coverage recommendations
- Conditional trade execution based on risk and return

## Workflow Structure

Each workflow is defined with the following structure:

```json
{
  "id": "workflow_id",
  "name": "Workflow Name",
  "description": "Workflow description",
  "version": "1.0",
  "created_at": "2023-05-01T12:00:00Z",
  "updated_at": "2023-05-01T12:00:00Z",
  "steps": [
    {
      "id": "step_1",
      "name": "Step Name",
      "description": "Step description",
      "type": "step_type",
      ...step-specific properties...
    },
    ...more steps...
  ],
  "input_schema": {
    ...JSON schema for workflow inputs...
  },
  "output_schema": {
    ...JSON schema for workflow outputs...
  }
}
```

## Step Types

### Agent Task

Assigns a task to an agent and waits for completion.

```json
{
  "type": "agent_task",
  "agent_id": "agent_id",
  "task_type": "task_type",
  "parameters": {
    ...task parameters...
  }
}
```

### Reasoning

Performs advanced reasoning on data.

```json
{
  "type": "reasoning",
  "reasoning_type": "causal",
  "context": "Context with ${variables}",
  "question": "Question with ${variables}",
  "variables": ["var1", "var2"]
}
```

### Transformation

Transforms data from one format to another.

```json
{
  "type": "transformation",
  "transformation_type": "map",
  "mapping": {
    "target_field": "source_field",
    "complex_field": {
      "source": "source_field",
      "transform": {
        "type": "transform_type"
      }
    }
  }
}
```

### Condition

Evaluates a condition and executes different branches based on the result.

```json
{
  "type": "condition",
  "condition": {
    "type": "simple",
    "field": "field_name",
    "operator": "eq",
    "value": "value"
  },
  "true_branch": [
    ...steps to execute if condition is true...
  ],
  "false_branch": [
    ...steps to execute if condition is false...
  ]
}
```

### Parallel

Executes multiple branches in parallel and waits for all to complete.

```json
{
  "type": "parallel",
  "branches": [
    [
      ...steps for branch 1...
    ],
    [
      ...steps for branch 2...
    ]
  ]
}
```

## Variable Substitution

Workflows support variable substitution using the `${variable_name}` syntax. Variables can come from:

1. Workflow input data
2. Results of previous steps
3. System-provided variables (e.g., `current_timestamp`)

## Execution

Workflows are executed by the Advanced Agent Coordinator:

```python
from core.advanced_agent_coordinator import AdvancedAgentCoordinator

# Initialize coordinator
coordinator = AdvancedAgentCoordinator(state_manager, llm_router, agent_coordinator, shutdown_event)
await coordinator.initialize()

# Execute workflow
execution_id = await coordinator.execute_workflow(
    workflow_id="insurance_claim_workflow",
    input_data={
        "claim_id": "CLM-12345",
        "policy_id": "POL-67890",
        "customer_id": "CUST-54321",
        "claim_amount": 5000.00,
        "preferred_payment_method": "direct_deposit"
    }
)

# Get execution status
status = await coordinator.get_workflow_execution_status(execution_id)
print(f"Workflow execution status: {status['status']}")
```

## Creating New Workflows

To create a new workflow:

1. Define the workflow JSON structure
2. Save it in the `workflows` directory
3. Register it with the Advanced Agent Coordinator

```python
# Load workflow from file
with open("workflows/my_workflow.json", "r") as f:
    workflow_def = json.load(f)

# Create workflow
workflow_id = await coordinator.create_workflow(
    name=workflow_def["name"],
    description=workflow_def["description"],
    steps=workflow_def["steps"],
    metadata={
        "version": workflow_def["version"],
        "input_schema": workflow_def["input_schema"],
        "output_schema": workflow_def["output_schema"]
    }
)
```

## Best Practices

1. **Modularity**: Design workflows with reusable steps
2. **Error Handling**: Include error handling branches
3. **Validation**: Define input and output schemas
4. **Documentation**: Document each step's purpose
5. **Testing**: Test workflows with various inputs
6. **Monitoring**: Include steps for logging and monitoring
7. **Security**: Be careful with sensitive data in workflows
