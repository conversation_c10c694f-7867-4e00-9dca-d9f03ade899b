@echo off
echo Start Alyssa's Insurance Drip Campaign with Fixed UI-TARS
echo ======================================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Ask for UI-TARS configuration path
echo Enter the path to UI-TARS configuration file (leave empty to auto-detect):
set /p CONFIG_PATH=""

REM Ask for UI-TARS installation path
echo Enter the path to UI-TARS executable (leave empty to auto-detect):
set /p UI_TARS_PATH=""

REM Run the campaign script
echo.
echo Starting Alyssa's insurance drip campaign...
echo.

set COMMAND=python start_alyssa_campaign_with_fixed_ui_tars.py

if not "%CONFIG_PATH%"=="" (
    set COMMAND=%COMMAND% --config "%CONFIG_PATH%"
)

if not "%UI_TARS_PATH%"=="" (
    set COMMAND=%COMMAND% --ui-tars-path "%UI_TARS_PATH%"
)

%COMMAND%

echo.
if %errorlevel% equ 0 (
    echo Alyssa's insurance drip campaign started successfully!
) else (
    echo There was an error starting Alyssa's insurance drip campaign.
)

echo.
pause
