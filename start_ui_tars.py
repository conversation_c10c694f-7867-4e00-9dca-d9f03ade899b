"""
Start UI-TARS with Enhanced Configuration

This script starts UI-TARS with enhanced configuration to fix browser integration issues.
It uses a more direct approach to start UI-TARS and verify it's running correctly.
"""
import os
import sys
import json
import time
import socket
import logging
import argparse
import platform
import subprocess
import requests
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("start_ui_tars.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("start_ui_tars")

def find_ui_tars_executable():
    """Find the UI-TARS executable."""
    logger.info("Searching for UI-TARS executable...")
    
    os_type = platform.system()
    
    if os_type == "Windows":
        # Common installation locations on Windows
        possible_paths = [
            os.path.join(os.environ.get("PROGRAMFILES", "C:\\Program Files"), "UI-TARS", "UI-TARS.exe"),
            os.path.join(os.environ.get("PROGRAMFILES(X86)", "C:\\Program Files (x86)"), "UI-TARS", "UI-TARS.exe"),
            os.path.join(os.environ.get("LOCALAPPDATA", "C:\\Users\\<USER>\\AppData\\Local".format(os.getlogin())), "UI-TARS", "UI-TARS.exe"),
            "UI-TARS.exe"
        ]
    elif os_type == "Darwin":  # macOS
        # Common installation locations on macOS
        possible_paths = [
            "/Applications/UI-TARS.app/Contents/MacOS/UI-TARS",
            os.path.expanduser("~/Applications/UI-TARS.app/Contents/MacOS/UI-TARS"),
        ]
    else:  # Linux
        # Common installation locations on Linux
        possible_paths = [
            "/usr/local/bin/ui-tars",
            "/usr/bin/ui-tars",
            os.path.expanduser("~/.local/bin/ui-tars"),
        ]
        
    # Check if any of the paths exist
    for path in possible_paths:
        if os.path.exists(path):
            logger.info(f"Found UI-TARS executable at: {path}")
            return path
            
    # Try to find in PATH
    try:
        if os_type == "Windows":
            result = subprocess.run(["where", "UI-TARS.exe"], capture_output=True, text=True)
        else:
            result = subprocess.run(["which", "ui-tars"], capture_output=True, text=True)
            
        if result.returncode == 0:
            path = result.stdout.strip()
            logger.info(f"Found UI-TARS executable in PATH: {path}")
            return path
    except Exception as e:
        logger.debug(f"Error searching for UI-TARS in PATH: {e}")
        
    logger.warning("Could not find UI-TARS executable")
    return None

def find_ui_tars_config():
    """Find the UI-TARS configuration file."""
    logger.info("Searching for UI-TARS configuration file...")
    
    # Check common locations
    possible_paths = [
        "config/ui_tars_config.json",
        "ui_tars/config.json",
        "ui_tars_config.json",
        "ui_tars_config_v2.json"
    ]
    
    # Check if any of the paths exist
    for path in possible_paths:
        if os.path.exists(path):
            logger.info(f"Found UI-TARS configuration at: {path}")
            return path
            
    logger.warning("Could not find UI-TARS configuration file")
    return None

def detect_browsers():
    """Detect installed browsers."""
    logger.info("Detecting installed browsers...")
    
    browsers = {}
    os_type = platform.system()
    
    if os_type == "Windows":
        # Check for common browsers on Windows
        paths = [
            (r"C:\Program Files\Google\Chrome\Application\chrome.exe", "chrome"),
            (r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe", "chrome"),
            (r"C:\Program Files\Mozilla Firefox\firefox.exe", "firefox"),
            (r"C:\Program Files (x86)\Mozilla Firefox\firefox.exe", "firefox"),
            (r"C:\Program Files\Microsoft\Edge\Application\msedge.exe", "edge"),
            (r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe", "edge"),
            (r"C:\Program Files\BraveSoftware\Brave-Browser\Application\brave.exe", "brave"),
            (r"C:\Program Files (x86)\BraveSoftware\Brave-Browser\Application\brave.exe", "brave"),
        ]
    elif os_type == "Darwin":  # macOS
        # Check for common browsers on macOS
        paths = [
            ("/Applications/Google Chrome.app/Contents/MacOS/Google Chrome", "chrome"),
            ("/Applications/Firefox.app/Contents/MacOS/firefox", "firefox"),
            ("/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge", "edge"),
            ("/Applications/Brave Browser.app/Contents/MacOS/Brave Browser", "brave"),
        ]
    else:  # Linux
        # Check for common browsers on Linux
        paths = [
            ("/usr/bin/google-chrome", "chrome"),
            ("/usr/bin/firefox", "firefox"),
            ("/usr/bin/microsoft-edge", "edge"),
            ("/usr/bin/brave-browser", "brave"),
        ]
        
    # Check if any of the paths exist
    for path, browser_type in paths:
        if os.path.exists(path):
            browsers[browser_type] = path
            logger.info(f"Found {browser_type} browser at: {path}")
            
    return browsers

def check_port_open(host, port, timeout=5):
    """Check if a port is open."""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(timeout)
            result = s.connect_ex((host, port))
            return result == 0
    except Exception as e:
        logger.debug(f"Error checking port {port}: {e}")
        return False

def check_ui_tars_api(host="localhost", port=8080, max_retries=10, retry_delay=2):
    """Check if the UI-TARS API is running."""
    logger.info(f"Checking if UI-TARS API is running on {host}:{port}...")
    
    for retry in range(max_retries):
        if check_port_open(host, port):
            logger.info(f"Port {port} is open")
            
            try:
                # Try different API endpoints
                endpoints = [
                    f"http://{host}:{port}/health",
                    f"http://{host}:{port}/v1/models",
                    f"http://{host}:{port}/api/status"
                ]
                
                for endpoint in endpoints:
                    try:
                        logger.info(f"Trying endpoint: {endpoint}")
                        response = requests.get(endpoint, timeout=5)
                        
                        if response.status_code < 400:
                            logger.info(f"UI-TARS API is running (endpoint: {endpoint})")
                            return True
                    except requests.exceptions.RequestException:
                        continue
            except Exception as e:
                logger.debug(f"Error checking API endpoints: {e}")
        
        if retry < max_retries - 1:
            logger.info(f"UI-TARS API not detected, retrying in {retry_delay} seconds (attempt {retry + 1}/{max_retries})...")
            time.sleep(retry_delay)
    
    logger.warning(f"UI-TARS API not running on {host}:{port} after {max_retries} attempts")
    return False

def update_ui_tars_config(config_path, browser_type, browser_path):
    """Update UI-TARS configuration with browser information."""
    logger.info(f"Updating UI-TARS configuration at {config_path}...")
    
    config_data = {}
    
    # Load existing configuration if available
    if os.path.exists(config_path):
        try:
            with open(config_path, "r") as f:
                config_data = json.load(f)
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            config_data = {}
            
    # Create new configuration if needed
    if not config_data:
        config_data = {
            "ui_tars": {
                "version": "1.5",
                "enabled": True
            }
        }
        
    # Update browser configuration
    if "ui_tars" not in config_data:
        config_data["ui_tars"] = {}
        
    if "browser" not in config_data["ui_tars"]:
        config_data["ui_tars"]["browser"] = {}
        
    # Update browser settings
    config_data["ui_tars"]["browser"].update({
        "type": browser_type,
        "executable_path": browser_path,
        "user_data_dir": os.path.join(os.environ.get("LOCALAPPDATA", ""), "UI-TARS", "browser_data"),
        "profile_directory": "Default",
        "detection": {
            "auto_detect": True,
            "fallback_types": ["chrome", "edge", "firefox", "brave"]
        }
    })
    
    # Add debug settings
    if "debug" not in config_data["ui_tars"]:
        config_data["ui_tars"]["debug"] = {}
        
    config_data["ui_tars"]["debug"].update({
        "enabled": True,
        "log_level": "debug",
        "log_file": "ui_tars_debug.log"
    })
    
    # Save updated configuration
    try:
        with open(config_path, "w") as f:
            json.dump(config_data, f, indent=2)
            
        logger.info(f"Updated configuration saved to: {config_path}")
        return True
        
    except Exception as e:
        logger.error(f"Error saving configuration: {e}")
        return False

def start_ui_tars(executable_path, config_path=None, wait_for_api=True):
    """Start UI-TARS with the given configuration."""
    logger.info(f"Starting UI-TARS from: {executable_path}")
    
    # Prepare command
    command = [executable_path]
    
    if config_path:
        command.extend(["--config", config_path])
        
    # Add additional options
    command.extend(["--debug", "--api"])
    
    # Start UI-TARS process
    try:
        # For Windows, use subprocess.Popen with CREATE_NO_WINDOW flag
        if platform.system() == "Windows":
            import subprocess
            CREATE_NO_WINDOW = 0x08000000
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                creationflags=CREATE_NO_WINDOW
            )
        else:
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
        
        # Wait a bit for the process to start
        time.sleep(5)
        
        # Check if process is still running
        if process.poll() is not None:
            logger.error(f"UI-TARS process exited with code: {process.returncode}")
            return False
            
        logger.info("UI-TARS started successfully")
        
        # Wait for API to be available if requested
        if wait_for_api:
            api_running = check_ui_tars_api(max_retries=15, retry_delay=2)
            if not api_running:
                logger.warning("UI-TARS API did not start properly")
                return False
                
        return True
        
    except Exception as e:
        logger.exception(f"Error starting UI-TARS: {e}")
        return False

def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Start UI-TARS with Enhanced Configuration")
    parser.add_argument("--path", type=str, help="Path to UI-TARS executable")
    parser.add_argument("--config", type=str, help="Path to UI-TARS configuration file")
    parser.add_argument("--browser", type=str, choices=["chrome", "edge", "firefox", "brave"], help="Type of browser to use")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    
    args = parser.parse_args()
    
    # Set log level
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        
    print("Starting UI-TARS with Enhanced Configuration")
    print("==========================================")
    print()
    
    # Find UI-TARS executable
    ui_tars_path = args.path or find_ui_tars_executable()
    if not ui_tars_path:
        print("❌ UI-TARS executable not found")
        print("Please install UI-TARS or provide the correct path with --path")
        return 1
        
    print(f"✅ UI-TARS executable found at: {ui_tars_path}")
    
    # Find UI-TARS configuration
    config_path = args.config or find_ui_tars_config()
    if not config_path:
        print("⚠️ UI-TARS configuration not found, will create a new one")
        config_path = "ui_tars_config.json"
    else:
        print(f"✅ UI-TARS configuration found at: {config_path}")
        
    # Detect browsers
    browsers = detect_browsers()
    if not browsers:
        print("❌ No browsers detected")
        print("Please install a supported browser (Chrome, Edge, Firefox, or Brave)")
        return 1
        
    print(f"✅ Found {len(browsers)} browsers:")
    for browser_type, path in browsers.items():
        print(f"  - {browser_type}: {path}")
        
    # Select browser
    selected_browser = None
    selected_path = None
    
    if args.browser and args.browser in browsers:
        selected_browser = args.browser
        selected_path = browsers[args.browser]
    elif "chrome" in browsers:
        selected_browser = "chrome"
        selected_path = browsers["chrome"]
    elif browsers:
        # Use first available browser
        selected_browser = next(iter(browsers.keys()))
        selected_path = browsers[selected_browser]
        
    if not selected_browser:
        print("❌ No suitable browser selected")
        return 1
        
    print(f"✅ Selected browser: {selected_browser} at {selected_path}")
    
    # Update UI-TARS configuration
    success = update_ui_tars_config(config_path, selected_browser, selected_path)
    if not success:
        print("❌ Failed to update UI-TARS configuration")
        return 1
        
    print(f"✅ Updated UI-TARS configuration at: {config_path}")
    
    # Check if UI-TARS API is already running
    api_running = check_ui_tars_api(max_retries=1, retry_delay=1)
    if api_running:
        print("✅ UI-TARS API is already running")
        return 0
        
    # Start UI-TARS
    success = start_ui_tars(ui_tars_path, config_path)
    if not success:
        print("❌ Failed to start UI-TARS")
        return 1
        
    print("✅ UI-TARS started successfully")
    print("✅ UI-TARS API is running")
    
    print()
    print("UI-TARS is now running with enhanced configuration.")
    print("You can use it with your AI agent system.")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
