Write-Host "Starting Enhanced UI-TARS Integration..." -ForegroundColor Cyan

# Set the current directory to the script directory
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location -Path $scriptPath

# Start Enhanced UI-TARS Integration
python start_enhanced_ui_tars.py --all

Write-Host "Press any key to exit..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
