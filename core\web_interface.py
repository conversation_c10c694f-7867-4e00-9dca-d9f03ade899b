"""
Web interface for the Multi-Agent AI System.
"""
import asyncio
from typing import Dict, List, Optional
import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, status, Request
from fastapi.responses import J<PERSON>NResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel
import jwt
from datetime import datetime, timedelta

import config
from core.logger import setup_logger
from core.state_manager import StateManager
from core.agent_manager import AgentManager

# Set up logger
logger = setup_logger("web_interface")

# Create FastAPI app
app = FastAPI(
    title=config.SYSTEM_NAME,
    description="Web interface for the Multi-Agent AI System",
    version=config.VERSION,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, restrict this to specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Templates setup
templates = Jinja2Templates(directory="templates")

# Global references to managers
state_manager_ref = None
agent_manager_ref = None

# Authentication models
class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None

class User(BaseModel):
    username: str
    disabled: Optional[bool] = None

class UserInDB(User):
    hashed_password: str

# Authentication functions
def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(seconds=config.SECURITY_CONFIG["jwt_expiration"])
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(
        to_encode, 
        config.SECURITY_CONFIG["jwt_secret"], 
        algorithm=config.SECURITY_CONFIG["jwt_algorithm"]
    )
    return encoded_jwt

async def get_current_user(token: str):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(
            token, 
            config.SECURITY_CONFIG["jwt_secret"], 
            algorithms=[config.SECURITY_CONFIG["jwt_algorithm"]]
        )
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_data = TokenData(username=username)
    except jwt.PyJWTError:
        raise credentials_exception
    return token_data

# API routes
@app.get("/")
async def root(request: Request):
    """Root endpoint that redirects to the dashboard."""
    return {"message": f"Welcome to {config.SYSTEM_NAME} API"}

@app.get("/api/status")
async def get_status():
    """Get system status."""
    if state_manager_ref is None:
        return {"status": "initializing"}
    
    system_state = await state_manager_ref.get_state("system")
    return {
        "status": "running",
        "version": config.VERSION,
        "start_time": system_state.get("start_time"),
        "uptime_seconds": (
            datetime.now() - 
            datetime.fromisoformat(system_state.get("start_time"))
        ).total_seconds(),
    }

@app.get("/api/agents")
async def get_agents():
    """Get all agents and their status."""
    if agent_manager_ref is None:
        return {"agents": []}
    
    agents = agent_manager_ref.get_all_agents()
    agent_info = {}
    
    for agent_id, agent in agents.items():
        agent_info[agent_id] = {
            "name": agent.name,
            "description": agent.description,
            "status": agent.status,
            "last_active": agent.last_active.isoformat() if agent.last_active else None,
        }
    
    return {"agents": agent_info}

@app.get("/api/agents/{agent_id}")
async def get_agent(agent_id: str):
    """Get information about a specific agent."""
    if agent_manager_ref is None:
        raise HTTPException(status_code=503, detail="Agent manager not available")
    
    agent = agent_manager_ref.get_agent(agent_id)
    if agent is None:
        raise HTTPException(status_code=404, detail=f"Agent {agent_id} not found")
    
    return {
        "id": agent_id,
        "name": agent.name,
        "description": agent.description,
        "status": agent.status,
        "last_active": agent.last_active.isoformat() if agent.last_active else None,
        "config": agent.config,
    }

@app.post("/api/agents/{agent_id}/message")
async def send_message_to_agent(agent_id: str, message: Dict):
    """Send a message to a specific agent."""
    if agent_manager_ref is None:
        raise HTTPException(status_code=503, detail="Agent manager not available")
    
    agent = agent_manager_ref.get_agent(agent_id)
    if agent is None:
        raise HTTPException(status_code=404, detail=f"Agent {agent_id} not found")
    
    # Add sender and recipient to message
    message["sender_id"] = "web_interface"
    message["recipient_id"] = agent_id
    message["timestamp"] = datetime.now().isoformat()
    
    # Send message
    await agent_manager_ref.send_message(message)
    
    return {"status": "message sent"}

# Error handlers
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler."""
    logger.exception(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={"detail": str(exc)},
    )

# Serve static files in production
if not config.DEBUG:
    app.mount("/static", StaticFiles(directory="static"), name="static")

async def start_web_interface(
    agent_manager: AgentManager,
    state_manager: StateManager,
    host: str = "0.0.0.0",
    port: int = 8000,
    debug: bool = False
):
    """
    Start the web interface.
    
    Args:
        agent_manager (AgentManager): Agent manager instance
        state_manager (StateManager): State manager instance
        host (str): Host to bind to
        port (int): Port to bind to
        debug (bool): Whether to run in debug mode
    """
    global agent_manager_ref, state_manager_ref
    
    # Set global references
    agent_manager_ref = agent_manager
    state_manager_ref = state_manager
    
    # Configure Uvicorn server
    config = uvicorn.Config(
        app=app,
        host=host,
        port=port,
        log_level="debug" if debug else "info",
        reload=debug,
    )
    
    # Start server
    server = uvicorn.Server(config)
    await server.serve()

    # Reset global references on shutdown
    agent_manager_ref = None
    state_manager_ref = None
