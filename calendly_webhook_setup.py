"""
Calendly Webhook Setup

This script sets up webhook subscriptions for Calendly events.
"""
import os
import sys
import json
import asyncio
import argparse
import requests
from typing import List, Dict, Any

def create_webhook_subscription(
    token: str,
    org_uuid: str,
    user_uuid: str,
    webhook_url: str,
    events: List[str] = None
) -> Dict[str, Any]:
    """
    Create a webhook subscription for Calendly events.
    
    Args:
        token (str): Calendly OAuth token
        org_uuid (str): Organization UUID
        user_uuid (str): User UUID
        webhook_url (str): Webhook endpoint URL
        events (List[str], optional): List of events to subscribe to
        
    Returns:
        Dict[str, Any]: Response from Calendly API
    """
    if events is None:
        events = ['invitee.created', 'invitee.canceled']
    
    base_url = 'https://api.calendly.com'
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    }
    
    org_uri = f'https://api.calendly.com/organizations/{org_uuid}'
    user_uri = f'https://api.calendly.com/users/{user_uuid}'
    
    payload = {
        'url': webhook_url,
        'events': events,
        'organization': org_uri,
        'user': user_uri,
        'scope': 'user'
    }
    
    response = requests.post(
        f'{base_url}/webhook_subscriptions',
        headers=headers,
        json=payload
    )
    
    if response.status_code == 201:
        print("Webhook subscription created successfully!")
        return response.json()
    else:
        print(f"Error creating webhook subscription: {response.status_code}")
        print(response.text)
        return None

def list_webhook_subscriptions(token: str) -> List[Dict[str, Any]]:
    """
    List all webhook subscriptions.
    
    Args:
        token (str): Calendly OAuth token
        
    Returns:
        List[Dict[str, Any]]: List of webhook subscriptions
    """
    base_url = 'https://api.calendly.com'
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    }
    
    response = requests.get(
        f'{base_url}/webhook_subscriptions',
        headers=headers
    )
    
    if response.status_code == 200:
        return response.json().get('collection', [])
    else:
        print(f"Error listing webhook subscriptions: {response.status_code}")
        print(response.text)
        return []

def delete_webhook_subscription(token: str, webhook_uuid: str) -> bool:
    """
    Delete a webhook subscription.
    
    Args:
        token (str): Calendly OAuth token
        webhook_uuid (str): Webhook subscription UUID
        
    Returns:
        bool: Success status
    """
    base_url = 'https://api.calendly.com'
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    }
    
    response = requests.delete(
        f'{base_url}/webhook_subscriptions/{webhook_uuid}',
        headers=headers
    )
    
    if response.status_code == 204:
        print(f"Webhook subscription {webhook_uuid} deleted successfully!")
        return True
    else:
        print(f"Error deleting webhook subscription: {response.status_code}")
        print(response.text)
        return False

def get_user_info(token: str) -> Dict[str, Any]:
    """
    Get user information.
    
    Args:
        token (str): Calendly OAuth token
        
    Returns:
        Dict[str, Any]: User information
    """
    base_url = 'https://api.calendly.com'
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    }
    
    response = requests.get(
        f'{base_url}/users/me',
        headers=headers
    )
    
    if response.status_code == 200:
        return response.json()
    else:
        print(f"Error getting user information: {response.status_code}")
        print(response.text)
        return None

def get_organization_memberships(token: str) -> List[Dict[str, Any]]:
    """
    Get organization memberships.
    
    Args:
        token (str): Calendly OAuth token
        
    Returns:
        List[Dict[str, Any]]: List of organization memberships
    """
    base_url = 'https://api.calendly.com'
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    }
    
    response = requests.get(
        f'{base_url}/organization_memberships',
        headers=headers
    )
    
    if response.status_code == 200:
        return response.json().get('collection', [])
    else:
        print(f"Error getting organization memberships: {response.status_code}")
        print(response.text)
        return []

def main():
    """Run the Calendly webhook setup."""
    parser = argparse.ArgumentParser(description="Calendly Webhook Setup")
    parser.add_argument("--token", type=str, help="Calendly OAuth token")
    parser.add_argument("--webhook-url", type=str, help="Webhook endpoint URL")
    parser.add_argument("--list", action="store_true", help="List webhook subscriptions")
    parser.add_argument("--delete", type=str, help="Delete webhook subscription by UUID")
    parser.add_argument("--user-info", action="store_true", help="Get user information")
    parser.add_argument("--org-memberships", action="store_true", help="Get organization memberships")
    args = parser.parse_args()
    
    # If token is not provided, try to load from credentials file
    token = args.token
    if not token:
        try:
            with open("credentials/calendly/calendly.json", "r") as f:
                credentials = json.load(f)
                token = credentials.get("api_key", "")
        except Exception as e:
            print(f"Error loading credentials: {e}")
    
    if not token:
        print("Calendly OAuth token is required")
        print("Please provide a token using the --token argument")
        print("You can find your token in your Calendly account settings")
        return
    
    # Get user information
    if args.user_info:
        user_info = get_user_info(token)
        if user_info:
            print("User Information:")
            print(f"Name: {user_info.get('resource', {}).get('name')}")
            print(f"Email: {user_info.get('resource', {}).get('email')}")
            print(f"URI: {user_info.get('resource', {}).get('uri')}")
            
            # Extract user UUID from URI
            uri = user_info.get('resource', {}).get('uri', '')
            user_uuid = uri.split('/')[-1] if uri else ''
            print(f"UUID: {user_uuid}")
            
            # Save user information to credentials file
            try:
                with open("credentials/calendly/calendly.json", "r") as f:
                    credentials = json.load(f)
                
                credentials["user_uri"] = uri
                credentials["user_uuid"] = user_uuid
                
                with open("credentials/calendly/calendly.json", "w") as f:
                    json.dump(credentials, f, indent=4)
                
                print("User information saved to credentials file")
            except Exception as e:
                print(f"Error saving user information: {e}")
    
    # Get organization memberships
    if args.org_memberships:
        org_memberships = get_organization_memberships(token)
        if org_memberships:
            print("Organization Memberships:")
            for i, membership in enumerate(org_memberships):
                org_uri = membership.get('organization', '')
                org_uuid = org_uri.split('/')[-1] if org_uri else ''
                print(f"{i+1}. Organization URI: {org_uri}")
                print(f"   UUID: {org_uuid}")
                
                # Save organization information to credentials file
                try:
                    with open("credentials/calendly/calendly.json", "r") as f:
                        credentials = json.load(f)
                    
                    credentials["organization_uri"] = org_uri
                    credentials["organization_uuid"] = org_uuid
                    
                    with open("credentials/calendly/calendly.json", "w") as f:
                        json.dump(credentials, f, indent=4)
                    
                    print("Organization information saved to credentials file")
                except Exception as e:
                    print(f"Error saving organization information: {e}")
    
    # List webhook subscriptions
    if args.list:
        webhooks = list_webhook_subscriptions(token)
        if webhooks:
            print("Webhook Subscriptions:")
            for i, webhook in enumerate(webhooks):
                print(f"{i+1}. UUID: {webhook.get('uri', '').split('/')[-1]}")
                print(f"   URL: {webhook.get('callback_url')}")
                print(f"   Events: {', '.join(webhook.get('events', []))}")
                print(f"   Scope: {webhook.get('scope')}")
                print(f"   Created At: {webhook.get('created_at')}")
    
    # Delete webhook subscription
    if args.delete:
        delete_webhook_subscription(token, args.delete)
    
    # Create webhook subscription
    if args.webhook_url:
        # Get user UUID and organization UUID
        user_uuid = None
        org_uuid = None
        
        try:
            with open("credentials/calendly/calendly.json", "r") as f:
                credentials = json.load(f)
                user_uuid = credentials.get("user_uuid", "")
                org_uuid = credentials.get("organization_uuid", "")
        except Exception as e:
            print(f"Error loading credentials: {e}")
        
        if not user_uuid:
            user_info = get_user_info(token)
            if user_info:
                uri = user_info.get('resource', {}).get('uri', '')
                user_uuid = uri.split('/')[-1] if uri else ''
        
        if not org_uuid:
            org_memberships = get_organization_memberships(token)
            if org_memberships:
                org_uri = org_memberships[0].get('organization', '')
                org_uuid = org_uri.split('/')[-1] if org_uri else ''
        
        if not user_uuid or not org_uuid:
            print("User UUID and Organization UUID are required")
            print("Please run with --user-info and --org-memberships first")
            return
        
        # Create webhook subscription
        result = create_webhook_subscription(
            token=token,
            org_uuid=org_uuid,
            user_uuid=user_uuid,
            webhook_url=args.webhook_url
        )
        
        if result:
            print("Webhook subscription created:")
            print(json.dumps(result, indent=2))

if __name__ == "__main__":
    main()
