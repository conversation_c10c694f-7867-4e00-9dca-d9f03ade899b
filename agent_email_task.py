#!/usr/bin/env python3
"""
Agent Email Task - Integrates with the agent system to send an email to Alyssa C.
using UI-TARS 1.5 browser automation.
"""

import os
import sys
import time
import json
import logging
import importlib.util
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("AgentEmailTask")

# Try to import from the agent system
try:
    # Add parent directory to path to import agent system modules
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    # Import agent system modules
    from agents.base_agent import BaseAgent
    from core.task_manager import TaskManager
    from services.email_service import EmailService
    from utils.browser_utils import BrowserUtils
    
    AGENT_SYSTEM_AVAILABLE = True
    logger.info("Agent system modules imported successfully")
except ImportError as e:
    logger.warning(f"Could not import agent system modules: {e}")
    AGENT_SYSTEM_AVAILABLE = False

# Import the UI-TARS email sender
spec = importlib.util.spec_from_file_location("send_email_with_uitars", 
                                             "send_email_with_uitars.py")
email_sender_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(email_sender_module)

class EmailAgent(BaseAgent if AGENT_SYSTEM_AVAILABLE else object):
    """Email Agent that uses UI-TARS 1.5 to send emails"""
    
    def __init__(self, name="EmailAgent"):
        if AGENT_SYSTEM_AVAILABLE:
            super().__init__(name=name)
        else:
            self.name = name
        
        self.logger = logging.getLogger(f"AgentSystem.{name}")
        self.logger.info(f"Initializing {name}")
    
    def send_urgent_email(self, recipient, subject, body):
        """Send an urgent email to a recipient"""
        self.logger.info(f"Sending urgent email to {recipient}")
        
        # Log the task
        self.log_task_start("send_urgent_email", {
            "recipient": recipient,
            "subject": subject,
            "body_length": len(body)
        })
        
        # Use UI-TARS to send the email
        try:
            # Use the imported module to send the email
            success = email_sender_module.send_email_with_uitars()
            
            if success:
                self.logger.info(f"Email sent successfully to {recipient}")
                self.log_task_complete("send_urgent_email", {"status": "success"})
                return True
            else:
                self.logger.error(f"Failed to send email to {recipient}")
                self.log_task_complete("send_urgent_email", {"status": "failed"})
                return False
                
        except Exception as e:
            self.logger.error(f"Error sending email: {e}")
            self.log_task_complete("send_urgent_email", {
                "status": "error",
                "error": str(e)
            })
            return False
    
    def log_task_start(self, task_name, params=None):
        """Log the start of a task"""
        if AGENT_SYSTEM_AVAILABLE:
            # Use the agent system's task logging
            super().log_task_start(task_name, params)
        else:
            # Simple logging if agent system is not available
            self.logger.info(f"Starting task: {task_name} with params: {params}")
    
    def log_task_complete(self, task_name, result=None):
        """Log the completion of a task"""
        if AGENT_SYSTEM_AVAILABLE:
            # Use the agent system's task logging
            super().log_task_complete(task_name, result)
        else:
            # Simple logging if agent system is not available
            self.logger.info(f"Completed task: {task_name} with result: {result}")

def main():
    """Main function to run the email agent"""
    logger.info("Starting Email Agent to send urgent email to Alyssa C.")
    
    # Email content
    recipient = "<EMAIL>"  # Replace with actual email
    subject = "URGENT: Your Insurance Options - Coverage Available Within Your $100 Monthly Budget"
    body = """
Hi Alyssa,

I hope this message finds you well. We've been trying to reach you through multiple channels (email, phone calls, voicemails, and texts) regarding your insurance needs, and I wanted to follow up personally as this is time-sensitive.

Based on your specific situation and $100 monthly budget, we have options ready for you that provide excellent coverage:

For your IUL policy (approximately $65/month):
- Cash value growth potential tied to market performance without the downside risk
- Death benefit protection for your loved ones
- Tax-free access to your cash value for future needs
- Living benefits that allow access to your death benefit if you become critically ill

For your health/dental/vision package (approximately $35/month):
- Comprehensive health coverage with our top-tier carriers that offer exceptional benefits
- Dental coverage including preventive care, basic procedures, and major work
- Vision benefits covering exams, frames, and contacts

What makes us the best agency to handle your insurance needs:
1. Our carriers offer some of the most comprehensive health benefits in the industry, with lower deductibles and better coverage than you'll find elsewhere
2. We have flexible IUL, whole life, and term policy options that can be customized to your exact needs
3. Our mortgage protection extends for the entire life of your loan, unlike competitors who offer limited coverage periods
4. For qualified applicants like yourself, we can secure over $1 million in coverage

We need to speak with you as soon as possible to secure this coverage before rates change. I have the following time slots available tomorrow (Monday):
- 10:00 AM - 10:30 AM
- 1:00 PM - 1:30 PM
- 4:00 PM - 4:30 PM

Or Tuesday:
- 9:00 AM - 9:30 AM
- 2:00 PM - 2:30 PM

Please let me know which time works best for you, or you can schedule directly through our Calendly link:
https://calendly.com/flofaction/insurance-consultation

It's critical that we connect in the next 24-48 hours to ensure we can lock in these rates for you.

Looking forward to speaking with you soon,

Paul Edwards
Flo Faction Insurance
(772) 208-9646
"""
    
    # Create and run the email agent
    email_agent = EmailAgent()
    success = email_agent.send_urgent_email(recipient, subject, body)
    
    if success:
        logger.info("Email agent completed task successfully")
        return 0
    else:
        logger.error("Email agent failed to complete task")
        return 1

if __name__ == "__main__":
    sys.exit(main())
