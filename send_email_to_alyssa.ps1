# PowerShell script to send an email to <PERSON><PERSON>. using browser automation
# This script uses UI automation to interact with Gmail in Chrome

Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName Microsoft.VisualBasic

# Function to wait for a specified number of seconds
function Wait-Seconds {
    param (
        [int]$Seconds
    )
    Start-Sleep -Seconds $Seconds
}

# Function to send keystrokes to the active window
function Send-Keystrokes {
    param (
        [string]$Text
    )
    [System.Windows.Forms.SendKeys]::SendWait($Text)
    Wait-Seconds 1
}

# Function to click at a specific position
function Click-Position {
    param (
        [int]$X,
        [int]$Y
    )
    [System.Windows.Forms.Cursor]::Position = New-Object System.Drawing.Point($X, $Y)
    Wait-Seconds 1
    $signature = @'
[DllImport("user32.dll",CharSet=CharSet.Auto,CallingConvention=CallingConvention.StdCall)]
public static extern void mouse_event(uint dwFlags, uint dx, uint dy, uint cButtons, uint dwExtraInfo);
'@
    $SendMouseClick = Add-Type -memberDefinition $signature -name "Win32MouseEventNew" -namespace Win32Functions -passThru
    $SendMouseClick::mouse_event(0x00000002, 0, 0, 0, 0) # Left button down
    $SendMouseClick::mouse_event(0x00000004, 0, 0, 0, 0) # Left button up
    Wait-Seconds 1
}

# Email content
$emailSubject = "URGENT: Your Insurance Options - Coverage Available Within Your $100 Monthly Budget"
$emailBody = @"
Hi Alyssa,

I hope this message finds you well. We've been trying to reach you through multiple channels (email, phone calls, voicemails, and texts) regarding your insurance needs, and I wanted to follow up personally as this is time-sensitive.

Based on your specific situation and $100 monthly budget, we have options ready for you that provide excellent coverage:

For your IUL policy (approximately $65/month):
- Cash value growth potential tied to market performance without the downside risk
- Death benefit protection for your loved ones
- Tax-free access to your cash value for future needs
- Living benefits that allow access to your death benefit if you become critically ill

For your health/dental/vision package (approximately $35/month):
- Comprehensive health coverage with our top-tier carriers that offer exceptional benefits
- Dental coverage including preventive care, basic procedures, and major work
- Vision benefits covering exams, frames, and contacts

What makes us the best agency to handle your insurance needs:
1. Our carriers offer some of the most comprehensive health benefits in the industry, with lower deductibles and better coverage than you'll find elsewhere
2. We have flexible IUL, whole life, and term policy options that can be customized to your exact needs
3. Our mortgage protection extends for the entire life of your loan, unlike competitors who offer limited coverage periods
4. For qualified applicants like yourself, we can secure over $1 million in coverage

We need to speak with you as soon as possible to secure this coverage before rates change. I have the following time slots available tomorrow (Monday):
- 10:00 AM - 10:30 AM
- 1:00 PM - 1:30 PM
- 4:00 PM - 4:30 PM

Or Tuesday:
- 9:00 AM - 9:30 AM
- 2:00 PM - 2:30 PM

Please let me know which time works best for you, or you can schedule directly through our Calendly link:
https://calendly.com/flofaction/insurance-consultation

It's critical that we connect in the next 24-48 hours to ensure we can lock in these rates for you.

Looking forward to speaking with you soon,

Paul Edwards
Flo Faction Insurance
(772) 208-9646
"@

# Main automation script
try {
    Write-Host "Starting email automation to Alyssa C..." -ForegroundColor Cyan

    # Activate Chrome window
    $wshell = New-Object -ComObject wscript.shell
    $wshell.AppActivate("Google Chrome")
    Wait-Seconds 2

    # Navigate to Gmail compose
    $wshell.SendKeys("^l")  # Ctrl+L to focus address bar
    Wait-Seconds 1
    $wshell.SendKeys("https://mail.google.com/mail/u/0/#inbox?compose=new")
    $wshell.SendKeys("{ENTER}")
    Wait-Seconds 5  # Wait for Gmail to load

    # Fill in recipient
    $wshell.SendKeys("<EMAIL>")  # Replace with actual email
    $wshell.SendKeys("{TAB}")
    Wait-Seconds 1

    # Fill in subject
    $wshell.SendKeys($emailSubject)
    $wshell.SendKeys("{TAB}")
    Wait-Seconds 1

    # Fill in email body
    $wshell.SendKeys($emailBody)
    Wait-Seconds 2

    # Send the email
    $wshell.SendKeys("^{ENTER}")  # Ctrl+Enter to send
    Wait-Seconds 3

    Write-Host "Email sent successfully to Alyssa C.!" -ForegroundColor Green
    Write-Host "Subject: $emailSubject" -ForegroundColor Green

} catch {
    Write-Host "Error sending email: $_" -ForegroundColor Red
}
