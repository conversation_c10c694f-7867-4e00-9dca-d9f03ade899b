"""
Fix Gmail authentication issues for multiple accounts.
This script helps you troubleshoot and fix Gmail authentication issues.
"""
import os
import sys
import json
import webbrowser
from pathlib import Path

def create_credentials_directory():
    """Create the credentials directory if it doesn't exist."""
    os.makedirs('credentials', exist_ok=True)
    print("Created credentials directory.")

def load_email_accounts():
    """Load email accounts from configuration file."""
    accounts_config_path = 'config/email_accounts.json'
    if not os.path.exists(accounts_config_path):
        print(f"Error: Email accounts configuration file not found at {accounts_config_path}")
        print("Please make sure the file exists.")
        return []

    with open(accounts_config_path, 'r') as f:
        accounts_config = json.load(f)

    # Combine priority and additional accounts
    all_accounts = []
    for account in accounts_config.get('priority_accounts', []):
        all_accounts.append(account)
    for account in accounts_config.get('additional_accounts', []):
        all_accounts.append(account)

    return all_accounts

def fix_gmail_auth(email):
    """
    Fix Gmail authentication for a specific account.

    Args:
        email (str): Email address to fix
    """
    print(f"\n=== Fixing Gmail Authentication for {email} ===")
    
    # Create a safe filename from the email address
    safe_email = email.replace("@", "_at_").replace(".", "_dot_")
    credentials_path = f'credentials/gmail_{safe_email}_credentials.json'
    token_path = f'credentials/gmail_{safe_email}_token.pickle'
    
    # Check if credentials file exists
    if not os.path.exists(credentials_path):
        print(f"Error: Credentials file not found at {credentials_path}")
        print("Please run setup_gmail_credentials.py first to set up your credentials.")
        return
    
    # Remove token file if it exists (to force re-authentication)
    if os.path.exists(token_path):
        os.remove(token_path)
        print(f"Removed existing token file: {token_path}")
    
    print("\nTo fix the authentication issues, follow these steps:")
    print("1. Go to https://console.cloud.google.com/apis/credentials")
    print("2. Select your project")
    print("3. Edit the OAuth 2.0 Client ID you're using")
    print("4. Add http://localhost:55253/ to the Authorized redirect URIs")
    print("5. Add http://localhost:0/ to the Authorized redirect URIs (for fallback)")
    print("6. Save the changes")
    print("7. Go to OAuth consent screen and make sure your app is properly configured")
    print("8. If your app is in 'Testing' mode, add your email as a test user")
    
    # Open Google Cloud Console
    print("\nOpening Google Cloud Console in your browser...")
    webbrowser.open("https://console.cloud.google.com/apis/credentials")
    
    input("\nPress Enter when you've made the changes...")
    
    print("\nNext steps:")
    print("1. Make sure the Gmail API is enabled for your project")
    print("2. Go to https://console.cloud.google.com/apis/library/gmail.googleapis.com")
    print("3. Enable the API if it's not already enabled")
    
    # Open Gmail API page
    print("\nOpening Gmail API page in your browser...")
    webbrowser.open("https://console.cloud.google.com/apis/library/gmail.googleapis.com")
    
    input("\nPress Enter when you've enabled the API...")
    
    print("\nThe next time you use the Gmail service, you'll need to re-authorize the application.")
    print("The authentication should now work correctly.")

def main():
    """Main entry point."""
    print("=== Fix Gmail Authentication ===")
    print("This script will help you fix Gmail authentication issues.")

    # Create credentials directory
    create_credentials_directory()

    # Load email accounts
    all_accounts = load_email_accounts()
    if not all_accounts:
        print("No email accounts found in the configuration.")
        return

    # Display available accounts
    print("\nAvailable email accounts:")
    for i, account in enumerate(all_accounts):
        print(f"{i+1}. {account['email']} - {account['description']}")

    print(f"{len(all_accounts)+1}. All accounts")

    # Get user selection
    selection = input("\nSelect an account to fix (or 'all' for all accounts): ")

    if selection.lower() == 'all' or selection == str(len(all_accounts)+1):
        # Fix all accounts
        for account in all_accounts:
            fix_gmail_auth(account['email'])
        return

    try:
        index = int(selection) - 1
        if 0 <= index < len(all_accounts):
            email = all_accounts[index]['email']
            fix_gmail_auth(email)
        else:
            print("Invalid selection.")
            return
    except ValueError:
        print("Invalid selection.")
        return

if __name__ == "__main__":
    main()
