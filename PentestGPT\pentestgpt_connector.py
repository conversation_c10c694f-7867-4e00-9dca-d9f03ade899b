"""
PentestGPT Connector for AI-powered security testing.

This module provides integration with PentestGPT, an AI-powered penetration testing tool
that can be used for security testing and vulnerability assessment.
"""
import asyncio
import json
import logging
import os
import subprocess
from typing import Dict, List, Optional, Any, Union
import uuid
from datetime import datetime
import aiohttp

from core.logger import setup_logger

# Set up logger
logger = setup_logger("pentestgpt_connector")

class PentestGPTConnector:
    """
    Connector for PentestGPT.
    
    This class provides an interface for interacting with PentestGPT,
    an AI-powered penetration testing tool.
    """
    
    def __init__(self, config: Dict):
        """
        Initialize the PentestGPT connector.
        
        Args:
            config (Dict): PentestGPT configuration
        """
        self.config = config
        self.api_key = config.get("api_key", "")
        self.api_url = config.get("api_url", "http://localhost:8000")
        self.enabled = config.get("enabled", False)
        self.local_mode = config.get("local_mode", True)
        self.local_path = config.get("local_path", "./PentestGPT")
        
        logger.info("PentestGPT connector initialized")
    
    async def initialize(self):
        """Initialize the PentestGPT connector."""
        if not self.enabled:
            logger.warning("PentestGPT connector is disabled")
            return
        
        try:
            if self.local_mode:
                # Check if PentestGPT is installed locally
                if not os.path.exists(self.local_path):
                    logger.warning(f"PentestGPT not found at {self.local_path}")
                    logger.info("Attempting to clone PentestGPT repository")
                    await self._clone_pentestgpt()
                
                # Check if dependencies are installed
                await self._check_dependencies()
                
                logger.info("PentestGPT initialized in local mode")
            else:
                # Check if API is accessible
                await self._check_api()
                
                logger.info("PentestGPT initialized in API mode")
                
        except Exception as e:
            logger.exception(f"Error initializing PentestGPT connector: {e}")
            self.enabled = False
    
    async def _clone_pentestgpt(self):
        """Clone PentestGPT repository."""
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(self.local_path), exist_ok=True)
            
            # Clone repository
            process = await asyncio.create_subprocess_exec(
                "git", "clone", "https://github.com/GreyDGL/PentestGPT.git", self.local_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                logger.error(f"Error cloning PentestGPT repository: {stderr.decode()}")
                raise RuntimeError(f"Error cloning PentestGPT repository: {stderr.decode()}")
            
            logger.info("PentestGPT repository cloned successfully")
            
        except Exception as e:
            logger.exception(f"Error cloning PentestGPT repository: {e}")
            raise
    
    async def _check_dependencies(self):
        """Check if PentestGPT dependencies are installed."""
        try:
            # Check if requirements.txt exists
            requirements_path = os.path.join(self.local_path, "requirements.txt")
            if not os.path.exists(requirements_path):
                logger.warning("PentestGPT requirements.txt not found")
                return
            
            # Install dependencies
            process = await asyncio.create_subprocess_exec(
                "pip", "install", "-r", requirements_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                logger.error(f"Error installing PentestGPT dependencies: {stderr.decode()}")
                raise RuntimeError(f"Error installing PentestGPT dependencies: {stderr.decode()}")
            
            logger.info("PentestGPT dependencies installed successfully")
            
        except Exception as e:
            logger.exception(f"Error checking PentestGPT dependencies: {e}")
            raise
    
    async def _check_api(self):
        """Check if PentestGPT API is accessible."""
        if not self.api_url:
            logger.warning("PentestGPT API URL not provided")
            self.enabled = False
            return
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.api_url}/health") as response:
                    if response.status != 200:
                        logger.warning(f"PentestGPT API not accessible: {response.status}")
                        self.enabled = False
                        return
                    
                    data = await response.json()
                    logger.info(f"PentestGPT API accessible: {data}")
                    
        except Exception as e:
            logger.exception(f"Error checking PentestGPT API: {e}")
            self.enabled = False
    
    async def run_security_scan(
        self,
        target: str,
        scan_type: str = "basic",
        options: Optional[Dict] = None,
    ) -> Dict:
        """
        Run a security scan using PentestGPT.
        
        Args:
            target (str): Target to scan (URL, IP, etc.)
            scan_type (str): Type of scan (basic, comprehensive, etc.)
            options (Optional[Dict]): Additional scan options
            
        Returns:
            Dict: Scan results
        """
        if not self.enabled:
            return {"error": "PentestGPT connector is disabled"}
        
        logger.info(f"Running {scan_type} security scan on {target}")
        
        try:
            if self.local_mode:
                return await self._run_local_scan(target, scan_type, options)
            else:
                return await self._run_api_scan(target, scan_type, options)
                
        except Exception as e:
            logger.exception(f"Error running security scan: {e}")
            return {"error": str(e)}
    
    async def _run_local_scan(
        self,
        target: str,
        scan_type: str,
        options: Optional[Dict],
    ) -> Dict:
        """
        Run a security scan using local PentestGPT installation.
        
        Args:
            target (str): Target to scan
            scan_type (str): Type of scan
            options (Optional[Dict]): Additional scan options
            
        Returns:
            Dict: Scan results
        """
        # Build command
        cmd = ["python", os.path.join(self.local_path, "pentestgpt.py"), "--target", target]
        
        # Add scan type
        if scan_type == "basic":
            cmd.extend(["--mode", "basic"])
        elif scan_type == "comprehensive":
            cmd.extend(["--mode", "comprehensive"])
        elif scan_type == "web":
            cmd.extend(["--mode", "web"])
        elif scan_type == "network":
            cmd.extend(["--mode", "network"])
        
        # Add options
        if options:
            for key, value in options.items():
                cmd.extend([f"--{key}", str(value)])
        
        # Run command
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
        )
        
        stdout, stderr = await process.communicate()
        
        if process.returncode != 0:
            logger.error(f"Error running PentestGPT scan: {stderr.decode()}")
            return {"error": stderr.decode()}
        
        # Parse output
        try:
            output = stdout.decode()
            
            # Try to parse JSON output
            try:
                result = json.loads(output)
            except json.JSONDecodeError:
                # If not JSON, return as text
                result = {"output": output}
            
            return {
                "target": target,
                "scan_type": scan_type,
                "timestamp": datetime.now().isoformat(),
                "result": result,
            }
            
        except Exception as e:
            logger.exception(f"Error parsing PentestGPT output: {e}")
            return {"error": str(e)}
    
    async def _run_api_scan(
        self,
        target: str,
        scan_type: str,
        options: Optional[Dict],
    ) -> Dict:
        """
        Run a security scan using PentestGPT API.
        
        Args:
            target (str): Target to scan
            scan_type (str): Type of scan
            options (Optional[Dict]): Additional scan options
            
        Returns:
            Dict: Scan results
        """
        # Prepare request payload
        payload = {
            "target": target,
            "scan_type": scan_type,
        }
        
        # Add options
        if options:
            payload["options"] = options
        
        # Add API key if available
        headers = {}
        if self.api_key:
            headers["Authorization"] = f"Bearer {self.api_key}"
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.api_url}/scan",
                    json=payload,
                    headers=headers,
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"Error running PentestGPT API scan: {error_text}")
                        return {"error": error_text}
                    
                    result = await response.json()
                    
                    return {
                        "target": target,
                        "scan_type": scan_type,
                        "timestamp": datetime.now().isoformat(),
                        "result": result,
                    }
                    
        except Exception as e:
            logger.exception(f"Error running PentestGPT API scan: {e}")
            return {"error": str(e)}
