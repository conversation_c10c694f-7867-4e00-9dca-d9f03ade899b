import requests
import json
import os

# Create credentials directory if it doesn't exist
os.makedirs("credentials/calendly", exist_ok=True)

# Ask for API key
api_key = input("Enter your Calendly API key: ")

# Test API key
try:
    response = requests.get(
        "https://api.calendly.com/users/me",
        headers={
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
    )
    
    if response.status_code == 200:
        user_info = response.json()
        print("API key is valid!")
        print(f"User: {user_info['resource']['name']} ({user_info['resource']['email']})")
        
        # Save API key to credentials file
        with open("credentials/calendly/calendly.json", "w") as f:
            json.dump({
                "api_key": api_key,
                "user_uri": user_info['resource']['uri'],
                "user_uuid": user_info['resource']['uri'].split('/')[-1]
            }, f, indent=4)
        
        print("API key saved to credentials/calendly/calendly.json")
        
        # Get event types
        print("\nGetting event types...")
        event_types_response = requests.get(
            "https://api.calendly.com/event_types",
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {api_key}"
            }
        )
        
        if event_types_response.status_code == 200:
            event_types = event_types_response.json()
            print(f"Found {len(event_types['collection'])} event types:")
            
            for i, event_type in enumerate(event_types['collection']):
                print(f"{i+1}. {event_type['name']} ({event_type['duration']} minutes)")
                print(f"   URI: {event_type['uri']}")
                print(f"   URL: {event_type['scheduling_url']}")
        else:
            print(f"Error getting event types: {event_types_response.status_code}")
            print(event_types_response.text)
    else:
        print(f"API key is invalid: {response.status_code}")
        print(response.text)
except Exception as e:
    print(f"Error: {e}")
