"""
Simple Gmail authentication script.
This script provides a simple way to authenticate with Gmail.
"""
import os
import sys
import pickle
import webbrowser
import subprocess
from pathlib import Path

def clear_screen():
    """Clear the terminal screen."""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_header():
    """Print the script header."""
    clear_screen()
    print("=" * 80)
    print("                     SIMPLE GMAIL AUTHENTICATION")
    print("=" * 80)
    print("\nThis script provides a simple way to authenticate with Gmail.")
    print("It works with Desktop OAuth clients that don't require verification.")
    print("\n")

def get_configured_accounts():
    """
    Get a list of configured Gmail accounts.
    
    Returns:
        list: List of configured Gmail accounts
    """
    accounts = []
    credentials_dir = 'credentials'
    
    if not os.path.exists(credentials_dir):
        return accounts
    
    for filename in os.listdir(credentials_dir):
        if filename.startswith('gmail_') and filename.endswith('_credentials.json'):
            # Extract email from filename
            email_part = filename[6:-16]  # Remove 'gmail_' prefix and '_credentials.json' suffix
            email = email_part.replace('_at_', '@').replace('_dot_', '.')
            accounts.append({
                'email': email,
                'credentials_path': os.path.join(credentials_dir, filename),
                'token_path': os.path.join(credentials_dir, filename.replace('_credentials.json', '_token.pickle'))
            })
    
    return accounts

def authenticate_gmail_account(account):
    """
    Authenticate a Gmail account.
    
    Args:
        account (dict): Account information
        
    Returns:
        bool: True if authentication was successful, False otherwise
    """
    email = account['email']
    credentials_path = account['credentials_path']
    token_path = account['token_path']
    
    print_header()
    print(f"AUTHENTICATING {email}")
    print("-" * 80)
    
    # Install required packages if not already installed
    try:
        from google.auth.transport.requests import Request
        from google.oauth2.credentials import Credentials
        from google_auth_oauthlib.flow import InstalledAppFlow
        from googleapiclient.discovery import build
        from googleapiclient.errors import HttpError
    except ImportError:
        print("\nInstalling required packages...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", 
                              "google-auth", "google-auth-oauthlib", 
                              "google-auth-httplib2", "google-api-python-client"])
        
        from google.auth.transport.requests import Request
        from google.oauth2.credentials import Credentials
        from google_auth_oauthlib.flow import InstalledAppFlow
        from googleapiclient.discovery import build
        from googleapiclient.errors import HttpError
    
    # Gmail API scopes
    SCOPES = [
        'https://www.googleapis.com/auth/gmail.readonly',
        'https://www.googleapis.com/auth/gmail.send',
        'https://www.googleapis.com/auth/gmail.compose',
        'https://www.googleapis.com/auth/gmail.modify'
    ]
    
    # Check if credentials file exists
    if not os.path.exists(credentials_path):
        print(f"\nError: Credentials file not found at {credentials_path}")
        return False
    
    # Remove token file if it exists (to force re-authentication)
    if os.path.exists(token_path):
        os.remove(token_path)
        print(f"\n✓ Removed existing token file: {token_path}")
    
    try:
        print("\nStarting authentication process...")
        print("A browser window will open for you to sign in to your Google account.")
        print("Please follow the instructions in the browser.")
        
        # Create a flow with local_server
        flow = InstalledAppFlow.from_client_secrets_file(
            credentials_path,
            SCOPES
        )
        
        # Run the flow with local_server
        creds = flow.run_local_server(port=0)
        
        # Save the credentials for future use
        with open(token_path, 'wb') as token:
            pickle.dump(creds, token)
        
        print("\n✓ Authentication successful!")
        print(f"✓ Token saved to {token_path}")
        
        # Test the credentials
        service = build('gmail', 'v1', credentials=creds)
        profile = service.users().getProfile(userId='me').execute()
        user_email = profile.get('emailAddress')
        
        print(f"\n✓ Successfully authenticated as {user_email}")
        
        # List a few messages to test the connection
        results = service.users().messages().list(userId='me', maxResults=5).execute()
        messages = results.get('messages', [])
        
        if not messages:
            print("No messages found.")
        else:
            print(f"✓ Found {len(messages)} messages.")
            
            # Get the first message details
            msg = service.users().messages().get(userId='me', id=messages[0]['id']).execute()
            headers = msg['payload']['headers']
            subject = next((header['value'] for header in headers if header['name'] == 'Subject'), 'No subject')
            sender = next((header['value'] for header in headers if header['name'] == 'From'), 'Unknown sender')
            
            print(f"✓ Latest message: '{subject}' from {sender}")
        
        return True
    
    except Exception as e:
        print(f"\nError authenticating: {e}")
        
        print("\nTroubleshooting steps:")
        print("1. Make sure you're using a Desktop OAuth client")
        print("2. Try using a different Google account")
        print("3. Make sure the Gmail API is enabled for your project")
        
        retry = input("\nDo you want to try again? (y/n): ").lower()
        if retry == 'y':
            return authenticate_gmail_account(account)
        
        return False

def main():
    """Main entry point."""
    print_header()
    
    # Get configured accounts
    accounts = get_configured_accounts()
    
    if not accounts:
        print("\nNo Gmail accounts configured.")
        print("Please run create_desktop_oauth_client.py to configure your accounts first.")
        return
    
    print("\nConfigured Gmail accounts:")
    for i, account in enumerate(accounts):
        print(f"{i+1}. {account['email']}")
    
    print(f"{len(accounts)+1}. Exit")
    
    try:
        index = int(input("\nSelect an account to authenticate: ")) - 1
        
        if index == len(accounts):
            # Exit
            print("\nExiting...")
            return
        
        elif 0 <= index < len(accounts):
            # Authenticate account
            authenticate_gmail_account(accounts[index])
            
            # Ask if user wants to authenticate another account
            another = input("\nDo you want to authenticate another account? (y/n): ").lower()
            if another == 'y':
                main()
        
        else:
            print("\nInvalid selection.")
            input("\nPress Enter to try again...")
            main()
    
    except ValueError:
        print("\nInvalid selection.")
        input("\nPress Enter to try again...")
        main()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nExiting...")
        sys.exit(0)
