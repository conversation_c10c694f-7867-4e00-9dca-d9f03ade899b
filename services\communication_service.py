"""
Communication services for the Multi-Agent AI System.
"""
import asyncio
from typing import Dict, List, Optional, Any, Union
from abc import ABC, abstractmethod
import json
from datetime import datetime
import aiohttp

import config
from core.logger import setup_logger

# Set up logger
logger = setup_logger("communication_service")

class CommunicationService(ABC):
    """
    Base class for communication services.
    """
    
    def __init__(self, service_type: str, config: Dict):
        """
        Initialize the communication service.
        
        Args:
            service_type (str): Type of communication service
            config (Dict): Service configuration
        """
        self.service_type = service_type
        self.config = config
        self.enabled = config.get("enabled", False)
        
        # Set up logger
        self.logger = setup_logger(f"communication.{service_type}")
    
    @abstractmethod
    async def send_message(self, recipient: str, content: str, **kwargs) -> Dict:
        """
        Send a message.
        
        Args:
            recipient (str): Message recipient
            content (str): Message content
            **kwargs: Additional parameters
            
        Returns:
            Dict: Response containing status and metadata
        """
        pass
    
    def is_enabled(self) -> bool:
        """
        Check if the service is enabled.
        
        Returns:
            bool: True if enabled, False otherwise
        """
        return self.enabled

class EmailService(CommunicationService):
    """
    Email communication service.
    """
    
    def __init__(self, config: Dict):
        """
        Initialize the email service.
        
        Args:
            config (Dict): Service configuration
        """
        super().__init__("email", config)
        
        self.service = config.get("service", "sendgrid")
        self.api_key = config.get("api_key", "")
        self.sender_email = config.get("sender_email", "")
        
        # Validate configuration
        if not self.api_key:
            self.logger.warning("Email API key not provided")
            self.enabled = False
        
        if not self.sender_email:
            self.logger.warning("Sender email not provided")
            self.enabled = False
    
    async def send_message(
        self,
        recipient: str,
        content: str,
        subject: str = "",
        html_content: Optional[str] = None,
        **kwargs
    ) -> Dict:
        """
        Send an email.
        
        Args:
            recipient (str): Recipient email address
            content (str): Email content (plain text)
            subject (str): Email subject
            html_content (Optional[str]): HTML content
            **kwargs: Additional parameters
            
        Returns:
            Dict: Response containing status and metadata
        """
        if not self.enabled:
            return {"error": "Email service is not enabled"}
        
        if self.service == "sendgrid":
            return await self._send_sendgrid(recipient, content, subject, html_content, **kwargs)
        else:
            return {"error": f"Unsupported email service: {self.service}"}
    
    async def _send_sendgrid(
        self,
        recipient: str,
        content: str,
        subject: str,
        html_content: Optional[str],
        **kwargs
    ) -> Dict:
        """
        Send an email using SendGrid.
        
        Args:
            recipient (str): Recipient email address
            content (str): Email content (plain text)
            subject (str): Email subject
            html_content (Optional[str]): HTML content
            **kwargs: Additional parameters
            
        Returns:
            Dict: Response containing status and metadata
        """
        if not subject:
            subject = "Message from Multi-Agent AI System"
        
        if not html_content:
            html_content = content.replace("\n", "<br>")
        
        payload = {
            "personalizations": [
                {
                    "to": [{"email": recipient}],
                    "subject": subject,
                }
            ],
            "from": {"email": self.sender_email},
            "content": [
                {
                    "type": "text/plain",
                    "value": content,
                },
                {
                    "type": "text/html",
                    "value": html_content,
                },
            ],
        }
        
        # Add CC if provided
        if "cc" in kwargs:
            payload["personalizations"][0]["cc"] = [
                {"email": cc} for cc in kwargs["cc"]
            ]
        
        # Add BCC if provided
        if "bcc" in kwargs:
            payload["personalizations"][0]["bcc"] = [
                {"email": bcc} for bcc in kwargs["bcc"]
            ]
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    "https://api.sendgrid.com/v3/mail/send",
                    headers={
                        "Authorization": f"Bearer {self.api_key}",
                        "Content-Type": "application/json",
                    },
                    json=payload,
                ) as response:
                    if response.status == 202:
                        self.logger.info(f"Email sent to {recipient}")
                        return {
                            "status": "sent",
                            "recipient": recipient,
                            "subject": subject,
                        }
                    else:
                        error_text = await response.text()
                        self.logger.error(f"Failed to send email: {response.status} - {error_text}")
                        return {
                            "error": f"API error: {response.status}",
                            "details": error_text,
                        }
        except Exception as e:
            self.logger.exception(f"Error sending email: {e}")
            return {"error": f"Failed to send email: {str(e)}"}

class SMSService(CommunicationService):
    """
    SMS communication service.
    """
    
    def __init__(self, config: Dict):
        """
        Initialize the SMS service.
        
        Args:
            config (Dict): Service configuration
        """
        super().__init__("sms", config)
        
        self.service = config.get("service", "twilio")
        self.account_sid = config.get("account_sid", "")
        self.auth_token = config.get("auth_token", "")
        self.phone_number = config.get("phone_number", "")
        
        # Validate configuration
        if not self.account_sid or not self.auth_token:
            self.logger.warning("SMS credentials not provided")
            self.enabled = False
        
        if not self.phone_number:
            self.logger.warning("Sender phone number not provided")
            self.enabled = False
    
    async def send_message(
        self,
        recipient: str,
        content: str,
        **kwargs
    ) -> Dict:
        """
        Send an SMS.
        
        Args:
            recipient (str): Recipient phone number
            content (str): SMS content
            **kwargs: Additional parameters
            
        Returns:
            Dict: Response containing status and metadata
        """
        if not self.enabled:
            return {"error": "SMS service is not enabled"}
        
        if self.service == "twilio":
            return await self._send_twilio(recipient, content, **kwargs)
        else:
            return {"error": f"Unsupported SMS service: {self.service}"}
    
    async def _send_twilio(
        self,
        recipient: str,
        content: str,
        **kwargs
    ) -> Dict:
        """
        Send an SMS using Twilio.
        
        Args:
            recipient (str): Recipient phone number
            content (str): SMS content
            **kwargs: Additional parameters
            
        Returns:
            Dict: Response containing status and metadata
        """
        # Ensure recipient is in E.164 format
        if not recipient.startswith("+"):
            recipient = f"+{recipient}"
        
        payload = {
            "To": recipient,
            "From": self.phone_number,
            "Body": content,
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"https://api.twilio.com/2010-04-01/Accounts/{self.account_sid}/Messages.json",
                    auth=aiohttp.BasicAuth(self.account_sid, self.auth_token),
                    data=payload,
                ) as response:
                    response_json = await response.json()
                    
                    if response.status == 201:
                        self.logger.info(f"SMS sent to {recipient}")
                        return {
                            "status": "sent",
                            "recipient": recipient,
                            "message_sid": response_json.get("sid"),
                        }
                    else:
                        self.logger.error(f"Failed to send SMS: {response.status} - {response_json}")
                        return {
                            "error": f"API error: {response.status}",
                            "details": response_json,
                        }
        except Exception as e:
            self.logger.exception(f"Error sending SMS: {e}")
            return {"error": f"Failed to send SMS: {str(e)}"}

class VoiceService(CommunicationService):
    """
    Voice communication service.
    """
    
    def __init__(self, config: Dict):
        """
        Initialize the voice service.
        
        Args:
            config (Dict): Service configuration
        """
        super().__init__("voice", config)
        
        self.service = config.get("service", "twilio")
        self.account_sid = config.get("account_sid", "")
        self.auth_token = config.get("auth_token", "")
        self.phone_number = config.get("phone_number", "")
        
        # Validate configuration
        if not self.account_sid or not self.auth_token:
            self.logger.warning("Voice credentials not provided")
            self.enabled = False
        
        if not self.phone_number:
            self.logger.warning("Caller phone number not provided")
            self.enabled = False
    
    async def send_message(
        self,
        recipient: str,
        content: str,
        **kwargs
    ) -> Dict:
        """
        Make a voice call.
        
        Args:
            recipient (str): Recipient phone number
            content (str): Voice message content
            **kwargs: Additional parameters
            
        Returns:
            Dict: Response containing status and metadata
        """
        if not self.enabled:
            return {"error": "Voice service is not enabled"}
        
        if self.service == "twilio":
            return await self._call_twilio(recipient, content, **kwargs)
        else:
            return {"error": f"Unsupported voice service: {self.service}"}
    
    async def _call_twilio(
        self,
        recipient: str,
        content: str,
        **kwargs
    ) -> Dict:
        """
        Make a voice call using Twilio.
        
        Args:
            recipient (str): Recipient phone number
            content (str): Voice message content
            **kwargs: Additional parameters
            
        Returns:
            Dict: Response containing status and metadata
        """
        # Ensure recipient is in E.164 format
        if not recipient.startswith("+"):
            recipient = f"+{recipient}"
        
        # Create TwiML for the call
        twiml = f"""
        <?xml version="1.0" encoding="UTF-8"?>
        <Response>
            <Say voice="alice">{content}</Say>
            <Pause length="1"/>
            <Say voice="alice">This is an automated call from the Multi-Agent AI System. Goodbye.</Say>
        </Response>
        """
        
        payload = {
            "To": recipient,
            "From": self.phone_number,
            "Twiml": twiml,
        }
        
        # Use a URL for TwiML if provided
        if "twiml_url" in kwargs:
            payload.pop("Twiml", None)
            payload["Url"] = kwargs["twiml_url"]
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"https://api.twilio.com/2010-04-01/Accounts/{self.account_sid}/Calls.json",
                    auth=aiohttp.BasicAuth(self.account_sid, self.auth_token),
                    data=payload,
                ) as response:
                    response_json = await response.json()
                    
                    if response.status == 201:
                        self.logger.info(f"Voice call initiated to {recipient}")
                        return {
                            "status": "initiated",
                            "recipient": recipient,
                            "call_sid": response_json.get("sid"),
                        }
                    else:
                        self.logger.error(f"Failed to initiate call: {response.status} - {response_json}")
                        return {
                            "error": f"API error: {response.status}",
                            "details": response_json,
                        }
        except Exception as e:
            self.logger.exception(f"Error initiating call: {e}")
            return {"error": f"Failed to initiate call: {str(e)}"}

class CommunicationServiceFactory:
    """
    Factory for creating communication services.
    """
    
    @staticmethod
    def create_service(service_type: str) -> Optional[CommunicationService]:
        """
        Create a communication service.
        
        Args:
            service_type (str): Type of communication service
            
        Returns:
            Optional[CommunicationService]: Communication service instance
        """
        if service_type == "email":
            return EmailService(config.COMMUNICATION_CONFIG["email"])
        elif service_type == "sms":
            return SMSService(config.COMMUNICATION_CONFIG["sms"])
        elif service_type == "voice":
            return VoiceService(config.COMMUNICATION_CONFIG["voice"])
        else:
            logger.warning(f"Unsupported communication service type: {service_type}")
            return None
