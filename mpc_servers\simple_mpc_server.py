"""
Simple Multi-Party Computation (MPC) Server for secure distributed computation.

This module implements a simplified MPC server for testing and development.
It provides basic MPC functionality without the complexity of a full MPC server.
"""
import asyncio
import json
import logging
import os
import secrets
import socket
import ssl
import time
from typing import Dict, List, Optional, Any, Callable, Union
import uuid
from datetime import datetime

from core.logger import setup_logger

# Set up logger
logger = setup_logger("simple_mpc_server")

class SimpleMPCServer:
    """
    Simple Multi-Party Computation Server for secure distributed computation.
    
    This class implements a simplified MPC server for testing and development.
    It provides basic MPC functionality without the complexity of a full MPC server.
    """
    
    def __init__(
        self,
        server_id: str,
        host: str = "0.0.0.0",
        port: int = 8766,
        use_ssl: bool = False,
        cert_file: Optional[str] = None,
        key_file: Optional[str] = None,
    ):
        """
        Initialize the simple MPC server.
        
        Args:
            server_id (str): Unique identifier for this server
            host (str): Host to bind to
            port (int): Port to bind to
            use_ssl (bool): Whether to use SSL
            cert_file (Optional[str]): Path to SSL certificate file
            key_file (Optional[str]): Path to SSL key file
        """
        self.server_id = server_id
        self.host = host
        self.port = port
        self.use_ssl = use_ssl
        self.cert_file = cert_file
        self.key_file = key_file
        
        # Server state
        self.running = False
        self.clients = {}
        self.computations = {}
        self.server = None
        
        # Security
        self.session_keys = {}
        
        # Supported computation types
        self.computation_handlers = {
            "secure_sum": self._compute_secure_sum,
            "secure_average": self._compute_secure_average,
            "secure_min": self._compute_secure_min,
            "secure_max": self._compute_secure_max,
            "secure_set_intersection": self._compute_secure_set_intersection,
        }
        
        logger.info(f"Simple MPC Server {server_id} initialized")
    
    async def start(self):
        """Start the simple MPC server."""
        if self.running:
            logger.warning("Simple MPC Server already running")
            return
        
        try:
            # Create server
            server = await asyncio.start_server(
                self._handle_client,
                self.host,
                self.port,
                ssl=self._create_ssl_context() if self.use_ssl else None,
            )
            
            self.server = server
            self.running = True
            
            logger.info(f"Simple MPC Server {self.server_id} started on {self.host}:{self.port}")
            
            # Serve forever
            async with server:
                await server.serve_forever()
                
        except Exception as e:
            logger.exception(f"Error starting Simple MPC Server: {e}")
            self.running = False
    
    async def stop(self):
        """Stop the simple MPC server."""
        if not self.running:
            logger.warning("Simple MPC Server not running")
            return
        
        try:
            # Close server
            if self.server:
                self.server.close()
                await self.server.wait_closed()
                self.server = None
            
            self.running = False
            logger.info(f"Simple MPC Server {self.server_id} stopped")
            
        except Exception as e:
            logger.exception(f"Error stopping Simple MPC Server: {e}")
    
    def _create_ssl_context(self) -> ssl.SSLContext:
        """
        Create SSL context for secure connections.
        
        Returns:
            ssl.SSLContext: SSL context
        """
        if not self.cert_file or not self.key_file:
            raise ValueError("SSL certificate and key files must be provided")
        
        ssl_context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
        ssl_context.load_cert_chain(self.cert_file, self.key_file)
        ssl_context.check_hostname = False
        
        return ssl_context
    
    async def _handle_client(self, reader, writer):
        """
        Handle a client connection.
        
        Args:
            reader: StreamReader for reading from client
            writer: StreamWriter for writing to client
        """
        client_id = str(uuid.uuid4())
        peer_name = writer.get_extra_info("peername")
        logger.info(f"New client connected: {client_id} from {peer_name}")
        
        # Add client to clients dict
        self.clients[client_id] = {
            "reader": reader,
            "writer": writer,
            "peer_name": peer_name,
            "connected_at": datetime.now().isoformat(),
            "last_active": datetime.now().isoformat(),
        }
        
        try:
            while True:
                # Read message from client
                data = await reader.read(4096)
                if not data:
                    break
                
                # Update last active time
                self.clients[client_id]["last_active"] = datetime.now().isoformat()
                
                # Process message
                try:
                    message = json.loads(data.decode())
                    response = await self._process_message(client_id, message)
                    
                    # Send response
                    writer.write(json.dumps(response).encode())
                    await writer.drain()
                    
                except json.JSONDecodeError:
                    logger.warning(f"Invalid JSON from client {client_id}")
                    writer.write(json.dumps({"error": "Invalid JSON"}).encode())
                    await writer.drain()
                    
                except Exception as e:
                    logger.exception(f"Error processing message from client {client_id}: {e}")
                    writer.write(json.dumps({"error": str(e)}).encode())
                    await writer.drain()
                    
        except asyncio.CancelledError:
            pass
        except Exception as e:
            logger.exception(f"Error handling client {client_id}: {e}")
        finally:
            # Remove client from clients dict
            if client_id in self.clients:
                del self.clients[client_id]
            
            # Close connection
            writer.close()
            try:
                await writer.wait_closed()
            except:
                pass
            
            logger.info(f"Client disconnected: {client_id}")
    
    async def _process_message(self, client_id: str, message: Dict) -> Dict:
        """
        Process a message from a client.
        
        Args:
            client_id (str): Client identifier
            message (Dict): Message from client
            
        Returns:
            Dict: Response message
        """
        message_type = message.get("type")
        
        if message_type == "hello":
            return await self._handle_hello(client_id, message)
        elif message_type == "create_computation":
            return await self._handle_create_computation(client_id, message)
        elif message_type == "submit_input":
            return await self._handle_submit_input(client_id, message)
        elif message_type == "get_result":
            return await self._handle_get_result(client_id, message)
        else:
            return {"error": f"Unknown message type: {message_type}"}
    
    async def _handle_hello(self, client_id: str, message: Dict) -> Dict:
        """
        Handle a hello message from a client.
        
        Args:
            client_id (str): Client identifier
            message (Dict): Hello message
            
        Returns:
            Dict: Response message
        """
        # Generate session key
        session_key = secrets.token_hex(16)
        self.session_keys[client_id] = session_key
        
        # Update client info
        self.clients[client_id]["client_id"] = message.get("client_id", client_id)
        
        return {
            "type": "hello_response",
            "server_id": self.server_id,
            "session_key": session_key,
            "timestamp": datetime.now().isoformat(),
        }
    
    async def _handle_create_computation(self, client_id: str, message: Dict) -> Dict:
        """
        Handle a create computation message from a client.
        
        Args:
            client_id (str): Client identifier
            message (Dict): Create computation message
            
        Returns:
            Dict: Response message
        """
        computation_type = message.get("computation_type")
        parameters = message.get("parameters", {})
        
        # Check if computation type is supported
        if computation_type not in self.computation_handlers:
            return {"error": f"Unsupported computation type: {computation_type}"}
        
        # Generate computation ID
        computation_id = str(uuid.uuid4())
        
        # Create computation
        self.computations[computation_id] = {
            "type": computation_type,
            "parameters": parameters,
            "creator": client_id,
            "created_at": datetime.now().isoformat(),
            "status": "created",
            "inputs": {},
            "result": None,
        }
        
        return {
            "type": "create_computation_response",
            "computation_id": computation_id,
            "status": "created",
            "timestamp": datetime.now().isoformat(),
        }
    
    async def _handle_submit_input(self, client_id: str, message: Dict) -> Dict:
        """
        Handle a submit input message from a client.
        
        Args:
            client_id (str): Client identifier
            message (Dict): Submit input message
            
        Returns:
            Dict: Response message
        """
        computation_id = message.get("computation_id")
        input_data = message.get("input_data")
        
        # Check if computation exists
        if computation_id not in self.computations:
            return {"error": f"Unknown computation: {computation_id}"}
        
        # Add input to computation
        self.computations[computation_id]["inputs"][client_id] = input_data
        
        # Check if all inputs are received
        computation = self.computations[computation_id]
        num_parties = computation["parameters"].get("num_parties", 2)
        
        if len(computation["inputs"]) >= num_parties:
            # Compute result
            computation["status"] = "computing"
            
            try:
                # Get computation handler
                handler = self.computation_handlers[computation["type"]]
                
                # Compute result
                result = await handler(computation)
                
                # Store result
                computation["result"] = result
                computation["status"] = "completed"
                computation["completed_at"] = datetime.now().isoformat()
                
            except Exception as e:
                logger.exception(f"Error computing result for {computation_id}: {e}")
                computation["status"] = "error"
                computation["error"] = str(e)
        
        return {
            "type": "submit_input_response",
            "computation_id": computation_id,
            "status": self.computations[computation_id]["status"],
            "timestamp": datetime.now().isoformat(),
        }
    
    async def _handle_get_result(self, client_id: str, message: Dict) -> Dict:
        """
        Handle a get result message from a client.
        
        Args:
            client_id (str): Client identifier
            message (Dict): Get result message
            
        Returns:
            Dict: Response message
        """
        computation_id = message.get("computation_id")
        
        # Check if computation exists
        if computation_id not in self.computations:
            return {"error": f"Unknown computation: {computation_id}"}
        
        # Get computation
        computation = self.computations[computation_id]
        
        # Check if computation is completed
        if computation["status"] != "completed":
            return {
                "type": "get_result_response",
                "computation_id": computation_id,
                "status": computation["status"],
                "timestamp": datetime.now().isoformat(),
            }
        
        return {
            "type": "get_result_response",
            "computation_id": computation_id,
            "status": "completed",
            "result": computation["result"],
            "timestamp": datetime.now().isoformat(),
        }
    
    async def _compute_secure_sum(self, computation: Dict) -> Any:
        """
        Compute the sum of inputs securely.
        
        Args:
            computation (Dict): Computation data
            
        Returns:
            Any: Computation result
        """
        inputs = list(computation["inputs"].values())
        return sum(inputs)
    
    async def _compute_secure_average(self, computation: Dict) -> Any:
        """
        Compute the average of inputs securely.
        
        Args:
            computation (Dict): Computation data
            
        Returns:
            Any: Computation result
        """
        inputs = list(computation["inputs"].values())
        return sum(inputs) / len(inputs)
    
    async def _compute_secure_min(self, computation: Dict) -> Any:
        """
        Compute the minimum of inputs securely.
        
        Args:
            computation (Dict): Computation data
            
        Returns:
            Any: Computation result
        """
        inputs = list(computation["inputs"].values())
        return min(inputs)
    
    async def _compute_secure_max(self, computation: Dict) -> Any:
        """
        Compute the maximum of inputs securely.
        
        Args:
            computation (Dict): Computation data
            
        Returns:
            Any: Computation result
        """
        inputs = list(computation["inputs"].values())
        return max(inputs)
    
    async def _compute_secure_set_intersection(self, computation: Dict) -> Any:
        """
        Compute the intersection of sets securely.
        
        Args:
            computation (Dict): Computation data
            
        Returns:
            Any: Computation result
        """
        inputs = list(computation["inputs"].values())
        result = set(inputs[0])
        for input_set in inputs[1:]:
            result = result.intersection(set(input_set))
        return list(result)
