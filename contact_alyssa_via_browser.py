"""
Contact Alyssa via Browser

This script uses Selenium to automate the browser and send an email to <PERSON><PERSON> through Gmail's web interface.
"""
import os
import sys
import time
import logging
import argparse
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("contact_alyssa_via_browser.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("contact_alyssa_via_browser")

# Constants
GMAIL_URL = "https://mail.google.com"
EMAIL_SUBJECT = "IUL Policy and Health Insurance Options"
EMAIL_RECIPIENT = "<EMAIL>"  # Replace with <PERSON><PERSON>'s actual email if different

# Email template for Alyssa
EMAIL_TEMPLATE = """
Dear Alyssa,

Thank you for your interest in our insurance products. Based on your $100/month budget, I'd like to discuss some options for an Indexed Universal Life (IUL) policy structured for maximum cash value growth, along with basic health, dental, and vision plans.

Here's what I'm thinking:

1. IUL Policy: We can structure this for optimal cash value growth while maintaining the life insurance benefit. This would be approximately $60-70 of your monthly budget.

2. Health Insurance: For the remaining $30-40, we can look at basic health plans that cover essential services.

3. Dental & Vision: We have some affordable options that can be added if your budget allows, or we can discuss slightly exceeding your budget if these are priorities for you.

Would you be available for a quick call to discuss these options in more detail? I can answer any questions you might have and provide specific policy recommendations based on your needs.

Please let me know what days and times work best for you.

Best regards,
Paul Edwards
Flo Faction Insurance
Phone: (*************
Email: <EMAIL>
"""

def setup_driver():
    """Set up the Chrome driver."""
    logger.info("Setting up Chrome driver...")
    
    try:
        chrome_options = Options()
        chrome_options.add_argument("--start-maximized")
        chrome_options.add_argument("--disable-notifications")
        
        # Use existing Chrome browser data
        user_data_dir = os.path.join(os.environ.get("LOCALAPPDATA", ""), "UI-TARS", "browser_data")
        if os.path.exists(user_data_dir):
            chrome_options.add_argument(f"--user-data-dir={user_data_dir}")
            chrome_options.add_argument("--profile-directory=Default")
        
        # Initialize the driver
        driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)
        driver.implicitly_wait(10)
        
        logger.info("Chrome driver set up successfully")
        return driver
    except Exception as e:
        logger.error(f"Error setting up Chrome driver: {e}")
        return None

def navigate_to_gmail(driver):
    """Navigate to Gmail."""
    logger.info("Navigating to Gmail...")
    
    try:
        driver.get(GMAIL_URL)
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//div[contains(text(), 'Compose')]"))
        )
        
        logger.info("Successfully navigated to Gmail")
        return True
    except Exception as e:
        logger.error(f"Error navigating to Gmail: {e}")
        return False

def login_to_gmail(driver, email, password):
    """Login to Gmail."""
    logger.info(f"Logging in to Gmail as {email}...")
    
    try:
        # Check if already logged in
        if "mail.google.com/mail/u/0" in driver.current_url:
            logger.info("Already logged in to Gmail")
            return True
            
        # Enter email
        email_input = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//input[@type='email']"))
        )
        email_input.clear()
        email_input.send_keys(email)
        email_input.send_keys(Keys.RETURN)
        
        # Wait for password field
        time.sleep(2)
        
        # Enter password
        password_input = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//input[@type='password']"))
        )
        password_input.clear()
        password_input.send_keys(password)
        password_input.send_keys(Keys.RETURN)
        
        # Wait for Gmail to load
        WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.XPATH, "//div[contains(text(), 'Compose')]"))
        )
        
        logger.info("Successfully logged in to Gmail")
        return True
    except Exception as e:
        logger.error(f"Error logging in to Gmail: {e}")
        return False

def compose_email(driver, recipient, subject, body):
    """Compose an email."""
    logger.info(f"Composing email to {recipient}...")
    
    try:
        # Click Compose button
        compose_button = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, "//div[contains(text(), 'Compose')]"))
        )
        compose_button.click()
        
        # Wait for compose window
        time.sleep(2)
        
        # Enter recipient
        to_input = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//input[@role='combobox' and contains(@aria-label, 'To')]"))
        )
        to_input.send_keys(recipient)
        
        # Enter subject
        subject_input = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//input[@name='subjectbox']"))
        )
        subject_input.send_keys(subject)
        
        # Enter body
        body_input = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//div[@role='textbox' and @aria-label='Message Body']"))
        )
        body_input.send_keys(body)
        
        logger.info("Email composed successfully")
        return True
    except Exception as e:
        logger.error(f"Error composing email: {e}")
        return False

def send_email(driver):
    """Send the email."""
    logger.info("Sending email...")
    
    try:
        # Click Send button
        send_button = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, "//div[contains(@role, 'button') and contains(text(), 'Send')]"))
        )
        send_button.click()
        
        # Wait for confirmation
        time.sleep(3)
        
        logger.info("Email sent successfully")
        return True
    except Exception as e:
        logger.error(f"Error sending email: {e}")
        return False

def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Contact Alyssa via Browser")
    parser.add_argument("--email", type=str, default="<EMAIL>", help="Gmail email address")
    parser.add_argument("--password", type=str, default="GodisSoGood!777", help="Gmail password")
    parser.add_argument("--recipient", type=str, default=EMAIL_RECIPIENT, help="Recipient email address")
    parser.add_argument("--subject", type=str, default=EMAIL_SUBJECT, help="Email subject")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    parser.add_argument("--dry-run", action="store_true", help="Don't actually send the email")
    
    args = parser.parse_args()
    
    # Set log level
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        
    print("Contact Alyssa via Browser")
    print("=========================")
    print()
    
    # Set up driver
    driver = setup_driver()
    if not driver:
        print("❌ Failed to set up Chrome driver")
        return 1
        
    try:
        # Navigate to Gmail
        if not navigate_to_gmail(driver):
            print("❌ Failed to navigate to Gmail")
            return 1
            
        print("✅ Successfully navigated to Gmail")
        
        # Login to Gmail if needed
        if "mail.google.com/mail/u/0" not in driver.current_url:
            if not login_to_gmail(driver, args.email, args.password):
                print("❌ Failed to login to Gmail")
                return 1
                
            print("✅ Successfully logged in to Gmail")
        else:
            print("✅ Already logged in to Gmail")
        
        # Compose email
        if not compose_email(driver, args.recipient, args.subject, EMAIL_TEMPLATE):
            print("❌ Failed to compose email")
            return 1
            
        print("✅ Successfully composed email to Alyssa")
        
        # Send email if not dry run
        if not args.dry_run:
            if not send_email(driver):
                print("❌ Failed to send email")
                return 1
                
            print("✅ Successfully sent email to Alyssa")
        else:
            print("ℹ️ Dry run - email not sent")
            
        print()
        print("Email Details:")
        print(f"- From: {args.email}")
        print(f"- To: {args.recipient}")
        print(f"- Subject: {args.subject}")
        print(f"- Content: {EMAIL_TEMPLATE[:50]}...")
        
        return 0
    finally:
        # Close the driver
        if driver:
            driver.quit()

if __name__ == "__main__":
    sys.exit(main())
