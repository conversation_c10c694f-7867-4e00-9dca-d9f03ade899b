# PowerShell script to download and install Python

# Define the Python version to install
$pythonVersion = "3.10.11"
$pythonUrl = "https://www.python.org/ftp/python/$pythonVersion/python-$pythonVersion-amd64.exe"
$pythonInstaller = "$env:TEMP\python-$pythonVersion-amd64.exe"

# Download Python installer
Write-Host "Downloading Python $pythonVersion..."
Invoke-WebRequest -Uri $pythonUrl -OutFile $pythonInstaller

# Install Python
Write-Host "Installing Python $pythonVersion..."
Start-Process -FilePath $pythonInstaller -ArgumentList "/quiet", "InstallAllUsers=1", "PrependPath=1", "Include_test=0" -Wait

# Verify installation
Write-Host "Verifying Python installation..."
$env:Path = [System.Environment]::GetEnvironmentVariable("Path", "Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path", "User")
python --version

# Clean up
Remove-Item $pythonInstaller

Write-Host "Python installation completed."
