"""
Update multi-account Gmail service configuration.
This script helps you update the multi-account Gmail service configuration.
"""
import os
import sys
import json
from pathlib import Path

def get_configured_accounts():
    """
    Get a list of configured Gmail accounts.
    
    Returns:
        list: List of configured Gmail accounts
    """
    accounts = []
    credentials_dir = 'credentials'
    
    if not os.path.exists(credentials_dir):
        return accounts
    
    for filename in os.listdir(credentials_dir):
        if filename.startswith('gmail_') and filename.endswith('_credentials.json'):
            # Extract email from filename
            email_part = filename[6:-16]  # Remove 'gmail_' prefix and '_credentials.json' suffix
            email = email_part.replace('_at_', '@').replace('_dot_', '.')
            accounts.append(email)
    
    return accounts

def update_email_accounts_config():
    """
    Update the email_accounts.json configuration file.
    """
    print("=== Update Email Accounts Configuration ===")
    
    # Load existing configuration
    config_path = 'config/email_accounts.json'
    
    if not os.path.exists(config_path):
        print(f"Error: Configuration file not found at {config_path}")
        print("Creating a new configuration file...")
        
        # Create config directory if it doesn't exist
        os.makedirs('config', exist_ok=True)
        
        # Create default configuration
        config = {
            "priority_accounts": [],
            "additional_accounts": []
        }
    else:
        # Load existing configuration
        with open(config_path, 'r') as f:
            config = json.load(f)
    
    # Get configured accounts
    configured_accounts = get_configured_accounts()
    
    if not configured_accounts:
        print("No Gmail accounts configured.")
        print("Please run setup_gmail_credentials.py to configure your accounts.")
        return
    
    print(f"Found {len(configured_accounts)} configured Gmail accounts:")
    for i, email in enumerate(configured_accounts):
        print(f"{i+1}. {email}")
    
    # Update configuration
    print("\nUpdating configuration...")
    
    # Get existing emails
    existing_emails = []
    for account in config.get('priority_accounts', []):
        existing_emails.append(account['email'])
    for account in config.get('additional_accounts', []):
        existing_emails.append(account['email'])
    
    # Add new accounts
    for email in configured_accounts:
        if email not in existing_emails:
            print(f"Adding new account: {email}")
            
            # Ask for account details
            description = input(f"Enter description for {email}: ")
            purpose = input(f"Enter purpose for {email}: ")
            
            # Ask if this is a priority account
            is_priority = input(f"Is {email} a priority account? (y/n): ").lower() == 'y'
            
            # Create account object
            account = {
                "email": email,
                "description": description,
                "purpose": purpose,
                "priority": 0  # Will be updated later
            }
            
            # Add to appropriate list
            if is_priority:
                config['priority_accounts'].append(account)
            else:
                config['additional_accounts'].append(account)
    
    # Update priorities
    print("\nUpdating priorities...")
    
    # Sort priority accounts
    if config['priority_accounts']:
        print("\nPriority accounts:")
        for i, account in enumerate(config['priority_accounts']):
            print(f"{i+1}. {account['email']} - {account['description']}")
        
        print("\nDo you want to change the order of priority accounts?")
        change_order = input("(y/n): ").lower() == 'y'
        
        if change_order:
            # Get new order
            print("\nEnter the new order of priority accounts (comma-separated indices):")
            print("Example: 2,1,3 means the second account becomes first, the first becomes second, and the third stays third")
            
            try:
                new_order = [int(i) - 1 for i in input("New order: ").split(',')]
                
                if len(new_order) != len(config['priority_accounts']):
                    print("Error: Number of indices doesn't match number of accounts.")
                else:
                    # Reorder accounts
                    config['priority_accounts'] = [config['priority_accounts'][i] for i in new_order]
            except ValueError:
                print("Error: Invalid input. Keeping current order.")
    
    # Update priority numbers
    for i, account in enumerate(config['priority_accounts']):
        account['priority'] = i + 1
    
    for i, account in enumerate(config['additional_accounts']):
        account['priority'] = len(config['priority_accounts']) + i + 1
    
    # Save configuration
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=4)
    
    print(f"\nConfiguration saved to {config_path}")
    print("You can now use the multi-account Gmail service in your AI Agent System.")

if __name__ == "__main__":
    update_email_accounts_config()
