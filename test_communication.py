"""
Script to test communication services.
"""
import sys
import asyncio
import argparse
from pathlib import Path

from services.communication_service import CommunicationServiceFactory
from core.logger import setup_logger
import config

# Set up logger
logger = setup_logger("test_communication")

async def test_email(recipient: str, subject: str, content: str):
    """
    Test email service.
    
    Args:
        recipient (str): Recipient email address
        subject (str): Email subject
        content (str): Email content
    """
    logger.info("Testing email service")
    
    # Create email service
    email_service = CommunicationServiceFactory.create_service("email")
    
    if not email_service:
        logger.error("Email service not available")
        return False
    
    if not email_service.is_enabled():
        logger.error("Email service is not enabled")
        logger.info("Check your .env file for EMAIL_API_KEY and SENDER_EMAIL")
        return False
    
    # Send email
    logger.info(f"Sending email to {recipient}")
    result = await email_service.send_message(
        recipient=recipient,
        content=content,
        subject=subject,
        html_content=f"<html><body><h1>{subject}</h1><p>{content}</p></body></html>"
    )
    
    # Check result
    if "error" in result:
        logger.error(f"Error sending email: {result['error']}")
        return False
    
    logger.info(f"Email sent successfully: {result}")
    return True

async def test_sms(recipient: str, content: str):
    """
    Test SMS service.
    
    Args:
        recipient (str): Recipient phone number
        content (str): SMS content
    """
    logger.info("Testing SMS service")
    
    # Create SMS service
    sms_service = CommunicationServiceFactory.create_service("sms")
    
    if not sms_service:
        logger.error("SMS service not available")
        return False
    
    if not sms_service.is_enabled():
        logger.error("SMS service is not enabled")
        logger.info("Check your .env file for TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN, and TWILIO_PHONE_NUMBER")
        return False
    
    # Send SMS
    logger.info(f"Sending SMS to {recipient}")
    result = await sms_service.send_message(
        recipient=recipient,
        content=content
    )
    
    # Check result
    if "error" in result:
        logger.error(f"Error sending SMS: {result['error']}")
        return False
    
    logger.info(f"SMS sent successfully: {result}")
    return True

async def test_voice(recipient: str, content: str):
    """
    Test voice service.
    
    Args:
        recipient (str): Recipient phone number
        content (str): Voice message content
    """
    logger.info("Testing voice service")
    
    # Create voice service
    voice_service = CommunicationServiceFactory.create_service("voice")
    
    if not voice_service:
        logger.error("Voice service not available")
        return False
    
    if not voice_service.is_enabled():
        logger.error("Voice service is not enabled")
        logger.info("Check your .env file for TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN, and TWILIO_PHONE_NUMBER")
        return False
    
    # Make call
    logger.info(f"Making call to {recipient}")
    result = await voice_service.send_message(
        recipient=recipient,
        content=content
    )
    
    # Check result
    if "error" in result:
        logger.error(f"Error making call: {result['error']}")
        return False
    
    logger.info(f"Call initiated successfully: {result}")
    return True

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Test communication services")
    parser.add_argument("service", choices=["email", "sms", "voice"], help="Service to test")
    parser.add_argument("recipient", help="Recipient (email or phone number)")
    parser.add_argument("--subject", default="Test from Multi-Agent AI System", help="Subject (for email)")
    parser.add_argument("--content", default="This is a test message from the Multi-Agent AI System.", help="Message content")
    args = parser.parse_args()
    
    # Run test
    if args.service == "email":
        success = asyncio.run(test_email(args.recipient, args.subject, args.content))
    elif args.service == "sms":
        success = asyncio.run(test_sms(args.recipient, args.content))
    elif args.service == "voice":
        success = asyncio.run(test_voice(args.recipient, args.content))
    else:
        print(f"Unknown service: {args.service}")
        return 1
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
