"""
Enhanced UI-TARS Connector for the Multi-Agent AI System.

This module provides an improved connector to interface with UI-TARS 1.5,
with enhanced browser detection, connection reliability, and error recovery.
"""
import os
import sys
import json
import asyncio
import logging
import subprocess
import requests
import time
import platform
import psutil
import socket
import shutil
import tempfile
from typing import Dict, List, Optional, Any, Union, Tuple
from pathlib import Path

try:
    from core.logger import setup_logger
except ImportError:
    # Fallback logging setup if core.logger is not available
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler("ui_tars_connector.log")
        ]
    )

    def setup_logger(name):
        return logging.getLogger(name)

# Set up logger
logger = setup_logger("enhanced_ui_tars_connector")

class EnhancedUITarsConnector:
    """
    Enhanced connector for UI-TARS 1.5.

    This class provides improved methods to interface with UI-TARS 1.5,
    with better browser detection, connection reliability, and error recovery.
    """

    def __init__(self,
                 api_url: Optional[str] = "http://localhost:8080",
                 api_key: Optional[str] = None,
                 model_name: Optional[str] = "UI-TARS-1.5-7B",
                 installation_path: Optional[str] = None,
                 browser_type: str = "chrome",
                 browser_path: Optional[str] = None,
                 remote_debugging_port: int = 9222,
                 max_retries: int = 3,
                 retry_delay: int = 5,
                 auto_start: bool = True,
                 auto_restart: bool = True):
        """
        Initialize the Enhanced UI-TARS connector.

        Args:
            api_url (Optional[str]): URL of the UI-TARS API
            api_key (Optional[str]): API key for UI-TARS
            model_name (Optional[str]): Name of the model to use
            installation_path (Optional[str]): Path to UI-TARS installation
            browser_type (str): Type of browser to use
            browser_path (Optional[str]): Path to browser executable
            remote_debugging_port (int): Port for browser remote debugging
            max_retries (int): Maximum number of connection retries
            retry_delay (int): Delay between retries in seconds
            auto_start (bool): Whether to automatically start UI-TARS
            auto_restart (bool): Whether to automatically restart UI-TARS on failure
        """
        self.api_url = api_url
        self.api_key = api_key
        self.model_name = model_name
        self.installation_path = installation_path
        self.browser_type = browser_type.lower()
        self.browser_path = browser_path
        self.remote_debugging_port = remote_debugging_port
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.auto_start = auto_start
        self.auto_restart = auto_restart

        self.session = None
        self.ui_tars_process = None
        self.browser_process = None
        self.is_running = False
        self.is_connected = False
        self.os_type = platform.system()  # 'Windows', 'Darwin' (macOS), or 'Linux'
        self.connection_lock = asyncio.Lock()
        self.last_error = None
        self.startup_attempts = 0
        self.max_startup_attempts = 3

    async def initialize(self):
        """Initialize the Enhanced UI-TARS connector with improved reliability."""
        logger.info("Initializing Enhanced UI-TARS connector")

        # Create a session for API requests
        self.session = requests.Session()
        if self.api_key:
            self.session.headers.update({"Authorization": f"Bearer {self.api_key}"})

        # Find UI-TARS installation if not provided
        if not self.installation_path:
            self.installation_path = await self._find_ui_tars_installation()
            if not self.installation_path:
                logger.error("UI-TARS installation not found")
                return False

        # Find browser if path not provided
        if not self.browser_path:
            self.browser_path = await self._find_browser()
            if not self.browser_path:
                logger.error(f"Browser '{self.browser_type}' not found")
                return False

        logger.info(f"UI-TARS installation: {self.installation_path}")
        logger.info(f"Browser: {self.browser_type} at {self.browser_path}")

        # Start UI-TARS if auto-start is enabled
        if self.auto_start:
            success = await self.start()
            if not success:
                logger.error("Failed to start UI-TARS")
                return False

        logger.info("Enhanced UI-TARS connector initialized successfully")
        return True

    async def _find_ui_tars_installation(self) -> Optional[str]:
        """Find UI-TARS installation path."""
        logger.info("Searching for UI-TARS installation")

        # Common installation paths
        if self.os_type == "Windows":
            paths = [
                os.path.join(os.environ.get("LOCALAPPDATA", ""), "UI-TARS", "UI-TARS.exe"),
                os.path.join(os.environ.get("PROGRAMFILES", ""), "UI-TARS", "UI-TARS.exe"),
                os.path.join(os.environ.get("PROGRAMFILES(X86)", ""), "UI-TARS", "UI-TARS.exe"),
                "C:\\UI-TARS\\UI-TARS.exe"
            ]
        elif self.os_type == "Darwin":  # macOS
            paths = [
                "/Applications/UI-TARS.app/Contents/MacOS/UI-TARS",
                os.path.expanduser("~/Applications/UI-TARS.app/Contents/MacOS/UI-TARS")
            ]
        else:  # Linux
            paths = [
                "/usr/bin/ui-tars",
                "/usr/local/bin/ui-tars",
                os.path.expanduser("~/.local/bin/ui-tars")
            ]

        # Check if any of the paths exist
        for path in paths:
            if os.path.exists(path):
                logger.info(f"Found UI-TARS at: {path}")
                return path

        # If not found in common paths, search for running process
        for proc in psutil.process_iter(['pid', 'name', 'exe']):
            try:
                if 'UI-TARS' in proc.info['name']:
                    exe = proc.info['exe']
                    if exe and os.path.exists(exe):
                        logger.info(f"Found running UI-TARS at: {exe}")
                        return exe
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass

        logger.warning("UI-TARS installation not found")
        return None

    async def _find_browser(self) -> Optional[str]:
        """Find browser executable path."""
        logger.info(f"Searching for {self.browser_type} browser")

        # Common browser paths
        if self.os_type == "Windows":
            browser_paths = {
                "chrome": [
                    os.path.join(os.environ.get("PROGRAMFILES", ""), "Google", "Chrome", "Application", "chrome.exe"),
                    os.path.join(os.environ.get("PROGRAMFILES(X86)", ""), "Google", "Chrome", "Application", "chrome.exe"),
                    os.path.join(os.environ.get("LOCALAPPDATA", ""), "Google", "Chrome", "Application", "chrome.exe")
                ],
                "edge": [
                    os.path.join(os.environ.get("PROGRAMFILES", ""), "Microsoft", "Edge", "Application", "msedge.exe"),
                    os.path.join(os.environ.get("PROGRAMFILES(X86)", ""), "Microsoft", "Edge", "Application", "msedge.exe")
                ],
                "firefox": [
                    os.path.join(os.environ.get("PROGRAMFILES", ""), "Mozilla Firefox", "firefox.exe"),
                    os.path.join(os.environ.get("PROGRAMFILES(X86)", ""), "Mozilla Firefox", "firefox.exe")
                ],
                "brave": [
                    os.path.join(os.environ.get("PROGRAMFILES", ""), "BraveSoftware", "Brave-Browser", "Application", "brave.exe"),
                    os.path.join(os.environ.get("LOCALAPPDATA", ""), "BraveSoftware", "Brave-Browser", "Application", "brave.exe")
                ]
            }
        elif self.os_type == "Darwin":  # macOS
            browser_paths = {
                "chrome": [
                    "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                    os.path.expanduser("~/Applications/Google Chrome.app/Contents/MacOS/Google Chrome")
                ],
                "edge": [
                    "/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge",
                    os.path.expanduser("~/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge")
                ],
                "firefox": [
                    "/Applications/Firefox.app/Contents/MacOS/firefox",
                    os.path.expanduser("~/Applications/Firefox.app/Contents/MacOS/firefox")
                ],
                "brave": [
                    "/Applications/Brave Browser.app/Contents/MacOS/Brave Browser",
                    os.path.expanduser("~/Applications/Brave Browser.app/Contents/MacOS/Brave Browser")
                ]
            }
        else:  # Linux
            browser_paths = {
                "chrome": [
                    "/usr/bin/google-chrome",
                    "/usr/bin/google-chrome-stable"
                ],
                "edge": [
                    "/usr/bin/microsoft-edge",
                    "/usr/bin/microsoft-edge-stable"
                ],
                "firefox": [
                    "/usr/bin/firefox"
                ],
                "brave": [
                    "/usr/bin/brave-browser",
                    "/usr/bin/brave"
                ]
            }

        # Check if browser type is supported
        if self.browser_type not in browser_paths:
            logger.error(f"Unsupported browser type: {self.browser_type}")
            return None

        # Check if any of the paths exist
        for path in browser_paths.get(self.browser_type, []):
            if os.path.exists(path):
                logger.info(f"Found {self.browser_type} at: {path}")
                return path

        logger.warning(f"{self.browser_type} browser not found")
        return None

    async def start(self) -> bool:
        """
        Start UI-TARS with enhanced reliability.

        Returns:
            bool: True if started successfully, False otherwise
        """
        async with self.connection_lock:
            if self.is_running:
                logger.info("UI-TARS is already running")
                return True

            logger.info("Starting UI-TARS with enhanced reliability")

            # Check if UI-TARS is already running
            if await self._check_ui_tars_running():
                logger.info("UI-TARS is already running (detected existing process)")
                self.is_running = True
                return await self._ensure_connection()

            # Start browser with remote debugging if needed
            browser_started = await self._start_browser()
            if not browser_started:
                logger.error("Failed to start browser")
                return False

            # Start UI-TARS
            ui_tars_started = await self._start_ui_tars()
            if not ui_tars_started:
                logger.error("Failed to start UI-TARS")
                return False

            # Wait for UI-TARS to initialize
            await asyncio.sleep(5)

            # Check connection
            connection_success = await self._ensure_connection()
            if not connection_success:
                logger.error("Failed to connect to UI-TARS API")
                if self.auto_restart and self.startup_attempts < self.max_startup_attempts:
                    self.startup_attempts += 1
                    logger.info(f"Attempting to restart UI-TARS (attempt {self.startup_attempts}/{self.max_startup_attempts})")
                    await self.stop()
                    await asyncio.sleep(2)
                    return await self.start()
                return False

            logger.info("UI-TARS started successfully")
            self.startup_attempts = 0
            return True

    async def stop(self) -> bool:
        """
        Stop UI-TARS and browser processes.

        Returns:
            bool: True if stopped successfully, False otherwise
        """
        async with self.connection_lock:
            logger.info("Stopping UI-TARS")

            # Stop UI-TARS process
            if self.ui_tars_process:
                try:
                    logger.info("Terminating UI-TARS process")
                    self.ui_tars_process.terminate()
                    await asyncio.sleep(2)
                    if self.ui_tars_process.poll() is None:
                        logger.info("Killing UI-TARS process")
                        self.ui_tars_process.kill()
                except Exception as e:
                    logger.error(f"Error stopping UI-TARS process: {e}")

            # Stop browser process
            if self.browser_process:
                try:
                    logger.info("Terminating browser process")
                    self.browser_process.terminate()
                    await asyncio.sleep(2)
                    if self.browser_process.poll() is None:
                        logger.info("Killing browser process")
                        self.browser_process.kill()
                except Exception as e:
                    logger.error(f"Error stopping browser process: {e}")

            # Reset state
            self.is_running = False
            self.is_connected = False
            self.ui_tars_process = None
            self.browser_process = None

            logger.info("UI-TARS stopped")
            return True

    async def restart(self) -> bool:
        """
        Restart UI-TARS with enhanced reliability.

        Returns:
            bool: True if restarted successfully, False otherwise
        """
        logger.info("Restarting UI-TARS")

        # Stop UI-TARS
        await self.stop()

        # Wait before restarting
        await asyncio.sleep(3)

        # Start UI-TARS
        return await self.start()

    async def execute_command(self, command: str, screenshot: Optional[str] = None, max_retries: int = 3) -> Dict:
        """
        Execute a command in UI-TARS with enhanced reliability.

        Args:
            command (str): Command to execute
            screenshot (Optional[str]): Path to screenshot to analyze
            max_retries (int): Maximum number of retries

        Returns:
            Dict: Response from UI-TARS
        """
        # Ensure connection
        if not self.is_connected:
            connection_success = await self._ensure_connection()
            if not connection_success:
                # Try to restart UI-TARS if auto-restart is enabled
                if self.auto_restart:
                    logger.info("Attempting to restart UI-TARS")
                    restart_success = await self.restart()
                    if not restart_success:
                        return {"error": "Failed to connect to UI-TARS API and restart failed"}
                else:
                    return {"error": "Not connected to UI-TARS API"}

        logger.info(f"Executing command: {command}")

        # Track retries
        retries = 0
        last_error = None

        while retries <= max_retries:
            try:
                # Prepare the request
                data = {
                    "command": command,
                    "model": self.model_name
                }

                # Add screenshot if provided
                if screenshot and os.path.exists(screenshot):
                    with open(screenshot, "rb") as f:
                        files = {"screenshot": f}
                        response = self.session.post(f"{self.api_url}/execute", data=data, files=files, timeout=30)
                else:
                    response = self.session.post(f"{self.api_url}/execute", json=data, timeout=30)

                response.raise_for_status()

                # Parse the response
                result = response.json()

                # Check if the response indicates an error
                if "error" in result:
                    error_msg = result["error"]
                    logger.warning(f"UI-TARS returned an error: {error_msg}")

                    # If it's a connection error, try to reconnect
                    if "connection" in error_msg.lower() or "timeout" in error_msg.lower():
                        self.is_connected = False
                        connection_success = await self._ensure_connection()
                        if connection_success:
                            retries += 1
                            continue

                logger.info("Command executed successfully")
                return result

            except requests.exceptions.RequestException as e:
                last_error = str(e)
                logger.error(f"Error executing command (attempt {retries+1}/{max_retries+1}): {e}")

                # Try to reconnect
                self.is_connected = False
                connection_success = await self._ensure_connection()

                if not connection_success and self.auto_restart:
                    # Try to restart UI-TARS
                    logger.info("Attempting to restart UI-TARS")
                    restart_success = await self.restart()
                    if not restart_success:
                        logger.error("Failed to restart UI-TARS")

                retries += 1
                if retries <= max_retries:
                    # Wait before retrying
                    await asyncio.sleep(self.retry_delay)
                    logger.info(f"Retrying command (attempt {retries+1}/{max_retries+1})")

            except Exception as e:
                last_error = str(e)
                logger.exception(f"Unexpected error executing command (attempt {retries+1}/{max_retries+1}): {e}")
                retries += 1
                if retries <= max_retries:
                    # Wait before retrying
                    await asyncio.sleep(self.retry_delay)
                    logger.info(f"Retrying command (attempt {retries+1}/{max_retries+1})")

        # All retries failed
        return {"error": f"Failed to execute command after {max_retries+1} attempts: {last_error}"}

    async def _check_ui_tars_running(self) -> bool:
        """
        Check if UI-TARS is already running.

        Returns:
            bool: True if running, False otherwise
        """
        # Check if UI-TARS API is responding
        try:
            response = requests.get(f"{self.api_url}/health", timeout=2)
            if response.status_code == 200:
                logger.info("UI-TARS API is responding")
                return True
        except:
            pass

        # Check for UI-TARS process
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if 'UI-TARS' in proc.info['name']:
                    logger.info(f"Found running UI-TARS process: {proc.info['pid']}")
                    return True
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass

        return False

    async def _start_browser(self) -> bool:
        """
        Start browser with remote debugging enabled.

        Returns:
            bool: True if started successfully, False otherwise
        """
        if not self.browser_path:
            logger.error("Browser path not set")
            return False

        logger.info(f"Starting {self.browser_type} with remote debugging on port {self.remote_debugging_port}")

        # Check if port is already in use
        if await self._is_port_in_use(self.remote_debugging_port):
            logger.warning(f"Port {self.remote_debugging_port} is already in use")

            # Check if it's a browser with remote debugging
            if await self._check_browser_debugging_active():
                logger.info("Browser with remote debugging is already running")
                return True

            # Try to find and kill processes using the port
            if await self._kill_process_using_port(self.remote_debugging_port):
                logger.info(f"Killed process using port {self.remote_debugging_port}")
            else:
                logger.error(f"Failed to free port {self.remote_debugging_port}")
                return False

        try:
            # Create user data directory if it doesn't exist
            user_data_dir = os.path.join(tempfile.gettempdir(), "ui_tars_browser_data")
            os.makedirs(user_data_dir, exist_ok=True)

            # Prepare command
            command = [
                self.browser_path,
                f"--remote-debugging-port={self.remote_debugging_port}",
                f"--user-data-dir={user_data_dir}",
                "--no-first-run",
                "--no-default-browser-check",
                "--disable-extensions",
                "--disable-component-extensions-with-background-pages",
                "--disable-background-networking",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-breakpad",
                "--disable-client-side-phishing-detection",
                "--disable-default-apps",
                "--disable-dev-shm-usage",
                "--disable-features=Translate,BackForwardCache,AcceptCHFrame,MediaRouter,OptimizationHints",
                "--disable-hang-monitor",
                "--disable-ipc-flooding-protection",
                "--disable-popup-blocking",
                "--disable-prompt-on-repost",
                "--disable-renderer-backgrounding",
                "--disable-sync",
                "--disable-web-security",
                "--enable-automation",
                "--force-color-profile=srgb",
                "--metrics-recording-only",
                "--no-sandbox",
                "--password-store=basic",
                "--use-mock-keychain",
                "--window-size=1280,720",
                "about:blank"
            ]

            # Start browser process
            self.browser_process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW if self.os_type == "Windows" else 0
            )

            # Wait for browser to start
            await asyncio.sleep(3)

            # Check if browser started successfully
            if self.browser_process.poll() is not None:
                logger.error(f"Browser process exited with code {self.browser_process.returncode}")
                return False

            # Check if remote debugging is active
            for _ in range(5):  # Try 5 times
                if await self._check_browser_debugging_active():
                    logger.info("Browser remote debugging is active")
                    return True
                await asyncio.sleep(1)

            logger.error("Browser remote debugging not active after multiple attempts")
            return False

        except Exception as e:
            logger.exception(f"Error starting browser: {e}")
            return False

    async def _start_ui_tars(self) -> bool:
        """
        Start UI-TARS process.

        Returns:
            bool: True if started successfully, False otherwise
        """
        if not self.installation_path:
            logger.error("UI-TARS installation path not set")
            return False

        logger.info(f"Starting UI-TARS from {self.installation_path}")

        try:
            # Prepare command
            command = [self.installation_path]

            # Start UI-TARS process
            self.ui_tars_process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW if self.os_type == "Windows" else 0
            )

            # Wait for UI-TARS to start
            await asyncio.sleep(5)

            # Check if UI-TARS started successfully
            if self.ui_tars_process.poll() is not None:
                logger.error(f"UI-TARS process exited with code {self.ui_tars_process.returncode}")
                return False

            self.is_running = True
            logger.info("UI-TARS process started")
            return True

        except Exception as e:
            logger.exception(f"Error starting UI-TARS: {e}")
            return False

    async def _ensure_connection(self) -> bool:
        """
        Ensure connection to UI-TARS API with retries.

        Returns:
            bool: True if connected, False otherwise
        """
        logger.info("Ensuring connection to UI-TARS API")

        for attempt in range(self.max_retries):
            try:
                response = requests.get(f"{self.api_url}/health", timeout=5)
                if response.status_code == 200:
                    self.is_connected = True
                    logger.info("Connected to UI-TARS API")
                    return True

                logger.warning(f"UI-TARS API returned status code {response.status_code}")
            except requests.exceptions.RequestException as e:
                logger.warning(f"Connection attempt {attempt+1}/{self.max_retries} failed: {e}")

            # Wait before retrying
            await asyncio.sleep(self.retry_delay)

        self.is_connected = False
        self.last_error = "Failed to connect to UI-TARS API after multiple attempts"
        logger.error(self.last_error)
        return False

    async def _is_port_in_use(self, port: int) -> bool:
        """
        Check if a port is in use.

        Args:
            port (int): Port to check

        Returns:
            bool: True if in use, False otherwise
        """
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            return s.connect_ex(('localhost', port)) == 0

    async def _check_browser_debugging_active(self) -> bool:
        """
        Check if browser remote debugging is active.

        Returns:
            bool: True if active, False otherwise
        """
        try:
            response = requests.get(f"http://localhost:{self.remote_debugging_port}/json/version", timeout=2)
            return response.status_code == 200
        except:
            return False

    async def _kill_process_using_port(self, port: int) -> bool:
        """
        Kill process using a specific port.

        Args:
            port (int): Port to check

        Returns:
            bool: True if killed, False otherwise
        """
        if self.os_type == "Windows":
            try:
                # Find process using the port
                output = subprocess.check_output(f"netstat -ano | findstr :{port}", shell=True).decode()

                # Extract PID
                for line in output.splitlines():
                    if f":{port}" in line and "LISTENING" in line:
                        pid = line.strip().split()[-1]
                        try:
                            # Kill process
                            subprocess.check_output(f"taskkill /F /PID {pid}", shell=True)
                            logger.info(f"Killed process with PID {pid} using port {port}")
                            return True
                        except:
                            pass
            except:
                pass
        else:
            try:
                # Find process using the port
                output = subprocess.check_output(f"lsof -i :{port} -t", shell=True).decode()

                # Kill processes
                for pid in output.splitlines():
                    try:
                        subprocess.check_output(f"kill -9 {pid}", shell=True)
                        logger.info(f"Killed process with PID {pid} using port {port}")
                        return True
                    except:
                        pass
            except:
                pass

        return False

    async def health_check(self) -> Dict:
        """
        Perform a health check on the UI-TARS connector.

        Returns:
            Dict: Health check result
        """
        logger.info("Performing health check")

        result = {
            "status": "healthy",
            "issues": [],
            "components": {
                "ui_tars_api": False,
                "browser_debugging": False,
                "ui_tars_process": False,
                "browser_process": False
            }
        }

        # Check UI-TARS API
        try:
            response = requests.get(f"{self.api_url}/health", timeout=5)
            if response.status_code == 200:
                result["components"]["ui_tars_api"] = True
            else:
                result["issues"].append(f"UI-TARS API returned status code {response.status_code}")
        except Exception as e:
            result["issues"].append(f"UI-TARS API not responding: {str(e)}")

        # Check browser remote debugging
        try:
            if await self._check_browser_debugging_active():
                result["components"]["browser_debugging"] = True
            else:
                result["issues"].append("Browser remote debugging not active")
        except Exception as e:
            result["issues"].append(f"Error checking browser debugging: {str(e)}")

        # Check UI-TARS process
        if self.ui_tars_process:
            if self.ui_tars_process.poll() is None:
                result["components"]["ui_tars_process"] = True
            else:
                result["issues"].append(f"UI-TARS process exited with code {self.ui_tars_process.returncode}")
        else:
            # Check if UI-TARS is running as a separate process
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    if 'UI-TARS' in proc.info['name']:
                        result["components"]["ui_tars_process"] = True
                        break
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    pass

            if not result["components"]["ui_tars_process"]:
                result["issues"].append("UI-TARS process not found")

        # Check browser process
        if self.browser_process:
            if self.browser_process.poll() is None:
                result["components"]["browser_process"] = True
            else:
                result["issues"].append(f"Browser process exited with code {self.browser_process.returncode}")
        else:
            # Check if browser is running as a separate process
            browser_names = {
                "chrome": ["chrome", "chrome.exe"],
                "edge": ["msedge", "msedge.exe"],
                "firefox": ["firefox", "firefox.exe"],
                "brave": ["brave", "brave.exe"]
            }

            browser_name_patterns = browser_names.get(self.browser_type, [])

            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    for pattern in browser_name_patterns:
                        if pattern.lower() in proc.info['name'].lower():
                            result["components"]["browser_process"] = True
                            break
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    pass

            if not result["components"]["browser_process"]:
                result["issues"].append(f"{self.browser_type} browser process not found")

        # Update overall status
        if result["issues"]:
            result["status"] = "unhealthy"

        return result

    async def auto_repair(self) -> Dict:
        """
        Attempt to automatically repair the UI-TARS connector.

        Returns:
            Dict: Repair result
        """
        logger.info("Attempting to auto-repair UI-TARS connector")

        # Perform health check
        health = await self.health_check()

        if health["status"] == "healthy":
            logger.info("UI-TARS connector is healthy, no repair needed")
            return {
                "success": True,
                "message": "UI-TARS connector is healthy, no repair needed",
                "actions_taken": []
            }

        actions_taken = []

        # Check if browser debugging is active
        if not health["components"]["browser_debugging"]:
            logger.info("Browser remote debugging not active, attempting to start browser")

            # Kill any process using the remote debugging port
            if await self._is_port_in_use(self.remote_debugging_port):
                if await self._kill_process_using_port(self.remote_debugging_port):
                    actions_taken.append(f"Killed process using port {self.remote_debugging_port}")

            # Start browser
            browser_started = await self._start_browser()
            if browser_started:
                actions_taken.append(f"Started {self.browser_type} browser with remote debugging")
            else:
                logger.error("Failed to start browser")
                return {
                    "success": False,
                    "message": "Failed to start browser",
                    "actions_taken": actions_taken
                }

        # Check if UI-TARS API is responding
        if not health["components"]["ui_tars_api"]:
            logger.info("UI-TARS API not responding, attempting to restart UI-TARS")

            # Stop UI-TARS
            await self.stop()
            actions_taken.append("Stopped UI-TARS")

            # Start UI-TARS
            ui_tars_started = await self._start_ui_tars()
            if ui_tars_started:
                actions_taken.append("Started UI-TARS")
            else:
                logger.error("Failed to start UI-TARS")
                return {
                    "success": False,
                    "message": "Failed to start UI-TARS",
                    "actions_taken": actions_taken
                }

            # Wait for UI-TARS to initialize
            await asyncio.sleep(5)

            # Check connection
            connection_success = await self._ensure_connection()
            if connection_success:
                actions_taken.append("Connected to UI-TARS API")
            else:
                logger.error("Failed to connect to UI-TARS API")
                return {
                    "success": False,
                    "message": "Failed to connect to UI-TARS API",
                    "actions_taken": actions_taken
                }

        # Perform another health check
        health = await self.health_check()

        if health["status"] == "healthy":
            logger.info("UI-TARS connector repaired successfully")
            return {
                "success": True,
                "message": "UI-TARS connector repaired successfully",
                "actions_taken": actions_taken
            }
        else:
            logger.error(f"Failed to repair UI-TARS connector: {health['issues']}")
            return {
                "success": False,
                "message": f"Failed to repair UI-TARS connector: {health['issues']}",
                "actions_taken": actions_taken
            }
