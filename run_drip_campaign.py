"""
Run Drip Campaign for the Multi-Agent AI System.

This script runs the drip campaign for insurance leads, managing
the sequence of calls, voicemails, texts, and emails with decreasing frequency.
"""
import asyncio
import json
import logging
import os
import sys
import time
import argparse
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta

# Add parent directory to path to import from core
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.logger import setup_logger
from drip_campaign_workflow import DripCampaignWorkflow
from insurance_drip_campaign_agent import InsuranceDripCampaignAgent

# Set up logger
logger = setup_logger("run_drip_campaign")

# Global variables
workflow = None
shutdown_event = asyncio.Event()

async def initialize_workflow(args):
    """
    Initialize the drip campaign workflow.
    
    Args:
        args: Command-line arguments
        
    Returns:
        bool: True if initialization was successful, False otherwise
    """
    global workflow
    
    # Create workflow
    workflow = DripCampaignWorkflow(
        config_path=args.config,
        ui_tars_api_url=args.api_url,
        ui_tars_model_name=args.model,
        ui_tars_installation_path=args.ui_tars_path,
        elevenlabs_api_key=args.elevenlabs_key
    )
    
    # Initialize workflow
    success = await workflow.initialize()
    if not success:
        logger.error("Failed to initialize drip campaign workflow")
        return False
    
    logger.info("Drip campaign workflow initialized successfully")
    return True

async def start_campaign(args):
    """
    Start a drip campaign for a client.
    
    Args:
        args: Command-line arguments
        
    Returns:
        bool: True if campaign was started successfully, False otherwise
    """
    global workflow
    
    if not workflow:
        logger.error("Workflow not initialized")
        return False
    
    # Start campaign
    result = await workflow.start_campaign_for_client(
        client_name=args.name,
        phone_number=args.phone,
        email=args.email,
        insurance_type=args.insurance_type,
        budget=args.budget
    )
    
    if result.get("success"):
        logger.info(f"Started drip campaign for {args.name} with ID {result.get('campaign_id')}")
        return True
    else:
        logger.error(f"Failed to start drip campaign for {args.name}: {result.get('error')}")
        return False

async def stop_campaign(args):
    """
    Stop a drip campaign.
    
    Args:
        args: Command-line arguments
        
    Returns:
        bool: True if campaign was stopped successfully, False otherwise
    """
    global workflow
    
    if not workflow:
        logger.error("Workflow not initialized")
        return False
    
    # Stop campaign
    result = await workflow.stop_campaign(
        identifier=args.identifier,
        identifier_type=args.identifier_type
    )
    
    if result.get("success"):
        logger.info(f"Stopped drip campaign for {result.get('client_name')} with ID {result.get('campaign_id')}")
        return True
    else:
        logger.error(f"Failed to stop drip campaign with {args.identifier_type} {args.identifier}: {result.get('error')}")
        return False

async def handle_response(args):
    """
    Handle a response from a client.
    
    Args:
        args: Command-line arguments
        
    Returns:
        bool: True if response was handled successfully, False otherwise
    """
    global workflow
    
    if not workflow:
        logger.error("Workflow not initialized")
        return False
    
    # Handle response
    result = await workflow.handle_client_response(
        response_type=args.response_type,
        sender=args.sender,
        content=args.content
    )
    
    if result.get("success"):
        logger.info(f"Handled {args.response_type} response from {args.sender} for campaign {result.get('campaign_id')}")
        return True
    else:
        logger.error(f"Failed to handle {args.response_type} response from {args.sender}: {result.get('error')}")
        return False

async def run_agent(args):
    """
    Run the drip campaign agent.
    
    Args:
        args: Command-line arguments
    """
    global workflow
    global shutdown_event
    
    if not workflow:
        logger.error("Workflow not initialized")
        return
    
    # Get agent
    agent = workflow.drip_campaign_agent
    if not agent:
        logger.error("Agent not initialized")
        return
    
    logger.info(f"Running drip campaign agent with interval {args.interval} seconds")
    
    try:
        # Run until shutdown
        while not shutdown_event.is_set():
            # Execute agent cycle
            logger.info("Executing agent cycle...")
            await agent.execute_cycle()
            
            # Wait for next cycle
            try:
                await asyncio.wait_for(shutdown_event.wait(), timeout=args.interval)
            except asyncio.TimeoutError:
                pass
    
    except KeyboardInterrupt:
        logger.info("Keyboard interrupt received")
    
    except Exception as e:
        logger.exception(f"Error running agent: {e}")

async def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Run Drip Campaign")
    
    # Common arguments
    parser.add_argument("--config", default="config/drip_campaign_config.json", help="Path to drip campaign configuration")
    parser.add_argument("--api-url", default="http://localhost:8080", help="URL of the UI-TARS API")
    parser.add_argument("--model", default="UI-TARS-1.5-7B", help="Name of the model to use")
    parser.add_argument("--ui-tars-path", help="Path to UI-TARS installation")
    parser.add_argument("--elevenlabs-key", help="ElevenLabs API key")
    
    # Subparsers
    subparsers = parser.add_subparsers(dest="command", help="Command to run")
    
    # Start campaign
    start_parser = subparsers.add_parser("start", help="Start a drip campaign")
    start_parser.add_argument("--name", required=True, help="Client's name")
    start_parser.add_argument("--phone", required=True, help="Client's phone number")
    start_parser.add_argument("--email", required=True, help="Client's email address")
    start_parser.add_argument("--insurance-type", required=True, help="Type of insurance")
    start_parser.add_argument("--budget", required=True, help="Client's budget")
    
    # Stop campaign
    stop_parser = subparsers.add_parser("stop", help="Stop a drip campaign")
    stop_parser.add_argument("--identifier", required=True, help="Campaign identifier, client name, phone number, or email")
    stop_parser.add_argument("--identifier-type", default="campaign_id", choices=["campaign_id", "name", "phone", "email"], help="Type of identifier")
    
    # Handle response
    response_parser = subparsers.add_parser("response", help="Handle a response from a client")
    response_parser.add_argument("--response-type", required=True, choices=["email", "text", "call"], help="Type of response")
    response_parser.add_argument("--sender", required=True, help="Sender identifier (email or phone)")
    response_parser.add_argument("--content", required=True, help="Response content")
    
    # Run agent
    run_parser = subparsers.add_parser("run", help="Run the drip campaign agent")
    run_parser.add_argument("--interval", type=int, default=60, help="Interval between agent cycles in seconds")
    
    args = parser.parse_args()
    
    # Initialize workflow
    if not await initialize_workflow(args):
        return 1
    
    # Execute command
    if args.command == "start":
        if not await start_campaign(args):
            return 1
    elif args.command == "stop":
        if not await stop_campaign(args):
            return 1
    elif args.command == "response":
        if not await handle_response(args):
            return 1
    elif args.command == "run":
        await run_agent(args)
    else:
        parser.print_help()
        return 1
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\nInterrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)
