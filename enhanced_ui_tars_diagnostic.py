"""
Enhanced UI-TARS Diagnostic Tool

This script provides comprehensive diagnostics for UI-TARS 1.5, including:
- Browser detection and validation
- API connection testing
- Sandbox environment setup
- Virtual PC configuration
- DPO (Direct Preference Optimization) support
- Detailed error reporting and debugging
"""
import os
import sys
import time
import json
import logging
import argparse
import subprocess
import platform
import socket
import requests
import asyncio
import shutil
import tempfile
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Union

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("ui_tars_diagnostic.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("enhanced_ui_tars_diagnostic")

# Constants
DEFAULT_API_PORT = 8080
DEFAULT_BROWSER_PORT = 9222
DEFAULT_SANDBOX_PORT = 9223
DEFAULT_TIMEOUT = 10
BROWSER_TYPES = ["chrome", "edge", "firefox", "brave"]

class UITarsDiagnostic:
    """Enhanced diagnostic tool for UI-TARS 1.5."""

    def __init__(self, 
                 executable_path: Optional[str] = None,
                 api_host: str = "localhost",
                 api_port: int = DEFAULT_API_PORT,
                 browser_type: str = "chrome",
                 sandbox_mode: bool = True,
                 virtual_pc: bool = True,
                 dpo_enabled: bool = True):
        """
        Initialize the UI-TARS diagnostic tool.
        
        Args:
            executable_path: Path to UI-TARS executable
            api_host: Host for the UI-TARS API
            api_port: Port for the UI-TARS API
            browser_type: Type of browser to use
            sandbox_mode: Whether to use sandbox mode
            virtual_pc: Whether to use virtual PC mode
            dpo_enabled: Whether to enable DPO
        """
        self.executable_path = executable_path
        self.api_host = api_host
        self.api_port = api_port
        self.browser_type = browser_type.lower()
        self.sandbox_mode = sandbox_mode
        self.virtual_pc = virtual_pc
        self.dpo_enabled = dpo_enabled
        self.os_type = platform.system()
        self.browser_paths = {}
        self.ui_tars_process = None
        self.sandbox_process = None
        self.virtual_pc_process = None
        self.browser_process = None
        self.config_path = None
        self.temp_dir = None
        self.session = requests.Session()
        
        # Find UI-TARS executable if not provided
        if not self.executable_path:
            self.executable_path = self.find_ui_tars_executable()
            
        # Create temp directory for sandbox if needed
        if self.sandbox_mode:
            self.temp_dir = tempfile.mkdtemp(prefix="ui_tars_sandbox_")
            logger.info(f"Created temporary directory for sandbox: {self.temp_dir}")

    def find_ui_tars_executable(self) -> Optional[str]:
        """Find the UI-TARS executable."""
        logger.info("Searching for UI-TARS executable...")
        
        if self.os_type == "Windows":
            # Common installation locations on Windows
            possible_paths = [
                os.path.join(os.environ.get("PROGRAMFILES", "C:\\Program Files"), "UI-TARS", "UI-TARS.exe"),
                os.path.join(os.environ.get("PROGRAMFILES(X86)", "C:\\Program Files (x86)"), "UI-TARS", "UI-TARS.exe"),
                os.path.join(os.environ.get("LOCALAPPDATA", "C:\\Users\\<USER>\\AppData\\Local".format(os.getlogin())), "UI-TARS", "UI-TARS.exe"),
                "UI-TARS.exe"
            ]
        elif self.os_type == "Darwin":  # macOS
            # Common installation locations on macOS
            possible_paths = [
                "/Applications/UI-TARS.app/Contents/MacOS/UI-TARS",
                os.path.expanduser("~/Applications/UI-TARS.app/Contents/MacOS/UI-TARS"),
            ]
        else:  # Linux
            # Common installation locations on Linux
            possible_paths = [
                "/usr/local/bin/ui-tars",
                "/usr/bin/ui-tars",
                os.path.expanduser("~/.local/bin/ui-tars"),
            ]
            
        # Check if any of the paths exist
        for path in possible_paths:
            if os.path.exists(path):
                logger.info(f"Found UI-TARS executable at: {path}")
                return path
                
        # Try to find in PATH
        try:
            if self.os_type == "Windows":
                result = subprocess.run(["where", "UI-TARS.exe"], capture_output=True, text=True)
            else:
                result = subprocess.run(["which", "ui-tars"], capture_output=True, text=True)
                
            if result.returncode == 0:
                path = result.stdout.strip()
                logger.info(f"Found UI-TARS executable in PATH: {path}")
                return path
        except Exception as e:
            logger.debug(f"Error searching for UI-TARS in PATH: {e}")
            
        logger.warning("Could not find UI-TARS executable")
        return None

    def detect_browsers(self) -> Dict[str, str]:
        """Detect installed browsers and their paths."""
        logger.info("Detecting installed browsers...")
        
        browsers = {}
        
        if self.os_type == "Windows":
            # Check for common browsers on Windows
            paths = [
                (r"C:\Program Files\Google\Chrome\Application\chrome.exe", "chrome"),
                (r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe", "chrome"),
                (r"C:\Program Files\Mozilla Firefox\firefox.exe", "firefox"),
                (r"C:\Program Files (x86)\Mozilla Firefox\firefox.exe", "firefox"),
                (r"C:\Program Files\Microsoft\Edge\Application\msedge.exe", "edge"),
                (r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe", "edge"),
                (r"C:\Program Files\BraveSoftware\Brave-Browser\Application\brave.exe", "brave"),
                (r"C:\Program Files (x86)\BraveSoftware\Brave-Browser\Application\brave.exe", "brave"),
            ]
        elif self.os_type == "Darwin":  # macOS
            # Check for common browsers on macOS
            paths = [
                ("/Applications/Google Chrome.app/Contents/MacOS/Google Chrome", "chrome"),
                ("/Applications/Firefox.app/Contents/MacOS/firefox", "firefox"),
                ("/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge", "edge"),
                ("/Applications/Brave Browser.app/Contents/MacOS/Brave Browser", "brave"),
            ]
        else:  # Linux
            # Check for common browsers on Linux
            paths = [
                ("/usr/bin/google-chrome", "chrome"),
                ("/usr/bin/firefox", "firefox"),
                ("/usr/bin/microsoft-edge", "edge"),
                ("/usr/bin/brave-browser", "brave"),
            ]
            
        # Check if any of the paths exist
        for path, browser_type in paths:
            if os.path.exists(path):
                browsers[browser_type] = path
                logger.info(f"Found {browser_type} browser at: {path}")
                
        self.browser_paths = browsers
        return browsers

    def check_port_open(self, host: str, port: int) -> bool:
        """Check if a port is open."""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(DEFAULT_TIMEOUT)
                result = s.connect_ex((host, port))
                return result == 0
        except Exception as e:
            logger.debug(f"Error checking port {port}: {e}")
            return False

    def check_ui_tars_api(self) -> bool:
        """Check if the UI-TARS API is running."""
        logger.info(f"Checking if UI-TARS API is running on {self.api_host}:{self.api_port}...")
        
        if not self.check_port_open(self.api_host, self.api_port):
            logger.warning(f"UI-TARS API not running on {self.api_host}:{self.api_port}")
            return False
        
        try:
            # Try different API endpoints
            endpoints = [
                f"http://{self.api_host}:{self.api_port}/health",
                f"http://{self.api_host}:{self.api_port}/v1/models",
                f"http://{self.api_host}:{self.api_port}/api/status"
            ]
            
            for endpoint in endpoints:
                try:
                    logger.info(f"Trying endpoint: {endpoint}")
                    response = self.session.get(endpoint, timeout=DEFAULT_TIMEOUT)
                    
                    if response.status_code < 400:
                        logger.info(f"UI-TARS API is running (endpoint: {endpoint})")
                        return True
                except requests.exceptions.RequestException:
                    continue
            
            logger.warning("UI-TARS API is not responding to any known endpoints")
            return False
            
        except Exception as e:
            logger.error(f"Error checking UI-TARS API: {e}")
            return False

    def create_sandbox_config(self) -> str:
        """Create a sandbox configuration for UI-TARS."""
        logger.info("Creating sandbox configuration...")
        
        # Create config directory in temp dir
        config_dir = os.path.join(self.temp_dir, "config")
        os.makedirs(config_dir, exist_ok=True)
        
        # Create config file
        config_path = os.path.join(config_dir, "ui_tars_sandbox_config.json")
        
        # Detect browsers
        browsers = self.detect_browsers()
        browser_path = browsers.get(self.browser_type)
        
        # Create config
        config = {
            "ui_tars": {
                "version": "1.5",
                "enabled": True,
                "sandbox": {
                    "enabled": True,
                    "isolation_level": "high",
                    "temp_dir": self.temp_dir
                },
                "virtual_pc": {
                    "enabled": self.virtual_pc,
                    "memory": 2048,
                    "cpu_cores": 2
                },
                "dpo": {
                    "enabled": self.dpo_enabled,
                    "preference_model": "default"
                },
                "browser": {
                    "type": self.browser_type,
                    "executable_path": browser_path,
                    "remote_debugging_port": DEFAULT_BROWSER_PORT,
                    "sandbox_port": DEFAULT_SANDBOX_PORT,
                    "user_data_dir": os.path.join(self.temp_dir, "browser_data"),
                    "profile_directory": "Default",
                    "detection": {
                        "auto_detect": True,
                        "fallback_types": BROWSER_TYPES
                    }
                },
                "api": {
                    "host": self.api_host,
                    "port": self.api_port,
                    "timeout": DEFAULT_TIMEOUT,
                    "retry_attempts": 3
                },
                "debug": {
                    "enabled": True,
                    "log_level": "debug",
                    "log_file": os.path.join(self.temp_dir, "ui_tars_debug.log")
                }
            }
        }
        
        # Write config to file
        with open(config_path, "w") as f:
            json.dump(config, f, indent=2)
            
        logger.info(f"Sandbox configuration created at: {config_path}")
        self.config_path = config_path
        return config_path

    async def start_ui_tars_with_sandbox(self) -> bool:
        """Start UI-TARS with sandbox configuration."""
        if not self.executable_path:
            logger.error("UI-TARS executable not found")
            return False
            
        if not os.path.exists(self.executable_path):
            logger.error(f"UI-TARS executable not found at: {self.executable_path}")
            return False
            
        # Create sandbox config if needed
        if self.sandbox_mode and not self.config_path:
            self.config_path = self.create_sandbox_config()
            
        logger.info(f"Starting UI-TARS with sandbox from: {self.executable_path}")
        
        try:
            # Prepare command
            command = [self.executable_path]
            
            if self.config_path:
                command.extend(["--config", self.config_path])
                
            if self.sandbox_mode:
                command.append("--sandbox")
                
            if self.virtual_pc:
                command.append("--virtual-pc")
                
            if self.dpo_enabled:
                command.append("--dpo")
                
            # Start UI-TARS process
            self.ui_tars_process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Wait for the process to start
            await asyncio.sleep(5)
            
            # Check if process is still running
            if self.ui_tars_process.poll() is not None:
                logger.error(f"UI-TARS process exited with code: {self.ui_tars_process.returncode}")
                return False
                
            logger.info("UI-TARS started successfully")
            
            # Check if API is running
            for _ in range(5):  # Try 5 times
                if self.check_ui_tars_api():
                    logger.info("UI-TARS API is running")
                    return True
                    
                await asyncio.sleep(2)
                
            logger.warning("UI-TARS API did not start properly")
            return False
            
        except Exception as e:
            logger.exception(f"Error starting UI-TARS: {e}")
            return False

    async def start_browser_in_sandbox(self) -> bool:
        """Start a browser in sandbox mode."""
        if not self.sandbox_mode:
            logger.info("Sandbox mode is disabled, skipping browser sandbox")
            return True
            
        # Detect browsers
        browsers = self.detect_browsers()
        browser_path = browsers.get(self.browser_type)
        
        if not browser_path:
            logger.error(f"Browser {self.browser_type} not found")
            return False
            
        logger.info(f"Starting {self.browser_type} browser in sandbox mode")
        
        try:
            # Create user data directory
            user_data_dir = os.path.join(self.temp_dir, "browser_data")
            os.makedirs(user_data_dir, exist_ok=True)
            
            # Prepare command
            command = [
                browser_path,
                f"--remote-debugging-port={DEFAULT_BROWSER_PORT}",
                f"--user-data-dir={user_data_dir}",
                "--no-first-run",
                "--no-default-browser-check",
                "--disable-extensions",
                "--disable-sync",
                "--disable-background-networking",
                "--disable-default-apps",
                "--disable-translate",
                "--disable-features=TranslateUI",
                "--disable-infobars",
                "--disable-save-password-bubble",
                "--disable-notifications",
                "--disable-popup-blocking",
                "--disable-component-update",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
                "--disable-background-mode",
                "--disable-breakpad",
                "--disable-client-side-phishing-detection",
                "--disable-hang-monitor",
                "--disable-prompt-on-repost",
                "--metrics-recording-only",
                "--safebrowsing-disable-auto-update",
                "--password-store=basic",
                "--use-mock-keychain",
                "--no-sandbox",
                "about:blank"
            ]
            
            # Start browser process
            self.browser_process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Wait for the process to start
            await asyncio.sleep(3)
            
            # Check if process is still running
            if self.browser_process.poll() is not None:
                logger.error(f"Browser process exited with code: {self.browser_process.returncode}")
                return False
                
            logger.info(f"{self.browser_type} browser started successfully in sandbox mode")
            
            # Check if browser debugging port is open
            if self.check_port_open("localhost", DEFAULT_BROWSER_PORT):
                logger.info(f"Browser debugging port {DEFAULT_BROWSER_PORT} is open")
                return True
            else:
                logger.warning(f"Browser debugging port {DEFAULT_BROWSER_PORT} is not open")
                return False
                
        except Exception as e:
            logger.exception(f"Error starting browser in sandbox: {e}")
            return False

    async def cleanup(self):
        """Clean up resources."""
        logger.info("Cleaning up resources...")
        
        # Stop UI-TARS process
        if self.ui_tars_process:
            try:
                self.ui_tars_process.terminate()
                await asyncio.sleep(2)
                
                if self.ui_tars_process.poll() is None:
                    self.ui_tars_process.kill()
                    
                logger.info("UI-TARS process terminated")
            except Exception as e:
                logger.error(f"Error terminating UI-TARS process: {e}")
                
        # Stop browser process
        if self.browser_process:
            try:
                self.browser_process.terminate()
                await asyncio.sleep(2)
                
                if self.browser_process.poll() is None:
                    self.browser_process.kill()
                    
                logger.info("Browser process terminated")
            except Exception as e:
                logger.error(f"Error terminating browser process: {e}")
                
        # Remove temp directory
        if self.temp_dir and os.path.exists(self.temp_dir):
            try:
                shutil.rmtree(self.temp_dir)
                logger.info(f"Temporary directory removed: {self.temp_dir}")
            except Exception as e:
                logger.error(f"Error removing temporary directory: {e}")

async def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Enhanced UI-TARS Diagnostic Tool")
    parser.add_argument("--path", type=str, help="Path to UI-TARS executable")
    parser.add_argument("--host", type=str, default="localhost", help="UI-TARS API host")
    parser.add_argument("--port", type=int, default=DEFAULT_API_PORT, help="UI-TARS API port")
    parser.add_argument("--browser", type=str, default="chrome", choices=BROWSER_TYPES, help="Browser type")
    parser.add_argument("--sandbox", action="store_true", help="Use sandbox mode")
    parser.add_argument("--virtual-pc", action="store_true", help="Use virtual PC mode")
    parser.add_argument("--dpo", action="store_true", help="Enable DPO (Direct Preference Optimization)")
    parser.add_argument("--start", action="store_true", help="Start UI-TARS")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    
    args = parser.parse_args()
    
    # Set log level
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        
    print("Enhanced UI-TARS Diagnostic Tool")
    print("===============================")
    print()
    
    # Create diagnostic tool
    diagnostic = UITarsDiagnostic(
        executable_path=args.path,
        api_host=args.host,
        api_port=args.port,
        browser_type=args.browser,
        sandbox_mode=args.sandbox,
        virtual_pc=args.virtual_pc,
        dpo_enabled=args.dpo
    )
    
    try:
        # Check UI-TARS executable
        if diagnostic.executable_path:
            print(f"✅ UI-TARS executable found at: {diagnostic.executable_path}")
        else:
            print("❌ UI-TARS executable not found")
            print("Please provide the path to UI-TARS.exe with --path")
            return 1
            
        # Detect browsers
        browsers = diagnostic.detect_browsers()
        if browsers:
            print(f"✅ Found {len(browsers)} browsers:")
            for browser_type, path in browsers.items():
                print(f"  - {browser_type}: {path}")
        else:
            print("❌ No browsers detected")
            
        # Check if selected browser is available
        if diagnostic.browser_type in browsers:
            print(f"✅ Selected browser ({diagnostic.browser_type}) is available")
        else:
            print(f"❌ Selected browser ({diagnostic.browser_type}) is not available")
            print(f"Please select one of the available browsers: {', '.join(browsers.keys())}")
            
        # Check UI-TARS API
        api_running = diagnostic.check_ui_tars_api()
        if api_running:
            print(f"✅ UI-TARS API is running on {diagnostic.api_host}:{diagnostic.api_port}")
        else:
            print(f"❌ UI-TARS API not running on {diagnostic.api_host}:{diagnostic.api_port}")
            
        # Start UI-TARS if requested
        if args.start and not api_running:
            print("Starting UI-TARS with enhanced configuration...")
            
            # Start UI-TARS with sandbox
            success = await diagnostic.start_ui_tars_with_sandbox()
            if success:
                print("✅ UI-TARS started successfully with enhanced configuration")
                
                # Start browser in sandbox if needed
                if args.sandbox:
                    browser_success = await diagnostic.start_browser_in_sandbox()
                    if browser_success:
                        print(f"✅ {diagnostic.browser_type} browser started successfully in sandbox mode")
                    else:
                        print(f"❌ Failed to start {diagnostic.browser_type} browser in sandbox mode")
            else:
                print("❌ Failed to start UI-TARS")
                
        # Print summary
        print()
        print("Diagnostic Summary:")
        print(f"- UI-TARS Executable: {'Found' if diagnostic.executable_path else 'Not Found'}")
        print(f"- Browsers Detected: {len(browsers)}")
        print(f"- Selected Browser: {diagnostic.browser_type} ({'Available' if diagnostic.browser_type in browsers else 'Not Available'})")
        print(f"- UI-TARS API: {'Running' if api_running else 'Not Running'}")
        print(f"- Sandbox Mode: {'Enabled' if args.sandbox else 'Disabled'}")
        print(f"- Virtual PC Mode: {'Enabled' if args.virtual_pc else 'Disabled'}")
        print(f"- DPO: {'Enabled' if args.dpo else 'Disabled'}")
        
        # Print recommendations
        print()
        print("Recommendations:")
        if not diagnostic.executable_path:
            print("1. Make sure UI-TARS 1.5 is installed")
            print("2. Provide the path to UI-TARS.exe with --path")
        elif not api_running:
            print("1. Start UI-TARS with enhanced configuration using --start --sandbox --virtual-pc --dpo")
            print("2. Check if UI-TARS is configured to expose the API")
            print("3. Check UI-TARS logs for API-related errors")
        elif diagnostic.browser_type not in browsers:
            print(f"1. Select an available browser with --browser [{'|'.join(browsers.keys())}]")
            
        return 0 if diagnostic.executable_path and api_running else 1
        
    finally:
        # Clean up resources
        await diagnostic.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
