"""
Midscene Connector for UI-TARS.

This module provides a connector to interface with Midscene.js,
allowing UI-TARS to control browsers and Android devices.
"""
import os
import sys
import json
import asyncio
import logging
import subprocess
from typing import Dict, List, Optional, Any, Union
import platform
import requests
import time
import shutil
import tempfile
import zipfile
from pathlib import Path

from core.logger import setup_logger

# Set up logger
logger = setup_logger("midscene_connector")

class MidsceneConnector:
    """
    Connector for Midscene.js.
    
    This class provides methods to interface with Midscene.js,
    allowing UI-TARS to control browsers and Android devices.
    """
    
    def __init__(self, 
                 api_url: Optional[str] = None,
                 api_key: Optional[str] = None,
                 model_name: Optional[str] = "UI-TARS-1.5-7B",
                 installation_path: Optional[str] = None,
                 browser_type: str = "chrome",
                 android_enabled: bool = False):
        """
        Initialize the Midscene connector.
        
        Args:
            api_url (Optional[str]): URL of the Midscene API
            api_key (Optional[str]): API key for Midscene
            model_name (Optional[str]): Name of the model to use
            installation_path (Optional[str]): Path to Midscene installation
            browser_type (str): Type of browser to use
            android_enabled (bool): Whether to enable Android automation
        """
        self.api_url = api_url
        self.api_key = api_key
        self.model_name = model_name
        self.installation_path = installation_path
        self.browser_type = browser_type
        self.android_enabled = android_enabled
        self.session = None
        self.process = None
        self.is_running = False
        self.os_type = platform.system()  # 'Windows', 'Darwin' (macOS), or 'Linux'
        self.midscene_version = "0.16.0"  # Latest version as of now
        
        # Find installation path if not provided
        if not self.installation_path:
            self._find_installation_path()
    
    def _find_installation_path(self):
        """Find the Midscene installation path based on the operating system."""
        # Check common Node.js module locations
        if self.os_type == "Windows":
            possible_paths = [
                os.path.join(os.environ.get("APPDATA", ""), "npm", "node_modules", "@midscene"),
                os.path.join(os.environ.get("USERPROFILE", ""), "node_modules", "@midscene"),
                os.path.join(os.environ.get("PROGRAMFILES", ""), "nodejs", "node_modules", "@midscene"),
            ]
        elif self.os_type == "Darwin":  # macOS
            possible_paths = [
                os.path.expanduser("~/node_modules/@midscene"),
                "/usr/local/lib/node_modules/@midscene",
                "/opt/homebrew/lib/node_modules/@midscene",
            ]
        else:  # Linux
            possible_paths = [
                os.path.expanduser("~/node_modules/@midscene"),
                "/usr/local/lib/node_modules/@midscene",
                "/usr/lib/node_modules/@midscene",
            ]
        
        # Check if any of the paths exist
        for path in possible_paths:
            if os.path.exists(path):
                self.installation_path = path
                logger.info(f"Found Midscene installation at: {path}")
                return
        
        logger.warning("Could not find Midscene installation path")
    
    async def initialize(self):
        """Initialize the Midscene connector."""
        logger.info("Initializing Midscene connector")
        
        # Create a session for API requests
        self.session = requests.Session()
        if self.api_key:
            self.session.headers.update({"Authorization": f"Bearer {self.api_key}"})
        
        # Check if Midscene is installed
        if not self.installation_path or not os.path.exists(self.installation_path):
            logger.warning("Midscene is not installed or installation path is incorrect")
            
            # Install Midscene
            success = await self._install_midscene()
            if not success:
                logger.error("Failed to install Midscene")
                return False
        
        logger.info("Midscene connector initialized")
        return True
    
    async def _install_midscene(self):
        """Install Midscene.js."""
        logger.info("Installing Midscene.js")
        
        try:
            # Check if npm is installed
            npm_check = subprocess.run(
                ["npm", "--version"], 
                stdout=subprocess.PIPE, 
                stderr=subprocess.PIPE,
                text=True
            )
            
            if npm_check.returncode != 0:
                logger.error("npm is not installed. Please install Node.js and npm first.")
                return False
            
            # Install Midscene packages
            packages = [
                "@midscene/web",
                "@midscene/android",
                "@midscene/core",
                "@midscene/cli"
            ]
            
            for package in packages:
                logger.info(f"Installing {package}...")
                result = subprocess.run(
                    ["npm", "install", "-g", f"{package}@{self.midscene_version}"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                
                if result.returncode != 0:
                    logger.error(f"Failed to install {package}: {result.stderr}")
                    return False
                
                logger.info(f"Successfully installed {package}")
            
            # Find the installation path again
            self._find_installation_path()
            
            if not self.installation_path:
                logger.warning("Could not find Midscene installation path after installation")
                
                # Set a default path
                if self.os_type == "Windows":
                    self.installation_path = os.path.join(os.environ.get("APPDATA", ""), "npm", "node_modules", "@midscene")
                elif self.os_type == "Darwin":  # macOS
                    self.installation_path = "/usr/local/lib/node_modules/@midscene"
                else:  # Linux
                    self.installation_path = "/usr/local/lib/node_modules/@midscene"
            
            logger.info(f"Midscene installed at: {self.installation_path}")
            return True
        
        except Exception as e:
            logger.exception(f"Error installing Midscene: {e}")
            return False
    
    async def start_browser_automation(self, url: Optional[str] = None):
        """
        Start browser automation with Midscene.
        
        Args:
            url (Optional[str]): URL to open in the browser
            
        Returns:
            Dict: Result of the operation
        """
        logger.info(f"Starting browser automation with Midscene, URL: {url}")
        
        try:
            # Prepare the command
            command = ["npx", "@midscene/cli", "start", "--browser", self.browser_type]
            
            # Add URL if provided
            if url:
                command.extend(["--url", url])
            
            # Add model if provided
            if self.model_name:
                command.extend(["--model", self.model_name])
            
            # Start the process
            self.process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Wait for the process to start
            await asyncio.sleep(5)
            
            self.is_running = True
            logger.info("Browser automation started")
            
            return {
                "success": True,
                "message": "Browser automation started",
                "process_id": self.process.pid
            }
        
        except Exception as e:
            logger.exception(f"Error starting browser automation: {e}")
            
            return {
                "success": False,
                "error": str(e)
            }
    
    async def start_android_automation(self, device_id: Optional[str] = None):
        """
        Start Android automation with Midscene.
        
        Args:
            device_id (Optional[str]): ID of the Android device
            
        Returns:
            Dict: Result of the operation
        """
        if not self.android_enabled:
            logger.warning("Android automation is not enabled")
            return {
                "success": False,
                "error": "Android automation is not enabled"
            }
        
        logger.info(f"Starting Android automation with Midscene, device ID: {device_id}")
        
        try:
            # Prepare the command
            command = ["npx", "@midscene/cli", "start", "--android"]
            
            # Add device ID if provided
            if device_id:
                command.extend(["--device", device_id])
            
            # Add model if provided
            if self.model_name:
                command.extend(["--model", self.model_name])
            
            # Start the process
            self.process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Wait for the process to start
            await asyncio.sleep(5)
            
            self.is_running = True
            logger.info("Android automation started")
            
            return {
                "success": True,
                "message": "Android automation started",
                "process_id": self.process.pid
            }
        
        except Exception as e:
            logger.exception(f"Error starting Android automation: {e}")
            
            return {
                "success": False,
                "error": str(e)
            }
    
    async def execute_command(self, command: str) -> Dict:
        """
        Execute a command in Midscene.
        
        Args:
            command (str): Command to execute
            
        Returns:
            Dict: Response from Midscene
        """
        if not self.is_running:
            logger.warning("Midscene is not running")
            return {"error": "Midscene is not running"}
        
        if not self.api_url:
            logger.warning("API URL is not set")
            return {"error": "API URL is not set"}
        
        logger.info(f"Executing command: {command}")
        
        try:
            # Prepare the request
            data = {
                "command": command,
                "model": self.model_name
            }
            
            # Send the request
            response = self.session.post(f"{self.api_url}/execute", json=data)
            response.raise_for_status()
            
            # Parse the response
            result = response.json()
            
            logger.info(f"Command executed successfully")
            return result
        
        except Exception as e:
            logger.exception(f"Error executing command: {e}")
            return {"error": str(e)}
    
    async def stop(self):
        """Stop Midscene automation."""
        if not self.is_running:
            logger.info("Midscene is not running")
            return True
        
        logger.info("Stopping Midscene automation")
        
        try:
            if self.process:
                self.process.terminate()
                await asyncio.sleep(2)
                
                # Force kill if still running
                if self.process.poll() is None:
                    self.process.kill()
                
                self.process = None
            
            self.is_running = False
            logger.info("Midscene automation stopped")
            return True
        
        except Exception as e:
            logger.exception(f"Error stopping Midscene: {e}")
            return False
    
    async def close(self):
        """Close the Midscene connector."""
        logger.info("Closing Midscene connector")
        
        # Stop Midscene if running
        if self.is_running:
            await self.stop()
        
        # Close the session
        if self.session:
            self.session.close()
            self.session = None
        
        logger.info("Midscene connector closed")
        
        return True
