# UI-TARS 1.5 Setup Guide

This guide will help you set up UI-TARS 1.5 for API access, which is required for integration with the AI Agent System.

## Prerequisites

- UI-TARS 1.5 installed on your system
- Chrome, Firefox, or Edge browser installed

## Step 1: Locate UI-TARS Installation

The diagnostic tool found UI-TARS at:
```
C:\Users\<USER>\AppData\Local\UI-TARS\UI-TARS.exe
```

## Step 2: Start UI-TARS Manually

1. Navigate to the UI-TARS installation directory
2. Double-click on `UI-TARS.exe` to start UI-TARS
3. Wait for UI-TARS to fully initialize

## Step 3: Configure UI-TARS for API Access

1. In the UI-TARS interface, look for a "Settings" or "Configuration" option
2. Find the API or Developer settings section
3. Enable the API server
4. Set the API port to 8080 (default)
5. Save the settings

If you can't find these settings, check the UI-TARS documentation or contact UI-TARS support.

## Step 4: Verify API Access

After configuring UI-TARS, run the diagnostic tool again:

```
python ui_tars_diagnostic.py
```

If the API is running, you should see:
```
✅ UI-TARS API is running on localhost:8080
```

## Step 5: Test UI-TARS Integration

Once the API is running, you can test the integration with the AI Agent System:

```
python ui_tars_gmail_integration.py
```

## Troubleshooting

### UI-TARS Won't Start

If UI-TARS won't start, try:
1. Running as administrator
2. Checking for error logs in the UI-TARS installation directory
3. Reinstalling UI-TARS

### API Not Running

If the API won't start, try:
1. Checking if another application is using port 8080
2. Configuring UI-TARS to use a different port
3. Checking the UI-TARS logs for API-related errors

### Connection Refused

If you get a "Connection Refused" error, make sure:
1. UI-TARS is running
2. The API is enabled in UI-TARS settings
3. The firewall is not blocking the connection

## Alternative Approach

If you can't get the UI-TARS API working, you can still use the browser automation approach for sending emails:

```
python simple_email_sender.py
```

This approach doesn't require UI-TARS and works reliably for sending <NAME_EMAIL>.
