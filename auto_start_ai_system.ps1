# Auto Start AI System
# This script automatically starts the AI agent system on Windows startup
# It launches VS Code, Terminal, and initializes the Jarvis AI interface

# Check if running as administrator
$currentPrincipal = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
$isAdmin = $currentPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

if (-not $isAdmin) {
    Write-Host "This script should be run as Administrator for full functionality." -ForegroundColor Yellow
    Write-Host "Some features may not work correctly." -ForegroundColor Yellow
    Start-Sleep -Seconds 2
}

# Set the current directory to the script directory
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location -Path $scriptPath

Write-Host "Starting AI Agent System..." -ForegroundColor Cyan

# Function to check if a service exists
function Test-ServiceExists {
    param (
        [string]$ServiceName
    )
    
    return [bool](Get-Service -Name $ServiceName -ErrorAction SilentlyContinue)
}

# Function to check if a service is running
function Test-ServiceRunning {
    param (
        [string]$ServiceName
    )
    
    $service = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
    return $service -and $service.Status -eq 'Running'
}

# Check and start AlphaEvolve service if it exists
if (Test-ServiceExists -ServiceName "AlphaEvolveService") {
    if (-not (Test-ServiceRunning -ServiceName "AlphaEvolveService")) {
        Write-Host "Starting AlphaEvolve service..." -ForegroundColor Cyan
        Start-Service -Name "AlphaEvolveService"
        Start-Sleep -Seconds 2
    } else {
        Write-Host "AlphaEvolve service is already running." -ForegroundColor Green
    }
} else {
    Write-Host "AlphaEvolve service not found. Installing service..." -ForegroundColor Yellow
    
    # Run the installation script if it exists
    if (Test-Path "$scriptPath\install_alphaevolve_service.ps1") {
        & "$scriptPath\install_alphaevolve_service.ps1"
    } else {
        Write-Host "Service installation script not found. Please run setup_alphaevolve_autostart.ps1 first." -ForegroundColor Red
    }
}

# Start MPC Servers
Write-Host "Starting MPC Servers..." -ForegroundColor Cyan
Start-Process -FilePath "cmd.exe" -ArgumentList "/c start_mpc_monitoring.bat" -WindowStyle Minimized -WorkingDirectory $scriptPath

# Start UI-TARS
Write-Host "Starting UI-TARS 1.5..." -ForegroundColor Cyan
Start-Process -FilePath "python" -ArgumentList "ui_tars\main.py --start --browser" -WindowStyle Minimized -WorkingDirectory $scriptPath

# Start VS Code with the project workspace
Write-Host "Opening VS Code with project workspace..." -ForegroundColor Cyan
$vscodePath = "C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe"
$altVscodePath = "C:\Program Files\Microsoft VS Code\Code.exe"

if (Test-Path $vscodePath) {
    Start-Process -FilePath $vscodePath -ArgumentList "`"$scriptPath`"" -WindowStyle Normal
} elseif (Test-Path $altVscodePath) {
    Start-Process -FilePath $altVscodePath -ArgumentList "`"$scriptPath`"" -WindowStyle Normal
} else {
    Write-Host "VS Code not found in standard locations. Trying to launch using 'code' command..." -ForegroundColor Yellow
    Start-Process -FilePath "code" -ArgumentList "`"$scriptPath`"" -WindowStyle Normal
}

# Wait for components to initialize
Write-Host "Waiting for components to initialize..." -ForegroundColor Cyan
Start-Sleep -Seconds 5

# Start Jarvis with AlphaEvolve integration in a new terminal window
Write-Host "Starting Jarvis with AlphaEvolve integration..." -ForegroundColor Cyan

# Create a new PowerShell window for Jarvis
Start-Process -FilePath "powershell.exe" -ArgumentList "-ExecutionPolicy Bypass -NoExit -File `"$scriptPath\start_jarvis_with_terminal.ps1`"" -WindowStyle Normal

Write-Host "AI Agent System startup complete!" -ForegroundColor Green
Write-Host "Jarvis is ready to accept commands." -ForegroundColor Green
Write-Host "Press Ctrl+Alt+X to exit Jarvis and return to regular terminal." -ForegroundColor Yellow

# Exit this script
exit
