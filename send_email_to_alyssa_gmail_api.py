"""
Send Email to Alyssa using Gmail API

This script sends an email to Alyssa using the Gmail API.
"""
import os
import sys
import json
import base64
import logging
import argparse
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request
from googleapiclient.discovery import build

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("send_email_to_alyssa_gmail_api.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("send_email_to_alyssa_gmail_api")

# Constants
SCOPES = ['https://www.googleapis.com/auth/gmail.send']
EMAIL_SUBJECT = "IUL Policy and Health Insurance Options"
EMAIL_RECIPIENT = "<EMAIL>"  # Replace with <PERSON><PERSON>'s actual email if different

# Email template for Alyssa
EMAIL_TEMPLATE = """
Dear <PERSON><PERSON>,

Thank you for your interest in our insurance products. Based on your $100/month budget, I'd like to discuss some options for an Indexed Universal Life (IUL) policy structured for maximum cash value growth, along with basic health, dental, and vision plans.

Here's what I'm thinking:

1. IUL Policy: We can structure this for optimal cash value growth while maintaining the life insurance benefit. This would be approximately $60-70 of your monthly budget.

2. Health Insurance: For the remaining $30-40, we can look at basic health plans that cover essential services.

3. Dental & Vision: We have some affordable options that can be added if your budget allows, or we can discuss slightly exceeding your budget if these are priorities for you.

Would you be available for a quick call to discuss these options in more detail? I can answer any questions you might have and provide specific policy recommendations based on your needs.

Please let me know what days and times work best for you.

Best regards,
Paul Edwards
Flo Faction Insurance
Phone: (*************
Email: <EMAIL>
"""

def get_credentials(credentials_file):
    """Get Gmail API credentials."""
    logger.info("Getting Gmail API credentials...")

    creds = None
    token_file = 'token.json'

    # Check if token.json exists
    if os.path.exists(token_file):
        try:
            creds = Credentials.from_authorized_user_info(json.loads(open(token_file).read()), SCOPES)
        except Exception as e:
            logger.error(f"Error loading credentials from token file: {e}")

    # If credentials don't exist or are invalid, get new ones
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            try:
                creds.refresh(Request())
            except Exception as e:
                logger.error(f"Error refreshing credentials: {e}")
                creds = None

        if not creds:
            try:
                flow = InstalledAppFlow.from_client_secrets_file(credentials_file, SCOPES)
                creds = flow.run_local_server(port=0)

                # Save the credentials for the next run
                with open(token_file, 'w') as token:
                    token.write(creds.to_json())
            except Exception as e:
                logger.error(f"Error getting new credentials: {e}")
                return None

    return creds

def create_message(sender, to, subject, body):
    """Create a message for an email."""
    message = MIMEMultipart()
    message['to'] = to
    message['from'] = sender
    message['subject'] = subject

    # Add body to email
    message.attach(MIMEText(body, 'plain'))

    # Encode the message
    raw_message = base64.urlsafe_b64encode(message.as_bytes()).decode()
    return {'raw': raw_message}

def send_email(service, user_id, message):
    """Send an email message."""
    try:
        message = service.users().messages().send(userId=user_id, body=message).execute()
        logger.info(f"Message sent successfully. Message ID: {message['id']}")
        return message
    except Exception as e:
        logger.error(f"Error sending message: {e}")
        return None

def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Send Email to Alyssa using Gmail API")
    parser.add_argument("--credentials", type=str, required=True, help="Path to credentials.json file")
    parser.add_argument("--sender", type=str, default="<EMAIL>", help="Sender email address")
    parser.add_argument("--recipient", type=str, default=EMAIL_RECIPIENT, help="Recipient email address")
    parser.add_argument("--subject", type=str, default=EMAIL_SUBJECT, help="Email subject")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")

    args = parser.parse_args()

    # Set log level
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)

    print("Send Email to Alyssa using Gmail API")
    print("==================================")
    print()

    # Check if credentials file exists
    if not os.path.exists(args.credentials):
        print(f"❌ Credentials file not found: {args.credentials}")
        return 1

    # Get credentials
    creds = get_credentials(args.credentials)
    if not creds:
        print("❌ Failed to get credentials")
        return 1

    print("✅ Successfully got credentials")

    # Build Gmail API service
    try:
        service = build('gmail', 'v1', credentials=creds)
        print("✅ Successfully built Gmail API service")
    except Exception as e:
        logger.error(f"Error building Gmail API service: {e}")
        print("❌ Failed to build Gmail API service")
        return 1

    # Create message
    message = create_message(args.sender, args.recipient, args.subject, EMAIL_TEMPLATE)

    # Send message
    print(f"Sending email from {args.sender} to {args.recipient}...")
    result = send_email(service, 'me', message)

    if result:
        print("✅ Email sent successfully to Alyssa!")
    else:
        print("❌ Failed to send email")
        return 1

    print()
    print("Email Details:")
    print(f"- From: {args.sender}")
    print(f"- To: {args.recipient}")
    print(f"- Subject: {args.subject}")
    print(f"- Content: {EMAIL_TEMPLATE[:50]}...")

    return 0

if __name__ == "__main__":
    sys.exit(main())
