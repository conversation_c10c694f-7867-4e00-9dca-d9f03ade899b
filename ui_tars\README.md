# Enhanced UI-TARS 1.5 Integration

This package provides an enhanced integration with UI-TARS 1.5, a powerful GUI agent that allows autonomous control of browsers and desktop applications. This enhanced version includes improved reliability, error handling, and Jarvis integration.

## Overview

UI-TARS 1.5 is a multimodal AI system developed by ByteDance that can understand and interact with graphical user interfaces. This enhanced integration allows the AI Agent System to leverage UI-TARS capabilities for autonomous browser control and desktop automation with improved reliability and error recovery.

## New Features in Enhanced Integration

- **Improved Browser Detection**: Automatically detects and configures browsers
- **Enhanced Error Handling**: Robust error recovery and automatic restart capabilities
- **Jarvis Integration**: Full integration with the Jarvis interface for voice control
- **Specialized Agents**: Gmail and Google Voice automation agents
- **Diagnostic Tools**: Comprehensive diagnostic and troubleshooting tools
- **Persona Support**: Support for both <PERSON> and <PERSON> personas

## Features

- **Autonomous Browser Control**: Control web browsers autonomously to perform complex tasks
- **Desktop Automation**: Automate desktop applications and workflows
- **Android Automation**: Control Android devices (requires additional setup)
- **Local LLM Integration**: Use locally installed models instead of remote APIs
- **Voice Command Support**: Control UI-TARS using voice commands
- **NVIDIA Acceleration**: Leverage NVIDIA GPUs for improved performance
- **GUI Dashboard**: Monitor and control UI-TARS operations through a user-friendly interface

## Installation

### Prerequisites

- Python 3.8 or higher
- Node.js and npm (for Midscene)
- NVIDIA GPU (optional, for acceleration)

### Automatic Installation

Run the installation script to automatically download and install UI-TARS and its dependencies:

```bash
python install.py
```

### Manual Installation

1. Install the required packages:

```bash
pip install -r requirements.txt
```

2. Install Midscene:

```bash
npm install -g @midscene/web @midscene/android @midscene/core @midscene/cli
```

3. Download UI-TARS Desktop from the [GitHub repository](https://github.com/bytedance/UI-TARS-desktop/releases)

4. Download the UI-TARS model from [Hugging Face](https://huggingface.co/ByteDance-Seed/UI-TARS-1.5-7B)

5. Update the configuration file (`config.json`) with the installation paths

## Usage

### Starting the GUI Dashboard

```bash
python main.py --gui
```

### Starting UI-TARS from the Command Line

```bash
python main.py --start
```

### Starting Browser Automation

```bash
python main.py --browser --url https://www.example.com
```

### Starting Android Automation

```bash
python main.py --android --device <device_id>
```

### Starting Local LLM Server

```bash
python main.py --local-llm
```

### Using UI-TARS in the AI Agent System

```python
from ui_tars.integration import UITarsIntegration

# Initialize UI-TARS integration
ui_tars_integration = UITarsIntegration(config)
await ui_tars_integration.initialize()
await ui_tars_integration.start()

# Execute a command
result = await ui_tars_integration.execute_command("Browse to https://www.example.com")

# Browse a website
result = await ui_tars_integration.browse_website("https://www.example.com", "find the contact page")

# Search the web
result = await ui_tars_integration.search_web("UI-TARS documentation", "google")

# Start autonomous mode
result = await ui_tars_integration.start_autonomous_mode("Browse to https://www.example.com and fill out the contact form")

# Stop autonomous mode
result = await ui_tars_integration.stop_autonomous_mode()

# Stop UI-TARS integration
await ui_tars_integration.stop()
```

## Configuration

The configuration file (`config.json`) contains settings for UI-TARS, Midscene, Local LLM, and the UI-TARS agent:

```json
{
    "ui_tars": {
        "api_url": "http://localhost:8080",
        "api_key": null,
        "model_name": "UI-TARS-1.5-7B",
        "installation_path": null,
        "auto_start": false
    },
    "midscene": {
        "api_url": "http://localhost:8081",
        "api_key": null,
        "model_name": "UI-TARS-1.5-7B",
        "installation_path": null,
        "browser_type": "chrome",
        "android_enabled": false,
        "auto_start_browser": false,
        "auto_start_android": false,
        "default_url": "https://www.google.com",
        "default_device_id": null
    },
    "local_llm": {
        "model_path": null,
        "model_type": "ui-tars",
        "host": "localhost",
        "port": 8000,
        "api_base": null,
        "quantization": "4bit",
        "auto_start": false
    },
    "agent": {
        "auto_start": false,
        "voice_commands_enabled": false,
        "nvidia_acceleration": true,
        "autonomous_mode": false
    }
}
```

## Voice Commands

UI-TARS supports voice commands for hands-free operation. To use voice commands:

1. Enable voice commands in the GUI dashboard or configuration file
2. Use the activation keyword "tars" followed by a command:
   - "tars start" - Start UI-TARS
   - "tars stop" - Stop UI-TARS
   - "tars browser" - Start browser automation
   - "tars screenshot" - Take a screenshot
   - "tars execute [command]" - Execute a command

## NVIDIA Acceleration

UI-TARS can leverage NVIDIA GPUs for improved performance. To enable NVIDIA acceleration:

1. Install NVIDIA drivers and CUDA
2. Enable NVIDIA acceleration in the GUI dashboard or configuration file
3. UI-TARS will automatically use the GPU for inference

## Enhanced Components

### Enhanced UI-TARS Connector

The enhanced connector (`connector/enhanced_ui_tars_connector.py`) provides improved browser detection, connection reliability, and error recovery:

```python
from ui_tars.connector.enhanced_ui_tars_connector import EnhancedUITarsConnector

# Initialize the connector
connector = EnhancedUITarsConnector(
    browser_type="chrome",
    auto_restart=True
)
await connector.initialize()

# Execute a command
result = await connector.execute_command("Browse to https://www.example.com")
```

### Specialized Agents

#### Gmail Automation Agent

The Gmail automation agent (`agent/gmail_automation_agent.py`) provides specialized methods for Gmail automation:

```python
from ui_tars.agent.gmail_automation_agent import GmailAutomationAgent

# Initialize the agent
gmail_agent = GmailAutomationAgent(
    default_email="<EMAIL>",
    default_password="your_password"
)
await gmail_agent.initialize()

# Send an email
result = await gmail_agent.send_email(
    to="<EMAIL>",
    subject="Hello from UI-TARS",
    body="This email was sent using UI-TARS automation."
)
```

#### Google Voice Automation Agent

The Google Voice automation agent (`agent/google_voice_automation_agent.py`) provides specialized methods for Google Voice automation:

```python
from ui_tars.agent.google_voice_automation_agent import GoogleVoiceAutomationAgent

# Initialize the agent
voice_agent = GoogleVoiceAutomationAgent(
    default_email="<EMAIL>",
    default_password="your_password"
)
await voice_agent.initialize()

# Send a text message
result = await voice_agent.send_text_message(
    phone_number="1234567890",
    message="Hello from UI-TARS!"
)
```

### Jarvis Integration

The Jarvis integration (`integration/jarvis_ui_tars_integration.py`) connects UI-TARS with the Jarvis interface:

```python
from ui_tars.integration.jarvis_ui_tars_integration import JarvisUITarsIntegration

# Initialize the integration
jarvis_integration = JarvisUITarsIntegration(
    jarvis_interface=jarvis_interface,
    config=config
)
await jarvis_integration.initialize()
```

### Diagnostic Tool

The diagnostic tool (`ui_tars_diagnostic.py`) checks the UI-TARS installation and configuration:

```bash
python ui_tars_diagnostic.py --fix --start
```

## Troubleshooting

### Using the Diagnostic Tool

The enhanced integration includes a comprehensive diagnostic tool that can detect and fix common issues:

```bash
python ui_tars_diagnostic.py --fix --start
```

Or use the batch file:

```bash
diagnose_ui_tars.bat
```

### Browser Connection Issues

If UI-TARS cannot connect to the browser:

1. Make sure the browser is installed and can be launched
2. Check if the remote debugging port (9222) is available
3. Run the diagnostic tool with the `--fix` option to automatically fix common issues

### UI-TARS API Not Responding

If the UI-TARS API is not responding:

1. Make sure UI-TARS is properly installed and configured
2. Check if UI-TARS is running
3. Run the diagnostic tool with the `--start` option to automatically start UI-TARS

### Gmail or Google Voice Authentication Issues

If Gmail or Google Voice authentication fails:

1. Make sure the email and password are correct in the configuration file
2. Check if two-factor authentication is enabled (use an app password instead)
3. Try logging in manually first to ensure the account is not locked

## License

This enhanced integration is provided under the Apache 2.0 License. UI-TARS and Midscene are subject to their respective licenses.
