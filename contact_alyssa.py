"""
Contact Alyssa using UI-TARS

This script uses UI-TARS to contact Alyssa through Gmail, demonstrating
that UI-TARS is working properly with browser integration.
"""
import os
import sys
import json
import time
import logging
import argparse
import requests
import subprocess
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("contact_alyssa.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("contact_alyssa")

# Constants
UI_TARS_API_URL = "http://localhost:8080/v1"
GMAIL_URL = "https://mail.google.com"
EMAIL_SUBJECT = "IUL Policy and Health Insurance Options"
EMAIL_RECIPIENT = "<EMAIL>"  # Replace with <PERSON>yssa's actual email if different

# Email template for Alyssa
EMAIL_TEMPLATE = """
Dear <PERSON><PERSON>,

Thank you for your interest in our insurance products. Based on your $100/month budget, I'd like to discuss some options for an Indexed Universal Life (IUL) policy structured for maximum cash value growth, along with basic health, dental, and vision plans.

Here's what I'm thinking:

1. IUL Policy: We can structure this for optimal cash value growth while maintaining the life insurance benefit. This would be approximately $60-70 of your monthly budget.

2. Health Insurance: For the remaining $30-40, we can look at basic health plans that cover essential services.

3. Dental & Vision: We have some affordable options that can be added if your budget allows, or we can discuss slightly exceeding your budget if these are priorities for you.

Would you be available for a quick call to discuss these options in more detail? I can answer any questions you might have and provide specific policy recommendations based on your needs.

Please let me know what days and times work best for you.

Best regards,
Paul Edwards
Flo Faction Insurance
Phone: (*************
Email: <EMAIL>
"""

def check_ui_tars_api():
    """Check if UI-TARS API is running."""
    logger.info("Checking if UI-TARS API is running...")
    
    try:
        response = requests.get(f"{UI_TARS_API_URL}/models")
        if response.status_code == 200:
            logger.info("UI-TARS API is running")
            return True
        else:
            logger.warning(f"UI-TARS API returned status code: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"Error checking UI-TARS API: {e}")
        return False

def send_ui_tars_command(command, params=None):
    """Send a command to UI-TARS API."""
    logger.info(f"Sending command to UI-TARS: {command}")
    
    try:
        url = f"{UI_TARS_API_URL}/completions"
        data = {
            "model": "UI-TARS-1.5-7B",
            "prompt": command,
            "max_tokens": 1000,
            "temperature": 0.7,
            "params": params or {}
        }
        
        response = requests.post(url, json=data)
        if response.status_code == 200:
            result = response.json()
            logger.info(f"UI-TARS command successful: {result.get('choices', [{}])[0].get('text', '')[:100]}...")
            return result
        else:
            logger.warning(f"UI-TARS command failed with status code: {response.status_code}")
            return None
    except Exception as e:
        logger.error(f"Error sending command to UI-TARS: {e}")
        return None

def navigate_to_gmail():
    """Navigate to Gmail using UI-TARS."""
    logger.info("Navigating to Gmail...")
    
    command = f"Navigate to {GMAIL_URL}"
    params = {
        "browser": {
            "url": GMAIL_URL
        }
    }
    
    result = send_ui_tars_command(command, params)
    if not result:
        logger.error("Failed to navigate to Gmail")
        return False
        
    logger.info("Successfully navigated to Gmail")
    return True

def login_to_gmail(email, password):
    """Login to Gmail using UI-TARS."""
    logger.info(f"Logging in to Gmail as {email}...")
    
    # Enter email
    command_email = f"Enter email address: {email}"
    params_email = {
        "browser": {
            "actions": [
                {
                    "type": "input",
                    "selector": "input[type='email']",
                    "value": email
                },
                {
                    "type": "click",
                    "selector": "button:contains('Next')"
                }
            ]
        }
    }
    
    result_email = send_ui_tars_command(command_email, params_email)
    if not result_email:
        logger.error("Failed to enter email")
        return False
        
    # Wait for password field
    time.sleep(3)
    
    # Enter password
    command_password = f"Enter password: {password}"
    params_password = {
        "browser": {
            "actions": [
                {
                    "type": "input",
                    "selector": "input[type='password']",
                    "value": password
                },
                {
                    "type": "click",
                    "selector": "button:contains('Next')"
                }
            ]
        }
    }
    
    result_password = send_ui_tars_command(command_password, params_password)
    if not result_password:
        logger.error("Failed to enter password")
        return False
        
    # Wait for Gmail to load
    time.sleep(5)
    
    logger.info("Successfully logged in to Gmail")
    return True

def compose_email():
    """Compose an email to Alyssa using UI-TARS."""
    logger.info("Composing email to Alyssa...")
    
    # Click Compose button
    command_compose = "Click Compose button"
    params_compose = {
        "browser": {
            "actions": [
                {
                    "type": "click",
                    "selector": "div[role='button']:contains('Compose')"
                }
            ]
        }
    }
    
    result_compose = send_ui_tars_command(command_compose, params_compose)
    if not result_compose:
        logger.error("Failed to click Compose button")
        return False
        
    # Wait for compose window
    time.sleep(2)
    
    # Enter recipient
    command_recipient = f"Enter recipient: {EMAIL_RECIPIENT}"
    params_recipient = {
        "browser": {
            "actions": [
                {
                    "type": "input",
                    "selector": "input[role='combobox'][aria-label*='To']",
                    "value": EMAIL_RECIPIENT
                }
            ]
        }
    }
    
    result_recipient = send_ui_tars_command(command_recipient, params_recipient)
    if not result_recipient:
        logger.error("Failed to enter recipient")
        return False
        
    # Enter subject
    command_subject = f"Enter subject: {EMAIL_SUBJECT}"
    params_subject = {
        "browser": {
            "actions": [
                {
                    "type": "input",
                    "selector": "input[name='subjectbox']",
                    "value": EMAIL_SUBJECT
                }
            ]
        }
    }
    
    result_subject = send_ui_tars_command(command_subject, params_subject)
    if not result_subject:
        logger.error("Failed to enter subject")
        return False
        
    # Enter email body
    command_body = "Enter email body"
    params_body = {
        "browser": {
            "actions": [
                {
                    "type": "input",
                    "selector": "div[role='textbox'][aria-label*='Message Body']",
                    "value": EMAIL_TEMPLATE
                }
            ]
        }
    }
    
    result_body = send_ui_tars_command(command_body, params_body)
    if not result_body:
        logger.error("Failed to enter email body")
        return False
        
    logger.info("Successfully composed email to Alyssa")
    return True

def send_email():
    """Send the email using UI-TARS."""
    logger.info("Sending email...")
    
    command_send = "Click Send button"
    params_send = {
        "browser": {
            "actions": [
                {
                    "type": "click",
                    "selector": "div[role='button']:contains('Send')"
                }
            ]
        }
    }
    
    result_send = send_ui_tars_command(command_send, params_send)
    if not result_send:
        logger.error("Failed to click Send button")
        return False
        
    # Wait for confirmation
    time.sleep(2)
    
    logger.info("Successfully sent email to Alyssa")
    return True

def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Contact Alyssa using UI-TARS")
    parser.add_argument("--email", type=str, default="<EMAIL>", help="Gmail email address")
    parser.add_argument("--password", type=str, default="GodisSoGood!777", help="Gmail password")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    parser.add_argument("--dry-run", action="store_true", help="Don't actually send the email")
    
    args = parser.parse_args()
    
    # Set log level
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        
    print("Contact Alyssa using UI-TARS")
    print("===========================")
    print()
    
    # Check if UI-TARS API is running
    if not check_ui_tars_api():
        print("❌ UI-TARS API is not running")
        print("Please make sure UI-TARS is running with browser integration")
        return 1
        
    print("✅ UI-TARS API is running")
    
    # Navigate to Gmail
    if not navigate_to_gmail():
        print("❌ Failed to navigate to Gmail")
        return 1
        
    print("✅ Successfully navigated to Gmail")
    
    # Login to Gmail
    if not login_to_gmail(args.email, args.password):
        print("❌ Failed to login to Gmail")
        return 1
        
    print("✅ Successfully logged in to Gmail")
    
    # Compose email
    if not compose_email():
        print("❌ Failed to compose email")
        return 1
        
    print("✅ Successfully composed email to Alyssa")
    
    # Send email if not dry run
    if not args.dry_run:
        if not send_email():
            print("❌ Failed to send email")
            return 1
            
        print("✅ Successfully sent email to Alyssa")
    else:
        print("ℹ️ Dry run - email not sent")
        
    print()
    print("Email Details:")
    print(f"- From: {args.email}")
    print(f"- To: {EMAIL_RECIPIENT}")
    print(f"- Subject: {EMAIL_SUBJECT}")
    print(f"- Content: {EMAIL_TEMPLATE[:50]}...")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
