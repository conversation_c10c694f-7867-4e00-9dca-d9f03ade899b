"""
Client Communication Agent

This agent handles client communications for the AI agent system.
It uses the Browser Automation Agent to interact with email, messaging, and other communication platforms.
"""
import os
import sys
import json
import time
import logging
import datetime
from pathlib import Path

# Import Browser Automation Agent
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from agents.browser_automation_agent import BrowserAutomationAgent

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/client_communication_agent.log", mode='a'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("client_communication_agent")

class ClientCommunicationAgent:
    """
    Client Communication Agent for the AI agent system.

    This agent handles client communications, including:
    - Sending emails to clients
    - Responding to client inquiries
    - Scheduling appointments
    - Following up with clients
    - Sending quotes and proposals
    """

    def __init__(self,
                 agent_name="Insurance Communication Agent",
                 agent_email="<EMAIL>",
                 templates_path="config/communication_templates.json",
                 client_db_path="data/clients.json",
                 debug=False):
        """
        Initialize the Client Communication Agent.

        Args:
            agent_name: Name of the agent
            agent_email: Email address of the agent
            templates_path: Path to communication templates
            client_db_path: Path to client database
            debug: Enable debug logging
        """
        self.agent_name = agent_name
        self.agent_email = agent_email
        self.templates_path = templates_path
        self.client_db_path = client_db_path
        self.debug = debug
        self.templates = {}
        self.clients = {}
        self.browser_agent = None

        # Create necessary directories
        os.makedirs("logs", exist_ok=True)
        os.makedirs("data", exist_ok=True)
        os.makedirs("config", exist_ok=True)

        # Set log level
        if debug:
            logging.getLogger().setLevel(logging.DEBUG)

        # Load templates and clients
        self.load_templates()
        self.load_clients()

        logger.info(f"Client Communication Agent initialized: {agent_name}")

    def load_templates(self):
        """Load communication templates."""
        logger.info(f"Loading communication templates from: {self.templates_path}")

        try:
            if os.path.exists(self.templates_path):
                with open(self.templates_path, "r") as f:
                    self.templates = json.load(f)
                logger.info(f"Loaded {len(self.templates)} templates")
            else:
                logger.warning(f"Templates file not found: {self.templates_path}")
                self.create_default_templates()
        except Exception as e:
            logger.error(f"Error loading templates: {e}")
            self.create_default_templates()

    def create_default_templates(self):
        """Create default communication templates."""
        logger.info("Creating default communication templates")

        self.templates = {
            "initial_contact": {
                "subject": "Insurance Options for Your Needs",
                "body": """
Dear {client_first_name},

Thank you for your interest in our insurance products. Based on your needs, I'd like to discuss some options that might be a good fit for you.

{custom_message}

Would you be available for a quick call to discuss these options in more detail? I can answer any questions you might have and provide specific policy recommendations based on your needs.

Please let me know what days and times work best for you.

Best regards,
{agent_name}
Flo Faction Insurance
Phone: (*************
Email: {agent_email}
"""
            },
            "follow_up": {
                "subject": "Following Up on Insurance Options",
                "body": """
Dear {client_first_name},

I wanted to follow up on our previous communication about insurance options. I hope you've had a chance to review the information I sent.

{custom_message}

I'm available to answer any questions you might have or provide additional information. Would you like to schedule a call to discuss further?

Best regards,
{agent_name}
Flo Faction Insurance
Phone: (*************
Email: {agent_email}
"""
            },
            "quote": {
                "subject": "Insurance Quote for Your Review",
                "body": """
Dear {client_first_name},

I'm pleased to provide you with a quote for the insurance options we discussed. Based on the information you provided, here are the details:

{custom_message}

Please review this quote and let me know if you have any questions or would like to make any adjustments. If you're ready to proceed, we can schedule a time to complete the application process.

Best regards,
{agent_name}
Flo Faction Insurance
Phone: (*************
Email: {agent_email}
"""
            },
            "appointment_confirmation": {
                "subject": "Confirmation of Our Appointment",
                "body": """
Dear {client_first_name},

This is to confirm our appointment on {appointment_date} at {appointment_time}.

{custom_message}

If you need to reschedule or have any questions before our meeting, please don't hesitate to contact me.

Looking forward to speaking with you!

Best regards,
{agent_name}
Flo Faction Insurance
Phone: (*************
Email: {agent_email}
"""
            },
            "iul_policy": {
                "subject": "Indexed Universal Life (IUL) Policy Options",
                "body": """
Dear {client_first_name},

Thank you for your interest in Indexed Universal Life (IUL) insurance. Based on your {budget} monthly budget, I'd like to discuss some options for an IUL policy structured for maximum cash value growth.

Here's what I'm thinking:

1. IUL Policy: We can structure this for optimal cash value growth while maintaining the life insurance benefit. This would be approximately {iul_budget} of your monthly budget.

2. Additional Coverage: For the remaining {remaining_budget}, we can look at supplemental coverage options that align with your needs.

Would you be available for a quick call to discuss these options in more detail? I can answer any questions you might have and provide specific policy recommendations based on your needs.

Please let me know what days and times work best for you.

Best regards,
{agent_name}
Flo Faction Insurance
Phone: (*************
Email: {agent_email}
"""
            }
        }

        # Save templates
        try:
            with open(self.templates_path, "w") as f:
                json.dump(self.templates, f, indent=2)
            logger.info(f"Saved default templates to: {self.templates_path}")
        except Exception as e:
            logger.error(f"Error saving default templates: {e}")

    def load_clients(self):
        """Load client database."""
        logger.info(f"Loading client database from: {self.client_db_path}")

        try:
            if os.path.exists(self.client_db_path):
                with open(self.client_db_path, "r") as f:
                    self.clients = json.load(f)
                logger.info(f"Loaded {len(self.clients)} clients")
            else:
                logger.warning(f"Client database not found: {self.client_db_path}")
                self.create_default_clients()
        except Exception as e:
            logger.error(f"Error loading client database: {e}")
            self.create_default_clients()

    def create_default_clients(self):
        """Create default client database."""
        logger.info("Creating default client database")

        self.clients = {
            "Alyssa": {
                "name": "Alyssa",
                "full_name": "Alyssa Chirinos",
                "email": "<EMAIL>",
                "phone": "",
                "address": "Bradenton, Florida",
                "dob": "8/16/97",
                "lead_type": "insurance",
                "insurance": {
                    "type": "IUL with Dental, Vision, and Basic Health",
                    "budget": "$100/month",
                    "primary_interest": "IUL",
                    "additional_interests": ["dental", "vision", "basic health"],
                    "coverage_needs": "Checkups, physicals, and bloodwork",
                    "carriers_to_check": "all"
                },
                "notes": "Primary interest is IUL. Also interested in dental, vision, and basic private health coverage for checkups, physicals, and bloodwork. TOTAL BUDGET IS $100/MONTH. Need to check all carriers for best solution within budget.",
                "status": "lead",
                "last_contact": "",
                "next_contact": "",
                "communication_history": []
            }
        }

        # Save clients
        try:
            with open(self.client_db_path, "w") as f:
                json.dump(self.clients, f, indent=2)
            logger.info(f"Saved default clients to: {self.client_db_path}")
        except Exception as e:
            logger.error(f"Error saving default clients: {e}")

    def save_clients(self):
        """Save client database."""
        logger.info(f"Saving client database to: {self.client_db_path}")

        try:
            with open(self.client_db_path, "w") as f:
                json.dump(self.clients, f, indent=2)
            logger.info(f"Saved {len(self.clients)} clients")
            return True
        except Exception as e:
            logger.error(f"Error saving client database: {e}")
            return False

    def get_client(self, name):
        """Get client by name."""
        # First try exact match
        if name in self.clients:
            return self.clients[name]

        # Try case-insensitive match
        for client_name, client in self.clients.items():
            if client_name.lower() == name.lower():
                return client

        # Try partial match
        for client_name, client in self.clients.items():
            if name.lower() in client_name.lower() or client_name.lower() in name.lower():
                return client

        # Try matching full name
        for client_name, client in self.clients.items():
            if name.lower() in client.get("full_name", "").lower():
                return client

        return None

    def get_client_by_email(self, email):
        """Get client by email."""
        for name, client in self.clients.items():
            if client.get("email") == email:
                return client
        return None

    def add_client(self, client_data):
        """Add a new client."""
        name = client_data.get("name")
        if not name:
            logger.error("Client name is required")
            return False

        logger.info(f"Adding new client: {name}")

        if name in self.clients:
            logger.warning(f"Client already exists: {name}")
            return False

        self.clients[name] = client_data
        self.save_clients()

        logger.info(f"Added new client: {name}")
        return True

    def update_client(self, name, client_data):
        """Update an existing client."""
        logger.info(f"Updating client: {name}")

        client = self.get_client(name)
        if not client:
            logger.warning(f"Client not found: {name}")
            return False

        # Get the actual name key in the dictionary
        actual_name = None
        for client_name in self.clients.keys():
            if client_name.lower() == name.lower() or name.lower() in client_name.lower():
                actual_name = client_name
                break

        if not actual_name:
            logger.warning(f"Client key not found: {name}")
            return False

        self.clients[actual_name].update(client_data)
        self.save_clients()

        logger.info(f"Updated client: {actual_name}")
        return True

    def record_communication(self, name, communication_type, subject, body, status="sent"):
        """Record a communication with a client."""
        logger.info(f"Recording {communication_type} communication with client: {name}")

        client = self.get_client(name)
        if not client:
            logger.warning(f"Client not found: {name}")
            return False

        # Get the actual name key in the dictionary
        actual_name = None
        for client_name in self.clients.keys():
            if client_name.lower() == name.lower() or name.lower() in client_name.lower():
                actual_name = client_name
                break

        if not actual_name:
            logger.warning(f"Client key not found: {name}")
            return False

        # Create communication record
        communication = {
            "type": communication_type,
            "subject": subject,
            "body": body,
            "status": status,
            "timestamp": datetime.datetime.now().isoformat()
        }

        # Add to client's communication history
        self.clients[actual_name]["communication_history"].append(communication)

        # Update last contact
        self.clients[actual_name]["last_contact"] = datetime.datetime.now().isoformat()

        # Save clients
        self.save_clients()

        logger.info(f"Recorded {communication_type} communication with client: {actual_name}")
        return True

    def format_template(self, template_name, variables):
        """Format a template with variables."""
        logger.info(f"Formatting template: {template_name}")

        if template_name not in self.templates:
            logger.warning(f"Template not found: {template_name}")
            return None, None

        template = self.templates[template_name]

        try:
            # Add agent variables if not provided
            if "agent_name" not in variables:
                variables["agent_name"] = self.agent_name
            if "agent_email" not in variables:
                variables["agent_email"] = self.agent_email

            # Format subject and body
            subject = template["subject"].format(**variables)
            body = template["body"].format(**variables)

            logger.info(f"Formatted template: {template_name}")
            return subject, body
        except KeyError as e:
            logger.error(f"Missing variable in template: {e}")
            return None, None
        except Exception as e:
            logger.error(f"Error formatting template: {e}")
            return None, None

    def initialize_browser_agent(self):
        """Initialize the Browser Automation Agent."""
        logger.info("Initializing Browser Automation Agent")

        if self.browser_agent is None:
            self.browser_agent = BrowserAutomationAgent(debug=self.debug)

        if not self.browser_agent.start():
            logger.error("Failed to start Browser Automation Agent")
            return False

        logger.info("Browser Automation Agent initialized")
        return True

    def send_email_to_client(self, name, template_name, custom_variables=None):
        """Send an email to a client using a template."""
        logger.info(f"Sending email to client: {name} using template: {template_name}")

        # Get client
        client = self.get_client(name)
        if not client:
            logger.error(f"Client not found: {name}")
            return False

        # Prepare variables
        variables = {
            "client_name": client.get("full_name", client.get("name")),
            "client_first_name": client.get("name"),
            "client_email": client.get("email")
        }

        # Add custom variables
        if custom_variables:
            variables.update(custom_variables)

        # Format template
        subject, body = self.format_template(template_name, variables)
        if not subject or not body:
            logger.error(f"Failed to format template: {template_name}")
            return False

        # Initialize browser agent
        if not self.initialize_browser_agent():
            logger.error("Failed to initialize browser agent")
            return False

        # Send email
        email_data = {
            "to": client.get("email"),
            "subject": subject,
            "body": body
        }

        if not self.browser_agent.send_email(email_data):
            logger.error(f"Failed to send email to client: {name}")
            return False

        # Record communication
        self.record_communication(name, "email", subject, body)

        logger.info(f"Email sent to client: {name}")
        return True

    def send_iul_email_to_client(self, name):
        """Send an IUL policy email to a client."""
        logger.info(f"Sending IUL policy email to client: {name}")

        # Get client
        client = self.get_client(name)
        if not client:
            logger.error(f"Client not found: {name}")
            return False

        # Check if client is an insurance lead
        if client.get("lead_type") != "insurance":
            logger.warning(f"Client {name} is not an insurance lead")
            return False

        # Get insurance details
        insurance = client.get("insurance", {})

        # Parse budget
        budget_str = insurance.get("budget", "$100/month")
        budget = budget_str.replace("$", "").replace("/month", "")
        try:
            budget_amount = int(budget)
        except ValueError:
            budget_amount = 100  # Default to $100 if parsing fails

        # Calculate IUL budget (60-70% of total budget)
        iul_budget = f"${int(budget_amount * 0.65)}-{int(budget_amount * 0.7)}"
        remaining_budget = f"${int(budget_amount * 0.3)}-{int(budget_amount * 0.35)}"

        # Get primary interest and additional interests
        primary_interest = insurance.get("primary_interest", "IUL")
        additional_interests = insurance.get("additional_interests", [])
        additional_interests_str = ", ".join(additional_interests)

        # Create custom message
        custom_message = f"Based on your interest in {primary_interest}"
        if additional_interests:
            custom_message += f" and {additional_interests_str}"
        custom_message += f", I believe we can find a solution within your budget of {budget_str}."

        # Prepare variables
        variables = {
            "budget": budget_str,
            "iul_budget": iul_budget,
            "remaining_budget": remaining_budget,
            "custom_message": custom_message
        }

        # Send email
        return self.send_email_to_client(name, "iul_policy", variables)

    def contact_alyssa(self):
        """Contact Alyssa with IUL policy information."""
        logger.info("Contacting Alyssa with IUL policy information")

        return self.send_iul_email_to_client("Alyssa")

    def stop(self):
        """Stop the Client Communication Agent."""
        logger.info("Stopping Client Communication Agent")

        if self.browser_agent:
            self.browser_agent.stop()

        logger.info("Client Communication Agent stopped")

# Example usage
if __name__ == "__main__":
    # Create agent
    agent = ClientCommunicationAgent(debug=True)

    # Contact Alyssa
    if agent.contact_alyssa():
        print("Successfully contacted Alyssa")
    else:
        print("Failed to contact Alyssa")

    # Stop agent
    agent.stop()
