"""
Diagnose UI-TARS Issues

This script provides detailed diagnostics for UI-TARS issues, including:
1. Checking UI-TARS installation
2. Verifying configuration
3. Testing browser integration
4. Checking API connectivity
5. Analyzing log files
"""
import os
import sys
import json
import time
import socket
import logging
import argparse
import platform
import subprocess
import requests
import re
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("diagnose_ui_tars.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("diagnose_ui_tars")

def find_ui_tars_executable():
    """Find the UI-TARS executable."""
    logger.info("Searching for UI-TARS executable...")
    
    os_type = platform.system()
    
    if os_type == "Windows":
        # Common installation locations on Windows
        possible_paths = [
            os.path.join(os.environ.get("PROGRAMFILES", "C:\\Program Files"), "UI-TARS", "UI-TARS.exe"),
            os.path.join(os.environ.get("PROGRAMFILES(X86)", "C:\\Program Files (x86)"), "UI-TARS", "UI-TARS.exe"),
            os.path.join(os.environ.get("LOCALAPPDATA", "C:\\Users\\<USER>\\AppData\\Local".format(os.getlogin())), "UI-TARS", "UI-TARS.exe"),
            "UI-TARS.exe"
        ]
    elif os_type == "Darwin":  # macOS
        # Common installation locations on macOS
        possible_paths = [
            "/Applications/UI-TARS.app/Contents/MacOS/UI-TARS",
            os.path.expanduser("~/Applications/UI-TARS.app/Contents/MacOS/UI-TARS"),
        ]
    else:  # Linux
        # Common installation locations on Linux
        possible_paths = [
            "/usr/local/bin/ui-tars",
            "/usr/bin/ui-tars",
            os.path.expanduser("~/.local/bin/ui-tars"),
        ]
        
    # Check if any of the paths exist
    for path in possible_paths:
        if os.path.exists(path):
            logger.info(f"Found UI-TARS executable at: {path}")
            return path
            
    # Try to find in PATH
    try:
        if os_type == "Windows":
            result = subprocess.run(["where", "UI-TARS.exe"], capture_output=True, text=True)
        else:
            result = subprocess.run(["which", "ui-tars"], capture_output=True, text=True)
            
        if result.returncode == 0:
            path = result.stdout.strip()
            logger.info(f"Found UI-TARS executable in PATH: {path}")
            return path
    except Exception as e:
        logger.debug(f"Error searching for UI-TARS in PATH: {e}")
        
    logger.warning("Could not find UI-TARS executable")
    return None

def find_ui_tars_config():
    """Find the UI-TARS configuration file."""
    logger.info("Searching for UI-TARS configuration file...")
    
    # Check common locations
    possible_paths = [
        "config/ui_tars_config.json",
        "ui_tars/config.json",
        "ui_tars_config.json",
        "ui_tars_config_v2.json"
    ]
    
    # Check if any of the paths exist
    for path in possible_paths:
        if os.path.exists(path):
            logger.info(f"Found UI-TARS configuration at: {path}")
            return path
            
    logger.warning("Could not find UI-TARS configuration file")
    return None

def find_ui_tars_logs():
    """Find UI-TARS log files."""
    logger.info("Searching for UI-TARS log files...")
    
    log_files = []
    
    # Check common locations
    possible_paths = [
        "ui_tars_debug.log",
        "ui_tars.log",
        "logs/ui_tars.log",
        os.path.join(os.environ.get("LOCALAPPDATA", ""), "UI-TARS", "logs", "ui_tars.log"),
        os.path.join(os.environ.get("APPDATA", ""), "UI-TARS", "logs", "ui_tars.log")
    ]
    
    # Check if any of the paths exist
    for path in possible_paths:
        if os.path.exists(path):
            logger.info(f"Found UI-TARS log file at: {path}")
            log_files.append(path)
            
    if not log_files:
        logger.warning("Could not find UI-TARS log files")
        
    return log_files

def detect_browsers():
    """Detect installed browsers."""
    logger.info("Detecting installed browsers...")
    
    browsers = {}
    os_type = platform.system()
    
    if os_type == "Windows":
        # Check for common browsers on Windows
        paths = [
            (r"C:\Program Files\Google\Chrome\Application\chrome.exe", "chrome"),
            (r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe", "chrome"),
            (r"C:\Program Files\Mozilla Firefox\firefox.exe", "firefox"),
            (r"C:\Program Files (x86)\Mozilla Firefox\firefox.exe", "firefox"),
            (r"C:\Program Files\Microsoft\Edge\Application\msedge.exe", "edge"),
            (r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe", "edge"),
            (r"C:\Program Files\BraveSoftware\Brave-Browser\Application\brave.exe", "brave"),
            (r"C:\Program Files (x86)\BraveSoftware\Brave-Browser\Application\brave.exe", "brave"),
        ]
    elif os_type == "Darwin":  # macOS
        # Check for common browsers on macOS
        paths = [
            ("/Applications/Google Chrome.app/Contents/MacOS/Google Chrome", "chrome"),
            ("/Applications/Firefox.app/Contents/MacOS/firefox", "firefox"),
            ("/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge", "edge"),
            ("/Applications/Brave Browser.app/Contents/MacOS/Brave Browser", "brave"),
        ]
    else:  # Linux
        # Check for common browsers on Linux
        paths = [
            ("/usr/bin/google-chrome", "chrome"),
            ("/usr/bin/firefox", "firefox"),
            ("/usr/bin/microsoft-edge", "edge"),
            ("/usr/bin/brave-browser", "brave"),
        ]
        
    # Check if any of the paths exist
    for path, browser_type in paths:
        if os.path.exists(path):
            browsers[browser_type] = path
            logger.info(f"Found {browser_type} browser at: {path}")
            
    return browsers

def check_port_open(host, port, timeout=5):
    """Check if a port is open."""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(timeout)
            result = s.connect_ex((host, port))
            return result == 0
    except Exception as e:
        logger.debug(f"Error checking port {port}: {e}")
        return False

def check_ui_tars_api(host="localhost", port=8080):
    """Check if the UI-TARS API is running."""
    logger.info(f"Checking if UI-TARS API is running on {host}:{port}...")
    
    if not check_port_open(host, port):
        logger.warning(f"UI-TARS API not running on {host}:{port}")
        return False
    
    try:
        # Try different API endpoints
        endpoints = [
            f"http://{host}:{port}/health",
            f"http://{host}:{port}/v1/models",
            f"http://{host}:{port}/api/status"
        ]
        
        for endpoint in endpoints:
            try:
                logger.info(f"Trying endpoint: {endpoint}")
                response = requests.get(endpoint, timeout=5)
                
                if response.status_code < 400:
                    logger.info(f"UI-TARS API is running (endpoint: {endpoint})")
                    return True
            except requests.exceptions.RequestException:
                continue
        
        logger.warning("UI-TARS API is not responding to any known endpoints")
        return False
        
    except Exception as e:
        logger.error(f"Error checking UI-TARS API: {e}")
        return False

def check_ui_tars_version(executable_path):
    """Check UI-TARS version."""
    logger.info("Checking UI-TARS version...")
    
    try:
        result = subprocess.run(
            [executable_path, "--version"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        if result.returncode == 0:
            version = result.stdout.strip()
            logger.info(f"UI-TARS version: {version}")
            return version
        else:
            logger.warning("Failed to get UI-TARS version")
            return None
    except Exception as e:
        logger.error(f"Error checking UI-TARS version: {e}")
        return None

def check_ui_tars_config(config_path):
    """Check UI-TARS configuration."""
    logger.info(f"Checking UI-TARS configuration at {config_path}...")
    
    try:
        with open(config_path, "r") as f:
            config = json.load(f)
            
        # Check if configuration is valid
        if not isinstance(config, dict):
            logger.warning("Invalid configuration format")
            return False
            
        # Check if ui_tars section exists
        if "ui_tars" not in config:
            logger.warning("Missing 'ui_tars' section in configuration")
            return False
            
        # Check if browser section exists
        if "browser" not in config["ui_tars"]:
            logger.warning("Missing 'browser' section in configuration")
            return False
            
        # Check if browser type is specified
        if "type" not in config["ui_tars"]["browser"]:
            logger.warning("Missing 'type' in browser configuration")
            return False
            
        # Check if browser executable path is specified
        if "executable_path" not in config["ui_tars"]["browser"]:
            logger.warning("Missing 'executable_path' in browser configuration")
            return False
            
        # Check if browser executable exists
        browser_path = config["ui_tars"]["browser"]["executable_path"]
        if not os.path.exists(browser_path):
            logger.warning(f"Browser executable not found at: {browser_path}")
            return False
            
        logger.info("UI-TARS configuration is valid")
        return True
    except Exception as e:
        logger.error(f"Error checking UI-TARS configuration: {e}")
        return False

def analyze_ui_tars_logs(log_files):
    """Analyze UI-TARS log files for errors."""
    logger.info("Analyzing UI-TARS log files...")
    
    errors = []
    
    for log_file in log_files:
        try:
            with open(log_file, "r") as f:
                log_content = f.read()
                
            # Look for error patterns
            error_patterns = [
                r"ERROR.*",
                r"Exception.*",
                r"Failed to.*",
                r"Could not.*",
                r"Error:.*"
            ]
            
            for pattern in error_patterns:
                matches = re.findall(pattern, log_content, re.MULTILINE)
                for match in matches:
                    errors.append(f"{log_file}: {match.strip()}")
                    
            logger.info(f"Analyzed log file: {log_file}")
        except Exception as e:
            logger.error(f"Error analyzing log file {log_file}: {e}")
            
    if errors:
        logger.warning(f"Found {len(errors)} errors in log files")
    else:
        logger.info("No errors found in log files")
        
    return errors

def check_browser_debugging_port(port=9222):
    """Check if browser debugging port is open."""
    logger.info(f"Checking if browser debugging port {port} is open...")
    
    if check_port_open("localhost", port):
        logger.info(f"Browser debugging port {port} is open")
        return True
    else:
        logger.warning(f"Browser debugging port {port} is not open")
        return False

def check_ui_tars_processes():
    """Check for running UI-TARS processes."""
    logger.info("Checking for running UI-TARS processes...")
    
    processes = []
    
    try:
        if platform.system() == "Windows":
            # Use tasklist on Windows
            result = subprocess.run(
                ["tasklist", "/FI", "IMAGENAME eq UI-TARS.exe", "/FO", "CSV"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            if "UI-TARS.exe" in result.stdout:
                logger.info("UI-TARS process is running")
                processes.append("UI-TARS.exe")
        else:
            # Use ps on Unix-like systems
            result = subprocess.run(
                ["ps", "aux"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            if "UI-TARS" in result.stdout:
                logger.info("UI-TARS process is running")
                processes.append("UI-TARS")
                
        # Check for Chrome/Edge processes with remote debugging
        if platform.system() == "Windows":
            result = subprocess.run(
                ["tasklist", "/FI", "IMAGENAME eq chrome.exe", "/FO", "CSV"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            if "chrome.exe" in result.stdout:
                logger.info("Chrome process is running")
                processes.append("chrome.exe")
                
            result = subprocess.run(
                ["tasklist", "/FI", "IMAGENAME eq msedge.exe", "/FO", "CSV"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            if "msedge.exe" in result.stdout:
                logger.info("Edge process is running")
                processes.append("msedge.exe")
    except Exception as e:
        logger.error(f"Error checking processes: {e}")
        
    return processes

def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Diagnose UI-TARS Issues")
    parser.add_argument("--path", type=str, help="Path to UI-TARS executable")
    parser.add_argument("--config", type=str, help="Path to UI-TARS configuration file")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    
    args = parser.parse_args()
    
    # Set log level
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        
    print("UI-TARS Diagnostics")
    print("==================")
    print()
    
    # Check UI-TARS installation
    ui_tars_path = args.path or find_ui_tars_executable()
    if ui_tars_path:
        print(f"✅ UI-TARS executable found at: {ui_tars_path}")
        
        # Check UI-TARS version
        version = check_ui_tars_version(ui_tars_path)
        if version:
            print(f"✅ UI-TARS version: {version}")
        else:
            print("❌ Could not determine UI-TARS version")
    else:
        print("❌ UI-TARS executable not found")
        
    # Check UI-TARS configuration
    config_path = args.config or find_ui_tars_config()
    if config_path:
        print(f"✅ UI-TARS configuration found at: {config_path}")
        
        # Check configuration validity
        if check_ui_tars_config(config_path):
            print("✅ UI-TARS configuration is valid")
        else:
            print("❌ UI-TARS configuration is invalid")
    else:
        print("❌ UI-TARS configuration not found")
        
    # Check browsers
    browsers = detect_browsers()
    if browsers:
        print(f"✅ Found {len(browsers)} browsers:")
        for browser_type, path in browsers.items():
            print(f"  - {browser_type}: {path}")
    else:
        print("❌ No browsers detected")
        
    # Check UI-TARS processes
    processes = check_ui_tars_processes()
    if "UI-TARS.exe" in processes:
        print("✅ UI-TARS process is running")
    else:
        print("❌ UI-TARS process is not running")
        
    # Check browser processes
    if "chrome.exe" in processes or "msedge.exe" in processes:
        print("✅ Browser process is running")
    else:
        print("❌ Browser process is not running")
        
    # Check browser debugging port
    if check_browser_debugging_port():
        print("✅ Browser debugging port is open")
    else:
        print("❌ Browser debugging port is not open")
        
    # Check UI-TARS API
    if check_ui_tars_api():
        print("✅ UI-TARS API is running")
    else:
        print("❌ UI-TARS API is not running")
        
    # Check UI-TARS logs
    log_files = find_ui_tars_logs()
    if log_files:
        print(f"✅ Found {len(log_files)} UI-TARS log files:")
        for log_file in log_files:
            print(f"  - {log_file}")
            
        # Analyze logs
        errors = analyze_ui_tars_logs(log_files)
        if errors:
            print(f"❌ Found {len(errors)} errors in log files:")
            for i, error in enumerate(errors[:10]):  # Show only first 10 errors
                print(f"  {i+1}. {error}")
                
            if len(errors) > 10:
                print(f"  ... and {len(errors) - 10} more errors")
        else:
            print("✅ No errors found in log files")
    else:
        print("❌ No UI-TARS log files found")
        
    # Print summary
    print()
    print("Diagnostic Summary:")
    print(f"- UI-TARS Executable: {'Found' if ui_tars_path else 'Not Found'}")
    print(f"- UI-TARS Configuration: {'Valid' if config_path and check_ui_tars_config(config_path) else 'Invalid or Not Found'}")
    print(f"- Browsers: {len(browsers)} detected")
    print(f"- UI-TARS Process: {'Running' if 'UI-TARS.exe' in processes else 'Not Running'}")
    print(f"- Browser Process: {'Running' if 'chrome.exe' in processes or 'msedge.exe' in processes else 'Not Running'}")
    print(f"- Browser Debugging Port: {'Open' if check_browser_debugging_port() else 'Closed'}")
    print(f"- UI-TARS API: {'Running' if check_ui_tars_api() else 'Not Running'}")
    print(f"- Log Files: {len(log_files)} found")
    
    # Print recommendations
    print()
    print("Recommendations:")
    if not ui_tars_path:
        print("- Install UI-TARS or provide the correct path")
    if not config_path:
        print("- Create or provide a valid UI-TARS configuration file")
    if not browsers:
        print("- Install a supported browser (Chrome, Edge, Firefox, or Brave)")
    if not "UI-TARS.exe" in processes:
        print("- Start UI-TARS using the start_ui_tars.py script")
    if not check_ui_tars_api():
        print("- Check if UI-TARS API is configured correctly")
        print("- Make sure no other application is using port 8080")
    if not check_browser_debugging_port():
        print("- Start a browser with remote debugging enabled")
        print("- Make sure no other application is using port 9222")
    if log_files and analyze_ui_tars_logs(log_files):
        print("- Check the log files for detailed error information")
        
    return 0

if __name__ == "__main__":
    sys.exit(main())
