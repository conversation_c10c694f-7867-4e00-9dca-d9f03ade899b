"""
<PERSON><PERSON><PERSON> to set up test credentials for cybersecurity testing.

This script allows you to securely store credentials for testing purposes.
"""
import sys
import os
import argparse
import getpass
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent))

from config.secure_credentials import C<PERSON><PERSON><PERSON><PERSON><PERSON>

def setup_credentials(master_password=None):
    """
    Set up credentials for testing.
    
    Args:
        master_password (str): Master password for encrypting credentials
    """
    if not master_password:
        master_password = getpass.getpass("Enter master password for credential encryption: ")
    
    # Initialize credential manager
    credential_manager = CredentialManager(master_password)
    
    while True:
        print("\nCredential Management")
        print("====================")
        print("1. Add credential")
        print("2. View credentials")
        print("3. Remove credential")
        print("4. Exit")
        
        choice = input("\nEnter your choice (1-4): ")
        
        if choice == "1":
            # Add credential
            service = input("Enter service name (e.g., example.com): ")
            username = input("Enter username: ")
            password = getpass.getpass("Enter password: ")
            
            # Additional information
            additional_info = {}
            add_more = input("Add additional information? (y/n): ").lower() == "y"
            while add_more:
                key = input("Enter information key: ")
                value = input("Enter information value: ")
                additional_info[key] = value
                add_more = input("Add more information? (y/n): ").lower() == "y"
            
            credential_manager.add_credential(service, username, password, additional_info)
            print(f"Credential for {service} added successfully.")
        
        elif choice == "2":
            # View credentials
            services = credential_manager.list_services()
            
            if not services:
                print("No credentials stored.")
                continue
            
            print("\nStored credentials:")
            for i, service in enumerate(services, 1):
                print(f"{i}. {service}")
            
            service_choice = input("\nEnter service number to view details (or 0 to go back): ")
            if service_choice == "0":
                continue
            
            try:
                service_index = int(service_choice) - 1
                if 0 <= service_index < len(services):
                    service = services[service_index]
                    credential = credential_manager.get_credential(service)
                    
                    print(f"\nService: {service}")
                    print(f"Username: {credential['username']}")
                    print(f"Password: {'*' * len(credential['password'])}")
                    
                    if credential.get('additional_info'):
                        print("Additional information:")
                        for key, value in credential['additional_info'].items():
                            print(f"  {key}: {value}")
                else:
                    print("Invalid service number.")
            except ValueError:
                print("Invalid input. Please enter a number.")
        
        elif choice == "3":
            # Remove credential
            services = credential_manager.list_services()
            
            if not services:
                print("No credentials stored.")
                continue
            
            print("\nStored credentials:")
            for i, service in enumerate(services, 1):
                print(f"{i}. {service}")
            
            service_choice = input("\nEnter service number to remove (or 0 to go back): ")
            if service_choice == "0":
                continue
            
            try:
                service_index = int(service_choice) - 1
                if 0 <= service_index < len(services):
                    service = services[service_index]
                    confirm = input(f"Are you sure you want to remove credentials for {service}? (y/n): ").lower() == "y"
                    
                    if confirm:
                        credential_manager.remove_credential(service)
                        print(f"Credentials for {service} removed successfully.")
                else:
                    print("Invalid service number.")
            except ValueError:
                print("Invalid input. Please enter a number.")
        
        elif choice == "4":
            # Exit
            break
        
        else:
            print("Invalid choice. Please enter a number between 1 and 4.")

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Set up test credentials for cybersecurity testing")
    parser.add_argument("--master-password", help="Master password for credential encryption")
    args = parser.parse_args()
    
    setup_credentials(args.master_password)

if __name__ == "__main__":
    main()
