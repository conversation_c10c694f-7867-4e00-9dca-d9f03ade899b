"""
Workflow Engine for managing and executing agent workflows.
"""
import os
import json
import asyncio
import time
from typing import Dict, List, Any, Optional, Union, Callable
from datetime import datetime
import logging
import uuid
import re

from core.logger import setup_logger
from core.state_manager import StateManager
from core.agent_manager import Agent<PERSON>anager

# Set up logger
logger = setup_logger("workflow_engine")

class WorkflowEngine:
    """
    Engine for loading, managing, and executing agent workflows.
    """
    
    def __init__(self, 
                 agent_manager: AgentManager,
                 state_manager: Optional[StateManager] = None,
                 config_path: str = "config/workflows.json"):
        """
        Initialize the Workflow Engine.
        
        Args:
            agent_manager (AgentManager): Agent manager for accessing agents
            state_manager (Optional[StateManager]): State manager for persistence
            config_path (str): Path to the workflow configuration file
        """
        self.agent_manager = agent_manager
        self.state_manager = state_manager
        self.config_path = config_path
        self.workflows = {}
        self.active_workflows = {}
        self.config = {}
        self.running = False
        self.workflow_queue = asyncio.Queue()
        self.scheduled_workflows = {}
        self.event_handlers = {}
        
    async def initialize(self):
        """Initialize the workflow engine."""
        logger.info("Initializing workflow engine")
        
        # Load configuration
        await self._load_config()
        
        # Load workflows
        await self._load_workflows()
        
        # Register event handlers
        self._register_event_handlers()
        
        # Schedule workflows
        await self._schedule_workflows()
        
        # Start workflow processor
        self.running = True
        asyncio.create_task(self._process_workflow_queue())
        
        logger.info(f"Workflow engine initialized with {len(self.workflows)} workflows")
        
    async def _load_config(self):
        """Load workflow configuration."""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r') as f:
                    self.config = json.load(f)
                logger.info(f"Loaded workflow configuration from {self.config_path}")
            else:
                logger.warning(f"Workflow configuration file not found at {self.config_path}")
                self.config = {
                    "enabled": True,
                    "workflows": [],
                    "workflow_execution": {
                        "max_concurrent_workflows": 3,
                        "max_step_retries": 3,
                        "step_timeout_seconds": 300,
                        "workflow_timeout_minutes": 60
                    }
                }
        except Exception as e:
            logger.error(f"Error loading workflow configuration: {str(e)}")
            self.config = {"enabled": False, "workflows": []}
    
    async def _load_workflows(self):
        """Load all workflows from configuration."""
        if not self.config.get("enabled", False):
            logger.warning("Workflow engine is disabled in configuration")
            return
            
        for workflow_config in self.config.get("workflows", []):
            if not workflow_config.get("enabled", True):
                continue
                
            workflow_id = workflow_config.get("id")
            workflow_path = workflow_config.get("path")
            
            if not workflow_id or not workflow_path:
                logger.warning(f"Invalid workflow configuration: {workflow_config}")
                continue
                
            try:
                if os.path.exists(workflow_path):
                    with open(workflow_path, 'r') as f:
                        workflow = json.load(f)
                    
                    self.workflows[workflow_id] = {
                        "id": workflow_id,
                        "config": workflow_config,
                        "definition": workflow
                    }
                    logger.info(f"Loaded workflow '{workflow_id}' from {workflow_path}")
                else:
                    logger.warning(f"Workflow file not found: {workflow_path}")
            except Exception as e:
                logger.error(f"Error loading workflow '{workflow_id}': {str(e)}")
    
    def _register_event_handlers(self):
        """Register event handlers for workflow triggers."""
        for workflow_id, workflow in self.workflows.items():
            definition = workflow["definition"]
            
            for trigger in definition.get("triggers", []):
                if trigger.get("type") == "event":
                    event_name = trigger.get("event")
                    if event_name:
                        if event_name not in self.event_handlers:
                            self.event_handlers[event_name] = []
                        self.event_handlers[event_name].append({
                            "workflow_id": workflow_id,
                            "trigger": trigger
                        })
                        logger.info(f"Registered event handler for '{event_name}' in workflow '{workflow_id}'")
    
    async def _schedule_workflows(self):
        """Schedule workflows based on their triggers."""
        for workflow_id, workflow in self.workflows.items():
            definition = workflow["definition"]
            
            for trigger in definition.get("triggers", []):
                if trigger.get("type") == "schedule":
                    schedule = trigger.get("schedule")
                    if schedule:
                        # For simplicity, we'll just schedule workflows to run every minute
                        # In a real implementation, you'd use a proper cron parser
                        task = asyncio.create_task(self._schedule_workflow_task(workflow_id, trigger))
                        self.scheduled_workflows[workflow_id] = task
                        logger.info(f"Scheduled workflow '{workflow_id}' with schedule '{schedule}'")
    
    async def _schedule_workflow_task(self, workflow_id: str, trigger: Dict):
        """Schedule a workflow to run periodically."""
        while self.running:
            # In a real implementation, you'd calculate the next run time based on the cron expression
            # For simplicity, we'll just run every minute
            await asyncio.sleep(60)
            
            if self.running:
                await self.trigger_workflow(workflow_id, {
                    "type": "schedule",
                    "trigger": trigger,
                    "timestamp": datetime.now().isoformat()
                })
    
    async def _process_workflow_queue(self):
        """Process the workflow queue."""
        while self.running:
            try:
                workflow_item = await self.workflow_queue.get()
                
                # Start the workflow in a separate task
                asyncio.create_task(self._execute_workflow(
                    workflow_item["workflow_id"],
                    workflow_item["context"]
                ))
                
                self.workflow_queue.task_done()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error processing workflow queue: {str(e)}")
    
    async def trigger_workflow(self, workflow_id: str, context: Dict):
        """
        Trigger a workflow to run.
        
        Args:
            workflow_id (str): ID of the workflow to trigger
            context (Dict): Context data for the workflow
        """
        if not self.running:
            logger.warning(f"Cannot trigger workflow '{workflow_id}': Workflow engine is not running")
            return
            
        if workflow_id not in self.workflows:
            logger.warning(f"Cannot trigger workflow '{workflow_id}': Workflow not found")
            return
            
        # Add to workflow queue
        await self.workflow_queue.put({
            "workflow_id": workflow_id,
            "context": context
        })
        
        logger.info(f"Triggered workflow '{workflow_id}'")
    
    async def trigger_event(self, event_name: str, event_data: Dict):
        """
        Trigger an event that may start workflows.
        
        Args:
            event_name (str): Name of the event
            event_data (Dict): Event data
        """
        if not self.running:
            logger.warning(f"Cannot trigger event '{event_name}': Workflow engine is not running")
            return
            
        if event_name not in self.event_handlers:
            logger.debug(f"No handlers registered for event '{event_name}'")
            return
            
        for handler in self.event_handlers[event_name]:
            await self.trigger_workflow(handler["workflow_id"], {
                "type": "event",
                "event": event_name,
                "trigger": handler["trigger"],
                "data": event_data,
                "timestamp": datetime.now().isoformat()
            })
    
    async def _execute_workflow(self, workflow_id: str, context: Dict):
        """
        Execute a workflow.
        
        Args:
            workflow_id (str): ID of the workflow to execute
            context (Dict): Context data for the workflow
        """
        if workflow_id not in self.workflows:
            logger.warning(f"Cannot execute workflow '{workflow_id}': Workflow not found")
            return
            
        workflow = self.workflows[workflow_id]
        definition = workflow["definition"]
        
        # Generate a unique execution ID
        execution_id = str(uuid.uuid4())
        
        # Initialize workflow state
        workflow_state = {
            "id": execution_id,
            "workflow_id": workflow_id,
            "context": context,
            "steps": {},
            "current_step": None,
            "status": "running",
            "start_time": datetime.now().isoformat(),
            "end_time": None
        }
        
        # Add to active workflows
        self.active_workflows[execution_id] = workflow_state
        
        logger.info(f"Starting workflow '{workflow_id}' (execution: {execution_id})")
        
        try:
            # Find the first step
            steps = {step["id"]: step for step in definition.get("steps", [])}
            
            if not steps:
                logger.warning(f"Workflow '{workflow_id}' has no steps")
                workflow_state["status"] = "completed"
                workflow_state["end_time"] = datetime.now().isoformat()
                return
            
            # Start with the first step
            current_step_id = definition.get("steps", [])[0]["id"]
            
            # Execute steps until we reach an end step or encounter an error
            while current_step_id and current_step_id != "end" and current_step_id in steps:
                step = steps[current_step_id]
                
                if step.get("type") == "end":
                    break
                
                # Update workflow state
                workflow_state["current_step"] = current_step_id
                
                # Execute the step
                step_result = await self._execute_step(step, workflow_state)
                
                # Store step result
                workflow_state["steps"][current_step_id] = {
                    "step": step,
                    "result": step_result,
                    "status": "completed" if step_result else "failed",
                    "timestamp": datetime.now().isoformat()
                }
                
                # Determine next step
                if "next" in step:
                    next_step = step["next"]
                    
                    if isinstance(next_step, str):
                        # Simple next step
                        current_step_id = next_step
                    elif isinstance(next_step, dict) and "condition" in next_step:
                        # Conditional next step
                        condition = next_step["condition"]
                        
                        # Evaluate the condition
                        condition_result = self._evaluate_condition(condition, {
                            "result": step_result,
                            "workflow": workflow_state,
                            "steps": workflow_state["steps"]
                        })
                        
                        if "cases" in next_step and condition_result in next_step["cases"]:
                            # Case-based condition
                            current_step_id = next_step["cases"][condition_result]
                        elif "true" in next_step and "false" in next_step:
                            # Boolean condition
                            current_step_id = next_step["true"] if condition_result else next_step["false"]
                        elif "default" in next_step:
                            # Default case
                            current_step_id = next_step["default"]
                        else:
                            logger.warning(f"Invalid next step configuration in workflow '{workflow_id}', step '{current_step_id}'")
                            break
                    else:
                        logger.warning(f"Invalid next step configuration in workflow '{workflow_id}', step '{current_step_id}'")
                        break
                else:
                    # No next step defined
                    break
            
            # Workflow completed successfully
            workflow_state["status"] = "completed"
            logger.info(f"Workflow '{workflow_id}' completed successfully (execution: {execution_id})")
            
        except Exception as e:
            # Workflow failed
            workflow_state["status"] = "failed"
            workflow_state["error"] = str(e)
            logger.error(f"Workflow '{workflow_id}' failed (execution: {execution_id}): {str(e)}")
            
        finally:
            # Update end time
            workflow_state["end_time"] = datetime.now().isoformat()
            
            # Remove from active workflows
            if execution_id in self.active_workflows:
                del self.active_workflows[execution_id]
    
    async def _execute_step(self, step: Dict, workflow_state: Dict) -> Dict:
        """
        Execute a workflow step.
        
        Args:
            step (Dict): Step definition
            workflow_state (Dict): Current workflow state
            
        Returns:
            Dict: Step execution result
        """
        step_id = step["id"]
        agent_id = step.get("agent")
        action = step.get("action")
        parameters = step.get("parameters", {})
        
        logger.info(f"Executing step '{step_id}' (agent: {agent_id}, action: {action})")
        
        if not agent_id or not action:
            logger.warning(f"Invalid step configuration: Missing agent or action")
            return {"error": "Invalid step configuration"}
        
        # Get the agent
        agent = self.agent_manager.get_agent(agent_id)
        if not agent:
            logger.warning(f"Agent '{agent_id}' not found")
            return {"error": f"Agent '{agent_id}' not found"}
        
        # Resolve parameter values
        resolved_parameters = {}
        for param_name, param_value in parameters.items():
            if isinstance(param_value, str) and param_value.startswith("${") and param_value.endswith("}"):
                # This is a reference to a previous step or context value
                resolved_value = self._resolve_reference(param_value, workflow_state)
                resolved_parameters[param_name] = resolved_value
            else:
                resolved_parameters[param_name] = param_value
        
        try:
            # Execute the action
            result = await agent.execute_action(action, resolved_parameters)
            return result
        except Exception as e:
            logger.error(f"Error executing step '{step_id}': {str(e)}")
            return {"error": str(e)}
    
    def _resolve_reference(self, reference: str, workflow_state: Dict) -> Any:
        """
        Resolve a reference to a value in the workflow state.
        
        Args:
            reference (str): Reference string (e.g., "${previous.result.value}")
            workflow_state (Dict): Current workflow state
            
        Returns:
            Any: Resolved value
        """
        # Extract the reference path
        match = re.match(r"\$\{(.*)\}", reference)
        if not match:
            return reference
            
        path = match.group(1)
        parts = path.split(".")
        
        # Special references
        if parts[0] == "trigger":
            value = workflow_state["context"]
            parts = parts[1:]
        elif parts[0] == "previous":
            # Get the previous step result
            steps = list(workflow_state["steps"].keys())
            if not steps:
                return None
                
            previous_step = steps[-1]
            value = workflow_state["steps"][previous_step]["result"]
            parts = parts[1:]
        elif parts[0] == "steps" and len(parts) > 1:
            # Get a specific step result
            step_id = parts[1]
            if step_id not in workflow_state["steps"]:
                return None
                
            value = workflow_state["steps"][step_id]["result"]
            parts = parts[2:]
        else:
            # Start with the workflow state
            value = workflow_state
        
        # Navigate the path
        for part in parts:
            if isinstance(value, dict) and part in value:
                value = value[part]
            elif isinstance(value, list) and part.isdigit() and int(part) < len(value):
                value = value[int(part)]
            else:
                return None
        
        return value
    
    def _evaluate_condition(self, condition: str, context: Dict) -> Any:
        """
        Evaluate a condition expression.
        
        Args:
            condition (str): Condition expression
            context (Dict): Evaluation context
            
        Returns:
            Any: Evaluation result
        """
        # For simplicity, we'll just support basic conditions
        # In a real implementation, you'd use a proper expression evaluator
        
        # Replace references with their values
        for var_name, var_value in context.items():
            condition = condition.replace(var_name + ".", f"context['{var_name}'].")
        
        # Simple equality check
        if " === " in condition:
            left, right = condition.split(" === ")
            left_value = eval(left, {"context": context})
            right_value = eval(right, {"context": context})
            return left_value == right_value
        
        # Simple inequality check
        if " !== " in condition:
            left, right = condition.split(" !== ")
            left_value = eval(left, {"context": context})
            right_value = eval(right, {"context": context})
            return left_value != right_value
        
        # Simple greater than check
        if " > " in condition:
            left, right = condition.split(" > ")
            left_value = eval(left, {"context": context})
            right_value = eval(right, {"context": context})
            return left_value > right_value
        
        # Simple less than check
        if " < " in condition:
            left, right = condition.split(" < ")
            left_value = eval(left, {"context": context})
            right_value = eval(right, {"context": context})
            return left_value < right_value
        
        # For other conditions, try to evaluate directly
        try:
            return eval(condition, {"context": context})
        except Exception as e:
            logger.error(f"Error evaluating condition '{condition}': {str(e)}")
            return False
    
    async def shutdown(self):
        """Shutdown the workflow engine."""
        logger.info("Shutting down workflow engine")
        
        # Stop the workflow processor
        self.running = False
        
        # Cancel all scheduled workflows
        for workflow_id, task in self.scheduled_workflows.items():
            task.cancel()
        
        # Clear all data
        self.workflows = {}
        self.active_workflows = {}
        self.scheduled_workflows = {}
        self.event_handlers = {}
        
        logger.info("Workflow engine shut down")
