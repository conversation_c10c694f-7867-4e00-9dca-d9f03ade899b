#!/usr/bin/env python3
"""
UI-TARS 1.5 Email Sender

This script properly integrates with UI-TARS 1.5 to send an email to Alyssa C.
using browser automation. It ensures the email fields are correctly populated
and demonstrates the actual UI-TARS 1.5 API calls being used.
"""

import os
import sys
import time
import json
import logging
import requests
import subprocess
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("UI-TARS-EmailSender")

# UI-TARS 1.5 API Configuration
UI_TARS_BASE_URL = "http://localhost:8080/v1"
UI_TARS_API_KEY = "hf_dummy_key"  # As specified in user preferences

# Email content
EMAIL_SUBJECT = "URGENT: Your Insurance Options - Coverage Available Within Your $100 Monthly Budget"
EMAIL_BODY = """
Hi <PERSON><PERSON>,

I hope this message finds you well. We've been trying to reach you through multiple channels (email, phone calls, voicemails, and texts) regarding your insurance needs, and I wanted to follow up personally as this is time-sensitive.

Based on your specific situation and $100 monthly budget, we have options ready for you that provide excellent coverage:

For your IUL policy (approximately $65/month):
- Cash value growth potential tied to market performance without the downside risk
- Death benefit protection for your loved ones
- Tax-free access to your cash value for future needs
- Living benefits that allow access to your death benefit if you become critically ill

For your health/dental/vision package (approximately $35/month):
- Comprehensive health coverage with our top-tier carriers that offer exceptional benefits
- Dental coverage including preventive care, basic procedures, and major work
- Vision benefits covering exams, frames, and contacts

What makes us the best agency to handle your insurance needs:
1. Our carriers offer some of the most comprehensive health benefits in the industry, with lower deductibles and better coverage than you'll find elsewhere
2. We have flexible IUL, whole life, and term policy options that can be customized to your exact needs
3. Our mortgage protection extends for the entire life of your loan, unlike competitors who offer limited coverage periods
4. For qualified applicants like yourself, we can secure over $1 million in coverage

We need to speak with you as soon as possible to secure this coverage before rates change. I have the following time slots available tomorrow (Monday):
- 10:00 AM - 10:30 AM
- 1:00 PM - 1:30 PM
- 4:00 PM - 4:30 PM

Or Tuesday:
- 9:00 AM - 9:30 AM
- 2:00 PM - 2:30 PM

Please let me know which time works best for you, or you can schedule directly through our Calendly link:
https://calendly.com/flofaction/insurance-consultation

It's critical that we connect in the next 24-48 hours to ensure we can lock in these rates for you.

Looking forward to speaking with you soon,

Paul Edwards
Flo Faction Insurance
(772) 208-9646
"""

RECIPIENT_EMAIL = "<EMAIL>"  # Replace with actual email

class UITarsClient:
    """Client for interacting with UI-TARS 1.5 API"""
    
    def __init__(self, base_url, api_key):
        self.base_url = base_url
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        self.session_id = None
    
    def check_connection(self):
        """Check if UI-TARS 1.5 is running and accessible"""
        try:
            response = requests.get(f"{self.base_url}/status", headers=self.headers)
            if response.status_code == 200:
                logger.info("UI-TARS 1.5 is running and accessible")
                return True
            else:
                logger.error(f"UI-TARS 1.5 returned status code: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to connect to UI-TARS 1.5: {e}")
            return False
    
    def start_uitars(self):
        """Start UI-TARS 1.5 if it's not running"""
        logger.info("Attempting to start UI-TARS 1.5...")
        
        # Check if UI-TARS directory exists
        if not os.path.exists("ui_tars"):
            logger.error("UI-TARS directory not found")
            return False
        
        # Try to start UI-TARS 1.5
        try:
            # Change to the UI-TARS directory
            os.chdir("ui_tars")
            
            # Start UI-TARS 1.5 in a new process
            subprocess.Popen(["python", "main.py"], 
                            stdout=subprocess.PIPE, 
                            stderr=subprocess.PIPE)
            
            # Change back to the original directory
            os.chdir("..")
            
            # Wait for UI-TARS to start
            logger.info("Waiting for UI-TARS 1.5 to start...")
            time.sleep(5)
            
            # Check if UI-TARS is now running
            for _ in range(5):  # Try 5 times
                if self.check_connection():
                    return True
                time.sleep(2)
            
            logger.error("Failed to start UI-TARS 1.5")
            return False
        except Exception as e:
            logger.error(f"Error starting UI-TARS 1.5: {e}")
            return False
    
    def start_session(self):
        """Start a new browser automation session"""
        try:
            payload = {
                "browser": "chrome",
                "headless": False,
                "timeout": 30000
            }
            response = requests.post(
                f"{self.base_url}/browser/session", 
                headers=self.headers,
                json=payload
            )
            if response.status_code == 200:
                self.session_id = response.json().get("session_id")
                logger.info(f"Started browser session with ID: {self.session_id}")
                return True
            else:
                logger.error(f"Failed to start browser session: {response.text}")
                return False
        except requests.exceptions.RequestException as e:
            logger.error(f"Error starting browser session: {e}")
            return False
    
    def navigate_to(self, url):
        """Navigate to a specific URL"""
        if not self.session_id:
            logger.error("No active session")
            return False
        
        try:
            payload = {
                "session_id": self.session_id,
                "url": url
            }
            response = requests.post(
                f"{self.base_url}/browser/navigate", 
                headers=self.headers,
                json=payload
            )
            if response.status_code == 200:
                logger.info(f"Navigated to: {url}")
                return True
            else:
                logger.error(f"Failed to navigate: {response.text}")
                return False
        except requests.exceptions.RequestException as e:
            logger.error(f"Error navigating: {e}")
            return False
    
    def wait_for_element(self, selector, timeout=10):
        """Wait for an element to be present on the page"""
        if not self.session_id:
            logger.error("No active session")
            return False
        
        try:
            payload = {
                "session_id": self.session_id,
                "selector": selector,
                "timeout": timeout * 1000
            }
            response = requests.post(
                f"{self.base_url}/browser/wait_for_element", 
                headers=self.headers,
                json=payload
            )
            if response.status_code == 200:
                logger.info(f"Element found: {selector}")
                return True
            else:
                logger.error(f"Element not found: {selector}")
                return False
        except requests.exceptions.RequestException as e:
            logger.error(f"Error waiting for element: {e}")
            return False
    
    def type_text(self, selector, text):
        """Type text into an element"""
        if not self.session_id:
            logger.error("No active session")
            return False
        
        try:
            payload = {
                "session_id": self.session_id,
                "selector": selector,
                "text": text
            }
            response = requests.post(
                f"{self.base_url}/browser/type", 
                headers=self.headers,
                json=payload
            )
            if response.status_code == 200:
                logger.info(f"Typed text into: {selector}")
                return True
            else:
                logger.error(f"Failed to type text: {response.text}")
                return False
        except requests.exceptions.RequestException as e:
            logger.error(f"Error typing text: {e}")
            return False
    
    def click_element(self, selector):
        """Click on an element"""
        if not self.session_id:
            logger.error("No active session")
            return False
        
        try:
            payload = {
                "session_id": self.session_id,
                "selector": selector
            }
            response = requests.post(
                f"{self.base_url}/browser/click", 
                headers=self.headers,
                json=payload
            )
            if response.status_code == 200:
                logger.info(f"Clicked element: {selector}")
                return True
            else:
                logger.error(f"Failed to click element: {response.text}")
                return False
        except requests.exceptions.RequestException as e:
            logger.error(f"Error clicking element: {e}")
            return False
    
    def close_session(self):
        """Close the browser session"""
        if not self.session_id:
            logger.warning("No active session to close")
            return True
        
        try:
            payload = {
                "session_id": self.session_id
            }
            response = requests.post(
                f"{self.base_url}/browser/close", 
                headers=self.headers,
                json=payload
            )
            if response.status_code == 200:
                logger.info(f"Closed browser session: {self.session_id}")
                self.session_id = None
                return True
            else:
                logger.error(f"Failed to close browser session: {response.text}")
                return False
        except requests.exceptions.RequestException as e:
            logger.error(f"Error closing browser session: {e}")
            return False

def send_email_with_uitars():
    """Send an email to Alyssa C. using UI-TARS 1.5 browser automation"""
    
    # Initialize UI-TARS client
    client = UITarsClient(UI_TARS_BASE_URL, UI_TARS_API_KEY)
    
    # Check if UI-TARS is running
    if not client.check_connection():
        logger.warning("UI-TARS 1.5 is not running, attempting to start it...")
        if not client.start_uitars():
            # If we can't start UI-TARS, fall back to direct browser automation
            logger.warning("Could not start UI-TARS 1.5, falling back to direct browser automation")
            return fallback_send_email()
    
    # Start a browser session
    if not client.start_session():
        logger.error("Failed to start browser session")
        return fallback_send_email()
    
    try:
        # Navigate to Gmail
        if not client.navigate_to("https://mail.google.com/mail/u/0/#inbox?compose=new"):
            logger.error("Failed to navigate to Gmail")
            return False
        
        # Wait for the compose form to load
        if not client.wait_for_element("div[role='dialog'][aria-label*='Compose']", timeout=15):
            logger.error("Compose form did not load")
            return False
        
        # Fill in recipient
        if not client.wait_for_element("input[role='combobox'][aria-label*='To']", timeout=5):
            logger.error("Recipient field not found")
            return False
        if not client.type_text("input[role='combobox'][aria-label*='To']", RECIPIENT_EMAIL):
            logger.error("Failed to enter recipient")
            return False
        
        # Fill in subject
        if not client.wait_for_element("input[name='subjectbox']", timeout=5):
            logger.error("Subject field not found")
            return False
        if not client.type_text("input[name='subjectbox']", EMAIL_SUBJECT):
            logger.error("Failed to enter subject")
            return False
        
        # Fill in email body
        if not client.wait_for_element("div[role='textbox'][aria-label*='Message Body']", timeout=5):
            logger.error("Email body field not found")
            return False
        if not client.type_text("div[role='textbox'][aria-label*='Message Body']", EMAIL_BODY):
            logger.error("Failed to enter email body")
            return False
        
        # Click send button
        if not client.wait_for_element("div[role='button'][aria-label*='Send']", timeout=5):
            logger.error("Send button not found")
            return False
        if not client.click_element("div[role='button'][aria-label*='Send']"):
            logger.error("Failed to click send button")
            return False
        
        # Wait for confirmation
        time.sleep(3)
        
        # Create a confirmation file
        create_confirmation_file("UI-TARS 1.5 Browser Automation")
        
        logger.info("Email sent successfully to Alyssa C.!")
        return True
    
    except Exception as e:
        logger.error(f"Error sending email: {e}")
        return fallback_send_email()
    
    finally:
        # Close the browser session
        client.close_session()

def fallback_send_email():
    """Fallback method to send email using PowerShell script"""
    logger.info("Using fallback method to send email...")
    
    try:
        # Run the PowerShell script
        result = subprocess.run(
            ["powershell", "-ExecutionPolicy", "Bypass", "-File", "send_email_chrome.ps1"],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            logger.info("Fallback email sent successfully")
            create_confirmation_file("Chrome Browser Automation (Fallback)")
            return True
        else:
            logger.error(f"Fallback email failed: {result.stderr}")
            return False
    except Exception as e:
        logger.error(f"Error in fallback email: {e}")
        return False

def create_confirmation_file(method):
    """Create a confirmation file for the sent email"""
    confirmation_content = f"""EMAIL SENT CONFIRMATION
------------------------
Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
From: Paul Edwards - Flo Faction Insurance <<EMAIL>>
To: {RECIPIENT_EMAIL}
Subject: {EMAIL_SUBJECT}
Status: SENT SUCCESSFULLY
Method: {method}
"""
    
    confirmation_file = "email_confirmation_uitars.txt"
    with open(confirmation_file, "w") as f:
        f.write(confirmation_content)
    
    logger.info(f"Confirmation saved to: {confirmation_file}")

if __name__ == "__main__":
    logger.info("Starting email automation to Alyssa C. using UI-TARS 1.5...")
    success = send_email_with_uitars()
    if success:
        logger.info("Email automation completed successfully!")
        sys.exit(0)
    else:
        logger.error("Email automation failed!")
        sys.exit(1)
