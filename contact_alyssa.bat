@echo off
echo Contact Alyssa using UI-TARS
echo ===========================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Check if UI-TARS is running
echo Checking if UI-TARS is running...
python check_ui_tars_running.py >nul 2>&1
if %errorlevel% neq 0 (
    echo UI-TARS is not running. Starting UI-TARS...
    start /b ui_tars_complete_solution.bat
    echo Waiting for UI-TARS to start...
    timeout /t 15 >nul
)

REM Ask for Gmail credentials
echo.
echo Enter Gmail email address (default: <EMAIL>):
set /p EMAIL=""
if "%EMAIL%"=="" set EMAIL=<EMAIL>

echo.
echo Enter Gmail password (default: GodisSoGood!777):
set /p PASSWORD=""
if "%PASSWORD%"=="" set PASSWORD=GodisSoGood!777

REM Ask for dry run option
echo.
echo Do you want to perform a dry run (don't actually send the email)? (Y/N, default: Y)
set /p DRY_RUN=""
if /i "%DRY_RUN%"=="N" (
    set DRY_RUN_OPTION=
) else (
    set DRY_RUN_OPTION=--dry-run
)

REM Run the contact script
echo.
echo Running contact script...
echo.

set COMMAND=python contact_alyssa.py --email "%EMAIL%" --password "%PASSWORD%" %DRY_RUN_OPTION% --debug

echo Executing: %COMMAND%
echo.

%COMMAND%

echo.
if %errorlevel% equ 0 (
    echo Test completed successfully!
) else (
    echo Test failed. Please check the error messages above.
)

echo.
pause
