"""
Setup script for Google services (Gmail and Google Drive).
This script helps you set up the necessary credentials for Gmail and Google Drive integration.
"""
import os
import sys
import json
import argparse
from pathlib import Path

def create_credentials_directory():
    """Create the credentials directory if it doesn't exist."""
    os.makedirs('credentials', exist_ok=True)
    print("Created credentials directory.")

def setup_gmail(email=None):
    """
    Set up Gmail credentials for a specific account or all accounts.

    Args:
        email (str, optional): Email address to set up. If None, will prompt for selection.
    """
    print("\n=== Gmail Setup ===")

    # Load email accounts configuration
    accounts_config_path = 'config/email_accounts.json'
    if not os.path.exists(accounts_config_path):
        print(f"Error: Email accounts configuration file not found at {accounts_config_path}")
        print("Please make sure the file exists.")
        return

    with open(accounts_config_path, 'r') as f:
        accounts_config = json.load(f)

    # Combine priority and additional accounts
    all_accounts = []
    for account in accounts_config.get('priority_accounts', []):
        all_accounts.append(account)
    for account in accounts_config.get('additional_accounts', []):
        all_accounts.append(account)

    if not all_accounts:
        print("No email accounts found in the configuration.")
        return

    # If email is not specified, prompt for selection
    if email is None:
        print("\nAvailable email accounts:")
        for i, account in enumerate(all_accounts):
            print(f"{i+1}. {account['email']} - {account['description']}")

        print(f"{len(all_accounts)+1}. All accounts")

        selection = input("\nSelect an account to set up (or 'all' for all accounts): ")

        if selection.lower() == 'all' or selection == str(len(all_accounts)+1):
            # Set up all accounts
            for account in all_accounts:
                setup_gmail_account(account['email'])
            return

        try:
            index = int(selection) - 1
            if 0 <= index < len(all_accounts):
                email = all_accounts[index]['email']
            else:
                print("Invalid selection.")
                return
        except ValueError:
            print("Invalid selection.")
            return

    # Set up the selected account
    setup_gmail_account(email)

def setup_gmail_account(email):
    """
    Set up Gmail credentials for a specific account.

    Args:
        email (str): Email address to set up
    """
    print(f"\n=== Setting up Gmail for {email} ===")
    print("To use Gmail with your AI agents, you need to create OAuth 2.0 credentials.")
    print("Follow these steps:")
    print("1. Go to https://console.cloud.google.com/")
    print("2. Create a new project or select an existing one")
    print("3. Enable the Gmail API for your project")
    print("4. Create OAuth 2.0 credentials (Desktop application)")
    print("5. Download the credentials JSON file")

    # Create a safe filename from the email address
    safe_email = email.replace("@", "_at_").replace(".", "_dot_")
    credentials_path = f'credentials/gmail_{safe_email}_credentials.json'

    if os.path.exists(credentials_path):
        print(f"\nGmail credentials already exist at {credentials_path}")
        replace = input("Do you want to replace them? (y/n): ").lower()
        if replace != 'y':
            print(f"Keeping existing Gmail credentials for {email}.")
            return

    print("\nWaiting for you to save the credentials file...")
    print(f"Please save the downloaded JSON file as '{credentials_path}'")
    input("Press Enter when you've saved the file...")

    if os.path.exists(credentials_path):
        print(f"Gmail credentials for {email} saved successfully!")
        print("The first time you use the Gmail service, you'll need to authorize the application.")
    else:
        print(f"Error: Could not find credentials file at {credentials_path}")
        print("Please make sure you've saved the file correctly.")

def setup_google_drive():
    """Set up Google Drive credentials."""
    print("\n=== Google Drive Setup ===")
    print("To use Google Drive with your AI agents, you need to create OAuth 2.0 credentials.")
    print("Follow these steps:")
    print("1. Go to https://console.cloud.google.com/")
    print("2. Create a new project or select an existing one")
    print("3. Enable the Google Drive API for your project")
    print("4. Create OAuth 2.0 credentials (Desktop application)")
    print("5. Download the credentials JSON file")
    print("6. Save the file as 'credentials/google_drive_credentials.json'")

    credentials_path = 'credentials/google_drive_credentials.json'

    if os.path.exists(credentials_path):
        print(f"\nGoogle Drive credentials already exist at {credentials_path}")
        replace = input("Do you want to replace them? (y/n): ").lower()
        if replace != 'y':
            print("Keeping existing Google Drive credentials.")
            return

    print("\nWaiting for you to save the credentials file...")
    print(f"Please save the downloaded JSON file as '{credentials_path}'")
    input("Press Enter when you've saved the file...")

    if os.path.exists(credentials_path):
        print("Google Drive credentials saved successfully!")
        print("The first time you use the Google Drive service, you'll need to authorize the application.")
    else:
        print(f"Error: Could not find credentials file at {credentials_path}")
        print("Please make sure you've saved the file correctly.")

def test_gmail_connection():
    """Test the Gmail connection."""
    try:
        from services.gmail_service import GmailServiceFactory

        print("\n=== Testing Gmail Connection ===")
        gmail_service = GmailServiceFactory.create_service()

        if gmail_service.is_enabled():
            print(f"Successfully connected to Gmail as {gmail_service.get_user_email()}")
            return True
        else:
            print("Failed to connect to Gmail. Please check your credentials.")
            return False

    except Exception as e:
        print(f"Error testing Gmail connection: {e}")
        return False

def test_google_drive_connection():
    """Test the Google Drive connection."""
    try:
        from services.google_drive_service import GoogleDriveServiceFactory
        import asyncio

        print("\n=== Testing Google Drive Connection ===")
        drive_service = GoogleDriveServiceFactory.create_service()

        if drive_service.is_enabled():
            # Test listing files
            loop = asyncio.get_event_loop()
            result = loop.run_until_complete(drive_service.list_files(max_results=5))

            if "error" in result:
                print(f"Error listing files: {result['error']}")
                return False

            print(f"Successfully connected to Google Drive")
            print(f"Found {len(result['files'])} files in your Drive")
            return True
        else:
            print("Failed to connect to Google Drive. Please check your credentials.")
            return False

    except Exception as e:
        print(f"Error testing Google Drive connection: {e}")
        return False

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Setup Google services for the AI Agent System")
    parser.add_argument("--gmail", action="store_true", help="Set up Gmail integration")
    parser.add_argument("--gmail-account", help="Specific Gmail account to set up")
    parser.add_argument("--drive", action="store_true", help="Set up Google Drive integration")
    parser.add_argument("--test", action="store_true", help="Test the connections")
    parser.add_argument("--all", action="store_true", help="Set up all services")

    args = parser.parse_args()

    # If no arguments provided, set up all services
    if not (args.gmail or args.gmail_account or args.drive or args.test or args.all):
        args.all = True

    print("=== Google Services Setup ===")
    print("This script will help you set up Google services for your AI Agent System.")

    # Create credentials directory
    create_credentials_directory()

    # Set up services
    if args.gmail or args.gmail_account or args.all:
        if args.gmail_account:
            setup_gmail(args.gmail_account)
        else:
            setup_gmail()

    if args.drive or args.all:
        setup_google_drive()

    # Test connections
    if args.test or args.all:
        if args.gmail or args.gmail_account or args.all:
            if args.gmail_account:
                # Test specific account
                from services.gmail_service import GmailService
                safe_email = args.gmail_account.replace("@", "_at_").replace(".", "_dot_")
                credentials_path = f'credentials/gmail_{safe_email}_credentials.json'
                token_path = f'credentials/gmail_{safe_email}_token.pickle'

                gmail_service = GmailService(credentials_path, token_path)
                if gmail_service.is_enabled():
                    print(f"Successfully connected to Gmail as {gmail_service.get_user_email()}")
                else:
                    print(f"Failed to connect to Gmail account {args.gmail_account}. Please check your credentials.")
            else:
                # Test multi-account setup
                try:
                    from services.multi_account_gmail_service import MultiAccountGmailServiceFactory

                    print("\n=== Testing Multi-Account Gmail Connection ===")
                    gmail_service = MultiAccountGmailServiceFactory.create_service()

                    if gmail_service.is_enabled():
                        enabled_accounts = gmail_service.get_enabled_accounts()
                        print(f"Successfully connected to {len(enabled_accounts)} Gmail accounts:")
                        for account in enabled_accounts:
                            print(f"  - {account}")
                        return True
                    else:
                        print("Failed to connect to Gmail. Please check your credentials.")
                        return False

                except Exception as e:
                    print(f"Error testing Gmail connection: {e}")

                    # Fall back to testing single account
                    test_gmail_connection()

        if args.drive or args.all:
            test_google_drive_connection()

    print("\n=== Setup Complete ===")
    print("You can now use Gmail and Google Drive with your AI agents.")
    print("To use these services in your agents, import the appropriate factory classes:")
    print("- For single Gmail account: from services.gmail_service import GmailServiceFactory")
    print("- For multiple Gmail accounts: from services.multi_account_gmail_service import MultiAccountGmailServiceFactory")
    print("- For Google Drive: from services.google_drive_service import GoogleDriveServiceFactory")

if __name__ == "__main__":
    main()
