#!/usr/bin/env python3
"""
Show <PERSON><PERSON> Contact Messages

This script displays the messages that would be sent to <PERSON><PERSON>
as part of the initial contact and follow-up campaign.
"""

import json
import os
from datetime import datetime, timedelta

def load_config():
    """Load configuration from the communication_services.json file."""
    try:
        config_path = os.path.join("config", "communication_services.json")
        with open(config_path, "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading configuration: {e}")
        return {}

def format_template(template, vars_dict):
    """Format a template with the given variables."""
    if isinstance(template, dict):
        subject = template.get("subject", "")
        body = template.get("body", "").format(**vars_dict)
        return subject, body
    else:
        return "", template.format(**vars_dict)

def main():
    """Main function to display contact messages for <PERSON><PERSON>."""
    # Load configuration
    config = load_config()
    if not config:
        print("No configuration found. Exiting.")
        return

    # Get templates
    email_templates = config.get("email_integration", {}).get("email_templates", {})
    text_templates = config.get("voice_calling_service", {}).get("text_templates", {})
    voicemail_templates = config.get("voice_calling_service", {}).get("voicemail_templates", {})
    call_templates = config.get("voice_calling_service", {}).get("call_templates", {})

    # Client information
    client_info = {
        "client_name": "Alyssa Chirinos",
        "first_name": "Alyssa",
        "agent_name": "Sandra",
        "phone_number": "(*************",
        "dob": "8/16/97",
        "address": "Bradenton, Florida",
        "insurance_type": "IUL with Dental, Vision, and Basic Health",
        "estimated_premium": "$100/month",
        "notes": "Primary interest is IUL. Also interested in dental, vision, and basic private health coverage for checkups, physicals, and bloodwork. TOTAL BUDGET IS $100/MONTH. Need to check all carriers for best solution within budget."
    }

    # Display initial contact messages
    print("\n" + "="*80)
    print(" INITIAL CONTACT MESSAGES FOR ALYSSA CHIRINOS ".center(80, "="))
    print("="*80 + "\n")

    # Display email messages
    print("\n" + "-"*80)
    print(" EMAIL MESSAGES ".center(80, "-"))
    print("-"*80 + "\n")
    
    if "new_client" in email_templates:
        subject, body = format_template(email_templates["new_client"], client_info)
        print(f"INITIAL EMAIL:")
        print(f"To: <EMAIL>")
        print(f"From: <EMAIL>")
        print(f"Subject: {subject}")
        print(f"Body:\n{body}\n")

    if "new_client_quote" in email_templates:
        subject, body = format_template(email_templates["new_client_quote"], client_info)
        print(f"QUOTE EMAIL:")
        print(f"To: <EMAIL>")
        print(f"From: <EMAIL>")
        print(f"Subject: {subject}")
        print(f"Body:\n{body}\n")

    # Display text message
    print("\n" + "-"*80)
    print(" TEXT MESSAGE ".center(80, "-"))
    print("-"*80 + "\n")
    
    if "new_client" in text_templates:
        message = text_templates["new_client"].format(**client_info)
        print(f"To: 9419294330")
        print(f"From: (*************")
        print(f"Message: {message}\n")

    # Display voicemail message
    print("\n" + "-"*80)
    print(" VOICEMAIL MESSAGE ".center(80, "-"))
    print("-"*80 + "\n")
    
    if "new_client" in voicemail_templates:
        message = voicemail_templates["new_client"].format(**client_info)
        print(f"To: 9419294330")
        print(f"From: (*************")
        print(f"Voice: ElevenLabs female voice")
        print(f"Message: {message}\n")

    # Display call script
    print("\n" + "-"*80)
    print(" CALL SCRIPT ".center(80, "-"))
    print("-"*80 + "\n")
    
    if "new_client" in call_templates:
        script = call_templates["new_client"]["script"].format(**client_info)
        print(f"To: 9419294330")
        print(f"From: (*************")
        print(f"Voice: {call_templates['new_client'].get('voice_type', 'female')} {call_templates['new_client'].get('voice_style', 'professional')}")
        print(f"Script: {script}\n")

    # Display follow-up schedule
    print("\n" + "="*80)
    print(" FOLLOW-UP SCHEDULE ".center(80, "="))
    print("="*80 + "\n")

    today = datetime.now()
    
    # Day 2 follow-up (text message)
    day2 = today + timedelta(days=2)
    print(f"DAY 2 FOLLOW-UP ({day2.strftime('%Y-%m-%d')}):")
    print("Text Message: Hey Alyssa! Just checking in to see if you had a chance to review those IUL options I sent. I'm especially excited about Option 2 with the health discount plan for your checkups. Let me know if you have any questions! - Sandra")
    
    # Day 4 follow-up (call)
    day4 = today + timedelta(days=4)
    print(f"\nDAY 4 FOLLOW-UP ({day4.strftime('%Y-%m-%d')}):")
    print("Phone Call: Hi Alyssa, it's Sandra from Flo Faction Insurance. I wanted to follow up on the IUL options I sent you last week. I'd love to answer any questions you might have and help you get set up with the coverage you need. Please give me a call back at (************* when you have a moment.")
    
    # Day 7 follow-up (email)
    day7 = today + timedelta(days=7)
    print(f"\nDAY 7 FOLLOW-UP ({day7.strftime('%Y-%m-%d')}):")
    print("Email Subject: Quick follow-up on your IUL options")
    print("Email Body: Hi Alyssa,\n\nI hope you're doing well! I wanted to check in about the insurance options I sent you last week. Have you had a chance to review them?\n\nI'm still thinking that Option 2 might be perfect for your situation, especially with the health discount plan for your checkups and bloodwork.\n\nI'm happy to answer any questions you might have or make adjustments to better fit your needs. Just let me know!\n\nBest regards,\nSandra\nFlo Faction Insurance\nCell: (*************\nEmail: <EMAIL>")
    
    # Day 14 follow-up (final check-in)
    day14 = today + timedelta(days=14)
    print(f"\nDAY 14 FOLLOW-UP ({day14.strftime('%Y-%m-%d')}):")
    print("Text Message: Hi Alyssa, just wanted to check in one last time about those insurance options. I'm still available to help if you're interested. No pressure at all - just want to make sure you have what you need! - Sandra")

if __name__ == "__main__":
    main()
