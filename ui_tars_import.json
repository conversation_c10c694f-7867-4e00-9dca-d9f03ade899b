{
    "name": "UI-TARS Local LLM Configuration",
    "version": "1.0",
    "models": {
        "ui-tars": {
            "path": "C:/Users/<USER>/models\\UI-TARS-1.5-7B",
            "type": "ui-tars",
            "quantization": "4bit",
            "enabled": true
        }
    },
    "settings": {
        "use_local_model": true,
        "api_key": null,
        "autonomous_mode": true,
        "voice_commands_enabled": true,
        "browser": {
            "type": "chrome"{
    "name": "UI-TARS Local LLM Configuration",
    "version": "1.0",
    "models": {
        "ui-tars": {
            "path": "C:/Users/<USER>/models/UI-TARS-1.5-7B",
            "type": "ui-tars",
            "quantization": "4bit",
            "enabled": true
        }
    },
    "settings": {
        "use_local_model": true,
        "api_key": null,
        "autonomous_mode": true,
        "voice_commands_enabled": true,
        "browser": {
            "type": "chrome",
            "automation": {
                "enabled": true
            }
        },
        "nvidia": {
            "enabled": true,
            "cuda": {
                "enabled": true
            }
        },
        "default_model": "ui-tars",
        "logging": {
            "level": "INFO",
            "file": "logs/ui-tars.log"
        }
    }
},
            "automation": {
                "enabled": true
            }
        },
        "nvidia": {
            "enabled": true,
            "cuda": {
                "enabled": true
            }
        },
        "default_model": "ui-tars"
    }
}