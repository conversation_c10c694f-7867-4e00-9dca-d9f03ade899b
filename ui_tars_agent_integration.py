"""
UI-TARS Agent Integration Module.

This module integrates UI-TARS with the AI Agent System, allowing agents to use
UI-TARS capabilities for autonomous browser control and desktop automation.
"""
import os
import sys
import json
import asyncio
import logging
import requests
import subprocess
import time
from typing import Dict, List, Optional, Any, Union
import threading

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("ui_tars_agent_integration")

class UITarsAgent:
    """
    UI-TARS Agent for the AI Agent System.
    
    This class provides an interface for agents to use UI-TARS capabilities.
    """
    
    def __init__(self, 
                 api_url: str = "http://127.0.0.1:8000",
                 api_key: str = "dummy_key",
                 model_name: str = "UI-TARS-1.5-7B",
                 ui_tars_path: Optional[str] = None,
                 server_script_path: Optional[str] = None):
        """
        Initialize the UI-TARS Agent.
        
        Args:
            api_url (str): URL of the UI-TARS API server
            api_key (str): API key for the UI-TARS API server
            model_name (str): Name of the model to use
            ui_tars_path (Optional[str]): Path to the UI-TARS executable
            server_script_path (Optional[str]): Path to the server script
        """
        self.api_url = api_url
        self.api_key = api_key
        self.model_name = model_name
        self.ui_tars_path = ui_tars_path
        self.server_script_path = server_script_path or "simple_hf_server.py"
        self.session = requests.Session()
        self.server_process = None
        self.ui_tars_process = None
        self.is_server_running = False
        self.is_ui_tars_running = False
    
    async def start_server(self):
        """Start the local API server."""
        if self.is_server_running:
            logger.info("Server is already running")
            return True
        
        logger.info("Starting local API server")
        
        try:
            # Check if the server is already running
            try:
                response = requests.get(f"{self.api_url}/health")
                if response.status_code == 200:
                    logger.info("Server is already running")
                    self.is_server_running = True
                    return True
            except:
                pass
            
            # Start the server
            self.server_process = subprocess.Popen(
                ["python", self.server_script_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Wait for the server to start
            max_retries = 30
            for i in range(max_retries):
                try:
                    response = requests.get(f"{self.api_url}/health")
                    if response.status_code == 200:
                        logger.info("Server started successfully")
                        self.is_server_running = True
                        return True
                except:
                    if i == max_retries - 1:
                        logger.warning("Timed out waiting for server to start")
                        return False
                    
                    logger.info(f"Waiting for server to start ({i+1}/{max_retries})...")
                    await asyncio.sleep(1)
            
            return False
        
        except Exception as e:
            logger.exception(f"Error starting server: {e}")
            return False
    
    async def start_ui_tars(self):
        """Start UI-TARS."""
        if self.is_ui_tars_running:
            logger.info("UI-TARS is already running")
            return True
        
        logger.info("Starting UI-TARS")
        
        try:
            # Check if UI-TARS path is provided
            if not self.ui_tars_path:
                # Try to find UI-TARS in common locations
                common_paths = [
                    os.path.join(os.environ.get("LOCALAPPDATA", ""), "UI-TARS", "UI-TARS.exe"),
                    os.path.join(os.environ.get("PROGRAMFILES", ""), "UI-TARS", "UI-TARS.exe"),
                    os.path.join(os.environ.get("PROGRAMFILES(X86)", ""), "UI-TARS", "UI-TARS.exe")
                ]
                
                for path in common_paths:
                    if os.path.exists(path):
                        self.ui_tars_path = path
                        break
            
            if not self.ui_tars_path or not os.path.exists(self.ui_tars_path):
                logger.warning("UI-TARS executable not found")
                return False
            
            # Start UI-TARS
            self.ui_tars_process = subprocess.Popen(
                [self.ui_tars_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Wait for UI-TARS to start
            await asyncio.sleep(5)
            
            self.is_ui_tars_running = True
            logger.info("UI-TARS started successfully")
            return True
        
        except Exception as e:
            logger.exception(f"Error starting UI-TARS: {e}")
            return False
    
    async def initialize(self):
        """Initialize the UI-TARS Agent."""
        logger.info("Initializing UI-TARS Agent")
        
        # Start the server
        server_started = await self.start_server()
        if not server_started:
            logger.warning("Failed to start server")
            return False
        
        # Start UI-TARS
        ui_tars_started = await self.start_ui_tars()
        if not ui_tars_started:
            logger.warning("Failed to start UI-TARS")
            # Continue anyway, as UI-TARS might already be running
        
        logger.info("UI-TARS Agent initialized")
        return True
    
    async def execute_command(self, command: str) -> Dict:
        """
        Execute a command in UI-TARS.
        
        Args:
            command (str): Command to execute
            
        Returns:
            Dict: Response from UI-TARS
        """
        logger.info(f"Executing command: {command}")
        
        try:
            # Prepare the request
            data = {
                "inputs": command,
                "parameters": {
                    "max_tokens": 1000,
                    "temperature": 0.7
                }
            }
            
            # Send the request
            response = self.session.post(
                f"{self.api_url}/models/{self.model_name}/generate",
                json=data,
                headers={"Authorization": f"Bearer {self.api_key}"}
            )
            response.raise_for_status()
            
            # Parse the response
            result = response.json()
            
            logger.info(f"Command executed successfully")
            return result
        
        except Exception as e:
            logger.exception(f"Error executing command: {e}")
            return {"error": str(e)}
    
    async def browse_website(self, url: str, task: str = "browse") -> Dict:
        """
        Browse a website using UI-TARS.
        
        Args:
            url (str): URL to browse
            task (str): Task to perform on the website
            
        Returns:
            Dict: Response from UI-TARS
        """
        logger.info(f"Browsing website: {url}, task: {task}")
        
        # Construct the command
        command = f"Browse to {url} and {task}"
        
        # Execute the command
        result = await self.execute_command(command)
        
        return result
    
    async def search_web(self, query: str, engine: str = "google") -> Dict:
        """
        Search the web using UI-TARS.
        
        Args:
            query (str): Search query
            engine (str): Search engine to use
            
        Returns:
            Dict: Response from UI-TARS
        """
        logger.info(f"Searching web: {query}, engine: {engine}")
        
        # Construct the command
        command = f"Search for {query} using {engine}"
        
        # Execute the command
        result = await self.execute_command(command)
        
        return result
    
    async def fill_form(self, url: str, form_data: Dict[str, str]) -> Dict:
        """
        Fill a form on a website using UI-TARS.
        
        Args:
            url (str): URL of the form
            form_data (Dict[str, str]): Form data to fill
            
        Returns:
            Dict: Response from UI-TARS
        """
        logger.info(f"Filling form on {url}")
        
        # Construct the command
        command = f"Go to {url} and fill out the form with the following information: "
        for field, value in form_data.items():
            command += f"{field}: {value}, "
        command = command.rstrip(", ")
        command += ". Then submit the form."
        
        # Execute the command
        result = await self.execute_command(command)
        
        return result
    
    async def take_screenshot(self) -> Dict:
        """
        Take a screenshot using UI-TARS.
        
        Returns:
            Dict: Response from UI-TARS
        """
        logger.info("Taking screenshot")
        
        # Construct the command
        command = "Take a screenshot"
        
        # Execute the command
        result = await self.execute_command(command)
        
        return result
    
    async def extract_data(self, url: str, data_type: str) -> Dict:
        """
        Extract data from a website using UI-TARS.
        
        Args:
            url (str): URL of the website
            data_type (str): Type of data to extract
            
        Returns:
            Dict: Response from UI-TARS
        """
        logger.info(f"Extracting {data_type} from {url}")
        
        # Construct the command
        command = f"Go to {url} and extract the {data_type}"
        
        # Execute the command
        result = await self.execute_command(command)
        
        return result
    
    async def shutdown(self):
        """Shut down the UI-TARS Agent."""
        logger.info("Shutting down UI-TARS Agent")
        
        # Stop UI-TARS
        if self.ui_tars_process:
            logger.info("Stopping UI-TARS")
            self.ui_tars_process.terminate()
            await asyncio.sleep(2)
            
            # Force kill if still running
            if self.ui_tars_process.poll() is None:
                self.ui_tars_process.kill()
            
            self.ui_tars_process = None
            self.is_ui_tars_running = False
        
        # Stop the server
        if self.server_process:
            logger.info("Stopping server")
            self.server_process.terminate()
            await asyncio.sleep(2)
            
            # Force kill if still running
            if self.server_process.poll() is None:
                self.server_process.kill()
            
            self.server_process = None
            self.is_server_running = False
        
        logger.info("UI-TARS Agent shut down")
        return True

# Example usage
async def main():
    """Example usage of the UI-TARS Agent."""
    # Create a UI-TARS Agent
    agent = UITarsAgent()
    
    # Initialize the agent
    await agent.initialize()
    
    # Execute a command
    result = await agent.execute_command("What is the weather today?")
    print(f"Command result: {result}")
    
    # Browse a website
    result = await agent.browse_website("https://www.example.com", "find the contact information")
    print(f"Browse result: {result}")
    
    # Search the web
    result = await agent.search_web("UI-TARS documentation")
    print(f"Search result: {result}")
    
    # Take a screenshot
    result = await agent.take_screenshot()
    print(f"Screenshot result: {result}")
    
    # Shut down the agent
    await agent.shutdown()

if __name__ == "__main__":
    asyncio.run(main())
