"""
Test Agent Integration with Browser Automation.

This script demonstrates how agents can use the browser automation capabilities.
"""
import os
import sys
import json
import asyncio
import logging
from pathlib import Path
from enum import Enum

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("test_agent_integration.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("test_agent_integration")

# Define AutomationProvider enum
class AutomationProvider(Enum):
    """Enum for automation providers."""
    UI_TARS = "ui_tars"
    MIDSCENE = "midscene"
    AUTO = "auto"

# Define a simple browser automation manager
class SimpleBrowserAutomationManager:
    """
    Simple browser automation manager for testing.
    
    This class simulates the browser automation manager for testing purposes.
    """
    
    def __init__(self, provider=AutomationProvider.AUTO):
        """
        Initialize the simple browser automation manager.
        
        Args:
            provider (AutomationProvider): Preferred automation provider
        """
        self.provider = provider
        self.active_provider = None
        self.initialized = False
        logger.info(f"Initialized simple browser automation manager with provider: {provider.value}")
    
    async def initialize(self):
        """Initialize the browser automation manager."""
        logger.info("Initializing browser automation manager")
        
        # Simulate initialization
        await asyncio.sleep(1)
        
        # Set active provider based on preference
        if self.provider == AutomationProvider.UI_TARS:
            self.active_provider = AutomationProvider.UI_TARS
        elif self.provider == AutomationProvider.MIDSCENE:
            self.active_provider = AutomationProvider.MIDSCENE
        else:
            # Auto mode: try UI-TARS first, then Midscene
            self.active_provider = AutomationProvider.UI_TARS
        
        self.initialized = True
        logger.info(f"Browser automation manager initialized with provider: {self.active_provider.value}")
        return True
    
    async def execute_command(self, command):
        """
        Execute a command.
        
        Args:
            command (str): Command to execute
            
        Returns:
            dict: Command result
        """
        if not self.initialized:
            await self.initialize()
        
        logger.info(f"Executing command with {self.active_provider.value}: {command}")
        
        # Simulate command execution
        await asyncio.sleep(1)
        
        # Simulate failure for specific commands to test fallback
        if "fail" in command.lower() and self.active_provider == AutomationProvider.UI_TARS:
            logger.warning(f"Command failed with {self.active_provider.value}")
            
            # Fallback to Midscene
            self.active_provider = AutomationProvider.MIDSCENE
            logger.info(f"Falling back to {self.active_provider.value}")
            
            # Retry command with fallback provider
            await asyncio.sleep(1)
            logger.info(f"Command executed successfully with fallback provider")
            return {"success": True, "message": f"Command executed with fallback provider: {self.active_provider.value}"}
        
        return {"success": True, "message": f"Command executed with provider: {self.active_provider.value}"}
    
    async def health_check(self):
        """
        Perform a health check.
        
        Returns:
            dict: Health check result
        """
        logger.info(f"Performing health check for {self.active_provider.value}")
        
        # Simulate health check
        await asyncio.sleep(1)
        
        return {
            "status": "healthy",
            "issues": [],
            "provider": self.active_provider.value
        }
    
    async def stop(self):
        """Stop the browser automation manager."""
        logger.info(f"Stopping browser automation manager with provider: {self.active_provider.value}")
        
        # Simulate stopping
        await asyncio.sleep(1)
        
        self.initialized = False
        logger.info("Browser automation manager stopped")
        return True

# Define a simple agent class
class Agent:
    """
    Simple agent class for testing.
    
    This class simulates an agent that uses browser automation.
    """
    
    def __init__(self, name, browser_manager):
        """
        Initialize the agent.
        
        Args:
            name (str): Agent name
            browser_manager (SimpleBrowserAutomationManager): Browser automation manager
        """
        self.name = name
        self.browser_manager = browser_manager
        logger.info(f"Agent {name} initialized")
    
    async def execute_task(self, task):
        """
        Execute a task.
        
        Args:
            task (str): Task to execute
            
        Returns:
            dict: Task result
        """
        logger.info(f"Agent {self.name} executing task: {task}")
        
        # Parse task
        if task.startswith("browse:"):
            url = task.split(":", 1)[1].strip()
            return await self.browse(url)
        elif task.startswith("search:"):
            query = task.split(":", 1)[1].strip()
            return await self.search(query)
        elif task.startswith("click:"):
            text = task.split(":", 1)[1].strip()
            return await self.click(text)
        elif task.startswith("type:"):
            text = task.split(":", 1)[1].strip()
            return await self.type(text)
        elif task == "screenshot":
            return await self.screenshot()
        elif task == "health":
            return await self.health_check()
        else:
            return {"error": f"Unknown task: {task}"}
    
    async def browse(self, url):
        """
        Browse to a URL.
        
        Args:
            url (str): URL to browse to
            
        Returns:
            dict: Command result
        """
        logger.info(f"Agent {self.name} browsing to {url}")
        return await self.browser_manager.execute_command(f"Browse to {url}")
    
    async def search(self, query):
        """
        Search for a query.
        
        Args:
            query (str): Query to search for
            
        Returns:
            dict: Command result
        """
        logger.info(f"Agent {self.name} searching for {query}")
        
        # First browse to Google
        await self.browser_manager.execute_command("Browse to https://www.google.com")
        
        # Then type the query
        await self.browser_manager.execute_command(f"Type {query}")
        
        # Then press Enter
        return await self.browser_manager.execute_command("Press Enter")
    
    async def click(self, text):
        """
        Click on text.
        
        Args:
            text (str): Text to click on
            
        Returns:
            dict: Command result
        """
        logger.info(f"Agent {self.name} clicking on {text}")
        return await self.browser_manager.execute_command(f"Click on {text}")
    
    async def type(self, text):
        """
        Type text.
        
        Args:
            text (str): Text to type
            
        Returns:
            dict: Command result
        """
        logger.info(f"Agent {self.name} typing {text}")
        return await self.browser_manager.execute_command(f"Type {text}")
    
    async def screenshot(self):
        """
        Take a screenshot.
        
        Returns:
            dict: Command result
        """
        logger.info(f"Agent {self.name} taking screenshot")
        return await self.browser_manager.execute_command("Take a screenshot")
    
    async def health_check(self):
        """
        Perform a health check.
        
        Returns:
            dict: Health check result
        """
        logger.info(f"Agent {self.name} performing health check")
        return await self.browser_manager.health_check()

async def test_agent_integration():
    """Test agent integration with browser automation."""
    print("\nTesting Agent Integration with Browser Automation")
    print("===============================================")
    
    # Create browser automation manager
    browser_manager = SimpleBrowserAutomationManager(provider=AutomationProvider.AUTO)
    
    # Create agents
    gmail_agent = Agent("GmailAgent", browser_manager)
    google_voice_agent = Agent("GoogleVoiceAgent", browser_manager)
    
    # Test Gmail agent
    print("\nTesting Gmail Agent:")
    
    # Browse to Gmail
    result = await gmail_agent.execute_task("browse:https://mail.google.com")
    print(f"Browse to Gmail result: {result}")
    
    # Type email
    result = await gmail_agent.execute_task("type:<EMAIL>")
    print(f"Type email result: {result}")
    
    # Click next
    result = await gmail_agent.execute_task("click:Next")
    print(f"Click next result: {result}")
    
    # Type password (simulated)
    result = await gmail_agent.execute_task("type:********")
    print(f"Type password result: {result}")
    
    # Take screenshot
    result = await gmail_agent.execute_task("screenshot")
    print(f"Screenshot result: {result}")
    
    # Test Google Voice agent
    print("\nTesting Google Voice Agent:")
    
    # Browse to Google Voice
    result = await google_voice_agent.execute_task("browse:https://voice.google.com")
    print(f"Browse to Google Voice result: {result}")
    
    # Type phone number
    result = await google_voice_agent.execute_task("type:7722089646")
    print(f"Type phone number result: {result}")
    
    # Type message
    result = await google_voice_agent.execute_task("type:This is a test message from the AI agent system")
    print(f"Type message result: {result}")
    
    # Test fallback mechanism
    print("\nTesting Fallback Mechanism:")
    
    # Execute a task that will fail and trigger fallback
    result = await gmail_agent.execute_task("browse:This will fail and trigger fallback")
    print(f"Fallback result: {result}")
    print(f"Active provider after fallback: {browser_manager.active_provider.value}")
    
    # Test health check
    print("\nTesting Health Check:")
    
    # Perform health check
    result = await gmail_agent.execute_task("health")
    print(f"Health check result: {result}")
    
    # Stop browser automation
    await browser_manager.stop()
    
    print("\nAgent integration test completed successfully")

async def main():
    """Main entry point for the script."""
    try:
        await test_agent_integration()
        return 0
    except Exception as e:
        logger.exception(f"Error in main: {e}")
        print(f"Error: {e}")
        return 1

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nTest cancelled")
        sys.exit(0)
