"""
Check UI-TARS Ports

This script checks which ports UI-TARS is using.
"""
import os
import sys
import subprocess
import re
from typing import List, Dict, <PERSON><PERSON>

def get_process_ids(process_name: str) -> List[int]:
    """
    Get the process IDs for a given process name.
    
    Args:
        process_name (str): Name of the process
        
    Returns:
        List[int]: List of process IDs
    """
    try:
        # Run tasklist command
        output = subprocess.check_output(["tasklist", "/FI", f"IMAGENAME eq {process_name}"], text=True)
        
        # Parse the output to extract PIDs
        pids = []
        for line in output.splitlines():
            if process_name in line:
                # Extract PID (second column)
                parts = re.split(r'\s+', line.strip())
                if len(parts) >= 2:
                    try:
                        pid = int(parts[1])
                        pids.append(pid)
                    except ValueError:
                        pass
        
        return pids
    
    except subprocess.CalledProcessError:
        return []

def get_process_ports(pid: int) -> List[Tuple[str, int]]:
    """
    Get the ports used by a process.
    
    Args:
        pid (int): Process ID
        
    Returns:
        List[Tuple[str, int]]: List of (protocol, port) tuples
    """
    try:
        # Run netstat command
        output = subprocess.check_output(["netstat", "-ano"], text=True)
        
        # Parse the output to extract ports
        ports = []
        for line in output.splitlines():
            if str(pid) in line:
                # Extract protocol and port
                parts = re.split(r'\s+', line.strip())
                if len(parts) >= 5:
                    protocol = parts[0]
                    local_address = parts[1]
                    
                    # Extract port from local address
                    if ":" in local_address:
                        port_str = local_address.split(":")[-1]
                        try:
                            port = int(port_str)
                            ports.append((protocol, port))
                        except ValueError:
                            pass
        
        return ports
    
    except subprocess.CalledProcessError:
        return []

def main():
    """Main entry point for the script."""
    print("Checking UI-TARS Ports")
    print("=====================")
    print()
    
    # Get UI-TARS process IDs
    process_name = "UI-TARS.exe"
    pids = get_process_ids(process_name)
    
    if not pids:
        print(f"No {process_name} processes found.")
        return
    
    print(f"Found {len(pids)} {process_name} processes:")
    for pid in pids:
        print(f"- PID: {pid}")
    
    print()
    print("Checking ports used by UI-TARS processes:")
    print()
    
    # Check ports for each process
    found_ports = False
    for pid in pids:
        ports = get_process_ports(pid)
        
        if ports:
            found_ports = True
            print(f"PID {pid} is using the following ports:")
            for protocol, port in ports:
                print(f"- {protocol}: {port}")
            print()
    
    if not found_ports:
        print("No ports found for UI-TARS processes.")
        print("This could mean that UI-TARS is not listening on any ports.")
        print("Try starting UI-TARS and enabling the API server in the settings.")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
