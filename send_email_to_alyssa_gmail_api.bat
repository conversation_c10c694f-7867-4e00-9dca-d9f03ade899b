@echo off
echo Send Email to Alyssa using Gmail API
echo ==================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Check if Google API libraries are installed
python -c "import google.oauth2" >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing Google API libraries...
    pip install google-auth google-auth-oauthlib google-auth-httplib2 google-api-python-client
)

REM Ask for credentials file
echo.
echo Enter path to credentials.json file:
set /p CREDENTIALS_FILE=""

if "%CREDENTIALS_FILE%"=="" (
    echo No credentials file specified. Using default paths...
    
    if exist "C:\Users\<USER>\Downloads\client_secret_82709489199-u97dcdct36ibaji62fffudfda81n07rm.apps.googleusercontent.com.json" (
        set CREDENTIALS_FILE=C:\Users\<USER>\Downloads\client_secret_82709489199-u97dcdct36ibaji62fffudfda81n07rm.apps.googleusercontent.com.json
    ) else if exist "C:\Users\<USER>\Downloads\client_secret_366167383952-v72gh5b189hnfl9qhian78ouuqgv9bdc.json" (
        set CREDENTIALS_FILE=C:\Users\<USER>\Downloads\client_secret_366167383952-v72gh5b189hnfl9qhian78ouuqgv9bdc.json
    ) else (
        echo No credentials file found. Please specify the path to credentials.json file.
        exit /b 1
    )
)

REM Ask for sender email
echo.
echo Enter sender email address (default: <EMAIL>):
set /p SENDER=""
if "%SENDER%"=="" set SENDER=<EMAIL>

REM Ask for recipient
echo.
echo Enter recipient email address (default: <EMAIL>):
set /p RECIPIENT=""
if "%RECIPIENT%"=="" set RECIPIENT=<EMAIL>

REM Run the script
echo.
echo Running script to send email...
echo.

set COMMAND=python send_email_to_alyssa_gmail_api.py --credentials "%CREDENTIALS_FILE%" --sender "%SENDER%" --recipient "%RECIPIENT%" --debug

echo Executing: %COMMAND%
echo.

%COMMAND%

echo.
if %errorlevel% equ 0 (
    echo Email sent successfully!
) else (
    echo Failed to send email. Please check the error messages above.
)

echo.
pause
