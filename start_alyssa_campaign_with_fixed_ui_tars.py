"""
Start Alyssa's Insurance Drip Campaign with Fixed UI-TARS

This script starts the insurance drip campaign for Alyssa Chirinos using the fixed UI-TARS configuration.
"""
import os
import sys
import json
import logging
import asyncio
import argparse
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("start_alyssa_campaign.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("start_alyssa_campaign")

# Import UI-TARS Gmail integration
try:
    from ui_tars_gmail_integration import UITarsGmailIntegration
    UI_TARS_GMAIL_AVAILABLE = True
except ImportError:
    logger.warning("UI-TARS Gmail integration not found. Running without Gmail integration.")
    UI_TARS_GMAIL_AVAILABLE = False
    UITarsGmailIntegration = None

async def send_email_to_alyssa(ui_tars_config_path, ui_tars_installation_path=None):
    """
    Send an email to Alyssa using UI-TARS Gmail integration.
    
    Args:
        ui_tars_config_path (str): Path to UI-TARS configuration file
        ui_tars_installation_path (str, optional): Path to UI-TARS installation
        
    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    if not UI_TARS_GMAIL_AVAILABLE:
        logger.error("UI-TARS Gmail integration not available")
        return False
    
    logger.info("Sending email to Alyssa using UI-TARS Gmail integration")
    
    # Client information
    client_info = {
        "name": "Alyssa Chirinos",
        "email": "<EMAIL>",  # Using test email for now
        "phone": "**********",
        "insurance_type": "IUL",
        "budget": "$100/month"
    }
    
    # Email content
    email_account = "<EMAIL>"
    password = "GodisSoGood!777"
    subject = f"Insurance Options for {client_info['name']}"
    body = f"""Hello {client_info['name'].split()[0]},

I hope this email finds you well. My name is Sandra from Flo Faction Insurance, and I'm reaching out regarding your interest in {client_info['insurance_type']} insurance.

With your budget of {client_info['budget']}, we can find a plan that provides the coverage you need without breaking the bank. I'd be happy to walk you through the options and help you make an informed decision.

Please feel free to call or text me at ************, or you can reply to this email.

You can also schedule a time to talk using my calendar link: https://calendly.com/flofaction-insurance/30min

I'm here to help!

Best regards,
Sandra
Flo Faction Insurance
************
<EMAIL>
"""
    
    try:
        # Create UI-TARS Gmail integration
        integration = UITarsGmailIntegration(
            browser_type="chrome",
            ui_tars_api_url="http://localhost:8080",
            ui_tars_model_name="UI-TARS-1.5-7B",
            ui_tars_installation_path=ui_tars_installation_path
        )
        
        # Initialize
        initialized = await integration.initialize()
        if not initialized:
            logger.error("Failed to initialize UI-TARS Gmail integration")
            return False
        
        try:
            # Send email
            result = await integration.send_email(
                email_account=email_account,
                password=password,
                to_email=client_info["email"],
                subject=subject,
                body=body
            )
            
            if result["success"]:
                logger.info("Email sent successfully")
                return True
            else:
                logger.error(f"Failed to send email: {result['error']}")
                return False
        
        finally:
            # Shut down
            await integration.shutdown()
    
    except Exception as e:
        logger.exception(f"Error sending email: {e}")
        return False

async def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Start Alyssa's Insurance Drip Campaign with Fixed UI-TARS")
    parser.add_argument("--config", help="Path to UI-TARS configuration file")
    parser.add_argument("--ui-tars-path", help="Path to UI-TARS installation")
    
    args = parser.parse_args()
    
    # Find UI-TARS configuration
    ui_tars_config_path = args.config
    if not ui_tars_config_path:
        # Try to find the configuration file
        possible_paths = [
            "ui_tars_config.json",
            os.path.join(tempfile.gettempdir(), "ui_tars_browser_fix_*", "ui_tars_config.json")
        ]
        
        for path_pattern in possible_paths:
            for path in Path().glob(path_pattern):
                if os.path.exists(path):
                    ui_tars_config_path = str(path)
                    break
            
            if ui_tars_config_path:
                break
    
    if not ui_tars_config_path:
        logger.error("UI-TARS configuration file not found. Please run the fix script first.")
        return 1
    
    # Send email to Alyssa
    success = await send_email_to_alyssa(ui_tars_config_path, args.ui_tars_path)
    
    if success:
        print("\nEmail sent successfully to Alyssa!")
        return 0
    else:
        print("\nFailed to send email to Alyssa. Check the log for details.")
        return 1

if __name__ == "__main__":
    asyncio.run(main())
