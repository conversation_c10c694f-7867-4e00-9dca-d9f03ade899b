"""
Test script to verify that the system can be initialized.
"""
import sys
import os
import asyncio
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

from core.agent_manager import Agent<PERSON>anager
from core.state_manager import StateManager
from llm.llm_router import <PERSON><PERSON><PERSON>er
import config

async def test_state_manager():
    """Test that the state manager can be initialized."""
    print("Testing state manager initialization...")
    state_manager = StateManager()
    await state_manager.initialize()
    print("State manager initialized successfully")
    
    # Test state operations
    await state_manager.update_state("test", "key", "value")
    value = await state_manager.get_state("test", "key")
    assert value == "value", f"Expected 'value', got {value}"
    print("State manager operations working correctly")
    
    await state_manager.close()
    print("State manager closed successfully")
    return True

async def test_llm_router():
    """Test that the LLM router can be initialized."""
    print("Testing LLM router initialization...")
    llm_router = LLMRouter()
    await llm_router.initialize()
    print("LLM router initialized successfully")
    
    # Get available providers
    providers = llm_router.get_available_providers()
    print(f"Available LLM providers: {providers}")
    
    # Get default provider
    default_provider = llm_router.get_default_provider()
    print(f"Default LLM provider: {default_provider}")
    
    return True

async def test_agent_manager():
    """Test that the agent manager can be initialized."""
    print("Testing agent manager initialization...")
    state_manager = StateManager()
    await state_manager.initialize()
    
    shutdown_event = asyncio.Event()
    agent_manager = AgentManager(state_manager, shutdown_event)
    
    # Start agent manager
    await agent_manager.start()
    print("Agent manager started successfully")
    
    # Get all agents
    agents = agent_manager.get_all_agents()
    print(f"Loaded agents: {list(agents.keys())}")
    
    # Stop agent manager
    await agent_manager.stop()
    print("Agent manager stopped successfully")
    
    await state_manager.close()
    return True

async def run_tests():
    """Run all tests."""
    tests = [
        test_state_manager,
        test_llm_router,
        test_agent_manager,
    ]
    
    results = []
    for test in tests:
        try:
            result = await test()
            results.append((test.__name__, result))
        except Exception as e:
            print(f"Error in {test.__name__}: {e}")
            results.append((test.__name__, False))
    
    # Print summary
    print("\nTest Results:")
    for name, result in results:
        status = "PASSED" if result else "FAILED"
        print(f"{name}: {status}")
    
    # Return True if all tests passed
    return all(result for _, result in results)

if __name__ == "__main__":
    print(f"Testing Multi-Agent AI System v{config.VERSION}")
    print(f"Python version: {sys.version}")
    print(f"Working directory: {os.getcwd()}")
    print()
    
    # Create necessary directories
    for directory in [config.DATA_DIR, config.LOG_DIR, config.MODELS_DIR]:
        directory.mkdir(exist_ok=True)
        print(f"Created directory: {directory}")
    
    # Run tests
    success = asyncio.run(run_tests())
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)
