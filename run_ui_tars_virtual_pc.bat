@echo off
echo UI-TARS Virtual PC Environment
echo ============================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Ask for UI-TARS path
echo Enter the path to UI-TARS executable (leave empty to auto-detect):
set /p UI_TARS_PATH=""

REM Ask for browser type
echo.
echo Select browser type:
echo 1. Chrome (default)
echo 2. Edge
echo 3. Firefox
echo 4. Brave
echo.
set /p BROWSER_CHOICE="Enter choice (1-4): "

if "%BROWSER_CHOICE%"=="2" (
    set BROWSER_TYPE=edge
) else if "%BROWSER_CHOICE%"=="3" (
    set BROWSER_TYPE=firefox
) else if "%BROWSER_CHOICE%"=="4" (
    set BROWSER_TYPE=brave
) else (
    set BROWSER_TYPE=chrome
)

REM Ask for memory allocation
echo.
echo Enter memory allocation in MB (default: 2048):
set /p MEMORY=""
if "%MEMORY%"=="" set MEMORY=2048

REM Ask for CPU cores
echo.
echo Enter number of CPU cores (default: 2):
set /p CPU=""
if "%CPU%"=="" set CPU=2

REM Ask for advanced options
echo.
echo Enable advanced options? (Y/N, default: Y)
set /p ADVANCED_OPTIONS="Enable advanced options? "

if /i "%ADVANCED_OPTIONS%"=="N" (
    set SANDBOX_OPTION=--no-sandbox
    set DPO_OPTION=--no-dpo
    set DEBUG_OPTION=
) else (
    set SANDBOX_OPTION=
    set DPO_OPTION=
    set DEBUG_OPTION=--debug
)

REM Run the UI-TARS Virtual PC
echo.
echo Starting UI-TARS Virtual PC environment...
echo.

if "%UI_TARS_PATH%"=="" (
    python ui_tars_virtual_pc.py --browser %BROWSER_TYPE% --memory %MEMORY% --cpu %CPU% %SANDBOX_OPTION% %DPO_OPTION% %DEBUG_OPTION%
) else (
    python ui_tars_virtual_pc.py --path "%UI_TARS_PATH%" --browser %BROWSER_TYPE% --memory %MEMORY% --cpu %CPU% %SANDBOX_OPTION% %DPO_OPTION% %DEBUG_OPTION%
)

echo.
if %errorlevel% equ 0 (
    echo UI-TARS Virtual PC environment has been shut down.
) else (
    echo There was an error with the UI-TARS Virtual PC environment.
)

echo.
pause
