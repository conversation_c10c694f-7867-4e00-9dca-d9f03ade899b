{"name": "autonomous_operations_workflow", "description": "Workflow for enabling autonomous operations of all agents", "version": "1.0.0", "triggers": [{"type": "schedule", "schedule": "*/30 * * * *", "description": "Run autonomous operations every 30 minutes"}, {"type": "event", "event": "system_startup", "description": "Triggered when the system starts up"}, {"type": "command", "command": "start_autonomous_mode", "description": "Manually triggered autonomous operations"}], "steps": [{"id": "check_system_status", "agent": "agent_coordinator", "action": "check_system_status", "parameters": {}, "next": {"condition": "result.status === 'healthy'", "true": "identify_tasks", "false": "handle_system_issues"}}, {"id": "handle_system_issues", "agent": "agent_coordinator", "action": "handle_system_issues", "parameters": {"issues": "${previous.result.issues}"}, "next": "identify_tasks"}, {"id": "identify_tasks", "agent": "agent_coordinator", "action": "identify_pending_tasks", "parameters": {"max_tasks": 10}, "next": {"condition": "result.tasks.length > 0", "true": "prioritize_tasks", "false": "identify_proactive_tasks"}}, {"id": "prioritize_tasks", "agent": "agent_coordinator", "action": "prioritize_tasks", "parameters": {"tasks": "${previous.result.tasks}"}, "next": "assign_tasks"}, {"id": "assign_tasks", "agent": "agent_coordinator", "action": "assign_tasks", "parameters": {"prioritized_tasks": "${previous.result.prioritized_tasks}"}, "next": "monitor_execution"}, {"id": "identify_proactive_tasks", "agent": "agent_coordinator", "action": "identify_proactive_tasks", "parameters": {"max_tasks": 5}, "next": {"condition": "result.tasks.length > 0", "true": "prioritize_tasks", "false": "end"}}, {"id": "monitor_execution", "agent": "agent_coordinator", "action": "monitor_task_execution", "parameters": {"assigned_tasks": "${previous.result.assigned_tasks}", "timeout_minutes": 15}, "next": "evaluate_results"}, {"id": "evaluate_results", "agent": "agent_coordinator", "action": "evaluate_task_results", "parameters": {"task_results": "${previous.result.task_results}"}, "next": "update_knowledge"}, {"id": "update_knowledge", "agent": "agent_coordinator", "action": "update_system_knowledge", "parameters": {"task_evaluations": "${previous.result.evaluations}"}, "next": "end"}, {"id": "end", "type": "end"}]}