{"name": "Optimization", "description": "Template for optimization problems", "template": "\nYou are tasked with creating an efficient algorithm for the following optimization problem:\n\n{objective}\n\nRequirements:\n{requirements}\n\nConstraints:\n{constraints}\n\nYour solution should be implemented as a Python function that takes the following inputs:\n{inputs}\n\nThe function should return:\n{outputs}\n\nFocus on creating an efficient and correct solution.\n", "variables": ["objective", "requirements", "constraints", "inputs", "outputs"]}