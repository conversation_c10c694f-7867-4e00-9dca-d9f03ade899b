"""
Test Google Voice Integration.

This script tests the Google Voice integration by sending a test text message.
"""
import os
import sys
import asyncio
import argparse
import logging
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent))

try:
    from core.logger import setup_logger
    from ui_tars.agent.google_voice_automation_agent import GoogleVoiceAutomationAgent
except ImportError as e:
    print(f"Error importing required modules: {e}")
    print("Make sure you're running this script from the project root directory.")
    sys.exit(1)

# Set up logger
logger = setup_logger("test_google_voice_integration")

async def load_config():
    """
    Load configuration from file.
    
    Returns:
        dict: Configuration dictionary
    """
    config_path = Path("ui_tars/config.json")
    if not config_path.exists():
        logger.error(f"Configuration file not found: {config_path}")
        return {}
    
    try:
        import json
        with open(config_path, "r") as f:
            config = json.load(f)
        
        logger.info(f"Configuration loaded from {config_path}")
        return config
    except Exception as e:
        logger.error(f"Error loading configuration: {e}")
        return {}

async def test_google_voice_integration(phone_number, message):
    """
    Test Google Voice integration by sending a test text message.
    
    Args:
        phone_number (str): Recipient phone number
        message (str): Text message content
        
    Returns:
        bool: True if successful, False otherwise
    """
    logger.info("Testing Google Voice integration")
    
    # Load configuration
    config = await load_config()
    if not config:
        logger.error("Failed to load configuration")
        return False
    
    # Extract Google Voice configuration
    google_voice_config = config.get("google_voice", {})
    
    # Create Google Voice agent
    agent = GoogleVoiceAutomationAgent(
        agent_id="google_voice_test_agent",
        api_url="http://localhost:8080",
        api_key=config.get("ui_tars", {}).get("api_key"),
        model_name="UI-TARS-1.5-7B",
        browser_type="chrome",
        auto_start=True,
        auto_restart=True,
        google_voice_url=google_voice_config.get("url", "https://voice.google.com"),
        default_email=google_voice_config.get("default_email"),
        default_password=google_voice_config.get("default_password"),
        default_phone_number=google_voice_config.get("default_phone_number")
    )
    
    # Initialize agent
    logger.info("Initializing Google Voice agent")
    success = await agent.initialize()
    if not success:
        logger.error("Failed to initialize Google Voice agent")
        return False
    
    try:
        # Log in to Google Voice
        logger.info("Logging in to Google Voice")
        login_result = await agent.login_to_google_voice()
        
        if not login_result["success"]:
            logger.error(f"Failed to log in to Google Voice: {login_result.get('error', 'Unknown error')}")
            return False
        
        # Send test text message
        logger.info(f"Sending test text message to {phone_number}")
        send_result = await agent.send_text_message(
            phone_number=phone_number,
            message=message
        )
        
        if not send_result["success"]:
            logger.error(f"Failed to send text message: {send_result.get('error', 'Unknown error')}")
            return False
        
        logger.info("Text message sent successfully")
        
        # Logout
        logger.info("Logging out from Google Voice")
        await agent.logout_from_google_voice()
        
        # Shutdown agent
        logger.info("Shutting down Google Voice agent")
        await agent.shutdown()
        
        return True
    
    except Exception as e:
        logger.exception(f"Error testing Google Voice integration: {e}")
        
        # Try to shutdown agent
        try:
            await agent.shutdown()
        except:
            pass
        
        return False

async def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Test Google Voice Integration")
    parser.add_argument("--phone", type=str, default="7722089646", help="Recipient phone number")
    parser.add_argument("--message", type=str, default="This is a test message sent by the Enhanced UI-TARS Google Voice integration.", help="Text message content")
    
    args = parser.parse_args()
    
    print("Google Voice Integration Test")
    print("============================")
    print()
    
    try:
        # Test Google Voice integration
        success = await test_google_voice_integration(args.phone, args.message)
        
        if success:
            print("\nGoogle Voice integration test successful")
            print(f"Text message sent to {args.phone}")
            return 0
        else:
            print("\nGoogle Voice integration test failed")
            return 1
    
    except Exception as e:
        logger.exception(f"Error in main: {e}")
        print(f"Error: {e}")
        return 1

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nTest cancelled")
        sys.exit(0)
