"""
Test Gmail authentication for multiple accounts.
This script helps you test Gmail authentication for multiple accounts.
"""
import os
import sys
import json
import pickle
from pathlib import Path

try:
    from google.auth.transport.requests import Request
    from google.oauth2.credentials import Credentials
    from google_auth_oauthlib.flow import InstalledAppFlow
    from googleapiclient.discovery import build
    from googleapiclient.errors import HttpError
except ImportError:
    print("Required packages not found. Installing...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", 
                          "google-auth", "google-auth-oauthlib", 
                          "google-auth-httplib2", "google-api-python-client"])
    
    from google.auth.transport.requests import Request
    from google.oauth2.credentials import Credentials
    from google_auth_oauthlib.flow import InstalledAppFlow
    from googleapiclient.discovery import build
    from googleapiclient.errors import HttpError

def load_email_accounts():
    """Load email accounts from configuration file."""
    accounts_config_path = 'config/email_accounts.json'
    if not os.path.exists(accounts_config_path):
        print(f"Error: Email accounts configuration file not found at {accounts_config_path}")
        print("Please make sure the file exists.")
        return []

    with open(accounts_config_path, 'r') as f:
        accounts_config = json.load(f)

    # Combine priority and additional accounts
    all_accounts = []
    for account in accounts_config.get('priority_accounts', []):
        all_accounts.append(account)
    for account in accounts_config.get('additional_accounts', []):
        all_accounts.append(account)

    return all_accounts

def test_gmail_auth(email):
    """
    Test Gmail authentication for a specific account.

    Args:
        email (str): Email address to test
    """
    print(f"\n=== Testing Gmail Authentication for {email} ===")
    
    # Gmail API scopes
    SCOPES = [
        'https://www.googleapis.com/auth/gmail.readonly',
        'https://www.googleapis.com/auth/gmail.send',
        'https://www.googleapis.com/auth/gmail.compose',
        'https://www.googleapis.com/auth/gmail.modify'
    ]
    
    # Create a safe filename from the email address
    safe_email = email.replace("@", "_at_").replace(".", "_dot_")
    credentials_path = f'credentials/gmail_{safe_email}_credentials.json'
    token_path = f'credentials/gmail_{safe_email}_token.pickle'
    
    # Check if credentials file exists
    if not os.path.exists(credentials_path):
        print(f"Error: Credentials file not found at {credentials_path}")
        print("Please run setup_gmail_credentials.py first to set up your credentials.")
        return False
    
    try:
        creds = None
        
        # Load token if it exists
        if os.path.exists(token_path):
            with open(token_path, 'rb') as token:
                creds = pickle.load(token)
        
        # Refresh token if expired
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        # Otherwise, get new credentials
        elif not creds:
            flow = InstalledAppFlow.from_client_secrets_file(credentials_path, SCOPES)
            creds = flow.run_local_server(port=0)
            
            # Save the credentials for the next run
            with open(token_path, 'wb') as token:
                pickle.dump(creds, token)
        
        # Build the service
        service = build('gmail', 'v1', credentials=creds)
        
        # Get user profile
        profile = service.users().getProfile(userId='me').execute()
        user_email = profile.get('emailAddress')
        
        print(f"Successfully authenticated as {user_email}")
        
        # List a few messages to test the connection
        results = service.users().messages().list(userId='me', maxResults=5).execute()
        messages = results.get('messages', [])
        
        if not messages:
            print("No messages found.")
        else:
            print(f"Found {len(messages)} messages.")
        
        return True
    
    except Exception as e:
        print(f"Error testing Gmail authentication: {e}")
        return False

def main():
    """Main entry point."""
    print("=== Test Gmail Authentication ===")
    print("This script will help you test Gmail authentication for multiple accounts.")

    # Load email accounts
    all_accounts = load_email_accounts()
    if not all_accounts:
        print("No email accounts found in the configuration.")
        return

    # Display available accounts
    print("\nAvailable email accounts:")
    for i, account in enumerate(all_accounts):
        print(f"{i+1}. {account['email']} - {account['description']}")

    print(f"{len(all_accounts)+1}. All accounts")

    # Get user selection
    selection = input("\nSelect an account to test (or 'all' for all accounts): ")

    if selection.lower() == 'all' or selection == str(len(all_accounts)+1):
        # Test all accounts
        results = {}
        for account in all_accounts:
            email = account['email']
            results[email] = test_gmail_auth(email)
        
        # Print summary
        print("\n=== Authentication Summary ===")
        for email, success in results.items():
            status = "SUCCESS" if success else "FAILED"
            print(f"{email}: {status}")
        
        return

    try:
        index = int(selection) - 1
        if 0 <= index < len(all_accounts):
            email = all_accounts[index]['email']
            test_gmail_auth(email)
        else:
            print("Invalid selection.")
            return
    except ValueError:
        print("Invalid selection.")
        return

if __name__ == "__main__":
    main()
