"""
LLM Code Generator for AlphaEvolve.

This module provides code generation capabilities using large language models,
enabling AlphaEvolve to generate code for evolutionary optimization.
"""
import asyncio
import json
import logging
import os
import random
from typing import Dict, List, Optional, Any, Union
import uuid
from datetime import datetime

# Add the project root to the Python path
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

from core.logger import setup_logger
from llm.llm_router import LLMRouter

# Set up logger
logger = setup_logger("llm_code_generator")

class LLMCodeGenerator:
    """
    LLM Code Generator for AlphaEvolve.

    This class provides code generation capabilities using large language models,
    enabling AlphaEvolve to generate code for evolutionary optimization.
    """

    def __init__(
        self,
        llm_router: Optional[LLMRouter] = None,
        config: Dict = None,
    ):
        """
        Initialize the LLM Code Generator.

        Args:
            llm_router (LLMRouter, optional): LLM router for code generation
            config (Dict, optional): Configuration
        """
        self.llm_router = llm_router
        self.config = config or {}
        self.initialized = False
        
        # Default models
        self.models = self.config.get("models", ["gemini-pro", "claude-3-sonnet"])
        
        # Generation parameters
        self.temperature = self.config.get("temperature", 0.7)
        self.max_tokens = self.config.get("max_tokens", 2000)
        self.top_p = self.config.get("top_p", 0.95)
        
        # Code generation history
        self.generation_history = []
        
    async def initialize(self):
        """Initialize the LLM Code Generator."""
        logger.info("Initializing LLM Code Generator")
        
        if not self.llm_router:
            from llm.llm_router import LLMRouter
            self.llm_router = LLMRouter()
            await self.llm_router.initialize()
        
        self.initialized = True
        logger.info("LLM Code Generator initialized")
    
    async def generate_code(self, prompt: str, model: str = None) -> str:
        """
        Generate code using a large language model.
        
        Args:
            prompt (str): Prompt for code generation
            model (str, optional): Model to use for generation
            
        Returns:
            str: Generated code
        """
        if not self.initialized:
            await self.initialize()
        
        # Select model
        if model is None:
            model = random.choice(self.models)
        
        try:
            # Add code generation instructions to prompt
            enhanced_prompt = self._enhance_prompt(prompt)
            
            # Generate code
            response = await self.llm_router.generate_text(
                prompt=enhanced_prompt,
                model=model,
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                top_p=self.top_p,
            )
            
            # Extract code from response
            code = self._extract_code(response.get("text", ""))
            
            # Record generation
            self._record_generation(prompt, code, model)
            
            return code
        
        except Exception as e:
            logger.exception(f"Error generating code: {e}")
            raise
    
    def _enhance_prompt(self, prompt: str) -> str:
        """
        Enhance prompt with code generation instructions.
        
        Args:
            prompt (str): Original prompt
            
        Returns:
            str: Enhanced prompt
        """
        # Add code generation instructions
        instructions = """
You are AlphaEvolve, an advanced code generation system designed to create high-quality, efficient algorithms.

IMPORTANT INSTRUCTIONS:
1. Generate ONLY code, no explanations or comments outside the code
2. The code should be complete, self-contained, and ready to execute
3. Focus on correctness, efficiency, and readability
4. Use appropriate data structures and algorithms
5. Include necessary imports
6. Respond ONLY with the code, nothing else

Here is the task:
"""
        
        return f"{instructions}\n\n{prompt}"
    
    def _extract_code(self, response: str) -> str:
        """
        Extract code from LLM response.
        
        Args:
            response (str): LLM response
            
        Returns:
            str: Extracted code
        """
        # Check if response is already just code
        if response.strip().startswith("import ") or response.strip().startswith("def ") or response.strip().startswith("class "):
            return response.strip()
        
        # Extract code from markdown code blocks
        import re
        code_blocks = re.findall(r"```(?:python)?\n(.*?)```", response, re.DOTALL)
        
        if code_blocks:
            # Join multiple code blocks if present
            return "\n\n".join(block.strip() for block in code_blocks)
        
        # If no code blocks found, return the whole response
        return response.strip()
    
    def _record_generation(self, prompt: str, code: str, model: str):
        """
        Record code generation for analysis.
        
        Args:
            prompt (str): Generation prompt
            code (str): Generated code
            model (str): Model used
        """
        generation = {
            "id": str(uuid.uuid4()),
            "prompt": prompt,
            "code": code,
            "model": model,
            "timestamp": datetime.now().isoformat(),
        }
        
        self.generation_history.append(generation)
        
        # Keep only the last 1000 generations
        if len(self.generation_history) > 1000:
            self.generation_history = self.generation_history[-1000:]
    
    async def generate_code_variations(
        self,
        base_code: str,
        prompt: str,
        num_variations: int = 5,
    ) -> List[str]:
        """
        Generate variations of existing code.
        
        Args:
            base_code (str): Base code to create variations from
            prompt (str): Prompt describing the desired variations
            num_variations (int, optional): Number of variations to generate
            
        Returns:
            List[str]: Generated code variations
        """
        variations = []
        
        for i in range(num_variations):
            try:
                # Create variation prompt
                variation_prompt = f"""
{prompt}

Here is the base code to improve:

```python
{base_code}
```

Create variation #{i+1} that improves this code. Focus on:
- Efficiency
- Correctness
- Readability
- Novel approach

Return ONLY the improved code.
"""
                
                # Generate variation
                variation = await self.generate_code(variation_prompt)
                variations.append(variation)
            
            except Exception as e:
                logger.error(f"Error generating variation {i+1}: {e}")
        
        return variations
    
    async def combine_code(self, code1: str, code2: str, prompt: str = None) -> str:
        """
        Combine two code snippets to create a new solution.
        
        Args:
            code1 (str): First code snippet
            code2 (str): Second code snippet
            prompt (str, optional): Prompt describing the combination goal
            
        Returns:
            str: Combined code
        """
        if prompt is None:
            prompt = "Combine the best aspects of both code snippets to create an improved solution."
        
        combination_prompt = f"""
{prompt}

First code snippet:
```python
{code1}
```

Second code snippet:
```python
{code2}
```

Create a new solution that combines the best aspects of both code snippets.
Return ONLY the combined code.
"""
        
        return await self.generate_code(combination_prompt)
    
    async def mutate_code(self, code: str, mutation_type: str = "random") -> str:
        """
        Mutate code to create a new variation.
        
        Args:
            code (str): Code to mutate
            mutation_type (str, optional): Type of mutation
            
        Returns:
            str: Mutated code
        """
        mutation_prompts = {
            "efficiency": "Optimize this code for better performance while maintaining correctness.",
            "readability": "Improve the readability and maintainability of this code.",
            "algorithm": "Change the underlying algorithm while maintaining the same functionality.",
            "structure": "Refactor the code structure while maintaining the same functionality.",
            "random": "Make meaningful improvements to this code in any way you see fit.",
        }
        
        prompt = mutation_prompts.get(mutation_type, mutation_prompts["random"])
        
        mutation_prompt = f"""
{prompt}

Original code:
```python
{code}
```

Return ONLY the improved code.
"""
        
        return await self.generate_code(mutation_prompt)
    
    async def repair_code(self, code: str, error_message: str = None) -> str:
        """
        Repair code that has errors.
        
        Args:
            code (str): Code to repair
            error_message (str, optional): Error message to help with repair
            
        Returns:
            str: Repaired code
        """
        repair_prompt = f"""
Fix the errors in this code:

```python
{code}
```
"""
        
        if error_message:
            repair_prompt += f"\nError message:\n{error_message}\n"
        
        repair_prompt += "\nReturn ONLY the fixed code."
        
        return await self.generate_code(repair_prompt)
    
    async def optimize_code(self, code: str, optimization_goal: str) -> str:
        """
        Optimize code for a specific goal.
        
        Args:
            code (str): Code to optimize
            optimization_goal (str): Optimization goal
            
        Returns:
            str: Optimized code
        """
        optimization_prompt = f"""
Optimize this code for {optimization_goal}:

```python
{code}
```

Return ONLY the optimized code.
"""
        
        return await self.generate_code(optimization_prompt)
