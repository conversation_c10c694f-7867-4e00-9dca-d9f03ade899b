"""
Hugging Face-related methods for the Research Agent.
"""
import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any
import json
import re
import uuid
import hashlib
import os

from core.logger import setup_logger

async def _handle_huggingface_search(self, task: Dict):
    """
    Handle a Hugging Face search task.
    
    Args:
        task (Dict): Task data
    """
    query = task.get("query")
    task_type = task.get("task_type")  # e.g., text-generation, translation
    library = task.get("library")  # e.g., transformers, diffusers
    search_type = task.get("search_type", "models")  # models, datasets
    
    # Check if Hugging Face service is available
    if not self.huggingface_service or not self.huggingface_service.is_enabled():
        raise ValueError("Hugging Face service is not available")
    
    # Perform search based on type
    if search_type == "models":
        search_results = await self.huggingface_service.search_models(query, task_type, library)
    elif search_type == "datasets":
        search_results = await self.huggingface_service.search_datasets(query, task_type)
    else:
        raise ValueError(f"Unsupported search type: {search_type}")
    
    # Check for errors
    if "error" in search_results:
        raise ValueError(f"Hugging Face search error: {search_results['error']}")
    
    # Store search results in task
    task["results"] = search_results
    
    # Add to search history
    search_id = str(uuid.uuid4())
    self.search_history[search_id] = {
        "id": search_id,
        "type": "huggingface",
        "search_type": search_type,
        "query": query,
        "task_type": task_type,
        "library": library,
        "timestamp": datetime.now().isoformat(),
        "results": search_results,
    }
    
    # If requested, analyze top results
    if task.get("analyze_results", False) and search_type == "models":
        # Analyze top model results
        for model in search_results[:3]:  # Limit to top 3
            model_id = model.get("id")
            
            if model_id:
                analysis_task_id = f"TASK-HF-MODEL-{datetime.now().strftime('%Y%m%d%H%M%S')}"
                
                analysis_task = {
                    "task_id": analysis_task_id,
                    "type": "huggingface_model_analysis",
                    "model_id": model_id,
                    "parent_task_id": task.get("task_id"),
                    "created_at": datetime.now().isoformat(),
                    "status": "pending",
                }
                
                # Add task to pending tasks
                pending_tasks = await self.state_manager.get_state("research", "pending_tasks") or {}
                pending_tasks[analysis_task_id] = analysis_task
                await self.state_manager.update_state("research", "pending_tasks", pending_tasks)

async def _handle_huggingface_model_analysis(self, task: Dict):
    """
    Handle a Hugging Face model analysis task.
    
    Args:
        task (Dict): Task data
    """
    model_id = task.get("model_id")
    
    # Check if Hugging Face service is available
    if not self.huggingface_service or not self.huggingface_service.is_enabled():
        raise ValueError("Hugging Face service is not available")
    
    # Get model card
    model_card = await self.huggingface_service.get_model_card(model_id)
    
    # Check for errors
    if "error" in model_card:
        raise ValueError(f"Hugging Face model error: {model_card['error']}")
    
    # Store model card in task
    task["model_card"] = model_card
    
    # Add to Hugging Face models
    model_hash = hashlib.md5(model_id.encode()).hexdigest()
    self.huggingface_models[model_hash] = {
        "id": model_hash,
        "model_id": model_id,
        "card": model_card,
        "analyzed_at": datetime.now().isoformat(),
    }
    
    # Generate model analysis using LLM
    prompt = f"""
    You are an AI researcher analyzing a model from the Hugging Face Hub. Please provide a comprehensive analysis of the following model:
    
    Model ID: {model_id}
    
    Model Card:
    {json.dumps(model_card, indent=2)}
    
    Please include:
    1. A brief overview of the model's purpose and capabilities
    2. Architecture and training details
    3. Performance metrics and benchmarks
    4. Potential use cases
    5. Limitations and ethical considerations
    6. Implementation requirements (hardware, software)
    
    Keep your analysis technical, detailed, and focused on practical applications.
    """
    
    response = await self.llm_router.generate_text(
        prompt=prompt,
        provider=self.llm_provider,
        max_tokens=1200,
        temperature=0.7
    )
    
    # Store analysis in task and model
    analysis = response.get("text", "")
    task["analysis"] = analysis
    self.huggingface_models[model_hash]["analysis"] = analysis
    
    # If this is part of a parent task, update the parent
    parent_task_id = task.get("parent_task_id")
    if parent_task_id:
        pending_tasks = await self.state_manager.get_state("research", "pending_tasks") or {}
        if parent_task_id in pending_tasks:
            parent_task = pending_tasks[parent_task_id]
            if "model_analyses" not in parent_task:
                parent_task["model_analyses"] = []
            
            parent_task["model_analyses"].append({
                "id": model_hash,
                "model_id": model_id,
                "analysis": analysis,
            })
            
            pending_tasks[parent_task_id] = parent_task
            await self.state_manager.update_state("research", "pending_tasks", pending_tasks)

async def _handle_technical_documentation(self, task: Dict):
    """
    Handle a technical documentation task.
    
    Args:
        task (Dict): Task data
    """
    topic = task.get("topic")
    sources = task.get("sources", [])  # List of source IDs (knowledge base, GitHub, Hugging Face)
    format_type = task.get("format", "markdown")  # markdown, html, etc.
    
    # Collect source content
    source_content = []
    
    for source_id in sources:
        # Check knowledge base
        if source_id in self.knowledge_base:
            source = self.knowledge_base[source_id]
            source_content.append({
                "type": source.get("type", "knowledge"),
                "title": source.get("title", ""),
                "content": source.get("content", ""),
                "analysis": source.get("analysis", ""),
            })
        
        # Check GitHub repositories
        elif source_id in self.github_repositories:
            source = self.github_repositories[source_id]
            source_content.append({
                "type": "github_repository",
                "full_name": source.get("full_name", ""),
                "description": source.get("info", {}).get("description", ""),
                "summary": source.get("summary", ""),
            })
        
        # Check Hugging Face models
        elif source_id in self.huggingface_models:
            source = self.huggingface_models[source_id]
            source_content.append({
                "type": "huggingface_model",
                "model_id": source.get("model_id", ""),
                "card": source.get("card", {}),
                "analysis": source.get("analysis", ""),
            })
    
    # Generate documentation using LLM
    prompt = f"""
    You are a technical writer creating documentation on {topic}. Please use the following sources to create comprehensive documentation:
    
    {json.dumps(source_content, indent=2)}
    
    Please create documentation that includes:
    1. Introduction to {topic}
    2. Technical details and architecture
    3. Implementation guidelines
    4. Examples and use cases
    5. Best practices
    6. References
    
    Format the documentation in {format_type} format. Make it technical, accurate, and well-structured.
    """
    
    response = await self.llm_router.generate_text(
        prompt=prompt,
        provider=self.llm_provider,
        max_tokens=2000,
        temperature=0.7
    )
    
    # Store documentation in task
    documentation = response.get("text", "")
    task["documentation"] = documentation
    
    # Add to knowledge base
    doc_id = hashlib.md5(f"{topic}-documentation-{datetime.now().isoformat()}".encode()).hexdigest()
    self.knowledge_base[doc_id] = {
        "id": doc_id,
        "type": "technical_documentation",
        "topic": topic,
        "content": documentation,
        "format": format_type,
        "sources": sources,
        "created_at": datetime.now().isoformat(),
    }
    
    # Return documentation ID
    task["documentation_id"] = doc_id
