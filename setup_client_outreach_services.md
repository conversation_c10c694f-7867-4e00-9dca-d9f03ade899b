# Setting Up Client Outreach Services

This document provides instructions for setting up the necessary services to enable the client outreach functionality in the AI Agent System.

## Overview

The client outreach process requires the following services to be configured:

1. Gmail API for sending emails
2. Twi<PERSON> for sending text messages and making calls
3. ElevenLabs for generating voice messages

## 1. Setting Up Gmail API

### Prerequisites
- A Google account with Gmail (<EMAIL>)
- Google Cloud Platform account

### Steps

1. **Create a Google Cloud Project**:
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project (e.g., "Flo Faction Insurance")
   - Note the project ID

2. **Enable the Gmail API**:
   - In the Google Cloud Console, go to "APIs & Services" > "Library"
   - Search for "Gmail API" and enable it

3. **Create OAuth 2.0 Credentials**:
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "OAuth client ID"
   - Select "Desktop app" as the application type
   - Name the client (e.g., "Flo Faction Insurance Client")
   - Download the credentials JSON file

4. **Save the Credentials**:
   - Create a directory named `credentials` in the project root if it doesn't exist
   - Save the downloaded JSON file as `credentials/gmail_credentials.json`

5. **Create a Token Directory**:
   - Create a directory named `tokens` in the project root if it doesn't exist

## 2. Setting Up Twilio

### Prerequisites
- A Twilio account

### Steps

1. **Sign Up for Twilio**:
   - Go to [Twilio](https://www.twilio.com/) and sign up for an account
   - Verify your email and phone number

2. **Get a Twilio Phone Number**:
   - In the Twilio Console, go to "Phone Numbers" > "Buy a Number"
   - Search for a number with SMS and voice capabilities
   - Purchase the number

3. **Get Your Account SID and Auth Token**:
   - In the Twilio Console, go to the Dashboard
   - Note your Account SID and Auth Token

4. **Update Configuration**:
   - Open `config/communication_services.json`
   - Update the following fields:
     ```json
     "twilio_account_sid": "YOUR_ACCOUNT_SID",
     "twilio_auth_token": "YOUR_AUTH_TOKEN",
     "default_from_number": "YOUR_TWILIO_NUMBER"
     ```

## 3. Setting Up ElevenLabs

### Prerequisites
- An ElevenLabs account

### Steps

1. **Sign Up for ElevenLabs**:
   - Go to [ElevenLabs](https://elevenlabs.io/) and sign up for an account
   - Verify your email

2. **Get Your API Key**:
   - In the ElevenLabs dashboard, go to your profile
   - Copy your API key

3. **Update Configuration**:
   - Open `config/communication_services.json`
   - Update the following field:
     ```json
     "elevenlabs_api_key": "YOUR_API_KEY"
     ```

4. **Choose a Voice**:
   - In the ElevenLabs dashboard, go to "Voices"
   - Choose a female voice for your agent
   - Note the voice ID

5. **Update Voice Configuration**:
   - Open `config/communication_services.json`
   - Update the following field:
     ```json
     "default_voice_id": "YOUR_VOICE_ID"
     ```

## 4. Testing the Services

### Testing Gmail

1. **Run the Gmail Test Script**:
   ```bash
   python test_gmail_service.py
   ```

2. **Follow the Authentication Flow**:
   - The script will open a browser window
   - Log <NAME_EMAIL>
   - Grant the requested permissions
   - The token will be saved in `tokens/gmail_token.json`

### Testing Twilio

1. **Run the Twilio Test Script**:
   ```bash
   python test_twilio_service.py
   ```

2. **Check the Console Output**:
   - The script should successfully connect to Twilio
   - It will send a test message to the specified number

### Testing ElevenLabs

1. **Run the ElevenLabs Test Script**:
   ```bash
   python test_elevenlabs_service.py
   ```

2. **Check the Console Output**:
   - The script should successfully connect to ElevenLabs
   - It will generate a test audio file

## 5. Using the Client Outreach System

Once all services are configured, you can use the client outreach system:

1. **For Interactive Use**:
   ```bash
   new_client_outreach.bat
   ```

2. **For Specific Clients**:
   ```bash
   contact_alyssa_chirinos.bat
   ```

3. **For Programmatic Use**:
   ```python
   from new_client_outreach import NewClientOutreach
   
   async def contact_client():
       outreach = NewClientOutreach()
       await outreach.initialize()
       
       result = await outreach.full_outreach(
           client_name="John Smith",
           email="<EMAIL>",
           phone_number="1234567890",
           insurance_type="Auto",
           estimated_premium="150",
           agent_name="Sandra",
           send_quote=True
       )
       
       print(result)
   
   # Run the async function
   import asyncio
   asyncio.run(contact_client())
   ```

## Troubleshooting

### Gmail Issues

- **Authentication Errors**: Delete the token file and try again
- **Quota Exceeded**: Wait or create a new project
- **Permission Errors**: Ensure the Gmail API is enabled and the correct scopes are requested

### Twilio Issues

- **Authentication Errors**: Check your Account SID and Auth Token
- **Invalid Number Format**: Ensure phone numbers are in E.164 format (+1XXXXXXXXXX)
- **Messaging Service Errors**: Verify your Twilio number has SMS capabilities

### ElevenLabs Issues

- **Authentication Errors**: Check your API key
- **Voice Not Found**: Verify the voice ID exists in your account
- **Rate Limiting**: Check your usage limits in the ElevenLabs dashboard

## Additional Resources

- [Gmail API Documentation](https://developers.google.com/gmail/api)
- [Twilio API Documentation](https://www.twilio.com/docs/api)
- [ElevenLabs API Documentation](https://docs.elevenlabs.io/api-reference)
