"""
Start Browser Automation Service.

This script starts the browser automation service and keeps it running.
"""
import os
import sys
import json
import asyncio
import logging
import signal
import time
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

try:
    from services.browser_automation_service import get_browser_automation_service
except ImportError:
    print("Error: Failed to import browser automation service")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("browser_automation_service.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("start_browser_automation")

# Flag to indicate whether the service should be stopped
stop_service = False

def signal_handler(sig, frame):
    """Handle signals to gracefully stop the service."""
    global stop_service
    logger.info(f"Received signal {sig}, stopping service...")
    stop_service = True

async def start_service():
    """Start the browser automation service."""
    logger.info("Starting browser automation service")
    
    # Get service
    service = get_browser_automation_service()
    
    try:
        # Initialize service
        success = await service.initialize()
        if not success:
            logger.error("Failed to initialize browser automation service")
            return False
        
        logger.info("Browser automation service started successfully")
        
        # Register signal handlers
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # Keep service running until stopped
        while not stop_service:
            # Perform health check
            health = await service.health_check()
            
            if health["status"] == "unhealthy":
                logger.warning(f"Health check failed: {health['issues']}")
                
                # Try to repair
                logger.info("Attempting to repair")
                repair_result = await service.auto_repair()
                
                if repair_result["success"]:
                    logger.info(f"Repair successful: {repair_result['actions_taken']}")
                else:
                    logger.error(f"Repair failed: {repair_result['message']}")
            
            # Wait for a while
            for _ in range(60):  # Check for stop_service every second
                if stop_service:
                    break
                await asyncio.sleep(1)
        
        # Stop service
        logger.info("Stopping browser automation service")
        await service.stop()
        
        logger.info("Browser automation service stopped")
        return True
    
    except Exception as e:
        logger.exception(f"Error starting browser automation service: {e}")
        
        # Try to stop service
        try:
            await service.stop()
        except:
            pass
        
        return False

def main():
    """Main entry point for the script."""
    try:
        # Run the service
        asyncio.run(start_service())
        return 0
    except Exception as e:
        logger.exception(f"Error in main: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
