@echo off
echo Fix UI-TARS Browser Connection
echo ============================
echo.

REM Kill any existing UI-TARS and browser processes
echo Step 1: Killing any existing UI-TARS and browser processes...
taskkill /F /IM UI-TARS.exe /T 2>nul
taskkill /F /IM chrome.exe /T 2>nul
taskkill /F /IM msedge.exe /T 2>nul
timeout /t 3 >nul

REM Create necessary directories
echo Step 2: Creating necessary directories...
mkdir config 2>nul
mkdir "C:\Users\<USER>\AppData\Local\UI-TARS\browser_data" 2>nul

REM Create optimized UI-TARS configuration
echo Step 3: Creating optimized UI-TARS configuration...
echo {> config\ui_tars_config.json
echo   "ui_tars": {>> config\ui_tars_config.json
echo     "version": "1.5",>> config\ui_tars_config.json
echo     "enabled": true,>> config\ui_tars_config.json
echo     "browser": {>> config\ui_tars_config.json
echo       "type": "chrome",>> config\ui_tars_config.json
echo       "executable_path": "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",>> config\ui_tars_config.json
echo       "user_data_dir": "C:\\Users\\<USER>\\AppData\\Local\\UI-TARS\\browser_data",>> config\ui_tars_config.json
echo       "profile_directory": "Default",>> config\ui_tars_config.json
echo       "remote_debugging_port": 9222,>> config\ui_tars_config.json
echo       "detection": {>> config\ui_tars_config.json
echo         "auto_detect": true,>> config\ui_tars_config.json
echo         "fallback_types": ["chrome", "edge", "firefox", "brave"]>> config\ui_tars_config.json
echo       }>> config\ui_tars_config.json
echo     },>> config\ui_tars_config.json
echo     "api": {>> config\ui_tars_config.json
echo       "host": "localhost",>> config\ui_tars_config.json
echo       "port": 8080,>> config\ui_tars_config.json
echo       "timeout": 30,>> config\ui_tars_config.json
echo       "retry_attempts": 3>> config\ui_tars_config.json
echo     },>> config\ui_tars_config.json
echo     "debug": {>> config\ui_tars_config.json
echo       "enabled": true,>> config\ui_tars_config.json
echo       "log_level": "debug",>> config\ui_tars_config.json
echo       "log_file": "ui_tars_debug.log">> config\ui_tars_config.json
echo     },>> config\ui_tars_config.json
echo     "sandbox": {>> config\ui_tars_config.json
echo       "enabled": true,>> config\ui_tars_config.json
echo       "isolation_level": "high">> config\ui_tars_config.json
echo     },>> config\ui_tars_config.json
echo     "virtual_pc": {>> config\ui_tars_config.json
echo       "enabled": true,>> config\ui_tars_config.json
echo       "memory_mb": 2048,>> config\ui_tars_config.json
echo       "cpu_cores": 2>> config\ui_tars_config.json
echo     },>> config\ui_tars_config.json
echo     "dpo": {>> config\ui_tars_config.json
echo       "enabled": true,>> config\ui_tars_config.json
echo       "preference_model": "default">> config\ui_tars_config.json
echo     }>> config\ui_tars_config.json
echo   }>> config\ui_tars_config.json
echo }>> config\ui_tars_config.json

REM Start Chrome with remote debugging
echo Step 4: Starting Chrome with remote debugging...
start "" "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222 --user-data-dir="C:\Users\<USER>\AppData\Local\UI-TARS\browser_data" --no-first-run --no-default-browser-check --disable-extensions --disable-component-extensions-with-background-pages --disable-background-networking --disable-client-side-phishing-detection --disable-sync --metrics-recording-only --disable-default-apps --no-default-browser-check --no-first-run --disable-backgrounding-occluded-windows --disable-renderer-backgrounding --disable-background-timer-throttling about:blank

echo Waiting for Chrome to start...
timeout /t 5 >nul

REM Verify Chrome is running with remote debugging
echo Step 5: Verifying Chrome is running with remote debugging...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:9222/json/version' -UseBasicParsing; if ($response.StatusCode -eq 200) { Write-Host 'Chrome remote debugging is working!' } else { Write-Host 'Chrome remote debugging is not working!' } } catch { Write-Host 'Chrome remote debugging is not working!' }"

REM Start UI-TARS with enhanced configuration
echo Step 6: Starting UI-TARS with enhanced configuration...
start "" "C:\Users\<USER>\AppData\Local\UI-TARS\UI-TARS.exe" --config "%CD%\config\ui_tars_config.json" --debug

echo Waiting for UI-TARS to start...
timeout /t 10 >nul

REM Verify UI-TARS API is running
echo Step 7: Verifying UI-TARS API is running...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8080/v1/models' -UseBasicParsing; if ($response.StatusCode -eq 200) { Write-Host 'UI-TARS API is running!' } else { Write-Host 'UI-TARS API is not running!' } } catch { Write-Host 'UI-TARS API is not running!' }"

echo.
echo Browser connection fix completed.
echo.
echo If you see 'Chrome remote debugging is working!' and 'UI-TARS API is running!',
echo then UI-TARS is properly connected to the browser.
echo.
echo Press any key to continue...
pause >nul
