{"response_time_target": 30, "facebook": {"enabled": true, "app_id": "", "app_secret": "", "access_token": "", "page_id": "", "webhook_secret": ""}, "instagram": {"enabled": true, "app_id": "", "app_secret": "", "access_token": "", "business_account_id": "", "webhook_secret": ""}, "tiktok": {"enabled": true, "app_id": "", "app_secret": "", "access_token": "", "webhook_secret": ""}, "website": {"enabled": true, "form_endpoint": "/api/insurance-lead", "webhook_secret": ""}, "booking_links": {"default": "https://calendly.com/flofaction-insurance/15min", "auto": "https://calendly.com/flofaction-insurance/auto-insurance-consultation", "home": "https://calendly.com/flofaction-insurance/home-insurance-consultation", "life": "https://calendly.com/flofaction-insurance/life-insurance-consultation", "health": "https://calendly.com/flofaction-insurance/health-insurance-consultation", "business": "https://calendly.com/flofaction-insurance/business-insurance-consultation", "renters": "https://calendly.com/flofaction-insurance/renters-insurance-consultation", "umbrella": "https://calendly.com/flofaction-insurance/umbrella-insurance-consultation", "flood": "https://calendly.com/flofaction-insurance/flood-insurance-consultation", "pet": "https://calendly.com/flofaction-insurance/pet-insurance-consultation"}, "response_templates": {"greeting": "Hi {first_name}, thanks for reaching out! Can I ask what kind of insurance you're looking for?", "qualification": "To help you better, could you tell me what specific type of insurance you're interested in? (e.g., auto, home, life, health)", "qualified": "Great! I'd be happy to help you with {insurance_type} insurance. Let's get you scheduled – here's a link: {booking_link}", "booking": "I'd be happy to help with your {insurance_type} insurance needs. Let's get you scheduled – here's a link to book a time with us: {booking_link}", "booked": "Excellent! I've confirmed your appointment. Looking forward to discussing your {insurance_type} insurance needs. If you need to reschedule, just let me know.", "error": "Having trouble at the moment – I've flagged this and someone will follow up with you shortly!", "follow_up": "Just checking in about your {insurance_type} insurance inquiry. Do you have any questions I can help with?", "info_auto": "For auto insurance, we offer comprehensive coverage including liability, collision, comprehensive, uninsured motorist, and more. We work with multiple carriers to find you the best rates.", "info_home": "Our home insurance policies cover your dwelling, personal property, liability, and additional living expenses. We can customize coverage to fit your specific needs.", "info_life": "We offer term life, whole life, and universal life insurance policies to protect your loved ones. Our agents can help determine the right coverage amount for your situation.", "info_health": "Our health insurance options include individual plans, family coverage, and Medicare supplements. We can help you navigate the complexities of health insurance.", "info_business": "Our business insurance solutions include general liability, professional liability, workers' compensation, commercial property, and more.", "info_renters": "Renters insurance protects your personal belongings and provides liability coverage. It's affordable and essential for anyone renting an apartment or home.", "info_umbrella": "Umbrella insurance provides additional liability coverage beyond your auto and home policies, protecting your assets from major claims.", "info_flood": "Flood insurance covers damage caused by flooding, which is typically not covered by standard homeowners policies. It's essential for properties in flood-prone areas.", "info_pet": "Pet insurance helps cover veterinary expenses for your furry family members. We offer plans that cover accidents, illnesses, and routine care."}, "escalation": {"max_retries": 1, "escalation_email": "<EMAIL>", "escalation_phone": "", "escalation_message": "This lead requires human attention due to {reason}."}, "security": {"fraud_detection_enabled": true, "fraud_keywords": ["scam", "hack", "illegal", "free money", "quick cash", "get rich", "investment opportunity"], "toxic_content_detection": true, "toxic_content_threshold": 0.7}, "logging": {"format": "[{timestamp}] | [{channel}] | [{user_id}] | [{summary}] | [{status}]", "log_level": "INFO", "log_file": "logs/lead_agent.log", "max_log_size": 10485760, "backup_count": 5}}