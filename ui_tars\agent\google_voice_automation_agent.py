"""
Google Voice Automation Agent using Enhanced UI-TARS.

This module provides a specialized agent for Google Voice automation using the Enhanced UI-TARS agent.
"""
import os
import sys
import json
import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import uuid
import time
import tempfile
from pathlib import Path

try:
    from ui_tars.agent.enhanced_ui_tars_agent import EnhancedUITarsAgent
    from core.logger import setup_logger
except ImportError:
    # Fallback imports for standalone usage
    from enhanced_ui_tars_agent import EnhancedUITarsAgent

    # Fallback logging setup
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler("google_voice_automation.log")
        ]
    )

    def setup_logger(name):
        return logging.getLogger(name)

# Set up logger
logger = setup_logger("google_voice_automation_agent")

class GoogleVoiceAutomationAgent(EnhancedUITarsAgent):
    """
    Google Voice Automation Agent using Enhanced UI-TARS.

    This agent provides specialized methods for Google Voice automation using UI-TARS.
    """

    def __init__(self,
                 agent_id: str = "google_voice_automation_agent",
                 config: Optional[Dict] = None,
                 state_manager = None,
                 message_queue = None,
                 shutdown_event = None,
                 api_url: Optional[str] = "http://localhost:8080",
                 api_key: Optional[str] = None,
                 model_name: Optional[str] = "UI-TARS-1.5-7B",
                 installation_path: Optional[str] = None,
                 browser_type: str = "chrome",
                 browser_path: Optional[str] = None,
                 remote_debugging_port: int = 9222,
                 auto_start: bool = True,
                 auto_restart: bool = True,
                 llm_router = None,
                 google_voice_url: str = "https://voice.google.com",
                 default_email: Optional[str] = None,
                 default_password: Optional[str] = None,
                 default_phone_number: Optional[str] = None,
                 max_retries: int = 3,
                 retry_delay: int = 5):
        """
        Initialize the Google Voice Automation Agent.

        Args:
            agent_id (str): Agent identifier
            config (Optional[Dict]): Agent configuration
            state_manager: State manager instance
            message_queue: Message queue for agent communication
            shutdown_event: Event to signal agent shutdown
            api_url (Optional[str]): URL of the UI-TARS API
            api_key (Optional[str]): API key for UI-TARS
            model_name (Optional[str]): Name of the model to use
            installation_path (Optional[str]): Path to UI-TARS installation
            browser_type (str): Type of browser to use
            browser_path (Optional[str]): Path to browser executable
            remote_debugging_port (int): Port for browser remote debugging
            auto_start (bool): Whether to automatically start UI-TARS
            auto_restart (bool): Whether to automatically restart UI-TARS on failure
            llm_router: LLM router instance
            google_voice_url (str): URL of Google Voice
            default_email (Optional[str]): Default Google account email address
            default_password (Optional[str]): Default Google account password
            default_phone_number (Optional[str]): Default Google Voice phone number
            max_retries (int): Maximum number of retries
            retry_delay (int): Delay between retries in seconds
        """
        # Initialize base agent
        super().__init__(
            agent_id=agent_id,
            config=config,
            state_manager=state_manager,
            message_queue=message_queue,
            shutdown_event=shutdown_event,
            api_url=api_url,
            api_key=api_key,
            model_name=model_name,
            installation_path=installation_path,
            browser_type=browser_type,
            browser_path=browser_path,
            remote_debugging_port=remote_debugging_port,
            auto_start=auto_start,
            auto_restart=auto_restart,
            llm_router=llm_router
        )

        # Google Voice settings
        self.google_voice_url = google_voice_url
        self.default_email = default_email
        self.default_password = default_password
        self.default_phone_number = default_phone_number
        self.max_retries = max_retries
        self.retry_delay = retry_delay

        # Google Voice state
        self.is_logged_in = False
        self.current_email = None
        self.login_time = None
        self.last_activity_time = None
        self.session_timeout = 3600  # 1 hour

        self.logger.info("Google Voice Automation Agent initialized")

    async def initialize(self):
        """Initialize the Google Voice Automation Agent."""
        self.logger.info("Initializing Google Voice Automation Agent")

        # Initialize base agent
        success = await super().initialize()
        if not success:
            self.logger.error("Failed to initialize base agent")
            return False

        self.logger.info("Google Voice Automation Agent initialized successfully")
        return True

    async def login_to_google_voice(self, email: Optional[str] = None, password: Optional[str] = None) -> Dict:
        """
        Log in to Google Voice.

        Args:
            email (Optional[str]): Google account email address
            password (Optional[str]): Google account password

        Returns:
            Dict: Result of the operation
        """
        email = email or self.default_email
        password = password or self.default_password

        if not email or not password:
            return {"success": False, "error": "Email and password are required"}

        self.logger.info(f"Logging in to Google Voice with email: {email}")

        try:
            # Navigate to Google Voice
            browse_result = await self.browse_website(self.google_voice_url, "navigate to Google Voice login page")

            # Wait for page to load
            await asyncio.sleep(3)

            # Take a screenshot
            screenshot_result = await self.take_screenshot()

            # Analyze the screen
            analysis_result = await self.analyze_screen()

            # Check if already logged in
            if "messages" in str(analysis_result).lower() or "calls" in str(analysis_result).lower():
                self.logger.info("Already logged in to Google Voice")
                self.is_logged_in = True
                self.current_email = email
                self.login_time = datetime.now()
                self.last_activity_time = datetime.now()

                return {
                    "success": True,
                    "message": "Already logged in to Google Voice",
                    "email": email
                }

            # Enter email
            email_command = f"Enter the email address {email} in the email input field and click Next"
            email_result = await self.execute_command(email_command)

            # Wait for password page
            await asyncio.sleep(3)

            # Enter password
            password_command = f"Enter the password in the password field and click Next"
            password_result = await self.execute_command(password_command)

            # Wait for login to complete
            await asyncio.sleep(5)

            # Take a screenshot
            screenshot_result = await self.take_screenshot()

            # Analyze the screen to check if login was successful
            analysis_result = await self.analyze_screen()

            # Check if login was successful
            if "messages" in str(analysis_result).lower() or "calls" in str(analysis_result).lower():
                self.logger.info("Successfully logged in to Google Voice")
                self.is_logged_in = True
                self.current_email = email
                self.login_time = datetime.now()
                self.last_activity_time = datetime.now()

                return {
                    "success": True,
                    "message": "Successfully logged in to Google Voice",
                    "email": email
                }
            else:
                self.logger.error("Failed to log in to Google Voice")
                return {
                    "success": False,
                    "error": "Failed to log in to Google Voice",
                    "analysis": analysis_result
                }

        except Exception as e:
            error_msg = f"Error logging in to Google Voice: {str(e)}"
            self.logger.exception(error_msg)
            return {"success": False, "error": error_msg}

    async def send_text_message(self, phone_number: str, message: str) -> Dict:
        """
        Send a text message using Google Voice.

        Args:
            phone_number (str): Recipient phone number
            message (str): Message content

        Returns:
            Dict: Result of the operation
        """
        if not self.is_logged_in:
            login_result = await self.login_to_google_voice()
            if not login_result["success"]:
                return login_result

        self.logger.info(f"Sending text message to: {phone_number}")

        try:
            # Click on Messages tab if not already there
            messages_command = "Click on the Messages tab or link"
            messages_result = await self.execute_command(messages_command)

            # Wait for messages to load
            await asyncio.sleep(2)

            # Click on New conversation or Start a new conversation button
            new_conversation_command = "Click on the New conversation button or Start a new conversation button"
            new_conversation_result = await self.execute_command(new_conversation_command)

            # Wait for new conversation form to open
            await asyncio.sleep(2)

            # Enter phone number
            phone_command = f"Enter the phone number {phone_number} in the recipient field"
            phone_result = await self.execute_command(phone_command)

            # Wait for recipient to be added
            await asyncio.sleep(2)

            # Click on the message input field
            message_field_command = "Click on the message input field"
            message_field_result = await self.execute_command(message_field_command)

            # Enter message
            message_command = f"Enter the following text in the message field: {message}"
            message_result = await self.execute_command(message_command)

            # Send message
            send_command = "Click the Send button"
            send_result = await self.execute_command(send_command)

            # Wait for confirmation
            await asyncio.sleep(3)

            # Update last activity time
            self.last_activity_time = datetime.now()

            return {
                "success": True,
                "message": "Text message sent successfully",
                "phone_number": phone_number
            }

        except Exception as e:
            error_msg = f"Error sending text message: {str(e)}"
            self.logger.exception(error_msg)
            return {"success": False, "error": error_msg}

    async def make_phone_call(self, phone_number: str) -> Dict:
        """
        Make a phone call using Google Voice.

        Args:
            phone_number (str): Recipient phone number

        Returns:
            Dict: Result of the operation
        """
        if not self.is_logged_in:
            login_result = await self.login_to_google_voice()
            if not login_result["success"]:
                return login_result

        self.logger.info(f"Making phone call to: {phone_number}")

        try:
            # Click on Calls tab
            calls_command = "Click on the Calls tab or link"
            calls_result = await self.execute_command(calls_command)

            # Wait for calls to load
            await asyncio.sleep(2)

            # Click on Dialpad button
            dialpad_command = "Click on the Dialpad button"
            dialpad_result = await self.execute_command(dialpad_command)

            # Wait for dialpad to open
            await asyncio.sleep(2)

            # Enter phone number
            phone_command = f"Enter the phone number {phone_number} in the dialpad"
            phone_result = await self.execute_command(phone_command)

            # Click call button
            call_command = "Click the Call button"
            call_result = await self.execute_command(call_command)

            # Wait for call to connect
            await asyncio.sleep(5)

            # Update last activity time
            self.last_activity_time = datetime.now()

            return {
                "success": True,
                "message": "Phone call initiated successfully",
                "phone_number": phone_number
            }

        except Exception as e:
            error_msg = f"Error making phone call: {str(e)}"
            self.logger.exception(error_msg)
            return {"success": False, "error": error_msg}

    async def check_voicemail(self) -> Dict:
        """
        Check voicemail in Google Voice.

        Returns:
            Dict: Result of the operation
        """
        if not self.is_logged_in:
            login_result = await self.login_to_google_voice()
            if not login_result["success"]:
                return login_result

        self.logger.info("Checking voicemail")

        try:
            # Click on Voicemail tab
            voicemail_command = "Click on the Voicemail tab or link"
            voicemail_result = await self.execute_command(voicemail_command)

            # Wait for voicemail to load
            await asyncio.sleep(3)

            # Analyze the screen to get voicemail list
            analysis_result = await self.analyze_screen()

            # Update last activity time
            self.last_activity_time = datetime.now()

            return {
                "success": True,
                "message": "Voicemail checked successfully",
                "analysis": analysis_result
            }

        except Exception as e:
            error_msg = f"Error checking voicemail: {str(e)}"
            self.logger.exception(error_msg)
            return {"success": False, "error": error_msg}

    async def check_text_messages(self, search_query: Optional[str] = None) -> Dict:
        """
        Check text messages in Google Voice.

        Args:
            search_query (Optional[str]): Search query to filter messages

        Returns:
            Dict: Result of the operation
        """
        if not self.is_logged_in:
            login_result = await self.login_to_google_voice()
            if not login_result["success"]:
                return login_result

        self.logger.info(f"Checking text messages with query: {search_query}")

        try:
            # Click on Messages tab
            messages_command = "Click on the Messages tab or link"
            messages_result = await self.execute_command(messages_command)

            # Wait for messages to load
            await asyncio.sleep(3)

            # Search if query provided
            if search_query:
                search_command = f"Enter the search query '{search_query}' in the search box and press Enter"
                search_result = await self.execute_command(search_command)

                # Wait for search results
                await asyncio.sleep(3)

            # Analyze the screen to get message list
            analysis_result = await self.analyze_screen()

            # Update last activity time
            self.last_activity_time = datetime.now()

            return {
                "success": True,
                "message": "Text messages checked successfully",
                "search_query": search_query,
                "analysis": analysis_result
            }

        except Exception as e:
            error_msg = f"Error checking text messages: {str(e)}"
            self.logger.exception(error_msg)
            return {"success": False, "error": error_msg}

    async def reply_to_text_message(self, contact_name_or_number: str, reply_message: str) -> Dict:
        """
        Reply to a text message in Google Voice.

        Args:
            contact_name_or_number (str): Contact name or phone number
            reply_message (str): Reply message content

        Returns:
            Dict: Result of the operation
        """
        if not self.is_logged_in:
            login_result = await self.login_to_google_voice()
            if not login_result["success"]:
                return login_result

        self.logger.info(f"Replying to text message from: {contact_name_or_number}")

        try:
            # Click on Messages tab
            messages_command = "Click on the Messages tab or link"
            messages_result = await self.execute_command(messages_command)

            # Wait for messages to load
            await asyncio.sleep(3)

            # Click on the conversation with the contact
            contact_command = f"Click on the conversation with {contact_name_or_number}"
            contact_result = await self.execute_command(contact_command)

            # Wait for conversation to open
            await asyncio.sleep(2)

            # Click on the message input field
            message_field_command = "Click on the message input field"
            message_field_result = await self.execute_command(message_field_command)

            # Enter reply message
            message_command = f"Enter the following text in the message field: {reply_message}"
            message_result = await self.execute_command(message_command)

            # Send message
            send_command = "Click the Send button"
            send_result = await self.execute_command(send_command)

            # Wait for confirmation
            await asyncio.sleep(3)

            # Update last activity time
            self.last_activity_time = datetime.now()

            return {
                "success": True,
                "message": "Reply sent successfully",
                "contact": contact_name_or_number
            }

        except Exception as e:
            error_msg = f"Error replying to text message: {str(e)}"
            self.logger.exception(error_msg)
            return {"success": False, "error": error_msg}

    async def logout_from_google_voice(self) -> Dict:
        """
        Log out from Google Voice.

        Returns:
            Dict: Result of the operation
        """
        if not self.is_logged_in:
            return {
                "success": True,
                "message": "Already logged out from Google Voice"
            }

        self.logger.info("Logging out from Google Voice")

        try:
            # Click on profile picture
            profile_command = "Click on the profile picture or icon in the top-right corner"
            profile_result = await self.execute_command(profile_command)

            # Wait for menu to open
            await asyncio.sleep(2)

            # Click sign out
            signout_command = "Click on the Sign out button"
            signout_result = await self.execute_command(signout_command)

            # Wait for logout to complete
            await asyncio.sleep(3)

            # Reset login state
            self.is_logged_in = False
            self.current_email = None
            self.login_time = None
            self.last_activity_time = None

            return {
                "success": True,
                "message": "Successfully logged out from Google Voice"
            }

        except Exception as e:
            error_msg = f"Error logging out from Google Voice: {str(e)}"
            self.logger.exception(error_msg)
            return {"success": False, "error": error_msg}

    async def check_session(self) -> bool:
        """
        Check if the Google Voice session is still valid.

        Returns:
            bool: True if session is valid, False otherwise
        """
        if not self.is_logged_in or not self.last_activity_time:
            return False

        # Check if session has timed out
        elapsed_seconds = (datetime.now() - self.last_activity_time).total_seconds()
        if elapsed_seconds > self.session_timeout:
            self.logger.info(f"Google Voice session timed out after {elapsed_seconds} seconds")
            self.is_logged_in = False
            return False

        return True
