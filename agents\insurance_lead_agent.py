"""
Insurance Lead Agent for omnichannel lead handling.

This agent monitors and responds to leads from multiple channels (Facebook, Instagram, TikTok, website)
and guides them toward scheduling appointments. It provides real-time responses, qualifies leads,
and tracks all interactions.
"""
import asyncio
import json
import logging
import os
import sys
import time
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
import re

# Add parent directory to path to import from core
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.logger import setup_logger
from agents.insurance_agent_communication import InsuranceAgentCommunication
from services.calendar_integration import CalendarIntegration
from services.social_media_service import SocialMediaService

# Set up logger
logger = setup_logger("insurance_lead_agent")

class InsuranceLeadAgent(InsuranceAgentCommunication):
    """
    Insurance Lead Agent for omnichannel lead handling.

    This agent monitors and responds to leads from multiple channels (Facebook, Instagram,
    TikTok, website) and guides them toward scheduling appointments. It provides real-time
    responses, qualifies leads, and tracks all interactions.
    """

    def __init__(
        self,
        agent_id: str,
        config: Dict,
        state_manager,
        message_queue,
        shutdown_event
    ):
        """Initialize the insurance lead agent."""
        super().__init__(agent_id, config, state_manager, message_queue, shutdown_event)

        # Lead agent specific configuration
        self.lead_config = config.get("lead_agent_config", {})
        self.response_time_target = self.lead_config.get("response_time_target", 30)  # seconds

        # Channel configurations
        self.channels = {
            "facebook": self.lead_config.get("facebook", {"enabled": False}),
            "instagram": self.lead_config.get("instagram", {"enabled": False}),
            "tiktok": self.lead_config.get("tiktok", {"enabled": False}),
            "website": self.lead_config.get("website", {"enabled": False})
        }

        # Lead data
        self.leads = {}
        self.interactions = {}
        self.pending_responses = {}

        # Calendly booking links
        self.booking_links = self.lead_config.get("booking_links", {})

        # Response templates
        self.response_templates = self.lead_config.get("response_templates", {})

        # Add lead handling capabilities
        self.capabilities.extend([
            "omnichannel_lead_handling",
            "real_time_response",
            "lead_qualification",
            "appointment_booking",
            "interaction_logging",
            "facebook_integration",
            "instagram_integration",
            "tiktok_integration",
            "website_integration"
        ])

        logger.info(f"Insurance lead agent initialized for agent {agent_id}")

    async def initialize(self):
        """Initialize the insurance lead agent."""
        await super().initialize()

        # Initialize channel connections
        await self._initialize_channels()

        # Load existing leads and interactions
        await self._load_lead_data()

        logger.info("Insurance lead agent initialization complete")

    async def execute_cycle(self):
        """Execute one cycle of the insurance lead agent's logic."""
        await super().execute_cycle()

        try:
            # Check for new leads across all channels
            await self._check_new_leads()

            # Process pending responses
            await self._process_pending_responses()

            # Save lead data
            await self._save_lead_data()

        except Exception as e:
            logger.exception(f"Error in insurance lead agent cycle: {e}")

    async def _initialize_channels(self):
        """Initialize connections to all enabled channels."""
        for channel, config in self.channels.items():
            if config.get("enabled", False):
                try:
                    # Initialize channel-specific connections
                    if channel == "facebook":
                        await self._initialize_facebook()
                    elif channel == "instagram":
                        await self._initialize_instagram()
                    elif channel == "tiktok":
                        await self._initialize_tiktok()
                    elif channel == "website":
                        await self._initialize_website()

                    logger.info(f"Initialized {channel} channel")
                except Exception as e:
                    logger.exception(f"Error initializing {channel} channel: {e}")
                    self.channels[channel]["status"] = "error"
                    self.channels[channel]["error"] = str(e)

    async def _load_lead_data(self):
        """Load existing lead data from state manager."""
        try:
            # Load leads
            leads = await self.state_manager.get_state("insurance_lead", "leads")
            if leads:
                self.leads = leads

            # Load interactions
            interactions = await self.state_manager.get_state("insurance_lead", "interactions")
            if interactions:
                self.interactions = interactions

            logger.info(f"Loaded {len(self.leads)} leads and {len(self.interactions)} interactions")
        except Exception as e:
            logger.exception(f"Error loading lead data: {e}")

    async def _save_lead_data(self):
        """Save lead data to state manager."""
        try:
            # Save leads
            await self.state_manager.update_state("insurance_lead", "leads", self.leads)

            # Save interactions
            await self.state_manager.update_state("insurance_lead", "interactions", self.interactions)

            logger.debug(f"Saved {len(self.leads)} leads and {len(self.interactions)} interactions")
        except Exception as e:
            logger.exception(f"Error saving lead data: {e}")

    async def _check_new_leads(self):
        """Check for new leads across all channels."""
        for channel, config in self.channels.items():
            if config.get("enabled", False) and config.get("status", "") != "error":
                try:
                    if channel == "facebook":
                        await self._check_facebook_leads()
                    elif channel == "instagram":
                        await self._check_instagram_leads()
                    elif channel == "tiktok":
                        await self._check_tiktok_leads()
                    elif channel == "website":
                        await self._check_website_leads()
                except Exception as e:
                    logger.exception(f"Error checking {channel} leads: {e}")

    async def _process_pending_responses(self):
        """Process pending responses."""
        current_time = datetime.now()

        for lead_id, response_data in list(self.pending_responses.items()):
            try:
                # Check if response is due
                if current_time >= response_data.get("response_due", current_time):
                    # Send response
                    await self._send_response(lead_id, response_data)

                    # Remove from pending responses
                    del self.pending_responses[lead_id]
            except Exception as e:
                logger.exception(f"Error processing pending response for lead {lead_id}: {e}")

                # Mark as failed after retry
                if response_data.get("retries", 0) >= 1:
                    await self._log_interaction(
                        lead_id,
                        response_data.get("channel"),
                        "error",
                        f"Failed to send response: {str(e)}",
                        "failure"
                    )

                    # Remove from pending responses
                    del self.pending_responses[lead_id]
                else:
                    # Increment retry count
                    response_data["retries"] = response_data.get("retries", 0) + 1

                    # Schedule retry in 30 seconds
                    response_data["response_due"] = current_time + timedelta(seconds=30)

                    self.pending_responses[lead_id] = response_data

    async def handle_lead(self, channel: str, lead_data: Dict) -> Dict:
        """
        Handle a new lead from any channel.

        Args:
            channel (str): Source channel (facebook, instagram, tiktok, website)
            lead_data (Dict): Lead data including message content

        Returns:
            Dict: Response data
        """
        try:
            # Extract lead information
            lead_id = lead_data.get("lead_id") or f"{channel}-{datetime.now().strftime('%Y%m%d%H%M%S')}"
            user_handle = lead_data.get("user_handle", "Unknown")
            message = lead_data.get("message", "")

            # Log the incoming lead
            logger.info(f"Received lead from {channel}: {lead_id} ({user_handle})")

            # Create or update lead record
            if lead_id not in self.leads:
                self.leads[lead_id] = {
                    "lead_id": lead_id,
                    "channel": channel,
                    "user_handle": user_handle,
                    "first_contact": datetime.now().isoformat(),
                    "last_contact": datetime.now().isoformat(),
                    "status": "new",
                    "qualified": False,
                    "insurance_type": None,
                    "appointment_booked": False,
                    "appointment_id": None,
                    "notes": []
                }
            else:
                # Update existing lead
                self.leads[lead_id]["last_contact"] = datetime.now().isoformat()

            # Log the interaction
            await self._log_interaction(
                lead_id,
                channel,
                "incoming",
                message,
                "received"
            )

            # Generate response
            response = await self._generate_response(lead_id, channel, message)

            # Schedule response
            self.pending_responses[lead_id] = {
                "lead_id": lead_id,
                "channel": channel,
                "response": response,
                "response_due": datetime.now() + timedelta(seconds=min(5, self.response_time_target)),
                "retries": 0
            }

            return {
                "success": True,
                "lead_id": lead_id,
                "response_scheduled": True
            }

        except Exception as e:
            logger.exception(f"Error handling lead from {channel}: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _generate_response(self, lead_id: str, channel: str, message: str) -> str:
        """
        Generate a response for a lead.

        Args:
            lead_id (str): Lead ID
            channel (str): Source channel
            message (str): Incoming message

        Returns:
            str: Response message
        """
        lead = self.leads.get(lead_id, {})
        lead_status = lead.get("status", "new")
        insurance_type = lead.get("insurance_type")

        # Determine if this is a new lead or existing conversation
        if lead_status == "new":
            # New lead greeting
            response = await self._generate_greeting(lead_id, channel)
        elif not lead.get("qualified", False):
            # Lead needs qualification
            response = await self._generate_qualification_response(lead_id, channel, message)
        elif not lead.get("appointment_booked", False):
            # Lead needs appointment
            response = await self._generate_booking_response(lead_id, channel, message)
        else:
            # General response for qualified lead with appointment
            response = await self._generate_general_response(lead_id, channel, message)

        return response

    async def _generate_greeting(self, lead_id: str, channel: str) -> str:
        """Generate greeting for new lead."""
        lead = self.leads.get(lead_id, {})
        user_handle = lead.get("user_handle", "there")

        # Extract first name if possible
        first_name = user_handle.split()[0] if " " in user_handle else user_handle

        # Get greeting template
        template = self.response_templates.get("greeting", "Hi {first_name}, thanks for reaching out! Can I ask what kind of insurance you're looking for?")

        # Format template
        response = template.format(first_name=first_name)

        # Update lead status
        lead["status"] = "greeted"
        self.leads[lead_id] = lead

        return response

    async def _generate_qualification_response(self, lead_id: str, channel: str, message: str) -> str:
        """Generate qualification response."""
        lead = self.leads.get(lead_id, {})
        user_handle = lead.get("user_handle", "there")
        first_name = user_handle.split()[0] if " " in user_handle else user_handle

        # Try to identify insurance type from message
        insurance_type = await self._identify_insurance_type(message)

        if insurance_type:
            # Update lead with identified insurance type
            lead["insurance_type"] = insurance_type
            lead["qualified"] = True
            lead["status"] = "qualified"
            self.leads[lead_id] = lead

            # Get qualified template
            template = self.response_templates.get("qualified", "Great! I'd be happy to help you with {insurance_type} insurance. Let's get you scheduled – here's a link: {booking_link}")

            # Get appropriate booking link
            booking_link = self._get_booking_link(insurance_type)

            # Format template
            response = template.format(
                first_name=first_name,
                insurance_type=insurance_type,
                booking_link=booking_link
            )
        else:
            # Still need qualification
            template = self.response_templates.get("qualification", "To help you better, could you tell me what specific type of insurance you're interested in? (e.g., auto, home, life, health)")

            # Format template
            response = template.format(first_name=first_name)

            # Update lead status
            lead["status"] = "qualifying"
            self.leads[lead_id] = lead

        return response

    async def _generate_booking_response(self, lead_id: str, channel: str, message: str) -> str:
        """Generate booking response."""
        lead = self.leads.get(lead_id, {})
        user_handle = lead.get("user_handle", "there")
        first_name = user_handle.split()[0] if " " in user_handle else user_handle
        insurance_type = lead.get("insurance_type", "insurance")

        # Check if message indicates booking confirmation
        if self._is_booking_confirmation(message):
            # Update lead with booking confirmation
            lead["appointment_booked"] = True
            lead["status"] = "booked"
            self.leads[lead_id] = lead

            # Get booked template
            template = self.response_templates.get("booked", "Excellent! I've confirmed your appointment. Looking forward to discussing your {insurance_type} insurance needs. If you need to reschedule, just let me know.")

            # Format template
            response = template.format(
                first_name=first_name,
                insurance_type=insurance_type
            )
        else:
            # Still need booking
            template = self.response_templates.get("booking", "I'd be happy to help with your {insurance_type} insurance needs. Let's get you scheduled – here's a link to book a time with us: {booking_link}")

            # Get appropriate booking link
            booking_link = self._get_booking_link(insurance_type)

            # Format template
            response = template.format(
                first_name=first_name,
                insurance_type=insurance_type,
                booking_link=booking_link
            )

        return response

    async def _generate_general_response(self, lead_id: str, channel: str, message: str) -> str:
        """Generate general response for qualified lead with appointment."""
        lead = self.leads.get(lead_id, {})
        user_handle = lead.get("user_handle", "there")
        first_name = user_handle.split()[0] if " " in user_handle else user_handle
        insurance_type = lead.get("insurance_type", "insurance")

        # Generate response using LLM
        prompt = f"""
        You are an insurance assistant for Flo Faction Insurance. Please respond to the following message:

        Customer: {first_name}
        Insurance Type: {insurance_type}
        Status: Has appointment booked

        Message: {message}

        Please provide a helpful, accurate, and professional response. Keep it brief and friendly.
        """

        try:
            response = await self.llm_router.generate_text(
                prompt=prompt,
                provider=self.llm_provider,
                max_tokens=200,
                temperature=0.7
            )

            return response.get("text", "Thanks for your message. I'll have someone from our team follow up with you shortly.")
        except Exception as e:
            logger.exception(f"Error generating general response: {e}")
            return "Thanks for your message. I'll have someone from our team follow up with you shortly."

    async def _identify_insurance_type(self, message: str) -> Optional[str]:
        """
        Identify insurance type from message.

        Args:
            message (str): Message to analyze

        Returns:
            Optional[str]: Identified insurance type or None
        """
        # Convert message to lowercase for easier matching
        message_lower = message.lower()

        # Define insurance type keywords
        insurance_types = {
            "auto": ["auto", "car", "vehicle", "truck", "motorcycle", "driving"],
            "home": ["home", "house", "property", "apartment", "condo", "homeowner"],
            "life": ["life", "death", "beneficiary", "term", "whole life"],
            "health": ["health", "medical", "doctor", "hospital", "healthcare"],
            "business": ["business", "commercial", "liability", "professional", "company"],
            "renters": ["rent", "renter", "tenant", "apartment"],
            "umbrella": ["umbrella", "excess", "additional coverage"],
            "flood": ["flood", "water damage", "flooding"],
            "pet": ["pet", "dog", "cat", "animal"]
        }

        # Check for insurance type mentions
        for insurance_type, keywords in insurance_types.items():
            for keyword in keywords:
                if keyword in message_lower:
                    return insurance_type

        # If no specific type is found, try to use LLM to identify
        try:
            prompt = f"""
            Identify the type of insurance the customer is interested in from their message.
            Return ONLY the insurance type (auto, home, life, health, business, renters, umbrella, flood, pet) or "unknown" if unclear.

            Customer message: {message}

            Insurance type:
            """

            response = await self.llm_router.generate_text(
                prompt=prompt,
                provider=self.llm_provider,
                max_tokens=10,
                temperature=0.3
            )

            identified_type = response.get("text", "").strip().lower()

            # Check if the response is a valid insurance type
            for insurance_type in insurance_types.keys():
                if insurance_type in identified_type:
                    return insurance_type

            return None
        except Exception as e:
            logger.exception(f"Error identifying insurance type with LLM: {e}")
            return None

    def _get_booking_link(self, insurance_type: str) -> str:
        """
        Get appropriate booking link for insurance type.

        Args:
            insurance_type (str): Insurance type

        Returns:
            str: Booking link
        """
        # Get type-specific booking link if available
        if insurance_type in self.booking_links:
            return self.booking_links[insurance_type]

        # Fall back to default booking link
        return self.booking_links.get("default", "https://calendly.com/flofaction-insurance/15min")

    def _is_booking_confirmation(self, message: str) -> bool:
        """
        Check if message indicates booking confirmation.

        Args:
            message (str): Message to analyze

        Returns:
            bool: True if message indicates booking confirmation
        """
        # Convert message to lowercase for easier matching
        message_lower = message.lower()

        # Define confirmation keywords
        confirmation_keywords = [
            "booked", "scheduled", "appointment", "confirmed", "set up",
            "reserved", "meeting", "call", "talk", "speak", "discuss",
            "yes", "sure", "definitely", "absolutely", "ok", "okay"
        ]

        # Check for confirmation keywords
        for keyword in confirmation_keywords:
            if keyword in message_lower:
                return True

        return False

    async def _send_response(self, lead_id: str, response_data: Dict) -> bool:
        """
        Send response to lead.

        Args:
            lead_id (str): Lead ID
            response_data (Dict): Response data

        Returns:
            bool: Success status
        """
        channel = response_data.get("channel")
        response = response_data.get("response", "")

        try:
            # Send response through appropriate channel
            if channel == "facebook":
                success = await self._send_facebook_response(lead_id, response)
            elif channel == "instagram":
                success = await self._send_instagram_response(lead_id, response)
            elif channel == "tiktok":
                success = await self._send_tiktok_response(lead_id, response)
            elif channel == "website":
                success = await self._send_website_response(lead_id, response)
            else:
                logger.error(f"Unknown channel: {channel}")
                return False

            if success:
                # Log the interaction
                await self._log_interaction(
                    lead_id,
                    channel,
                    "outgoing",
                    response,
                    "success"
                )
                return True
            else:
                # Log failure
                await self._log_interaction(
                    lead_id,
                    channel,
                    "outgoing",
                    response,
                    "failure"
                )
                return False

        except Exception as e:
            logger.exception(f"Error sending response to {channel} lead {lead_id}: {e}")

            # Log error
            await self._log_interaction(
                lead_id,
                channel,
                "outgoing",
                response,
                "failure"
            )

            return False

    async def _log_interaction(
        self,
        lead_id: str,
        channel: str,
        direction: str,
        content: str,
        status: str
    ) -> None:
        """
        Log an interaction with a lead.

        Args:
            lead_id (str): Lead ID
            channel (str): Channel (facebook, instagram, tiktok, website)
            direction (str): Direction (incoming, outgoing)
            content (str): Message content
            status (str): Status (received, success, failure, escalated)
        """
        # Create interaction ID
        interaction_id = f"{lead_id}-{datetime.now().strftime('%Y%m%d%H%M%S')}"

        # Create interaction record
        interaction = {
            "interaction_id": interaction_id,
            "lead_id": lead_id,
            "channel": channel,
            "direction": direction,
            "content": content,
            "status": status,
            "timestamp": datetime.now().isoformat()
        }

        # Add to interactions
        self.interactions[interaction_id] = interaction

        # Log in standard format
        log_message = f"[{datetime.now().strftime('%Y-%m-%d | %H:%M:%S')}] | [{channel}] | [{lead_id}] | [{content[:50]}{'...' if len(content) > 50 else ''}] | [{status}]"

        if status == "failure" or status == "escalated":
            logger.warning(log_message)
        else:
            logger.info(log_message)

    # Channel-specific methods

    async def _initialize_facebook(self):
        """Initialize Facebook integration."""
        config = self.channels.get("facebook", {})

        try:
            # Initialize Facebook API connection
            self.social_media_service = SocialMediaService(
                service_type="facebook",
                config=config
            )

            # Test connection
            status = await self.social_media_service.check_status()

            if status.get("status") == "available":
                self.channels["facebook"]["status"] = "available"
            else:
                self.channels["facebook"]["status"] = "unavailable"
                self.channels["facebook"]["error"] = status.get("error", "Unknown error")

        except Exception as e:
            logger.exception(f"Error initializing Facebook integration: {e}")
            self.channels["facebook"]["status"] = "error"
            self.channels["facebook"]["error"] = str(e)

    async def _initialize_instagram(self):
        """Initialize Instagram integration."""
        config = self.channels.get("instagram", {})

        try:
            # Initialize Instagram API connection
            self.social_media_service = SocialMediaService(
                service_type="instagram",
                config=config
            )

            # Test connection
            status = await self.social_media_service.check_status()

            if status.get("status") == "available":
                self.channels["instagram"]["status"] = "available"
            else:
                self.channels["instagram"]["status"] = "unavailable"
                self.channels["instagram"]["error"] = status.get("error", "Unknown error")

        except Exception as e:
            logger.exception(f"Error initializing Instagram integration: {e}")
            self.channels["instagram"]["status"] = "error"
            self.channels["instagram"]["error"] = str(e)

    async def _initialize_tiktok(self):
        """Initialize TikTok integration."""
        config = self.channels.get("tiktok", {})

        try:
            # Initialize TikTok API connection
            self.social_media_service = SocialMediaService(
                service_type="tiktok",
                config=config
            )

            # Test connection
            status = await self.social_media_service.check_status()

            if status.get("status") == "available":
                self.channels["tiktok"]["status"] = "available"
            else:
                self.channels["tiktok"]["status"] = "unavailable"
                self.channels["tiktok"]["error"] = status.get("error", "Unknown error")

        except Exception as e:
            logger.exception(f"Error initializing TikTok integration: {e}")
            self.channels["tiktok"]["status"] = "error"
            self.channels["tiktok"]["error"] = str(e)

    async def _initialize_website(self):
        """Initialize website integration."""
        config = self.channels.get("website", {})

        try:
            # For website, we might use a webhook or API endpoint
            # This is a placeholder for actual implementation
            self.channels["website"]["status"] = "available"

        except Exception as e:
            logger.exception(f"Error initializing website integration: {e}")
            self.channels["website"]["status"] = "error"
            self.channels["website"]["error"] = str(e)

    async def _check_facebook_leads(self):
        """Check for new Facebook leads."""
        if self.channels["facebook"].get("status") != "available":
            return

        try:
            # Get new messages from Facebook
            messages = await self.social_media_service.get_new_messages("facebook")

            for message in messages:
                # Extract message data
                lead_id = f"facebook-{message.get('sender_id')}"
                user_handle = message.get("sender_name", "Unknown")
                content = message.get("message", "")

                # Handle lead
                await self.handle_lead(
                    "facebook",
                    {
                        "lead_id": lead_id,
                        "user_handle": user_handle,
                        "message": content
                    }
                )

        except Exception as e:
            logger.exception(f"Error checking Facebook leads: {e}")

    async def _check_instagram_leads(self):
        """Check for new Instagram leads."""
        if self.channels["instagram"].get("status") != "available":
            return

        try:
            # Get new messages from Instagram
            messages = await self.social_media_service.get_new_messages("instagram")

            for message in messages:
                # Extract message data
                lead_id = f"instagram-{message.get('sender_id')}"
                user_handle = message.get("sender_name", "Unknown")
                content = message.get("message", "")

                # Handle lead
                await self.handle_lead(
                    "instagram",
                    {
                        "lead_id": lead_id,
                        "user_handle": user_handle,
                        "message": content
                    }
                )

        except Exception as e:
            logger.exception(f"Error checking Instagram leads: {e}")

    async def _check_tiktok_leads(self):
        """Check for new TikTok leads."""
        if self.channels["tiktok"].get("status") != "available":
            return

        try:
            # Get new messages from TikTok
            messages = await self.social_media_service.get_new_messages("tiktok")

            for message in messages:
                # Extract message data
                lead_id = f"tiktok-{message.get('sender_id')}"
                user_handle = message.get("sender_name", "Unknown")
                content = message.get("message", "")

                # Handle lead
                await self.handle_lead(
                    "tiktok",
                    {
                        "lead_id": lead_id,
                        "user_handle": user_handle,
                        "message": content
                    }
                )

        except Exception as e:
            logger.exception(f"Error checking TikTok leads: {e}")

    async def _check_website_leads(self):
        """Check for new website leads."""
        if self.channels["website"].get("status") != "available":
            return

        try:
            # For website, we might check an API endpoint or database
            # This is a placeholder for actual implementation

            # Example: Check for new form submissions
            form_submissions = await self._get_website_form_submissions()

            for submission in form_submissions:
                # Extract submission data
                lead_id = f"website-{submission.get('submission_id')}"
                user_handle = submission.get("name", "Unknown")
                content = submission.get("message", "")

                # Handle lead
                await self.handle_lead(
                    "website",
                    {
                        "lead_id": lead_id,
                        "user_handle": user_handle,
                        "message": content
                    }
                )

        except Exception as e:
            logger.exception(f"Error checking website leads: {e}")

    async def _get_website_form_submissions(self) -> List[Dict]:
        """
        Get website form submissions.

        Returns:
            List[Dict]: List of form submissions
        """
        # This is a placeholder for actual implementation
        # In a real system, this would query a database or API
        return []

    async def _send_facebook_response(self, lead_id: str, response: str) -> bool:
        """
        Send response to Facebook lead.

        Args:
            lead_id (str): Lead ID
            response (str): Response message

        Returns:
            bool: Success status
        """
        try:
            # Extract Facebook user ID from lead ID
            user_id = lead_id.replace("facebook-", "")

            # Send message
            result = await self.social_media_service.send_message(
                "facebook",
                user_id,
                response
            )

            return result.get("success", False)

        except Exception as e:
            logger.exception(f"Error sending Facebook response: {e}")
            return False

    async def _send_instagram_response(self, lead_id: str, response: str) -> bool:
        """
        Send response to Instagram lead.

        Args:
            lead_id (str): Lead ID
            response (str): Response message

        Returns:
            bool: Success status
        """
        try:
            # Extract Instagram user ID from lead ID
            user_id = lead_id.replace("instagram-", "")

            # Send message
            result = await self.social_media_service.send_message(
                "instagram",
                user_id,
                response
            )

            return result.get("success", False)

        except Exception as e:
            logger.exception(f"Error sending Instagram response: {e}")
            return False

    async def _send_tiktok_response(self, lead_id: str, response: str) -> bool:
        """
        Send response to TikTok lead.

        Args:
            lead_id (str): Lead ID
            response (str): Response message

        Returns:
            bool: Success status
        """
        try:
            # Extract TikTok user ID from lead ID
            user_id = lead_id.replace("tiktok-", "")

            # Send message
            result = await self.social_media_service.send_message(
                "tiktok",
                user_id,
                response
            )

            return result.get("success", False)

        except Exception as e:
            logger.exception(f"Error sending TikTok response: {e}")
            return False

    async def _send_website_response(self, lead_id: str, response: str) -> bool:
        """
        Send response to website lead.

        Args:
            lead_id (str): Lead ID
            response (str): Response message

        Returns:
            bool: Success status
        """
        try:
            # Extract website submission ID from lead ID
            submission_id = lead_id.replace("website-", "")

            # For website leads, we might send an email or SMS
            # This is a placeholder for actual implementation

            # Get lead data
            lead = self.leads.get(lead_id, {})

            # If we have an email, send email response
            if lead.get("email"):
                # Send email
                email_result = await self._send_email_response(lead_id, response)
                return email_result

            # If we have a phone number, send SMS response
            if lead.get("phone"):
                # Send SMS
                sms_result = await self._send_sms_response(lead_id, response)
                return sms_result

            # If we don't have contact info, log and return failure
            logger.warning(f"No contact info for website lead {lead_id}")
            return False

        except Exception as e:
            logger.exception(f"Error sending website response: {e}")
            return False

    async def _send_email_response(self, lead_id: str, response: str) -> bool:
        """
        Send email response to lead.

        Args:
            lead_id (str): Lead ID
            response (str): Response message

        Returns:
            bool: Success status
        """
        try:
            # Get lead data
            lead = self.leads.get(lead_id, {})
            email = lead.get("email")

            if not email:
                logger.warning(f"No email for lead {lead_id}")
                return False

            # Format email
            subject = "Your Insurance Inquiry - Flo Faction Insurance"

            # Send email
            # This would integrate with email service
            logger.info(f"Would send email to {email}: {subject}")

            # Placeholder for actual email sending
            return True

        except Exception as e:
            logger.exception(f"Error sending email response: {e}")
            return False

    async def _send_sms_response(self, lead_id: str, response: str) -> bool:
        """
        Send SMS response to lead.

        Args:
            lead_id (str): Lead ID
            response (str): Response message

        Returns:
            bool: Success status
        """
        try:
            # Get lead data
            lead = self.leads.get(lead_id, {})
            phone = lead.get("phone")

            if not phone:
                logger.warning(f"No phone for lead {lead_id}")
                return False

            # Send SMS
            # This would integrate with SMS service
            logger.info(f"Would send SMS to {phone}: {response}")

            # Placeholder for actual SMS sending
            return True

        except Exception as e:
            logger.exception(f"Error sending SMS response: {e}")
            return False
