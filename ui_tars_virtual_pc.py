"""
UI-TARS Virtual PC Environment

This script creates a virtual PC environment for UI-TARS 1.5, providing:
- Isolated browser environment
- Sandboxed execution
- Virtual display
- Enhanced security
- DPO (Direct Preference Optimization) support
"""
import os
import sys
import time
import json
import logging
import argparse
import subprocess
import platform
import socket
import requests
import asyncio
import shutil
import tempfile
import threading
import signal
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Union

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("ui_tars_virtual_pc.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("ui_tars_virtual_pc")

# Constants
DEFAULT_API_PORT = 8080
DEFAULT_BROWSER_PORT = 9222
DEFAULT_SANDBOX_PORT = 9223
DEFAULT_VIRTUAL_DISPLAY = 99
DEFAULT_TIMEOUT = 10
BROWSER_TYPES = ["chrome", "edge", "firefox", "brave"]

class UITarsVirtualPC:
    """Virtual PC environment for UI-TARS 1.5."""

    def __init__(self, 
                 ui_tars_path: Optional[str] = None,
                 browser_type: str = "chrome",
                 memory_mb: int = 2048,
                 cpu_cores: int = 2,
                 virtual_display: bool = True,
                 sandbox_mode: bool = True,
                 dpo_enabled: bool = True,
                 debug_mode: bool = False):
        """
        Initialize the UI-TARS Virtual PC.
        
        Args:
            ui_tars_path: Path to UI-TARS executable
            browser_type: Type of browser to use
            memory_mb: Memory allocation in MB
            cpu_cores: Number of CPU cores to allocate
            virtual_display: Whether to use a virtual display
            sandbox_mode: Whether to use sandbox mode
            dpo_enabled: Whether to enable DPO
            debug_mode: Whether to enable debug mode
        """
        self.ui_tars_path = ui_tars_path
        self.browser_type = browser_type.lower()
        self.memory_mb = memory_mb
        self.cpu_cores = cpu_cores
        self.virtual_display = virtual_display
        self.sandbox_mode = sandbox_mode
        self.dpo_enabled = dpo_enabled
        self.debug_mode = debug_mode
        self.os_type = platform.system()
        self.browser_paths = {}
        self.ui_tars_process = None
        self.browser_process = None
        self.xvfb_process = None
        self.sandbox_process = None
        self.virtual_pc_dir = None
        self.config_path = None
        self.display_num = DEFAULT_VIRTUAL_DISPLAY
        self.running = False
        self.shutdown_event = threading.Event()
        
        # Set up virtual PC directory
        self.virtual_pc_dir = tempfile.mkdtemp(prefix="ui_tars_virtual_pc_")
        logger.info(f"Created virtual PC directory: {self.virtual_pc_dir}")
        
        # Find UI-TARS executable if not provided
        if not self.ui_tars_path:
            self.ui_tars_path = self._find_ui_tars_executable()
            
        # Detect browsers
        self.detect_browsers()

    def _find_ui_tars_executable(self) -> Optional[str]:
        """Find the UI-TARS executable."""
        logger.info("Searching for UI-TARS executable...")
        
        if self.os_type == "Windows":
            # Common installation locations on Windows
            possible_paths = [
                os.path.join(os.environ.get("PROGRAMFILES", "C:\\Program Files"), "UI-TARS", "UI-TARS.exe"),
                os.path.join(os.environ.get("PROGRAMFILES(X86)", "C:\\Program Files (x86)"), "UI-TARS", "UI-TARS.exe"),
                os.path.join(os.environ.get("LOCALAPPDATA", "C:\\Users\\<USER>\\AppData\\Local".format(os.getlogin())), "UI-TARS", "UI-TARS.exe"),
                "UI-TARS.exe"
            ]
        elif self.os_type == "Darwin":  # macOS
            # Common installation locations on macOS
            possible_paths = [
                "/Applications/UI-TARS.app/Contents/MacOS/UI-TARS",
                os.path.expanduser("~/Applications/UI-TARS.app/Contents/MacOS/UI-TARS"),
            ]
        else:  # Linux
            # Common installation locations on Linux
            possible_paths = [
                "/usr/local/bin/ui-tars",
                "/usr/bin/ui-tars",
                os.path.expanduser("~/.local/bin/ui-tars"),
            ]
            
        # Check if any of the paths exist
        for path in possible_paths:
            if os.path.exists(path):
                logger.info(f"Found UI-TARS executable at: {path}")
                return path
                
        # Try to find in PATH
        try:
            if self.os_type == "Windows":
                result = subprocess.run(["where", "UI-TARS.exe"], capture_output=True, text=True)
            else:
                result = subprocess.run(["which", "ui-tars"], capture_output=True, text=True)
                
            if result.returncode == 0:
                path = result.stdout.strip()
                logger.info(f"Found UI-TARS executable in PATH: {path}")
                return path
        except Exception as e:
            logger.debug(f"Error searching for UI-TARS in PATH: {e}")
            
        logger.warning("Could not find UI-TARS executable")
        return None

    def detect_browsers(self) -> Dict[str, str]:
        """Detect installed browsers and their paths."""
        logger.info("Detecting installed browsers...")
        
        browsers = {}
        
        if self.os_type == "Windows":
            # Check for common browsers on Windows
            paths = [
                (r"C:\Program Files\Google\Chrome\Application\chrome.exe", "chrome"),
                (r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe", "chrome"),
                (r"C:\Program Files\Mozilla Firefox\firefox.exe", "firefox"),
                (r"C:\Program Files (x86)\Mozilla Firefox\firefox.exe", "firefox"),
                (r"C:\Program Files\Microsoft\Edge\Application\msedge.exe", "edge"),
                (r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe", "edge"),
                (r"C:\Program Files\BraveSoftware\Brave-Browser\Application\brave.exe", "brave"),
                (r"C:\Program Files (x86)\BraveSoftware\Brave-Browser\Application\brave.exe", "brave"),
            ]
        elif self.os_type == "Darwin":  # macOS
            # Check for common browsers on macOS
            paths = [
                ("/Applications/Google Chrome.app/Contents/MacOS/Google Chrome", "chrome"),
                ("/Applications/Firefox.app/Contents/MacOS/firefox", "firefox"),
                ("/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge", "edge"),
                ("/Applications/Brave Browser.app/Contents/MacOS/Brave Browser", "brave"),
            ]
        else:  # Linux
            # Check for common browsers on Linux
            paths = [
                ("/usr/bin/google-chrome", "chrome"),
                ("/usr/bin/firefox", "firefox"),
                ("/usr/bin/microsoft-edge", "edge"),
                ("/usr/bin/brave-browser", "brave"),
            ]
            
        # Check if any of the paths exist
        for path, browser_type in paths:
            if os.path.exists(path):
                browsers[browser_type] = path
                logger.info(f"Found {browser_type} browser at: {path}")
                
        self.browser_paths = browsers
        return browsers

    def create_virtual_pc_config(self) -> str:
        """Create a virtual PC configuration for UI-TARS."""
        logger.info("Creating virtual PC configuration...")
        
        # Create config directory in virtual PC dir
        config_dir = os.path.join(self.virtual_pc_dir, "config")
        os.makedirs(config_dir, exist_ok=True)
        
        # Create config file
        config_path = os.path.join(config_dir, "ui_tars_virtual_pc_config.json")
        
        # Get browser path
        browser_path = self.browser_paths.get(self.browser_type)
        
        # Create config
        config = {
            "ui_tars": {
                "version": "1.5",
                "enabled": True,
                "virtual_pc": {
                    "enabled": True,
                    "memory_mb": self.memory_mb,
                    "cpu_cores": self.cpu_cores,
                    "virtual_display": self.virtual_display,
                    "display_num": self.display_num,
                    "temp_dir": self.virtual_pc_dir
                },
                "sandbox": {
                    "enabled": self.sandbox_mode,
                    "isolation_level": "high",
                    "temp_dir": os.path.join(self.virtual_pc_dir, "sandbox")
                },
                "dpo": {
                    "enabled": self.dpo_enabled,
                    "preference_model": "default"
                },
                "browser": {
                    "type": self.browser_type,
                    "executable_path": browser_path,
                    "remote_debugging_port": DEFAULT_BROWSER_PORT,
                    "sandbox_port": DEFAULT_SANDBOX_PORT,
                    "user_data_dir": os.path.join(self.virtual_pc_dir, "browser_data"),
                    "profile_directory": "Default",
                    "detection": {
                        "auto_detect": True,
                        "fallback_types": BROWSER_TYPES
                    }
                },
                "api": {
                    "host": "localhost",
                    "port": DEFAULT_API_PORT,
                    "timeout": DEFAULT_TIMEOUT,
                    "retry_attempts": 3
                },
                "debug": {
                    "enabled": self.debug_mode,
                    "log_level": "debug" if self.debug_mode else "info",
                    "log_file": os.path.join(self.virtual_pc_dir, "ui_tars_debug.log")
                }
            }
        }
        
        # Write config to file
        with open(config_path, "w") as f:
            json.dump(config, f, indent=2)
            
        logger.info(f"Virtual PC configuration created at: {config_path}")
        self.config_path = config_path
        return config_path

    async def start_virtual_display(self) -> bool:
        """Start a virtual display for UI-TARS."""
        if not self.virtual_display or self.os_type == "Windows":
            logger.info("Virtual display not needed or not supported on this platform")
            return True
            
        logger.info(f"Starting virtual display (DISPLAY=:{self.display_num})...")
        
        try:
            # Check if Xvfb is installed
            xvfb_check = subprocess.run(
                ["which", "Xvfb"], 
                stdout=subprocess.PIPE, 
                stderr=subprocess.PIPE,
                text=True
            )
            
            if xvfb_check.returncode != 0:
                logger.error("Xvfb is not installed. Please install it first.")
                return False
                
            # Start Xvfb
            self.xvfb_process = subprocess.Popen(
                ["Xvfb", f":{self.display_num}", "-screen", "0", "1920x1080x24"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Wait for Xvfb to start
            await asyncio.sleep(2)
            
            # Check if Xvfb is running
            if self.xvfb_process.poll() is not None:
                logger.error(f"Xvfb process exited with code: {self.xvfb_process.returncode}")
                return False
                
            # Set DISPLAY environment variable
            os.environ["DISPLAY"] = f":{self.display_num}"
            
            logger.info(f"Virtual display started (DISPLAY=:{self.display_num})")
            return True
            
        except Exception as e:
            logger.exception(f"Error starting virtual display: {e}")
            return False

    async def start_browser_in_virtual_pc(self) -> bool:
        """Start a browser in the virtual PC."""
        # Get browser path
        browser_path = self.browser_paths.get(self.browser_type)
        
        if not browser_path:
            logger.error(f"Browser {self.browser_type} not found")
            return False
            
        logger.info(f"Starting {self.browser_type} browser in virtual PC...")
        
        try:
            # Create user data directory
            user_data_dir = os.path.join(self.virtual_pc_dir, "browser_data")
            os.makedirs(user_data_dir, exist_ok=True)
            
            # Prepare command
            command = [
                browser_path,
                f"--remote-debugging-port={DEFAULT_BROWSER_PORT}",
                f"--user-data-dir={user_data_dir}",
                "--no-first-run",
                "--no-default-browser-check",
                "--disable-extensions",
                "--disable-sync",
                "--disable-background-networking",
                "--disable-default-apps",
                "--disable-translate",
                "--disable-features=TranslateUI",
                "--disable-infobars",
                "--disable-save-password-bubble",
                "--disable-notifications",
                "--disable-popup-blocking",
                "--disable-component-update",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
                "--disable-background-mode",
                "--disable-breakpad",
                "--disable-client-side-phishing-detection",
                "--disable-hang-monitor",
                "--disable-prompt-on-repost",
                "--metrics-recording-only",
                "--safebrowsing-disable-auto-update",
                "--password-store=basic",
                "--use-mock-keychain",
                "--no-sandbox",
                "about:blank"
            ]
            
            # Start browser process
            env = os.environ.copy()
            if self.virtual_display and self.os_type != "Windows":
                env["DISPLAY"] = f":{self.display_num}"
                
            self.browser_process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                env=env
            )
            
            # Wait for the process to start
            await asyncio.sleep(3)
            
            # Check if process is still running
            if self.browser_process.poll() is not None:
                logger.error(f"Browser process exited with code: {self.browser_process.returncode}")
                return False
                
            logger.info(f"{self.browser_type} browser started successfully in virtual PC")
            
            # Check if browser debugging port is open
            for _ in range(5):  # Try 5 times
                try:
                    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                        s.settimeout(DEFAULT_TIMEOUT)
                        result = s.connect_ex(("localhost", DEFAULT_BROWSER_PORT))
                        if result == 0:
                            logger.info(f"Browser debugging port {DEFAULT_BROWSER_PORT} is open")
                            return True
                except Exception:
                    pass
                    
                await asyncio.sleep(1)
                
            logger.warning(f"Browser debugging port {DEFAULT_BROWSER_PORT} is not open")
            return False
                
        except Exception as e:
            logger.exception(f"Error starting browser in virtual PC: {e}")
            return False

    async def start_ui_tars_in_virtual_pc(self) -> bool:
        """Start UI-TARS in the virtual PC."""
        if not self.ui_tars_path:
            logger.error("UI-TARS executable not found")
            return False
            
        if not os.path.exists(self.ui_tars_path):
            logger.error(f"UI-TARS executable not found at: {self.ui_tars_path}")
            return False
            
        # Create virtual PC config if needed
        if not self.config_path:
            self.config_path = self.create_virtual_pc_config()
            
        logger.info(f"Starting UI-TARS in virtual PC from: {self.ui_tars_path}")
        
        try:
            # Prepare command
            command = [self.ui_tars_path]
            
            if self.config_path:
                command.extend(["--config", self.config_path])
                
            # Add additional options
            if self.sandbox_mode:
                command.append("--sandbox")
                
            if self.dpo_enabled:
                command.append("--dpo")
                
            if self.debug_mode:
                command.append("--debug")
                
            # Start UI-TARS process
            env = os.environ.copy()
            if self.virtual_display and self.os_type != "Windows":
                env["DISPLAY"] = f":{self.display_num}"
                
            self.ui_tars_process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                env=env
            )
            
            # Wait for the process to start
            await asyncio.sleep(5)
            
            # Check if process is still running
            if self.ui_tars_process.poll() is not None:
                logger.error(f"UI-TARS process exited with code: {self.ui_tars_process.returncode}")
                return False
                
            logger.info("UI-TARS started successfully in virtual PC")
            
            # Check if API is running
            for _ in range(5):  # Try 5 times
                try:
                    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                        s.settimeout(DEFAULT_TIMEOUT)
                        result = s.connect_ex(("localhost", DEFAULT_API_PORT))
                        if result == 0:
                            logger.info(f"UI-TARS API is running on port {DEFAULT_API_PORT}")
                            return True
                except Exception:
                    pass
                    
                await asyncio.sleep(2)
                
            logger.warning(f"UI-TARS API is not running on port {DEFAULT_API_PORT}")
            return False
            
        except Exception as e:
            logger.exception(f"Error starting UI-TARS in virtual PC: {e}")
            return False

    async def start(self) -> bool:
        """Start the UI-TARS Virtual PC environment."""
        logger.info("Starting UI-TARS Virtual PC environment...")
        
        try:
            # Start virtual display if needed
            if self.virtual_display and self.os_type != "Windows":
                display_success = await self.start_virtual_display()
                if not display_success:
                    logger.error("Failed to start virtual display")
                    return False
                    
            # Start browser in virtual PC
            browser_success = await self.start_browser_in_virtual_pc()
            if not browser_success:
                logger.error("Failed to start browser in virtual PC")
                return False
                
            # Start UI-TARS in virtual PC
            ui_tars_success = await self.start_ui_tars_in_virtual_pc()
            if not ui_tars_success:
                logger.error("Failed to start UI-TARS in virtual PC")
                return False
                
            self.running = True
            logger.info("UI-TARS Virtual PC environment started successfully")
            
            return True
            
        except Exception as e:
            logger.exception(f"Error starting UI-TARS Virtual PC environment: {e}")
            return False

    async def stop(self) -> bool:
        """Stop the UI-TARS Virtual PC environment."""
        logger.info("Stopping UI-TARS Virtual PC environment...")
        
        try:
            # Stop UI-TARS process
            if self.ui_tars_process:
                try:
                    self.ui_tars_process.terminate()
                    await asyncio.sleep(2)
                    
                    if self.ui_tars_process.poll() is None:
                        self.ui_tars_process.kill()
                        
                    logger.info("UI-TARS process terminated")
                except Exception as e:
                    logger.error(f"Error terminating UI-TARS process: {e}")
                    
            # Stop browser process
            if self.browser_process:
                try:
                    self.browser_process.terminate()
                    await asyncio.sleep(2)
                    
                    if self.browser_process.poll() is None:
                        self.browser_process.kill()
                        
                    logger.info("Browser process terminated")
                except Exception as e:
                    logger.error(f"Error terminating browser process: {e}")
                    
            # Stop Xvfb process
            if self.xvfb_process:
                try:
                    self.xvfb_process.terminate()
                    await asyncio.sleep(2)
                    
                    if self.xvfb_process.poll() is None:
                        self.xvfb_process.kill()
                        
                    logger.info("Xvfb process terminated")
                except Exception as e:
                    logger.error(f"Error terminating Xvfb process: {e}")
                    
            # Remove virtual PC directory
            if self.virtual_pc_dir and os.path.exists(self.virtual_pc_dir):
                try:
                    shutil.rmtree(self.virtual_pc_dir)
                    logger.info(f"Virtual PC directory removed: {self.virtual_pc_dir}")
                except Exception as e:
                    logger.error(f"Error removing virtual PC directory: {e}")
                    
            self.running = False
            logger.info("UI-TARS Virtual PC environment stopped")
            
            return True
            
        except Exception as e:
            logger.exception(f"Error stopping UI-TARS Virtual PC environment: {e}")
            return False

    async def run(self) -> None:
        """Run the UI-TARS Virtual PC environment until shutdown."""
        # Start the environment
        success = await self.start()
        if not success:
            logger.error("Failed to start UI-TARS Virtual PC environment")
            return
            
        logger.info("UI-TARS Virtual PC environment is running")
        
        # Wait for shutdown event
        try:
            while not self.shutdown_event.is_set():
                # Check if processes are still running
                if self.ui_tars_process and self.ui_tars_process.poll() is not None:
                    logger.warning(f"UI-TARS process exited with code: {self.ui_tars_process.returncode}")
                    break
                    
                if self.browser_process and self.browser_process.poll() is not None:
                    logger.warning(f"Browser process exited with code: {self.browser_process.returncode}")
                    break
                    
                if self.xvfb_process and self.xvfb_process.poll() is not None:
                    logger.warning(f"Xvfb process exited with code: {self.xvfb_process.returncode}")
                    break
                    
                await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt, shutting down...")
            
        finally:
            # Stop the environment
            await self.stop()

async def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="UI-TARS Virtual PC Environment")
    parser.add_argument("--path", type=str, help="Path to UI-TARS executable")
    parser.add_argument("--browser", type=str, default="chrome", choices=BROWSER_TYPES, help="Browser type")
    parser.add_argument("--memory", type=int, default=2048, help="Memory allocation in MB")
    parser.add_argument("--cpu", type=int, default=2, help="Number of CPU cores to allocate")
    parser.add_argument("--no-virtual-display", action="store_true", help="Disable virtual display")
    parser.add_argument("--no-sandbox", action="store_true", help="Disable sandbox mode")
    parser.add_argument("--no-dpo", action="store_true", help="Disable DPO")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    
    args = parser.parse_args()
    
    # Set log level
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        
    print("UI-TARS Virtual PC Environment")
    print("=============================")
    print()
    
    # Create virtual PC
    virtual_pc = UITarsVirtualPC(
        ui_tars_path=args.path,
        browser_type=args.browser,
        memory_mb=args.memory,
        cpu_cores=args.cpu,
        virtual_display=not args.no_virtual_display,
        sandbox_mode=not args.no_sandbox,
        dpo_enabled=not args.no_dpo,
        debug_mode=args.debug
    )
    
    # Set up signal handlers
    def signal_handler(sig, frame):
        print("\nReceived signal, shutting down...")
        virtual_pc.shutdown_event.set()
        
    if platform.system() != "Windows":
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    # Run the virtual PC
    await virtual_pc.run()
    
    return 0

if __name__ == "__main__":
    asyncio.run(main())
