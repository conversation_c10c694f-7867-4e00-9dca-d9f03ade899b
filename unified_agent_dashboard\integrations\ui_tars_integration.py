"""
UI-TARS Integration.

This module provides integration with UI-TARS for browser automation.
"""
import os
import sys
import json
import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
import requests
from datetime import datetime
import base64
from io import BytesIO
from PIL import Image

# Add the project root to the Python path
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent.parent))

try:
    from core.logger import setup_logger
    from core.state_manager import StateManager
    from core.agent_manager import AgentManager
    from ui_tars.connector.ui_tars_connector import UITarsConnector
    from ui_tars.connector.enhanced_ui_tars_connector import EnhancedUITarsConnector
    from ui_tars.agent.enhanced_ui_tars_agent import EnhancedUITarsAgent
except ImportError as e:
    print(f"Error importing required modules: {e}")
    # Fallback imports for standalone usage
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler("ui_tars_integration.log")
        ]
    )

    def setup_logger(name):
        return logging.getLogger(name)

    StateManager = object
    AgentManager = object
    UITarsConnector = object
    EnhancedUITarsConnector = object
    EnhancedUITarsAgent = object

# Set up logger
logger = setup_logger("ui_tars_integration")

class UITarsIntegration:
    """
    UI-TARS Integration.

    This class provides integration with UI-TARS for browser automation.
    """

    def __init__(self,
                 state_manager: Optional[StateManager] = None,
                 agent_manager: Optional[AgentManager] = None,
                 ui_tars_connector: Optional[Union[UITarsConnector, EnhancedUITarsConnector]] = None,
                 api_url: str = "http://localhost:8080",
                 api_key: str = "hf_dummy_key",
                 model_name: str = "UI-TARS-1.5-7B",
                 browser_type: str = "chrome",
                 auto_start: bool = True,
                 auto_restart: bool = True):
        """
        Initialize the UI-TARS Integration.

        Args:
            state_manager (Optional[StateManager]): State manager
            agent_manager (Optional[AgentManager]): Agent manager
            ui_tars_connector (Optional[Union[UITarsConnector, EnhancedUITarsConnector]]): UI-TARS connector
            api_url (str): API URL
            api_key (str): API key
            model_name (str): Model name
            browser_type (str): Browser type
            auto_start (bool): Whether to auto-start the browser
            auto_restart (bool): Whether to auto-restart the browser
        """
        self.state_manager = state_manager
        self.agent_manager = agent_manager
        self.ui_tars_connector = ui_tars_connector
        self.api_url = api_url
        self.api_key = api_key
        self.model_name = model_name
        self.browser_type = browser_type
        self.auto_start = auto_start
        self.auto_restart = auto_restart
        self.initialized = False
        self.browser_id = None
        self.screenshot_data = None
        self.last_command = None
        self.command_history = []

    async def initialize(self) -> bool:
        """
        Initialize the UI-TARS Integration.

        Returns:
            bool: Whether initialization was successful
        """
        try:
            logger.info("Initializing UI-TARS Integration")

            # Create UI-TARS connector if not provided
            if not self.ui_tars_connector:
                self.ui_tars_connector = EnhancedUITarsConnector(
                    state_manager=self.state_manager,
                    agent_manager=self.agent_manager,
                    api_url=self.api_url,
                    api_key=self.api_key,
                    model_name=self.model_name
                )
                await self.ui_tars_connector.initialize()

            # Register with agent manager
            if self.agent_manager:
                await self.agent_manager.register_service("ui_tars", self)

            # Start browser if auto-start is enabled
            if self.auto_start:
                await self.start_browser()

            # Set initialized flag
            self.initialized = True

            logger.info("UI-TARS Integration initialized successfully")
            return True

        except Exception as e:
            logger.exception(f"Error initializing UI-TARS Integration: {e}")
            return False

    async def start_browser(self) -> Dict:
        """
        Start the browser.

        Returns:
            Dict: Browser information
        """
        try:
            logger.info("Starting browser")

            # Check if initialized
            if not self.initialized and not self.ui_tars_connector:
                logger.error("UI-TARS Integration not initialized")
                return {"error": "UI-TARS Integration not initialized"}

            # Start browser
            browser_info = await self.ui_tars_connector.start_browser(browser_type=self.browser_type)

            if "error" not in browser_info:
                self.browser_id = browser_info.get("browser_id")
                logger.info(f"Browser started successfully: {self.browser_id}")
                
                # Take initial screenshot
                await self.take_screenshot()
                
                return browser_info
            else:
                logger.error(f"Failed to start browser: {browser_info.get('error')}")
                return browser_info

        except Exception as e:
            logger.exception(f"Error starting browser: {e}")
            return {"error": str(e)}

    async def stop_browser(self) -> Dict:
        """
        Stop the browser.

        Returns:
            Dict: Result of the operation
        """
        try:
            logger.info("Stopping browser")

            # Check if initialized
            if not self.initialized and not self.ui_tars_connector:
                logger.error("UI-TARS Integration not initialized")
                return {"error": "UI-TARS Integration not initialized"}

            # Check if browser is running
            if not self.browser_id:
                logger.warning("No browser is running")
                return {"error": "No browser is running"}

            # Stop browser
            result = await self.ui_tars_connector.stop_browser(browser_id=self.browser_id)

            if "error" not in result:
                self.browser_id = None
                self.screenshot_data = None
                logger.info("Browser stopped successfully")
                return {"status": "success"}
            else:
                logger.error(f"Failed to stop browser: {result.get('error')}")
                return result

        except Exception as e:
            logger.exception(f"Error stopping browser: {e}")
            return {"error": str(e)}

    async def take_screenshot(self) -> Dict:
        """
        Take a screenshot.

        Returns:
            Dict: Screenshot data
        """
        try:
            logger.info("Taking screenshot")

            # Check if initialized
            if not self.initialized and not self.ui_tars_connector:
                logger.error("UI-TARS Integration not initialized")
                return {"error": "UI-TARS Integration not initialized"}

            # Check if browser is running
            if not self.browser_id:
                logger.warning("No browser is running")
                return {"error": "No browser is running"}

            # Take screenshot
            screenshot = await self.ui_tars_connector.take_screenshot(browser_id=self.browser_id)

            if "error" not in screenshot:
                # Decode base64 image
                image_data = base64.b64decode(screenshot.get("data", ""))
                
                # Create image from data
                image = Image.open(BytesIO(image_data))
                
                # Save image to memory
                output = BytesIO()
                image.save(output, format="PNG")
                
                # Store screenshot data
                self.screenshot_data = output
                
                logger.info("Screenshot taken successfully")
                return {"status": "success", "data": output}
            else:
                logger.error(f"Failed to take screenshot: {screenshot.get('error')}")
                return screenshot

        except Exception as e:
            logger.exception(f"Error taking screenshot: {e}")
            return {"error": str(e)}

    async def execute_command(self, command: str) -> Dict:
        """
        Execute a command.

        Args:
            command (str): Command to execute

        Returns:
            Dict: Result of the operation
        """
        try:
            logger.info(f"Executing command: {command}")

            # Check if initialized
            if not self.initialized and not self.ui_tars_connector:
                logger.error("UI-TARS Integration not initialized")
                return {"error": "UI-TARS Integration not initialized"}

            # Check if browser is running
            if not self.browser_id:
                logger.warning("No browser is running")
                return {"error": "No browser is running"}

            # Store command in history
            self.last_command = command
            self.command_history.append(command)

            # Execute command
            result = await self.ui_tars_connector.execute_command(
                browser_id=self.browser_id,
                command=command
            )

            if "error" not in result:
                logger.info(f"Command executed successfully: {result}")
                
                # Take screenshot after command
                await self.take_screenshot()
                
                return result
            else:
                logger.error(f"Failed to execute command: {result.get('error')}")
                return result

        except Exception as e:
            logger.exception(f"Error executing command: {e}")
            return {"error": str(e)}

    async def get_browser_info(self) -> Dict:
        """
        Get browser information.

        Returns:
            Dict: Browser information
        """
        try:
            logger.info("Getting browser information")

            # Check if initialized
            if not self.initialized and not self.ui_tars_connector:
                logger.error("UI-TARS Integration not initialized")
                return {"error": "UI-TARS Integration not initialized"}

            # Check if browser is running
            if not self.browser_id:
                logger.warning("No browser is running")
                return {"error": "No browser is running"}

            # Get browser info
            browser_info = await self.ui_tars_connector.get_browser_info(browser_id=self.browser_id)

            if "error" not in browser_info:
                logger.info(f"Browser information retrieved successfully: {browser_info}")
                return browser_info
            else:
                logger.error(f"Failed to get browser information: {browser_info.get('error')}")
                return browser_info

        except Exception as e:
            logger.exception(f"Error getting browser information: {e}")
            return {"error": str(e)}
