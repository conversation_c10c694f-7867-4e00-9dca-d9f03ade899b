# UI-TARS Email Integration

This package provides integration between UI-TARS and your AI Agent System for sending emails. It demonstrates how to use UI-TARS browser automation to send emails through Gmail, as well as how to integrate this with your existing agent system.

## Overview

The integration allows your agents to send emails using two methods:

1. **UI-TARS Browser Automation**: Uses UI-TARS to control a browser, navigate to Gmail, and send emails visually
2. **Gmail API**: Uses the Gmail API to send emails programmatically

Each method has its advantages and disadvantages, and this package allows you to choose the best method for your needs or even use both methods together.

## Components

The package includes the following components:

- `ui_tars_gmail_automation.py`: Core module for automating Gmail using UI-TARS
- `agent_ui_tars_email.py`: Integration with the agent system
- `email_sender_demo.py`: Demo script that shows how to use both methods
- `send_email_ui_tars.bat`: Simple batch script for sending emails using UI-TARS
- `run_email_sender_demo.bat`: Batch script for running the email sender demo

## Prerequisites

Before using this integration, make sure you have:

1. UI-TARS 1.5 installed and configured
2. Chrome browser installed
3. Gmail account credentials (email and password)
4. Gmail API credentials (for API-based email sending)

## Usage

### Quick Start: Send an Email with UI-TARS

To quickly send an email using UI-TARS browser automation, run:

```
send_email_ui_tars.bat
```

This will prompt you for:
- <NAME_EMAIL>
- Email subject
- Email body

The script will then use UI-TARS to automate the browser, log in to Gmail, and send the email.

### Email Sender Demo

To try both methods and compare them, run:

```
run_email_sender_demo.bat
```

This will prompt you to choose a method:
1. UI-TARS browser automation
2. Gmail API
3. Both (for comparison)

It will then ask for email details and send the email using the selected method(s).

### Programmatic Usage

To use the integration in your own code:

```python
import asyncio
from ui_tars_gmail_automation import GmailUITarsAutomation

async def send_email():
    # Create Gmail UI-TARS automation
    gmail_automation = GmailUITarsAutomation()
    
    # Initialize
    await gmail_automation.initialize()
    
    try:
        # Send email
        result = await gmail_automation.send_email(
            email_account="<EMAIL>",
            password="your_password",
            to_email="<EMAIL>",
            subject="Test Email",
            body="This is a test email sent using UI-TARS."
        )
        
        if result["success"]:
            print("Email sent successfully")
        else:
            print(f"Failed to send email: {result['error']}")
    
    finally:
        # Shut down
        await gmail_automation.shutdown()

if __name__ == "__main__":
    asyncio.run(send_email())
```

### Integration with Agent System

To integrate with your agent system:

```python
import asyncio
from agent_ui_tars_email import EmailUITarsAgent

async def agent_demo():
    # Create Email UI-TARS Agent
    agent = EmailUITarsAgent()
    
    # Initialize
    await agent.initialize()
    
    try:
        # Send email using UI-TARS
        result = await agent.send_email_ui_tars(
            email_account="<EMAIL>",
            password="your_password",
            to_email="<EMAIL>",
            subject="Test Email",
            body="This is a test email sent using UI-TARS."
        )
        
        if result["success"]:
            print("Email sent successfully using UI-TARS")
        
        # Send email using Gmail API
        result = await agent.send_email_api(
            to_email="<EMAIL>",
            subject="Test Email",
            body="This is a test email sent using the Gmail API."
        )
        
        if result["success"]:
            print("Email sent successfully using Gmail API")
    
    finally:
        # Shut down
        await agent.shutdown()

if __name__ == "__main__":
    asyncio.run(agent_demo())
```

## Comparison of Methods

### UI-TARS Browser Automation

**Pros:**
- Visual feedback (you can see what's happening)
- Can handle CAPTCHAs and other interactive elements
- Works with any email provider, not just Gmail
- No need for API setup

**Cons:**
- Slower than API-based methods
- Requires email password
- Less reliable (browser elements might change)
- More resource-intensive

### Gmail API

**Pros:**
- Faster and more reliable
- No need for email password (uses OAuth)
- More secure
- Can be used headlessly

**Cons:**
- Limited to Gmail
- Requires API setup and credentials
- No visual feedback
- May require periodic token refresh

## Troubleshooting

If you encounter issues:

1. **UI-TARS not starting**: Make sure UI-TARS is installed and the installation path is correct
2. **Browser automation failing**: Check that Chrome is installed and UI-TARS is configured to use it
3. **Gmail login failing**: Verify your email and password are correct
4. **Gmail API errors**: Check your API credentials and token

## Advanced Configuration

For advanced configuration, you can modify:

- `ui_tars_config.yaml`: UI-TARS configuration
- `config/email_accounts.json`: Email account configuration
- `credentials/`: Gmail API credentials

## Next Steps

After setting up this integration, you can:

1. Create more sophisticated email automation workflows
2. Integrate with other parts of your agent system
3. Add support for other email providers
4. Implement email reading and processing
