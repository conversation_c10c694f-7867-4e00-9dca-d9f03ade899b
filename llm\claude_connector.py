"""
Claude/Anthropic connector for the Multi-Agent AI System.
"""
from typing import Dict, List, Optional, Any, Union
import asyncio
import json
from datetime import datetime
import aiohttp
from tenacity import retry, stop_after_attempt, wait_exponential

from llm.llm_connector import LLMConnector
from core.logger import setup_logger

class ClaudeConnector(LLMConnector):
    """
    Connector for Claude/Anthropic API.
    """
    
    def __init__(self, config: Dict):
        """
        Initialize the Claude connector.
        
        Args:
            config (Dict): LLM configuration
        """
        self.provider = "anthropic"
        super().__init__(config)
        
        # Claude-specific configuration
        self.api_url = "https://api.anthropic.com/v1"
        self.api_version = "2023-06-01"  # Update as needed
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=1, max=10))
    async def generate_text(
        self,
        prompt: str,
        model: Optional[str] = None,
        max_tokens: int = 1000,
        temperature: float = 0.7,
        stop_sequences: Optional[List[str]] = None,
        **kwargs
    ) -> Dict:
        """
        Generate text using Claude.
        
        Args:
            prompt (str): Input prompt
            model (Optional[str]): Model to use, defaults to default_model
            max_tokens (int): Maximum number of tokens to generate
            temperature (float): Sampling temperature
            stop_sequences (Optional[List[str]]): Sequences that stop generation
            **kwargs: Additional model-specific parameters
            
        Returns:
            Dict: Response containing generated text and metadata
        """
        if not self.enabled:
            return {"error": f"{self.provider} is not enabled"}
        
        if not model:
            model = self.default_model
        
        if model not in self.models:
            self.logger.warning(f"Model {model} not available, using {self.default_model}")
            model = self.default_model
        
        # Convert prompt to Claude's expected format
        system_prompt = kwargs.get("system_prompt", "")
        if system_prompt:
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ]
            return await self.generate_chat(messages, model, max_tokens, temperature, stop_sequences, **kwargs)
        else:
            messages = [{"role": "user", "content": prompt}]
            return await self.generate_chat(messages, model, max_tokens, temperature, stop_sequences, **kwargs)
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=1, max=10))
    async def generate_chat(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        max_tokens: int = 1000,
        temperature: float = 0.7,
        stop_sequences: Optional[List[str]] = None,
        **kwargs
    ) -> Dict:
        """
        Generate a chat response using Claude.
        
        Args:
            messages (List[Dict[str, str]]): List of message dictionaries
            model (Optional[str]): Model to use, defaults to default_model
            max_tokens (int): Maximum number of tokens to generate
            temperature (float): Sampling temperature
            stop_sequences (Optional[List[str]]): Sequences that stop generation
            **kwargs: Additional model-specific parameters
            
        Returns:
            Dict: Response containing generated text and metadata
        """
        if not self.enabled:
            return {"error": f"{self.provider} is not enabled"}
        
        if not model:
            model = self.default_model
        
        if model not in self.models:
            self.logger.warning(f"Model {model} not available, using {self.default_model}")
            model = self.default_model
        
        # Prepare request payload
        payload = {
            "model": model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
        }
        
        # Add stop sequences if provided
        if stop_sequences:
            payload["stop_sequences"] = stop_sequences
        
        # Add additional parameters
        for key, value in kwargs.items():
            if key not in ["system_prompt"]:  # Skip system_prompt as it's handled separately
                payload[key] = value
        
        # Make API request
        start_time = datetime.now()
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.api_url}/messages",
                    headers={
                        "x-api-key": self.api_key,
                        "anthropic-version": self.api_version,
                        "content-type": "application/json",
                    },
                    json=payload,
                ) as response:
                    response_json = await response.json()
                    
                    if response.status != 200:
                        self.logger.error(f"Claude API error: {response.status} - {response_json}")
                        return {
                            "error": f"API error: {response.status}",
                            "details": response_json,
                        }
                    
                    # Process response
                    end_time = datetime.now()
                    latency = (end_time - start_time).total_seconds()
                    
                    return {
                        "text": response_json.get("content", [{"text": ""}])[0].get("text", ""),
                        "model": model,
                        "provider": self.provider,
                        "latency": latency,
                        "raw_response": response_json,
                    }
        
        except Exception as e:
            self.logger.exception(f"Error calling Claude API: {e}")
            return {
                "error": f"API request failed: {str(e)}",
                "model": model,
                "provider": self.provider,
            }
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=1, max=10))
    async def embed_text(
        self,
        text: Union[str, List[str]],
        model: Optional[str] = None,
        **kwargs
    ) -> Dict:
        """
        Generate embeddings for text using Claude.
        
        Note: As of my knowledge cutoff, Claude/Anthropic may not have a dedicated
        embeddings API. This implementation is a placeholder and should be updated
        when such functionality becomes available.
        
        Args:
            text (Union[str, List[str]]): Text to embed
            model (Optional[str]): Model to use, defaults to default_model
            **kwargs: Additional model-specific parameters
            
        Returns:
            Dict: Response containing embeddings and metadata
        """
        # Claude/Anthropic may not have a dedicated embeddings API
        # This is a placeholder implementation
        return {
            "error": "Embeddings not supported by Claude/Anthropic API",
            "provider": self.provider,
        }
