"""
Monitor MPC Servers Script

This script monitors MPC servers and ensures they stay active.
It automatically restarts any inactive servers.
"""
import asyncio
import argparse
import logging
import os
import sys
import signal
import json
import time
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

# Add parent directory to path to import from core
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.logger import setup_logger
from mpc_servers.mpc_server import MPCServer
from mpc_servers.simple_mpc_server import SimpleMPCServer
from mpc_servers.advanced_mpc_server import AdvancedMPCServer

# Set up logger
logger = setup_logger("monitor_mpc_servers")

# Global variables
servers_info = {}
running = True

async def check_server_status(server_id: str, host: str, port: int) -> Dict:
    """
    Check the status of an MPC server.
    
    Args:
        server_id (str): Server ID
        host (str): Server host
        port (int): Server port
        
    Returns:
        Dict: Server status
    """
    try:
        # Create a connection to the server
        reader, writer = await asyncio.open_connection(host, port)
        
        # Send a status check message
        message = json.dumps({"type": "status_check"}).encode()
        writer.write(message)
        await writer.drain()
        
        # Read response
        data = await reader.read(1024)
        response = json.loads(data.decode())
        
        # Close connection
        writer.close()
        await writer.wait_closed()
        
        return {
            "server_id": server_id,
            "status": "active",
            "response": response,
            "last_checked": datetime.now().isoformat()
        }
    except Exception as e:
        logger.warning(f"Error checking server {server_id} status: {e}")
        return {
            "server_id": server_id,
            "status": "inactive",
            "error": str(e),
            "last_checked": datetime.now().isoformat()
        }

async def start_server(server_config: Dict) -> Dict:
    """
    Start an MPC server.
    
    Args:
        server_config (Dict): Server configuration
        
    Returns:
        Dict: Server information
    """
    server_id = server_config.get("id")
    server_type = server_config.get("type", "standard")
    host = server_config.get("host", "0.0.0.0")
    port = server_config.get("port", 8765)
    use_ssl = server_config.get("use_ssl", False)
    cert_file = server_config.get("cert_file")
    key_file = server_config.get("key_file")
    security_tools_dir = server_config.get("security_tools_dir")
    
    logger.info(f"Starting {server_type} MPC Server {server_id} on {host}:{port}")
    
    try:
        # Create server based on type
        if server_type == "simple":
            server = SimpleMPCServer(
                server_id=server_id,
                host=host,
                port=port,
                use_ssl=use_ssl,
                cert_file=cert_file,
                key_file=key_file,
            )
        elif server_type == "advanced":
            server = AdvancedMPCServer(
                server_id=server_id,
                host=host,
                port=port,
                use_ssl=use_ssl,
                cert_file=cert_file,
                key_file=key_file,
                security_tools_dir=security_tools_dir,
            )
        else:  # standard
            server = MPCServer(
                server_id=server_id,
                host=host,
                port=port,
                use_ssl=use_ssl,
                cert_file=cert_file,
                key_file=key_file,
            )
        
        # Start server in a separate task
        task = asyncio.create_task(server.start())
        
        # Return server information
        return {
            "server": server,
            "task": task,
            "config": server_config,
            "start_time": datetime.now().isoformat(),
            "status": "starting"
        }
    except Exception as e:
        logger.exception(f"Error starting MPC Server {server_id}: {e}")
        return {
            "server": None,
            "task": None,
            "config": server_config,
            "start_time": datetime.now().isoformat(),
            "status": "error",
            "error": str(e)
        }

async def monitor_servers(config_file: str, check_interval: int = 60):
    """
    Monitor MPC servers and restart inactive ones.
    
    Args:
        config_file (str): Path to server configuration file
        check_interval (int): Interval between status checks in seconds
    """
    global servers_info, running
    
    # Load configuration
    try:
        with open(config_file, "r") as f:
            config = json.load(f)
    except Exception as e:
        logger.exception(f"Error loading configuration file: {e}")
        return
    
    # Get server configurations
    server_configs = config.get("servers", [])
    
    if not server_configs:
        logger.warning("No server configurations found")
        return
    
    # Start monitoring loop
    while running:
        logger.info("Checking MPC server status...")
        
        # Check each server
        for server_config in server_configs:
            server_id = server_config.get("id")
            host = server_config.get("host", "0.0.0.0")
            port = server_config.get("port", 8765)
            auto_restart = server_config.get("auto_restart", True)
            
            # Check if server is in our list
            if server_id not in servers_info:
                # Start server if not already started
                servers_info[server_id] = await start_server(server_config)
                continue
            
            # Check server status
            status = await check_server_status(server_id, host, port)
            
            # Update server status
            servers_info[server_id]["last_status"] = status
            
            # Restart server if inactive and auto-restart is enabled
            if status["status"] == "inactive" and auto_restart:
                logger.warning(f"Server {server_id} is inactive. Restarting...")
                
                # Stop existing server if any
                if servers_info[server_id]["server"] and servers_info[server_id]["task"]:
                    try:
                        # Cancel task
                        servers_info[server_id]["task"].cancel()
                        
                        # Wait for task to be cancelled
                        try:
                            await asyncio.wait_for(servers_info[server_id]["task"], timeout=5)
                        except asyncio.TimeoutError:
                            logger.warning(f"Timeout waiting for server {server_id} to stop")
                        except asyncio.CancelledError:
                            pass
                    except Exception as e:
                        logger.exception(f"Error stopping server {server_id}: {e}")
                
                # Start server
                servers_info[server_id] = await start_server(server_config)
                logger.info(f"Server {server_id} restarted")
            else:
                logger.info(f"Server {server_id} is {status['status']}")
        
        # Wait before checking again
        try:
            await asyncio.sleep(check_interval)
        except asyncio.CancelledError:
            break

def signal_handler(sig, frame):
    """Handle signals to gracefully shut down."""
    global running
    
    logger.info(f"Received signal {sig}, shutting down...")
    running = False

async def main():
    """Main function."""
    global running
    
    # Parse arguments
    parser = argparse.ArgumentParser(description="Monitor MPC Servers")
    parser.add_argument("--config", default="mpc_servers/config.json", help="Path to server configuration file")
    parser.add_argument("--interval", type=int, default=60, help="Interval between status checks in seconds")
    args = parser.parse_args()
    
    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Start monitoring
    logger.info(f"Starting MPC server monitoring with interval {args.interval} seconds")
    await monitor_servers(args.config, args.interval)
    
    logger.info("MPC server monitoring stopped")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Interrupted by user")
    except Exception as e:
        logger.exception(f"Error in main: {e}")
