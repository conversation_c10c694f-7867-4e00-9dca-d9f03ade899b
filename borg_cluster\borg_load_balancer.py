"""
Borg Load Balancer for the Multi-Agent AI System.

This module provides load balancing capabilities for the system,
distributing tasks across agents and servers based on resource availability.
"""
import asyncio
import json
import logging
import os
from typing import Dict, List, Optional, Any, Union, Set, Tuple
import uuid
from datetime import datetime, timedelta
import heapq
import random

# Add the project root to the Python path
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

from core.logger import setup_logger
from core.state_manager import StateManager
from borg_cluster.borg_resource_manager import BorgResourceManager, ResourceType

# Set up logger
logger = setup_logger("borg_load_balancer")

class TaskPriority:
    """Task priority levels."""
    LOW = 0
    NORMAL = 50
    HIGH = 100
    CRITICAL = 200

class TaskStatus:
    """Task status values."""
    PENDING = "pending"
    ASSIGNED = "assigned"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELED = "canceled"

class BorgLoadBalancer:
    """
    Borg Load Balancer for the Multi-Agent AI System.
    
    This class provides load balancing capabilities for the system,
    distributing tasks across agents and servers based on resource availability.
    """
    
    def __init__(
        self,
        state_manager: StateManager,
        resource_manager: BorgResourceManager,
        config: Dict = None,
    ):
        """
        Initialize the Borg Load Balancer.
        
        Args:
            state_manager (StateManager): System state manager
            resource_manager (BorgResourceManager): Borg resource manager
            config (Dict, optional): Configuration options
        """
        self.state_manager = state_manager
        self.resource_manager = resource_manager
        self.config = config or {}
        
        # Task tracking
        self.tasks = {}
        self.task_queue = []  # Priority queue
        self.agent_tasks = {}  # Tasks assigned to each agent
        self.agent_capabilities = {}  # Capabilities of each agent
        self.agent_performance = {}  # Performance metrics for each agent
        
        # Load balancing configuration
        self.max_tasks_per_agent = self.config.get("max_tasks_per_agent", 5)
        self.task_timeout = self.config.get("task_timeout", 300)  # seconds
        self.rebalance_interval = self.config.get("rebalance_interval", 60)  # seconds
        
        # Background tasks
        self.scheduler_task = None
        self.rebalance_task = None
        
        logger.info("Borg Load Balancer initialized")
    
    async def initialize(self):
        """Initialize the Borg Load Balancer."""
        try:
            # Load existing state if available
            balancer_state = await self.state_manager.get_state("borg", "load_balancer")
            if balancer_state:
                # Restore tasks
                if "tasks" in balancer_state:
                    self.tasks = balancer_state["tasks"]
                
                # Restore agent capabilities
                if "agent_capabilities" in balancer_state:
                    self.agent_capabilities = balancer_state["agent_capabilities"]
                
                # Restore agent performance
                if "agent_performance" in balancer_state:
                    self.agent_performance = balancer_state["agent_performance"]
                
                logger.info("Restored Borg Load Balancer state")
            
            # Rebuild task queue
            await self._rebuild_task_queue()
            
            # Start scheduler task
            self.scheduler_task = asyncio.create_task(self._schedule_tasks())
            
            # Start rebalance task
            self.rebalance_task = asyncio.create_task(self._rebalance_tasks())
            
            logger.info("Borg Load Balancer initialized successfully")
            
        except Exception as e:
            logger.exception(f"Error initializing Borg Load Balancer: {e}")
            raise
    
    async def _rebuild_task_queue(self):
        """Rebuild the task queue from the tasks dictionary."""
        # Clear existing queue
        self.task_queue = []
        
        # Add pending tasks to queue
        for task_id, task in self.tasks.items():
            if task["status"] == TaskStatus.PENDING:
                # Add to queue with priority
                heapq.heappush(
                    self.task_queue,
                    (-task["priority"], task["created_at"], task_id)
                )
        
        # Rebuild agent_tasks dictionary
        self.agent_tasks = {}
        for task_id, task in self.tasks.items():
            if task["status"] in [TaskStatus.ASSIGNED, TaskStatus.RUNNING]:
                agent_id = task["agent_id"]
                if agent_id not in self.agent_tasks:
                    self.agent_tasks[agent_id] = set()
                self.agent_tasks[agent_id].add(task_id)
    
    async def register_agent_capabilities(
        self,
        agent_id: str,
        capabilities: List[str],
    ):
        """
        Register an agent's capabilities.
        
        Args:
            agent_id (str): Agent identifier
            capabilities (List[str]): List of agent capabilities
        """
        self.agent_capabilities[agent_id] = capabilities
        
        # Save state
        await self._save_state()
        
        logger.info(f"Registered capabilities for agent {agent_id}: {capabilities}")
    
    async def update_agent_performance(
        self,
        agent_id: str,
        performance_metrics: Dict,
    ):
        """
        Update an agent's performance metrics.
        
        Args:
            agent_id (str): Agent identifier
            performance_metrics (Dict): Performance metrics
        """
        if agent_id not in self.agent_performance:
            self.agent_performance[agent_id] = {}
        
        # Update metrics
        self.agent_performance[agent_id].update(performance_metrics)
        
        # Add timestamp
        self.agent_performance[agent_id]["last_updated"] = datetime.now().isoformat()
        
        # Save state
        await self._save_state()
        
        logger.debug(f"Updated performance metrics for agent {agent_id}")
    
    async def create_task(
        self,
        task_type: str,
        parameters: Dict,
        priority: int = TaskPriority.NORMAL,
        required_capabilities: List[str] = None,
        deadline: Optional[datetime] = None,
    ) -> str:
        """
        Create a new task.
        
        Args:
            task_type (str): Type of task
            parameters (Dict): Task parameters
            priority (int): Task priority
            required_capabilities (List[str]): Required agent capabilities
            deadline (Optional[datetime]): Task deadline
            
        Returns:
            str: Task identifier
        """
        # Create task ID
        task_id = str(uuid.uuid4())
        
        # Create task
        task = {
            "id": task_id,
            "type": task_type,
            "parameters": parameters,
            "priority": priority,
            "required_capabilities": required_capabilities or [],
            "deadline": deadline.isoformat() if deadline else None,
            "status": TaskStatus.PENDING,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "agent_id": None,
            "result": None,
            "error": None,
        }
        
        # Store task
        self.tasks[task_id] = task
        
        # Add to task queue
        heapq.heappush(
            self.task_queue,
            (-priority, task["created_at"], task_id)
        )
        
        # Save state
        await self._save_state()
        
        logger.info(f"Created task {task_id} of type {task_type} with priority {priority}")
        
        return task_id
    
    async def get_task(self, task_id: str) -> Optional[Dict]:
        """
        Get a task by ID.
        
        Args:
            task_id (str): Task identifier
            
        Returns:
            Optional[Dict]: Task information, or None if not found
        """
        return self.tasks.get(task_id)
    
    async def get_agent_tasks(self, agent_id: str) -> List[Dict]:
        """
        Get tasks assigned to an agent.
        
        Args:
            agent_id (str): Agent identifier
            
        Returns:
            List[Dict]: List of tasks assigned to the agent
        """
        if agent_id not in self.agent_tasks:
            return []
        
        return [
            self.tasks[task_id]
            for task_id in self.agent_tasks[agent_id]
            if task_id in self.tasks
        ]
    
    async def update_task_status(
        self,
        task_id: str,
        status: str,
        result: Any = None,
        error: str = None,
    ):
        """
        Update a task's status.
        
        Args:
            task_id (str): Task identifier
            status (str): New task status
            result (Any, optional): Task result
            error (str, optional): Error message
        """
        if task_id not in self.tasks:
            raise ValueError(f"Task not found: {task_id}")
        
        # Get task
        task = self.tasks[task_id]
        
        # Update task
        task["status"] = status
        task["updated_at"] = datetime.now().isoformat()
        
        if result is not None:
            task["result"] = result
        
        if error is not None:
            task["error"] = error
        
        # Update agent tasks
        if status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELED]:
            agent_id = task.get("agent_id")
            if agent_id and agent_id in self.agent_tasks and task_id in self.agent_tasks[agent_id]:
                self.agent_tasks[agent_id].remove(task_id)
        
        # Save state
        await self._save_state()
        
        logger.info(f"Updated task {task_id} status to {status}")
    
    async def _schedule_tasks(self):
        """Schedule tasks to agents."""
        while True:
            try:
                # Process tasks in priority order
                while self.task_queue:
                    # Peek at highest priority task
                    _, _, task_id = self.task_queue[0]
                    
                    # Check if task exists and is still pending
                    if task_id not in self.tasks or self.tasks[task_id]["status"] != TaskStatus.PENDING:
                        # Remove from queue
                        heapq.heappop(self.task_queue)
                        continue
                    
                    # Find suitable agent
                    agent_id = await self._find_suitable_agent(task_id)
                    
                    if agent_id:
                        # Assign task to agent
                        await self._assign_task(task_id, agent_id)
                        
                        # Remove from queue
                        heapq.heappop(self.task_queue)
                    else:
                        # No suitable agent found, try again later
                        break
                
            except Exception as e:
                logger.exception(f"Error scheduling tasks: {e}")
            
            # Sleep for a while
            await asyncio.sleep(1)
    
    async def _find_suitable_agent(self, task_id: str) -> Optional[str]:
        """
        Find a suitable agent for a task.
        
        Args:
            task_id (str): Task identifier
            
        Returns:
            Optional[str]: Agent identifier, or None if no suitable agent found
        """
        # Get task
        task = self.tasks[task_id]
        
        # Get required capabilities
        required_capabilities = task.get("required_capabilities", [])
        
        # Find agents with required capabilities
        suitable_agents = []
        
        for agent_id, capabilities in self.agent_capabilities.items():
            # Check if agent has all required capabilities
            if all(cap in capabilities for cap in required_capabilities):
                # Check if agent has capacity
                if agent_id not in self.agent_tasks or len(self.agent_tasks[agent_id]) < self.max_tasks_per_agent:
                    suitable_agents.append(agent_id)
        
        if not suitable_agents:
            return None
        
        # Sort agents by performance if available
        if self.agent_performance:
            # Sort by success rate and average completion time
            sorted_agents = sorted(
                suitable_agents,
                key=lambda a: (
                    self.agent_performance.get(a, {}).get("success_rate", 0),
                    -self.agent_performance.get(a, {}).get("avg_completion_time", float("inf")),
                ),
                reverse=True,
            )
            
            return sorted_agents[0]
        else:
            # No performance data, choose randomly
            return random.choice(suitable_agents)
    
    async def _assign_task(self, task_id: str, agent_id: str):
        """
        Assign a task to an agent.
        
        Args:
            task_id (str): Task identifier
            agent_id (str): Agent identifier
        """
        # Get task
        task = self.tasks[task_id]
        
        # Update task
        task["status"] = TaskStatus.ASSIGNED
        task["agent_id"] = agent_id
        task["updated_at"] = datetime.now().isoformat()
        
        # Update agent tasks
        if agent_id not in self.agent_tasks:
            self.agent_tasks[agent_id] = set()
        
        self.agent_tasks[agent_id].add(task_id)
        
        # Save state
        await self._save_state()
        
        logger.info(f"Assigned task {task_id} to agent {agent_id}")
    
    async def _rebalance_tasks(self):
        """Rebalance tasks across agents."""
        while True:
            try:
                # Check for overloaded agents
                for agent_id, task_ids in self.agent_tasks.items():
                    # Skip if agent is not overloaded
                    if len(task_ids) <= self.max_tasks_per_agent:
                        continue
                    
                    # Get tasks for this agent
                    agent_tasks = [
                        (self.tasks[task_id]["priority"], task_id)
                        for task_id in task_ids
                        if task_id in self.tasks
                    ]
                    
                    # Sort by priority (lowest first)
                    agent_tasks.sort()
                    
                    # Rebalance lowest priority tasks
                    tasks_to_rebalance = agent_tasks[:len(task_ids) - self.max_tasks_per_agent]
                    
                    for _, task_id in tasks_to_rebalance:
                        # Reset task to pending
                        await self.update_task_status(task_id, TaskStatus.PENDING)
                        
                        # Add back to queue
                        task = self.tasks[task_id]
                        heapq.heappush(
                            self.task_queue,
                            (-task["priority"], task["created_at"], task_id)
                        )
                        
                        logger.info(f"Rebalanced task {task_id} from agent {agent_id}")
                
            except Exception as e:
                logger.exception(f"Error rebalancing tasks: {e}")
            
            # Sleep for rebalance interval
            await asyncio.sleep(self.rebalance_interval)
    
    async def _save_state(self):
        """Save the current state to the state manager."""
        try:
            # Create state
            state = {
                "tasks": self.tasks,
                "agent_capabilities": self.agent_capabilities,
                "agent_performance": self.agent_performance,
                "last_updated": datetime.now().isoformat(),
            }
            
            # Save state
            await self.state_manager.update_state("borg", "load_balancer", state)
            
        except Exception as e:
            logger.exception(f"Error saving Borg Load Balancer state: {e}")
    
    async def close(self):
        """Close the Borg Load Balancer."""
        try:
            # Cancel background tasks
            if self.scheduler_task:
                self.scheduler_task.cancel()
                try:
                    await self.scheduler_task
                except asyncio.CancelledError:
                    pass
            
            if self.rebalance_task:
                self.rebalance_task.cancel()
                try:
                    await self.rebalance_task
                except asyncio.CancelledError:
                    pass
            
            # Save final state
            await self._save_state()
            
            logger.info("Borg Load Balancer closed")
            
        except Exception as e:
            logger.exception(f"Error closing Borg Load Balancer: {e}")
            raise
