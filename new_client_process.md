# New Client Outreach Process

This document outlines the process for contacting new insurance clients/leads using the AI Agent System.

## Overview

The new client outreach process involves:

1. Sending an <NAME_EMAIL>
2. Sending a text message from the Google Voice number (************)
3. Leaving a voicemail using ElevenLabs female voice
4. Optionally making a call to the client

## Prerequisites

- The AI Agent System must be set up and running
- Gmail service must be <NAME_EMAIL>
- Voice calling service must be configured with the Google Voice number
- ElevenLabs API key must be configured for voice generation

## Process Steps

### 1. Gather Client Information

Collect the following information about the new client:

- Full name
- Email address
- Phone number
- Date of birth (optional)
- Address (optional)
- Insurance type of interest (optional)
- Estimated premium (optional)

### 2. Run the New Client Outreach Script

There are two ways to run the outreach process:

#### Option 1: Using the Interactive Batch Script

Run the `new_client_outreach.bat` script and follow the prompts to enter the client information.

```
new_client_outreach.bat
```

#### Option 2: Using the Python Script Directly

Run the `new_client_outreach.py` script with the appropriate command-line arguments:

```
python new_client_outreach.py --name "Client Name" --email "<EMAIL>" --phone "1234567890" [options]
```

Available options:
- `--dob`: Client's date of birth (MM/DD/YY)
- `--address`: Client's address
- `--insurance-type`: Type of insurance
- `--premium`: Estimated premium amount
- `--agent`: Agent's name (default: Sandra)
- `--quote`: Send quote email (flag)

### 3. Create a Custom Script for a Specific Client

For frequently contacted clients or to automate the process for a specific client, create a custom batch script:

1. Copy the `contact_alyssa_chirinos.bat` script
2. Rename it to match the client's name (e.g., `contact_john_smith.bat`)
3. Edit the script to update the client information

Example:
```batch
@echo off
REM Contact John Smith Script

set CLIENT_NAME=John Smith
set CLIENT_EMAIL=<EMAIL>
set CLIENT_PHONE=1234567890
set CLIENT_DOB=1/1/80
set CLIENT_ADDRESS=Miami, Florida
set INSURANCE_TYPE=Home
set PREMIUM=200
set AGENT_NAME=Sandra

python new_client_outreach.py --name "%CLIENT_NAME%" --email "%CLIENT_EMAIL%" --phone "%CLIENT_PHONE%" --dob "%CLIENT_DOB%" --address "%CLIENT_ADDRESS%" --insurance-type "%INSURANCE_TYPE%" --premium "%PREMIUM%" --agent "%AGENT_NAME%" --quote
```

## Templates

The system uses the following templates for client communication:

### Email Templates

1. **new_client**: Initial email to welcome the client
2. **new_client_quote**: Follow-up email with a preliminary quote

### Text Message Templates

1. **new_client**: Initial text message to welcome the client

### Voicemail Templates

1. **new_client**: Initial voicemail to welcome the client

### Call Templates

1. **new_client**: Initial call script to welcome the client

## Customizing Templates

To customize the templates, edit the `config/communication_services.json` file:

1. For email templates, modify the `email_integration.email_templates` section
2. For text templates, modify the `voice_calling_service.text_templates` section
3. For voicemail templates, modify the `voice_calling_service.voicemail_templates` section
4. For call templates, modify the `voice_calling_service.call_templates` section

## Troubleshooting

If you encounter issues with the outreach process:

1. Check the logs in the `logs` directory
2. Verify that the Gmail service is properly configured
3. Verify that the voice calling service is properly configured
4. Ensure that the ElevenLabs API key is valid
5. Check that the Google Voice number is properly set up

## Additional Resources

- [Gmail API Documentation](https://developers.google.com/gmail/api)
- [ElevenLabs API Documentation](https://docs.elevenlabs.io/api-reference)
- [Twilio API Documentation](https://www.twilio.com/docs/api)
