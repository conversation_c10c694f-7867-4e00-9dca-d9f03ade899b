# AI Agent System - Client Communication Module

This module enables AI agents to communicate with clients through various channels, with a focus on browser automation using UI-TARS 1.5.

## Overview

The Client Communication Module is part of a larger AI agent system that handles various tasks for Flo Faction Insurance, music services, and marketing. It allows agents to:

1. Send personalized emails to clients
2. Use templates for different types of communications
3. Track communication history
4. Adapt to different types of leads (insurance, music, marketing)
5. Reason and adapt during conversations

## Key Components

### Browser Automation Agent

The Browser Automation Agent (`agents/browser_automation_agent.py`) uses UI-TARS 1.5 to automate browser interactions, enabling agents to:

- Navigate to websites
- Fill out forms
- Send emails
- Extract information
- Interact with web applications

### Client Communication Agent

The Client Communication Agent (`agents/client_communication_agent.py`) manages client communications, including:

- Sending emails to clients
- Responding to client inquiries
- Scheduling appointments
- Following up with clients
- Sending quotes and proposals

## Client Data Structure

Clients are stored with their name as the primary identifier, making it natural to refer to them. Each client record includes:

```json
{
  "name": "<PERSON><PERSON>",
  "full_name": "<PERSON><PERSON>",
  "email": "<EMAIL>",
  "phone": "",
  "address": "Bradenton, Florida",
  "dob": "8/16/97",
  "lead_type": "insurance",
  "insurance": {
    "type": "IUL with Dental, Vision, and Basic Health",
    "budget": "$100/month",
    "primary_interest": "IUL",
    "additional_interests": ["dental", "vision", "basic health"],
    "coverage_needs": "Checkups, physicals, and bloodwork",
    "carriers_to_check": "all"
  },
  "notes": "...",
  "status": "lead",
  "last_contact": "",
  "next_contact": "",
  "communication_history": []
}
```

For different lead types (music, marketing), the structure includes relevant fields:

### Music Lead Example
```json
{
  "name": "John",
  "full_name": "John Smith",
  "email": "<EMAIL>",
  "lead_type": "music",
  "music": {
    "services": ["consulting", "management", "production"],
    "budget": "$500/month",
    "genre": "Hip-Hop",
    "goals": "Increase streaming presence and secure sync licensing deals"
  }
}
```

### Marketing Lead Example
```json
{
  "name": "Sarah",
  "full_name": "Sarah Johnson",
  "email": "<EMAIL>",
  "lead_type": "marketing",
  "marketing": {
    "services": ["content creation", "social media", "AI avatars"],
    "budget": "$1000/month",
    "platforms": ["TikTok", "Instagram", "YouTube"],
    "goals": "Increase brand awareness and generate leads"
  }
}
```

## Communication Templates

Templates are used to create personalized communications for different scenarios:

- `initial_contact`: First contact with a client
- `follow_up`: Following up on previous communications
- `quote`: Sending a quote or proposal
- `appointment_confirmation`: Confirming an appointment
- `iul_policy`: Specific template for IUL insurance policies

## Usage

### Contacting a Client

To contact a client, run:

```
contact_client.bat
```

This will prompt you for:
- Client name (e.g., "Alyssa")
- Template to use (e.g., "iul_policy")
- Agent name and email

### Adding a New Client

Clients can be added by updating the `data/clients.json` file with their information.

### Creating New Templates

Templates can be added by updating the `config/communication_templates.json` file.

## Integration with UI-TARS 1.5

The system uses UI-TARS 1.5 for browser automation, which enables agents to:

1. Navigate to Gmail or other communication platforms
2. Compose and send emails
3. Fill out forms
4. Extract information from websites

## Future Enhancements

1. **Music Services Module**: Specialized templates and workflows for music consulting, management, sync licensing, etc.
2. **Marketing Module**: Tools for content creation, AI avatars, and social media management
3. **Voice Communication**: Integration with phone systems for calls and voicemails
4. **SMS Integration**: Ability to send and receive text messages
5. **Calendar Integration**: Automatic scheduling and appointment management

## Requirements

- Python 3.8 or higher
- UI-TARS 1.5
- Chrome, Edge, Firefox, or Brave browser
