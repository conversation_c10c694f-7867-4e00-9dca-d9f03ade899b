"""
Google Sheets Lead Tracker for the Multi-Agent AI System.

This module provides integration with Google Sheets for tracking insurance leads
and their drip campaign status.
"""
import asyncio
import json
import logging
import os
import sys
import time
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
import argparse

# Add parent directory to path to import from core
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.logger import setup_logger
from drip_campaign_workflow import DripCampaignWorkflow

# Set up logger
logger = setup_logger("google_sheets_lead_tracker")

try:
    import gspread
    from google.oauth2.service_account import Credentials
    from gspread_formatting import *
    GSPREAD_AVAILABLE = True
except ImportError:
    logger.warning("gspread or gspread_formatting not installed. Install with: pip install gspread gspread_formatting")
    GSPREAD_AVAILABLE = False

class GoogleSheetsLeadTracker:
    """
    Google Sheets Lead Tracker for the Multi-Agent AI System.

    This class provides integration with Google Sheets for tracking insurance leads
    and their drip campaign status.
    """
    
    def __init__(self, 
                 config_path: str = "config/google_sheets_config.json",
                 credentials_path: str = "credentials/google_sheets/service_account.json",
                 drip_campaign_config_path: str = "config/drip_campaign_config.json"):
        """
        Initialize the Google Sheets Lead Tracker.
        
        Args:
            config_path (str): Path to Google Sheets configuration
            credentials_path (str): Path to Google Sheets API credentials
            drip_campaign_config_path (str): Path to drip campaign configuration
        """
        self.config_path = config_path
        self.credentials_path = credentials_path
        self.drip_campaign_config_path = drip_campaign_config_path
        
        self.config = {}
        self.client = None
        self.spreadsheet = None
        self.worksheet = None
        
        self.drip_campaign_workflow = None
        self.running = False
    
    async def initialize(self) -> bool:
        """
        Initialize the Google Sheets Lead Tracker.
        
        Returns:
            bool: True if initialization was successful, False otherwise
        """
        logger.info("Initializing Google Sheets Lead Tracker")
        
        if not GSPREAD_AVAILABLE:
            logger.error("gspread or gspread_formatting not installed")
            return False
        
        # Load configuration
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, "r", encoding="utf-8") as f:
                    self.config = json.load(f)
                logger.info("Loaded Google Sheets configuration")
            else:
                logger.warning(f"Configuration file not found: {self.config_path}")
                self.config = {
                    "spreadsheet_id": "",
                    "worksheet_name": "Insurance Leads",
                    "check_interval": 300,  # 5 minutes
                    "columns": [
                        "Timestamp",
                        "Name",
                        "Email",
                        "Phone",
                        "Insurance Type",
                        "Budget",
                        "Campaign ID",
                        "Campaign Status",
                        "Last Contact",
                        "Next Contact",
                        "Notes"
                    ]
                }
                
                # Create directory if it doesn't exist
                os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
                
                # Save default configuration
                with open(self.config_path, "w", encoding="utf-8") as f:
                    json.dump(self.config, f, indent=4)
                logger.info(f"Created default configuration file: {self.config_path}")
        except Exception as e:
            logger.exception(f"Error loading configuration: {e}")
            return False
        
        # Initialize Google Sheets client
        try:
            if not os.path.exists(self.credentials_path):
                logger.error(f"Credentials file not found: {self.credentials_path}")
                return False
            
            # Set up credentials
            scopes = [
                "https://www.googleapis.com/auth/spreadsheets",
                "https://www.googleapis.com/auth/drive"
            ]
            
            credentials = Credentials.from_service_account_file(
                self.credentials_path,
                scopes=scopes
            )
            
            # Create client
            self.client = gspread.authorize(credentials)
            
            # Get spreadsheet
            spreadsheet_id = self.config.get("spreadsheet_id")
            if not spreadsheet_id:
                logger.warning("No spreadsheet ID configured")
                return False
            
            self.spreadsheet = self.client.open_by_key(spreadsheet_id)
            
            # Get or create worksheet
            worksheet_name = self.config.get("worksheet_name", "Insurance Leads")
            try:
                self.worksheet = self.spreadsheet.worksheet(worksheet_name)
                logger.info(f"Found worksheet: {worksheet_name}")
            except gspread.exceptions.WorksheetNotFound:
                logger.info(f"Worksheet not found, creating: {worksheet_name}")
                self.worksheet = self.spreadsheet.add_worksheet(
                    title=worksheet_name,
                    rows=1000,
                    cols=20
                )
                
                # Set up header row
                columns = self.config.get("columns", [])
                if columns:
                    self.worksheet.update("A1:K1", [columns])
                    
                    # Format header row
                    fmt = CellFormat(
                        backgroundColor=Color(0.9, 0.9, 0.9),
                        textFormat=TextFormat(bold=True),
                        horizontalAlignment="CENTER"
                    )
                    
                    format_cell_range(self.worksheet, "1:1", fmt)
                    
                    logger.info("Set up header row")
            
            logger.info("Google Sheets client initialized successfully")
            
        except Exception as e:
            logger.exception(f"Error initializing Google Sheets client: {e}")
            return False
        
        # Initialize drip campaign workflow
        self.drip_campaign_workflow = DripCampaignWorkflow(
            config_path=self.drip_campaign_config_path
        )
        
        success = await self.drip_campaign_workflow.initialize()
        if not success:
            logger.error("Failed to initialize drip campaign workflow")
            return False
        
        logger.info("Google Sheets Lead Tracker initialized successfully")
        return True
    
    async def check_new_leads(self) -> List[Dict]:
        """
        Check for new leads from Google Sheets.
        
        Returns:
            List[Dict]: List of new leads
        """
        logger.info("Checking for new leads in Google Sheets")
        
        if not self.worksheet:
            logger.error("Worksheet not initialized")
            return []
        
        new_leads = []
        
        try:
            # Get all records
            records = self.worksheet.get_all_records()
            
            for record in records:
                # Check if this is a new lead (no campaign ID)
                if not record.get("Campaign ID"):
                    # Extract lead information
                    lead = {
                        "name": record.get("Name"),
                        "email": record.get("Email"),
                        "phone": record.get("Phone"),
                        "insurance_type": record.get("Insurance Type"),
                        "budget": record.get("Budget"),
                        "timestamp": record.get("Timestamp"),
                        "row_index": records.index(record) + 2  # +2 for header row and 0-indexing
                    }
                    
                    # Validate required fields
                    if lead["name"] and lead["email"]:
                        new_leads.append(lead)
                    else:
                        logger.warning(f"Incomplete lead data in row {lead['row_index']}")
            
            logger.info(f"Found {len(new_leads)} new leads in Google Sheets")
            return new_leads
            
        except Exception as e:
            logger.exception(f"Error checking for new leads in Google Sheets: {e}")
            return []
    
    async def process_leads(self, leads: List[Dict]) -> None:
        """
        Process leads and start drip campaigns.
        
        Args:
            leads (List[Dict]): List of leads to process
        """
        logger.info(f"Processing {len(leads)} leads from Google Sheets")
        
        for lead in leads:
            try:
                # Start drip campaign
                logger.info(f"Starting drip campaign for Google Sheets lead: {lead['name']}")
                result = await self.drip_campaign_workflow.start_campaign_for_client(
                    client_name=lead["name"],
                    phone_number=lead["phone"] or "5555555555",  # Use placeholder if missing
                    email=lead["email"],
                    insurance_type=lead["insurance_type"] or "Unknown",
                    budget=lead["budget"] or "Unknown"
                )
                
                if result.get("success"):
                    campaign_id = result.get("campaign_id")
                    logger.info(f"Started drip campaign for {lead['name']} with ID {campaign_id}")
                    
                    # Update spreadsheet
                    row_index = lead["row_index"]
                    
                    # Get column indices
                    columns = self.config.get("columns", [])
                    campaign_id_col = columns.index("Campaign ID") + 1 if "Campaign ID" in columns else None
                    campaign_status_col = columns.index("Campaign Status") + 1 if "Campaign Status" in columns else None
                    last_contact_col = columns.index("Last Contact") + 1 if "Last Contact" in columns else None
                    next_contact_col = columns.index("Next Contact") + 1 if "Next Contact" in columns else None
                    
                    # Update campaign ID
                    if campaign_id_col:
                        self.worksheet.update_cell(row_index, campaign_id_col, campaign_id)
                    
                    # Update campaign status
                    if campaign_status_col:
                        self.worksheet.update_cell(row_index, campaign_status_col, "Active")
                    
                    # Update last contact
                    if last_contact_col:
                        self.worksheet.update_cell(row_index, last_contact_col, datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
                    
                    # Update next contact (estimate 5 minutes from now)
                    if next_contact_col:
                        next_contact = (datetime.now() + timedelta(minutes=5)).strftime("%Y-%m-%d %H:%M:%S")
                        self.worksheet.update_cell(row_index, next_contact_col, next_contact)
                    
                    logger.info(f"Updated Google Sheets for lead {lead['name']}")
                else:
                    logger.error(f"Failed to start drip campaign for {lead['name']}: {result.get('error')}")
            
            except Exception as e:
                logger.exception(f"Error processing lead {lead.get('name')}: {e}")
    
    async def update_campaign_status(self) -> None:
        """Update campaign status in Google Sheets."""
        logger.info("Updating campaign status in Google Sheets")
        
        if not self.worksheet:
            logger.error("Worksheet not initialized")
            return
        
        try:
            # Get all records
            records = self.worksheet.get_all_records()
            
            for record in records:
                # Check if this lead has a campaign ID
                campaign_id = record.get("Campaign ID")
                if not campaign_id:
                    continue
                
                # Get campaign status
                campaign = await self.drip_campaign_workflow.drip_campaign_agent.state_manager.get_state(
                    "drip_campaign", 
                    f"active_campaigns.{campaign_id}"
                )
                
                if not campaign:
                    continue
                
                # Get row index
                row_index = records.index(record) + 2  # +2 for header row and 0-indexing
                
                # Get column indices
                columns = self.config.get("columns", [])
                campaign_status_col = columns.index("Campaign Status") + 1 if "Campaign Status" in columns else None
                last_contact_col = columns.index("Last Contact") + 1 if "Last Contact" in columns else None
                next_contact_col = columns.index("Next Contact") + 1 if "Next Contact" in columns else None
                notes_col = columns.index("Notes") + 1 if "Notes" in columns else None
                
                # Update campaign status
                if campaign_status_col:
                    status = campaign.get("status", "Unknown")
                    self.worksheet.update_cell(row_index, campaign_status_col, status)
                
                # Update last contact if available
                if last_contact_col and campaign.get("last_contact"):
                    last_contact = datetime.fromisoformat(campaign["last_contact"]).strftime("%Y-%m-%d %H:%M:%S")
                    self.worksheet.update_cell(row_index, last_contact_col, last_contact)
                
                # Update next contact if available
                if next_contact_col:
                    # Get next scheduled communication
                    next_comm = None
                    scheduled_comms = await self.drip_campaign_workflow.drip_campaign_agent.state_manager.get_state(
                        "drip_campaign", 
                        "scheduled_communications"
                    )
                    
                    if scheduled_comms:
                        for comm_id, comm in scheduled_comms.items():
                            if comm.get("campaign_id") == campaign_id and comm.get("status") == "scheduled":
                                if not next_comm or comm["scheduled_time"] < next_comm["scheduled_time"]:
                                    next_comm = comm
                    
                    if next_comm:
                        next_contact = datetime.fromisoformat(next_comm["scheduled_time"]).strftime("%Y-%m-%d %H:%M:%S")
                        self.worksheet.update_cell(row_index, next_contact_col, next_contact)
                
                # Update notes if available
                if notes_col and campaign.get("notes"):
                    notes = campaign.get("notes", "")
                    self.worksheet.update_cell(row_index, notes_col, notes)
                
                logger.info(f"Updated status for campaign {campaign_id}")
            
        except Exception as e:
            logger.exception(f"Error updating campaign status in Google Sheets: {e}")
    
    async def run(self, check_interval: Optional[int] = None) -> None:
        """
        Run the Google Sheets lead tracker.
        
        Args:
            check_interval (Optional[int]): Interval between checks in seconds
        """
        self.running = True
        
        # Use configured interval or default
        interval = check_interval or self.config.get("check_interval", 300)
        
        logger.info(f"Running Google Sheets lead tracker with interval {interval} seconds")
        
        try:
            while self.running:
                # Check for new leads
                new_leads = await self.check_new_leads()
                
                # Process new leads
                if new_leads:
                    await self.process_leads(new_leads)
                
                # Update campaign status
                await self.update_campaign_status()
                
                # Wait for next check
                logger.info(f"Waiting {interval} seconds before next check")
                await asyncio.sleep(interval)
        
        except asyncio.CancelledError:
            logger.info("Google Sheets lead tracker cancelled")
            self.running = False
        
        except Exception as e:
            logger.exception(f"Error in Google Sheets lead tracker: {e}")
            self.running = False
    
    def stop(self) -> None:
        """Stop the Google Sheets lead tracker."""
        logger.info("Stopping Google Sheets lead tracker")
        self.running = False

async def main():
    """Main function."""
    # Parse arguments
    parser = argparse.ArgumentParser(description="Google Sheets Lead Tracker")
    parser.add_argument("--config", default="config/google_sheets_config.json", help="Path to Google Sheets configuration")
    parser.add_argument("--credentials", default="credentials/google_sheets/service_account.json", help="Path to Google Sheets API credentials")
    parser.add_argument("--drip-campaign-config", default="config/drip_campaign_config.json", help="Path to drip campaign configuration")
    parser.add_argument("--interval", type=int, help="Interval between checks in seconds")
    args = parser.parse_args()
    
    # Create tracker
    tracker = GoogleSheetsLeadTracker(
        config_path=args.config,
        credentials_path=args.credentials,
        drip_campaign_config_path=args.drip_campaign_config
    )
    
    # Initialize
    success = await tracker.initialize()
    if not success:
        logger.error("Failed to initialize Google Sheets lead tracker")
        return 1
    
    try:
        # Run tracker
        await tracker.run(args.interval)
    except KeyboardInterrupt:
        logger.info("Interrupted by user")
        tracker.stop()
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\nInterrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)
