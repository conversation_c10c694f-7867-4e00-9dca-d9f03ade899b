# Insurance Lead Agent: Omnichannel Lead Handler

The Insurance Lead Agent is a specialized agent designed to monitor and respond to insurance leads from multiple channels (Facebook, Instagram, TikTok, and website). It provides real-time responses, qualifies leads, guides them toward scheduling appointments, and tracks all interactions.

## Features

- **Omnichannel Monitoring**: Monitors leads from Facebook, Instagram, TikTok, and website forms
- **Real-time Response**: Responds to leads within 30 seconds when possible
- **Lead Qualification**: Identifies insurance type needs through conversation
- **Appointment Booking**: Provides personalized Calendly booking links based on insurance type
- **Comprehensive Logging**: Tracks all interactions with standardized logging format
- **Error Handling**: Implements retry logic and escalation paths for failures
- **Security**: Includes fraud detection and credential recovery mechanisms

## Configuration

The agent is configured through the `config/lead_agent_config.json` file, which includes:

- Channel-specific settings (Facebook, Instagram, TikTok, website)
- Response templates for different scenarios
- Booking links for different insurance types
- Escalation settings
- Security settings
- Logging format

## Usage

### Initializing the Agent

```python
from agents.insurance_lead_agent import InsuranceLeadAgent
from core.state_manager import StateManager
from llm.llm_router import LLMRouter

# Create state manager
state_manager = StateManager()
await state_manager.initialize()

# Create LLM router
llm_router = LLMRouter()
await llm_router.initialize()

# Create message queue and shutdown event
message_queue = asyncio.Queue()
shutdown_event = asyncio.Event()

# Load lead agent configuration
with open("config/lead_agent_config.json", "r") as f:
    lead_config = json.load(f)

# Create agent configuration
agent_config = {
    "name": "Insurance Lead Agent",
    "description": "Handles leads from multiple channels",
    "llm_provider": "anthropic",
    "lead_agent_config": lead_config
}

# Create and initialize the agent
agent = InsuranceLeadAgent(
    agent_id="insurance_lead_agent_1",
    config=agent_config,
    state_manager=state_manager,
    message_queue=message_queue,
    shutdown_event=shutdown_event
)

# Set LLM router
agent.llm_router = llm_router

# Initialize the agent
await agent.initialize()
```

### Handling Leads

```python
# Handle a Facebook lead
facebook_lead = {
    "lead_id": "facebook-12345",
    "user_handle": "John Smith",
    "message": "Hi, I'm looking for auto insurance. Can you help?"
}

result = await agent.handle_lead("facebook", facebook_lead)
```

### Running the Agent Cycle

```python
# Run the agent cycle to process pending responses
await agent.execute_cycle()
```

## Response Format

The agent follows a structured response format:

1. **Greeting**: "Hi [First Name], thanks for reaching out!"
2. **Qualification**: "Can I ask what kind of insurance you're looking for?"
3. **Info Response**: Brief answer to the question
4. **Booking CTA**: "Let's get you scheduled – here's a link: [insert link]"
5. **Error Handling**: "Having trouble at the moment – I've flagged this and someone will follow up!"

## Logging Format

All interactions are logged in the following format:

```
[Date | Time] | [Channel] | [UserHandle/ID] | [Summary] | [Status: success/failure/escalated]
```

## Channel Integration

The agent integrates with multiple channels:

### Facebook

Uses the Facebook Messenger API to monitor and respond to messages.

### Instagram

Uses the Instagram Direct API to monitor and respond to direct messages.

### TikTok

Uses the TikTok Messaging API to monitor and respond to messages.

### Website

Monitors form submissions and responds via email or SMS.

## Error Handling

The agent implements robust error handling:

1. **Retry Logic**: Attempts to retry failed responses once
2. **Escalation**: Escalates to human operators after failed retries
3. **Credential Recovery**: Handles authentication failures gracefully
4. **Fraud Detection**: Flags potential fraud or toxic messages

## Security

The agent includes security features:

1. **Fraud Detection**: Identifies potential fraud based on keywords
2. **Toxic Content Detection**: Flags potentially inappropriate messages
3. **Credential Management**: Securely handles API credentials
4. **Privacy Compliance**: Ensures GDPR/CCPA compliance in data handling

## Example

See `examples/insurance_lead_agent_example.py` for a complete example of using the Insurance Lead Agent.
