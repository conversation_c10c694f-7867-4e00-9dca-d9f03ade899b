"""
Security Tools Manager for the Multi-Agent AI System.

This module provides a centralized manager for security tools, ensuring
that all agents have access to tools like <PERSON>per, <PERSON><PERSON><PERSON>, etc.
"""
import asyncio
import json
import logging
import os
import subprocess
import platform
import shutil
import sys
from typing import Dict, List, Optional, Any, Union
import uuid
from datetime import datetime
import aiohttp

# Add parent directory to path to import from core
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.logger import setup_logger

# Set up logger
logger = setup_logger("security_tools_manager")

class SecurityToolsManager:
    """
    Manager for security tools.
    
    This class provides a centralized manager for security tools, ensuring
    that all agents have access to tools like <PERSON> the Ripper, Nmap, etc.
    """
    
    def __init__(self, config: Dict):
        """
        Initialize the security tools manager.
        
        Args:
            config (Dict): Security tools configuration
        """
        self.config = config
        self.enabled = config.get("enabled", True)
        self.tools_dir = config.get("tools_dir", "./tools")
        self.tool_registry = config.get("tool_registry", {})
        
        # Platform-specific settings
        self.platform = platform.system().lower()
        
        # Tool status
        self.tool_status = {}
        
        logger.info("Security tools manager initialized")
    
    async def initialize(self):
        """Initialize the security tools manager."""
        if not self.enabled:
            logger.warning("Security tools manager is disabled")
            return
        
        try:
            # Create tools directory if it doesn't exist
            os.makedirs(self.tools_dir, exist_ok=True)
            
            # Check installed tools
            await self._check_installed_tools()
            
            # Install missing tools if auto_install is enabled
            if self.config.get("auto_install", True):
                await self._install_missing_tools()
            
            logger.info("Security tools manager initialized")
            
        except Exception as e:
            logger.exception(f"Error initializing security tools manager: {e}")
            self.enabled = False
    
    async def _check_installed_tools(self):
        """Check which security tools are installed."""
        for tool_id, tool_config in self.tool_registry.items():
            if tool_config.get("enabled", True):
                installed = await self.check_tool_installed(tool_id)
                
                self.tool_status[tool_id] = {
                    "installed": installed,
                    "checked_at": datetime.now().isoformat(),
                }
                
                if installed:
                    logger.info(f"Tool {tool_id} is installed")
                else:
                    logger.warning(f"Tool {tool_id} is not installed")
    
    async def _install_missing_tools(self):
        """Install missing security tools."""
        for tool_id, status in self.tool_status.items():
            if not status.get("installed", False):
                tool_config = self.tool_registry.get(tool_id, {})
                
                if tool_config.get("enabled", True) and tool_config.get("auto_install", True):
                    logger.info(f"Installing missing tool: {tool_id}")
                    
                    success = await self.install_tool(tool_id)
                    
                    if success:
                        logger.info(f"Successfully installed {tool_id}")
                        self.tool_status[tool_id]["installed"] = True
                    else:
                        logger.warning(f"Failed to install {tool_id}")
    
    async def check_tool_installed(self, tool_id: str) -> bool:
        """
        Check if a security tool is installed.
        
        Args:
            tool_id (str): Tool identifier
            
        Returns:
            bool: True if installed, False otherwise
        """
        if tool_id not in self.tool_registry:
            logger.warning(f"Unknown tool: {tool_id}")
            return False
        
        tool_config = self.tool_registry[tool_id]
        
        # Get check command based on platform
        check_cmd = None
        if self.platform == "windows":
            check_cmd = tool_config.get("windows", {}).get("check_cmd")
        elif self.platform == "linux":
            check_cmd = tool_config.get("linux", {}).get("check_cmd")
        elif self.platform == "darwin":
            check_cmd = tool_config.get("darwin", {}).get("check_cmd")
        
        # If no platform-specific check command, use generic one
        if not check_cmd:
            check_cmd = tool_config.get("check_cmd")
        
        if not check_cmd:
            logger.warning(f"No check command for tool {tool_id} on platform {self.platform}")
            return False
        
        try:
            # Run check command
            process = await asyncio.create_subprocess_shell(
                check_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            
            # Check if command succeeded
            if process.returncode == 0:
                logger.info(f"Tool {tool_id} is installed")
                return True
            else:
                logger.warning(f"Tool {tool_id} check failed: {stderr.decode()}")
                return False
                
        except Exception as e:
            logger.exception(f"Error checking if tool {tool_id} is installed: {e}")
            return False
    
    async def install_tool(self, tool_id: str) -> bool:
        """
        Install a security tool.
        
        Args:
            tool_id (str): Tool identifier
            
        Returns:
            bool: True if installed successfully, False otherwise
        """
        if tool_id not in self.tool_registry:
            logger.warning(f"Unknown tool: {tool_id}")
            return False
        
        tool_config = self.tool_registry[tool_id]
        
        # Get install command based on platform
        install_cmd = None
        if self.platform == "windows":
            install_cmd = tool_config.get("windows", {}).get("install_cmd")
        elif self.platform == "linux":
            install_cmd = tool_config.get("linux", {}).get("install_cmd")
        elif self.platform == "darwin":
            install_cmd = tool_config.get("darwin", {}).get("install_cmd")
        
        # If no platform-specific install command, use generic one
        if not install_cmd:
            install_cmd = tool_config.get("install_cmd")
        
        if not install_cmd:
            logger.warning(f"No install command for tool {tool_id} on platform {self.platform}")
            return False
        
        try:
            # Run install command
            process = await asyncio.create_subprocess_shell(
                install_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            
            # Check if command succeeded
            if process.returncode == 0:
                logger.info(f"Tool {tool_id} installed successfully")
                
                # Update tool status
                self.tool_status[tool_id] = {
                    "installed": True,
                    "installed_at": datetime.now().isoformat(),
                }
                
                return True
            else:
                logger.warning(f"Tool {tool_id} installation failed: {stderr.decode()}")
                return False
                
        except Exception as e:
            logger.exception(f"Error installing tool {tool_id}: {e}")
            return False
    
    async def run_tool(self, tool_id: str, args: List[str] = None) -> Dict:
        """
        Run a security tool.
        
        Args:
            tool_id (str): Tool identifier
            args (List[str]): Tool arguments
            
        Returns:
            Dict: Tool execution results
        """
        if not self.enabled:
            return {"error": "Security tools manager is disabled"}
        
        if tool_id not in self.tool_registry:
            return {"error": f"Unknown tool: {tool_id}"}
        
        tool_config = self.tool_registry[tool_id]
        
        if not tool_config.get("enabled", True):
            return {"error": f"Tool {tool_id} is disabled"}
        
        # Check if tool is installed
        installed = await self.check_tool_installed(tool_id)
        if not installed:
            # Try to install tool
            if tool_config.get("auto_install", True):
                logger.info(f"Tool {tool_id} not installed, attempting to install")
                installed = await self.install_tool(tool_id)
                
                if not installed:
                    return {"error": f"Tool {tool_id} is not installed and installation failed"}
            else:
                return {"error": f"Tool {tool_id} is not installed"}
        
        # Get tool command based on platform
        tool_cmd = None
        if self.platform == "windows":
            tool_cmd = tool_config.get("windows", {}).get("cmd")
        elif self.platform == "linux":
            tool_cmd = tool_config.get("linux", {}).get("cmd")
        elif self.platform == "darwin":
            tool_cmd = tool_config.get("darwin", {}).get("cmd")
        
        # If no platform-specific command, use generic one
        if not tool_cmd:
            tool_cmd = tool_config.get("cmd")
        
        if not tool_cmd:
            return {"error": f"No command for tool {tool_id} on platform {self.platform}"}
        
        # Prepare command with arguments
        if args:
            cmd = [tool_cmd] + args
        else:
            cmd = [tool_cmd]
        
        try:
            # Run tool
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            
            # Prepare result
            result = {
                "tool": tool_id,
                "command": " ".join(cmd),
                "returncode": process.returncode,
                "stdout": stdout.decode(),
                "stderr": stderr.decode(),
                "timestamp": datetime.now().isoformat(),
            }
            
            return result
                
        except Exception as e:
            logger.exception(f"Error running tool {tool_id}: {e}")
            return {"error": str(e)}
    
    async def get_tool_status(self) -> Dict:
        """
        Get the status of all security tools.
        
        Returns:
            Dict: Tool status
        """
        return self.tool_status
    
    async def ensure_tool_accessible(self, tool_id: str) -> bool:
        """
        Ensure a security tool is accessible to all agents.
        
        Args:
            tool_id (str): Tool identifier
            
        Returns:
            bool: True if tool is accessible, False otherwise
        """
        if not self.enabled:
            logger.warning("Security tools manager is disabled")
            return False
        
        if tool_id not in self.tool_registry:
            logger.warning(f"Unknown tool: {tool_id}")
            return False
        
        # Check if tool is installed
        installed = await self.check_tool_installed(tool_id)
        if not installed:
            # Try to install tool
            tool_config = self.tool_registry[tool_id]
            if tool_config.get("auto_install", True):
                logger.info(f"Tool {tool_id} not installed, attempting to install")
                installed = await self.install_tool(tool_id)
                
                if not installed:
                    logger.warning(f"Tool {tool_id} installation failed")
                    return False
            else:
                logger.warning(f"Tool {tool_id} is not installed")
                return False
        
        # Ensure tool is accessible to all agents
        # This might involve setting permissions, creating symlinks, etc.
        # For now, we just check if the tool is installed
        
        return True
