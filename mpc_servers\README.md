# Multi-Party Computation (MPC) Servers

This directory contains the implementation of Multi-Party Computation (MPC) servers and clients for the Multi-Agent AI System. MPC allows multiple parties to jointly compute a function over their inputs while keeping those inputs private.

## Overview

MPC is a cryptographic technique that enables multiple parties to jointly compute a function over their inputs while keeping those inputs private. This is particularly useful for scenarios where multiple agents need to collaborate on sensitive data without revealing that data to each other.

## Components

- `mpc_server.py`: Implementation of the MPC server
- `mpc_client.py`: Implementation of the MPC client
- `simple_mpc_server.py`: A simplified MPC server for testing

## Usage

### Starting an MPC Server

To start a local MPC server:

```python
from mpc_servers.mpc_server import MPCServer
import asyncio

async def start_server():
    server = MPCServer(
        server_id="local",
        host="localhost",
        port=8765,
        use_ssl=False
    )
    await server.start()

asyncio.run(start_server())
```

For a secure server with SSL:

```python
from mpc_servers.mpc_server import MPCServer
import asyncio

async def start_secure_server():
    server = MPCServer(
        server_id="secure",
        host="0.0.0.0",
        port=8765,
        use_ssl=True,
        cert_file="path/to/cert.pem",
        key_file="path/to/key.pem"
    )
    await server.start()

asyncio.run(start_secure_server())
```

### Connecting to an MPC Server

To connect to an MPC server:

```python
from mpc_servers.mpc_client import MPCClient
import asyncio

async def connect_to_server():
    client = MPCClient(
        client_id="client1",
        server_host="localhost",
        server_port=8765,
        use_ssl=False
    )
    await client.connect()
    
    # Create a computation
    computation_id = await client.create_computation(
        computation_type="secure_sum",
        parameters={"num_parties": 3}
    )
    
    # Submit input
    await client.submit_input(
        computation_id=computation_id,
        input_data=42
    )
    
    # Get result
    result = await client.get_result(computation_id)
    print(f"Result: {result}")
    
    await client.disconnect()

asyncio.run(connect_to_server())
```

## Supported Computation Types

The MPC server supports various types of secure computations:

1. **Secure Sum**: Compute the sum of inputs from multiple parties without revealing individual inputs
2. **Secure Average**: Compute the average of inputs from multiple parties
3. **Secure Minimum/Maximum**: Find the minimum or maximum value among inputs
4. **Secure Set Intersection**: Find the intersection of sets without revealing the sets
5. **Secure Machine Learning**: Train machine learning models on distributed data

## Security Features

- **Encryption**: All communications are encrypted using TLS/SSL
- **Authentication**: Clients are authenticated using session keys
- **Input Privacy**: Inputs from different parties are kept private
- **Computation Integrity**: Results are verified for correctness

## Integration with Agents

Agents can use MPC for secure collaborative computations. For example:

```python
# In an agent's execute_cycle method
async def execute_cycle(self):
    # Get MPC client from services
    mpc_client = self.get_service("mpc_client")
    
    if mpc_client and mpc_client.is_connected():
        # Create a secure computation
        computation_id = await mpc_client.create_computation(
            computation_type="secure_sum",
            parameters={"num_parties": 3}
        )
        
        # Submit the agent's private input
        await mpc_client.submit_input(
            computation_id=computation_id,
            input_data=self.private_data
        )
        
        # Wait for result
        result = await mpc_client.get_result(computation_id)
        
        # Use the result for decision making
        if result > threshold:
            await self.take_action()
```

## External MPC Servers

The system can also connect to external MPC servers for more advanced computations:

1. [SCALE-MAMBA](https://github.com/KULeuven-COSIC/SCALE-MAMBA): A framework for secure multi-party computation
2. [MP-SPDZ](https://github.com/data61/MP-SPDZ): A versatile framework for multi-party computation
3. [Sharemind](https://sharemind.cyber.ee/): A commercial MPC platform

## Configuration

MPC servers and clients can be configured in the `.env` file:

```
# MPC (Multi-Party Computation) server settings
ENABLE_MPC=True
MPC_LOCAL_HOST=localhost
MPC_LOCAL_PORT=8765
MPC_LOCAL_USE_SSL=False
ENABLE_MPC_LOCAL=True

MPC_REMOTE_HOST=example.com
MPC_REMOTE_PORT=8765
MPC_REMOTE_USE_SSL=True
MPC_REMOTE_CA_FILE=path/to/ca.pem
ENABLE_MPC_REMOTE=False
```
