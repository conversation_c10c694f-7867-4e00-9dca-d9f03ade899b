# UI-TARS Browser Fix and Insurance Drip Campaign

This package provides tools to fix UI-TARS browser integration issues and start an insurance drip campaign for Alyssa <PERSON>s.

## Overview

UI-TARS 1.5 is a powerful tool for browser automation, but it can sometimes have issues with browser detection and integration. This package provides a simple solution to fix these issues and start using UI-TARS for insurance drip campaigns.

## Prerequisites

Before using this package, make sure you have:

1. Python 3.8 or higher installed
2. Google Chrome installed
3. UI-TARS 1.5 installed
4. Gmail account credentials (email and password)

## Quick Start

The easiest way to fix UI-TARS browser integration issues and start the insurance drip campaign is to follow these steps:

1. Run the simple fix script:
   ```
   run_simple_fix.bat
   ```

2. Start Alyssa's insurance drip campaign:
   ```
   start_alyssa_campaign_with_fixed_ui_tars.bat
   ```

## Tools Included

### 1. Simple Fix for UI-TARS Browser Integration Issues

This tool provides a simple fix for UI-TARS browser integration issues by:

1. Detecting installed browsers
2. Finding the UI-TARS installation
3. Creating a configuration file that properly configures UI-TARS to use the detected browser

```
python simple_fix_ui_tars_browser.py
```

### 2. Start Alyssa's Insurance Drip Campaign

This tool starts the insurance drip campaign for Alyssa Chirinos using the fixed UI-TARS configuration:

```
python start_alyssa_campaign_with_fixed_ui_tars.py --config "path/to/config.json" --ui-tars-path "path/to/UI-TARS.exe"
```

Options:
- `--config`: Path to UI-TARS configuration file (optional, will auto-detect if not provided)
- `--ui-tars-path`: Path to UI-TARS executable (optional, will auto-detect if not provided)

## Batch Files

For convenience, the following batch files are provided:

- `run_simple_fix.bat`: Run the simple fix script
- `start_alyssa_campaign_with_fixed_ui_tars.bat`: Start Alyssa's insurance drip campaign

## Troubleshooting

### UI-TARS Not Starting

If UI-TARS fails to start after fixing browser detection issues:

1. Check the logs in the `simple_fix_ui_tars_browser.log` file
2. Make sure the UI-TARS executable path is correct
3. Try running with the `--ui-tars-path` option to specify the path explicitly

### Browser Not Detected

If the browser is not detected:

1. Make sure Chrome is installed
2. Check if Chrome is in one of the standard installation locations
3. If Chrome is installed in a non-standard location, you may need to modify the script

### Email Not Sending

If the email to Alyssa is not being sent:

1. Check the logs in the `start_alyssa_campaign.log` file
2. Make sure the Gmail credentials are correct
3. Make sure UI-TARS is properly configured to use Chrome
4. Try running the script with the `--config` and `--ui-tars-path` options to specify the paths explicitly

## Advanced Usage

### Using a Different Browser

To use a different browser, modify the `simple_fix_ui_tars_browser.py` script to detect and use the desired browser.

### Customizing the Email Content

To customize the email content, modify the `start_alyssa_campaign_with_fixed_ui_tars.py` script to change the subject and body of the email.

### Adding More Clients

To add more clients to the drip campaign, create additional scripts based on the `start_alyssa_campaign_with_fixed_ui_tars.py` script with different client information.

## Next Steps

After fixing UI-TARS browser integration issues and starting the insurance drip campaign, you can:

1. Monitor the campaign progress
2. Handle client responses
3. Schedule follow-up communications
4. Add more clients to the campaign

## License

This package is licensed under the Apache 2.0 License.
