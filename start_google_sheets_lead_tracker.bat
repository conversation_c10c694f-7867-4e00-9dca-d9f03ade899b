@echo off
REM Start Google Sheets Lead Tracker
REM This script starts the Google Sheets lead tracker to automatically process leads from Google Sheets

echo.
echo ===================================
echo    Start Google Sheets Lead Tracker
echo ===================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Check if gspread is installed
pip show gspread >nul 2>&1
if %errorlevel% neq 0 (
    echo gspread is not installed. Installing...
    pip install gspread gspread_formatting
    if %errorlevel% neq 0 (
        echo Failed to install gspread. Please install manually: pip install gspread gspread_formatting
        exit /b 1
    )
    echo gspread installed successfully.
)

REM Set configuration file paths
set CONFIG_FILE=config/google_sheets_config.json
set CREDENTIALS_FILE=credentials/google_sheets/service_account.json
set DRIP_CAMPAIGN_CONFIG=config/drip_campaign_config.json

REM Check if credentials file exists
if not exist "%CREDENTIALS_FILE%" (
    echo Google Sheets API credentials file not found: %CREDENTIALS_FILE%
    echo Creating directory structure...
    
    REM Create directory if it doesn't exist
    if not exist "credentials\google_sheets" mkdir credentials\google_sheets
    
    echo.
    echo You need to create a service account in the Google Cloud Console and download the credentials file.
    echo 1. Go to https://console.cloud.google.com/
    echo 2. Create a new project or select an existing one
    echo 3. Enable the Google Sheets API and Google Drive API
    echo 4. Create a service account
    echo 5. Create a key for the service account (JSON format)
    echo 6. Download the key and save it as: %CREDENTIALS_FILE%
    echo 7. Share your Google Sheet with the service account email address
    echo.
    
    set /p CONTINUE="Do you want to continue without credentials? (y/n): "
    if /i not "%CONTINUE%"=="y" (
        echo Google Sheets lead tracker cancelled.
        goto end
    )
)

REM Check if configuration file exists
if not exist "%CONFIG_FILE%" (
    echo Configuration file not found: %CONFIG_FILE%
    echo Creating directory structure...
    
    REM Create directory if it doesn't exist
    if not exist "config" mkdir config
    
    echo Creating template configuration file...
    
    REM Create template configuration file
    echo {> "%CONFIG_FILE%"
    echo     "spreadsheet_id": "",>> "%CONFIG_FILE%"
    echo     "worksheet_name": "Insurance Leads",>> "%CONFIG_FILE%"
    echo     "check_interval": 300,>> "%CONFIG_FILE%"
    echo     "columns": [>> "%CONFIG_FILE%"
    echo         "Timestamp",>> "%CONFIG_FILE%"
    echo         "Name",>> "%CONFIG_FILE%"
    echo         "Email",>> "%CONFIG_FILE%"
    echo         "Phone",>> "%CONFIG_FILE%"
    echo         "Insurance Type",>> "%CONFIG_FILE%"
    echo         "Budget",>> "%CONFIG_FILE%"
    echo         "Campaign ID",>> "%CONFIG_FILE%"
    echo         "Campaign Status",>> "%CONFIG_FILE%"
    echo         "Last Contact",>> "%CONFIG_FILE%"
    echo         "Next Contact",>> "%CONFIG_FILE%"
    echo         "Notes">> "%CONFIG_FILE%"
    echo     ]>> "%CONFIG_FILE%"
    echo }>> "%CONFIG_FILE%"
    
    echo Template configuration file created: %CONFIG_FILE%
    echo Please edit this file to add your Google Sheets spreadsheet ID.
    echo.
    
    set /p SPREADSHEET_ID="Enter your Google Sheets spreadsheet ID: "
    if not "%SPREADSHEET_ID%"=="" (
        powershell -Command "(Get-Content '%CONFIG_FILE%') -replace '\"spreadsheet_id\": \"\"', '\"spreadsheet_id\": \"%SPREADSHEET_ID%\"' | Set-Content '%CONFIG_FILE%'"
        echo Updated spreadsheet ID in configuration file.
    )
)

REM Set check interval
set /p CHECK_INTERVAL="Enter check interval in seconds (default: 300): "
if "%CHECK_INTERVAL%"=="" set CHECK_INTERVAL=300

REM Start tracker
echo.
echo Starting Google Sheets lead tracker with interval %CHECK_INTERVAL% seconds...
echo.

start "Google Sheets Lead Tracker" cmd /k python google_sheets_lead_tracker.py --config "%CONFIG_FILE%" --credentials "%CREDENTIALS_FILE%" --drip-campaign-config "%DRIP_CAMPAIGN_CONFIG%" --interval %CHECK_INTERVAL%

echo.
echo Google Sheets lead tracker started in a new window.
echo.
echo To stop tracker, close the tracker window or press Ctrl+C in that window.
echo.

:end
pause
