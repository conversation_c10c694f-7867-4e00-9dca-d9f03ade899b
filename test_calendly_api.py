"""
Test Calendly API

This script tests the Calendly API to verify that the API key works.
"""
import os
import sys
import json
import argparse
import requests
from typing import Dict, Any, Optional

def test_api_key(token: str) -> bool:
    """
    Test if the Calendly API key works.
    
    Args:
        token (str): Calendly API key
        
    Returns:
        bool: True if the API key works, False otherwise
    """
    try:
        # Make a request to the Calendly API
        response = requests.get(
            "https://api.calendly.com/users/me",
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}"
            }
        )
        
        # Check if the request was successful
        if response.status_code == 200:
            user_info = response.json()
            print("API key is valid!")
            print(f"User: {user_info['resource']['name']} ({user_info['resource']['email']})")
            return True
        else:
            print(f"API key is invalid: {response.status_code}")
            print(response.text)
            return False
    
    except Exception as e:
        print(f"Error testing API key: {e}")
        return False

def get_event_types(token: str) -> Optional[Dict[str, Any]]:
    """
    Get event types from Calendly.
    
    Args:
        token (str): Calendly API key
        
    Returns:
        Optional[Dict[str, Any]]: Event types or None if an error occurred
    """
    try:
        # Make a request to the Calendly API
        response = requests.get(
            "https://api.calendly.com/event_types",
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}"
            }
        )
        
        # Check if the request was successful
        if response.status_code == 200:
            event_types = response.json()
            print(f"Found {len(event_types['collection'])} event types:")
            
            for i, event_type in enumerate(event_types['collection']):
                print(f"{i+1}. {event_type['name']} ({event_type['duration']} minutes)")
                print(f"   URI: {event_type['uri']}")
                print(f"   URL: {event_type['scheduling_url']}")
            
            return event_types
        else:
            print(f"Error getting event types: {response.status_code}")
            print(response.text)
            return None
    
    except Exception as e:
        print(f"Error getting event types: {e}")
        return None

def get_scheduled_events(token: str) -> Optional[Dict[str, Any]]:
    """
    Get scheduled events from Calendly.
    
    Args:
        token (str): Calendly API key
        
    Returns:
        Optional[Dict[str, Any]]: Scheduled events or None if an error occurred
    """
    try:
        # Make a request to the Calendly API
        response = requests.get(
            "https://api.calendly.com/scheduled_events",
            params={
                "count": 10,
                "status": "active"
            },
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}"
            }
        )
        
        # Check if the request was successful
        if response.status_code == 200:
            scheduled_events = response.json()
            print(f"Found {len(scheduled_events['collection'])} scheduled events:")
            
            for i, event in enumerate(scheduled_events['collection']):
                print(f"{i+1}. {event['name']}")
                print(f"   Start: {event['start_time']}")
                print(f"   End: {event['end_time']}")
                print(f"   URI: {event['uri']}")
            
            return scheduled_events
        else:
            print(f"Error getting scheduled events: {response.status_code}")
            print(response.text)
            return None
    
    except Exception as e:
        print(f"Error getting scheduled events: {e}")
        return None

def save_event_types_to_config(token: str, event_types: Dict[str, Any]):
    """
    Save event types to the Calendly configuration file.
    
    Args:
        token (str): Calendly API key
        event_types (Dict[str, Any]): Event types from Calendly API
    """
    try:
        # Load existing configuration
        config_path = "credentials/calendly/calendly.json"
        
        if os.path.exists(config_path):
            with open(config_path, "r") as f:
                config = json.load(f)
        else:
            config = {
                "api_key": token,
                "event_types": {}
            }
        
        # Initialize event_types if it doesn't exist
        if "event_types" not in config:
            config["event_types"] = {}
        
        # Map event types
        for event_type in event_types['collection']:
            name = event_type['name'].lower()
            uri = event_type['uri']
            url = event_type['scheduling_url']
            
            # Try to map to insurance types
            insurance_types = [
                "auto", "home", "life", "health", "business", 
                "renters", "umbrella", "flood", "pet"
            ]
            
            mapped = False
            
            for insurance_type in insurance_types:
                if insurance_type in name:
                    config["event_types"][insurance_type] = {
                        "name": event_type['name'],
                        "uri": uri,
                        "url": url
                    }
                    mapped = True
                    print(f"Mapped {name} to {insurance_type}")
            
            # If not mapped to a specific insurance type, check if it's the default
            if not mapped and ("15" in name or "consultation" in name.lower()):
                config["event_types"]["default"] = {
                    "name": event_type['name'],
                    "uri": uri,
                    "url": url
                }
                print(f"Mapped {name} to default")
        
        # Save configuration
        with open(config_path, "w") as f:
            json.dump(config, f, indent=4)
        
        print(f"Saved event types to {config_path}")
    
    except Exception as e:
        print(f"Error saving event types to config: {e}")

def main():
    """Test the Calendly API."""
    parser = argparse.ArgumentParser(description="Test Calendly API")
    parser.add_argument("--token", type=str, help="Calendly API key")
    parser.add_argument("--event-types", action="store_true", help="Get event types")
    parser.add_argument("--scheduled-events", action="store_true", help="Get scheduled events")
    parser.add_argument("--save-config", action="store_true", help="Save event types to config")
    args = parser.parse_args()
    
    # If token is not provided, try to load from credentials file
    token = args.token
    if not token:
        try:
            with open("credentials/calendly/calendly.json", "r") as f:
                credentials = json.load(f)
                token = credentials.get("api_key", "")
        except Exception as e:
            print(f"Error loading credentials: {e}")
    
    if not token:
        print("Calendly API key is required")
        print("Please provide a token using the --token argument")
        print("You can find your token in your Calendly account settings")
        return
    
    # Test API key
    if not test_api_key(token):
        return
    
    # Get event types
    if args.event_types or args.save_config:
        event_types = get_event_types(token)
        
        if args.save_config and event_types:
            save_event_types_to_config(token, event_types)
    
    # Get scheduled events
    if args.scheduled_events:
        get_scheduled_events(token)

if __name__ == "__main__":
    main()
