"""
Fix OAuth settings for Gmail authentication.
This script helps you fix OAuth settings for Gmail authentication.
"""
import os
import sys
import webbrowser
from pathlib import Path

def fix_oauth_settings():
    """
    Fix OAuth settings for Gmail authentication.
    """
    print("\n=== Fixing OAuth Settings for Gmail Authentication ===")
    
    print("To fix the OAuth settings, follow these steps:")
    
    # Step 1: Configure OAuth consent screen
    print("\nStep 1: Configure OAuth consent screen")
    print("1. Go to OAuth consent screen")
    print("2. Make sure your app is properly configured")
    print("3. Add the following scopes:")
    print("   - https://www.googleapis.com/auth/gmail.readonly")
    print("   - https://www.googleapis.com/auth/gmail.send")
    print("   - https://www.googleapis.com/auth/gmail.compose")
    print("   - https://www.googleapis.com/auth/gmail.modify")
    print("4. Add all your Gmail accounts as test users:")
    print("   - <EMAIL>")
    print("   - <EMAIL>")
    print("   - <EMAIL>")
    print("   - <EMAIL>")
    print("   - <EMAIL>")
    print("   - <EMAIL>")
    
    # Open OAuth consent screen
    print("\nOpening OAuth consent screen in your browser...")
    webbrowser.open("https://console.cloud.google.com/apis/credentials/consent")
    
    input("\nPress Enter when you've updated the OAuth consent screen...")
    
    # Step 2: Update redirect URIs
    print("\nStep 2: Update redirect URIs")
    print("1. Go to Credentials")
    print("2. Edit your OAuth 2.0 Client ID")
    print("3. Add the following Authorized redirect URIs:")
    print("   - http://localhost:55253/")
    print("   - http://localhost:0/")
    
    # Open Credentials page
    print("\nOpening Credentials page in your browser...")
    webbrowser.open("https://console.cloud.google.com/apis/credentials")
    
    input("\nPress Enter when you've updated the redirect URIs...")
    
    # Step 3: Enable Gmail API
    print("\nStep 3: Enable Gmail API")
    print("1. Go to API Library")
    print("2. Search for 'Gmail API'")
    print("3. Make sure it's enabled")
    
    # Open API Library
    print("\nOpening API Library in your browser...")
    webbrowser.open("https://console.cloud.google.com/apis/library/gmail.googleapis.com")
    
    input("\nPress Enter when you've enabled the Gmail API...")
    
    print("\nOAuth settings have been updated. Now you can test the authentication.")
    print("Run the test_flofactionllc_gmail.py script to test the authentication.")

if __name__ == "__main__":
    fix_oauth_settings()
