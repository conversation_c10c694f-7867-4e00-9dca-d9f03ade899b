"""
Workflow Visualization for the Multi-Agent AI System.

This module provides visualization capabilities for workflows,
allowing users to see the structure and execution status of workflows.
"""
import os
import json
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime
import networkx as nx
import matplotlib.pyplot as plt
from matplotlib.colors import LinearSegmentedColormap
import matplotlib.patches as mpatches
from pathlib import Path

from core.logger import setup_logger
from core.state_manager import StateManager

# Set up logger
logger = setup_logger("workflow_visualizer")

class WorkflowVisualizer:
    """
    Visualizer for workflows.
    
    This class provides visualization capabilities for workflows,
    allowing users to see the structure and execution status of workflows.
    """
    
    def __init__(self, state_manager: StateManager, output_dir: Optional[str] = None):
        """
        Initialize the workflow visualizer.
        
        Args:
            state_manager (StateManager): System state manager
            output_dir (Optional[str]): Directory to save visualizations
        """
        self.state_manager = state_manager
        self.output_dir = output_dir or "visualizations"
        
        # Create output directory if it doesn't exist
        os.makedirs(self.output_dir, exist_ok=True)
    
    async def visualize_workflow(self, workflow_id: str, save: bool = True) -> Optional[str]:
        """
        Visualize a workflow.
        
        Args:
            workflow_id (str): Workflow ID
            save (bool): Whether to save the visualization
            
        Returns:
            Optional[str]: Path to the saved visualization if save=True, None otherwise
        """
        # Get workflow from state manager
        workflow = await self.state_manager.get_state("coordinator", f"workflows.{workflow_id}")
        
        if not workflow:
            logger.error(f"Workflow not found: {workflow_id}")
            return None
        
        # Create graph
        G = nx.DiGraph()
        
        # Add nodes and edges
        self._build_workflow_graph(G, workflow)
        
        # Get workflow execution status
        execution_status = await self._get_workflow_execution_status(workflow_id)
        
        # Set node colors based on execution status
        node_colors = []
        for node in G.nodes():
            status = execution_status.get(node, "pending")
            if status == "completed":
                node_colors.append("green")
            elif status == "running":
                node_colors.append("yellow")
            elif status == "failed":
                node_colors.append("red")
            else:
                node_colors.append("lightgray")
        
        # Create figure
        plt.figure(figsize=(12, 8))
        
        # Set title
        plt.title(f"Workflow: {workflow.get('name', workflow_id)}")
        
        # Create layout
        pos = nx.spring_layout(G)
        
        # Draw nodes
        nx.draw_networkx_nodes(G, pos, node_color=node_colors, node_size=500, alpha=0.8)
        
        # Draw edges
        nx.draw_networkx_edges(G, pos, width=1.0, alpha=0.5, arrows=True)
        
        # Draw labels
        nx.draw_networkx_labels(G, pos, font_size=10, font_family="sans-serif")
        
        # Add legend
        legend_elements = [
            mpatches.Patch(color="lightgray", label="Pending"),
            mpatches.Patch(color="yellow", label="Running"),
            mpatches.Patch(color="green", label="Completed"),
            mpatches.Patch(color="red", label="Failed"),
        ]
        plt.legend(handles=legend_elements, loc="upper right")
        
        # Save or show
        if save:
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            filename = f"workflow_{workflow_id}_{timestamp}.png"
            filepath = os.path.join(self.output_dir, filename)
            plt.savefig(filepath)
            plt.close()
            logger.info(f"Workflow visualization saved to {filepath}")
            return filepath
        else:
            plt.show()
            plt.close()
            return None
    
    def _build_workflow_graph(self, G: nx.DiGraph, workflow: Dict):
        """
        Build a graph representation of a workflow.
        
        Args:
            G (nx.DiGraph): Graph to build
            workflow (Dict): Workflow data
        """
        steps = workflow.get("steps", [])
        
        # Add start node
        G.add_node("start", label="Start")
        
        # Add step nodes
        for i, step in enumerate(steps):
            step_id = f"step_{i}"
            step_type = step.get("type", "unknown")
            
            if step_type == "agent_task":
                agent_id = step.get("agent_id", "unknown")
                task_type = step.get("task_type", "unknown")
                label = f"{agent_id}: {task_type}"
            elif step_type == "reasoning":
                reasoning_type = step.get("reasoning_type", "unknown")
                label = f"Reasoning: {reasoning_type}"
            elif step_type == "transformation":
                label = "Transformation"
            elif step_type == "condition":
                label = "Condition"
            elif step_type == "parallel":
                label = "Parallel"
                
                # Add parallel branch nodes
                branches = step.get("branches", [])
                for j, branch in enumerate(branches):
                    branch_id = f"{step_id}_branch_{j}"
                    G.add_node(branch_id, label=f"Branch {j+1}")
                    G.add_edge(step_id, branch_id)
                    
                    # Add branch step nodes
                    for k, branch_step in enumerate(branch):
                        branch_step_id = f"{branch_id}_step_{k}"
                        branch_step_type = branch_step.get("type", "unknown")
                        
                        if branch_step_type == "agent_task":
                            agent_id = branch_step.get("agent_id", "unknown")
                            task_type = branch_step.get("task_type", "unknown")
                            branch_label = f"{agent_id}: {task_type}"
                        else:
                            branch_label = branch_step_type.capitalize()
                        
                        G.add_node(branch_step_id, label=branch_label)
                        
                        if k == 0:
                            G.add_edge(branch_id, branch_step_id)
                        else:
                            G.add_edge(f"{branch_id}_step_{k-1}", branch_step_id)
            else:
                label = step_type.capitalize()
            
            G.add_node(step_id, label=label)
            
            # Add edge from previous step
            if i == 0:
                G.add_edge("start", step_id)
            else:
                # Check if previous step was parallel
                prev_step = steps[i-1]
                if prev_step.get("type") == "parallel":
                    # Add edges from all branch endpoints
                    branches = prev_step.get("branches", [])
                    for j, branch in enumerate(branches):
                        branch_id = f"step_{i-1}_branch_{j}"
                        if branch:
                            last_branch_step_id = f"{branch_id}_step_{len(branch)-1}"
                            G.add_edge(last_branch_step_id, step_id)
                else:
                    G.add_edge(f"step_{i-1}", step_id)
        
        # Add end node
        G.add_node("end", label="End")
        
        # Add edge from last step to end
        if steps:
            last_step = steps[-1]
            if last_step.get("type") == "parallel":
                # Add edges from all branch endpoints
                branches = last_step.get("branches", [])
                for j, branch in enumerate(branches):
                    branch_id = f"step_{len(steps)-1}_branch_{j}"
                    if branch:
                        last_branch_step_id = f"{branch_id}_step_{len(branch)-1}"
                        G.add_edge(last_branch_step_id, "end")
            else:
                G.add_edge(f"step_{len(steps)-1}", "end")
        else:
            G.add_edge("start", "end")
    
    async def _get_workflow_execution_status(self, workflow_id: str) -> Dict[str, str]:
        """
        Get the execution status of a workflow.
        
        Args:
            workflow_id (str): Workflow ID
            
        Returns:
            Dict[str, str]: Mapping of node IDs to status
        """
        # Get workflow executions from state manager
        executions = await self.state_manager.get_state("advanced_coordinator", "workflow_executions")
        
        if not executions:
            return {}
        
        # Find the most recent execution for this workflow
        workflow_executions = [
            execution for execution in executions.values()
            if execution.get("workflow_id") == workflow_id
        ]
        
        if not workflow_executions:
            return {}
        
        # Sort by start time (most recent first)
        workflow_executions.sort(
            key=lambda x: datetime.fromisoformat(x.get("created_at", "2000-01-01T00:00:00")),
            reverse=True
        )
        
        latest_execution = workflow_executions[0]
        
        # Get step results
        step_results = latest_execution.get("step_results", {})
        current_step = latest_execution.get("current_step", 0)
        status = latest_execution.get("status", "pending")
        
        # Build status mapping
        node_status = {
            "start": "completed"  # Start node is always completed
        }
        
        # Set status for steps
        for i in range(len(step_results)):
            step_id = f"step_{i}"
            node_status[step_id] = "completed"
            
            # Check if step is parallel
            step_result = step_results.get(str(i), {})
            if step_result.get("type") == "parallel":
                branch_results = step_result.get("branch_results", {})
                for branch_id, branch_result in branch_results.items():
                    branch_node_id = f"{step_id}_branch_{branch_id}"
                    node_status[branch_node_id] = "completed"
                    
                    for branch_step_id, branch_step_result in branch_result.items():
                        node_id = f"{branch_node_id}_step_{branch_step_id}"
                        node_status[node_id] = "completed"
        
        # Set status for current step
        if status == "running" and current_step < len(latest_execution.get("workflow", {}).get("steps", [])):
            step_id = f"step_{current_step}"
            node_status[step_id] = "running"
        
        # Set status for end node
        if status == "completed":
            node_status["end"] = "completed"
        elif status == "failed":
            # Mark current step as failed
            step_id = f"step_{current_step}"
            node_status[step_id] = "failed"
        
        return node_status
    
    async def visualize_all_workflows(self, save: bool = True) -> List[str]:
        """
        Visualize all workflows.
        
        Args:
            save (bool): Whether to save the visualizations
            
        Returns:
            List[str]: Paths to the saved visualizations if save=True, empty list otherwise
        """
        # Get all workflows from state manager
        workflows = await self.state_manager.get_state("coordinator", "workflows")
        
        if not workflows:
            logger.warning("No workflows found")
            return []
        
        # Visualize each workflow
        filepaths = []
        for workflow_id in workflows:
            filepath = await self.visualize_workflow(workflow_id, save)
            if filepath:
                filepaths.append(filepath)
        
        return filepaths
