"""
MPC Server Discovery for the Multi-Agent AI System.

This module provides automatic discovery of MPC servers on the network,
allowing the system to dynamically connect to available servers.
"""
import asyncio
import json
import logging
import os
import socket
import struct
from typing import Dict, List, Optional, Any, Union, Set, Tuple
import uuid
from datetime import datetime, timedelta
import aiohttp
import ipaddress

# Add the project root to the Python path
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

from core.logger import setup_logger
from core.state_manager import StateManager
from borg_cluster.borg_resource_manager import BorgResourceManager, ResourceType

# Set up logger
logger = setup_logger("mpc_server_discovery")

class MPCServerDiscovery:
    """
    MPC Server Discovery for the Multi-Agent AI System.

    This class provides automatic discovery of MPC servers on the network,
    allowing the system to dynamically connect to available servers.
    """

    def __init__(
        self,
        state_manager: StateManager,
        resource_manager: BorgResourceManager,
        config: Dict = None,
    ):
        """
        Initialize the MPC Server Discovery.

        Args:
            state_manager (StateManager): System state manager
            resource_manager (BorgResourceManager): Borg resource manager
            config (Dict, optional): Configuration options
        """
        self.state_manager = state_manager
        self.resource_manager = resource_manager
        self.config = config or {}

        # Discovery configuration
        self.discovery_interval = self.config.get("discovery_interval", 60)  # seconds
        self.discovery_timeout = self.config.get("discovery_timeout", 5)  # seconds
        self.discovery_port = self.config.get("discovery_port", 8765)  # default MPC server port
        self.discovery_multicast_group = self.config.get("discovery_multicast_group", "224.0.0.1")
        self.discovery_multicast_port = self.config.get("discovery_multicast_port", 8766)

        # Server tracking
        self.known_servers = {}
        self.active_servers = set()

        # Background tasks
        self.discovery_task = None
        self.health_check_task = None

        logger.info("MPC Server Discovery initialized")

    async def initialize(self):
        """Initialize the MPC Server Discovery."""
        try:
            # Load existing state if available
            discovery_state = await self.state_manager.get_state("borg", "mpc_server_discovery")
            if discovery_state:
                # Restore known servers
                if "known_servers" in discovery_state:
                    self.known_servers = discovery_state["known_servers"]

                # Restore active servers
                if "active_servers" in discovery_state:
                    self.active_servers = set(discovery_state["active_servers"])

                logger.info("Restored MPC Server Discovery state")

            # Start discovery task
            self.discovery_task = asyncio.create_task(self._discover_servers())

            # Start health check task
            self.health_check_task = asyncio.create_task(self._check_server_health())

            logger.info("MPC Server Discovery initialized successfully")

        except Exception as e:
            logger.exception(f"Error initializing MPC Server Discovery: {e}")
            raise

    async def _discover_servers(self):
        """Discover MPC servers on the network."""
        while True:
            try:
                # Discover servers using multiple methods
                await asyncio.gather(
                    self._discover_servers_multicast(),
                    self._discover_servers_scan(),
                    self._discover_servers_dns(),
                    return_exceptions=True,
                )

                # Save state
                await self._save_state()

            except Exception as e:
                logger.exception(f"Error discovering MPC servers: {e}")

            # Sleep for discovery interval
            await asyncio.sleep(self.discovery_interval)

    async def _discover_servers_multicast(self):
        """Discover MPC servers using multicast."""
        try:
            # Create multicast socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(self.discovery_timeout)

            # Set socket options
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)

            # Bind to multicast port
            sock.bind(('', self.discovery_multicast_port))

            # Join multicast group
            group = socket.inet_aton(self.discovery_multicast_group)
            mreq = struct.pack('4sL', group, socket.INADDR_ANY)
            sock.setsockopt(socket.IPPROTO_IP, socket.IP_ADD_MEMBERSHIP, mreq)

            # Send discovery message
            message = json.dumps({
                "type": "discovery",
                "client_id": str(uuid.uuid4()),
                "timestamp": datetime.now().isoformat(),
            }).encode()

            sock.sendto(message, (self.discovery_multicast_group, self.discovery_multicast_port))

            # Receive responses
            start_time = datetime.now()
            while (datetime.now() - start_time).total_seconds() < self.discovery_timeout:
                try:
                    data, addr = sock.recvfrom(1024)
                    response = json.loads(data.decode())

                    if response.get("type") == "discovery_response":
                        server_id = response.get("server_id")
                        server_host = addr[0]
                        server_port = response.get("port", self.discovery_port)
                        server_type = response.get("server_type", "standard")

                        # Register server
                        await self._register_server(
                            server_id=server_id,
                            server_host=server_host,
                            server_port=server_port,
                            server_type=server_type,
                            server_info=response.get("server_info", {}),
                        )

                except socket.timeout:
                    pass

            # Close socket
            sock.close()

        except Exception as e:
            logger.exception(f"Error discovering MPC servers using multicast: {e}")

    async def _discover_servers_scan(self):
        """Discover MPC servers by scanning the network."""
        try:
            # Get local network information
            local_ip = socket.gethostbyname(socket.gethostname())
            network = ipaddress.IPv4Network(f"{local_ip}/24", strict=False)

            # Scan network
            scan_tasks = []
            for ip in network.hosts():
                scan_tasks.append(self._check_server(str(ip), self.discovery_port))

                # Limit concurrent scans
                if len(scan_tasks) >= 100:
                    await asyncio.gather(*scan_tasks, return_exceptions=True)
                    scan_tasks = []

            # Wait for remaining scans
            if scan_tasks:
                await asyncio.gather(*scan_tasks, return_exceptions=True)

        except Exception as e:
            logger.exception(f"Error discovering MPC servers by scanning: {e}")

    async def _discover_servers_dns(self):
        """Discover MPC servers using DNS-SD or similar service discovery."""
        # This is a placeholder for DNS-SD or similar service discovery
        # Implement based on your specific requirements
        pass

    async def _check_server(self, host: str, port: int):
        """
        Check if an MPC server is running at the specified host and port.

        Args:
            host (str): Server host
            port (int): Server port
        """
        try:
            # Try to connect to server
            reader, writer = await asyncio.open_connection(host, port)

            # Send hello message
            message = json.dumps({
                "type": "hello",
                "client_id": str(uuid.uuid4()),
                "timestamp": datetime.now().isoformat(),
            }).encode()

            writer.write(message)
            await writer.drain()

            # Receive response
            response_data = await asyncio.wait_for(reader.read(1024), timeout=self.discovery_timeout)
            response = json.loads(response_data.decode())

            # Close connection
            writer.close()
            await writer.wait_closed()

            if response.get("type") == "hello_response":
                server_id = response.get("server_id")
                server_type = response.get("server_type", "standard")

                # Register server
                await self._register_server(
                    server_id=server_id,
                    server_host=host,
                    server_port=port,
                    server_type=server_type,
                    server_info=response.get("server_info", {}),
                )

        except (asyncio.TimeoutError, ConnectionRefusedError, OSError):
            # No server at this address
            pass
        except Exception as e:
            logger.debug(f"Error checking server at {host}:{port}: {e}")

    async def _register_server(
        self,
        server_id: str,
        server_host: str,
        server_port: int,
        server_type: str,
        server_info: Dict,
    ):
        """
        Register an MPC server.

        Args:
            server_id (str): Server identifier
            server_host (str): Server host
            server_port (int): Server port
            server_type (str): Server type
            server_info (Dict): Additional server information
        """
        # Create server info
        server_key = f"{server_host}:{server_port}"

        # Check if server is already known
        if server_key in self.known_servers:
            # Update server info
            self.known_servers[server_key].update({
                "server_id": server_id,
                "server_host": server_host,
                "server_port": server_port,
                "server_type": server_type,
                "last_seen": datetime.now().isoformat(),
                "active": True,
            })

            # Update server info
            if server_info:
                self.known_servers[server_key]["server_info"] = server_info

            logger.debug(f"Updated MPC server: {server_key}")
        else:
            # Add new server
            self.known_servers[server_key] = {
                "server_id": server_id,
                "server_host": server_host,
                "server_port": server_port,
                "server_type": server_type,
                "discovered_at": datetime.now().isoformat(),
                "last_seen": datetime.now().isoformat(),
                "active": True,
                "server_info": server_info,
            }

            logger.info(f"Discovered new MPC server: {server_key}")

        # Add to active servers
        self.active_servers.add(server_key)

        # Register with resource manager
        resource_id = f"mpc_server:{server_key}"

        # Create resource info
        resource_info = {
            "id": resource_id,
            "server_id": server_id,
            "server_host": server_host,
            "server_port": server_port,
            "server_type": server_type,
            "available": True,
            "allocated": False,
            "server_info": server_info,
        }

        # Register resource
        await self.resource_manager.register_resource(
            resource_type=ResourceType.MPC_SERVER,
            resource_id=resource_id,
            resource_info=resource_info,
        )

    async def _check_server_health(self):
        """Check the health of known MPC servers."""
        while True:
            try:
                # Get current time
                now = datetime.now()

                # Check each known server
                for server_key in list(self.known_servers.keys()):
                    # Parse server key
                    host, port_str = server_key.split(":")
                    port = int(port_str)

                    # Check if server is active
                    is_active = await self._check_server_active(host, port)

                    # Update server status
                    if is_active:
                        # Update last seen time
                        self.known_servers[server_key]["last_seen"] = now.isoformat()
                        self.known_servers[server_key]["active"] = True

                        # Add to active servers
                        self.active_servers.add(server_key)
                    else:
                        # Mark as inactive
                        self.known_servers[server_key]["active"] = False

                        # Remove from active servers
                        if server_key in self.active_servers:
                            self.active_servers.remove(server_key)

                            # Unregister from resource manager
                            resource_id = f"mpc_server:{server_key}"
                            try:
                                await self.resource_manager.unregister_resource(
                                    resource_type=ResourceType.MPC_SERVER,
                                    resource_id=resource_id,
                                )
                            except ValueError:
                                # Resource not registered
                                pass

                            logger.info(f"MPC server {server_key} is no longer active")

                # Save state
                await self._save_state()

            except Exception as e:
                logger.exception(f"Error checking MPC server health: {e}")

            # Sleep for a while
            await asyncio.sleep(30)

    async def _check_server_active(self, host: str, port: int) -> bool:
        """
        Check if an MPC server is active.

        Args:
            host (str): Server host
            port (int): Server port

        Returns:
            bool: Whether the server is active
        """
        try:
            # Try to connect to server
            reader, writer = await asyncio.open_connection(host, port)

            # Send ping message
            message = json.dumps({
                "type": "ping",
                "client_id": str(uuid.uuid4()),
                "timestamp": datetime.now().isoformat(),
            }).encode()

            writer.write(message)
            await writer.drain()

            # Receive response
            response_data = await asyncio.wait_for(reader.read(1024), timeout=self.discovery_timeout)

            # Close connection
            writer.close()
            await writer.wait_closed()

            # Check if response is valid
            try:
                response = json.loads(response_data.decode())
                return response.get("type") == "pong"
            except json.JSONDecodeError:
                return False

        except (asyncio.TimeoutError, ConnectionRefusedError, OSError):
            return False
        except Exception as e:
            logger.debug(f"Error checking server at {host}:{port}: {e}")
            return False

    async def get_active_servers(self) -> List[Dict]:
        """
        Get a list of active MPC servers.

        Returns:
            List[Dict]: List of active server information
        """
        active_servers = []

        for server_key in self.active_servers:
            if server_key in self.known_servers:
                active_servers.append(self.known_servers[server_key])

        return active_servers

    async def get_server_info(self, server_key: str) -> Optional[Dict]:
        """
        Get information about a specific MPC server.

        Args:
            server_key (str): Server key in the format "host:port"

        Returns:
            Optional[Dict]: Server information, or None if not found
        """
        return self.known_servers.get(server_key)

    async def _save_state(self):
        """Save the current state to the state manager."""
        try:
            # Create state
            state = {
                "known_servers": self.known_servers,
                "active_servers": list(self.active_servers),
                "last_updated": datetime.now().isoformat(),
            }

            # Save state
            await self.state_manager.update_state("borg", "mpc_server_discovery", state)

        except Exception as e:
            logger.exception(f"Error saving MPC Server Discovery state: {e}")

    async def close(self):
        """Close the MPC Server Discovery."""
        try:
            # Cancel background tasks
            if self.discovery_task:
                self.discovery_task.cancel()
                try:
                    await self.discovery_task
                except asyncio.CancelledError:
                    pass

            if self.health_check_task:
                self.health_check_task.cancel()
                try:
                    await self.health_check_task
                except asyncio.CancelledError:
                    pass

            # Save final state
            await self._save_state()

            logger.info("MPC Server Discovery closed")

        except Exception as e:
            logger.exception(f"Error closing MPC Server Discovery: {e}")
            raise
