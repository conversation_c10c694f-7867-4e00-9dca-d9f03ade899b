@echo off
echo Running UI-TARS Task Example...

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Check if the server is running
curl -s http://127.0.0.1:8000/health >nul 2>&1
if %errorlevel% neq 0 (
    echo Starting the local API server...
    start "UI-TARS API Server" cmd /c "python simple_hf_server.py"
    
    REM Wait for the server to start
    echo Waiting for the server to start...
    timeout /t 5 /nobreak >nul
)

REM Ask for the task to perform
echo.
echo Choose a task to perform:
echo 1. Search and summarize
echo 2. Fill out a contact form
echo 3. Extract product information
echo.
set /p TASK_CHOICE="Enter your choice (1-3): "

REM Set the task based on the choice
if "%TASK_CHOICE%"=="1" (
    set TASK=search
    set /p QUERY="Enter search query (default: UI-TARS documentation): "
    if "%QUERY%"=="" set QUERY=UI-TARS documentation
    set ARGS=--task search --query "%QUERY%"
) else if "%TASK_CHOICE%"=="2" (
    set TASK=form
    set /p WEBSITE="Enter website URL (default: https://www.example.com): "
    if "%WEBSITE%"=="" set WEBSITE=https://www.example.com
    set ARGS=--task form --website "%WEBSITE%"
) else if "%TASK_CHOICE%"=="3" (
    set TASK=extract
    set /p WEBSITE="Enter website URL (default: https://www.example.com): "
    if "%WEBSITE%"=="" set WEBSITE=https://www.example.com
    set /p PRODUCT="Enter product type (default: electronics): "
    if "%PRODUCT%"=="" set PRODUCT=electronics
    set ARGS=--task extract --website "%WEBSITE%" --product "%PRODUCT%"
) else (
    echo Invalid choice. Exiting.
    exit /b 1
)

REM Run the example
echo.
echo Running the %TASK% task...
echo.
python ui_tars_task_example.py %ARGS%

echo.
echo Task completed. Press any key to exit...
pause >nul

exit /b 0
