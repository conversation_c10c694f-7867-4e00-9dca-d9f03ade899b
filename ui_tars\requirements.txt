# UI-TARS Requirements

# Core dependencies
requests>=2.28.0
aiohttp>=3.8.0
asyncio>=3.4.3
pillow>=9.0.0
numpy>=1.22.0
pyyaml>=6.0

# GUI dependencies
tk>=0.1.0

# Voice command dependencies
SpeechRecognition>=3.8.1
pyttsx3>=2.90

# NVIDIA dependencies
nvidia-ml-py>=11.495.46
tensorrt>=8.4.0; platform_system == "Linux" or platform_system == "Windows"

# Browser automation dependencies
selenium>=4.1.0
webdriver-manager>=3.8.0

# Local LLM dependencies
transformers>=4.20.0
torch>=1.12.0
accelerate>=0.12.0
bitsandbytes>=0.35.0; platform_system == "Linux" or platform_system == "Windows"
