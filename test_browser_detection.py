"""
Test Browser Detection

This script tests browser detection functionality without relying on the full UI-TARS framework.
"""
import os
import sys
import json
import logging
import platform
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("test_browser_detection.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("test_browser_detection")

class BrowserInfo:
    """Information about a browser installation."""
    
    def __init__(self, 
                 browser_type: str,
                 executable_path: str,
                 version: Optional[str] = None,
                 user_data_dir: Optional[str] = None,
                 default_profile: Optional[str] = None,
                 is_default: bool = False):
        """
        Initialize browser information.
        
        Args:
            browser_type (str): Type of browser
            executable_path (str): Path to browser executable
            version (Optional[str]): Browser version
            user_data_dir (Optional[str]): Path to user data directory
            default_profile (Optional[str]): Default profile name
            is_default (bool): Whether this is the default browser
        """
        self.browser_type = browser_type
        self.executable_path = executable_path
        self.version = version
        self.user_data_dir = user_data_dir
        self.default_profile = default_profile
        self.is_default = is_default
        
    def __str__(self) -> str:
        """String representation."""
        return f"{self.browser_type} ({self.version or 'unknown'}) at {self.executable_path}"

def detect_windows_browsers() -> Dict[str, BrowserInfo]:
    """Detect browsers on Windows."""
    browsers = {}
    
    # Common browser paths on Windows
    browser_paths = {
        "chrome": [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
        ],
        "edge": [
            r"C:\Program Files\Microsoft\Edge\Application\msedge.exe",
            r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe"
        ],
        "firefox": [
            r"C:\Program Files\Mozilla Firefox\firefox.exe",
            r"C:\Program Files (x86)\Mozilla Firefox\firefox.exe"
        ],
        "brave": [
            r"C:\Program Files\BraveSoftware\Brave-Browser\Application\brave.exe",
            r"C:\Program Files (x86)\BraveSoftware\Brave-Browser\Application\brave.exe"
        ]
    }
    
    # Check if any of the paths exist
    for browser_type, paths in browser_paths.items():
        for path in paths:
            if os.path.exists(path):
                # Get user data directory
                user_data_dir = None
                default_profile = None
                
                if browser_type == "chrome":
                    user_data_dir = os.path.join(os.environ.get("LOCALAPPDATA", ""), r"Google\Chrome\User Data")
                    default_profile = "Default"
                elif browser_type == "edge":
                    user_data_dir = os.path.join(os.environ.get("LOCALAPPDATA", ""), r"Microsoft\Edge\User Data")
                    default_profile = "Default"
                elif browser_type == "firefox":
                    user_data_dir = os.path.join(os.environ.get("APPDATA", ""), r"Mozilla\Firefox\Profiles")
                    default_profile = None
                elif browser_type == "brave":
                    user_data_dir = os.path.join(os.environ.get("LOCALAPPDATA", ""), r"BraveSoftware\Brave-Browser\User Data")
                    default_profile = "Default"
                
                # Create browser info
                browsers[browser_type] = BrowserInfo(
                    browser_type=browser_type,
                    executable_path=path,
                    user_data_dir=user_data_dir,
                    default_profile=default_profile
                )
                
                logger.info(f"Found {browser_type} at {path}")
                break
    
    return browsers

def detect_browsers() -> Dict[str, BrowserInfo]:
    """Detect installed browsers."""
    os_type = platform.system()
    browsers = {}
    
    logger.info(f"Detecting browsers on {os_type}")
    
    if os_type == "Windows":
        browsers = detect_windows_browsers()
    else:
        logger.warning(f"Browser detection not implemented for {os_type}")
    
    logger.info(f"Detected {len(browsers)} browsers")
    for browser_type, browser_info in browsers.items():
        logger.info(f"- {browser_info}")
    
    return browsers

def main():
    """Main function."""
    logger.info("Starting browser detection test")
    
    # Detect browsers
    browsers = detect_browsers()
    
    # Print results
    print("\nDetected Browsers:")
    if browsers:
        for browser_type, browser_info in browsers.items():
            print(f"- {browser_info}")
    else:
        print("No browsers detected")
    
    # Check if UI-TARS is installed
    print("\nChecking for UI-TARS installation:")
    ui_tars_paths = [
        os.path.join(os.environ.get("PROGRAMFILES", "C:\\Program Files"), "UI-TARS", "UI-TARS.exe"),
        os.path.join(os.environ.get("PROGRAMFILES(X86)", "C:\\Program Files (x86)"), "UI-TARS", "UI-TARS.exe"),
        os.path.join(os.environ.get("LOCALAPPDATA", "C:\\Users\\<USER>\\AppData\\Local".format(os.getlogin())), "UI-TARS", "UI-TARS.exe")
    ]
    
    ui_tars_found = False
    for path in ui_tars_paths:
        if os.path.exists(path):
            print(f"UI-TARS found at: {path}")
            ui_tars_found = True
            break
    
    if not ui_tars_found:
        print("UI-TARS not found")
    
    print("\nTest completed")

if __name__ == "__main__":
    main()
