"""
Start Jarvis with AlphaEvolve Integration.

This script starts the Jarvis interface with full AlphaEvolve integration,
initializing all necessary components and establishing connections between them.
"""
import asyncio
import argparse
import logging
import os
import sys
import signal
import keyboard
import threading
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent))

try:
    from core.logger import setup_logger
    from core.state_manager import StateManager
    from borg_cluster.borg_resource_manager import BorgResourceManager
    from borg_cluster.borg_load_balancer import BorgLoadBalancer
    # Use the auto-start version of JarvisInterface
    from borg_cluster.jarvis_interface_autostart import Jarvis<PERSON><PERSON>face
    from alpha_evolve.alpha_evolve_engine import AlphaEvolveEngine
    from alpha_evolve.integration.borg_integration import BorgIntegration
    from alpha_evolve.integration.jarvis_integration import JarvisAlphaEvolveCommands
    from alpha_evolve.integration.agent_integration import AgentIntegration
    from core.agent_manager import <PERSON><PERSON>anager
    from crewai_integration import CrewAIIntegration
except ImportError as e:
    print(f"Error importing required modules: {e}")
    print("Make sure you're running this script from the project root directory.")
    sys.exit(1)

# Set up logger
logger = setup_logger("start_jarvis_with_alphaevolve")

# Global flag to control system shutdown
shutdown_event = asyncio.Event()

async def initialize_components():
    """Initialize all components required for Jarvis with AlphaEvolve integration."""
    logger.info("Initializing components for Jarvis with AlphaEvolve integration")

    try:
        # Initialize state manager
        logger.info("Initializing State Manager")
        state_manager = StateManager()
        await state_manager.initialize()

        # Initialize agent manager
        logger.info("Initializing Agent Manager")
        agent_manager = AgentManager(state_manager=state_manager)
        await agent_manager.initialize()

        # Initialize Borg resource manager
        logger.info("Initializing Borg Resource Manager")
        resource_manager = BorgResourceManager(state_manager=state_manager)
        await resource_manager.initialize()

        # Initialize Borg load balancer
        logger.info("Initializing Borg Load Balancer")
        load_balancer = BorgLoadBalancer(
            resource_manager=resource_manager,
            state_manager=state_manager
        )
        await load_balancer.initialize()

        # Initialize AlphaEvolve engine
        logger.info("Initializing AlphaEvolve Engine")
        alpha_evolve_engine = AlphaEvolveEngine(state_manager=state_manager)
        await alpha_evolve_engine.initialize()

        # Initialize Borg integration
        logger.info("Initializing Borg Integration")
        borg_integration = BorgIntegration(
            alpha_evolve_engine=alpha_evolve_engine,
            resource_manager=resource_manager,
            load_balancer=load_balancer
        )
        await borg_integration.initialize()

        # Initialize agent integration
        logger.info("Initializing Agent Integration")
        agent_integration = AgentIntegration(
            alpha_evolve_engine=alpha_evolve_engine,
            agent_manager=agent_manager
        )
        await agent_integration.initialize()

        # Initialize Jarvis interface
        logger.info("Initializing Jarvis Interface")
        jarvis_interface = JarvisInterface(
            resource_manager=resource_manager,
            load_balancer=load_balancer,
            agent_manager=agent_manager,
            state_manager=state_manager
        )
        await jarvis_interface.initialize()

        # Initialize Jarvis AlphaEvolve commands
        logger.info("Initializing Jarvis AlphaEvolve Commands")
        jarvis_commands = JarvisAlphaEvolveCommands(
            jarvis_interface=jarvis_interface,
            alpha_evolve_engine=alpha_evolve_engine,
            borg_integration=borg_integration,
            agent_integration=agent_integration
        )
        await jarvis_commands.initialize()

        # Initialize CrewAI integration
        logger.info("Initializing CrewAI Integration")
        crewai_integration = CrewAIIntegration(
            state_manager=state_manager,
            agent_manager=agent_manager,
            load_balancer=load_balancer
        )
        await crewai_integration.initialize()

        logger.info("All components initialized successfully")

        return {
            "state_manager": state_manager,
            "agent_manager": agent_manager,
            "resource_manager": resource_manager,
            "load_balancer": load_balancer,
            "alpha_evolve_engine": alpha_evolve_engine,
            "borg_integration": borg_integration,
            "agent_integration": agent_integration,
            "jarvis_interface": jarvis_interface,
            "jarvis_commands": jarvis_commands,
            "crewai_integration": crewai_integration
        }

    except Exception as e:
        logger.exception(f"Error initializing components: {e}")
        raise

def setup_exit_handler():
    """Set up the exit key combination handler (Ctrl+Alt+X)."""
    try:
        # Register the exit key combination
        keyboard.add_hotkey('ctrl+alt+x', lambda: signal_handler())
        logger.info("Exit key handler registered (Ctrl+Alt+X)")
    except Exception as e:
        logger.warning(f"Could not register exit key handler: {e}")
        logger.warning("Exit using Ctrl+C instead")

async def start_jarvis(args):
    """
    Start Jarvis with AlphaEvolve integration.

    Args:
        args: Command-line arguments
    """
    logger.info("Starting Jarvis with AlphaEvolve integration")

    try:
        # Set up exit key handler if in interactive mode
        if args.interactive:
            # Run in a separate thread to avoid blocking
            threading.Thread(target=setup_exit_handler, daemon=True).start()

        # Initialize components
        components = await initialize_components()

        # Display welcome message
        jarvis_interface = components["jarvis_interface"]

        welcome_message = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║                  JARVIS WITH ALPHAEVOLVE INTEGRATION ACTIVE                  ║
║                                                                              ║
║  AlphaEvolve's evolutionary programming capabilities are now integrated      ║
║  with the Borg Cluster Management System to:                                 ║
║                                                                              ║
║  1. Enhance the learning capabilities of all agents and sub-agents           ║
║  2. Improve task completion efficiency across the system                     ║
║  3. Optimize agent decision-making and problem-solving abilities             ║
║  4. Continuously evolve and improve the system autonomously                  ║
║                                                                              ║
║  Type 'help' to see available commands.                                      ║
║  Type 'alpha-evolve-status' to check AlphaEvolve status.                     ║
║  Press Ctrl+Alt+X to exit and return to regular terminal.                    ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
"""
        print(welcome_message)

        # Start Jarvis interface
        if args.interactive:
            # If auto-start is enabled, skip the manual activation prompt
            if args.auto_start:
                logger.info("Auto-start enabled, skipping manual activation")
                # Set any auto-start specific configurations here
                jarvis_interface.auto_start = True

            await jarvis_interface.start_interactive_mode()
        else:
            # Wait for shutdown signal
            await shutdown_event.wait()

        # Shutdown components
        await shutdown_components(components)

    except Exception as e:
        logger.exception(f"Error starting Jarvis: {e}")
        sys.exit(1)

async def shutdown_components(components):
    """
    Shutdown all components.

    Args:
        components: Dictionary of components
    """
    logger.info("Shutting down components")

    try:
        # Shutdown in reverse order of initialization
        if "jarvis_commands" in components:
            logger.info("Shutting down Jarvis AlphaEvolve Commands")
            # No shutdown method for jarvis_commands

        if "crewai_integration" in components:
            logger.info("Shutting down CrewAI Integration")
            await components["crewai_integration"].shutdown()

        if "jarvis_interface" in components:
            logger.info("Shutting down Jarvis Interface")
            await components["jarvis_interface"].shutdown()

        if "agent_integration" in components:
            logger.info("Shutting down Agent Integration")
            await components["agent_integration"].shutdown()

        if "borg_integration" in components:
            logger.info("Shutting down Borg Integration")
            await components["borg_integration"].shutdown()

        if "alpha_evolve_engine" in components:
            logger.info("Shutting down AlphaEvolve Engine")
            await components["alpha_evolve_engine"].shutdown()

        if "load_balancer" in components:
            logger.info("Shutting down Borg Load Balancer")
            await components["load_balancer"].shutdown()

        if "resource_manager" in components:
            logger.info("Shutting down Borg Resource Manager")
            await components["resource_manager"].shutdown()

        if "agent_manager" in components:
            logger.info("Shutting down Agent Manager")
            await components["agent_manager"].shutdown()

        if "state_manager" in components:
            logger.info("Shutting down State Manager")
            await components["state_manager"].shutdown()

        logger.info("All components shut down successfully")

    except Exception as e:
        logger.exception(f"Error shutting down components: {e}")

def signal_handler():
    """Handle termination signals."""
    logger.info("Received termination signal")
    shutdown_event.set()

def main():
    """Main entry point."""
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="Start Jarvis with AlphaEvolve Integration")
    parser.add_argument("--interactive", action="store_true", help="Start in interactive mode")
    parser.add_argument("--auto-start", action="store_true", help="Start automatically without requiring manual activation")
    parser.add_argument("--log-level", type=str, default="INFO", help="Logging level")
    args = parser.parse_args()

    # Set up signal handlers
    for sig in (signal.SIGINT, signal.SIGTERM):
        signal.signal(sig, lambda signum, frame: signal_handler())

    # Start Jarvis
    try:
        asyncio.run(start_jarvis(args))
    except KeyboardInterrupt:
        print("\nShutting down Jarvis...")
        sys.exit(0)

if __name__ == "__main__":
    main()
