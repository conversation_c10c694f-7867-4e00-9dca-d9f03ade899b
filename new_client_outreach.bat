@echo off
REM New Client Outreach Batch Script
REM This script runs the new_client_outreach.py script with the provided client information

echo.
echo ===================================
echo    New Client Outreach System
echo ===================================
echo.

REM Get client information
set /p CLIENT_NAME="Enter client's full name: "
set /p CLIENT_EMAIL="Enter client's email address: "
set /p CLIENT_PHONE="Enter client's phone number: "
set /p CLIENT_DOB="Enter client's date of birth (MM/DD/YY) [optional]: "
set /p CLIENT_ADDRESS="Enter client's address [optional]: "
set /p INSURANCE_TYPE="Enter insurance type [optional]: "
set /p PREMIUM="Enter estimated premium [optional]: "
set /p AGENT_NAME="Enter agent's name [default: Sandra]: "

REM Set default agent name if not provided
if "%AGENT_NAME%"=="" set AGENT_NAME=Sandra

REM Ask if quote should be sent
set /p SEND_QUOTE="Send quote email? (y/n) [default: y]: "
if "%SEND_QUOTE%"=="" set SEND_QUOTE=y

REM Prepare quote flag
set QUOTE_FLAG=
if /i "%SEND_QUOTE%"=="y" set QUOTE_FLAG=--quote

REM Build command with optional parameters
set CMD=python new_client_outreach.py --name "%CLIENT_NAME%" --email "%CLIENT_EMAIL%" --phone "%CLIENT_PHONE%" --agent "%AGENT_NAME%" %QUOTE_FLAG%

REM Add optional parameters if provided
if not "%CLIENT_DOB%"=="" set CMD=%CMD% --dob "%CLIENT_DOB%"
if not "%CLIENT_ADDRESS%"=="" set CMD=%CMD% --address "%CLIENT_ADDRESS%"
if not "%INSURANCE_TYPE%"=="" set CMD=%CMD% --insurance-type "%INSURANCE_TYPE%"
if not "%PREMIUM%"=="" set CMD=%CMD% --premium "%PREMIUM%"

echo.
echo Running outreach for %CLIENT_NAME%...
echo.

REM Run the command
%CMD%

echo.
echo Outreach completed.
echo.

pause
