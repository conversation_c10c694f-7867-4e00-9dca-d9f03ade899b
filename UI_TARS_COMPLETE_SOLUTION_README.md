# UI-TARS Complete Solution

This package provides a complete solution for fixing UI-TARS 1.5 browser integration issues and ensuring it works properly with your AI agent system.

## Quick Start

The easiest way to get UI-TARS working properly is to run the complete solution batch file:

```
ui_tars_complete_solution.bat
```

This will:
1. Kill any existing UI-TARS and browser processes
2. Create an optimized UI-TARS configuration
3. Start Chrome with remote debugging enabled
4. Start UI-TARS with the enhanced configuration

## Tools Included

### 1. Complete Solution
- `ui_tars_complete_solution.bat`: One-click solution to fix UI-TARS browser integration

### 2. Diagnostic Tools
- `check_ui_tars_running.py`: Check if UI-TARS is running
- `diagnose_ui_tars.py`: Comprehensive diagnostics for UI-TARS issues

### 3. Fix Tools
- `fix_ui_tars_browser_integration.py`: Fix browser integration issues
- `fix_ui_tars_browser.py`: Fix browser detection issues
- `start_ui_tars.py`: Start UI-TARS with enhanced configuration

### 4. Enhanced Features
- `enhanced_ui_tars_diagnostic.py`: Enhanced diagnostic tool
- `ui_tars_virtual_pc.py`: Virtual PC environment for UI-TARS
- `ui_tars\utils\browser_sandbox.py`: Browser sandbox environment
- `ui_tars\utils\dpo_optimizer.py`: DPO optimization module
- `ui_tars\utils\enhanced_browser_detection.py`: Enhanced browser detection

## Troubleshooting

### UI-TARS Not Starting

If UI-TARS fails to start:

1. Run `diagnose_ui_tars.py` to identify the issues
2. Check the logs in `ui_tars_debug.log`
3. Make sure no other instance of UI-TARS or Chrome is running
4. Try running `ui_tars_complete_solution.bat` again

### Browser Not Detected

If the browser is not detected:

1. Make sure Chrome is installed
2. Check if Chrome is running with remote debugging enabled
3. Verify that port 9222 is not used by another application
4. Run `fix_ui_tars_browser_integration.py` to fix browser integration

### API Connection Issues

If UI-TARS API connection fails:

1. Make sure UI-TARS is running
2. Check if port 8080 is not used by another application
3. Try restarting UI-TARS
4. Check the logs for API-related errors

## Advanced Usage

### Custom Browser Configuration

To use a different browser or configuration:

```
python fix_ui_tars_browser_integration.py --browser edge --port 9223
```

### Virtual PC Environment

To run UI-TARS in a virtual PC environment:

```
python ui_tars_virtual_pc.py --browser chrome --memory 4096 --cpu 4
```

### DPO Optimization

To enable DPO optimization for improved performance:

```
python ui_tars\utils\dpo_optimizer.py
```

## Requirements

- Python 3.8 or higher
- UI-TARS 1.5
- Chrome, Edge, Firefox, or Brave browser

## License

This software is licensed under the Apache License, Version 2.0. See the LICENSE file for the full license text.

## Credits

Developed for Flo Faction Insurance to enhance UI-TARS 1.5 capabilities and fix browser integration issues.
