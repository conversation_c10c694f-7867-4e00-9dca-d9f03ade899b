"""
Test script for email and SMS communication.

This script tests the email and SMS communication functionality for the Insurance Lead Agent.
"""
import os
import sys
import json
import asyncio
import argparse
from typing import Dict, List, Optional, Any
from datetime import datetime
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEM<PERSON>ipart

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.logger import setup_logger

# Set up logger
logger = setup_logger("test_communication")

async def test_email_sending(recipient: str, subject: str, message: str, smtp_config: Dict):
    """
    Test sending an email.
    
    Args:
        recipient (str): Recipient email
        subject (str): Email subject
        message (str): Email message
        smtp_config (Dict): SMTP configuration
        
    Returns:
        bool: Success status
    """
    logger.info(f"Testing email sending to {recipient}...")
    
    try:
        # Create email
        email = MIMEMultipart()
        email["From"] = smtp_config.get("username", "")
        email["To"] = recipient
        email["Subject"] = subject
        
        # Add message
        email.attach(MIMEText(message, "plain"))
        
        # Connect to SMTP server
        with smtplib.SMTP(smtp_config.get("server", ""), smtp_config.get("port", 587)) as server:
            # Start TLS
            server.starttls()
            
            # Login
            server.login(smtp_config.get("username", ""), smtp_config.get("password", ""))
            
            # Send email
            server.send_message(email)
        
        logger.info("Email sent successfully!")
        
        return True
    
    except Exception as e:
        logger.exception(f"Error sending email: {e}")
        return False

async def test_twilio_sms(recipient: str, message: str, twilio_config: Dict):
    """
    Test sending an SMS via Twilio.
    
    Args:
        recipient (str): Recipient phone number
        message (str): SMS message
        twilio_config (Dict): Twilio configuration
        
    Returns:
        bool: Success status
    """
    logger.info(f"Testing SMS sending to {recipient}...")
    
    try:
        # Import Twilio client
        from twilio.rest import Client
        
        # Create Twilio client
        client = Client(twilio_config.get("account_sid", ""), twilio_config.get("auth_token", ""))
        
        # Send SMS
        sms = client.messages.create(
            body=message,
            from_=twilio_config.get("phone_numbers", {}).get("sms", ""),
            to=recipient
        )
        
        logger.info(f"SMS sent successfully! SID: {sms.sid}")
        
        return True
    
    except ImportError:
        logger.error("Twilio package not installed. Install with: pip install twilio")
        return False
    
    except Exception as e:
        logger.exception(f"Error sending SMS: {e}")
        return False

async def load_smtp_config():
    """
    Load SMTP configuration.
    
    Returns:
        Dict: SMTP configuration
    """
    try:
        # Check if email configuration file exists
        if os.path.exists("credentials/communication/email.json"):
            with open("credentials/communication/email.json", "r") as f:
                return json.load(f)
        
        # If not, create a new one
        smtp_config = {
            "server": "",
            "port": 587,
            "username": "",
            "password": "",
            "enabled": True,
            "last_updated": datetime.now().isoformat(),
            "notes": "SMTP configuration for Flo Faction Insurance"
        }
        
        with open("credentials/communication/email.json", "w") as f:
            json.dump(smtp_config, f, indent=4)
        
        return smtp_config
    
    except Exception as e:
        logger.exception(f"Error loading SMTP configuration: {e}")
        return {}

async def load_twilio_config():
    """
    Load Twilio configuration.
    
    Returns:
        Dict: Twilio configuration
    """
    try:
        # Check if Twilio configuration file exists
        if os.path.exists("credentials/communication/twilio.json"):
            with open("credentials/communication/twilio.json", "r") as f:
                return json.load(f)
        
        return {}
    
    except Exception as e:
        logger.exception(f"Error loading Twilio configuration: {e}")
        return {}

async def save_smtp_config(smtp_config: Dict):
    """
    Save SMTP configuration.
    
    Args:
        smtp_config (Dict): SMTP configuration
    """
    try:
        # Update last updated timestamp
        smtp_config["last_updated"] = datetime.now().isoformat()
        
        # Save configuration
        with open("credentials/communication/email.json", "w") as f:
            json.dump(smtp_config, f, indent=4)
        
        logger.info("SMTP configuration saved successfully")
    
    except Exception as e:
        logger.exception(f"Error saving SMTP configuration: {e}")

async def save_twilio_config(twilio_config: Dict):
    """
    Save Twilio configuration.
    
    Args:
        twilio_config (Dict): Twilio configuration
    """
    try:
        # Update last updated timestamp
        twilio_config["last_updated"] = datetime.now().isoformat()
        
        # Save configuration
        with open("credentials/communication/twilio.json", "w") as f:
            json.dump(twilio_config, f, indent=4)
        
        logger.info("Twilio configuration saved successfully")
    
    except Exception as e:
        logger.exception(f"Error saving Twilio configuration: {e}")

async def main():
    """Run the communication test."""
    parser = argparse.ArgumentParser(description="Communication Test")
    
    # Email arguments
    parser.add_argument("--email-recipient", type=str, help="Email recipient")
    parser.add_argument("--email-subject", type=str, help="Email subject")
    parser.add_argument("--email-message", type=str, help="Email message")
    parser.add_argument("--smtp-server", type=str, help="SMTP server")
    parser.add_argument("--smtp-port", type=int, help="SMTP port")
    parser.add_argument("--smtp-username", type=str, help="SMTP username")
    parser.add_argument("--smtp-password", type=str, help="SMTP password")
    
    # SMS arguments
    parser.add_argument("--sms-recipient", type=str, help="SMS recipient")
    parser.add_argument("--sms-message", type=str, help="SMS message")
    parser.add_argument("--twilio-account-sid", type=str, help="Twilio Account SID")
    parser.add_argument("--twilio-auth-token", type=str, help="Twilio Auth Token")
    parser.add_argument("--twilio-phone-number", type=str, help="Twilio Phone Number")
    
    args = parser.parse_args()
    
    # Load configurations
    smtp_config = await load_smtp_config()
    twilio_config = await load_twilio_config()
    
    # Update SMTP configuration if provided
    if args.smtp_server:
        smtp_config["server"] = args.smtp_server
    
    if args.smtp_port:
        smtp_config["port"] = args.smtp_port
    
    if args.smtp_username:
        smtp_config["username"] = args.smtp_username
    
    if args.smtp_password:
        smtp_config["password"] = args.smtp_password
    
    # Update Twilio configuration if provided
    if args.twilio_account_sid:
        twilio_config["account_sid"] = args.twilio_account_sid
    
    if args.twilio_auth_token:
        twilio_config["auth_token"] = args.twilio_auth_token
    
    if args.twilio_phone_number:
        if "phone_numbers" not in twilio_config:
            twilio_config["phone_numbers"] = {}
        
        twilio_config["phone_numbers"]["sms"] = args.twilio_phone_number
        twilio_config["phone_numbers"]["default"] = args.twilio_phone_number
    
    # Save configurations
    await save_smtp_config(smtp_config)
    await save_twilio_config(twilio_config)
    
    # Test email sending if recipient is provided
    if args.email_recipient:
        subject = args.email_subject or "Insurance Lead Agent Test"
        message = args.email_message or "This is a test email from the Insurance Lead Agent."
        
        if smtp_config.get("server") and smtp_config.get("username") and smtp_config.get("password"):
            await test_email_sending(args.email_recipient, subject, message, smtp_config)
        else:
            logger.error("SMTP configuration is incomplete. Please provide server, username, and password.")
    
    # Test SMS sending if recipient is provided
    if args.sms_recipient:
        message = args.sms_message or "This is a test SMS from the Insurance Lead Agent."
        
        if twilio_config.get("account_sid") and twilio_config.get("auth_token") and twilio_config.get("phone_numbers", {}).get("sms"):
            await test_twilio_sms(args.sms_recipient, message, twilio_config)
        else:
            logger.error("Twilio configuration is incomplete. Please provide account SID, auth token, and phone number.")
    
    # If no recipient is provided, print help
    if not args.email_recipient and not args.sms_recipient:
        parser.print_help()

if __name__ == "__main__":
    asyncio.run(main())
