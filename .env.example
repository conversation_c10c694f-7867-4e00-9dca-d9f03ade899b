# System settings
DEBUG=False
LOG_LEVEL=INFO

# Database settings
DATABASE_URL=sqlite:///data/system.db

# LLM API settings
ANTHROPIC_API_KEY=your_anthropic_api_key
ENABLE_ANTHROPIC=True
OPENAI_API_KEY=your_openai_api_key
ENABLE_OPENAI=True
GROK_API_KEY=your_grok_api_key
ENABLE_GROK=False
DEFAULT_LLM_PROVIDER=anthropic

# Agent settings
ENABLE_INSURANCE_AGENT=True
INSURANCE_AGENT_LLM=anthropic
INSURANCE_AGENT_POLLING_INTERVAL=300

ENABLE_TRADING_AGENT=True
TRADING_AGENT_LLM=anthropic
TRADING_AGENT_POLLING_INTERVAL=60

ENABLE_SOCIAL_MEDIA_AGENT=True
SOCIAL_MEDIA_AGENT_LLM=anthropic
SOCIAL_MEDIA_AGENT_POLLING_INTERVAL=600

ENABLE_MUSIC_AGENT=True
MUSIC_AGENT_LLM=anthropic
MUSIC_AGENT_POLLING_INTERVAL=1800

ENABLE_COMMUNICATION_AGENT=True
COMMUNICATION_AGENT_LLM=anthropic
COMMUNICATION_AGENT_POLLING_INTERVAL=120

ENABLE_RESEARCH_AGENT=True
RESEARCH_AGENT_LLM=anthropic
RESEARCH_AGENT_POLLING_INTERVAL=300

ENABLE_CYBERSECURITY_AGENT=True
CYBERSECURITY_AGENT_LLM=anthropic
CYBERSECURITY_AGENT_POLLING_INTERVAL=300

ENABLE_EMAIL_AGENT=True
EMAIL_AGENT_LLM=anthropic
EMAIL_AGENT_POLLING_INTERVAL=300

ENABLE_MULTI_ACCOUNT_EMAIL_AGENT=True
MULTI_ACCOUNT_EMAIL_AGENT_LLM=anthropic
MULTI_ACCOUNT_EMAIL_AGENT_POLLING_INTERVAL=300

ENABLE_INSURANCE_LEAD_AGENT=True
INSURANCE_LEAD_AGENT_LLM=anthropic
INSURANCE_LEAD_AGENT_POLLING_INTERVAL=60

# Communication service settings
EMAIL_SERVICE=sendgrid
EMAIL_API_KEY=your_sendgrid_api_key
SENDER_EMAIL=<EMAIL>
ENABLE_EMAIL=False

SMS_SERVICE=twilio
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number
ENABLE_SMS=False

VOICE_SERVICE=twilio
ENABLE_VOICE=False

# Trading platform settings
ALPACA_API_KEY=your_alpaca_api_key
ALPACA_API_SECRET=your_alpaca_api_secret
ALPACA_BASE_URL=https://paper-api.alpaca.markets
ENABLE_ALPACA=False

CRYPTO_EXCHANGE=binance
CRYPTO_API_KEY=your_crypto_api_key
CRYPTO_API_SECRET=your_crypto_api_secret
ENABLE_CRYPTO=False

# Social media platform settings
TWITTER_API_KEY=your_twitter_api_key
TWITTER_API_SECRET=your_twitter_api_secret
TWITTER_ACCESS_TOKEN=your_twitter_access_token
TWITTER_ACCESS_TOKEN_SECRET=your_twitter_access_token_secret
ENABLE_TWITTER=False

FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret
FACEBOOK_ACCESS_TOKEN=your_facebook_access_token
ENABLE_FACEBOOK=False

INSTAGRAM_USERNAME=your_instagram_username
INSTAGRAM_PASSWORD=your_instagram_password
ENABLE_INSTAGRAM=False

YOUTUBE_API_KEY=your_youtube_api_key
YOUTUBE_CHANNEL_ID=your_youtube_channel_id
ENABLE_YOUTUBE=False

# Music platform settings
SPOTIFY_CLIENT_ID=your_spotify_client_id
SPOTIFY_CLIENT_SECRET=your_spotify_client_secret
SPOTIFY_REDIRECT_URI=your_spotify_redirect_uri
ENABLE_SPOTIFY=False

SOUNDCLOUD_CLIENT_ID=your_soundcloud_client_id
SOUNDCLOUD_CLIENT_SECRET=your_soundcloud_client_secret
ENABLE_SOUNDCLOUD=False

# Web interface settings
ENABLE_WEB_INTERFACE=True
WEB_HOST=0.0.0.0
WEB_PORT=8000

# GitHub settings
GITHUB_API_KEY=your_github_api_key
GITHUB_USERNAME=your_github_username
ENABLE_GITHUB=False

# Hugging Face settings
HUGGINGFACE_API_KEY=your_huggingface_api_key
HUGGINGFACE_USE_LOCAL=False
HUGGINGFACE_LOCAL_URL=http://localhost:8080
ENABLE_HUGGINGFACE=False
HUGGINGFACE_TEXT_GENERATION_ENDPOINT=your_text_generation_endpoint
HUGGINGFACE_EMBEDDINGS_ENDPOINT=your_embeddings_endpoint

# Security settings
SYSTEM_API_KEY=your_system_api_key
JWT_SECRET=your_jwt_secret
JWT_ALGORITHM=HS256
JWT_EXPIRATION=86400

# Tool service settings
ENABLE_TOOL_SERVICE=True
ENABLE_NMAP=True
ENABLE_JOHN=True
ENABLE_WIRESHARK=True
ENABLE_METASPLOIT=True
ENABLE_BURPSUITE=True
ENABLE_AIRCRACK=True
ENABLE_SQLMAP=True
ENABLE_ZAPROXY=True
ENABLE_THEHARVESTER=True
ENABLE_NIKTO=True
ENABLE_PENTESTGPT=True

# Vulnerability database settings
ENABLE_VULNERABILITY_DATABASE=True
NVD_API_KEY=your_nvd_api_key
VULNERABILITY_CACHE_TTL=86400

# MPC (Multi-Party Computation) server settings
ENABLE_MPC=True
MPC_LOCAL_HOST=localhost
MPC_LOCAL_PORT=8765
MPC_LOCAL_USE_SSL=False
MPC_LOCAL_CERT_FILE=
MPC_LOCAL_KEY_FILE=
ENABLE_MPC_LOCAL=True

MPC_REMOTE_HOST=
MPC_REMOTE_PORT=8765
MPC_REMOTE_USE_SSL=True
MPC_REMOTE_CA_FILE=
ENABLE_MPC_REMOTE=False

# Quantum computing settings
ENABLE_QUANTUM=True
QUANTUM_PROVIDER=simulator
QUANTUM_API_KEY=
QUANTUM_USE_LOCAL=True
QUANTUM_LOCAL_URL=http://localhost:8081

# Audio processing settings
ENABLE_AUDIO=True
AUDIO_API_KEY=
AUDIO_API_URL=
AUDIO_LOCAL_MODE=True
SPEECH_RECOGNITION_ENGINE=whisper
TEXT_TO_SPEECH_ENGINE=pyttsx3

# Machine learning settings
ENABLE_MACHINE_LEARNING=True
MACHINE_LEARNING_USE_LOCAL=True
MACHINE_LEARNING_API_KEY=
MACHINE_LEARNING_API_URL=
