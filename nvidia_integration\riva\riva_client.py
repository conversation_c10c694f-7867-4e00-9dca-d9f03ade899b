"""
NVIDIA Riva client for speech AI capabilities.
"""
import asyncio
import logging
import os
from typing import Dict, Optional, Any, Union, List
import json
import io
import wave
import numpy as np

from core.logger import setup_logger

# Optional imports
try:
    import riva.client
    import riva.client.audio_io
    RIVA_AVAILABLE = True
except ImportError:
    RIVA_AVAILABLE = False

# Set up logger
logger = setup_logger("riva_client")

class RivaClient:
    """
    Client for NVIDIA Riva speech AI services.
    
    This class provides capabilities for:
    - Automatic Speech Recognition (ASR)
    - Text-to-Speech (TTS)
    - Natural Language Processing (NLP)
    """
    
    def __init__(self, config: Dict):
        """
        Initialize the Riva client.
        
        Args:
            config (Dict): Configuration for Riva client
        """
        self.config = config
        self.enabled = config.get("enabled", False)
        self.server_url = config.get("server_url", "localhost:50051")
        self.language_code = config.get("language_code", "en-US")
        self.voice_name = config.get("voice_name", "English-US.Female-1")
        self.sample_rate_hz = config.get("sample_rate_hz", 16000)
        self.auth_key = config.get("auth_key", None)
        
        # Client objects
        self.asr_service = None
        self.tts_service = None
        self.nlp_service = None
        
        # Initialization status
        self.initialized = False
    
    async def initialize(self):
        """Initialize the Riva client and connect to the Riva server."""
        if not self.enabled:
            logger.info("Riva integration is disabled. Skipping initialization.")
            return
        
        if not RIVA_AVAILABLE:
            logger.warning("Riva client not available. Install nvidia-riva-client to enable Riva integration.")
            return
        
        logger.info("Initializing Riva client with server: %s", self.server_url)
        
        try:
            # Create auth object if auth_key is provided
            auth = None
            if self.auth_key:
                auth = riva.client.Auth(api_key=self.auth_key)
            
            # Initialize services
            self.asr_service = riva.client.ASRService(self.server_url, auth=auth)
            self.tts_service = riva.client.TTSService(self.server_url, auth=auth)
            
            try:
                self.nlp_service = riva.client.NLPService(self.server_url, auth=auth)
                logger.info("Riva NLP service initialized")
            except Exception as e:
                logger.warning("Riva NLP service not available: %s", e)
            
            self.initialized = True
            logger.info("Riva client initialized successfully")
            
        except Exception as e:
            logger.exception("Error initializing Riva client: %s", e)
    
    async def speech_to_text(self, audio_data: Union[bytes, str, np.ndarray], **kwargs) -> Dict:
        """
        Convert speech to text using Riva ASR.
        
        Args:
            audio_data: Audio data as bytes, file path, or numpy array
            **kwargs: Additional parameters for ASR
                - boosted_lm_words: List of words to boost in recognition
                - boosted_lm_score: Score for boosted words
                - audio_format: Format of audio data (wav, raw)
                
        Returns:
            Dict containing transcribed text and metadata
        """
        if not self.initialized or not self.asr_service:
            logger.warning("Riva ASR service not initialized")
            return {"success": False, "error": "Riva ASR service not initialized"}
        
        try:
            # Process parameters
            boosted_lm_words = kwargs.get("boosted_lm_words", self.config.get("asr_boosted_lm_words", []))
            boosted_lm_score = kwargs.get("boosted_lm_score", self.config.get("asr_boosted_lm_score", 10.0))
            audio_format = kwargs.get("audio_format", "wav")
            
            # Process audio data
            audio_samples = None
            
            # Case 1: audio_data is a path to a file
            if isinstance(audio_data, str) and os.path.isfile(audio_data):
                audio_samples = riva.client.audio_io.load_audio_from_file(
                    audio_data,
                    self.sample_rate_hz
                )
            
            # Case 2: audio_data is bytes representing a WAV file
            elif isinstance(audio_data, bytes):
                # Convert bytes to in-memory file-like object
                with io.BytesIO(audio_data) as audio_file:
                    with wave.open(audio_file, 'rb') as wav_file:
                        sample_rate = wav_file.getframerate()
                        audio_samples = np.frombuffer(wav_file.readframes(wav_file.getnframes()), dtype=np.int16)
                        
                        # Resample if needed
                        if sample_rate != self.sample_rate_hz:
                            # Simple resampling for demo purposes
                            # In production, use a proper resampling library
                            logger.warning("Audio sample rate mismatch. Resampling may affect quality.")
                            audio_samples = audio_samples[::int(sample_rate/self.sample_rate_hz)]
            
            # Case 3: audio_data is a numpy array
            elif isinstance(audio_data, np.ndarray):
                audio_samples = audio_data
            
            # If we couldn't process the audio data
            if audio_samples is None:
                return {"success": False, "error": "Invalid audio data format"}
            
            # Set up ASR config
            config = riva.client.RecognitionConfig(
                encoding=riva.client.AudioEncoding.LINEAR_PCM,
                language_code=self.language_code,
                max_alternatives=1,
                profanity_filter=False,
                enable_automatic_punctuation=True,
                audio_channel_count=1,
                sample_rate_hertz=self.sample_rate_hz
            )
            
            # Add boosted words if any
            if boosted_lm_words:
                config.speech_context.phrases.extend(boosted_lm_words)
                config.speech_context.boost = boosted_lm_score
            
            # Perform offline recognition
            response = self.asr_service.offline_recognize(audio_samples, config)
            
            # Extract results
            result = {
                "success": True,
                "transcript": "",
                "confidence": 0.0,
                "alternatives": []
            }
            
            for transcript in response.results:
                for alternative in transcript.alternatives:
                    if result["transcript"] == "":  # Take the first/best result as the main transcript
                        result["transcript"] = alternative.transcript
                        result["confidence"] = alternative.confidence
                    
                    result["alternatives"].append({
                        "transcript": alternative.transcript,
                        "confidence": alternative.confidence
                    })
            
            logger.info("ASR completed successfully with confidence: %.2f", result["confidence"])
            return result
            
        except Exception as e:
            logger.exception("Error in speech_to_text: %s", e)
            return {"success": False, "error": str(e)}
    
    async def text_to_speech(self, text: str, **kwargs) -> Dict:
        """
        Convert text to speech using Riva TTS.
        
        Args:
            text: Text to synthesize
            **kwargs: Additional parameters for TTS
                - voice_name: Voice to use for synthesis
                - speaking_rate: Speed of speech (0.5 to 2.0)
                - pitch: Voice pitch adjustment (-10.0 to 10.0)
                
        Returns:
            Dict containing synthesized audio data and metadata
        """
        if not self.initialized or not self.tts_service:
            logger.warning("Riva TTS service not initialized")
            return {"success": False, "error": "Riva TTS service not initialized"}
        
        try:
            # Process parameters
            voice_name = kwargs.get("voice_name", self.voice_name)
            speaking_rate = kwargs.get("speaking_rate", self.config.get("tts_speaking_rate", 1.0))
            pitch = kwargs.get("pitch", self.config.get("tts_pitch", 0.0))
            
            # Set up TTS request
            req = riva.client.SynthesizeSpeechRequest()
            req.text = text
            req.language_code = self.language_code
            req.encoding = riva.client.AudioEncoding.LINEAR_PCM
            req.sample_rate_hz = self.sample_rate_hz
            req.voice_name = voice_name
            
            # Set speaking rate and pitch
            if speaking_rate != 1.0:
                req.speaking_rate = speaking_rate
            if pitch != 0.0:
                req.pitch = pitch
            
            # Perform synthesis
            resp = self.tts_service.synthesize(req)
            
            # Create WAV file in memory
            audio_bytes = io.BytesIO()
            with wave.open(audio_bytes, 'wb') as wav_file:
                wav_file.setnchannels(1)  # Mono
                wav_file.setsampwidth(2)  # 16-bit
                wav_file.setframerate(self.sample_rate_hz)
                wav_file.writeframes(resp.audio)
            
            # Return audio data and metadata
            result = {
                "success": True,
                "audio_bytes": audio_bytes.getvalue(),
                "audio_format": "wav",
                "sample_rate": self.sample_rate_hz,
                "duration_seconds": len(resp.audio) / (2 * self.sample_rate_hz),  # 16-bit = 2 bytes per sample
                "voice_name": voice_name
            }
            
            logger.info("TTS completed successfully. Generated %.2f seconds of audio.", result["duration_seconds"])
            return result
            
        except Exception as e:
            logger.exception("Error in text_to_speech: %s", e)
            return {"success": False, "error": str(e)}
    
    async def perform_nlp_task(self, text: str, task: str, **kwargs) -> Dict:
        """
        Perform NLP tasks using Riva NLP.
        
        Args:
            text: Input text
            task: NLP task to perform (e.g., 'classification', 'named_entity_recognition')
            **kwargs: Additional parameters for the NLP task
                
        Returns:
            Dict containing NLP results
        """
        if not self.initialized or not self.nlp_service:
            logger.warning("Riva NLP service not initialized")
            return {"success": False, "error": "Riva NLP service not initialized"}
        
        try:
            result = {"success": True, "task": task}
            
            if task == "classification":
                # Text classification
                model_name = kwargs.get("model_name", "riva-intent")
                resp = self.nlp_service.classify_text(text, model_name)
                
                # Extract results
                result["classes"] = []
                for label in resp.results[0].labels:
                    result["classes"].append({
                        "class": label.class_name,
                        "score": label.score
                    })
                
            elif task == "named_entity_recognition":
                # Named entity recognition
                model_name = kwargs.get("model_name", "riva-ner")
                resp = self.nlp_service.analyze_entities(text, model_name)
                
                # Extract results
                result["entities"] = []
                for entity in resp.results[0].entities:
                    result["entities"].append({
                        "text": entity.text,
                        "type": entity.type,
                        "start_offset": entity.start_offset,
                        "end_offset": entity.end_offset,
                        "score": entity.score
                    })
                
            elif task == "punctuation":
                # Punctuation and capitalization
                resp = self.nlp_service.punctuate_text(text)
                result["punctuated_text"] = resp.results[0].punctuated_text
                
            else:
                return {"success": False, "error": f"Unsupported NLP task: {task}"}
            
            logger.info("NLP task '%s' completed successfully", task)
            return result
            
        except Exception as e:
            logger.exception("Error in perform_nlp_task: %s", e)
            return {"success": False, "error": str(e)}
        
    async def shutdown(self):
        """Shutdown the Riva client and release resources."""
        if self.initialized:
            logger.info("Shutting down Riva client")
            
            # No explicit cleanup needed for Riva clients
            self.asr_service = None
            self.tts_service = None
            self.nlp_service = None
            
            self.initialized = False