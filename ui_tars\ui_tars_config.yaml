advanced:
  debug: false
  development: false
  performance:
    gc_interval: 300
    max_memory: 4096
    thread_pool_size: 4
  security:
    file_system_access: true
    network_access: true
    sandbox: true
android:
  auto_start: false
  default_device_id: ''
  enabled: false
browser:
  alternatives:
  - edge
  - firefox
  automation:
    auto_start: false
    enabled: true
    navigation:
      timeout: 30
      wait_for_load: true
    screenshots:
      auto_capture: true
      format: png
      quality: 80
  default_url: https://www.google.com
  type: chrome
dashboard:
  enabled: true
  refresh_interval: 5
  theme: dark
  window:
    height: 768
    width: 1024
general:
  auto_start: true
  log_level: info
  name: UI-TARS Integration
  version: '1.5'
integration:
  auto_start: true
  enabled: true
  message_queue:
    max_size: 100
    timeout: 5
  state_management:
    file_path: settings/ui_tars_state.json
    persistence: true
    save_interval: 60
local_llm:
  enabled: true
  model_type: ui-tars
  models:
    llama: C:/Users/<USER>/models/Llama-3-8B
    mistral: C:/Users/<USER>/models/Mistral-7B
    qwen: C:/Users/<USER>/models/Qwen2.5-VL-7B
    ui-tars: C:/Users/<USER>/models\UI-TARS-1.5-7B
  quantization: 4bit
  server:
    auto_start: true
    host: localhost
    port: 8000
nvidia:
  cuda:
    device: 0
    enabled: true
  enabled: true
  tensorrt:
    enabled: true
    precision: fp16
ui_tars:
  api_key: null
  autonomous_mode: true
  model_name: UI-TARS-1.5-7B
  use_local_model: true
  voice_activation_keyword: tars
  voice_commands_enabled: true
voice:
  enabled: true
  recognition:
    keyword: tars
    language: en-US
    threshold: 0.7
  tts:
    enabled: true
    rate: 150
    voice: ''
