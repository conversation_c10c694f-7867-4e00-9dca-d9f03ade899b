# UI-TARS Local Setup

This guide will help you set up UI-TARS 1.5 to work with your local LLMs without requiring a paid Hugging Face subscription.

## Quick Start

1. Run the simple server:
   ```
   .\start_simple_server.bat
   ```

2. Configure UI-TARS with these settings:
   - **VLM Provider**: Hugging Face
   - **VLM Base URL**: http://127.0.0.1:8000
   - **VLM API Key**: dummy_key
   - **VLM Model Name**: UI-TARS-1.5-7B

3. Start using UI-TARS with your local setup!

## What's Included

- **simple_hf_server.py**: A simple API server that mimics the Hugging Face API but returns dummy responses
- **start_simple_server.bat**: A batch script to start the simple server
- **advanced_local_hf_server.py**: An advanced server that can use your actual local models
- **start_advanced_local_hf_server.bat**: A batch script to start the advanced server
- **UI-TARS-LOCAL-SETUP.md**: Detailed setup instructions

## How It Works

Since UI-TARS 1.5 only supports Hugging Face and VoiceArk as VLM providers, we've created a local API server that mimics the Hugging Face API. This server runs on your local machine and responds to requests from UI-TARS.

When UI-TARS sends a request to the server, the server returns either a dummy response or a response generated by your local model, depending on which server you're using.

## Offline Operation

This setup allows UI-TARS to work completely offline:

1. The local API server runs on your machine and doesn't require internet access
2. UI-TARS communicates with the local server instead of the Hugging Face API
3. Your local LLMs run on your machine and don't require internet access

The only time internet access is required is when you specifically ask UI-TARS to perform a task that requires internet access, such as browsing a website or searching for information.

## Advanced Usage

If you want to use your actual local models instead of dummy responses, you can use the advanced server:

1. Make sure your model is properly installed at the specified path
2. Run the advanced server:
   ```
   .\start_advanced_local_hf_server.bat
   ```
3. When prompted, enter the path to your model and choose whether to use dummy responses

## Troubleshooting

If you encounter any issues:

1. Make sure the server is running
2. Check if the Base URL is correct: `http://127.0.0.1:8000`
3. Try restarting UI-TARS
4. Check the server logs for any error messages

## Next Steps

Now that you have UI-TARS set up to work with your local LLMs, you can:

1. Integrate it with your AI agent system
2. Use it for autonomous browser control
3. Enable voice commands
4. Explore its capabilities for desktop automation

Remember to keep the server running whenever you're using UI-TARS. If you close the server, UI-TARS will no longer be able to generate responses.
