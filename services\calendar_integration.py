"""
Calendar Integration Service for the Multi-Agent AI System.

This module provides integration with Google Calendar and Calendly to enable
agents to set and manage appointments.
"""
import asyncio
import json
import logging
import os
import sys
import time
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
import aiohttp
import base64
import re

# Add parent directory to path to import from core
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.logger import setup_logger

# Set up logger
logger = setup_logger("calendar_integration")

class CalendarIntegration:
    """
    Calendar Integration Service for the Multi-Agent AI System.

    This class provides integration with Google Calendar and Calendly to enable
    agents to set and manage appointments.
    """

    def __init__(self, config: Dict):
        """
        Initialize the calendar integration service.

        Args:
            config (Dict): Service configuration
        """
        self.config = config
        self.enabled = config.get("enabled", True)

        # Google Calendar settings
        self.google_calendar_enabled = config.get("google_calendar_enabled", True)
        self.google_calendar_credentials = config.get("google_calendar_credentials", {})
        self.google_calendar_token_path = config.get("google_calendar_token_path", "")
        self.google_calendar_scopes = config.get("google_calendar_scopes", ["https://www.googleapis.com/auth/calendar"])
        self.default_calendar_id = config.get("default_calendar_id", "primary")

        # Calendly settings
        self.calendly_enabled = config.get("calendly_enabled", True)
        self.calendly_api_key = config.get("calendly_api_key", "")
        self.calendly_user_uri = config.get("calendly_user_uri", "")

        # Service status
        self.service_status = {
            "google_calendar": {"status": "unknown", "last_checked": None},
            "calendly": {"status": "unknown", "last_checked": None}
        }

        logger.info("Calendar integration service initialized")

    async def initialize(self):
        """Initialize the calendar integration service."""
        if not self.enabled:
            logger.warning("Calendar integration service is disabled")
            return

        try:
            # Check service status
            await self._check_service_status()

            logger.info("Calendar integration service initialized")

        except Exception as e:
            logger.exception(f"Error initializing calendar integration service: {e}")
            self.enabled = False

    async def _check_service_status(self):
        """Check the status of all calendar services."""
        # Check Google Calendar
        if self.google_calendar_enabled:
            google_calendar_status = await self._check_google_calendar_status()
            self.service_status["google_calendar"] = google_calendar_status

        # Check Calendly
        if self.calendly_enabled:
            calendly_status = await self._check_calendly_status()
            self.service_status["calendly"] = calendly_status

    async def _check_google_calendar_status(self) -> Dict:
        """
        Check the status of Google Calendar service.

        Returns:
            Dict: Service status
        """
        try:
            # Check if token file exists
            if not os.path.exists(self.google_calendar_token_path):
                return {
                    "status": "unavailable",
                    "last_checked": datetime.now().isoformat(),
                    "error": "Token file not found"
                }

            # Load token
            with open(self.google_calendar_token_path, "r") as f:
                token = json.load(f)

            # Check if token is expired
            if "expiry" in token:
                expiry = datetime.fromisoformat(token["expiry"].replace("Z", "+00:00"))
                if expiry < datetime.now():
                    return {
                        "status": "unavailable",
                        "last_checked": datetime.now().isoformat(),
                        "error": "Token expired"
                    }

            # Make a test API call
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Bearer {token['token']}",
                    "Content-Type": "application/json"
                }

                async with session.get(
                    f"https://www.googleapis.com/calendar/v3/calendars/{self.default_calendar_id}/events?maxResults=1",
                    headers=headers
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            "status": "available",
                            "last_checked": datetime.now().isoformat(),
                            "details": {
                                "calendar_id": self.default_calendar_id,
                                "events_count": len(data.get("items", []))
                            }
                        }
                    else:
                        error_data = await response.text()
                        return {
                            "status": "unavailable",
                            "last_checked": datetime.now().isoformat(),
                            "error": f"API call failed: {error_data}"
                        }

        except Exception as e:
            logger.exception(f"Error checking Google Calendar status: {e}")
            return {
                "status": "error",
                "last_checked": datetime.now().isoformat(),
                "error": str(e)
            }

    async def _check_calendly_status(self) -> Dict:
        """
        Check the status of Calendly service.

        Returns:
            Dict: Service status
        """
        try:
            if not self.calendly_api_key:
                return {
                    "status": "unavailable",
                    "last_checked": datetime.now().isoformat(),
                    "error": "API key not provided"
                }

            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Bearer {self.calendly_api_key}",
                    "Content-Type": "application/json"
                }

                async with session.get("https://api.calendly.com/users/me", headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            "status": "available",
                            "last_checked": datetime.now().isoformat(),
                            "details": data
                        }
                    else:
                        error_data = await response.text()
                        return {
                            "status": "unavailable",
                            "last_checked": datetime.now().isoformat(),
                            "error": f"API call failed: {error_data}"
                        }

        except Exception as e:
            logger.exception(f"Error checking Calendly status: {e}")
            return {
                "status": "error",
                "last_checked": datetime.now().isoformat(),
                "error": str(e)
            }

    async def create_google_calendar_event(self, event_data: Dict) -> Dict:
        """
        Create an event in Google Calendar.

        Args:
            event_data (Dict): Event data
                - summary (str): Event summary/title
                - description (str): Event description
                - start_time (str): Start time in ISO format
                - end_time (str): End time in ISO format
                - location (str, optional): Event location
                - attendees (List[str], optional): List of attendee email addresses
                - calendar_id (str, optional): Calendar ID

        Returns:
            Dict: Event creation result
        """
        if not self.enabled or not self.google_calendar_enabled:
            return {"error": "Google Calendar integration is disabled"}

        # Check if Google Calendar is available
        if self.service_status["google_calendar"]["status"] != "available":
            return {"error": "Google Calendar service is not available"}

        # Get event data
        summary = event_data.get("summary")
        description = event_data.get("description")
        start_time = event_data.get("start_time")
        end_time = event_data.get("end_time")
        location = event_data.get("location")
        attendees = event_data.get("attendees", [])
        calendar_id = event_data.get("calendar_id", self.default_calendar_id)

        # Validate required fields
        if not summary or not start_time or not end_time:
            return {"error": "Missing required fields: summary, start_time, end_time"}

        try:
            # Load token
            with open(self.google_calendar_token_path, "r") as f:
                token = json.load(f)

            # Prepare event data
            event = {
                "summary": summary,
                "description": description,
                "start": {
                    "dateTime": start_time,
                    "timeZone": "America/New_York"  # Default timezone
                },
                "end": {
                    "dateTime": end_time,
                    "timeZone": "America/New_York"  # Default timezone
                }
            }

            if location:
                event["location"] = location

            if attendees:
                event["attendees"] = [{"email": email} for email in attendees]

            # Create event
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Bearer {token['token']}",
                    "Content-Type": "application/json"
                }

                async with session.post(
                    f"https://www.googleapis.com/calendar/v3/calendars/{calendar_id}/events",
                    headers=headers,
                    json=event
                ) as response:
                    if response.status in [200, 201]:
                        data = await response.json()
                        return {
                            "success": True,
                            "event_id": data.get("id"),
                            "html_link": data.get("htmlLink"),
                            "details": data
                        }
                    else:
                        error_data = await response.text()
                        return {
                            "error": f"Failed to create event: {error_data}",
                            "status_code": response.status
                        }

        except Exception as e:
            logger.exception(f"Error creating Google Calendar event: {e}")
            return {"error": str(e)}

    async def get_google_calendar_events(self, params: Dict = None) -> Dict:
        """
        Get events from Google Calendar.

        Args:
            params (Dict, optional): Query parameters
                - calendar_id (str, optional): Calendar ID
                - time_min (str, optional): Minimum time in ISO format
                - time_max (str, optional): Maximum time in ISO format
                - max_results (int, optional): Maximum number of results
                - q (str, optional): Search query

        Returns:
            Dict: Events result
        """
        if not self.enabled or not self.google_calendar_enabled:
            return {"error": "Google Calendar integration is disabled"}

        # Check if Google Calendar is available
        if self.service_status["google_calendar"]["status"] != "available":
            return {"error": "Google Calendar service is not available"}

        # Get parameters
        params = params or {}
        calendar_id = params.get("calendar_id", self.default_calendar_id)
        time_min = params.get("time_min", datetime.now().isoformat())
        time_max = params.get("time_max", (datetime.now() + timedelta(days=7)).isoformat())
        max_results = params.get("max_results", 10)
        q = params.get("q")

        try:
            # Load token
            with open(self.google_calendar_token_path, "r") as f:
                token = json.load(f)

            # Prepare query parameters
            query_params = {
                "timeMin": time_min,
                "timeMax": time_max,
                "maxResults": max_results,
                "singleEvents": "true",
                "orderBy": "startTime"
            }

            if q:
                query_params["q"] = q

            # Get events
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Bearer {token['token']}",
                    "Content-Type": "application/json"
                }

                async with session.get(
                    f"https://www.googleapis.com/calendar/v3/calendars/{calendar_id}/events",
                    headers=headers,
                    params=query_params
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            "success": True,
                            "events": data.get("items", []),
                            "next_page_token": data.get("nextPageToken")
                        }
                    else:
                        error_data = await response.text()
                        return {
                            "error": f"Failed to get events: {error_data}",
                            "status_code": response.status
                        }

        except Exception as e:
            logger.exception(f"Error getting Google Calendar events: {e}")
            return {"error": str(e)}

    async def get_calendly_event_types(self) -> Dict:
        """
        Get event types from Calendly.

        Returns:
            Dict: Event types result
        """
        if not self.enabled or not self.calendly_enabled:
            return {"error": "Calendly integration is disabled"}

        # Check if Calendly is available
        if self.service_status["calendly"]["status"] != "available":
            return {"error": "Calendly service is not available"}

        try:
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Bearer {self.calendly_api_key}",
                    "Content-Type": "application/json"
                }

                # Get user URI if not provided
                user_uri = self.calendly_user_uri
                if not user_uri:
                    async with session.get("https://api.calendly.com/users/me", headers=headers) as response:
                        if response.status == 200:
                            data = await response.json()
                            user_uri = data.get("resource", {}).get("uri")
                        else:
                            error_data = await response.text()
                            return {
                                "error": f"Failed to get user URI: {error_data}",
                                "status_code": response.status
                            }

                # Get event types
                async with session.get(
                    "https://api.calendly.com/event_types",
                    headers=headers,
                    params={"user": user_uri}
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            "success": True,
                            "event_types": data.get("collection", []),
                            "pagination": data.get("pagination", {})
                        }
                    else:
                        error_data = await response.text()
                        return {
                            "error": f"Failed to get event types: {error_data}",
                            "status_code": response.status
                        }

        except Exception as e:
            logger.exception(f"Error getting Calendly event types: {e}")
            return {"error": str(e)}

    async def get_calendly_availability(self, event_type_uri: str, params: Dict = None) -> Dict:
        """
        Get availability for a Calendly event type.

        Args:
            event_type_uri (str): Event type URI
            params (Dict, optional): Query parameters
                - start_time (str, optional): Start time in ISO format
                - end_time (str, optional): End time in ISO format

        Returns:
            Dict: Availability result
        """
        if not self.enabled or not self.calendly_enabled:
            return {"error": "Calendly integration is disabled"}

        # Check if Calendly is available
        if self.service_status["calendly"]["status"] != "available":
            return {"error": "Calendly service is not available"}

        # Get parameters
        params = params or {}
        start_time = params.get("start_time", datetime.now().isoformat())
        end_time = params.get("end_time", (datetime.now() + timedelta(days=7)).isoformat())

        try:
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Bearer {self.calendly_api_key}",
                    "Content-Type": "application/json"
                }

                # Get availability
                async with session.get(
                    "https://api.calendly.com/scheduling_links",
                    headers=headers,
                    params={
                        "event_type": event_type_uri,
                        "start_time": start_time,
                        "end_time": end_time
                    }
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            "success": True,
                            "scheduling_links": data.get("collection", []),
                            "pagination": data.get("pagination", {})
                        }
                    else:
                        error_data = await response.text()
                        return {
                            "error": f"Failed to get availability: {error_data}",
                            "status_code": response.status
                        }

        except Exception as e:
            logger.exception(f"Error getting Calendly availability: {e}")
            return {"error": str(e)}

    async def create_calendly_scheduling_link(self, event_type_uri: str, params: Dict = None) -> Dict:
        """
        Create a scheduling link for a Calendly event type.

        Args:
            event_type_uri (str): Event type URI
            params (Dict, optional): Link parameters
                - max_booking_time (str, optional): Maximum booking time in ISO format
                - name (str, optional): Name for the scheduling link

        Returns:
            Dict: Scheduling link result
        """
        if not self.enabled or not self.calendly_enabled:
            return {"error": "Calendly integration is disabled"}

        # Check if Calendly is available
        if self.service_status["calendly"]["status"] != "available":
            return {"error": "Calendly service is not available"}

        # Get parameters
        params = params or {}
        max_booking_time = params.get("max_booking_time", (datetime.now() + timedelta(days=30)).isoformat())
        name = params.get("name", f"Scheduling Link {datetime.now().strftime('%Y-%m-%d')}")

        try:
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Bearer {self.calendly_api_key}",
                    "Content-Type": "application/json"
                }

                # Create scheduling link
                payload = {
                    "max_booking_time": max_booking_time,
                    "owner": self.calendly_user_uri,
                    "owner_type": "User",
                    "event_type": event_type_uri
                }

                if name:
                    payload["name"] = name

                async with session.post(
                    "https://api.calendly.com/scheduling_links",
                    headers=headers,
                    json={"resource": payload}
                ) as response:
                    if response.status in [200, 201]:
                        data = await response.json()
                        return {
                            "success": True,
                            "scheduling_link": data.get("resource", {}),
                            "booking_url": data.get("resource", {}).get("booking_url")
                        }
                    else:
                        error_data = await response.text()
                        return {
                            "error": f"Failed to create scheduling link: {error_data}",
                            "status_code": response.status
                        }

        except Exception as e:
            logger.exception(f"Error creating Calendly scheduling link: {e}")
            return {"error": str(e)}

    async def parse_appointment_request(self, text: str) -> Dict:
        """
        Parse an appointment request from text.

        Args:
            text (str): Text containing appointment request

        Returns:
            Dict: Parsed appointment data
        """
        try:
            # Extract date
            date_patterns = [
                r"(?:on|for|at)\s+(\w+day,?\s+\w+\s+\d{1,2}(?:st|nd|rd|th)?)",  # Monday, January 1st
                r"(?:on|for|at)\s+(\d{1,2}(?:st|nd|rd|th)?\s+\w+)",  # 1st January
                r"(?:on|for|at)\s+(\w+\s+\d{1,2}(?:st|nd|rd|th)?)",  # January 1st
                r"(\d{1,2}/\d{1,2}/\d{2,4})",  # MM/DD/YYYY or DD/MM/YYYY
                r"(\d{1,2}-\d{1,2}-\d{2,4})",  # MM-DD-YYYY or DD-MM-YYYY
                r"(\d{4}-\d{1,2}-\d{1,2})"  # YYYY-MM-DD
            ]

            date_str = None
            for pattern in date_patterns:
                match = re.search(pattern, text)
                if match:
                    date_str = match.group(1)
                    break

            # Extract time
            time_patterns = [
                r"(?:at|from)\s+(\d{1,2}:\d{2}\s*(?:am|pm|AM|PM))",  # 10:30 AM
                r"(?:at|from)\s+(\d{1,2}\s*(?:am|pm|AM|PM))",  # 10 AM
                r"(\d{1,2}:\d{2}\s*(?:am|pm|AM|PM))",  # 10:30 AM
                r"(\d{1,2}\s*(?:am|pm|AM|PM))"  # 10 AM
            ]

            time_str = None
            for pattern in time_patterns:
                match = re.search(pattern, text)
                if match:
                    time_str = match.group(1)
                    break

            # Extract duration
            duration_patterns = [
                r"(?:for|of)\s+(\d+)\s*(?:hour|hr)s?",  # for 1 hour
                r"(?:for|of)\s+(\d+)\s*(?:minute|min)s?",  # for 30 minutes
                r"(\d+)\s*(?:hour|hr)s?\s+(?:appointment|meeting)",  # 1 hour appointment
                r"(\d+)\s*(?:minute|min)s?\s+(?:appointment|meeting)"  # 30 minute appointment
            ]

            duration = None
            duration_unit = None
            for pattern in duration_patterns:
                match = re.search(pattern, text)
                if match:
                    duration = int(match.group(1))
                    duration_unit = "hour" if "hour" in pattern or "hr" in pattern else "minute"
                    break

            # Extract name
            name_patterns = [
                r"(?:name is|this is|I am|I'm)\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)",  # My name is John Smith
                r"(?:for|with)\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)"  # for John Smith
            ]

            name = None
            for pattern in name_patterns:
                match = re.search(pattern, text)
                if match:
                    name = match.group(1)
                    break

            # Extract email
            email_pattern = r"([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})"
            email_match = re.search(email_pattern, text)
            email = email_match.group(1) if email_match else None

            # Extract phone
            phone_pattern = r"(\+?\d{1,3}[-.\s]?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})"
            phone_match = re.search(phone_pattern, text)
            phone = phone_match.group(1) if phone_match else None

            # Extract purpose/topic
            purpose_patterns = [
                r"(?:about|regarding|for|to discuss)\s+(.+?)(?:\.|\n|$)",  # about insurance options.
                r"(?:purpose is|reason is|topic is)\s+(.+?)(?:\.|\n|$)"  # purpose is to discuss insurance options.
            ]

            purpose = None
            for pattern in purpose_patterns:
                match = re.search(pattern, text)
                if match:
                    purpose = match.group(1).strip()
                    break

            # Prepare result
            result = {
                "success": True,
                "date": date_str,
                "time": time_str,
                "duration": duration,
                "duration_unit": duration_unit,
                "name": name,
                "email": email,
                "phone": phone,
                "purpose": purpose,
                "original_text": text
            }

            return result

        except Exception as e:
            logger.exception(f"Error parsing appointment request: {e}")
            return {
                "error": str(e),
                "original_text": text
            }
