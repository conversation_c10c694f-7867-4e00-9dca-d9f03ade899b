"""
Evolutionary Optimizer for AlphaEvolve.

This module provides evolutionary optimization capabilities for AlphaEvolve,
enabling the evolution of code through selection, crossover, and mutation.
"""
import asyncio
import json
import logging
import os
import random
import copy
from typing import Dict, List, Optional, Any, Union, Tuple
import uuid
from datetime import datetime

# Add the project root to the Python path
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

from core.logger import setup_logger
from alpha_evolve.llm_code_generator import LLMCodeGenerator

# Set up logger
logger = setup_logger("evolutionary_optimizer")

class EvolutionaryOptimizer:
    """
    Evolutionary Optimizer for AlphaEvolve.

    This class provides evolutionary optimization capabilities for AlphaEvolve,
    enabling the evolution of code through selection, crossover, and mutation.
    """

    def __init__(self, config: Dict = None):
        """
        Initialize the Evolutionary Optimizer.

        Args:
            config (Dict, optional): Configuration
        """
        self.config = config or {}
        self.initialized = False
        
        # Evolutionary parameters
        self.population_size = self.config.get("population_size", 50)
        self.tournament_size = self.config.get("tournament_size", 5)
        self.crossover_rate = self.config.get("crossover_rate", 0.8)
        self.mutation_rate = self.config.get("mutation_rate", 0.2)
        self.elitism = self.config.get("elitism", 2)
        
        # Evolution history
        self.evolution_history = []
        
    async def initialize(self):
        """Initialize the Evolutionary Optimizer."""
        logger.info("Initializing Evolutionary Optimizer")
        
        self.initialized = True
        logger.info("Evolutionary Optimizer initialized")
    
    async def create_next_generation(
        self,
        population: List[Dict],
        fitness_scores: List[float],
        code_generator: LLMCodeGenerator,
        problem: Dict,
    ) -> List[Dict]:
        """
        Create the next generation through selection, crossover, and mutation.
        
        Args:
            population (List[Dict]): Current population
            fitness_scores (List[float]): Fitness scores for current population
            code_generator (LLMCodeGenerator): Code generator for mutations
            problem (Dict): Problem definition
            
        Returns:
            List[Dict]: New population
        """
        if not self.initialized:
            await self.initialize()
        
        # Create new population
        new_population = []
        
        # Apply elitism (keep best solutions)
        if self.elitism > 0:
            # Sort population by fitness
            sorted_population = [x for _, x in sorted(
                zip(fitness_scores, population),
                key=lambda pair: pair[0],
                reverse=True
            )]
            
            # Add elite solutions to new population
            for i in range(min(self.elitism, len(sorted_population))):
                elite = copy.deepcopy(sorted_population[i])
                elite["metadata"]["elite"] = True
                new_population.append(elite)
        
        # Fill the rest of the population
        while len(new_population) < len(population):
            # Determine operation: crossover or mutation
            if random.random() < self.crossover_rate and len(population) >= 2:
                # Select parents
                parent1 = await self._tournament_selection(population, fitness_scores)
                parent2 = await self._tournament_selection(population, fitness_scores)
                
                # Perform crossover
                child = await self._crossover(parent1, parent2, code_generator)
                
                # Add to new population
                new_population.append(child)
            else:
                # Select parent
                parent = await self._tournament_selection(population, fitness_scores)
                
                # Perform mutation
                child = await self._mutate(parent, code_generator)
                
                # Add to new population
                new_population.append(child)
        
        # Ensure population size is maintained
        if len(new_population) > len(population):
            new_population = new_population[:len(population)]
        
        return new_population
    
    async def _tournament_selection(
        self,
        population: List[Dict],
        fitness_scores: List[float],
    ) -> Dict:
        """
        Select a solution using tournament selection.
        
        Args:
            population (List[Dict]): Population
            fitness_scores (List[float]): Fitness scores
            
        Returns:
            Dict: Selected solution
        """
        # Select tournament participants
        tournament_size = min(self.tournament_size, len(population))
        tournament_indices = random.sample(range(len(population)), tournament_size)
        
        # Find best solution in tournament
        best_index = tournament_indices[0]
        best_fitness = fitness_scores[best_index]
        
        for idx in tournament_indices[1:]:
            if fitness_scores[idx] > best_fitness:
                best_index = idx
                best_fitness = fitness_scores[idx]
        
        return population[best_index]
    
    async def _crossover(
        self,
        parent1: Dict,
        parent2: Dict,
        code_generator: LLMCodeGenerator,
    ) -> Dict:
        """
        Perform crossover between two parent solutions.
        
        Args:
            parent1 (Dict): First parent
            parent2 (Dict): Second parent
            code_generator (LLMCodeGenerator): Code generator
            
        Returns:
            Dict: Child solution
        """
        try:
            # Get parent codes
            code1 = parent1["code"]
            code2 = parent2["code"]
            
            # Create crossover prompt
            prompt = "Combine the best aspects of both code snippets to create an improved solution."
            
            # Generate combined code
            combined_code = await code_generator.combine_code(code1, code2, prompt)
            
            # Create child solution
            child = {
                "id": str(uuid.uuid4()),
                "code": combined_code,
                "prompt": f"Crossover of solutions {parent1['id']} and {parent2['id']}",
                "generation": max(parent1.get("generation", 0), parent2.get("generation", 0)) + 1,
                "parent_ids": [parent1["id"], parent2["id"]],
                "metadata": {
                    "operation": "crossover",
                    "timestamp": datetime.now().isoformat(),
                },
            }
            
            return child
        
        except Exception as e:
            logger.error(f"Error during crossover: {e}")
            
            # If crossover fails, return a copy of the better parent
            if parent1.get("fitness", 0) > parent2.get("fitness", 0):
                better_parent = parent1
            else:
                better_parent = parent2
            
            child = copy.deepcopy(better_parent)
            child["id"] = str(uuid.uuid4())
            child["generation"] = better_parent.get("generation", 0) + 1
            child["parent_ids"] = [better_parent["id"]]
            child["metadata"] = {
                "operation": "crossover_fallback",
                "error": str(e),
                "timestamp": datetime.now().isoformat(),
            }
            
            return child
    
    async def _mutate(
        self,
        parent: Dict,
        code_generator: LLMCodeGenerator,
    ) -> Dict:
        """
        Perform mutation on a parent solution.
        
        Args:
            parent (Dict): Parent solution
            code_generator (LLMCodeGenerator): Code generator
            
        Returns:
            Dict: Mutated solution
        """
        try:
            # Get parent code
            code = parent["code"]
            
            # Select mutation type
            mutation_types = ["efficiency", "readability", "algorithm", "structure", "random"]
            mutation_type = random.choice(mutation_types)
            
            # Generate mutated code
            mutated_code = await code_generator.mutate_code(code, mutation_type)
            
            # Create mutated solution
            child = {
                "id": str(uuid.uuid4()),
                "code": mutated_code,
                "prompt": f"Mutation ({mutation_type}) of solution {parent['id']}",
                "generation": parent.get("generation", 0) + 1,
                "parent_ids": [parent["id"]],
                "metadata": {
                    "operation": "mutation",
                    "mutation_type": mutation_type,
                    "timestamp": datetime.now().isoformat(),
                },
            }
            
            return child
        
        except Exception as e:
            logger.error(f"Error during mutation: {e}")
            
            # If mutation fails, return a slightly modified copy of the parent
            child = copy.deepcopy(parent)
            child["id"] = str(uuid.uuid4())
            child["generation"] = parent.get("generation", 0) + 1
            child["parent_ids"] = [parent["id"]]
            child["metadata"] = {
                "operation": "mutation_fallback",
                "error": str(e),
                "timestamp": datetime.now().isoformat(),
            }
            
            # Add a comment to the code
            child["code"] = f"{child['code']}\n# Mutation failed: {str(e)}"
            
            return child
    
    async def evolve_population(
        self,
        initial_population: List[Dict],
        fitness_function: callable,
        code_generator: LLMCodeGenerator,
        generations: int = 10,
        fitness_threshold: float = 0.95,
    ) -> Tuple[List[Dict], List[float]]:
        """
        Evolve a population over multiple generations.
        
        Args:
            initial_population (List[Dict]): Initial population
            fitness_function (callable): Function to evaluate fitness
            code_generator (LLMCodeGenerator): Code generator
            generations (int, optional): Number of generations
            fitness_threshold (float, optional): Fitness threshold to stop evolution
            
        Returns:
            Tuple[List[Dict], List[float]]: Final population and fitness scores
        """
        # Initialize population
        population = initial_population
        
        # Evaluate initial population
        fitness_scores = []
        for solution in population:
            fitness = await fitness_function(solution)
            fitness_scores.append(fitness)
            solution["fitness"] = fitness
        
        # Evolution history
        history = [{
            "generation": 0,
            "best_fitness": max(fitness_scores) if fitness_scores else 0,
            "avg_fitness": sum(fitness_scores) / len(fitness_scores) if fitness_scores else 0,
            "best_solution_id": population[fitness_scores.index(max(fitness_scores))]["id"] if fitness_scores else None,
        }]
        
        # Main evolution loop
        for generation in range(generations):
            # Check if fitness threshold reached
            if max(fitness_scores) >= fitness_threshold:
                logger.info(f"Fitness threshold {fitness_threshold} reached at generation {generation}")
                break
            
            # Create next generation
            population = await self.create_next_generation(
                population, fitness_scores, code_generator, {}
            )
            
            # Evaluate new population
            fitness_scores = []
            for solution in population:
                fitness = await fitness_function(solution)
                fitness_scores.append(fitness)
                solution["fitness"] = fitness
            
            # Record history
            history.append({
                "generation": generation + 1,
                "best_fitness": max(fitness_scores) if fitness_scores else 0,
                "avg_fitness": sum(fitness_scores) / len(fitness_scores) if fitness_scores else 0,
                "best_solution_id": population[fitness_scores.index(max(fitness_scores))]["id"] if fitness_scores else None,
            })
            
            logger.info(f"Generation {generation + 1}: Best fitness = {max(fitness_scores)}, Avg fitness = {sum(fitness_scores) / len(fitness_scores)}")
        
        # Record evolution
        self.evolution_history.append({
            "id": str(uuid.uuid4()),
            "start_time": datetime.now().isoformat(),
            "generations": len(history) - 1,
            "history": history,
            "final_best_fitness": max(fitness_scores) if fitness_scores else 0,
        })
        
        return population, fitness_scores
    
    async def shutdown(self):
        """Shutdown the Evolutionary Optimizer."""
        logger.info("Shutting down Evolutionary Optimizer")
        logger.info("Evolutionary Optimizer shut down")
