@echo off
echo Enhanced UI-TARS Diagnostic and Startup Tool
echo ==========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Ask for UI-TARS path
echo Enter the path to UI-TARS executable (leave empty to auto-detect):
set /p UI_TARS_PATH=""

REM Ask for browser type
echo.
echo Select browser type:
echo 1. Chrome (default)
echo 2. Edge
echo 3. Firefox
echo 4. Brave
echo.
set /p BROWSER_CHOICE="Enter choice (1-4): "

if "%BROWSER_CHOICE%"=="2" (
    set BROWSER_TYPE=edge
) else if "%BROWSER_CHOICE%"=="3" (
    set BROWSER_TYPE=firefox
) else if "%BROWSER_CHOICE%"=="4" (
    set BROWSER_TYPE=brave
) else (
    set BROWSER_TYPE=chrome
)

REM Ask for advanced options
echo.
echo Enable advanced options? (Y/N, default: Y)
set /p ADVANCED_OPTIONS="Enable advanced options? "

if /i "%ADVANCED_OPTIONS%"=="N" (
    set SANDBOX_OPTION=
    set VIRTUAL_PC_OPTION=
    set DPO_OPTION=
) else (
    set SANDBOX_OPTION=--sandbox
    set VIRTUAL_PC_OPTION=--virtual-pc
    set DPO_OPTION=--dpo
)

REM Run the enhanced diagnostic tool
echo.
echo Running enhanced UI-TARS diagnostic...
echo.

if "%UI_TARS_PATH%"=="" (
    python enhanced_ui_tars_diagnostic.py --browser %BROWSER_TYPE% %SANDBOX_OPTION% %VIRTUAL_PC_OPTION% %DPO_OPTION% --start --debug
) else (
    python enhanced_ui_tars_diagnostic.py --path "%UI_TARS_PATH%" --browser %BROWSER_TYPE% %SANDBOX_OPTION% %VIRTUAL_PC_OPTION% %DPO_OPTION% --start --debug
)

echo.
if %errorlevel% equ 0 (
    echo UI-TARS is properly configured and running with enhanced settings!
) else (
    echo There are issues with the UI-TARS configuration. Please check the recommendations above.
)

echo.
pause
