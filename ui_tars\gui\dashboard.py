"""
UI-TARS Dashboard.

This module provides a GUI dashboard for UI-TARS 1.5.
"""
import os
import sys
import json
import asyncio
import logging
import tkinter as tk
from tkinter import ttk, scrolledtext, filedialog, messagebox, simpledialog
from typing import Dict, List, Optional, Any, Union
import threading
import queue
import time
from datetime import datetime
import webbrowser

from core.logger import setup_logger
from ui_tars.connector.ui_tars_connector import UITarsConnector
from ui_tars.connector.midscene_connector import MidsceneConnector
from ui_tars.connector.local_llm_connector import LocalLLMConnector
from ui_tars.utils.voice_utils import VoiceCommandListener, TextToSpeech
from ui_tars.utils.nvidia_utils import check_nvidia_gpu, get_nvidia_gpu_info, setup_cuda_environment

# Set up logger
logger = setup_logger("ui_tars_dashboard")

class UITarsDashboard:
    """
    GUI Dashboard for UI-TARS 1.5.

    This class provides a GUI dashboard for UI-TARS 1.5,
    allowing users to control and monitor UI-TARS operations.
    """

    def __init__(self,
                 ui_tars_connector: Optional[UITarsConnector] = None,
                 midscene_connector: Optional[MidsceneConnector] = None,
                 local_llm_connector: Optional[LocalLLMConnector] = None):
        """
        Initialize the UI-TARS Dashboard.

        Args:
            ui_tars_connector (Optional[UITarsConnector]): UI-TARS connector
            midscene_connector (Optional[MidsceneConnector]): Midscene connector
            local_llm_connector (Optional[LocalLLMConnector]): Local LLM connector
        """
        self.ui_tars_connector = ui_tars_connector
        self.midscene_connector = midscene_connector
        self.local_llm_connector = local_llm_connector
        self.root = None
        self.notebook = None
        self.status_var = None
        self.command_entry = None
        self.output_text = None
        self.screenshot_label = None
        self.voice_listener = None
        self.text_to_speech = None
        self.voice_enabled = False
        self.nvidia_enabled = False
        self.command_history = []
        self.command_index = 0
        self.message_queue = queue.Queue()
        self.is_running = False

        # Check for NVIDIA GPU
        self.nvidia_enabled = check_nvidia_gpu()
        if self.nvidia_enabled:
            logger.info("NVIDIA GPU detected, enabling acceleration")
            setup_cuda_environment()

    def initialize(self):
        """Initialize the UI-TARS Dashboard."""
        logger.info("Initializing UI-TARS Dashboard")

        # Create the main window
        self.root = tk.Tk()
        self.root.title("UI-TARS Dashboard")
        self.root.geometry("1024x768")
        self.root.minsize(800, 600)

        # Set up the main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create a notebook (tabbed interface)
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # Create tabs
        control_tab = ttk.Frame(self.notebook)
        settings_tab = ttk.Frame(self.notebook)
        logs_tab = ttk.Frame(self.notebook)
        about_tab = ttk.Frame(self.notebook)

        self.notebook.add(control_tab, text="Control")
        self.notebook.add(settings_tab, text="Settings")
        self.notebook.add(logs_tab, text="Logs")
        self.notebook.add(about_tab, text="About")

        # Set up the control tab
        self._setup_control_tab(control_tab)

        # Set up the settings tab
        self._setup_settings_tab(settings_tab)

        # Set up the logs tab
        self._setup_logs_tab(logs_tab)

        # Set up the about tab
        self._setup_about_tab(about_tab)

        # Set up the status bar
        self._setup_status_bar(main_frame)

        # Set up voice command listener
        self._setup_voice_commands()

        # Start the message processing thread
        self.is_running = True
        threading.Thread(target=self._process_messages, daemon=True).start()

        logger.info("UI-TARS Dashboard initialized")

    def _setup_control_tab(self, parent):
        """
        Set up the control tab.

        Args:
            parent: Parent widget
        """
        # Create frames
        top_frame = ttk.Frame(parent)
        top_frame.pack(fill=tk.X, padx=5, pady=5)

        middle_frame = ttk.Frame(parent)
        middle_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        bottom_frame = ttk.Frame(parent)
        bottom_frame.pack(fill=tk.X, padx=5, pady=5)

        # Add controls to top frame
        ttk.Label(top_frame, text="Command:").pack(side=tk.LEFT, padx=5)

        self.command_entry = ttk.Entry(top_frame)
        self.command_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        self.command_entry.bind("<Return>", self._on_command_enter)
        self.command_entry.bind("<Up>", self._on_up_key)
        self.command_entry.bind("<Down>", self._on_down_key)

        ttk.Button(top_frame, text="Execute", command=self._execute_command).pack(side=tk.LEFT, padx=5)
        ttk.Button(top_frame, text="Screenshot", command=self._take_screenshot).pack(side=tk.LEFT, padx=5)

        # Add paned window to middle frame
        paned_window = ttk.PanedWindow(middle_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)

        # Left pane for output text
        left_frame = ttk.Frame(paned_window)
        paned_window.add(left_frame, weight=1)

        ttk.Label(left_frame, text="Output:").pack(anchor=tk.W, padx=5, pady=2)

        self.output_text = scrolledtext.ScrolledText(left_frame, wrap=tk.WORD)
        self.output_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.output_text.config(state=tk.DISABLED)

        # Right pane for screenshot
        right_frame = ttk.Frame(paned_window)
        paned_window.add(right_frame, weight=1)

        ttk.Label(right_frame, text="Screenshot:").pack(anchor=tk.W, padx=5, pady=2)

        screenshot_frame = ttk.Frame(right_frame, borderwidth=1, relief=tk.SUNKEN)
        screenshot_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.screenshot_label = ttk.Label(screenshot_frame, text="No screenshot available")
        self.screenshot_label.pack(fill=tk.BOTH, expand=True)

        # Add controls to bottom frame
        ttk.Button(bottom_frame, text="Start UI-TARS", command=self._start_ui_tars).pack(side=tk.LEFT, padx=5)
        ttk.Button(bottom_frame, text="Stop UI-TARS", command=self._stop_ui_tars).pack(side=tk.LEFT, padx=5)
        ttk.Button(bottom_frame, text="Start Browser", command=self._start_browser).pack(side=tk.LEFT, padx=5)
        ttk.Button(bottom_frame, text="Start Android", command=self._start_android).pack(side=tk.LEFT, padx=5)

        # Voice command toggle
        self.voice_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(bottom_frame, text="Voice Commands", variable=self.voice_var,
                        command=self._toggle_voice_commands).pack(side=tk.RIGHT, padx=5)

    def _setup_settings_tab(self, parent):
        """
        Set up the settings tab.

        Args:
            parent: Parent widget
        """
        # Create frames
        settings_frame = ttk.LabelFrame(parent, text="Settings")
        settings_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # UI-TARS settings
        ui_tars_frame = ttk.LabelFrame(settings_frame, text="UI-TARS Settings")
        ui_tars_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(ui_tars_frame, text="Installation Path:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.ui_tars_path_var = tk.StringVar()
        if self.ui_tars_connector and self.ui_tars_connector.installation_path:
            self.ui_tars_path_var.set(self.ui_tars_connector.installation_path)

        path_entry = ttk.Entry(ui_tars_frame, textvariable=self.ui_tars_path_var)
        path_entry.grid(row=0, column=1, sticky=tk.EW, padx=5, pady=5)

        ttk.Button(ui_tars_frame, text="Browse", command=lambda: self._browse_path(self.ui_tars_path_var)).grid(
            row=0, column=2, padx=5, pady=5)

        ttk.Label(ui_tars_frame, text="Model Name:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.model_name_var = tk.StringVar(value="UI-TARS-1.5-7B")
        ttk.Combobox(ui_tars_frame, textvariable=self.model_name_var,
                    values=["UI-TARS-1.5-7B", "Qwen2.5-VL-7B", "gpt-4o"]).grid(
            row=1, column=1, sticky=tk.EW, padx=5, pady=5)

        # Midscene settings
        midscene_frame = ttk.LabelFrame(settings_frame, text="Midscene Settings")
        midscene_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(midscene_frame, text="Browser Type:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.browser_type_var = tk.StringVar(value="chrome")
        ttk.Combobox(midscene_frame, textvariable=self.browser_type_var,
                    values=["chrome", "firefox", "edge"]).grid(
            row=0, column=1, sticky=tk.EW, padx=5, pady=5)

        self.android_enabled_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(midscene_frame, text="Enable Android Automation",
                        variable=self.android_enabled_var).grid(
            row=1, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)

        # Local LLM settings
        llm_frame = ttk.LabelFrame(settings_frame, text="Local LLM Settings")
        llm_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(llm_frame, text="Model Path:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.llm_path_var = tk.StringVar()
        if self.local_llm_connector and self.local_llm_connector.model_path:
            self.llm_path_var.set(self.local_llm_connector.model_path)

        llm_path_entry = ttk.Entry(llm_frame, textvariable=self.llm_path_var)
        llm_path_entry.grid(row=0, column=1, sticky=tk.EW, padx=5, pady=5)

        ttk.Button(llm_frame, text="Browse", command=lambda: self._browse_path(self.llm_path_var)).grid(
            row=0, column=2, padx=5, pady=5)

        ttk.Label(llm_frame, text="Model Type:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.llm_type_var = tk.StringVar(value="ui-tars")
        ttk.Combobox(llm_frame, textvariable=self.llm_type_var,
                    values=["ui-tars", "qwen", "llama"]).grid(
            row=1, column=1, sticky=tk.EW, padx=5, pady=5)

        # NVIDIA settings
        nvidia_frame = ttk.LabelFrame(settings_frame, text="NVIDIA Settings")
        nvidia_frame.pack(fill=tk.X, padx=10, pady=10)

        self.nvidia_info_var = tk.StringVar(value="Checking NVIDIA GPU...")
        ttk.Label(nvidia_frame, textvariable=self.nvidia_info_var).grid(
            row=0, column=0, columnspan=3, sticky=tk.W, padx=5, pady=5)

        self.nvidia_enabled_var = tk.BooleanVar(value=self.nvidia_enabled)
        ttk.Checkbutton(nvidia_frame, text="Enable NVIDIA Acceleration",
                        variable=self.nvidia_enabled_var, state=tk.DISABLED if not self.nvidia_enabled else tk.NORMAL).grid(
            row=1, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)

        # Update NVIDIA info
        self._update_nvidia_info()

        # Save settings button
        ttk.Button(settings_frame, text="Save Settings", command=self._save_settings).pack(
            side=tk.RIGHT, padx=10, pady=10)

        # Configure grid
        ui_tars_frame.columnconfigure(1, weight=1)
        midscene_frame.columnconfigure(1, weight=1)
        llm_frame.columnconfigure(1, weight=1)

    def _setup_logs_tab(self, parent):
        """
        Set up the logs tab.

        Args:
            parent: Parent widget
        """
        # Create frames
        log_frame = ttk.Frame(parent)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Add log text widget
        ttk.Label(log_frame, text="Logs:").pack(anchor=tk.W, padx=5, pady=2)

        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.log_text.config(state=tk.DISABLED)

        # Add buttons
        button_frame = ttk.Frame(log_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(button_frame, text="Clear Logs", command=self._clear_logs).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Save Logs", command=self._save_logs).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Refresh", command=self._refresh_logs).pack(side=tk.LEFT, padx=5)

    def _setup_about_tab(self, parent):
        """
        Set up the about tab.

        Args:
            parent: Parent widget
        """
        # Create frames
        about_frame = ttk.Frame(parent)
        about_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Add about information
        ttk.Label(about_frame, text="UI-TARS Dashboard", font=("TkDefaultFont", 16, "bold")).pack(pady=10)
        ttk.Label(about_frame, text="Version 0.1.0").pack()
        ttk.Label(about_frame, text="A GUI dashboard for UI-TARS 1.5").pack(pady=5)

        # Add links
        links_frame = ttk.Frame(about_frame)
        links_frame.pack(pady=20)

        ttk.Label(links_frame, text="UI-TARS GitHub:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Label(links_frame, text="https://github.com/bytedance/UI-TARS",
                 foreground="blue", cursor="hand2").grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)

        ttk.Label(links_frame, text="UI-TARS Desktop:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Label(links_frame, text="https://github.com/bytedance/UI-TARS-desktop",
                 foreground="blue", cursor="hand2").grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)

        ttk.Label(links_frame, text="Midscene:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Label(links_frame, text="https://github.com/web-infra-dev/midscene",
                 foreground="blue", cursor="hand2").grid(row=2, column=1, sticky=tk.W, padx=5, pady=2)

        # Add system information
        system_frame = ttk.LabelFrame(about_frame, text="System Information")
        system_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(system_frame, text=f"OS: {platform.system()} {platform.release()}").pack(anchor=tk.W, padx=5, pady=2)
        ttk.Label(system_frame, text=f"Python: {platform.python_version()}").pack(anchor=tk.W, padx=5, pady=2)
        ttk.Label(system_frame, text=f"NVIDIA GPU: {'Detected' if self.nvidia_enabled else 'Not detected'}").pack(
            anchor=tk.W, padx=5, pady=2)

    def _setup_status_bar(self, parent):
        """
        Set up the status bar.

        Args:
            parent: Parent widget
        """
        status_frame = ttk.Frame(parent, relief=tk.SUNKEN, borderwidth=1)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM, padx=5, pady=5)

        self.status_var = tk.StringVar(value="Ready")
        ttk.Label(status_frame, textvariable=self.status_var).pack(side=tk.LEFT, padx=5)

        # Add a progress bar
        self.progress_var = tk.DoubleVar(value=0)
        ttk.Progressbar(status_frame, variable=self.progress_var, length=100, mode="indeterminate").pack(
            side=tk.RIGHT, padx=5)

    def _setup_voice_commands(self):
        """Set up voice command listener."""
        # Initialize voice command listener
        self.voice_listener = VoiceCommandListener(
            commands={
                "start": self._start_ui_tars,
                "stop": self._stop_ui_tars,
                "browser": self._start_browser,
                "android": self._start_android,
                "screenshot": self._take_screenshot,
                "execute": self._execute_voice_command
            },
            activation_keyword="tars"
        )

        # Initialize text-to-speech
        self.text_to_speech = TextToSpeech()

    def _toggle_voice_commands(self):
        """Toggle voice commands."""
        self.voice_enabled = self.voice_var.get()

        if self.voice_enabled:
            # Start voice command listener
            success = self.voice_listener.start_listening()
            if success:
                self.status_var.set("Voice commands enabled")
                self.text_to_speech.speak("Voice commands enabled")
            else:
                self.voice_var.set(False)
                self.voice_enabled = False
                self.status_var.set("Failed to enable voice commands")
                messagebox.showerror("Error", "Failed to enable voice commands")
        else:
            # Stop voice command listener
            self.voice_listener.stop_listening()
            self.status_var.set("Voice commands disabled")

    def _execute_voice_command(self, command_text):
        """
        Execute a voice command.

        Args:
            command_text (str): Command text
        """
        # Extract the command from the text
        command = command_text.replace("execute", "").strip()

        # Set the command in the entry
        self.command_entry.delete(0, tk.END)
        self.command_entry.insert(0, command)

        # Execute the command
        self._execute_command()

    def _on_command_enter(self, event):
        """
        Handle command entry key press.

        Args:
            event: Event object
        """
        self._execute_command()

    def _on_up_key(self, event):
        """
        Handle up key press in command entry.

        Args:
            event: Event object
        """
        if self.command_history and self.command_index > 0:
            self.command_index -= 1
            self.command_entry.delete(0, tk.END)
            self.command_entry.insert(0, self.command_history[self.command_index])

        return "break"  # Prevent default behavior

    def _on_down_key(self, event):
        """
        Handle down key press in command entry.

        Args:
            event: Event object
        """
        if self.command_history and self.command_index < len(self.command_history) - 1:
            self.command_index += 1
            self.command_entry.delete(0, tk.END)
            self.command_entry.insert(0, self.command_history[self.command_index])
        elif self.command_index == len(self.command_history) - 1:
            self.command_index += 1
            self.command_entry.delete(0, tk.END)

        return "break"  # Prevent default behavior

    def _execute_command(self):
        """Execute the command in the command entry."""
        command = self.command_entry.get().strip()
        if not command:
            return

        # Add command to history
        self.command_history.append(command)
        self.command_index = len(self.command_history)

        # Clear command entry
        self.command_entry.delete(0, tk.END)

        # Update status
        self.status_var.set(f"Executing command: {command}")

        # Add command to output
        self._append_to_output(f">> {command}\n", "command")

        # Execute command
        if self.ui_tars_connector:
            # Create a thread to execute the command
            threading.Thread(target=self._execute_command_thread, args=(command,), daemon=True).start()
        else:
            self._append_to_output("Error: UI-TARS connector not initialized\n", "error")
            self.status_var.set("Error: UI-TARS connector not initialized")

    def _execute_command_thread(self, command):
        """
        Execute command in a separate thread.

        Args:
            command (str): Command to execute
        """
        try:
            # Start progress bar
            self.root.after(0, lambda: self.progress_var.set(0))
            self.root.after(0, lambda: self._start_progress())

            # Execute command
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(self.ui_tars_connector.execute_command(command))
            loop.close()

            # Stop progress bar
            self.root.after(0, lambda: self._stop_progress())

            # Process result
            if "error" in result:
                self.root.after(0, lambda: self._append_to_output(f"Error: {result['error']}\n", "error"))
                self.root.after(0, lambda: self.status_var.set(f"Error: {result['error']}"))
            else:
                self.root.after(0, lambda: self._append_to_output(f"{json.dumps(result, indent=2)}\n", "result"))
                self.root.after(0, lambda: self.status_var.set("Command executed successfully"))

                # Update screenshot if available
                if "screenshot" in result:
                    self.root.after(0, lambda: self._update_screenshot(result["screenshot"]))

        except Exception as e:
            logger.exception(f"Error executing command: {e}")
            self.root.after(0, lambda: self._append_to_output(f"Error: {str(e)}\n", "error"))
            self.root.after(0, lambda: self.status_var.set(f"Error: {str(e)}"))
            self.root.after(0, lambda: self._stop_progress())

    def _take_screenshot(self):
        """Take a screenshot using UI-TARS."""
        if not self.ui_tars_connector:
            self._append_to_output("Error: UI-TARS connector not initialized\n", "error")
            self.status_var.set("Error: UI-TARS connector not initialized")
            return

        # Update status
        self.status_var.set("Taking screenshot...")

        # Create a thread to take the screenshot
        threading.Thread(target=self._take_screenshot_thread, daemon=True).start()

    def _take_screenshot_thread(self):
        """Take screenshot in a separate thread."""
        try:
            # Start progress bar
            self.root.after(0, lambda: self.progress_var.set(0))
            self.root.after(0, lambda: self._start_progress())

            # Take screenshot
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(self.ui_tars_connector.take_screenshot())
            loop.close()

            # Stop progress bar
            self.root.after(0, lambda: self._stop_progress())

            # Process result
            if "error" in result:
                self.root.after(0, lambda: self._append_to_output(f"Error: {result['error']}\n", "error"))
                self.root.after(0, lambda: self.status_var.set(f"Error: {result['error']}"))
            else:
                self.root.after(0, lambda: self._append_to_output("Screenshot taken successfully\n", "info"))
                self.root.after(0, lambda: self.status_var.set("Screenshot taken successfully"))

                # Update screenshot
                if "screenshot" in result:
                    self.root.after(0, lambda: self._update_screenshot(result["screenshot"]))

        except Exception as e:
            logger.exception(f"Error taking screenshot: {e}")
            self.root.after(0, lambda: self._append_to_output(f"Error: {str(e)}\n", "error"))
            self.root.after(0, lambda: self.status_var.set(f"Error: {str(e)}"))
            self.root.after(0, lambda: self._stop_progress())

    def _update_screenshot(self, screenshot_data):
        """
        Update the screenshot display.

        Args:
            screenshot_data (str): Base64-encoded screenshot data
        """
        try:
            import base64
            from PIL import Image, ImageTk
            import io

            # Decode the screenshot data
            image_data = base64.b64decode(screenshot_data)

            # Create an image from the data
            image = Image.open(io.BytesIO(image_data))

            # Resize the image to fit the display
            width, height = image.size
            max_width = self.screenshot_label.winfo_width()
            max_height = self.screenshot_label.winfo_height()

            if max_width > 0 and max_height > 0:
                # Calculate the scaling factor
                scale = min(max_width / width, max_height / height)

                # Resize the image
                new_width = int(width * scale)
                new_height = int(height * scale)
                image = image.resize((new_width, new_height), Image.LANCZOS)

            # Convert the image to a PhotoImage
            photo = ImageTk.PhotoImage(image)

            # Update the screenshot label
            self.screenshot_label.config(image=photo)
            self.screenshot_label.image = photo  # Keep a reference to prevent garbage collection

        except Exception as e:
            logger.exception(f"Error updating screenshot: {e}")
            self.screenshot_label.config(text=f"Error: {str(e)}")

    def _start_ui_tars(self):
        """Start UI-TARS."""
        if not self.ui_tars_connector:
            self._append_to_output("Error: UI-TARS connector not initialized\n", "error")
            self.status_var.set("Error: UI-TARS connector not initialized")
            return

        # Update status
        self.status_var.set("Starting UI-TARS...")

        # Create a thread to start UI-TARS
        threading.Thread(target=self._start_ui_tars_thread, daemon=True).start()

    def _start_ui_tars_thread(self):
        """Start UI-TARS in a separate thread."""
        try:
            # Start progress bar
            self.root.after(0, lambda: self.progress_var.set(0))
            self.root.after(0, lambda: self._start_progress())

            # Start UI-TARS
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(self.ui_tars_connector.start())
            loop.close()

            # Stop progress bar
            self.root.after(0, lambda: self._stop_progress())

            # Process result
            if result:
                self.root.after(0, lambda: self._append_to_output("UI-TARS started successfully\n", "info"))
                self.root.after(0, lambda: self.status_var.set("UI-TARS started successfully"))
            else:
                self.root.after(0, lambda: self._append_to_output("Failed to start UI-TARS\n", "error"))
                self.root.after(0, lambda: self.status_var.set("Failed to start UI-TARS"))

        except Exception as e:
            logger.exception(f"Error starting UI-TARS: {e}")
            self.root.after(0, lambda: self._append_to_output(f"Error: {str(e)}\n", "error"))
            self.root.after(0, lambda: self.status_var.set(f"Error: {str(e)}"))
            self.root.after(0, lambda: self._stop_progress())

    def _stop_ui_tars(self):
        """Stop UI-TARS."""
        if not self.ui_tars_connector:
            self._append_to_output("Error: UI-TARS connector not initialized\n", "error")
            self.status_var.set("Error: UI-TARS connector not initialized")
            return

        # Update status
        self.status_var.set("Stopping UI-TARS...")

        # Create a thread to stop UI-TARS
        threading.Thread(target=self._stop_ui_tars_thread, daemon=True).start()

    def _stop_ui_tars_thread(self):
        """Stop UI-TARS in a separate thread."""
        try:
            # Start progress bar
            self.root.after(0, lambda: self.progress_var.set(0))
            self.root.after(0, lambda: self._start_progress())

            # Stop UI-TARS
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(self.ui_tars_connector.stop())
            loop.close()

            # Stop progress bar
            self.root.after(0, lambda: self._stop_progress())

            # Process result
            if result:
                self.root.after(0, lambda: self._append_to_output("UI-TARS stopped successfully\n", "info"))
                self.root.after(0, lambda: self.status_var.set("UI-TARS stopped successfully"))
            else:
                self.root.after(0, lambda: self._append_to_output("Failed to stop UI-TARS\n", "error"))
                self.root.after(0, lambda: self.status_var.set("Failed to stop UI-TARS"))

        except Exception as e:
            logger.exception(f"Error stopping UI-TARS: {e}")
            self.root.after(0, lambda: self._append_to_output(f"Error: {str(e)}\n", "error"))
            self.root.after(0, lambda: self.status_var.set(f"Error: {str(e)}"))
            self.root.after(0, lambda: self._stop_progress())

    def _start_browser(self):
        """Start browser automation with Midscene."""
        if not self.midscene_connector:
            self._append_to_output("Error: Midscene connector not initialized\n", "error")
            self.status_var.set("Error: Midscene connector not initialized")
            return

        # Update status
        self.status_var.set("Starting browser automation...")

        # Create a thread to start browser automation
        threading.Thread(target=self._start_browser_thread, daemon=True).start()

    def _start_browser_thread(self):
        """Start browser automation in a separate thread."""
        try:
            # Start progress bar
            self.root.after(0, lambda: self.progress_var.set(0))
            self.root.after(0, lambda: self._start_progress())

            # Get URL from user
            url = None
            self.root.after(0, lambda: self._get_url(lambda u: globals().update(url=u)))

            # Wait for URL
            while url is None:
                time.sleep(0.1)

            # Start browser automation
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(self.midscene_connector.start_browser_automation(url))
            loop.close()

            # Stop progress bar
            self.root.after(0, lambda: self._stop_progress())

            # Process result
            if result.get("success", False):
                self.root.after(0, lambda: self._append_to_output(f"Browser automation started successfully: {result.get('message', '')}\n", "info"))
                self.root.after(0, lambda: self.status_var.set("Browser automation started successfully"))
            else:
                self.root.after(0, lambda: self._append_to_output(f"Failed to start browser automation: {result.get('error', '')}\n", "error"))
                self.root.after(0, lambda: self.status_var.set("Failed to start browser automation"))

        except Exception as e:
            logger.exception(f"Error starting browser automation: {e}")
            self.root.after(0, lambda: self._append_to_output(f"Error: {str(e)}\n", "error"))
            self.root.after(0, lambda: self.status_var.set(f"Error: {str(e)}"))
            self.root.after(0, lambda: self._stop_progress())

    def _get_url(self, callback):
        """
        Get URL from user.

        Args:
            callback: Callback function to call with the URL
        """
        url = simpledialog.askstring("URL", "Enter URL to open (leave empty for default):")
        callback(url)

    def _start_android(self):
        """Start Android automation with Midscene."""
        if not self.midscene_connector:
            self._append_to_output("Error: Midscene connector not initialized\n", "error")
            self.status_var.set("Error: Midscene connector not initialized")
            return

        if not self.midscene_connector.android_enabled:
            self._append_to_output("Error: Android automation is not enabled\n", "error")
            self.status_var.set("Error: Android automation is not enabled")
            return

        # Update status
        self.status_var.set("Starting Android automation...")

        # Create a thread to start Android automation
        threading.Thread(target=self._start_android_thread, daemon=True).start()

    def _start_android_thread(self):
        """Start Android automation in a separate thread."""
        try:
            # Start progress bar
            self.root.after(0, lambda: self.progress_var.set(0))
            self.root.after(0, lambda: self._start_progress())

            # Get device ID from user
            device_id = None
            self.root.after(0, lambda: self._get_device_id(lambda d: globals().update(device_id=d)))

            # Wait for device ID
            while device_id is None:
                time.sleep(0.1)

            # Start Android automation
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(self.midscene_connector.start_android_automation(device_id))
            loop.close()

            # Stop progress bar
            self.root.after(0, lambda: self._stop_progress())

            # Process result
            if result.get("success", False):
                self.root.after(0, lambda: self._append_to_output(f"Android automation started successfully: {result.get('message', '')}\n", "info"))
                self.root.after(0, lambda: self.status_var.set("Android automation started successfully"))
            else:
                self.root.after(0, lambda: self._append_to_output(f"Failed to start Android automation: {result.get('error', '')}\n", "error"))
                self.root.after(0, lambda: self.status_var.set("Failed to start Android automation"))

        except Exception as e:
            logger.exception(f"Error starting Android automation: {e}")
            self.root.after(0, lambda: self._append_to_output(f"Error: {str(e)}\n", "error"))
            self.root.after(0, lambda: self.status_var.set(f"Error: {str(e)}"))
            self.root.after(0, lambda: self._stop_progress())

    def _get_device_id(self, callback):
        """
        Get device ID from user.

        Args:
            callback: Callback function to call with the device ID
        """
        device_id = simpledialog.askstring("Device ID", "Enter device ID (leave empty for default):")
        callback(device_id)

    def _browse_path(self, string_var):
        """
        Browse for a path and update the string variable.

        Args:
            string_var: String variable to update
        """
        path = filedialog.askdirectory()
        if path:
            string_var.set(path)

    def _save_settings(self):
        """Save settings."""
        # Update UI-TARS connector settings
        if self.ui_tars_connector:
            self.ui_tars_connector.installation_path = self.ui_tars_path_var.get()
            self.ui_tars_connector.model_name = self.model_name_var.get()

        # Update Midscene connector settings
        if self.midscene_connector:
            self.midscene_connector.browser_type = self.browser_type_var.get()
            self.midscene_connector.android_enabled = self.android_enabled_var.get()

        # Update Local LLM connector settings
        if self.local_llm_connector:
            self.local_llm_connector.model_path = self.llm_path_var.get()
            self.local_llm_connector.model_type = self.llm_type_var.get()

        # Update NVIDIA settings
        if self.nvidia_enabled:
            # Only update if NVIDIA is enabled
            self.nvidia_enabled = self.nvidia_enabled_var.get()

        # Save settings to file
        self._save_settings_to_file()

        # Show success message
        self.status_var.set("Settings saved successfully")
        messagebox.showinfo("Settings", "Settings saved successfully")

    def _save_settings_to_file(self):
        """Save settings to file."""
        try:
            settings = {
                "ui_tars": {
                    "installation_path": self.ui_tars_path_var.get(),
                    "model_name": self.model_name_var.get()
                },
                "midscene": {
                    "browser_type": self.browser_type_var.get(),
                    "android_enabled": self.android_enabled_var.get()
                },
                "local_llm": {
                    "model_path": self.llm_path_var.get(),
                    "model_type": self.llm_type_var.get()
                },
                "nvidia": {
                    "enabled": self.nvidia_enabled_var.get()
                }
            }

            # Create the settings directory if it doesn't exist
            os.makedirs("settings", exist_ok=True)

            # Save settings to file
            with open("settings/ui_tars_settings.json", "w") as f:
                json.dump(settings, f, indent=4)

            logger.info("Settings saved to file")

        except Exception as e:
            logger.exception(f"Error saving settings to file: {e}")
            messagebox.showerror("Error", f"Error saving settings to file: {e}")

    def _load_settings_from_file(self):
        """Load settings from file."""
        try:
            # Check if settings file exists
            if not os.path.exists("settings/ui_tars_settings.json"):
                logger.info("Settings file not found")
                return

            # Load settings from file
            with open("settings/ui_tars_settings.json", "r") as f:
                settings = json.load(f)

            # Update UI-TARS settings
            if "ui_tars" in settings:
                if "installation_path" in settings["ui_tars"]:
                    self.ui_tars_path_var.set(settings["ui_tars"]["installation_path"])
                if "model_name" in settings["ui_tars"]:
                    self.model_name_var.set(settings["ui_tars"]["model_name"])

            # Update Midscene settings
            if "midscene" in settings:
                if "browser_type" in settings["midscene"]:
                    self.browser_type_var.set(settings["midscene"]["browser_type"])
                if "android_enabled" in settings["midscene"]:
                    self.android_enabled_var.set(settings["midscene"]["android_enabled"])

            # Update Local LLM settings
            if "local_llm" in settings:
                if "model_path" in settings["local_llm"]:
                    self.llm_path_var.set(settings["local_llm"]["model_path"])
                if "model_type" in settings["local_llm"]:
                    self.llm_type_var.set(settings["local_llm"]["model_type"])

            # Update NVIDIA settings
            if "nvidia" in settings and self.nvidia_enabled:
                if "enabled" in settings["nvidia"]:
                    self.nvidia_enabled_var.set(settings["nvidia"]["enabled"])

            logger.info("Settings loaded from file")

        except Exception as e:
            logger.exception(f"Error loading settings from file: {e}")
            messagebox.showerror("Error", f"Error loading settings from file: {e}")

    def _update_nvidia_info(self):
        """Update NVIDIA GPU information."""
        if self.nvidia_enabled:
            # Get NVIDIA GPU info
            info = get_nvidia_gpu_info()

            if "error" in info:
                self.nvidia_info_var.set(f"NVIDIA GPU: Error - {info['error']}")
            else:
                self.nvidia_info_var.set(
                    f"NVIDIA GPU: {info.get('name', 'Unknown')}\n"
                    f"Driver: {info.get('driver_version', 'Unknown')}\n"
                    f"Memory: {info.get('memory_used', 'Unknown')} / {info.get('memory_total', 'Unknown')}\n"
                    f"Temperature: {info.get('temperature', 'Unknown')}"
                )
        else:
            self.nvidia_info_var.set("NVIDIA GPU: Not detected")

    def _append_to_output(self, text, tag=None):
        """
        Append text to the output text widget.

        Args:
            text (str): Text to append
            tag (str): Tag for the text
        """
        self.output_text.config(state=tk.NORMAL)
        self.output_text.insert(tk.END, text, tag)
        self.output_text.see(tk.END)
        self.output_text.config(state=tk.DISABLED)

    def _clear_logs(self):
        """Clear the logs."""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)

        self.status_var.set("Logs cleared")

    def _save_logs(self):
        """Save the logs to a file."""
        try:
            # Get the file path
            file_path = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )

            if not file_path:
                return

            # Save the logs
            with open(file_path, "w") as f:
                f.write(self.log_text.get(1.0, tk.END))

            self.status_var.set(f"Logs saved to {file_path}")

        except Exception as e:
            logger.exception(f"Error saving logs: {e}")
            messagebox.showerror("Error", f"Error saving logs: {e}")

    def _refresh_logs(self):
        """Refresh the logs."""
        # TODO: Implement log refreshing
        self.status_var.set("Logs refreshed")

    def _start_progress(self):
        """Start the progress bar."""
        self.progress_var.set(0)
        self.root.after(50, self._update_progress)

    def _update_progress(self):
        """Update the progress bar."""
        if self.progress_var.get() < 100:
            self.progress_var.set((self.progress_var.get() + 1) % 100)
            self.root.after(50, self._update_progress)

    def _stop_progress(self):
        """Stop the progress bar."""
        self.progress_var.set(0)

    def _process_messages(self):
        """Process messages from the message queue."""
        while self.is_running:
            try:
                # Get a message from the queue
                message = self.message_queue.get(timeout=0.1)

                # Process the message
                if message.get("type") == "log":
                    self.root.after(0, lambda m=message: self._append_to_output(f"{m.get('text', '')}\n", m.get("tag")))
                elif message.get("type") == "status":
                    self.root.after(0, lambda m=message: self.status_var.set(m.get("text", "")))
                elif message.get("type") == "screenshot":
                    self.root.after(0, lambda m=message: self._update_screenshot(m.get("data")))

                # Mark the message as processed
                self.message_queue.task_done()

            except queue.Empty:
                # No messages in the queue
                pass

            except Exception as e:
                logger.exception(f"Error processing message: {e}")

    def run(self):
        """Run the UI-TARS Dashboard."""
        logger.info("Running UI-TARS Dashboard")

        # Initialize the dashboard
        self.initialize()

        # Load settings from file
        self._load_settings_from_file()

        # Configure text tags
        self.output_text.tag_configure("command", foreground="blue")
        self.output_text.tag_configure("result", foreground="green")
        self.output_text.tag_configure("error", foreground="red")
        self.output_text.tag_configure("info", foreground="black")

        # Start the main loop
        self.root.mainloop()

        # Clean up
        self.is_running = False

        logger.info("UI-TARS Dashboard stopped")

    def close(self):
        """Close the UI-TARS Dashboard."""
        logger.info("Closing UI-TARS Dashboard")

        # Stop UI-TARS if running
        if self.ui_tars_connector and self.ui_tars_connector.is_running:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self.ui_tars_connector.stop())
            loop.close()

        # Stop Midscene if running
        if self.midscene_connector and self.midscene_connector.is_running:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self.midscene_connector.stop())
            loop.close()

        # Stop Local LLM if running
        if self.local_llm_connector and self.local_llm_connector.is_running:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self.local_llm_connector.stop_server())
            loop.close()

        # Stop voice command listener if running
        if self.voice_listener and self.voice_enabled:
            self.voice_listener.stop_listening()

        # Stop the dashboard
        self.is_running = False

        # Close the window
        if self.root:
            self.root.destroy()

        logger.info("UI-TARS Dashboard closed")
