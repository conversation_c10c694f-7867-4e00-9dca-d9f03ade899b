"""
Run AlphaEvolve.

This script initializes and runs AlphaEvolve, integrating it with the
Borg Cluster Management System and the Jarvis Interface.
"""
import asyncio
import argparse
import logging
import os
import sys
import signal
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent))

from core.logger import setup_logger
from core.state_manager import StateManager
from borg_cluster.borg_resource_manager import BorgResourceManager
from borg_cluster.borg_load_balancer import BorgLoadBalancer
from borg_cluster.jarvis_interface import JarvisInterface
from alpha_evolve.alpha_evolve_engine import AlphaEvolveEngine
from alpha_evolve.integration.borg_integration import BorgIntegration
from alpha_evolve.integration.jarvis_integration import Jarvis<PERSON><PERSON>phaEvolveCommands
from alpha_evolve.integration.agent_integration import AgentIntegration
from core.agent_manager import AgentManager

# Set up logger
logger = setup_logger("run_alpha_evolve")

# Global flag to control system shutdown
shutdown_event = asyncio.Event()

async def initialize_alpha_evolve(args):
    """
    Initialize AlphaEvolve and its integrations.
    
    Args:
        args: Command-line arguments
        
    Returns:
        Tuple: AlphaEvolve components
    """
    logger.info("Initializing AlphaEvolve")
    
    # Initialize state manager
    state_manager = StateManager()
    await state_manager.initialize()
    
    # Initialize AlphaEvolve engine
    alpha_evolve_engine = AlphaEvolveEngine(
        state_manager=state_manager,
        config_path=args.config,
    )
    await alpha_evolve_engine.initialize()
    
    # Initialize Borg components if needed
    borg_integration = None
    jarvis_commands = None
    
    if args.integrate_borg:
        # Initialize Borg resource manager
        resource_manager = BorgResourceManager()
        await resource_manager.initialize()
        
        # Initialize Borg load balancer
        load_balancer = BorgLoadBalancer()
        await load_balancer.initialize()
        
        # Initialize Borg integration
        borg_integration = BorgIntegration(
            alpha_evolve_engine=alpha_evolve_engine,
            resource_manager=resource_manager,
            load_balancer=load_balancer,
        )
        await borg_integration.initialize()
        
        # Initialize Jarvis interface if needed
        if args.integrate_jarvis:
            # Get Jarvis interface
            jarvis_interface = JarvisInterface(
                resource_manager=resource_manager,
                load_balancer=load_balancer,
            )
            await jarvis_interface.initialize()
            
            # Initialize Jarvis commands
            jarvis_commands = JarvisAlphaEvolveCommands(
                jarvis_interface=jarvis_interface,
                alpha_evolve_engine=alpha_evolve_engine,
                borg_integration=borg_integration,
            )
            await jarvis_commands.initialize()
    
    # Initialize agent integration if needed
    agent_integration = None
    
    if args.integrate_agents:
        # Initialize agent manager
        agent_manager = AgentManager()
        await agent_manager.initialize()
        
        # Initialize agent integration
        agent_integration = AgentIntegration(
            alpha_evolve_engine=alpha_evolve_engine,
            agent_manager=agent_manager,
        )
        await agent_integration.initialize()
        
        # Update Jarvis commands if needed
        if jarvis_commands:
            jarvis_commands.agent_integration = agent_integration
    
    logger.info("AlphaEvolve initialized")
    
    return alpha_evolve_engine, borg_integration, jarvis_commands, agent_integration

async def run_alpha_evolve(args):
    """
    Run AlphaEvolve.
    
    Args:
        args: Command-line arguments
    """
    # Initialize AlphaEvolve
    alpha_evolve_engine, borg_integration, jarvis_commands, agent_integration = await initialize_alpha_evolve(args)
    
    # Run example evolution if requested
    if args.run_example:
        await run_example_evolution(alpha_evolve_engine)
    
    # Wait for shutdown signal
    await shutdown_event.wait()
    
    # Shutdown AlphaEvolve
    await shutdown_alpha_evolve(alpha_evolve_engine, borg_integration, agent_integration)

async def run_example_evolution(alpha_evolve_engine):
    """
    Run an example evolution.
    
    Args:
        alpha_evolve_engine: AlphaEvolve engine
    """
    logger.info("Running example evolution")
    
    # Define a simple optimization problem
    problem = {
        "type": "optimization",
        "objective": "Find the most efficient sorting algorithm for a list of integers",
        "requirements": [
            "The algorithm must correctly sort a list of integers in ascending order",
            "The algorithm should be as efficient as possible in terms of time complexity",
            "The algorithm should handle edge cases (empty list, single element, etc.)",
        ],
        "inputs": {
            "data": "List[int]",
        },
        "outputs": "List[int]",
    }
    
    # Run evolution
    result = await alpha_evolve_engine.evolve(
        problem=problem,
        generations=10,  # Small number for example
        population_size=5,  # Small number for example
        timeout=60,  # Short timeout for example
    )
    
    # Print result
    logger.info(f"Example evolution completed with status: {result['status']}")
    logger.info(f"Best fitness: {result['best_fitness']}")
    
    if result.get("best_solution"):
        logger.info("Best solution found:")
        logger.info(result["best_solution"]["code"])

async def shutdown_alpha_evolve(alpha_evolve_engine, borg_integration, agent_integration):
    """
    Shutdown AlphaEvolve.
    
    Args:
        alpha_evolve_engine: AlphaEvolve engine
        borg_integration: Borg integration
        agent_integration: Agent integration
    """
    logger.info("Shutting down AlphaEvolve")
    
    # Shutdown agent integration
    if agent_integration:
        await agent_integration.shutdown()
    
    # Shutdown Borg integration
    if borg_integration:
        await borg_integration.shutdown()
    
    # Shutdown AlphaEvolve engine
    await alpha_evolve_engine.shutdown()
    
    logger.info("AlphaEvolve shut down")

def signal_handler():
    """Handle termination signals."""
    logger.info("Received termination signal")
    shutdown_event.set()

def main():
    """Main entry point."""
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="Run AlphaEvolve")
    parser.add_argument("--config", type=str, default="config/alpha_evolve_config.json", help="Path to configuration file")
    parser.add_argument("--integrate-borg", action="store_true", help="Integrate with Borg Cluster Management System")
    parser.add_argument("--integrate-jarvis", action="store_true", help="Integrate with Jarvis Interface")
    parser.add_argument("--integrate-agents", action="store_true", help="Integrate with agent system")
    parser.add_argument("--run-example", action="store_true", help="Run an example evolution")
    args = parser.parse_args()
    
    # Set up signal handlers
    for sig in (signal.SIGINT, signal.SIGTERM):
        signal.signal(sig, lambda signum, frame: signal_handler())
    
    # Run AlphaEvolve
    asyncio.run(run_alpha_evolve(args))

if __name__ == "__main__":
    main()
