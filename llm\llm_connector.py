"""
Base LLM connector for the Multi-Agent AI System.
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union
import asyncio
from datetime import datetime
import json

from core.logger import setup_logger

class LLMConnector(ABC):
    """
    Base class for LLM connectors.
    
    This abstract class defines the interface for connecting to
    different LLM providers.
    """
    
    def __init__(self, config: Dict):
        """
        Initialize the LLM connector.
        
        Args:
            config (Dict): LLM configuration
        """
        self.config = config
        self.provider = "base"
        self.models = config.get("models", [])
        self.default_model = config.get("default_model")
        self.api_key = config.get("api_key", "")
        self.enabled = config.get("enabled", False)
        
        # Set up logger
        self.logger = setup_logger(f"llm.{self.provider}")
        
        # Validate configuration
        self._validate_config()
    
    def _validate_config(self):
        """Validate the LLM configuration."""
        if not self.api_key:
            self.logger.warning(f"{self.provider} API key not provided")
            self.enabled = False
        
        if not self.models:
            self.logger.warning(f"No models specified for {self.provider}")
            self.enabled = False
        
        if not self.default_model:
            if self.models:
                self.default_model = self.models[0]
                self.logger.info(f"Default model not specified, using {self.default_model}")
            else:
                self.logger.warning(f"No default model specified for {self.provider}")
                self.enabled = False
    
    @abstractmethod
    async def generate_text(
        self,
        prompt: str,
        model: Optional[str] = None,
        max_tokens: int = 1000,
        temperature: float = 0.7,
        stop_sequences: Optional[List[str]] = None,
        **kwargs
    ) -> Dict:
        """
        Generate text using the LLM.
        
        Args:
            prompt (str): Input prompt
            model (Optional[str]): Model to use, defaults to default_model
            max_tokens (int): Maximum number of tokens to generate
            temperature (float): Sampling temperature
            stop_sequences (Optional[List[str]]): Sequences that stop generation
            **kwargs: Additional model-specific parameters
            
        Returns:
            Dict: Response containing generated text and metadata
        """
        pass
    
    @abstractmethod
    async def generate_chat(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        max_tokens: int = 1000,
        temperature: float = 0.7,
        stop_sequences: Optional[List[str]] = None,
        **kwargs
    ) -> Dict:
        """
        Generate a chat response using the LLM.
        
        Args:
            messages (List[Dict[str, str]]): List of message dictionaries
            model (Optional[str]): Model to use, defaults to default_model
            max_tokens (int): Maximum number of tokens to generate
            temperature (float): Sampling temperature
            stop_sequences (Optional[List[str]]): Sequences that stop generation
            **kwargs: Additional model-specific parameters
            
        Returns:
            Dict: Response containing generated text and metadata
        """
        pass
    
    @abstractmethod
    async def embed_text(
        self,
        text: Union[str, List[str]],
        model: Optional[str] = None,
        **kwargs
    ) -> Dict:
        """
        Generate embeddings for text.
        
        Args:
            text (Union[str, List[str]]): Text to embed
            model (Optional[str]): Model to use, defaults to default_model
            **kwargs: Additional model-specific parameters
            
        Returns:
            Dict: Response containing embeddings and metadata
        """
        pass
    
    def is_enabled(self) -> bool:
        """
        Check if the LLM connector is enabled.
        
        Returns:
            bool: True if enabled, False otherwise
        """
        return self.enabled
    
    def get_provider(self) -> str:
        """
        Get the LLM provider name.
        
        Returns:
            str: Provider name
        """
        return self.provider
    
    def get_models(self) -> List[str]:
        """
        Get available models.
        
        Returns:
            List[str]: List of available models
        """
        return self.models
    
    def get_default_model(self) -> str:
        """
        Get the default model.
        
        Returns:
            str: Default model name
        """
        return self.default_model
