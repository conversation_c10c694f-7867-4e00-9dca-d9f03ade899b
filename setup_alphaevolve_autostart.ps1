# Setup AlphaEvolve to run automatically on startup
# This script will:
# 1. Download NSSM if needed
# 2. Install AlphaEvolve as a Windows service
# 3. Create a shortcut to the Jarvis interface

# Check if running as administrator
$currentPrincipal = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
$isAdmin = $currentPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

if (-not $isAdmin) {
    Write-Host "This script must be run as Administrator. Please restart PowerShell as Administrator and try again." -ForegroundColor Red
    
    # Pause to see the message
    Write-Host "Press any key to exit..." -ForegroundColor Yellow
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    exit
}

# Get the current script directory
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path

# Check if NSSM is already available
$nssmPath = Join-Path -Path $scriptPath -ChildPath "nssm.exe"
$nssmInstalled = Test-Path $nssmPath

if (-not $nssmInstalled) {
    Write-Host "NSSM not found. Downloading it now..." -ForegroundColor Cyan
    
    # Run the download script
    & "$scriptPath\download_nssm.ps1"
    
    # Check if NSSM is now available
    $nssmInstalled = Test-Path $nssmPath
    
    if (-not $nssmInstalled) {
        Write-Host "Failed to download NSSM. Please download it manually from https://nssm.cc/download and place nssm.exe in this directory." -ForegroundColor Red
        
        # Pause to see the message
        Write-Host "Press any key to exit..." -ForegroundColor Yellow
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        exit
    }
}

# Create config directory if it doesn't exist
$configDir = Join-Path -Path $scriptPath -ChildPath "config"
if (-not (Test-Path $configDir)) {
    New-Item -Path $configDir -ItemType Directory | Out-Null
    Write-Host "Created config directory." -ForegroundColor Green
}

# Check if alpha_evolve_config.json exists
$configPath = Join-Path -Path $configDir -ChildPath "alpha_evolve_config.json"
if (-not (Test-Path $configPath)) {
    Write-Host "Running customize_alpha_evolve.py to create configuration files..." -ForegroundColor Cyan
    
    # Run the customization script
    & python "$scriptPath\customize_alpha_evolve.py" --all
}

# Create templates directory if it doesn't exist
$templatesDir = Join-Path -Path $scriptPath -ChildPath "alpha_evolve\templates"
if (-not (Test-Path $templatesDir)) {
    New-Item -Path $templatesDir -ItemType Directory -Force | Out-Null
    Write-Host "Created templates directory." -ForegroundColor Green
}

# Install AlphaEvolve as a Windows service
Write-Host "Installing AlphaEvolve as a Windows service..." -ForegroundColor Cyan
& "$scriptPath\install_alphaevolve_service.ps1"

# Create a task to open Jarvis interface at login
Write-Host "Creating a task to open Jarvis interface at login..." -ForegroundColor Cyan

$taskName = "OpenJarvisInterface"
$taskExists = Get-ScheduledTask -TaskName $taskName -ErrorAction SilentlyContinue

if ($taskExists) {
    Write-Host "Task '$taskName' already exists. Removing it..." -ForegroundColor Yellow
    Unregister-ScheduledTask -TaskName $taskName -Confirm:$false
}

$action = New-ScheduledTaskAction -Execute "powershell.exe" -Argument "-ExecutionPolicy Bypass -WindowStyle Normal -File `"$scriptPath\start_jarvis.ps1`"" -WorkingDirectory $scriptPath
$trigger = New-ScheduledTaskTrigger -AtLogOn
$settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable

Register-ScheduledTask -TaskName $taskName -Action $action -Trigger $trigger -Settings $settings -Description "Open Jarvis interface at login" -RunLevel Highest

Write-Host "Task created successfully. Jarvis interface will open automatically when you log in." -ForegroundColor Green

# Create a README file with instructions
$readmePath = Join-Path -Path $scriptPath -ChildPath "AUTOSTART_README.md"
$readmeContent = @"
# AlphaEvolve Autostart Setup

AlphaEvolve has been set up to run automatically when your computer starts. Here's what has been configured:

1. **AlphaEvolve Service**: Runs in the background as a Windows service, continuing work on previous tasks and waiting for new requests.
   - Service Name: AlphaEvolveService
   - Status: Running
   - Startup Type: Automatic

2. **Jarvis Interface**: Opens automatically when you log in.
   - A scheduled task has been created to open the Jarvis interface at login.
   - Task Name: OpenJarvisInterface

## Managing the Service

You can manage the AlphaEvolve service in the Windows Services management console:
1. Press Win+R, type `services.msc`, and press Enter
2. Find "AlphaEvolve Service" in the list
3. Right-click and select Start, Stop, or Restart as needed

## Service Logs

Service logs are saved in the following location:
\`$scriptPath\logs\alphaevolve_service_stdout.log\`
\`$scriptPath\logs\alphaevolve_service_stderr.log\`

## Manual Start

If you need to start the Jarvis interface manually, you can:
1. Double-click the "Jarvis Interface" shortcut on your desktop
2. Run \`start_jarvis.ps1\` from PowerShell

## Uninstalling

To uninstall the autostart configuration:
1. Run \`uninstall_alphaevolve_service.ps1\` as Administrator
2. This will remove the Windows service and scheduled task

## Troubleshooting

If you encounter any issues:
1. Check the service logs in the \`logs\` directory
2. Make sure the service is running
3. Try restarting the service
4. If all else fails, run \`setup_alphaevolve_autostart.ps1\` again as Administrator
"@

Set-Content -Path $readmePath -Value $readmeContent
Write-Host "Created README file with instructions: $readmePath" -ForegroundColor Green

# Create an uninstall script
$uninstallPath = Join-Path -Path $scriptPath -ChildPath "uninstall_alphaevolve_service.ps1"
$uninstallContent = @"
# Uninstall AlphaEvolve Service

# Check if running as administrator
`$currentPrincipal = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
`$isAdmin = `$currentPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

if (-not `$isAdmin) {
    Write-Host "This script must be run as Administrator. Please restart PowerShell as Administrator and try again." -ForegroundColor Red
    exit
}

# Define the service name
`$serviceName = "AlphaEvolveService"

# Check if the service exists
`$serviceExists = Get-Service -Name `$serviceName -ErrorAction SilentlyContinue

if (`$serviceExists) {
    Write-Host "Stopping and removing AlphaEvolve service..." -ForegroundColor Cyan
    
    # Stop the service
    Stop-Service -Name `$serviceName -Force
    
    # Get the NSSM path
    `$nssmPath = "`$PSScriptRoot\nssm.exe"
    
    # Remove the service
    & `$nssmPath remove `$serviceName confirm
    
    Write-Host "AlphaEvolve service removed." -ForegroundColor Green
} else {
    Write-Host "AlphaEvolve service not found." -ForegroundColor Yellow
}

# Remove the scheduled task
`$taskName = "OpenJarvisInterface"
`$taskExists = Get-ScheduledTask -TaskName `$taskName -ErrorAction SilentlyContinue

if (`$taskExists) {
    Write-Host "Removing scheduled task..." -ForegroundColor Cyan
    Unregister-ScheduledTask -TaskName `$taskName -Confirm:`$false
    Write-Host "Scheduled task removed." -ForegroundColor Green
} else {
    Write-Host "Scheduled task not found." -ForegroundColor Yellow
}

Write-Host "AlphaEvolve autostart configuration has been removed." -ForegroundColor Green
Write-Host "Press any key to exit..." -ForegroundColor Yellow
`$null = `$Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
"@

Set-Content -Path $uninstallPath -Value $uninstallContent
Write-Host "Created uninstall script: $uninstallPath" -ForegroundColor Green

# Final message
Write-Host @"

╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║                  ALPHAEVOLVE AUTOSTART SETUP COMPLETE                        ║
║                                                                              ║
║  AlphaEvolve will now run automatically when your computer starts.           ║
║  The service is already running in the background.                           ║
║  The Jarvis interface will open automatically when you log in.               ║
║                                                                              ║
║  You can also open the Jarvis interface manually using the desktop shortcut. ║
║                                                                              ║
║  For more information, see AUTOSTART_README.md                               ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝

"@ -ForegroundColor Green

# Pause to see the message
Write-Host "Press any key to exit..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
