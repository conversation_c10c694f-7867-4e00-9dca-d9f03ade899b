@echo off
echo Installing and configuring UI-TARS 1.5...

REM Install UI-TARS
echo Installing UI-TARS...
start /wait UI.TARS-0.1.0.Setup.exe

REM Run the setup script to scan for local LLMs
echo Setting up UI-TARS with local LLMs...
python ui_tars/setup_local_llm.py --config ui_tars/ui_tars_config.yaml --output ui_tars_import.json

echo.
echo UI-TARS has been installed and configured to use your local LLMs.
echo.
echo Import Instructions:
echo 1. Open UI-TARS Desktop
echo 2. Go to Settings
echo 3. Click on "Import Configuration"
echo 4. Select the file: %CD%\ui_tars_import.json
echo 5. Click "Import"
echo.
echo The configuration will be imported and UI-TARS will use your local LLMs.
echo UI-TARS will work offline without internet unless you give it a task that requires internet access.
echo.
echo Press any key to exit...
pause >nul

exit /b 0
