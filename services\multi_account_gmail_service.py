"""
Multi-Account Gmail Service for the Multi-Agent AI System.
Allows agents to manage multiple Gmail accounts simultaneously.
"""
import os
import json
import pickle
import asyncio
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import logging
from pathlib import Path

from services.gmail_service import GmailService
from core.logger import setup_logger

# Set up logger
logger = setup_logger("multi_account_gmail_service")

class MultiAccountGmailService:
    """
    Service for managing multiple Gmail accounts.
    """
    
    def __init__(self, accounts_config_path: str = 'config/email_accounts.json'):
        """
        Initialize the Multi-Account Gmail Service.
        
        Args:
            accounts_config_path (str): Path to the accounts configuration JSON file
        """
        self.accounts_config_path = accounts_config_path
        self.accounts = {}
        self.enabled = False
        
        # Load accounts configuration
        try:
            self._load_accounts_config()
            self.enabled = True
            logger.info(f"Multi-Account Gmail Service initialized with {len(self.accounts)} accounts")
        except Exception as e:
            logger.error(f"Failed to initialize Multi-Account Gmail Service: {e}")
    
    def _load_accounts_config(self):
        """Load accounts configuration from JSON file."""
        if not os.path.exists(self.accounts_config_path):
            logger.error(f"Accounts configuration file not found: {self.accounts_config_path}")
            raise FileNotFoundError(f"Accounts configuration file not found: {self.accounts_config_path}")
        
        with open(self.accounts_config_path, 'r') as f:
            config = json.load(f)
        
        # Initialize services for priority accounts
        for account in config.get('priority_accounts', []):
            email = account['email']
            credentials_path = f'credentials/gmail_{email.replace("@", "_at_").replace(".", "_dot_")}_credentials.json'
            token_path = f'credentials/gmail_{email.replace("@", "_at_").replace(".", "_dot_")}_token.pickle'
            
            self.accounts[email] = {
                'service': GmailService(credentials_path, token_path),
                'info': account
            }
        
        # Initialize services for additional accounts
        for account in config.get('additional_accounts', []):
            email = account['email']
            credentials_path = f'credentials/gmail_{email.replace("@", "_at_").replace(".", "_dot_")}_credentials.json'
            token_path = f'credentials/gmail_{email.replace("@", "_at_").replace(".", "_dot_")}_token.pickle'
            
            self.accounts[email] = {
                'service': GmailService(credentials_path, token_path),
                'info': account
            }
    
    def is_enabled(self) -> bool:
        """Check if the service is enabled."""
        return self.enabled
    
    def get_account_service(self, email: str) -> Optional[GmailService]:
        """
        Get the Gmail service for a specific account.
        
        Args:
            email (str): Email address of the account
            
        Returns:
            Optional[GmailService]: Gmail service for the account, or None if not found
        """
        if email in self.accounts:
            return self.accounts[email]['service']
        return None
    
    def get_enabled_accounts(self) -> List[str]:
        """
        Get a list of enabled accounts.
        
        Returns:
            List[str]: List of enabled account email addresses
        """
        return [email for email, account in self.accounts.items() if account['service'].is_enabled()]
    
    def get_priority_accounts(self) -> List[str]:
        """
        Get a list of priority accounts.
        
        Returns:
            List[str]: List of priority account email addresses
        """
        return [email for email, account in self.accounts.items() 
                if account['info'].get('priority', 999) <= 4 and account['service'].is_enabled()]
    
    async def list_messages_from_all_accounts(self, query: Optional[str] = None, 
                                             max_results: int = 5, 
                                             label_ids: Optional[List[str]] = None,
                                             priority_only: bool = True) -> Dict:
        """
        List messages from all accounts.
        
        Args:
            query (Optional[str]): Search query
            max_results (int): Maximum number of results per account
            label_ids (Optional[List[str]]): List of label IDs to filter by
            priority_only (bool): Whether to only include priority accounts
            
        Returns:
            Dict: Messages from all accounts
        """
        if not self.enabled:
            return {"error": "Multi-Account Gmail Service is not enabled"}
        
        results = {}
        accounts_to_check = self.get_priority_accounts() if priority_only else self.get_enabled_accounts()
        
        for email in accounts_to_check:
            service = self.accounts[email]['service']
            if service.is_enabled():
                try:
                    account_results = await service.list_messages(query, max_results, label_ids)
                    if "error" not in account_results:
                        results[email] = account_results
                except Exception as e:
                    logger.error(f"Error listing messages from {email}: {e}")
                    results[email] = {"error": str(e)}
        
        return results
    
    async def get_message(self, email: str, message_id: str, format: str = 'full') -> Dict:
        """
        Get a specific message from an account.
        
        Args:
            email (str): Email address of the account
            message_id (str): ID of the message to get
            format (str): Format of the message (full, minimal, raw, metadata)
            
        Returns:
            Dict: Message details
        """
        if not self.enabled:
            return {"error": "Multi-Account Gmail Service is not enabled"}
        
        service = self.get_account_service(email)
        if not service:
            return {"error": f"Account not found: {email}"}
        
        if not service.is_enabled():
            return {"error": f"Account not enabled: {email}"}
        
        return await service.get_message(message_id, format)
    
    async def send_message(self, email: str, to: str, subject: str, body: str, 
                          cc: Optional[str] = None, 
                          bcc: Optional[str] = None,
                          attachments: Optional[List[str]] = None,
                          html_body: Optional[str] = None) -> Dict:
        """
        Send an email from a specific account.
        
        Args:
            email (str): Email address to send from
            to (str): Recipient email address
            subject (str): Email subject
            body (str): Email body (plain text)
            cc (Optional[str]): CC recipients
            bcc (Optional[str]): BCC recipients
            attachments (Optional[List[str]]): List of file paths to attach
            html_body (Optional[str]): HTML version of the email body
            
        Returns:
            Dict: Send status
        """
        if not self.enabled:
            return {"error": "Multi-Account Gmail Service is not enabled"}
        
        service = self.get_account_service(email)
        if not service:
            return {"error": f"Account not found: {email}"}
        
        if not service.is_enabled():
            return {"error": f"Account not enabled: {email}"}
        
        return await service.send_message(to, subject, body, cc, bcc, attachments, html_body)
    
    async def search_across_accounts(self, query: str, max_results_per_account: int = 5, 
                                    priority_only: bool = True) -> Dict:
        """
        Search for messages across all accounts.
        
        Args:
            query (str): Search query
            max_results_per_account (int): Maximum number of results per account
            priority_only (bool): Whether to only include priority accounts
            
        Returns:
            Dict: Search results from all accounts
        """
        return await self.list_messages_from_all_accounts(query, max_results_per_account, None, priority_only)

# Factory for creating Multi-Account Gmail Service instances
class MultiAccountGmailServiceFactory:
    """Factory for creating Multi-Account Gmail Service instances."""
    
    _instance = None
    
    @classmethod
    def create_service(cls) -> MultiAccountGmailService:
        """Create or return the singleton instance of MultiAccountGmailService."""
        if cls._instance is None:
            cls._instance = MultiAccountGmailService()
        return cls._instance
