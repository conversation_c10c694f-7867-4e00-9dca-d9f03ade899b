# Advanced Credential Manager

This module provides advanced credential management capabilities for the Multi-Agent AI System, including secure storage and retrieval, automatic credential recovery and refresh, multi-service credential management, and credential rotation and security monitoring.

## Overview

The Advanced Credential Manager is designed to securely manage credentials for various services used by the agent system. It handles different types of credentials, including OAuth tokens, API keys, and service-specific credentials, with features for automatic refresh, security monitoring, and recovery.

## Features

### Secure Credential Storage

- Encrypted storage of sensitive credentials
- Support for multiple credential types (OAuth tokens, API keys, etc.)
- Secure in-memory caching for performance

### Automatic Credential Recovery

- Automatic detection of expired or invalid credentials
- Intelligent recovery mechanisms for different credential types
- Configurable recovery policies

### Multi-Service Support

- Pre-configured support for common services (Gmail, Google Drive, OpenAI, etc.)
- Extensible architecture for adding new services
- Service-specific credential handling

### Security Monitoring

- Credential access logging
- Detection of unusual access patterns
- Security alerts for potential issues

## Usage

### Getting Credentials

```python
from services.advanced_credential_manager import AdvancedCredentialManager

# Initialize credential manager
credential_manager = AdvancedCredentialManager(state_manager)
await credential_manager.initialize()

# Get Gmail token for a specific account
gmail_token = await credential_manager.get_credential(
    service_name="gmail",
    account_id="<EMAIL>",
    credential_type="token"
)

# Get API key
openai_key = await credential_manager.get_credential(
    service_name="openai",
    account_id="default",
    credential_type="api_key"
)
```

### Storing Credentials

```python
# Store OAuth credentials
await credential_manager.store_credential(
    service_name="gmail",
    account_id="<EMAIL>",
    credential_type="credentials",
    credential_data=credentials_json
)

# Store OAuth token
await credential_manager.store_credential(
    service_name="gmail",
    account_id="<EMAIL>",
    credential_type="token",
    credential_data=token_data
)

# Store API key
await credential_manager.store_credential(
    service_name="openai",
    account_id="default",
    credential_type="api_key",
    credential_data="sk-..."
)
```

### Refreshing Credentials

```python
# Refresh OAuth token
success = await credential_manager.refresh_credential(
    service_name="gmail",
    account_id="<EMAIL>",
    credential_type="token"
)

if success:
    print("Token refreshed successfully")
else:
    print("Failed to refresh token")
```

### Security Monitoring

```python
# Check for security issues
security_alerts = await credential_manager.check_security()

if security_alerts:
    print(f"Found {len(security_alerts)} security alerts:")
    for alert in security_alerts:
        print(f"- {alert['type']}: {alert['message']}")
else:
    print("No security issues found")

# Get credential status
status = await credential_manager.get_credential_status()
print(f"Total credentials: {status['total_credentials']}")
print(f"Available credentials: {status['available_credentials']}")
print(f"Unavailable credentials: {status['unavailable_credentials']}")
```

## Supported Services

The Advanced Credential Manager supports the following services out of the box:

### Email Services

- **Gmail**: OAuth2 authentication for sending, receiving, and managing emails
- **Outlook**: OAuth2 authentication for Microsoft email services

### Cloud Storage

- **Google Drive**: OAuth2 authentication for file storage and retrieval
- **Dropbox**: OAuth2 authentication for file storage and retrieval

### AI Services

- **OpenAI**: API key authentication for GPT models
- **Anthropic**: API key authentication for Claude models
- **Hugging Face**: API key authentication for model hosting

### Social Media

- **Twitter/X**: OAuth1.0a authentication for posting and reading tweets
- **LinkedIn**: OAuth2 authentication for professional networking
- **Facebook**: OAuth2 authentication for social media integration

### Financial Services

- **Plaid**: API key authentication for banking data
- **Stripe**: API key authentication for payments
- **PayPal**: OAuth2 authentication for payments

## Security Features

### Encryption

Credentials are encrypted at rest using Fernet symmetric encryption. The encryption key is stored securely and is not exposed to agents or external systems.

### Access Logging

All credential access is logged with the following information:
- Timestamp
- Service name
- Account identifier
- Credential type
- Action performed (get, store, refresh)

### Anomaly Detection

The credential manager monitors for unusual access patterns, such as:
- Rapid successive access to the same credential
- Access from unusual sources
- Failed access attempts

### Automatic Rotation

For supported services, credentials can be automatically rotated based on:
- Time-based policies (e.g., rotate every 30 days)
- Usage-based policies (e.g., rotate after 1000 uses)
- Risk-based policies (e.g., rotate after detecting suspicious activity)

## Integration with Agents

Agents can access the credential manager through the service registry:

```python
# In an agent's execute_cycle method
async def execute_cycle(self):
    # Get credential manager from services
    credential_manager = self.get_service("credential_manager")
    
    if credential_manager:
        # Get Gmail token for sending emails
        gmail_token = await credential_manager.get_credential(
            service_name="gmail",
            account_id="<EMAIL>",
            credential_type="token"
        )
        
        if gmail_token:
            # Use token to send email
            await self._send_email_with_token(gmail_token)
        else:
            self.logger.error("Failed to get Gmail token")
```

## Configuration

The Advanced Credential Manager can be configured in the `.env` file:

```
# Credential manager settings
CREDENTIAL_MANAGER_ENCRYPTION_ENABLED=True
CREDENTIAL_MANAGER_AUTO_RECOVERY_ENABLED=True
CREDENTIAL_MANAGER_CREDENTIALS_DIR=credentials
```
