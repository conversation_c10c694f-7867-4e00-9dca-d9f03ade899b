"""
Direct Gmail Access
This script provides direct access to Gmail using your email and password.
"""
import os
import sys
import smtplib
import imaplib
import email
import getpass
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON><PERSON>ipart
from pathlib import Path

def clear_screen():
    """Clear the terminal screen."""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_header():
    """Print the script header."""
    clear_screen()
    print("=" * 80)
    print("                     DIRECT GMAIL ACCESS")
    print("=" * 80)
    print("\nThis script provides direct access to Gmail using your email and password.")
    print("It uses SMTP and IMAP protocols to send and receive emails.")
    print("\n")

def send_email():
    """Send an email using SMTP."""
    print_header()
    
    print("SEND EMAIL")
    print("-" * 80)
    
    # Get email credentials
    email_address = input("\nEnter your Gmail address: ")
    password = getpass.getpass("Enter your Gmail password or app password: ")
    
    # Get email details
    to_email = input("\nTo: ")
    subject = input("Subject: ")
    body = input("Body: ")
    
    try:
        # Create the email message
        print("\nCreating email message...")
        message = MIMEMultipart()
        message['From'] = email_address
        message['To'] = to_email
        message['Subject'] = subject
        message.attach(MIMEText(body, 'plain'))
        
        # Connect to Gmail SMTP server
        print("Connecting to Gmail SMTP server...")
        server = smtplib.SMTP('smtp.gmail.com', 587)
        server.starttls()
        
        # Login to Gmail
        print("Logging in to Gmail...")
        server.login(email_address, password)
        
        # Send the email
        print("Sending email...")
        server.send_message(message)
        server.quit()
        
        print("\n✓ Email sent successfully!")
        return True
    
    except Exception as e:
        print(f"\nError sending email: {e}")
        
        print("\nTroubleshooting steps:")
        print("1. Make sure you've entered the correct email address and password")
        print("2. If you're using 2-factor authentication, you need to use an app password")
        print("3. Make sure 'Less secure app access' is enabled in your Google account")
        
        retry = input("\nDo you want to try again? (y/n): ").lower()
        if retry == 'y':
            return send_email()
        
        return False

def read_emails():
    """Read emails using IMAP."""
    print_header()
    
    print("READ EMAILS")
    print("-" * 80)
    
    # Get email credentials
    email_address = input("\nEnter your Gmail address: ")
    password = getpass.getpass("Enter your Gmail password or app password: ")
    
    try:
        # Connect to Gmail IMAP server
        print("\nConnecting to Gmail IMAP server...")
        mail = imaplib.IMAP4_SSL('imap.gmail.com')
        
        # Login to Gmail
        print("Logging in to Gmail...")
        mail.login(email_address, password)
        
        # Select the mailbox
        print("Selecting inbox...")
        mail.select('inbox')
        
        # Search for emails
        print("Searching for emails...")
        result, data = mail.search(None, 'ALL')
        
        # Get the latest 5 emails
        email_ids = data[0].split()
        latest_email_ids = email_ids[-5:] if len(email_ids) >= 5 else email_ids
        
        # Print the emails
        print(f"\nLatest {len(latest_email_ids)} emails:")
        
        for email_id in reversed(latest_email_ids):
            result, data = mail.fetch(email_id, '(RFC822)')
            raw_email = data[0][1]
            msg = email.message_from_bytes(raw_email)
            
            # Get email details
            from_address = msg['From']
            subject = msg['Subject']
            date = msg['Date']
            
            print(f"\nFrom: {from_address}")
            print(f"Subject: {subject}")
            print(f"Date: {date}")
            print("-" * 40)
        
        # Logout
        mail.logout()
        
        print("\n✓ Emails read successfully!")
        return True
    
    except Exception as e:
        print(f"\nError reading emails: {e}")
        
        print("\nTroubleshooting steps:")
        print("1. Make sure you've entered the correct email address and password")
        print("2. If you're using 2-factor authentication, you need to use an app password")
        print("3. Make sure IMAP is enabled in your Gmail settings")
        
        retry = input("\nDo you want to try again? (y/n): ").lower()
        if retry == 'y':
            return read_emails()
        
        return False

def main():
    """Main entry point."""
    print_header()
    
    print("MAIN MENU")
    print("-" * 80)
    print("1. Send Email")
    print("2. Read Emails")
    print("3. Exit")
    
    choice = input("\nEnter your choice (1-3): ")
    
    if choice == '1':
        send_email()
        input("\nPress Enter to return to the main menu...")
        main()
    
    elif choice == '2':
        read_emails()
        input("\nPress Enter to return to the main menu...")
        main()
    
    elif choice == '3':
        print("\nExiting...")
        return
    
    else:
        print("\nInvalid choice. Please try again.")
        input("\nPress Enter to continue...")
        main()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nExiting...")
        sys.exit(0)
