{"enabled": true, "tools_dir": "./tools", "auto_install": true, "tool_registry": {"john": {"name": "<PERSON> Ripper", "description": "Password cracking tool", "enabled": true, "auto_install": true, "windows": {"check_cmd": "where john", "install_cmd": "powershell -Command \"Invoke-WebRequest -Uri 'https://www.openwall.com/john/k/john-1.9.0-jumbo-1-win64.zip' -OutFile 'john.zip'; Expand-Archive -Path 'john.zip' -DestinationPath '.\\tools'; Move-Item -Path '.\\tools\\john*\\run\\*' -Destination '.\\tools\\john'; Remove-Item -Path 'john.zip' -Force\"", "cmd": ".\\tools\\john\\john.exe"}, "linux": {"check_cmd": "which john", "install_cmd": "sudo apt-get update && sudo apt-get install -y john", "cmd": "john"}, "darwin": {"check_cmd": "which john", "install_cmd": "brew install john", "cmd": "john"}}, "nmap": {"name": "Nmap", "description": "Network scanning tool", "enabled": true, "auto_install": true, "windows": {"check_cmd": "where nmap", "install_cmd": "powershell -Command \"Invoke-WebRequest -Uri 'https://nmap.org/dist/nmap-7.92-setup.exe' -OutFile 'nmap-setup.exe'; Start-Process -FilePath 'nmap-setup.exe' -ArgumentList '/S' -Wait; Remove-Item -Path 'nmap-setup.exe' -Force\"", "cmd": "nmap"}, "linux": {"check_cmd": "which nmap", "install_cmd": "sudo apt-get update && sudo apt-get install -y nmap", "cmd": "nmap"}, "darwin": {"check_cmd": "which nmap", "install_cmd": "brew install nmap", "cmd": "nmap"}}, "wireshark": {"name": "Wireshark", "description": "Network protocol analyzer", "enabled": true, "auto_install": true, "windows": {"check_cmd": "where tshark", "install_cmd": "powershell -Command \"Invoke-WebRequest -Uri 'https://1.na.dl.wireshark.org/win64/Wireshark-win64-latest.exe' -OutFile 'wireshark-setup.exe'; Start-Process -FilePath 'wireshark-setup.exe' -ArgumentList '/S' -Wait; Remove-Item -Path 'wireshark-setup.exe' -Force\"", "cmd": "tshark"}, "linux": {"check_cmd": "which tshark", "install_cmd": "sudo apt-get update && sudo apt-get install -y wireshark tshark", "cmd": "tshark"}, "darwin": {"check_cmd": "which tshark", "install_cmd": "brew install wireshark", "cmd": "tshark"}}, "metasploit": {"name": "Metasploit Framework", "description": "Penetration testing framework", "enabled": true, "auto_install": false, "windows": {"check_cmd": "where msfconsole", "install_cmd": "echo \"Metasploit installation on Windows requires manual steps. Please install from https://www.metasploit.com/download\"", "cmd": "msfconsole"}, "linux": {"check_cmd": "which msfconsole", "install_cmd": "curl https://raw.githubusercontent.com/rapid7/metasploit-omnibus/master/config/templates/metasploit-framework-wrappers/msfupdate.erb > msfinstall && chmod 755 msfinstall && ./msfinstall", "cmd": "msfconsole"}, "darwin": {"check_cmd": "which msfconsole", "install_cmd": "brew install metasploit", "cmd": "msfconsole"}}, "burpsuite": {"name": "Burp Suite", "description": "Web vulnerability scanner", "enabled": true, "auto_install": false, "windows": {"check_cmd": "where java", "install_cmd": "echo \"Burp Suite installation requires manual steps. Please download from https://portswigger.net/burp/communitydownload\"", "cmd": "java -jar burpsuite_community.jar"}, "linux": {"check_cmd": "which java", "install_cmd": "sudo apt-get update && sudo apt-get install -y default-jre && wget -O burpsuite_community.jar 'https://portswigger.net/burp/releases/download?product=community&version=latest&type=jar'", "cmd": "java -jar burpsuite_community.jar"}, "darwin": {"check_cmd": "which java", "install_cmd": "brew install --cask burp-suite", "cmd": "open -a \"Burp Suite Community Edition\""}}, "aircrack-ng": {"name": "Aircrack-ng", "description": "Wireless network security tool", "enabled": true, "auto_install": true, "windows": {"check_cmd": "where aircrack-ng", "install_cmd": "echo \"Aircrack-ng installation on Windows requires manual steps. Please download from https://www.aircrack-ng.org/downloads.html\"", "cmd": "aircrack-ng"}, "linux": {"check_cmd": "which aircrack-ng", "install_cmd": "sudo apt-get update && sudo apt-get install -y aircrack-ng", "cmd": "aircrack-ng"}, "darwin": {"check_cmd": "which aircrack-ng", "install_cmd": "brew install aircrack-ng", "cmd": "aircrack-ng"}}, "sqlmap": {"name": "SQLMap", "description": "SQL injection tool", "enabled": true, "auto_install": true, "windows": {"check_cmd": "where python && python -c \"import sqlmap\"", "install_cmd": "pip install sqlmap", "cmd": "sqlmap"}, "linux": {"check_cmd": "which sqlmap", "install_cmd": "sudo apt-get update && sudo apt-get install -y sqlmap", "cmd": "sqlmap"}, "darwin": {"check_cmd": "which sqlmap", "install_cmd": "brew install sqlmap", "cmd": "sqlmap"}}, "zaproxy": {"name": "OWASP ZAP", "description": "Web application security scanner", "enabled": true, "auto_install": false, "windows": {"check_cmd": "where java", "install_cmd": "echo \"OWASP ZAP installation on Windows requires manual steps. Please download from https://www.zaproxy.org/download/\"", "cmd": "zap.bat"}, "linux": {"check_cmd": "which zap", "install_cmd": "sudo apt-get update && sudo apt-get install -y zaproxy", "cmd": "zap"}, "darwin": {"check_cmd": "which zap", "install_cmd": "brew install --cask owasp-zap", "cmd": "zap"}}, "theharvester": {"name": "TheHarvester", "description": "Email, subdomain and name harvester", "enabled": true, "auto_install": true, "windows": {"check_cmd": "where python && python -c \"import theHarvester\"", "install_cmd": "pip install theHarvester", "cmd": "theHarvester"}, "linux": {"check_cmd": "which theHarvester", "install_cmd": "sudo apt-get update && sudo apt-get install -y theharvester", "cmd": "theHarvester"}, "darwin": {"check_cmd": "which theHarvester", "install_cmd": "pip install theHarvester", "cmd": "theHarvester"}}, "nikto": {"name": "<PERSON><PERSON>", "description": "Web server scanner", "enabled": true, "auto_install": true, "windows": {"check_cmd": "where perl && where nikto", "install_cmd": "echo \"Nikto installation on Windows requires manual steps. Please download from https://github.com/sullo/nikto\"", "cmd": "perl nikto.pl"}, "linux": {"check_cmd": "which nikto", "install_cmd": "sudo apt-get update && sudo apt-get install -y nikto", "cmd": "nikto"}, "darwin": {"check_cmd": "which nikto", "install_cmd": "brew install nikto", "cmd": "nikto"}}, "pentestgpt": {"name": "PentestGPT", "description": "AI-enhanced penetration testing tool", "enabled": true, "auto_install": true, "windows": {"check_cmd": "where python && python -c \"import pentestgpt\"", "install_cmd": "pip install pentestgpt", "cmd": "pentestgpt"}, "linux": {"check_cmd": "which pentestgpt", "install_cmd": "pip install pentestgpt", "cmd": "pentestgpt"}, "darwin": {"check_cmd": "which pentestgpt", "install_cmd": "pip install pentestgpt", "cmd": "pentestgpt"}}}}