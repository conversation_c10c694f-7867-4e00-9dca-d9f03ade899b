"""
Test script for the Multi-Account Email Agent.
This script tests the Multi-Account Email Agent's ability to read, analyze, and respond to emails
across multiple Gmail accounts.
"""
import os
import sys
import json
import asyncio
import argparse
from typing import Dict, List, Optional, Any

from agents.multi_account_email_agent import MultiAccountEmailAgent
from llm.llm_connector import LLMConnectorFactory
from core.logger import setup_logger

# Set up logger
logger = setup_logger("test_multi_account_email_agent")

async def test_list_accounts(email_agent: MultiAccountEmailAgent, enabled_only: bool = True):
    """Test listing accounts."""
    print("\n=== Testing List Accounts ===")
    print(f"Enabled Only: {enabled_only}")
    
    result = await email_agent.list_accounts(enabled_only)
    
    if "error" in result:
        print(f"Error: {result['error']}")
        return
    
    print(f"Found {len(result['accounts'])} accounts")
    
    for account in result['accounts']:
        print(f"\nAccount: {account['email']}")
        print(f"Description: {account['info']['description']}")
        print(f"Purpose: {account['info']['purpose']}")
        print(f"Priority: {account['info']['priority']}")
        if not enabled_only:
            print(f"Enabled: {account['enabled']}")
    
    return result['accounts']

async def test_read_emails(email_agent: MultiAccountEmailAgent, query: Optional[str] = None, 
                          max_results: int = 5, priority_only: bool = True):
    """Test reading emails from multiple accounts."""
    print("\n=== Testing Read Emails ===")
    print(f"Query: {query or 'None'}")
    print(f"Max Results: {max_results}")
    print(f"Priority Only: {priority_only}")
    
    result = await email_agent.read_emails(query, max_results, priority_only)
    
    if "error" in result:
        print(f"Error: {result['error']}")
        return
    
    # Print overall summary if available
    if "overall_summary" in result:
        print("\nOverall Summary:")
        print(result["overall_summary"])
    
    # Print account-specific results
    for email, account_results in result.items():
        if email == "overall_summary":
            continue
        
        if "error" in account_results:
            print(f"\nAccount {email}: Error - {account_results['error']}")
            continue
        
        message_count = len(account_results.get("messages", []))
        print(f"\nAccount {email}: {message_count} messages")
        
        if "summary" in account_results:
            print(f"\nSummary for {email}:")
            print(account_results["summary"])
        
        if message_count > 0:
            print(f"\nFirst few messages from {email}:")
            for i, message in enumerate(account_results["messages"][:3]):
                print(f"\nMessage {i+1}:")
                print(f"From: {message['from']}")
                print(f"Subject: {message['subject']}")
                print(f"Date: {message['date']}")
                print(f"Snippet: {message['snippet']}")
    
    # Collect all messages from all accounts
    all_messages = []
    for email, account_results in result.items():
        if email == "overall_summary":
            continue
        
        if "error" not in account_results and "messages" in account_results:
            for message in account_results["messages"]:
                message["account"] = email
                all_messages.append(message)
    
    return all_messages

async def test_analyze_email(email_agent: MultiAccountEmailAgent, account_email: str, email_id: str):
    """Test analyzing an email."""
    print("\n=== Testing Analyze Email ===")
    print(f"Account: {account_email}")
    print(f"Email ID: {email_id}")
    
    result = await email_agent.analyze_email(account_email, email_id)
    
    if "error" in result:
        print(f"Error: {result['error']}")
        return
    
    print(f"Email details:")
    print(f"From: {result['from']}")
    print(f"Subject: {result['subject']}")
    print(f"Date: {result['date']}")
    
    if "analysis" in result:
        print("\nAnalysis:")
        print(result["analysis"])
    
    return result

async def test_draft_response(email_agent: MultiAccountEmailAgent, account_email: str, email_id: str, 
                             response_type: str = "professional", 
                             include_reasoning: bool = True):
    """Test drafting a response to an email."""
    print("\n=== Testing Draft Response ===")
    print(f"Account: {account_email}")
    print(f"Email ID: {email_id}")
    print(f"Response Type: {response_type}")
    print(f"Include Reasoning: {include_reasoning}")
    
    result = await email_agent.draft_response(account_email, email_id, response_type, include_reasoning)
    
    if "error" in result:
        print(f"Error: {result['error']}")
        return
    
    print(f"\nDraft response from account {result['account_email']}")
    print(f"To email from: {result['original_email']['from']}")
    print(f"Subject: {result['original_email']['subject']}")
    
    print("\nDraft Response:")
    print(result["draft_response"])
    
    if include_reasoning and result["reasoning"]:
        print("\nReasoning:")
        print(result["reasoning"])
    
    return result

async def test_send_email(email_agent: MultiAccountEmailAgent, account_email: str, 
                         to: str, subject: str, body: str,
                         cc: Optional[str] = None, 
                         bcc: Optional[str] = None):
    """Test sending an email."""
    print("\n=== Testing Send Email ===")
    print(f"From Account: {account_email}")
    print(f"To: {to}")
    print(f"Subject: {subject}")
    
    # Confirm before sending
    confirm = input("\nDo you want to send this email? (y/n): ").lower()
    if confirm != 'y':
        print("Email sending cancelled.")
        return
    
    result = await email_agent.send_email(account_email, to, subject, body, cc, bcc)
    
    if "error" in result:
        print(f"Error: {result['error']}")
        return
    
    print(f"Email sent successfully!")
    print(f"Message ID: {result['message_id']}")
    
    return result

async def test_search_across_accounts(email_agent: MultiAccountEmailAgent, query: str, 
                                     max_results_per_account: int = 5, 
                                     priority_only: bool = True):
    """Test searching across accounts."""
    print("\n=== Testing Search Across Accounts ===")
    print(f"Query: {query}")
    print(f"Max Results Per Account: {max_results_per_account}")
    print(f"Priority Only: {priority_only}")
    
    result = await email_agent.search_across_accounts(query, max_results_per_account, priority_only)
    
    if "error" in result:
        print(f"Error: {result['error']}")
        return
    
    if "summary" in result:
        print("\nSearch Summary:")
        print(result["summary"])
    
    # Print account-specific results
    for email, account_results in result.items():
        if email == "summary":
            continue
        
        if "error" in account_results:
            print(f"\nAccount {email}: Error - {account_results['error']}")
            continue
        
        message_count = len(account_results.get("messages", []))
        print(f"\nAccount {email}: {message_count} messages")
        
        if message_count > 0:
            print(f"\nSearch results from {email}:")
            for i, message in enumerate(account_results["messages"][:3]):
                print(f"\nMessage {i+1}:")
                print(f"From: {message['from']}")
                print(f"Subject: {message['subject']}")
                print(f"Date: {message['date']}")
                print(f"Snippet: {message['snippet']}")
    
    return result

async def run_interactive_test(email_agent: MultiAccountEmailAgent):
    """Run an interactive test of the Multi-Account Email Agent."""
    print("\n=== Interactive Multi-Account Email Agent Test ===")
    print("This test will guide you through testing the Multi-Account Email Agent's capabilities.")
    
    # Test listing accounts
    accounts = await test_list_accounts(email_agent)
    
    if not accounts:
        print("No accounts found or enabled.")
        return
    
    # Select an account for testing
    print("\nSelect an account for testing:")
    for i, account in enumerate(accounts):
        print(f"{i+1}. {account['email']} - {account['info']['description']}")
    
    account_selection = int(input("\nEnter the number of the account to use (1-{len(accounts)}): ") or "1") - 1
    if account_selection < 0 or account_selection >= len(accounts):
        print("Invalid selection.")
        return
    
    selected_account = accounts[account_selection]['email']
    print(f"\nSelected account: {selected_account}")
    
    # Test reading emails
    query = input("\nEnter a search query for emails (or leave blank for all): ")
    max_results = int(input("Enter maximum number of emails to retrieve per account (default 5): ") or "5")
    priority_only = input("Only include priority accounts? (y/n, default y): ").lower() != 'n'
    
    messages = await test_read_emails(email_agent, query if query else None, max_results, priority_only)
    
    if not messages:
        print("No emails found to analyze or respond to.")
        return
    
    # Filter messages for the selected account
    account_messages = [msg for msg in messages if msg.get('account') == selected_account]
    
    if not account_messages:
        print(f"No emails found for the selected account: {selected_account}")
        return
    
    # Select an email to analyze
    print(f"\nSelect an email from {selected_account} to analyze:")
    for i, message in enumerate(account_messages[:min(5, len(account_messages))]):
        print(f"{i+1}. From: {message['from']} - Subject: {message['subject']}")
    
    message_selection = int(input("\nEnter the number of the email to analyze (1-{len(account_messages)}): ") or "1") - 1
    if message_selection < 0 or message_selection >= len(account_messages):
        print("Invalid selection.")
        return
    
    selected_message = account_messages[message_selection]
    
    # Analyze the selected email
    analysis_result = await test_analyze_email(email_agent, selected_account, selected_message['id'])
    
    if not analysis_result:
        print("Failed to analyze email.")
        return
    
    # Draft a response
    response_type = input("\nEnter response type (formal, casual, sales, support, professional): ") or "professional"
    include_reasoning = input("Include reasoning? (y/n): ").lower() == 'y'
    
    draft_result = await test_draft_response(email_agent, selected_account, selected_message['id'], response_type, include_reasoning)
    
    if not draft_result:
        print("Failed to draft response.")
        return
    
    # Optionally send the email
    send_email = input("\nDo you want to send this email? (y/n): ").lower() == 'y'
    
    if send_email:
        to = input(f"Enter recipient email (default: {analysis_result['from']}): ") or analysis_result['from']
        subject = input(f"Enter subject (default: Re: {analysis_result['subject']}): ") or f"Re: {analysis_result['subject']}"
        body = draft_result["draft_response"]
        
        print("\nEmail Body:")
        print(body)
        
        await test_send_email(email_agent, selected_account, to, subject, body)
    
    # Test search across accounts
    search_test = input("\nDo you want to test searching across accounts? (y/n): ").lower() == 'y'
    
    if search_test:
        search_query = input("\nEnter search query: ")
        max_results = int(input("Enter maximum number of results per account (default 5): ") or "5")
        priority_only = input("Only include priority accounts? (y/n, default y): ").lower() != 'n'
        
        await test_search_across_accounts(email_agent, search_query, max_results, priority_only)

async def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Test the Multi-Account Email Agent")
    parser.add_argument("--interactive", action="store_true", help="Run an interactive test")
    parser.add_argument("--list-accounts", action="store_true", help="Test listing accounts")
    parser.add_argument("--read", action="store_true", help="Test reading emails")
    parser.add_argument("--analyze", action="store_true", help="Test analyzing an email")
    parser.add_argument("--draft", action="store_true", help="Test drafting a response")
    parser.add_argument("--send", action="store_true", help="Test sending an email")
    parser.add_argument("--search", action="store_true", help="Test searching across accounts")
    parser.add_argument("--account", help="Email address of the account to use")
    parser.add_argument("--email-id", help="ID of the email to analyze or respond to")
    parser.add_argument("--to", help="Recipient email address for send test")
    parser.add_argument("--subject", help="Email subject for send test")
    parser.add_argument("--body", help="Email body for send test")
    parser.add_argument("--query", help="Search query for read or search test")
    parser.add_argument("--max-results", type=int, default=5, help="Maximum results per account")
    parser.add_argument("--priority-only", action="store_true", default=True, help="Only include priority accounts")
    
    args = parser.parse_args()
    
    # Create LLM connector
    llm_connector = LLMConnectorFactory.create_connector()
    
    # Create Multi-Account Email Agent
    email_agent = MultiAccountEmailAgent(llm_connector=llm_connector)
    
    # Run tests
    if args.interactive or not (args.list_accounts or args.read or args.analyze or args.draft or args.send or args.search):
        await run_interactive_test(email_agent)
    else:
        if args.list_accounts:
            await test_list_accounts(email_agent)
        
        if args.read:
            await test_read_emails(email_agent, args.query, args.max_results, args.priority_only)
        
        if args.analyze:
            if not (args.account and args.email_id):
                print("Error: --account and --email-id are required for analyze test")
                return
            
            await test_analyze_email(email_agent, args.account, args.email_id)
        
        if args.draft:
            if not (args.account and args.email_id):
                print("Error: --account and --email-id are required for draft test")
                return
            
            response_type = input("Enter response type (formal, casual, sales, support, professional): ") or "professional"
            include_reasoning = input("Include reasoning? (y/n): ").lower() == 'y'
            await test_draft_response(email_agent, args.account, args.email_id, response_type, include_reasoning)
        
        if args.send:
            if not (args.account and args.to and args.subject and args.body):
                print("Error: --account, --to, --subject, and --body are required for send test")
                return
            
            await test_send_email(email_agent, args.account, args.to, args.subject, args.body)
        
        if args.search:
            if not args.query:
                print("Error: --query is required for search test")
                return
            
            await test_search_across_accounts(email_agent, args.query, args.max_results, args.priority_only)

if __name__ == "__main__":
    asyncio.run(main())
