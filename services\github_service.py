"""
GitHub service for the Multi-Agent AI System.
"""
import asyncio
from typing import Dict, List, Optional, Any, Union
import aiohttp
import base64
import re
import os
from datetime import datetime
import json

from core.logger import setup_logger
import config

# Set up logger
logger = setup_logger("github_service")

class GitHubService:
    """
    Service for interacting with GitHub repositories.
    
    This service provides functionality for:
    - Fetching repository information
    - Searching for code
    - Downloading files and directories
    - Cloning repositories
    - Analyzing code
    """
    
    def __init__(self, config: Dict):
        """
        Initialize the GitHub service.
        
        Args:
            config (Dict): Service configuration
        """
        self.api_key = config.get("api_key", "")
        self.username = config.get("username", "")
        self.api_url = "https://api.github.com"
        self.enabled = config.get("enabled", False)
        self.cache_dir = config.get("cache_dir", "data/github_cache")
        
        # Create cache directory if it doesn't exist
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # Validate configuration
        if not self.api_key:
            logger.warning("GitHub API key not provided")
            self.enabled = False
    
    async def get_repository(self, owner: str, repo: str) -> Dict:
        """
        Get information about a repository.
        
        Args:
            owner (str): Repository owner
            repo (str): Repository name
            
        Returns:
            Dict: Repository information
        """
        if not self.enabled:
            return {"error": "GitHub service is not enabled"}
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.api_url}/repos/{owner}/{repo}",
                    headers=self._get_headers(),
                ) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        error_text = await response.text()
                        logger.error(f"Failed to get repository: {response.status} - {error_text}")
                        return {
                            "error": f"API error: {response.status}",
                            "details": error_text,
                        }
        except Exception as e:
            logger.exception(f"Error getting repository: {e}")
            return {"error": f"Failed to get repository: {str(e)}"}
    
    async def search_code(self, query: str, language: Optional[str] = None, owner: Optional[str] = None) -> Dict:
        """
        Search for code on GitHub.
        
        Args:
            query (str): Search query
            language (Optional[str]): Filter by language
            owner (Optional[str]): Filter by repository owner
            
        Returns:
            Dict: Search results
        """
        if not self.enabled:
            return {"error": "GitHub service is not enabled"}
        
        # Build search query
        search_query = query
        if language:
            search_query += f" language:{language}"
        if owner:
            search_query += f" user:{owner}"
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.api_url}/search/code",
                    headers=self._get_headers(),
                    params={"q": search_query, "per_page": 10},
                ) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        error_text = await response.text()
                        logger.error(f"Failed to search code: {response.status} - {error_text}")
                        return {
                            "error": f"API error: {response.status}",
                            "details": error_text,
                        }
        except Exception as e:
            logger.exception(f"Error searching code: {e}")
            return {"error": f"Failed to search code: {str(e)}"}
    
    async def get_file_content(self, owner: str, repo: str, path: str, ref: Optional[str] = None) -> Dict:
        """
        Get the content of a file from a repository.
        
        Args:
            owner (str): Repository owner
            repo (str): Repository name
            path (str): File path
            ref (Optional[str]): Git reference (branch, tag, commit)
            
        Returns:
            Dict: File content
        """
        if not self.enabled:
            return {"error": "GitHub service is not enabled"}
        
        # Build URL
        url = f"{self.api_url}/repos/{owner}/{repo}/contents/{path}"
        if ref:
            url += f"?ref={ref}"
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    url,
                    headers=self._get_headers(),
                ) as response:
                    if response.status == 200:
                        content_json = await response.json()
                        
                        # Check if it's a file or directory
                        if isinstance(content_json, list):
                            # It's a directory
                            return {
                                "type": "directory",
                                "path": path,
                                "items": content_json,
                            }
                        else:
                            # It's a file
                            if content_json.get("encoding") == "base64" and content_json.get("content"):
                                # Decode content
                                content = base64.b64decode(content_json["content"]).decode("utf-8")
                                return {
                                    "type": "file",
                                    "path": path,
                                    "content": content,
                                    "size": content_json.get("size", 0),
                                    "sha": content_json.get("sha", ""),
                                    "url": content_json.get("html_url", ""),
                                }
                            else:
                                return {
                                    "error": "Unsupported content format",
                                    "details": content_json,
                                }
                    else:
                        error_text = await response.text()
                        logger.error(f"Failed to get file content: {response.status} - {error_text}")
                        return {
                            "error": f"API error: {response.status}",
                            "details": error_text,
                        }
        except Exception as e:
            logger.exception(f"Error getting file content: {e}")
            return {"error": f"Failed to get file content: {str(e)}"}
    
    async def get_repository_structure(self, owner: str, repo: str, ref: Optional[str] = None) -> Dict:
        """
        Get the structure of a repository.
        
        Args:
            owner (str): Repository owner
            repo (str): Repository name
            ref (Optional[str]): Git reference (branch, tag, commit)
            
        Returns:
            Dict: Repository structure
        """
        if not self.enabled:
            return {"error": "GitHub service is not enabled"}
        
        # Start with the root directory
        return await self._get_directory_structure(owner, repo, "", ref)
    
    async def _get_directory_structure(self, owner: str, repo: str, path: str, ref: Optional[str] = None) -> Dict:
        """
        Recursively get the structure of a directory.
        
        Args:
            owner (str): Repository owner
            repo (str): Repository name
            path (str): Directory path
            ref (Optional[str]): Git reference (branch, tag, commit)
            
        Returns:
            Dict: Directory structure
        """
        # Get directory content
        content = await self.get_file_content(owner, repo, path, ref)
        
        if "error" in content:
            return content
        
        if content["type"] == "file":
            return content
        
        # Process directory items
        items = content.get("items", [])
        structure = {
            "type": "directory",
            "path": path,
            "items": [],
        }
        
        for item in items:
            item_path = item.get("path", "")
            item_type = item.get("type", "")
            
            if item_type == "file":
                structure["items"].append({
                    "type": "file",
                    "path": item_path,
                    "name": item.get("name", ""),
                    "size": item.get("size", 0),
                    "url": item.get("html_url", ""),
                })
            elif item_type == "dir":
                # Add directory with placeholder for items
                structure["items"].append({
                    "type": "directory",
                    "path": item_path,
                    "name": item.get("name", ""),
                    "url": item.get("html_url", ""),
                })
        
        return structure
    
    async def clone_repository(self, owner: str, repo: str, ref: Optional[str] = None) -> Dict:
        """
        Clone a repository to the local cache.
        
        Args:
            owner (str): Repository owner
            repo (str): Repository name
            ref (Optional[str]): Git reference (branch, tag, commit)
            
        Returns:
            Dict: Clone result
        """
        if not self.enabled:
            return {"error": "GitHub service is not enabled"}
        
        # Create cache directory for this repository
        repo_cache_dir = os.path.join(self.cache_dir, f"{owner}_{repo}")
        os.makedirs(repo_cache_dir, exist_ok=True)
        
        # Get repository structure
        structure = await self.get_repository_structure(owner, repo, ref)
        
        if "error" in structure:
            return structure
        
        # Clone repository by downloading all files
        await self._clone_directory(owner, repo, "", repo_cache_dir, ref)
        
        return {
            "status": "success",
            "repository": f"{owner}/{repo}",
            "cache_dir": repo_cache_dir,
        }
    
    async def _clone_directory(self, owner: str, repo: str, path: str, local_dir: str, ref: Optional[str] = None) -> None:
        """
        Recursively clone a directory.
        
        Args:
            owner (str): Repository owner
            repo (str): Repository name
            path (str): Directory path
            local_dir (str): Local directory to clone to
            ref (Optional[str]): Git reference (branch, tag, commit)
        """
        # Get directory content
        content = await self.get_file_content(owner, repo, path, ref)
        
        if "error" in content:
            logger.error(f"Error cloning directory {path}: {content['error']}")
            return
        
        if content["type"] == "file":
            # Write file to local directory
            file_path = os.path.join(local_dir, os.path.basename(path))
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content["content"])
            return
        
        # Process directory items
        items = content.get("items", [])
        
        for item in items:
            item_path = item.get("path", "")
            item_type = item.get("type", "")
            item_name = item.get("name", "")
            
            if item_type == "file":
                # Get file content and write to local directory
                file_content = await self.get_file_content(owner, repo, item_path, ref)
                if "error" not in file_content:
                    file_path = os.path.join(local_dir, item_name)
                    with open(file_path, "w", encoding="utf-8") as f:
                        f.write(file_content["content"])
            elif item_type == "dir":
                # Create local directory and clone recursively
                dir_path = os.path.join(local_dir, item_name)
                os.makedirs(dir_path, exist_ok=True)
                await self._clone_directory(owner, repo, item_path, dir_path, ref)
    
    async def search_repositories(self, query: str, language: Optional[str] = None, sort: str = "stars") -> Dict:
        """
        Search for repositories on GitHub.
        
        Args:
            query (str): Search query
            language (Optional[str]): Filter by language
            sort (str): Sort by (stars, forks, updated)
            
        Returns:
            Dict: Search results
        """
        if not self.enabled:
            return {"error": "GitHub service is not enabled"}
        
        # Build search query
        search_query = query
        if language:
            search_query += f" language:{language}"
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.api_url}/search/repositories",
                    headers=self._get_headers(),
                    params={"q": search_query, "sort": sort, "per_page": 10},
                ) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        error_text = await response.text()
                        logger.error(f"Failed to search repositories: {response.status} - {error_text}")
                        return {
                            "error": f"API error: {response.status}",
                            "details": error_text,
                        }
        except Exception as e:
            logger.exception(f"Error searching repositories: {e}")
            return {"error": f"Failed to search repositories: {str(e)}"}
    
    def _get_headers(self) -> Dict[str, str]:
        """
        Get headers for GitHub API requests.
        
        Returns:
            Dict[str, str]: Headers
        """
        return {
            "Authorization": f"token {self.api_key}",
            "Accept": "application/vnd.github.v3+json",
            "User-Agent": f"Multi-Agent-AI-System/{config.VERSION}",
        }
    
    def is_enabled(self) -> bool:
        """
        Check if the service is enabled.
        
        Returns:
            bool: True if enabled, False otherwise
        """
        return self.enabled


class GitHubServiceFactory:
    """
    Factory for creating GitHub service.
    """
    
    @staticmethod
    def create_service() -> Optional[GitHubService]:
        """
        Create a GitHub service.
        
        Returns:
            Optional[GitHubService]: GitHub service instance
        """
        return GitHubService(config.GITHUB_CONFIG)
