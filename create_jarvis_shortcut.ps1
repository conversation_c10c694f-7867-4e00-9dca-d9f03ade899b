# Create a desktop shortcut for <PERSON> with AlphaEvolve integration

# Get the current script directory
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path

# Define the target script
$targetScript = Join-Path -Path $scriptPath -ChildPath "start_jarvis.ps1"

# Get the desktop path
$desktopPath = [Environment]::GetFolderPath("Desktop")

# Define the shortcut path
$shortcutPath = Join-Path -Path $desktopPath -ChildPath "Jarvis with AlphaEvolve.lnk"

# Create a WScript.Shell object
$shell = New-Object -ComObject WScript.Shell

# Create the shortcut
$shortcut = $shell.CreateShortcut($shortcutPath)
$shortcut.TargetPath = "powershell.exe"
$shortcut.Arguments = "-ExecutionPolicy Bypass -File `"$targetScript`""
$shortcut.WorkingDirectory = $scriptPath
$shortcut.Description = "Start Jarvis with AlphaEvolve integration"
$shortcut.IconLocation = "powershell.exe,0"
$shortcut.Save()

Write-Host "Shortcut created on your desktop: 'Jarvis with AlphaEvolve.lnk'" -ForegroundColor Green
Write-Host "You can now double-click this shortcut to start <PERSON> with AlphaEvolve integration." -ForegroundColor Cyan

# Pause to see the message
Write-Host "Press any key to exit..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
