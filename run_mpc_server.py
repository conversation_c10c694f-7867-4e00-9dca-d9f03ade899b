"""
Run an MPC server for the Borg Cluster Management System.

This script starts an MPC server that can be discovered by the Borg Cluster Management System.
"""
import asyncio
import argparse
import logging
import os
import sys
import signal
import json
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent))

from core.logger import setup_logger
from mpc_servers.mpc_server import MPCServer
from mpc_servers.simple_mpc_server import SimpleMPCServer
from mpc_servers.advanced_mpc_server import AdvancedMPCServer

# Set up logger
logger = setup_logger("run_mpc_server")

# Global flag to control system shutdown
shutdown_event = asyncio.Event()

def handle_shutdown(sig, frame):
    """Handle shutdown signals gracefully."""
    logger.info(f"Received shutdown signal {sig}")
    shutdown_event.set()

# Register signal handlers
signal.signal(signal.SIGINT, handle_shutdown)
signal.signal(signal.SIGTERM, handle_shutdown)

async def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Run an MPC server")
    parser.add_argument("--id", type=str, default="mpc-server-1", help="Server ID")
    parser.add_argument("--host", type=str, default="localhost", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8765, help="Port to bind to")
    parser.add_argument("--type", type=str, choices=["standard", "simple", "advanced"], default="standard", help="Server type")
    parser.add_argument("--ssl", action="store_true", help="Enable SSL")
    parser.add_argument("--cert", type=str, help="Path to SSL certificate file")
    parser.add_argument("--key", type=str, help="Path to SSL key file")
    parser.add_argument("--security-tools", type=str, help="Path to security tools directory")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    args = parser.parse_args()
    
    # Set log level
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        # Create server based on type
        if args.type == "simple":
            server = SimpleMPCServer(
                server_id=args.id,
                host=args.host,
                port=args.port,
                use_ssl=args.ssl,
                cert_file=args.cert,
                key_file=args.key,
            )
            logger.info(f"Created Simple MPC Server {args.id} on {args.host}:{args.port}")
        elif args.type == "advanced":
            server = AdvancedMPCServer(
                server_id=args.id,
                host=args.host,
                port=args.port,
                use_ssl=args.ssl,
                cert_file=args.cert,
                key_file=args.key,
                security_tools_dir=args.security_tools,
            )
            logger.info(f"Created Advanced MPC Server {args.id} on {args.host}:{args.port}")
        else:  # standard
            server = MPCServer(
                server_id=args.id,
                host=args.host,
                port=args.port,
                use_ssl=args.ssl,
                cert_file=args.cert,
                key_file=args.key,
            )
            logger.info(f"Created Standard MPC Server {args.id} on {args.host}:{args.port}")
        
        # Start server in a separate task
        server_task = asyncio.create_task(server.start())
        
        logger.info(f"Started MPC Server {args.id} on {args.host}:{args.port}")
        
        # Wait for shutdown signal
        await shutdown_event.wait()
        
        # Cancel server task
        server_task.cancel()
        try:
            await server_task
        except asyncio.CancelledError:
            pass
        
        logger.info(f"MPC Server {args.id} shutdown complete")
        
    except Exception as e:
        logger.exception(f"Error running MPC Server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
