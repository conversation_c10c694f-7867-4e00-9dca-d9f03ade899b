@echo off
echo Starting Local Hugging Face API Server for UI-TARS...

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Start the server
echo Server is starting at http://127.0.0.1:8000
echo.
echo Configure UI-TARS with these settings:
echo - VLM Provider: Hugging Face
echo - VLM Base URL: http://127.0.0.1:8000
echo - VLM API Key: dummy_key
echo - VLM Model Name: UI-TARS-1.5-7B
echo.
echo Press Ctrl+C to stop the server when you're done.
echo.

python local_hf_server.py --dummy

exit /b 0
