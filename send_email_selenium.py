#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to send an email to Alyssa C. using Selenium browser automation.
This script properly integrates with the agent system and ensures
the email fields are correctly populated.
"""

import os
import sys
import time
import logging
from datetime import datetime

try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.common.keys import Keys
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False
    print("Selenium is not installed. Please install it with: pip install selenium")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("EmailSender")

# Email content
EMAIL_SUBJECT = "URGENT: Your Insurance Options - Coverage Available Within Your $100 Monthly Budget"
EMAIL_BODY = """
Hi <PERSON><PERSON>,

I hope this message finds you well. We've been trying to reach you through multiple channels (email, phone calls, voicemails, and texts) regarding your insurance needs, and I wanted to follow up personally as this is time-sensitive.

Based on your specific situation and $100 monthly budget, we have options ready for you that provide excellent coverage:

For your IUL policy (approximately $65/month):
- Cash value growth potential tied to market performance without the downside risk
- Death benefit protection for your loved ones
- Tax-free access to your cash value for future needs
- Living benefits that allow access to your death benefit if you become critically ill

For your health/dental/vision package (approximately $35/month):
- Comprehensive health coverage with our top-tier carriers that offer exceptional benefits
- Dental coverage including preventive care, basic procedures, and major work
- Vision benefits covering exams, frames, and contacts

What makes us the best agency to handle your insurance needs:
1. Our carriers offer some of the most comprehensive health benefits in the industry, with lower deductibles and better coverage than you'll find elsewhere
2. We have flexible IUL, whole life, and term policy options that can be customized to your exact needs
3. Our mortgage protection extends for the entire life of your loan, unlike competitors who offer limited coverage periods
4. For qualified applicants like yourself, we can secure over $1 million in coverage

We need to speak with you as soon as possible to secure this coverage before rates change. I have the following time slots available tomorrow (Monday):
- 10:00 AM - 10:30 AM
- 1:00 PM - 1:30 PM
- 4:00 PM - 4:30 PM

Or Tuesday:
- 9:00 AM - 9:30 AM
- 2:00 PM - 2:30 PM

Please let me know which time works best for you, or you can schedule directly through our Calendly link:
https://calendly.com/flofaction/insurance-consultation

It's critical that we connect in the next 24-48 hours to ensure we can lock in these rates for you.

Looking forward to speaking with you soon,

Paul Edwards
Flo Faction Insurance
(772) 208-9646
"""

RECIPIENT_EMAIL = "<EMAIL>"  # Replace with actual email
GMAIL_URL = "https://mail.google.com/mail/u/0/#inbox?compose=new"

class EmailSender:
    """Class to send emails using Selenium browser automation"""
    
    def __init__(self):
        self.driver = None
        self.logger = logging.getLogger("EmailSender.Selenium")
    
    def setup_driver(self):
        """Set up the Chrome WebDriver"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--start-maximized")
            
            # Create a new Chrome driver
            self.driver = webdriver.Chrome(options=chrome_options)
            self.logger.info("Chrome WebDriver initialized successfully")
            return True
        except Exception as e:
            self.logger.error(f"Failed to initialize Chrome WebDriver: {e}")
            return False
    
    def navigate_to_gmail(self):
        """Navigate to Gmail compose page"""
        try:
            self.driver.get(GMAIL_URL)
            self.logger.info(f"Navigated to: {GMAIL_URL}")
            
            # Wait for Gmail to load
            WebDriverWait(self.driver, 30).until(
                EC.presence_of_element_located((By.XPATH, "//div[contains(@role, 'dialog')]"))
            )
            
            # Check if we need to log in
            if "accounts.google.com" in self.driver.current_url:
                self.logger.info("Login page detected, please log in manually")
                
                # Wait for manual login
                input("Please log in to Gmail manually and press Enter when done...")
                
                # Navigate to compose again after login
                self.driver.get(GMAIL_URL)
                WebDriverWait(self.driver, 30).until(
                    EC.presence_of_element_located((By.XPATH, "//div[contains(@role, 'dialog')]"))
                )
            
            return True
        except Exception as e:
            self.logger.error(f"Failed to navigate to Gmail: {e}")
            return False
    
    def compose_email(self, recipient, subject, body):
        """Compose and send an email"""
        try:
            # Wait for compose form to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//div[contains(@role, 'dialog')]"))
            )
            
            # Fill in recipient
            recipient_field = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//input[contains(@role, 'combobox') and contains(@aria-label, 'To')]"))
            )
            recipient_field.send_keys(recipient)
            recipient_field.send_keys(Keys.TAB)
            self.logger.info(f"Entered recipient: {recipient}")
            
            # Fill in subject
            subject_field = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.NAME, "subjectbox"))
            )
            subject_field.send_keys(subject)
            self.logger.info(f"Entered subject: {subject}")
            
            # Fill in email body
            body_field = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//div[contains(@role, 'textbox') and contains(@aria-label, 'Message Body')]"))
            )
            body_field.send_keys(body)
            self.logger.info("Entered email body")
            
            # Click send button
            send_button = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//div[contains(@role, 'button') and contains(@aria-label, 'Send')]"))
            )
            send_button.click()
            self.logger.info("Clicked send button")
            
            # Wait for confirmation
            time.sleep(3)
            
            self.logger.info(f"Email sent successfully to {recipient}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to compose and send email: {e}")
            return False
    
    def close_driver(self):
        """Close the WebDriver"""
        if self.driver:
            try:
                self.driver.quit()
                self.logger.info("WebDriver closed successfully")
            except Exception as e:
                self.logger.error(f"Error closing WebDriver: {e}")

def send_email():
    """Send an email to Alyssa C. using Selenium browser automation"""
    sender = EmailSender()
    
    try:
        # Set up the WebDriver
        if not sender.setup_driver():
            logger.error("Failed to set up WebDriver")
            return False
        
        # Navigate to Gmail
        if not sender.navigate_to_gmail():
            logger.error("Failed to navigate to Gmail")
            return False
        
        # Compose and send the email
        if not sender.compose_email(RECIPIENT_EMAIL, EMAIL_SUBJECT, EMAIL_BODY):
            logger.error("Failed to compose and send email")
            return False
        
        logger.info(f"Email sent successfully to {RECIPIENT_EMAIL}")
        return True
    
    except Exception as e:
        logger.error(f"Error sending email: {e}")
        return False
    
    finally:
        # Close the WebDriver
        sender.close_driver()

if __name__ == "__main__":
    logger.info("Starting email automation to Alyssa C. using Selenium...")
    success = send_email()
    if success:
        logger.info("Email automation completed successfully!")
        sys.exit(0)
    else:
        logger.error("Email automation failed!")
        sys.exit(1)
