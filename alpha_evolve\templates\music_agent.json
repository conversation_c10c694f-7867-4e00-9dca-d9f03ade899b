{
  "name": "Music Agent Enhancement",
  "description": "Template for enhancing music agent capabilities",
  "template": "You are tasked with enhancing the {capability} capability of a music agent.

The goal is to optimize for {optimization_metric}.

The music agent handles various music industry tasks including metadata management, sync licensing, release promotion, EPK creation, and artwork design.

Requirements:
1. The implementation must understand music industry standards and best practices
2. It must handle music metadata accurately and comprehensively
3. It should identify and pursue appropriate licensing opportunities
4. It must create professional and compelling promotional materials
5. It should optimize release strategies for maximum impact

Your solution should be implemented as a Python function that follows this interface:
{interface}

Focus on creating a solution that maximizes {optimization_metric} while maintaining artistic integrity and industry standards.",
  "variables": ["capability", "optimization_metric", "interface"]
}
