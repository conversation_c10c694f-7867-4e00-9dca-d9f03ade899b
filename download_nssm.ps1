# Download NSSM (Non-Sucking Service Manager)

# Get the current script directory
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path

# Define the NSSM download URL and destination
$nssmUrl = "https://nssm.cc/release/nssm-2.24.zip"
$nssmZipPath = Join-Path -Path $scriptPath -ChildPath "nssm.zip"
$nssmExtractPath = Join-Path -Path $scriptPath -ChildPath "nssm-extract"

Write-Host "Downloading NSSM from $nssmUrl..." -ForegroundColor Cyan

# Download NSSM
try {
    Invoke-WebRequest -Uri $nssmUrl -OutFile $nssmZipPath
    Write-Host "Download complete." -ForegroundColor Green
}
catch {
    Write-Host "Failed to download NSSM: $_" -ForegroundColor Red
    exit
}

# Create extraction directory if it doesn't exist
if (-not (Test-Path $nssmExtractPath)) {
    New-Item -Path $nssmExtractPath -ItemType Directory | Out-Null
}

# Extract the ZIP file
Write-Host "Extracting NSSM..." -ForegroundColor Cyan
try {
    Expand-Archive -Path $nssmZipPath -DestinationPath $nssmExtractPath -Force
    Write-Host "Extraction complete." -ForegroundColor Green
}
catch {
    Write-Host "Failed to extract NSSM: $_" -ForegroundColor Red
    exit
}

# Find the appropriate NSSM executable based on architecture
$architecture = if ([Environment]::Is64BitOperatingSystem) { "win64" } else { "win32" }
$nssmExePath = Join-Path -Path $nssmExtractPath -ChildPath "nssm-2.24\$architecture\nssm.exe"

if (Test-Path $nssmExePath) {
    # Copy NSSM to the script directory
    Copy-Item -Path $nssmExePath -Destination $scriptPath
    Write-Host "NSSM copied to $scriptPath" -ForegroundColor Green
    
    # Clean up
    Remove-Item -Path $nssmZipPath -Force
    Remove-Item -Path $nssmExtractPath -Recurse -Force
    
    Write-Host "NSSM is now ready to use." -ForegroundColor Green
    Write-Host "You can now run install_alphaevolve_service.ps1 to install AlphaEvolve as a Windows service." -ForegroundColor Cyan
}
else {
    Write-Host "Could not find NSSM executable for $architecture architecture." -ForegroundColor Red
}

# Pause to see the message
Write-Host "Press any key to exit..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
