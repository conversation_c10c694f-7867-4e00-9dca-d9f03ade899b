{"id": "insurance_claim_workflow", "name": "Insurance Claim Processing Workflow", "description": "Processes an insurance claim with fraud detection, risk assessment, and financial impact analysis", "version": "1.0", "created_at": "2023-05-01T12:00:00Z", "updated_at": "2023-05-01T12:00:00Z", "steps": [{"id": "step_1", "name": "Claim Validation", "description": "Validate the claim data and check policy coverage", "type": "agent_task", "agent_id": "insurance_agent", "task_type": "claim_processing", "parameters": {"claim_id": "${claim_id}", "validation_level": "detailed"}}, {"id": "step_2", "name": "<PERSON>aud Detection", "description": "Run advanced fraud detection on the claim", "type": "agent_task", "agent_id": "insurance_agent", "task_type": "fraud_detection", "parameters": {"claim_id": "${claim_id}", "use_quantum_detection": true}}, {"id": "step_3", "name": "Fraud Assessment", "description": "Evaluate fraud detection results", "type": "condition", "condition": {"type": "simple", "field": "fraud_assessment.assessment", "operator": "eq", "value": "fraudulent"}, "true_branch": [{"id": "step_3a", "name": "<PERSON><PERSON>", "description": "Initiate fraud investigation process", "type": "agent_task", "agent_id": "insurance_agent", "task_type": "fraud_investigation", "parameters": {"claim_id": "${claim_id}", "fraud_assessment": "${fraud_assessment}"}}], "false_branch": [{"id": "step_4", "name": "Risk Assessment", "description": "Assess risk associated with the claim", "type": "agent_task", "agent_id": "insurance_agent", "task_type": "risk_assessment", "parameters": {"policy_id": "${policy_id}"}}, {"id": "step_5", "name": "Financial Impact Analysis", "description": "Analyze financial impact of claim payout", "type": "parallel", "branches": [[{"id": "step_5a", "name": "Reserve Analysis", "description": "Analyze impact on reserves", "type": "agent_task", "agent_id": "insurance_agent", "task_type": "reserve_analysis", "parameters": {"claim_id": "${claim_id}", "policy_id": "${policy_id}", "claim_amount": "${claim_amount}"}}], [{"id": "step_5b", "name": "Investment Impact", "description": "Analyze impact on investment portfolio", "type": "agent_task", "agent_id": "trading_agent", "task_type": "portfolio_analysis", "parameters": {"portfolio_id": "insurance_reserves", "withdrawal_amount": "${claim_amount}", "time_horizon": "short_term"}}]]}, {"id": "step_6", "name": "Decision Analysis", "description": "Analyze claim decision using advanced reasoning", "type": "reasoning", "reasoning_type": "causal", "context": "Claim Information: ${claim_data}\nRisk Assessment: ${risk_assessment}\nFinancial Impact: ${financial_impact}", "question": "What is the optimal decision for this claim based on risk assessment and financial impact?", "variables": ["claim_amount", "risk_level", "financial_impact", "policy_terms", "customer_history"]}, {"id": "step_7", "name": "Claim Decision", "description": "Make final decision on claim", "type": "condition", "condition": {"type": "and", "conditions": [{"type": "simple", "field": "risk_assessment.risk_level", "operator": "in", "value": ["low", "medium"]}, {"type": "simple", "field": "financial_impact.reserves.impact_level", "operator": "in", "value": ["low", "medium"]}]}, "true_branch": [{"id": "step_7a", "name": "Approve Claim", "description": "Approve and process claim payment", "type": "agent_task", "agent_id": "insurance_agent", "task_type": "approve_claim", "parameters": {"claim_id": "${claim_id}", "approval_notes": "Approved based on risk assessment and financial impact analysis", "payment_method": "${preferred_payment_method}"}}, {"id": "step_7b", "name": "Portfolio Adjustment", "description": "Adjust investment portfolio after claim payment", "type": "agent_task", "agent_id": "trading_agent", "task_type": "portfolio_adjustment", "parameters": {"portfolio_id": "insurance_reserves", "adjustment_type": "rebalance", "withdrawal_amount": "${claim_amount}"}}], "false_branch": [{"id": "step_7c", "name": "Additional Review", "description": "Flag claim for additional review", "type": "agent_task", "agent_id": "insurance_agent", "task_type": "flag_for_review", "parameters": {"claim_id": "${claim_id}", "review_reason": "High risk or significant financial impact", "risk_assessment": "${risk_assessment}", "financial_impact": "${financial_impact}"}}]}]}, {"id": "step_8", "name": "Customer Notification", "description": "Notify customer of claim decision", "type": "agent_task", "agent_id": "insurance_agent", "task_type": "customer_notification", "parameters": {"customer_id": "${customer_id}", "claim_id": "${claim_id}", "notification_type": "email", "notification_template": "claim_decision"}}, {"id": "step_9", "name": "Workflow Summary", "description": "Generate summary of workflow execution", "type": "transformation", "transformation_type": "map", "mapping": {"workflow_id": "insurance_claim_workflow", "claim_id": "claim_id", "customer_id": "customer_id", "decision": "claim_decision", "fraud_detected": {"source": "fraud_assessment.assessment", "transform": {"type": "bool"}}, "risk_level": "risk_assessment.risk_level", "financial_impact": "financial_impact.summary", "completed_at": {"source": "current_timestamp", "transform": {"type": "string"}}}}], "input_schema": {"type": "object", "required": ["claim_id", "policy_id", "customer_id", "claim_amount"], "properties": {"claim_id": {"type": "string", "description": "Unique identifier for the claim"}, "policy_id": {"type": "string", "description": "Unique identifier for the policy"}, "customer_id": {"type": "string", "description": "Unique identifier for the customer"}, "claim_amount": {"type": "number", "description": "Amount of the claim in dollars"}, "preferred_payment_method": {"type": "string", "description": "Customer's preferred payment method", "default": "direct_deposit"}}}, "output_schema": {"type": "object", "properties": {"workflow_id": {"type": "string", "description": "Identifier for the workflow"}, "claim_id": {"type": "string", "description": "Unique identifier for the claim"}, "customer_id": {"type": "string", "description": "Unique identifier for the customer"}, "decision": {"type": "string", "description": "Final decision on the claim", "enum": ["approved", "denied", "under_review"]}, "fraud_detected": {"type": "boolean", "description": "Whether fraud was detected"}, "risk_level": {"type": "string", "description": "Risk level assessment", "enum": ["low", "medium", "high", "very_high"]}, "financial_impact": {"type": "object", "description": "Summary of financial impact"}, "completed_at": {"type": "string", "description": "Timestamp when workflow completed"}}}}