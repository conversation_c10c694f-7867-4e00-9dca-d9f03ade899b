"""
Mock LLM Connector for testing purposes.
"""
from typing import Dict, List, Optional, Union
import json
import random

from llm.llm_connector import LLMConnector
from core.logger import setup_logger

# Set up logger
logger = setup_logger("mock_llm_connector")

class MockLLMConnector(LLMConnector):
    """
    Mock LLM connector for testing purposes.
    
    This connector returns predefined responses for testing without
    making actual API calls to LLM providers.
    """
    
    def __init__(self, config: Dict):
        """
        Initialize the mock LLM connector.
        
        Args:
            config (Dict): LLM configuration
        """
        super().__init__(config)
        self.provider = "mock"
        self.enabled = True
        self.logger.info("Mock LLM connector initialized")
    
    async def generate_text(
        self,
        prompt: str,
        model: Optional[str] = None,
        max_tokens: int = 1000,
        temperature: float = 0.7,
        stop_sequences: Optional[List[str]] = None,
        **kwargs
    ) -> str:
        """
        Generate text using the mock LLM.
        
        Args:
            prompt (str): Input prompt
            model (Optional[str]): Model to use, defaults to default_model
            max_tokens (int): Maximum number of tokens to generate
            temperature (float): Sampling temperature
            stop_sequences (Optional[List[str]]): Sequences that stop generation
            **kwargs: Additional model-specific parameters
            
        Returns:
            str: Generated text
        """
        self.logger.info(f"Mock LLM generating text for prompt: {prompt[:50]}...")
        
        # Generate a mock response based on the prompt
        if "analyze" in prompt.lower() and "email" in prompt.lower():
            return self._generate_email_analysis()
        elif "draft" in prompt.lower() and "response" in prompt.lower():
            return self._generate_email_response()
        elif "summarize" in prompt.lower() and "email" in prompt.lower():
            return self._generate_email_summary()
        else:
            return "This is a mock response from the LLM. In a real implementation, this would be a response from the actual LLM provider."
    
    async def generate_chat(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        max_tokens: int = 1000,
        temperature: float = 0.7,
        stop_sequences: Optional[List[str]] = None,
        **kwargs
    ) -> Dict:
        """
        Generate a chat response using the mock LLM.
        
        Args:
            messages (List[Dict[str, str]]): List of message dictionaries
            model (Optional[str]): Model to use, defaults to default_model
            max_tokens (int): Maximum number of tokens to generate
            temperature (float): Sampling temperature
            stop_sequences (Optional[List[str]]): Sequences that stop generation
            **kwargs: Additional model-specific parameters
            
        Returns:
            Dict: Response containing generated text and metadata
        """
        self.logger.info(f"Mock LLM generating chat response for {len(messages)} messages")
        
        # Get the last message content
        last_message = messages[-1]["content"] if messages else ""
        
        # Generate a mock response
        response_text = await self.generate_text(last_message, model, max_tokens, temperature, stop_sequences, **kwargs)
        
        return {
            "text": response_text,
            "model": model or self.default_model,
            "usage": {
                "prompt_tokens": len(last_message.split()),
                "completion_tokens": len(response_text.split()),
                "total_tokens": len(last_message.split()) + len(response_text.split())
            }
        }
    
    async def embed_text(
        self,
        text: Union[str, List[str]],
        model: Optional[str] = None,
        **kwargs
    ) -> Dict:
        """
        Generate embeddings for text.
        
        Args:
            text (Union[str, List[str]]): Text to embed
            model (Optional[str]): Model to use, defaults to default_model
            **kwargs: Additional model-specific parameters
            
        Returns:
            Dict: Response containing embeddings and metadata
        """
        self.logger.info(f"Mock LLM generating embeddings")
        
        # Convert text to list if it's a string
        if isinstance(text, str):
            text_list = [text]
        else:
            text_list = text
        
        # Generate mock embeddings
        embeddings = []
        for t in text_list:
            # Generate a random embedding vector of length 384
            embedding = [random.uniform(-1, 1) for _ in range(384)]
            embeddings.append(embedding)
        
        return {
            "embeddings": embeddings,
            "model": model or self.default_model,
            "usage": {
                "prompt_tokens": sum(len(t.split()) for t in text_list),
                "total_tokens": sum(len(t.split()) for t in text_list)
            }
        }
    
    def _generate_email_analysis(self) -> str:
        """Generate a mock email analysis."""
        return """
        # Email Analysis

        ## Main Purpose
        This email appears to be a business inquiry about insurance coverage options for a small business.

        ## Key Points
        - The sender is looking for comprehensive business insurance
        - They specifically mentioned liability and property coverage
        - They have 15 employees and operate in the retail sector
        - They requested a quote and information about available plans

        ## Tone and Sentiment
        The email has a professional and straightforward tone. The sender seems genuinely interested in your services and is looking for helpful information.

        ## Action Items
        1. Prepare a quote for business insurance
        2. Send information about liability and property coverage options
        3. Include details about any special plans for retail businesses

        ## Potential Concerns
        None identified. This appears to be a legitimate business inquiry.

        ## Recommended Next Steps
        Respond promptly with the requested information and quote. This is a good potential lead that should be prioritized.
        """
    
    def _generate_email_response(self) -> str:
        """Generate a mock email response."""
        return """
        Dear [Client Name],

        Thank you for reaching out to Flo Faction Insurance about business insurance options for your retail company. I appreciate your interest in our services.

        Based on the information you provided about your 15-employee retail business, I've prepared some preliminary information about our coverage options:

        1. **General Liability Insurance**: Protects against claims of bodily injury, property damage, and advertising injury.
        
        2. **Commercial Property Insurance**: Covers your building, inventory, equipment, and fixtures against damage from fire, theft, and certain natural disasters.
        
        3. **Business Interruption Insurance**: Helps replace lost income if your business must temporarily close due to a covered event.
        
        4. **Workers' Compensation**: Required coverage for your employees that provides benefits for work-related injuries or illnesses.

        We offer specialized packages for retail businesses that combine these coverages at competitive rates. I'd be happy to prepare a detailed quote for you. To do so, I'll need a few additional details about your business:
        
        - The approximate square footage of your retail space
        - The value of your inventory and equipment
        - Your business's annual revenue
        - Any previous claims history

        Please feel free to call me directly at (555) 123-4567 or reply to this email with the additional information. I'm also available to schedule a consultation to discuss your specific needs in more detail.

        Thank you for considering Flo Faction Insurance for your business insurance needs. I look forward to helping you protect your retail business.

        Best regards,

        Paul Edwards
        Insurance Agent
        Flo Faction Insurance
        (555) 123-4567
        <EMAIL>
        
        Reasoning:
        I crafted this response to be professional, informative, and action-oriented. I acknowledged their specific situation (retail business with 15 employees), provided relevant information about appropriate coverage types, and clearly outlined next steps. I included my contact information to make it easy for them to follow up and requested the additional information needed to provide an accurate quote.
        """
    
    def _generate_email_summary(self) -> str:
        """Generate a mock email summary."""
        return """
        I've analyzed the 5 emails in your inbox:
        
        - 3 emails are unread
        - Key senders include: John Smith (client inquiry), ABC Suppliers (invoice), and Insurance Regulatory Board (policy update)
        - The email from the Insurance Regulatory Board about policy changes requires urgent attention as it mentions compliance deadlines
        - There are 2 client inquiries about insurance quotes that should be addressed soon
        
        The most important email appears to be from the Insurance Regulatory Board regarding upcoming regulatory changes that require action within the next 7 days. I recommend prioritizing this email.
        """
