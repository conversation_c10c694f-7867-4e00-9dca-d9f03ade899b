"""
Configuration Manager for the Multi-Agent AI System.

This module provides a configuration manager that loads and validates
configuration files for the multi-agent AI system.
"""
import os
import sys
import json
import logging
from typing import Dict, Optional, Any
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("config_manager.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("config_manager")

class ConfigManager:
    """
    Configuration Manager for the Multi-Agent AI System.
    
    This class provides methods to load and validate configuration files
    for the multi-agent AI system.
    """
    
    def __init__(self, config_dir: str = "config"):
        """
        Initialize the Configuration Manager.
        
        Args:
            config_dir (str): Directory containing configuration files
        """
        self.config_dir = config_dir
        self.configs = {}
        
        logger.info(f"Configuration Manager initialized with config directory: {config_dir}")
    
    def load_config(self, config_name: str) -> Dict:
        """
        Load a configuration file.
        
        Args:
            config_name (str): Name of the configuration file (without extension)
            
        Returns:
            Dict: Configuration dictionary
        """
        config_path = os.path.join(self.config_dir, f"{config_name}.json")
        
        logger.info(f"Loading configuration from {config_path}")
        
        try:
            with open(config_path, "r") as f:
                config = json.load(f)
            
            self.configs[config_name] = config
            logger.info(f"Configuration loaded successfully: {config_name}")
            return config
        
        except FileNotFoundError:
            logger.error(f"Configuration file not found: {config_path}")
            return {}
        
        except json.JSONDecodeError:
            logger.error(f"Invalid JSON in configuration file: {config_path}")
            return {}
        
        except Exception as e:
            logger.exception(f"Error loading configuration: {e}")
            return {}
    
    def get_config(self, config_name: str) -> Dict:
        """
        Get a configuration.
        
        Args:
            config_name (str): Name of the configuration
            
        Returns:
            Dict: Configuration dictionary
        """
        if config_name in self.configs:
            return self.configs[config_name]
        else:
            return self.load_config(config_name)
    
    def save_config(self, config_name: str, config: Dict) -> bool:
        """
        Save a configuration.
        
        Args:
            config_name (str): Name of the configuration
            config (Dict): Configuration dictionary
            
        Returns:
            bool: True if successful, False otherwise
        """
        config_path = os.path.join(self.config_dir, f"{config_name}.json")
        
        logger.info(f"Saving configuration to {config_path}")
        
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(config_path), exist_ok=True)
            
            with open(config_path, "w") as f:
                json.dump(config, f, indent=4)
            
            self.configs[config_name] = config
            logger.info(f"Configuration saved successfully: {config_name}")
            return True
        
        except Exception as e:
            logger.exception(f"Error saving configuration: {e}")
            return False
    
    def validate_config(self, config_name: str, schema: Dict) -> bool:
        """
        Validate a configuration against a schema.
        
        Args:
            config_name (str): Name of the configuration
            schema (Dict): Schema dictionary
            
        Returns:
            bool: True if valid, False otherwise
        """
        config = self.get_config(config_name)
        
        logger.info(f"Validating configuration: {config_name}")
        
        try:
            # Simple validation: check if all required keys are present
            for key, value in schema.items():
                if key not in config:
                    logger.error(f"Missing required key in configuration: {key}")
                    return False
                
                # If value is a dictionary, recursively validate
                if isinstance(value, dict) and isinstance(config[key], dict):
                    for subkey in value:
                        if subkey not in config[key]:
                            logger.error(f"Missing required subkey in configuration: {key}.{subkey}")
                            return False
            
            logger.info(f"Configuration validated successfully: {config_name}")
            return True
        
        except Exception as e:
            logger.exception(f"Error validating configuration: {e}")
            return False
    
    def merge_configs(self, *config_names: str) -> Dict:
        """
        Merge multiple configurations.
        
        Args:
            *config_names: Names of the configurations to merge
            
        Returns:
            Dict: Merged configuration dictionary
        """
        merged_config = {}
        
        logger.info(f"Merging configurations: {config_names}")
        
        try:
            for config_name in config_names:
                config = self.get_config(config_name)
                merged_config.update(config)
            
            logger.info(f"Configurations merged successfully: {config_names}")
            return merged_config
        
        except Exception as e:
            logger.exception(f"Error merging configurations: {e}")
            return {}

# Create a singleton instance
config_manager = ConfigManager()

def get_config_manager() -> ConfigManager:
    """
    Get the singleton instance of the Configuration Manager.
    
    Returns:
        ConfigManager: Singleton instance
    """
    return config_manager

def load_config(config_name: str) -> Dict:
    """
    Load a configuration file.
    
    Args:
        config_name (str): Name of the configuration file (without extension)
        
    Returns:
        Dict: Configuration dictionary
    """
    return config_manager.load_config(config_name)

def get_config(config_name: str) -> Dict:
    """
    Get a configuration.
    
    Args:
        config_name (str): Name of the configuration
        
    Returns:
        Dict: Configuration dictionary
    """
    return config_manager.get_config(config_name)

def save_config(config_name: str, config: Dict) -> bool:
    """
    Save a configuration.
    
    Args:
        config_name (str): Name of the configuration
        config (Dict): Configuration dictionary
        
    Returns:
        bool: True if successful, False otherwise
    """
    return config_manager.save_config(config_name, config)

def validate_config(config_name: str, schema: Dict) -> bool:
    """
    Validate a configuration against a schema.
    
    Args:
        config_name (str): Name of the configuration
        schema (Dict): Schema dictionary
        
    Returns:
        bool: True if valid, False otherwise
    """
    return config_manager.validate_config(config_name, schema)

def merge_configs(*config_names: str) -> Dict:
    """
    Merge multiple configurations.
    
    Args:
        *config_names: Names of the configurations to merge
        
    Returns:
        Dict: Merged configuration dictionary
    """
    return config_manager.merge_configs(*config_names)
