@echo off
echo Send Email to Alyssa - Simple Version
echo ===================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Check if yagmail is installed
python -c "import yagmail" >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing yagmail...
    pip install yagmail keyring
)

REM Ask for Gmail credentials
echo.
echo Enter Gmail email address (default: <EMAIL>):
set /p EMAIL=""
if "%EMAIL%"=="" set EMAIL=<EMAIL>

echo.
echo Enter Gmail app password:
echo Note: You need to generate an app password at https://myaccount.google.com/apppasswords
set /p PASSWORD=""

if "%PASSWORD%"=="" (
    echo No password provided. Exiting.
    exit /b 1
)

REM Ask for recipient
echo.
echo Enter recipient email address (default: <EMAIL>):
set /p RECIPIENT=""
if "%RECIPIENT%"=="" set RECIPIENT=<EMAIL>

REM Run the script
echo.
echo Running script to send email...
echo.

set COMMAND=python send_email_to_alyssa_simple.py --email "%EMAIL%" --password "%PASSWORD%" --recipient "%RECIPIENT%" --debug

echo Executing: %COMMAND%
echo.

%COMMAND%

echo.
if %errorlevel% equ 0 (
    echo Email sent successfully!
) else (
    echo Failed to send email. Please check the error messages above.
)

echo.
pause
