"""
UI-TARS Connector for the Multi-Agent AI System.

This module provides a connector to interface with UI-TARS 1.5,
allowing the system to control browsers and desktop applications.
"""
import os
import sys
import json
import asyncio
import logging
import subprocess
import requests
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
import platform

from core.logger import setup_logger

# Set up logger
logger = setup_logger("ui_tars_connector")

class UITarsConnector:
    """
    Connector for UI-TARS 1.5.

    This class provides methods to interface with UI-TARS 1.5,
    allowing the system to control browsers and desktop applications.
    """

    def __init__(self,
                 api_url: Optional[str] = None,
                 api_key: Optional[str] = None,
                 model_name: Optional[str] = None,
                 installation_path: Optional[str] = None):
        """
        Initialize the UI-TARS connector.

        Args:
            api_url (Optional[str]): URL of the UI-TARS API
            api_key (Optional[str]): API key for UI-TARS
            model_name (Optional[str]): Name of the model to use
            installation_path (Optional[str]): Path to UI-TARS installation
        """
        self.api_url = api_url
        self.api_key = api_key
        self.model_name = model_name or "UI-TARS-1.5-7B"
        self.installation_path = installation_path
        self.session = None
        self.process = None
        self.is_running = False
        self.os_type = platform.system()  # 'Windows', 'Darwin' (macOS), or 'Linux'

        # Find installation path if not provided
        if not self.installation_path:
            self._find_installation_path()

    def _find_installation_path(self):
        """Find the UI-TARS installation path based on the operating system."""
        if self.os_type == "Windows":
            # Common installation locations on Windows
            possible_paths = [
                os.path.join(os.environ.get("PROGRAMFILES", "C:\\Program Files"), "UI-TARS"),
                os.path.join(os.environ.get("PROGRAMFILES(X86)", "C:\\Program Files (x86)"), "UI-TARS"),
                os.path.join(os.environ.get("LOCALAPPDATA", "C:\\Users\\<USER>\\AppData\\Local".format(os.getlogin())), "UI-TARS"),
            ]
        elif self.os_type == "Darwin":  # macOS
            # Common installation locations on macOS
            possible_paths = [
                "/Applications/UI-TARS.app",
                os.path.expanduser("~/Applications/UI-TARS.app"),
            ]
        else:  # Linux
            # Common installation locations on Linux
            possible_paths = [
                "/usr/local/bin/ui-tars",
                "/usr/bin/ui-tars",
                os.path.expanduser("~/.local/bin/ui-tars"),
            ]

        # Check if any of the paths exist
        for path in possible_paths:
            if os.path.exists(path):
                self.installation_path = path
                logger.info(f"Found UI-TARS installation at: {path}")
                return

        logger.warning("Could not find UI-TARS installation path")

    async def initialize(self):
        """Initialize the UI-TARS connector."""
        logger.info("Initializing UI-TARS connector")

        # Create a session for API requests
        self.session = requests.Session()
        if self.api_key:
            self.session.headers.update({"Authorization": f"Bearer {self.api_key}"})

        # Check if UI-TARS is installed
        if not self.installation_path or not os.path.exists(self.installation_path):
            logger.warning("UI-TARS is not installed or installation path is incorrect")
            return False

        logger.info("UI-TARS connector initialized")
        return True

    async def start(self):
        """Start the UI-TARS application."""
        if self.is_running:
            logger.info("UI-TARS is already running")
            return True

        logger.info("Starting UI-TARS application")

        try:
            # Start UI-TARS based on the operating system
            if self.os_type == "Windows":
                # Try different executable names
                possible_executables = [
                    os.path.join(self.installation_path, "UI-TARS.exe"),
                    os.path.join(self.installation_path, "UI.TARS.exe"),
                    os.path.join(self.installation_path, "UITARS.exe"),
                    os.path.join(self.installation_path, "UI_TARS.exe")
                ]

                executable = None
                for exe in possible_executables:
                    if os.path.exists(exe):
                        executable = exe
                        break

                if not executable:
                    raise FileNotFoundError(f"UI-TARS executable not found in {self.installation_path}")

                logger.info(f"Starting UI-TARS with executable: {executable}")
                self.process = subprocess.Popen([executable])
            elif self.os_type == "Darwin":  # macOS
                self.process = subprocess.Popen(["open", self.installation_path])
            else:  # Linux
                self.process = subprocess.Popen([self.installation_path])

            # Wait for the application to start
            await asyncio.sleep(5)

            self.is_running = True
            logger.info("UI-TARS application started")
            return True

        except Exception as e:
            logger.exception(f"Error starting UI-TARS: {e}")
            return False

    async def stop(self):
        """Stop the UI-TARS application."""
        if not self.is_running:
            logger.info("UI-TARS is not running")
            return True

        logger.info("Stopping UI-TARS application")

        try:
            if self.process:
                self.process.terminate()
                await asyncio.sleep(2)

                # Force kill if still running
                if self.process.poll() is None:
                    self.process.kill()

                self.process = None

            self.is_running = False
            logger.info("UI-TARS application stopped")
            return True

        except Exception as e:
            logger.exception(f"Error stopping UI-TARS: {e}")
            return False

    async def execute_command(self, command: str, screenshot: bool = True) -> Dict:
        """
        Execute a command in UI-TARS.

        Args:
            command (str): Command to execute
            screenshot (bool): Whether to include a screenshot in the response

        Returns:
            Dict: Response from UI-TARS
        """
        if not self.is_running:
            logger.warning("UI-TARS is not running")
            return {"error": "UI-TARS is not running"}

        if not self.api_url:
            logger.warning("API URL is not set")
            return {"error": "API URL is not set"}

        logger.info(f"Executing command: {command}")

        try:
            # Prepare the request
            data = {
                "command": command,
                "screenshot": screenshot,
                "model": self.model_name
            }

            # Send the request
            response = self.session.post(f"{self.api_url}/execute", json=data)
            response.raise_for_status()

            # Parse the response
            result = response.json()

            logger.info(f"Command executed successfully")
            return result

        except Exception as e:
            logger.exception(f"Error executing command: {e}")
            return {"error": str(e)}

    async def take_screenshot(self) -> Dict:
        """
        Take a screenshot using UI-TARS.

        Returns:
            Dict: Response containing the screenshot
        """
        if not self.is_running:
            logger.warning("UI-TARS is not running")
            return {"error": "UI-TARS is not running"}

        if not self.api_url:
            logger.warning("API URL is not set")
            return {"error": "API URL is not set"}

        logger.info("Taking screenshot")

        try:
            # Send the request
            response = self.session.get(f"{self.api_url}/screenshot")
            response.raise_for_status()

            # Parse the response
            result = response.json()

            logger.info("Screenshot taken successfully")
            return result

        except Exception as e:
            logger.exception(f"Error taking screenshot: {e}")
            return {"error": str(e)}

    async def close(self):
        """Close the UI-TARS connector."""
        logger.info("Closing UI-TARS connector")

        # Stop UI-TARS if running
        if self.is_running:
            await self.stop()

        # Close the session
        if self.session:
            self.session.close()
            self.session = None

        logger.info("UI-TARS connector closed")
