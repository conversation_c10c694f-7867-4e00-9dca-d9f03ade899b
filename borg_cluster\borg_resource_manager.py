"""
Borg Resource Manager for the Multi-Agent AI System.

This module provides a centralized resource manager inspired by Google's Borg system,
allowing for efficient resource allocation and management across multiple agents and servers.
"""
import asyncio
import json
import logging
import os
from typing import Dict, List, Optional, Any, Union, Set, Tuple
import uuid
from datetime import datetime, timedelta
import psutil
import numpy as np

# Add the project root to the Python path
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

from core.logger import setup_logger
from core.state_manager import StateManager

# Set up logger
logger = setup_logger("borg_resource_manager")

class ResourceType:
    """Resource types supported by the Borg Resource Manager."""
    CPU = "cpu"
    MEMORY = "memory"
    GPU = "gpu"
    MPC_SERVER = "mpc_server"
    NETWORK = "network"
    STORAGE = "storage"

class ResourceAllocation:
    """Resource allocation information."""
    def __init__(
        self,
        allocation_id: str,
        resource_type: str,
        resource_id: str,
        agent_id: str,
        amount: float,
        priority: int,
        expiration: Optional[datetime] = None,
    ):
        self.allocation_id = allocation_id
        self.resource_type = resource_type
        self.resource_id = resource_id
        self.agent_id = agent_id
        self.amount = amount
        self.priority = priority
        self.expiration = expiration
        self.created_at = datetime.now()

class BorgResourceManager:
    """
    Centralized resource manager inspired by Google's Borg system.

    This class manages resources across the entire system, including:
    - CPU allocation
    - Memory allocation
    - GPU allocation
    - MPC server allocation
    - Network bandwidth allocation
    - Storage allocation

    It provides efficient resource allocation based on priority and requirements,
    and monitors resource usage to optimize allocation.
    """

    def __init__(self, state_manager: StateManager):
        """
        Initialize the Borg Resource Manager.

        Args:
            state_manager (StateManager): System state manager
        """
        self.state_manager = state_manager

        # Resource tracking
        self.resources = {
            ResourceType.CPU: {},
            ResourceType.MEMORY: {},
            ResourceType.GPU: {},
            ResourceType.MPC_SERVER: {},
            ResourceType.NETWORK: {},
            ResourceType.STORAGE: {},
        }

        # Resource allocations
        self.allocations = {}

        # Resource usage history
        self.usage_history = {}

        # Resource optimization parameters
        self.optimization_interval = 60  # seconds

        # Background tasks
        self.monitor_task = None
        self.optimize_task = None

        logger.info("Borg Resource Manager initialized")

    async def initialize(self):
        """Initialize the Borg Resource Manager."""
        try:
            # Load existing state if available
            borg_state = await self.state_manager.get_state("borg", "resource_manager")
            if borg_state:
                # Restore resources
                if "resources" in borg_state:
                    self.resources = borg_state["resources"]

                # Restore allocations
                if "allocations" in borg_state:
                    self.allocations = borg_state["allocations"]

                logger.info("Restored Borg Resource Manager state")

            # Discover local resources
            await self._discover_local_resources()

            # Start monitoring task
            self.monitor_task = asyncio.create_task(self._monitor_resources())

            # Start optimization task
            self.optimize_task = asyncio.create_task(self._optimize_resources())

            logger.info("Borg Resource Manager initialized successfully")

        except Exception as e:
            logger.exception(f"Error initializing Borg Resource Manager: {e}")
            raise

    async def _discover_local_resources(self):
        """Discover local resources."""
        try:
            # CPU resources
            cpu_count = psutil.cpu_count(logical=True)
            cpu_physical_count = psutil.cpu_count(logical=False)

            self.resources[ResourceType.CPU]["local"] = {
                "id": "local",
                "total": cpu_count,
                "physical_cores": cpu_physical_count,
                "logical_cores": cpu_count,
                "available": cpu_count,
                "allocated": 0,
            }

            # Memory resources
            memory = psutil.virtual_memory()

            self.resources[ResourceType.MEMORY]["local"] = {
                "id": "local",
                "total": memory.total,
                "available": memory.available,
                "allocated": 0,
            }

            # GPU resources (if available)
            try:
                import torch
                if torch.cuda.is_available():
                    gpu_count = torch.cuda.device_count()

                    for i in range(gpu_count):
                        gpu_id = f"cuda:{i}"
                        gpu_name = torch.cuda.get_device_name(i)
                        gpu_memory = torch.cuda.get_device_properties(i).total_memory

                        self.resources[ResourceType.GPU][gpu_id] = {
                            "id": gpu_id,
                            "name": gpu_name,
                            "total": gpu_memory,
                            "available": gpu_memory,
                            "allocated": 0,
                        }

                    logger.info(f"Discovered {gpu_count} GPUs")
            except ImportError:
                logger.info("PyTorch not available, skipping GPU discovery")
            except Exception as e:
                logger.warning(f"Error discovering GPUs: {e}")

            # Network resources
            net_io = psutil.net_io_counters()

            self.resources[ResourceType.NETWORK]["local"] = {
                "id": "local",
                "bytes_sent": net_io.bytes_sent,
                "bytes_recv": net_io.bytes_recv,
                "packets_sent": net_io.packets_sent,
                "packets_recv": net_io.packets_recv,
                "available_bandwidth": 1000000000,  # Placeholder, 1 Gbps
                "allocated_bandwidth": 0,
            }

            # Storage resources
            disk = psutil.disk_usage('/')

            self.resources[ResourceType.STORAGE]["local"] = {
                "id": "local",
                "total": disk.total,
                "used": disk.used,
                "free": disk.free,
                "available": disk.free,
                "allocated": 0,
            }

            logger.info("Discovered local resources")

        except Exception as e:
            logger.exception(f"Error discovering local resources: {e}")
            raise

    async def register_resource(
        self,
        resource_type: str,
        resource_id: str,
        resource_info: Dict,
    ):
        """
        Register a resource with the Borg Resource Manager.

        Args:
            resource_type (str): Type of resource
            resource_id (str): Unique identifier for the resource
            resource_info (Dict): Resource information
        """
        if resource_type not in self.resources:
            raise ValueError(f"Invalid resource type: {resource_type}")

        self.resources[resource_type][resource_id] = resource_info

        # Save state
        await self._save_state()

        logger.info(f"Registered {resource_type} resource: {resource_id}")

    async def unregister_resource(
        self,
        resource_type: str,
        resource_id: str,
    ):
        """
        Unregister a resource from the Borg Resource Manager.

        Args:
            resource_type (str): Type of resource
            resource_id (str): Unique identifier for the resource
        """
        if resource_type not in self.resources:
            raise ValueError(f"Invalid resource type: {resource_type}")

        if resource_id not in self.resources[resource_type]:
            raise ValueError(f"Resource not found: {resource_type}/{resource_id}")

        # Check if resource has allocations
        for allocation_id, allocation in self.allocations.items():
            if allocation.resource_type == resource_type and allocation.resource_id == resource_id:
                # Release allocation
                await self.release_allocation(allocation_id)

        # Remove resource
        del self.resources[resource_type][resource_id]

        # Save state
        await self._save_state()

        logger.info(f"Unregistered {resource_type} resource: {resource_id}")

    async def allocate_resource(
        self,
        resource_type: str,
        agent_id: str,
        amount: float,
        priority: int = 0,
        resource_id: Optional[str] = None,
        expiration: Optional[datetime] = None,
    ) -> str:
        """
        Allocate a resource to an agent.

        Args:
            resource_type (str): Type of resource
            agent_id (str): Agent identifier
            amount (float): Amount of resource to allocate
            priority (int): Priority of the allocation (higher is more important)
            resource_id (Optional[str]): Specific resource to allocate, or None for any
            expiration (Optional[datetime]): When the allocation expires

        Returns:
            str: Allocation identifier
        """
        if resource_type not in self.resources:
            raise ValueError(f"Invalid resource type: {resource_type}")

        # Find suitable resource
        if resource_id is None:
            # Find resource with enough available capacity
            for rid, resource in self.resources[resource_type].items():
                if resource.get("available", 0) >= amount:
                    resource_id = rid
                    break

            if resource_id is None:
                raise ValueError(f"No available {resource_type} resource with {amount} capacity")
        else:
            # Check if specified resource exists
            if resource_id not in self.resources[resource_type]:
                raise ValueError(f"Resource not found: {resource_type}/{resource_id}")

            # Check if resource has enough available capacity
            resource = self.resources[resource_type][resource_id]
            if resource.get("available", 0) < amount:
                raise ValueError(f"Resource {resource_type}/{resource_id} does not have enough available capacity")

        # Create allocation
        allocation_id = str(uuid.uuid4())
        allocation = ResourceAllocation(
            allocation_id=allocation_id,
            resource_type=resource_type,
            resource_id=resource_id,
            agent_id=agent_id,
            amount=amount,
            priority=priority,
            expiration=expiration,
        )

        # Update resource
        resource = self.resources[resource_type][resource_id]
        resource["available"] = resource.get("available", 0) - amount
        resource["allocated"] = resource.get("allocated", 0) + amount

        # Store allocation
        self.allocations[allocation_id] = allocation

        # Save state
        await self._save_state()

        logger.info(f"Allocated {amount} of {resource_type}/{resource_id} to agent {agent_id}")

        return allocation_id

    async def release_allocation(self, allocation_id: str):
        """
        Release a resource allocation.

        Args:
            allocation_id (str): Allocation identifier
        """
        if allocation_id not in self.allocations:
            raise ValueError(f"Allocation not found: {allocation_id}")

        # Get allocation
        allocation = self.allocations[allocation_id]

        # Update resource
        resource = self.resources[allocation.resource_type][allocation.resource_id]
        resource["available"] = resource.get("available", 0) + allocation.amount
        resource["allocated"] = resource.get("allocated", 0) - allocation.amount

        # Remove allocation
        del self.allocations[allocation_id]

        # Save state
        await self._save_state()

        logger.info(f"Released allocation {allocation_id}")

    async def get_resource_status(self, resource_type: Optional[str] = None) -> Dict:
        """
        Get the status of resources.

        Args:
            resource_type (Optional[str]): Type of resource, or None for all

        Returns:
            Dict: Resource status
        """
        if resource_type is not None and resource_type not in self.resources:
            raise ValueError(f"Invalid resource type: {resource_type}")

        if resource_type is not None:
            return self.resources[resource_type]
        else:
            return self.resources

    async def get_allocations(self, agent_id: Optional[str] = None) -> Dict:
        """
        Get resource allocations.

        Args:
            agent_id (Optional[str]): Agent identifier, or None for all

        Returns:
            Dict: Resource allocations
        """
        if agent_id is None:
            return self.allocations
        else:
            return {
                allocation_id: allocation
                for allocation_id, allocation in self.allocations.items()
                if allocation.agent_id == agent_id
            }

    async def _monitor_resources(self):
        """Monitor resources and update their status."""
        while True:
            try:
                # Update local resources
                await self._update_local_resources()

                # Check for expired allocations
                now = datetime.now()
                expired_allocations = [
                    allocation_id
                    for allocation_id, allocation in self.allocations.items()
                    if allocation.expiration is not None and allocation.expiration <= now
                ]

                # Release expired allocations
                for allocation_id in expired_allocations:
                    await self.release_allocation(allocation_id)

                # Save state
                await self._save_state()

            except Exception as e:
                logger.exception(f"Error monitoring resources: {e}")

            # Sleep for a while
            await asyncio.sleep(10)

    async def _update_local_resources(self):
        """Update local resource information."""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_resource = self.resources[ResourceType.CPU]["local"]
            cpu_resource["usage_percent"] = cpu_percent

            # Memory usage
            memory = psutil.virtual_memory()
            memory_resource = self.resources[ResourceType.MEMORY]["local"]
            memory_resource["available"] = memory.available
            memory_resource["usage_percent"] = memory.percent

            # GPU usage (if available)
            try:
                import torch
                if torch.cuda.is_available():
                    for i in range(torch.cuda.device_count()):
                        gpu_id = f"cuda:{i}"
                        if gpu_id in self.resources[ResourceType.GPU]:
                            # Update GPU memory usage
                            allocated_memory = torch.cuda.memory_allocated(i)
                            reserved_memory = torch.cuda.memory_reserved(i)
                            total_memory = self.resources[ResourceType.GPU][gpu_id]["total"]

                            self.resources[ResourceType.GPU][gpu_id]["allocated_memory"] = allocated_memory
                            self.resources[ResourceType.GPU][gpu_id]["reserved_memory"] = reserved_memory
                            self.resources[ResourceType.GPU][gpu_id]["available"] = total_memory - reserved_memory
                            self.resources[ResourceType.GPU][gpu_id]["usage_percent"] = (reserved_memory / total_memory) * 100
            except ImportError:
                pass
            except Exception as e:
                logger.warning(f"Error updating GPU resources: {e}")

            # Network usage
            net_io = psutil.net_io_counters()
            network_resource = self.resources[ResourceType.NETWORK]["local"]
            network_resource["bytes_sent"] = net_io.bytes_sent
            network_resource["bytes_recv"] = net_io.bytes_recv
            network_resource["packets_sent"] = net_io.packets_sent
            network_resource["packets_recv"] = net_io.packets_recv

            # Storage usage
            disk = psutil.disk_usage('/')
            storage_resource = self.resources[ResourceType.STORAGE]["local"]
            storage_resource["used"] = disk.used
            storage_resource["free"] = disk.free
            storage_resource["available"] = disk.free
            storage_resource["usage_percent"] = disk.percent

        except Exception as e:
            logger.exception(f"Error updating local resources: {e}")

    async def _optimize_resources(self):
        """Optimize resource allocation."""
        while True:
            try:
                # Collect resource usage statistics
                await self._collect_usage_statistics()

                # Analyze resource usage patterns
                await self._analyze_usage_patterns()

                # Optimize allocations
                await self._optimize_allocations()

            except Exception as e:
                logger.exception(f"Error optimizing resources: {e}")

            # Sleep for optimization interval
            await asyncio.sleep(self.optimization_interval)

    async def _collect_usage_statistics(self):
        """Collect resource usage statistics."""
        # Implement resource usage statistics collection
        pass

    async def _analyze_usage_patterns(self):
        """Analyze resource usage patterns."""
        # Implement resource usage pattern analysis
        pass

    async def _optimize_allocations(self):
        """Optimize resource allocations."""
        # Implement resource allocation optimization
        pass

    async def _save_state(self):
        """Save the current state to the state manager."""
        try:
            # Convert resources to serializable format
            serializable_resources = {}
            for resource_type, resources in self.resources.items():
                serializable_resources[resource_type] = resources

            # Convert allocations to serializable format
            serializable_allocations = {}
            for allocation_id, allocation in self.allocations.items():
                serializable_allocations[allocation_id] = {
                    "allocation_id": allocation.allocation_id,
                    "resource_type": allocation.resource_type,
                    "resource_id": allocation.resource_id,
                    "agent_id": allocation.agent_id,
                    "amount": allocation.amount,
                    "priority": allocation.priority,
                    "expiration": allocation.expiration.isoformat() if allocation.expiration else None,
                    "created_at": allocation.created_at.isoformat(),
                }

            # Create state
            state = {
                "resources": serializable_resources,
                "allocations": serializable_allocations,
                "last_updated": datetime.now().isoformat(),
            }

            # Save state
            await self.state_manager.update_state("borg", "resource_manager", state)

        except Exception as e:
            logger.exception(f"Error saving Borg Resource Manager state: {e}")

    async def close(self):
        """Close the Borg Resource Manager."""
        try:
            # Cancel background tasks
            if self.monitor_task:
                self.monitor_task.cancel()
                try:
                    await self.monitor_task
                except asyncio.CancelledError:
                    pass

            if self.optimize_task:
                self.optimize_task.cancel()
                try:
                    await self.optimize_task
                except asyncio.CancelledError:
                    pass

            # Save final state
            await self._save_state()

            logger.info("Borg Resource Manager closed")

        except Exception as e:
            logger.exception(f"Error closing Borg Resource Manager: {e}")
            raise
