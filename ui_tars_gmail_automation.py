"""
UI-TARS Gmail Automation Script

This script uses UI-TARS to automate Gmail in the browser to send emails.
It demonstrates how to use UI-TARS for browser automation to interact with Gmail.
"""
import os
import sys
import asyncio
import logging
import argparse
from typing import Dict, Optional, Any

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ui_tars_gmail")

# Try to import UI-TARS connector
try:
    from ui_tars.connector.ui_tars_connector import UITarsConnector
except ImportError:
    logger.error("UI-TARS connector not found. Make sure UI-TARS is installed.")
    sys.exit(1)

class GmailUITarsAutomation:
    """Class to automate Gmail using UI-TARS."""
    
    def __init__(self, 
                 api_url: str = "http://localhost:8080",
                 api_key: Optional[str] = None,
                 model_name: str = "UI-TARS-1.5-7B",
                 installation_path: Optional[str] = None):
        """
        Initialize the Gmail UI-TARS automation.
        
        Args:
            api_url (str): URL of the UI-TARS API
            api_key (Optional[str]): API key for UI-TARS
            model_name (str): Name of the model to use
            installation_path (Optional[str]): Path to UI-TARS installation
        """
        self.api_url = api_url
        self.api_key = api_key
        self.model_name = model_name
        self.installation_path = installation_path
        self.connector = None
        self.is_initialized = False
        
    async def initialize(self) -> bool:
        """
        Initialize the UI-TARS connector.
        
        Returns:
            bool: True if initialization was successful, False otherwise
        """
        logger.info("Initializing UI-TARS connector for Gmail automation")
        
        # Create UI-TARS connector
        self.connector = UITarsConnector(
            api_url=self.api_url,
            api_key=self.api_key,
            model_name=self.model_name,
            installation_path=self.installation_path
        )
        
        # Initialize connector
        success = await self.connector.initialize()
        if not success:
            logger.error("Failed to initialize UI-TARS connector")
            return False
        
        # Start UI-TARS if not already running
        if not self.connector.is_running:
            success = await self.connector.start()
            if not success:
                logger.error("Failed to start UI-TARS")
                return False
        
        self.is_initialized = True
        logger.info("UI-TARS connector initialized successfully")
        return True
    
    async def send_email(self, 
                        email_account: str,
                        password: str,
                        to_email: str, 
                        subject: str, 
                        body: str) -> Dict[str, Any]:
        """
        Send an email using Gmail through UI-TARS browser automation.
        
        Args:
            email_account (str): Gmail account to send from
            password (str): Password for the Gmail account
            to_email (str): Recipient email address
            subject (str): Email subject
            body (str): Email body
            
        Returns:
            Dict[str, Any]: Result of the operation
        """
        if not self.is_initialized:
            logger.error("UI-TARS connector not initialized")
            return {"success": False, "error": "UI-TARS connector not initialized"}
        
        try:
            # Step 1: Navigate to Gmail
            logger.info("Navigating to Gmail")
            result = await self.connector.execute_command(
                "Navigate to https://mail.google.com"
            )
            
            # Step 2: Check if already logged in
            logger.info("Checking login status")
            result = await self.connector.execute_command(
                "Check if I'm already logged in to Gmail. If not, click 'Sign in'"
            )
            
            # Step 3: Enter email
            logger.info(f"Entering email: {email_account}")
            result = await self.connector.execute_command(
                f"Enter '{email_account}' in the email field and click Next"
            )
            
            # Step 4: Enter password
            logger.info("Entering password")
            result = await self.connector.execute_command(
                f"Enter '{password}' in the password field and click Next"
            )
            
            # Step 5: Wait for Gmail to load
            logger.info("Waiting for Gmail to load")
            await asyncio.sleep(5)
            
            # Step 6: Click Compose
            logger.info("Clicking Compose button")
            result = await self.connector.execute_command(
                "Click the Compose button"
            )
            
            # Step 7: Fill in recipient
            logger.info(f"Entering recipient: {to_email}")
            result = await self.connector.execute_command(
                f"Enter '{to_email}' in the To field"
            )
            
            # Step 8: Fill in subject
            logger.info(f"Entering subject: {subject}")
            result = await self.connector.execute_command(
                f"Enter '{subject}' in the Subject field"
            )
            
            # Step 9: Fill in body
            logger.info("Entering email body")
            result = await self.connector.execute_command(
                f"Enter '{body}' in the email body"
            )
            
            # Step 10: Send the email
            logger.info("Sending email")
            result = await self.connector.execute_command(
                "Click the Send button"
            )
            
            # Step 11: Verify the email was sent
            logger.info("Verifying email was sent")
            result = await self.connector.execute_command(
                "Check if the email was sent successfully"
            )
            
            logger.info("Email sent successfully")
            return {"success": True, "message": "Email sent successfully"}
            
        except Exception as e:
            logger.exception(f"Error sending email: {e}")
            return {"success": False, "error": str(e)}
    
    async def shutdown(self) -> None:
        """Shut down the UI-TARS connector."""
        if self.connector:
            await self.connector.stop()
        logger.info("UI-TARS connector shut down")

async def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="UI-TARS Gmail Automation")
    parser.add_argument("--email", type=str, required=True, help="Gmail account to send from")
    parser.add_argument("--password", type=str, required=True, help="Password for the Gmail account")
    parser.add_argument("--to", type=str, required=True, help="Recipient email address")
    parser.add_argument("--subject", type=str, default="Test Email from UI-TARS", help="Email subject")
    parser.add_argument("--body", type=str, default="This is a test email sent using UI-TARS browser automation.", help="Email body")
    parser.add_argument("--api-url", type=str, default="http://localhost:8080", help="URL of the UI-TARS API")
    parser.add_argument("--model", type=str, default="UI-TARS-1.5-7B", help="Name of the model to use")
    parser.add_argument("--installation-path", type=str, help="Path to UI-TARS installation")
    
    args = parser.parse_args()
    
    # Create Gmail UI-TARS automation
    gmail_automation = GmailUITarsAutomation(
        api_url=args.api_url,
        model_name=args.model,
        installation_path=args.installation_path
    )
    
    # Initialize
    initialized = await gmail_automation.initialize()
    if not initialized:
        logger.error("Failed to initialize Gmail UI-TARS automation")
        return
    
    try:
        # Send email
        result = await gmail_automation.send_email(
            email_account=args.email,
            password=args.password,
            to_email=args.to,
            subject=args.subject,
            body=args.body
        )
        
        if result["success"]:
            logger.info("Email sent successfully")
        else:
            logger.error(f"Failed to send email: {result['error']}")
    
    finally:
        # Shut down
        await gmail_automation.shutdown()

if __name__ == "__main__":
    asyncio.run(main())
