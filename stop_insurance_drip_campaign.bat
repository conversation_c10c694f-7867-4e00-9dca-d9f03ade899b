@echo off
REM Stop Insurance Drip Campaign
REM This script stops a drip campaign for an insurance client

echo.
echo ===================================
echo    Stop Insurance Drip Campaign
echo ===================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Get identifier type
echo Select identifier type:
echo 1. Campaign ID
echo 2. Client Name
echo 3. Phone Number
echo 4. Email Address
echo.

set /p IDENTIFIER_TYPE_CHOICE="Enter choice (1-4): "

if "%IDENTIFIER_TYPE_CHOICE%"=="1" (
    set IDENTIFIER_TYPE=campaign_id
    set PROMPT_TEXT=campaign ID
) else if "%IDENTIFIER_TYPE_CHOICE%"=="2" (
    set IDENTIFIER_TYPE=name
    set PROMPT_TEXT=client name
) else if "%IDENTIFIER_TYPE_CHOICE%"=="3" (
    set IDENTIFIER_TYPE=phone
    set PROMPT_TEXT=phone number
) else if "%IDENTIFIER_TYPE_CHOICE%"=="4" (
    set IDENTIFIER_TYPE=email
    set PROMPT_TEXT=email address
) else (
    echo Invalid choice.
    goto end
)

REM Get identifier
set /p IDENTIFIER="Enter %PROMPT_TEXT%: "

REM Confirm information
echo.
echo You are about to stop the drip campaign with %IDENTIFIER_TYPE% "%IDENTIFIER%".
echo.

set /p CONFIRM="Are you sure you want to stop this campaign? (y/n): "
if /i not "%CONFIRM%"=="y" (
    echo Drip campaign stop cancelled.
    goto end
)

REM Stop drip campaign
echo.
echo Stopping drip campaign...
echo.

python run_drip_campaign.py stop --identifier "%IDENTIFIER%" --identifier-type "%IDENTIFIER_TYPE%"

if %errorlevel% neq 0 (
    echo Failed to stop drip campaign.
    goto end
)

echo.
echo Drip campaign stopped successfully.
echo.

:end
echo.
pause
