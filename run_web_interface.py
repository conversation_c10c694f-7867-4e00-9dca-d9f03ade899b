"""
Script to run the web interface.
"""
import sys
import asyncio
import argparse
from pathlib import Path

from core.state_manager import StateManager
from core.agent_manager import Agent<PERSON>ana<PERSON>
from core.web_interface import start_web_interface
from core.logger import setup_logger
import config

# Set up logger
logger = setup_logger("web_interface")

async def run_web_interface(host: str = None, port: int = None, debug: bool = False):
    """
    Run the web interface.
    
    Args:
        host (str, optional): Host to bind to
        port (int, optional): Port to bind to
        debug (bool, optional): Whether to run in debug mode
    """
    logger.info("Starting web interface")
    
    # Use config values if not specified
    host = host or config.WEB_INTERFACE["host"]
    port = port or config.WEB_INTERFACE["port"]
    debug = debug or config.WEB_INTERFACE["debug"]
    
    # Initialize state manager
    state_manager = StateManager()
    await state_manager.initialize()
    
    # Create shutdown event
    shutdown_event = asyncio.Event()
    
    # Initialize agent manager
    agent_manager = AgentManager(state_manager, shutdown_event)
    
    try:
        # Start agent manager
        await agent_manager.start()
        
        # Start web interface
        logger.info(f"Web interface starting on http://{host}:{port}")
        await start_web_interface(
            agent_manager=agent_manager,
            state_manager=state_manager,
            host=host,
            port=port,
            debug=debug
        )
        
        return True
    
    except KeyboardInterrupt:
        logger.info("Interrupted by user")
        return True
    
    except Exception as e:
        logger.exception(f"Error running web interface: {e}")
        return False
    
    finally:
        # Stop agent manager
        await agent_manager.stop()
        
        # Close state manager
        await state_manager.close()

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Run the web interface")
    parser.add_argument("--host", help="Host to bind to")
    parser.add_argument("--port", type=int, help="Port to bind to")
    parser.add_argument("--debug", action="store_true", help="Run in debug mode")
    args = parser.parse_args()
    
    # Run web interface
    success = asyncio.run(run_web_interface(args.host, args.port, args.debug))
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
