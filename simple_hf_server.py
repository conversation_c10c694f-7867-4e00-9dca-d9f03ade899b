"""
Simple Hugging Face API Server for UI-TARS.

This script creates a simple API server that mimics the Hugging Face API
but returns dummy responses. This allows UI-TARS to work without requiring
a paid Hugging Face subscription.
"""
import logging
from fastapi import Fast<PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("simple_hf_server")

# Create FastAPI app
app = FastAPI(title="Simple Hugging Face API Server")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Dummy response
DUMMY_RESPONSE = {
    "generated_text": "This is a dummy response from the local Hugging Face API server. "
                     "UI-TARS is now configured to work with your local models without "
                     "requiring a paid Hugging Face subscription."
}

@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "name": "Simple Hugging Face API Server",
        "version": "1.0.0",
        "description": "A simple API server that mimics the Hugging Face API.",
        "models": ["UI-TARS-1.5-7B"],
        "status": "running"
    }

@app.post("/models/{model_name}/generate")
async def generate(model_name: str, request: Request):
    """Generate text using a dummy response."""
    body = await request.json()
    logger.info(f"Generate request for model {model_name}: {body}")
    return DUMMY_RESPONSE

@app.post("/chat/completions")
async def chat_completions(request: Request):
    """Chat completions endpoint."""
    body = await request.json()
    logger.info(f"Chat completion request: {body}")
    
    return {
        "id": "chatcmpl-local-123456",
        "object": "chat.completion",
        "created": 1683000000,
        "model": body.get("model", "UI-TARS-1.5-7B"),
        "choices": [
            {
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": DUMMY_RESPONSE["generated_text"]
                },
                "finish_reason": "stop"
            }
        ],
        "usage": {
            "prompt_tokens": 100,
            "completion_tokens": 100,
            "total_tokens": 200
        }
    }

@app.post("/models/{model_name}")
async def model_inference(model_name: str, request: Request):
    """Generic model inference endpoint."""
    body = await request.json()
    logger.info(f"Model inference request for {model_name}: {body}")
    return DUMMY_RESPONSE

@app.get("/models")
async def list_models():
    """List available models."""
    return {
        "models": [
            {
                "id": "UI-TARS-1.5-7B",
                "name": "UI-TARS-1.5-7B"
            }
        ]
    }

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "ok"}

if __name__ == "__main__":
    logger.info("Starting Simple Hugging Face API Server on http://127.0.0.1:8000")
    uvicorn.run(app, host="127.0.0.1", port=8000)
