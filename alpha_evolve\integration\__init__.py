"""
AlphaEvolve Integration Package.

This package provides integration between AlphaEvolve and other components
of the Multi-Agent AI System, including the Borg Cluster Management System,
the Jarvis Interface, and the agent system.
"""

from alpha_evolve.integration.borg_integration import BorgIntegration
from alpha_evolve.integration.jarvis_integration import JarvisAlphaEvolveCommands
from alpha_evolve.integration.agent_integration import AgentIntegration

__all__ = [
    "BorgIntegration",
    "JarvisAlphaEvolveCommands",
    "AgentIntegration",
]
