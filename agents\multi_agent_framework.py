"""
Multi-Agent Framework using LangGraph.

This module provides a framework for creating and managing multi-agent systems
using LangGraph, allowing agents to collaborate on complex tasks.
"""
import asyncio
from typing import Dict, List, Optional, Any, Callable, Union
import json
import uuid
import logging
from pathlib import Path

from core.logger import setup_logger
from llm.llm_router import LLMRouter

# Set up logger
logger = setup_logger("agents.multi_agent_framework")

class Agent:
    """
    Base class for agents in the multi-agent system.
    
    This class provides the basic functionality for agents to process
    messages and interact with other agents.
    """
    
    def __init__(
        self,
        agent_id: str,
        name: str,
        description: str,
        llm_router: LLMRouter,
        system_prompt: str = "",
    ):
        """
        Initialize the agent.
        
        Args:
            agent_id (str): Unique identifier for the agent
            name (str): Human-readable name for the agent
            description (str): Description of the agent's role
            llm_router (LLMRouter): LLM router for generating responses
            system_prompt (str): System prompt for the agent
        """
        self.agent_id = agent_id
        self.name = name
        self.description = description
        self.llm_router = llm_router
        self.system_prompt = system_prompt
        self.memory = []
    
    async def process_message(self, message: Dict) -> Dict:
        """
        Process a message and generate a response.
        
        Args:
            message (Dict): Message to process
            
        Returns:
            Dict: Response message
        """
        # Add message to memory
        self.memory.append(message)
        
        # Generate response using LLM
        prompt = self._create_prompt(message)
        
        response = await self.llm_router.generate_text(
            prompt=prompt,
            max_tokens=1000,
            temperature=0.7,
        )
        
        # Create response message
        response_message = {
            "id": str(uuid.uuid4()),
            "sender_id": self.agent_id,
            "sender_name": self.name,
            "content": response.get("text", ""),
            "timestamp": message.get("timestamp", ""),
            "in_response_to": message.get("id", ""),
        }
        
        # Add response to memory
        self.memory.append(response_message)
        
        return response_message
    
    def _create_prompt(self, message: Dict) -> str:
        """
        Create a prompt for the LLM based on the message and agent's memory.
        
        Args:
            message (Dict): Message to process
            
        Returns:
            str: Prompt for the LLM
        """
        # Create prompt with system prompt and relevant memory
        prompt = f"{self.system_prompt}\n\n"
        
        # Add relevant context from memory
        relevant_memory = self._get_relevant_memory(message)
        if relevant_memory:
            prompt += "Previous messages:\n"
            for mem in relevant_memory:
                sender_name = mem.get("sender_name", mem.get("sender_id", "Unknown"))
                content = mem.get("content", "")
                prompt += f"{sender_name}: {content}\n"
            prompt += "\n"
        
        # Add current message
        sender_name = message.get("sender_name", message.get("sender_id", "Unknown"))
        content = message.get("content", "")
        prompt += f"{sender_name}: {content}\n\n"
        
        # Add response instruction
        prompt += f"You are {self.name}. {self.description}\n"
        prompt += "Respond to the message above."
        
        return prompt
    
    def _get_relevant_memory(self, message: Dict) -> List[Dict]:
        """
        Get relevant memory for the current message.
        
        Args:
            message (Dict): Current message
            
        Returns:
            List[Dict]: Relevant memory items
        """
        # For now, just return the last few messages
        # This could be enhanced with semantic search or other techniques
        return self.memory[-10:] if len(self.memory) > 0 else []

class MultiAgentSystem:
    """
    Multi-agent system using LangGraph.
    
    This class manages a group of agents that collaborate to solve complex tasks.
    """
    
    def __init__(self, system_id: str, name: str, description: str):
        """
        Initialize the multi-agent system.
        
        Args:
            system_id (str): Unique identifier for the system
            name (str): Human-readable name for the system
            description (str): Description of the system's purpose
        """
        self.system_id = system_id
        self.name = name
        self.description = description
        self.agents = {}
        self.workflows = {}
        self.conversation_history = []
        self.llm_router = None
    
    async def initialize(self, llm_router: LLMRouter):
        """
        Initialize the multi-agent system.
        
        Args:
            llm_router (LLMRouter): LLM router for generating responses
        """
        self.llm_router = llm_router
    
    def add_agent(self, agent: Agent):
        """
        Add an agent to the system.
        
        Args:
            agent (Agent): Agent to add
        """
        self.agents[agent.agent_id] = agent
    
    def define_workflow(self, workflow_id: str, steps: List[Dict]):
        """
        Define a workflow for the multi-agent system.
        
        Args:
            workflow_id (str): Unique identifier for the workflow
            steps (List[Dict]): Steps in the workflow
        """
        self.workflows[workflow_id] = steps
    
    async def execute_workflow(self, workflow_id: str, input_data: Dict) -> Dict:
        """
        Execute a workflow.
        
        Args:
            workflow_id (str): Identifier of the workflow to execute
            input_data (Dict): Input data for the workflow
            
        Returns:
            Dict: Result of the workflow execution
        """
        if workflow_id not in self.workflows:
            raise ValueError(f"Workflow {workflow_id} not found")
        
        workflow = self.workflows[workflow_id]
        
        # Create initial message
        message = {
            "id": str(uuid.uuid4()),
            "sender_id": "user",
            "sender_name": "User",
            "content": input_data.get("query", ""),
            "timestamp": input_data.get("timestamp", ""),
            "metadata": input_data.get("metadata", {}),
        }
        
        # Add message to conversation history
        self.conversation_history.append(message)
        
        # Execute workflow steps
        result = message
        for step in workflow:
            agent_id = step.get("agent_id")
            if agent_id not in self.agents:
                raise ValueError(f"Agent {agent_id} not found")
            
            agent = self.agents[agent_id]
            result = await agent.process_message(result)
            
            # Add result to conversation history
            self.conversation_history.append(result)
        
        return result
    
    async def chat(self, message: Dict) -> Dict:
        """
        Process a chat message using the most appropriate agent.
        
        Args:
            message (Dict): Message to process
            
        Returns:
            Dict: Response message
        """
        # Add message to conversation history
        self.conversation_history.append(message)
        
        # Determine which agent should handle the message
        agent_id = await self._route_message(message)
        
        if agent_id not in self.agents:
            raise ValueError(f"Agent {agent_id} not found")
        
        agent = self.agents[agent_id]
        result = await agent.process_message(message)
        
        # Add result to conversation history
        self.conversation_history.append(result)
        
        return result
    
    async def _route_message(self, message: Dict) -> str:
        """
        Route a message to the most appropriate agent.
        
        Args:
            message (Dict): Message to route
            
        Returns:
            str: ID of the agent that should handle the message
        """
        # For now, use a simple routing strategy
        # This could be enhanced with more sophisticated routing
        
        # If the message is a response to another message, route to the same agent
        in_response_to = message.get("in_response_to", "")
        for msg in self.conversation_history:
            if msg.get("id") == in_response_to and msg.get("sender_id") in self.agents:
                return msg.get("sender_id")
        
        # Otherwise, route to the first agent
        if self.agents:
            return list(self.agents.keys())[0]
        
        raise ValueError("No agents available for routing")
