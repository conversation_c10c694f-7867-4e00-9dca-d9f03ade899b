"""
Example script for the Insurance Lead Agent.

This script demonstrates how to initialize and use the Insurance Lead Agent
to handle leads from multiple channels.
"""
import os
import sys
import json
import asyncio
import argparse
from typing import Dict, List, Optional, Any
from datetime import datetime

# Add parent directory to path to import from project
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.insurance_lead_agent import InsuranceLeadAgent
from core.state_manager import StateManager
from core.logger import setup_logger
from llm.llm_router import LLMRouter

# Set up logger
logger = setup_logger("insurance_lead_agent_example")

async def main():
    """Run the Insurance Lead Agent example."""
    parser = argparse.ArgumentParser(description="Insurance Lead Agent Example")
    parser.add_argument("--config", type=str, default="config/lead_agent_config.json",
                        help="Path to lead agent configuration file")
    args = parser.parse_args()
    
    # Load configuration
    try:
        with open(args.config, "r") as f:
            lead_config = json.load(f)
    except Exception as e:
        logger.error(f"Error loading configuration: {e}")
        return
    
    # Create state manager (in-memory for example)
    state_manager = StateManager(use_database=False)
    await state_manager.initialize()
    
    # Create LLM router
    llm_router = LLMRouter()
    await llm_router.initialize()
    
    # Create message queue and shutdown event
    message_queue = asyncio.Queue()
    shutdown_event = asyncio.Event()
    
    # Create agent configuration
    agent_config = {
        "name": "Insurance Lead Agent",
        "description": "Handles leads from multiple channels",
        "llm_provider": "anthropic",
        "lead_agent_config": lead_config
    }
    
    # Create and initialize the agent
    agent = InsuranceLeadAgent(
        agent_id="insurance_lead_agent_1",
        config=agent_config,
        state_manager=state_manager,
        message_queue=message_queue,
        shutdown_event=shutdown_event
    )
    
    # Set LLM router
    agent.llm_router = llm_router
    
    # Initialize the agent
    await agent.initialize()
    
    logger.info("Insurance Lead Agent initialized")
    
    # Simulate some leads
    await simulate_leads(agent)
    
    # Run the agent for a few cycles
    for _ in range(5):
        logger.info("Running agent cycle...")
        await agent.execute_cycle()
        await asyncio.sleep(1)
    
    logger.info("Example complete")

async def simulate_leads(agent: InsuranceLeadAgent):
    """
    Simulate some leads for the agent to handle.
    
    Args:
        agent (InsuranceLeadAgent): The agent to handle the leads
    """
    # Simulate a Facebook lead
    facebook_lead = {
        "lead_id": "facebook-12345",
        "user_handle": "John Smith",
        "message": "Hi, I'm looking for auto insurance. Can you help?"
    }
    
    # Simulate an Instagram lead
    instagram_lead = {
        "lead_id": "instagram-67890",
        "user_handle": "Sarah Johnson",
        "message": "Hello, I need home insurance for my new house."
    }
    
    # Simulate a TikTok lead
    tiktok_lead = {
        "lead_id": "tiktok-24680",
        "user_handle": "Mike Brown",
        "message": "Hey there! I'm interested in life insurance."
    }
    
    # Simulate a website lead
    website_lead = {
        "lead_id": "website-13579",
        "user_handle": "Emily Davis",
        "message": "I'm looking for health insurance options for my family.",
        "email": "<EMAIL>",
        "phone": "************"
    }
    
    # Handle the leads
    logger.info("Simulating leads...")
    
    await agent.handle_lead("facebook", facebook_lead)
    logger.info("Handled Facebook lead")
    
    await agent.handle_lead("instagram", instagram_lead)
    logger.info("Handled Instagram lead")
    
    await agent.handle_lead("tiktok", tiktok_lead)
    logger.info("Handled TikTok lead")
    
    await agent.handle_lead("website", website_lead)
    logger.info("Handled website lead")

if __name__ == "__main__":
    asyncio.run(main())
