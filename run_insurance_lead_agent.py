"""
Run the Insurance Lead Agent.

This script runs the Insurance Lead Agent to handle leads from multiple channels.
"""
import os
import sys
import json
import asyncio
import argparse
import signal
from typing import Dict, List, Optional, Any
from datetime import datetime

from agents.insurance_lead_agent import InsuranceLeadAgent
from core.state_manager import StateManager
from core.logger import setup_logger
from llm.llm_router import LLMRouter

# Set up logger
logger = setup_logger("run_insurance_lead_agent")

# Global variables
agent = None
shutdown_event = None

async def initialize_agent(config_path: str):
    """
    Initialize the Insurance Lead Agent.
    
    Args:
        config_path (str): Path to lead agent configuration file
        
    Returns:
        InsuranceLeadAgent: Initialized agent
    """
    logger.info("Initializing Insurance Lead Agent...")
    
    # Load configuration
    try:
        with open(config_path, "r") as f:
            lead_config = json.load(f)
    except Exception as e:
        logger.error(f"Error loading configuration: {e}")
        return None
    
    # Create state manager
    state_manager = StateManager()
    await state_manager.initialize()
    
    # Create LLM router
    llm_router = LLMRouter()
    await llm_router.initialize()
    
    # Create message queue and shutdown event
    message_queue = asyncio.Queue()
    global shutdown_event
    shutdown_event = asyncio.Event()
    
    # Create agent configuration
    agent_config = {
        "name": "Insurance Lead Agent",
        "description": "Handles leads from multiple channels",
        "llm_provider": "anthropic",
        "lead_agent_config": lead_config
    }
    
    # Create and initialize the agent
    global agent
    agent = InsuranceLeadAgent(
        agent_id="insurance_lead_agent_1",
        config=agent_config,
        state_manager=state_manager,
        message_queue=message_queue,
        shutdown_event=shutdown_event
    )
    
    # Set LLM router
    agent.llm_router = llm_router
    
    # Initialize the agent
    await agent.initialize()
    
    logger.info("Insurance Lead Agent initialized")
    
    return agent

async def run_agent(interval: int = 60):
    """
    Run the Insurance Lead Agent.
    
    Args:
        interval (int): Interval between agent cycles in seconds
    """
    logger.info(f"Running Insurance Lead Agent with interval {interval} seconds")
    
    global agent
    global shutdown_event
    
    if not agent:
        logger.error("Agent not initialized")
        return
    
    try:
        # Run until shutdown
        while not shutdown_event.is_set():
            # Execute agent cycle
            logger.info("Executing agent cycle...")
            await agent.execute_cycle()
            
            # Wait for next cycle
            try:
                await asyncio.wait_for(shutdown_event.wait(), timeout=interval)
            except asyncio.TimeoutError:
                pass
    
    except KeyboardInterrupt:
        logger.info("Keyboard interrupt received")
    
    except Exception as e:
        logger.exception(f"Error running agent: {e}")
    
    finally:
        # Shutdown
        logger.info("Shutting down...")
        shutdown_event.set()
        
        # Close state manager
        await agent.state_manager.close()
        
        logger.info("Shutdown complete")

def signal_handler(sig, frame):
    """
    Handle signals.
    
    Args:
        sig: Signal number
        frame: Current stack frame
    """
    logger.info(f"Signal {sig} received")
    
    global shutdown_event
    if shutdown_event:
        shutdown_event.set()

async def main():
    """Run the Insurance Lead Agent."""
    parser = argparse.ArgumentParser(description="Run the Insurance Lead Agent")
    parser.add_argument("--config", type=str, default="config/lead_agent_config.json", help="Path to lead agent configuration file")
    parser.add_argument("--interval", type=int, default=60, help="Interval between agent cycles in seconds")
    args = parser.parse_args()
    
    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Initialize agent
    agent = await initialize_agent(args.config)
    
    if not agent:
        return
    
    # Run agent
    await run_agent(args.interval)

if __name__ == "__main__":
    asyncio.run(main())
