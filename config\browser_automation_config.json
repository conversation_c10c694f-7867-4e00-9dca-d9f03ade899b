{"browser_automation": {"preferred_provider": "auto", "auto_start": true, "auto_restart": true, "auto_fallback": true, "health_check_interval": 300, "max_retries": 3, "retry_delay": 2}, "ui_tars": {"api_url": "http://localhost:8080", "api_key": "hf_dummy_key", "model_name": "UI-TARS-1.5-7B", "browser_type": "chrome", "browser_path": "", "remote_debugging_port": 9222}, "midscene": {"api_url": "http://localhost:8081", "api_key": "", "browser_type": "chrome", "browser_path": ""}, "gmail": {"email": "<EMAIL>", "password": "GodisSoGood!777", "signature": "Best regards,\nPaul <PERSON>\nFlo Faction Insurance"}, "google_voice": {"email": "<EMAIL>", "password": "GodisSoGood!777", "phone_number": "**********"}, "logging": {"level": "INFO", "file": "browser_automation.log", "max_size": 10485760, "backup_count": 5}}