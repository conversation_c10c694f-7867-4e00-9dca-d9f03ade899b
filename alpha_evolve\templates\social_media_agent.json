{
  "name": "Social Media Agent Enhancement",
  "description": "Template for enhancing social media agent capabilities",
  "template": "You are tasked with enhancing the {capability} capability of a social media agent.

The goal is to optimize for {optimization_metric}.

The social media agent handles various platforms including Facebook, Instagram, Twitter, TikTok, and LinkedIn, managing content creation, scheduling, audience analysis, and engagement.

Requirements:
1. The implementation must understand platform-specific best practices and algorithms
2. It must create engaging content that resonates with the target audience
3. It should optimize posting times and frequency for maximum engagement
4. It must analyze audience data to inform content strategy
5. It should respond to engagement opportunities promptly and appropriately

Your solution should be implemented as a Python function that follows this interface:
{interface}

Focus on creating a solution that maximizes {optimization_metric} while maintaining brand voice and audience engagement.",
  "variables": ["capability", "optimization_metric", "interface"]
}
