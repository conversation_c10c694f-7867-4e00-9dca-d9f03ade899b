"""
<PERSON><PERSON><PERSON> to run a specific agent for testing.
"""
import sys
import asyncio
import argparse
from pathlib import Path
import importlib

from core.state_manager import StateManager
from core.logger import setup_logger
from llm.llm_router import <PERSON><PERSON>outer
import config

# Set up logger
logger = setup_logger("run_agent")

async def run_agent(agent_type: str):
    """
    Run a specific agent for testing.
    
    Args:
        agent_type (str): Type of agent to run (e.g., "insurance", "trading")
    """
    logger.info(f"Running {agent_type} agent for testing")
    
    # Initialize state manager
    state_manager = StateManager()
    await state_manager.initialize()
    
    # Create shutdown event
    shutdown_event = asyncio.Event()
    
    try:
        # Import agent module
        module_name = f"agents.{agent_type}_agent"
        class_name = "".join(word.capitalize() for word in agent_type.split("_")) + "Agent"
        
        try:
            module = importlib.import_module(module_name)
            agent_class = getattr(module, class_name)
        except (ImportError, AttributeError) as e:
            logger.error(f"Failed to import agent {agent_type}: {e}")
            return False
        
        # Get agent config
        agent_config = config.AGENT_CONFIG.get(agent_type)
        if not agent_config:
            logger.error(f"No configuration found for agent type: {agent_type}")
            return False
        
        # Create message queue
        message_queue = asyncio.Queue()
        
        # Create agent instance
        agent = agent_class(
            agent_id=agent_type,
            config=agent_config,
            state_manager=state_manager,
            message_queue=message_queue,
            shutdown_event=shutdown_event
        )
        
        # Initialize and run agent
        logger.info(f"Initializing {agent_type} agent")
        await agent.initialize()
        
        # Run agent for a limited time
        logger.info(f"Running {agent_type} agent")
        agent_task = asyncio.create_task(agent.run())
        
        # Wait for user to press Ctrl+C or for 60 seconds
        try:
            await asyncio.wait_for(shutdown_event.wait(), timeout=60)
        except asyncio.TimeoutError:
            logger.info("Time limit reached, shutting down")
            shutdown_event.set()
        
        # Wait for agent to shut down
        await agent_task
        
        # Shut down agent
        logger.info(f"Shutting down {agent_type} agent")
        await agent.shutdown()
        
        return True
    
    except KeyboardInterrupt:
        logger.info("Interrupted by user")
        shutdown_event.set()
        return True
    
    except Exception as e:
        logger.exception(f"Error running agent: {e}")
        return False
    
    finally:
        # Close state manager
        await state_manager.close()

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Run a specific agent for testing")
    parser.add_argument("agent_type", help="Type of agent to run (e.g., insurance, trading)")
    args = parser.parse_args()
    
    # Validate agent type
    agent_type = args.agent_type.lower()
    if agent_type not in config.AGENT_CONFIG:
        print(f"Error: Unknown agent type: {agent_type}")
        print(f"Available agent types: {', '.join(config.AGENT_CONFIG.keys())}")
        return 1
    
    # Run agent
    success = asyncio.run(run_agent(agent_type))
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
