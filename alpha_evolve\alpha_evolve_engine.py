"""
AlphaEvolve Engine for the Multi-Agent AI System.

This module provides the core evolutionary algorithm engine for AlphaEvolve,
enabling the discovery and optimization of algorithms through evolutionary techniques
combined with large language models.
"""
import asyncio
import json
import logging
import os
import random
import time
from typing import Dict, List, Optional, Any, Union, Callable, Tuple
import uuid
from datetime import datetime
import heapq
import copy
import sys
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

from core.logger import setup_logger
from core.state_manager import StateManager
from llm.llm_router import LLMRouter
from alpha_evolve.llm_code_generator import LLMCodeGenerator
from alpha_evolve.code_evaluator import CodeEvaluator
from alpha_evolve.evolutionary_optimizer import EvolutionaryOptimizer
from alpha_evolve.prompt_engineering import PromptManager

# Set up logger
logger = setup_logger("alpha_evolve_engine")

class AlphaEvolveEngine:
    """
    AlphaEvolve Engine for the Multi-Agent AI System.

    This class provides the core evolutionary algorithm engine for AlphaEvolve,
    enabling the discovery and optimization of algorithms through evolutionary techniques
    combined with large language models.
    """

    def __init__(
        self,
        state_manager: Optional[StateManager] = None,
        llm_router: Optional[LLMRouter] = None,
        config_path: str = "config/alpha_evolve_config.json",
    ):
        """
        Initialize the AlphaEvolve Engine.

        Args:
            state_manager (StateManager, optional): State manager for persistence
            llm_router (LLMRouter, optional): LLM router for code generation
            config_path (str, optional): Path to configuration file
        """
        self.state_manager = state_manager
        self.llm_router = llm_router
        self.config_path = config_path
        self.initialized = False
        
        # Evolution components
        self.code_generator = None
        self.code_evaluator = None
        self.evolutionary_optimizer = None
        self.prompt_manager = None
        
        # Evolution state
        self.active_evolutions = {}
        self.evolution_history = {}
        self.best_solutions = {}
        
        # Performance metrics
        self.performance_metrics = {}
        
        # Configuration
        self.config = {}
        
    async def initialize(self):
        """Initialize the AlphaEvolve Engine."""
        logger.info("Initializing AlphaEvolve Engine")
        
        # Load configuration
        await self._load_config()
        
        # Initialize components
        if not self.llm_router:
            from llm.llm_router import LLMRouter
            self.llm_router = LLMRouter()
            await self.llm_router.initialize()
        
        if not self.state_manager:
            from core.state_manager import StateManager
            self.state_manager = StateManager()
            await self.state_manager.initialize()
        
        # Initialize evolution components
        self.code_generator = LLMCodeGenerator(self.llm_router, self.config.get("code_generation", {}))
        await self.code_generator.initialize()
        
        self.code_evaluator = CodeEvaluator(self.config.get("code_evaluation", {}))
        await self.code_evaluator.initialize()
        
        self.evolutionary_optimizer = EvolutionaryOptimizer(self.config.get("evolutionary_optimization", {}))
        await self.evolutionary_optimizer.initialize()
        
        self.prompt_manager = PromptManager(self.config.get("prompt_engineering", {}))
        await self.prompt_manager.initialize()
        
        # Load state
        await self._load_state()
        
        self.initialized = True
        logger.info("AlphaEvolve Engine initialized")
    
    async def _load_config(self):
        """Load configuration from file."""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, "r") as f:
                    self.config = json.load(f)
                logger.info(f"Loaded configuration from {self.config_path}")
            else:
                # Use default configuration
                self.config = {
                    "code_generation": {
                        "models": ["gemini-pro", "claude-3-sonnet"],
                        "temperature": 0.7,
                        "max_tokens": 2000,
                        "top_p": 0.95,
                    },
                    "code_evaluation": {
                        "timeout": 10,
                        "max_memory": "1GB",
                        "metrics": ["correctness", "efficiency", "complexity"],
                    },
                    "evolutionary_optimization": {
                        "population_size": 50,
                        "tournament_size": 5,
                        "crossover_rate": 0.8,
                        "mutation_rate": 0.2,
                        "elitism": 2,
                    },
                    "prompt_engineering": {
                        "templates_dir": "alpha_evolve/templates",
                    },
                }
                logger.info("Using default configuration")
        except Exception as e:
            logger.exception(f"Error loading configuration: {e}")
            # Use minimal default configuration
            self.config = {
                "code_generation": {"models": ["gemini-pro"]},
                "code_evaluation": {"timeout": 10},
                "evolutionary_optimization": {"population_size": 20},
                "prompt_engineering": {},
            }
    
    async def _load_state(self):
        """Load state from state manager."""
        if not self.state_manager:
            logger.warning("State manager not available, skipping state loading")
            return
        
        try:
            # Load evolution history
            history = await self.state_manager.get_state("alpha_evolve", "evolution_history")
            if history:
                self.evolution_history = history
                logger.info(f"Loaded {len(history)} evolution history records")
            
            # Load best solutions
            solutions = await self.state_manager.get_state("alpha_evolve", "best_solutions")
            if solutions:
                self.best_solutions = solutions
                logger.info(f"Loaded {len(solutions)} best solutions")
            
            # Load performance metrics
            metrics = await self.state_manager.get_state("alpha_evolve", "performance_metrics")
            if metrics:
                self.performance_metrics = metrics
                logger.info(f"Loaded performance metrics")
        
        except Exception as e:
            logger.exception(f"Error loading state: {e}")
    
    async def _save_state(self):
        """Save state to state manager."""
        if not self.state_manager:
            logger.warning("State manager not available, skipping state saving")
            return
        
        try:
            # Save evolution history
            await self.state_manager.update_state("alpha_evolve", "evolution_history", self.evolution_history)
            
            # Save best solutions
            await self.state_manager.update_state("alpha_evolve", "best_solutions", self.best_solutions)
            
            # Save performance metrics
            await self.state_manager.update_state("alpha_evolve", "performance_metrics", self.performance_metrics)
            
            logger.info("Saved AlphaEvolve state")
        
        except Exception as e:
            logger.exception(f"Error saving state: {e}")
    
    async def evolve(
        self,
        problem: Dict,
        population_size: int = None,
        generations: int = 100,
        fitness_threshold: float = 0.95,
        timeout: int = 3600,
        callback: Optional[Callable] = None,
    ) -> Dict:
        """
        Run an evolutionary process to discover or optimize an algorithm.
        
        Args:
            problem (Dict): Problem definition
            population_size (int, optional): Population size
            generations (int, optional): Maximum number of generations
            fitness_threshold (float, optional): Fitness threshold to stop evolution
            timeout (int, optional): Timeout in seconds
            callback (Callable, optional): Callback function for progress updates
            
        Returns:
            Dict: Evolution results
        """
        if not self.initialized:
            await self.initialize()
        
        # Create evolution ID
        evolution_id = str(uuid.uuid4())
        
        # Use default population size if not specified
        if population_size is None:
            population_size = self.config["evolutionary_optimization"]["population_size"]
        
        # Create evolution record
        evolution = {
            "id": evolution_id,
            "problem": problem,
            "parameters": {
                "population_size": population_size,
                "generations": generations,
                "fitness_threshold": fitness_threshold,
                "timeout": timeout,
            },
            "status": "running",
            "start_time": datetime.now().isoformat(),
            "end_time": None,
            "current_generation": 0,
            "best_fitness": 0.0,
            "best_solution": None,
            "population": [],
            "fitness_history": [],
        }
        
        # Store in active evolutions
        self.active_evolutions[evolution_id] = evolution
        
        try:
            # Generate initial population
            logger.info(f"Generating initial population for evolution {evolution_id}")
            population = await self._generate_initial_population(problem, population_size)
            evolution["population"] = population
            
            # Evaluate initial population
            logger.info(f"Evaluating initial population for evolution {evolution_id}")
            fitness_scores = await self._evaluate_population(population, problem)
            
            # Main evolution loop
            start_time = time.time()
            for generation in range(generations):
                # Check timeout
                if time.time() - start_time > timeout:
                    logger.info(f"Evolution {evolution_id} timed out after {generation} generations")
                    evolution["status"] = "timeout"
                    break
                
                # Update evolution record
                evolution["current_generation"] = generation
                
                # Find best solution in current population
                best_idx = fitness_scores.index(max(fitness_scores))
                best_solution = population[best_idx]
                best_fitness = fitness_scores[best_idx]
                
                # Update best solution if improved
                if best_fitness > evolution["best_fitness"]:
                    evolution["best_fitness"] = best_fitness
                    evolution["best_solution"] = best_solution
                    logger.info(f"New best solution found in generation {generation} with fitness {best_fitness}")
                
                # Record fitness history
                evolution["fitness_history"].append({
                    "generation": generation,
                    "best_fitness": best_fitness,
                    "avg_fitness": sum(fitness_scores) / len(fitness_scores),
                    "min_fitness": min(fitness_scores),
                })
                
                # Call callback if provided
                if callback:
                    await callback(evolution)
                
                # Check if fitness threshold reached
                if best_fitness >= fitness_threshold:
                    logger.info(f"Evolution {evolution_id} reached fitness threshold {fitness_threshold}")
                    evolution["status"] = "completed"
                    break
                
                # Create next generation
                logger.info(f"Creating generation {generation + 1} for evolution {evolution_id}")
                population, fitness_scores = await self._create_next_generation(
                    population, fitness_scores, problem
                )
                evolution["population"] = population
            
            # Final evaluation
            if evolution["status"] == "running":
                evolution["status"] = "completed"
            
            # Record end time
            evolution["end_time"] = datetime.now().isoformat()
            
            # Store in evolution history
            self.evolution_history[evolution_id] = evolution
            
            # Store best solution if good enough
            if evolution["best_solution"] and evolution["best_fitness"] > 0.7:
                problem_type = problem.get("type", "unknown")
                if problem_type not in self.best_solutions:
                    self.best_solutions[problem_type] = []
                
                self.best_solutions[problem_type].append({
                    "evolution_id": evolution_id,
                    "solution": evolution["best_solution"],
                    "fitness": evolution["best_fitness"],
                    "timestamp": datetime.now().isoformat(),
                })
                
                # Sort and keep only top 10 solutions
                self.best_solutions[problem_type].sort(key=lambda x: x["fitness"], reverse=True)
                self.best_solutions[problem_type] = self.best_solutions[problem_type][:10]
            
            # Save state
            await self._save_state()
            
            # Remove from active evolutions
            del self.active_evolutions[evolution_id]
            
            return {
                "evolution_id": evolution_id,
                "status": evolution["status"],
                "generations": evolution["current_generation"],
                "best_fitness": evolution["best_fitness"],
                "best_solution": evolution["best_solution"],
                "fitness_history": evolution["fitness_history"],
                "elapsed_time": time.time() - start_time,
            }
        
        except Exception as e:
            logger.exception(f"Error in evolution {evolution_id}: {e}")
            evolution["status"] = "error"
            evolution["error"] = str(e)
            evolution["end_time"] = datetime.now().isoformat()
            
            # Store in evolution history
            self.evolution_history[evolution_id] = evolution
            
            # Save state
            await self._save_state()
            
            # Remove from active evolutions
            del self.active_evolutions[evolution_id]
            
            return {
                "evolution_id": evolution_id,
                "status": "error",
                "error": str(e),
            }
    
    async def _generate_initial_population(self, problem: Dict, population_size: int) -> List[Dict]:
        """
        Generate initial population for evolution.
        
        Args:
            problem (Dict): Problem definition
            population_size (int): Population size
            
        Returns:
            List[Dict]: Initial population
        """
        # Generate prompts for the problem
        prompts = await self.prompt_manager.generate_prompts(problem, population_size)
        
        # Generate code for each prompt
        population = []
        for i, prompt in enumerate(prompts):
            try:
                code = await self.code_generator.generate_code(prompt)
                
                solution = {
                    "id": str(uuid.uuid4()),
                    "code": code,
                    "prompt": prompt,
                    "generation": 0,
                    "parent_ids": [],
                    "metadata": {},
                }
                
                population.append(solution)
                logger.debug(f"Generated solution {i+1}/{population_size}")
            
            except Exception as e:
                logger.error(f"Error generating solution {i+1}/{population_size}: {e}")
        
        # If we couldn't generate enough solutions, duplicate existing ones
        while len(population) < population_size:
            if not population:
                # If no solutions were generated, create a dummy solution
                population.append({
                    "id": str(uuid.uuid4()),
                    "code": "# Placeholder solution\ndef solve(input_data):\n    return input_data",
                    "prompt": "Generate a placeholder solution",
                    "generation": 0,
                    "parent_ids": [],
                    "metadata": {"placeholder": True},
                })
            else:
                # Duplicate a random solution
                solution = copy.deepcopy(random.choice(population))
                solution["id"] = str(uuid.uuid4())
                population.append(solution)
        
        return population
    
    async def _evaluate_population(self, population: List[Dict], problem: Dict) -> List[float]:
        """
        Evaluate population fitness.
        
        Args:
            population (List[Dict]): Population to evaluate
            problem (Dict): Problem definition
            
        Returns:
            List[float]: Fitness scores
        """
        fitness_scores = []
        
        for solution in population:
            try:
                fitness = await self.code_evaluator.evaluate_code(solution["code"], problem)
                fitness_scores.append(fitness)
            except Exception as e:
                logger.error(f"Error evaluating solution {solution['id']}: {e}")
                fitness_scores.append(0.0)
        
        return fitness_scores
    
    async def _create_next_generation(
        self, 
        population: List[Dict], 
        fitness_scores: List[float],
        problem: Dict
    ) -> Tuple[List[Dict], List[float]]:
        """
        Create next generation through selection, crossover, and mutation.
        
        Args:
            population (List[Dict]): Current population
            fitness_scores (List[float]): Fitness scores for current population
            problem (Dict): Problem definition
            
        Returns:
            Tuple[List[Dict], List[float]]: New population and fitness scores
        """
        # Use evolutionary optimizer to create next generation
        new_population = await self.evolutionary_optimizer.create_next_generation(
            population, fitness_scores, self.code_generator, problem
        )
        
        # Evaluate new population
        new_fitness_scores = await self._evaluate_population(new_population, problem)
        
        return new_population, new_fitness_scores
    
    async def get_evolution_status(self, evolution_id: str) -> Dict:
        """
        Get status of an evolution.
        
        Args:
            evolution_id (str): Evolution ID
            
        Returns:
            Dict: Evolution status
        """
        # Check active evolutions
        if evolution_id in self.active_evolutions:
            return self.active_evolutions[evolution_id]
        
        # Check evolution history
        if evolution_id in self.evolution_history:
            return self.evolution_history[evolution_id]
        
        return {"status": "not_found"}
    
    async def get_best_solution(self, problem_type: str) -> Dict:
        """
        Get best solution for a problem type.
        
        Args:
            problem_type (str): Problem type
            
        Returns:
            Dict: Best solution
        """
        if problem_type in self.best_solutions and self.best_solutions[problem_type]:
            return self.best_solutions[problem_type][0]
        
        return {"status": "not_found"}
    
    async def optimize_existing_code(
        self,
        code: str,
        problem: Dict,
        generations: int = 50,
        population_size: int = 20,
        fitness_threshold: float = 0.95,
    ) -> Dict:
        """
        Optimize existing code.
        
        Args:
            code (str): Existing code to optimize
            problem (Dict): Problem definition
            generations (int, optional): Maximum number of generations
            population_size (int, optional): Population size
            fitness_threshold (float, optional): Fitness threshold to stop evolution
            
        Returns:
            Dict: Optimization results
        """
        # Create initial solution
        initial_solution = {
            "id": str(uuid.uuid4()),
            "code": code,
            "prompt": "Optimize existing code",
            "generation": 0,
            "parent_ids": [],
            "metadata": {"original": True},
        }
        
        # Create problem with optimization focus
        optimization_problem = copy.deepcopy(problem)
        optimization_problem["original_code"] = code
        optimization_problem["optimization_focus"] = True
        
        # Generate initial population based on the existing code
        population = [initial_solution]
        
        # Generate variations of the existing code
        for i in range(population_size - 1):
            try:
                # Create prompt for optimization
                prompt = await self.prompt_manager.generate_optimization_prompt(
                    code, problem, variation_index=i
                )
                
                # Generate optimized code
                optimized_code = await self.code_generator.generate_code(prompt)
                
                solution = {
                    "id": str(uuid.uuid4()),
                    "code": optimized_code,
                    "prompt": prompt,
                    "generation": 0,
                    "parent_ids": [initial_solution["id"]],
                    "metadata": {"variation": i},
                }
                
                population.append(solution)
            
            except Exception as e:
                logger.error(f"Error generating optimization variation {i}: {e}")
                # Duplicate the initial solution with small random changes
                solution = copy.deepcopy(initial_solution)
                solution["id"] = str(uuid.uuid4())
                solution["metadata"] = {"variation": i, "error": str(e)}
                population.append(solution)
        
        # Run evolution with the initial population
        return await self.evolve(
            problem=optimization_problem,
            population_size=population_size,
            generations=generations,
            fitness_threshold=fitness_threshold,
        )
    
    async def enhance_agent_capability(
        self,
        agent_id: str,
        capability: str,
        optimization_metric: str,
        code_to_enhance: Optional[str] = None,
    ) -> Dict:
        """
        Enhance an agent's capability.
        
        Args:
            agent_id (str): Agent ID
            capability (str): Capability to enhance
            optimization_metric (str): Metric to optimize
            code_to_enhance (str, optional): Existing code to enhance
            
        Returns:
            Dict: Enhancement results
        """
        # Create problem definition for agent enhancement
        problem = {
            "type": "agent_enhancement",
            "agent_id": agent_id,
            "capability": capability,
            "optimization_metric": optimization_metric,
        }
        
        if code_to_enhance:
            # Optimize existing code
            return await self.optimize_existing_code(
                code=code_to_enhance,
                problem=problem,
            )
        else:
            # Discover new implementation
            return await self.evolve(problem=problem)
    
    async def shutdown(self):
        """Shutdown the AlphaEvolve Engine."""
        logger.info("Shutting down AlphaEvolve Engine")
        
        # Save state
        await self._save_state()
        
        # Shutdown components
        if self.code_evaluator:
            await self.code_evaluator.shutdown()
        
        if self.evolutionary_optimizer:
            await self.evolutionary_optimizer.shutdown()
        
        logger.info("AlphaEvolve Engine shut down")
