@echo off
echo UI-TARS Gmail Integration
echo =======================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Install required packages if not already installed
echo Checking and installing required packages...
pip install -r email_automation_requirements.txt
if %errorlevel% neq 0 (
    echo Failed to install required packages. Please check your internet connection.
    exit /b 1
)

REM Ask for browser
echo.
echo Choose a browser:
echo 1. Chrome (recommended)
echo 2. Firefox
echo 3. Edge
echo.
set /p BROWSER_CHOICE="Enter your choice (1-3, default: 1): "

if "%BROWSER_CHOICE%"=="2" (
    set BROWSER=firefox
) else if "%BROWSER_CHOICE%"=="3" (
    set BROWSER=edge
) else (
    set BROWSER=chrome
)

REM Ask for UI-TARS path
echo.
echo Enter the path to UI-TARS installation (leave empty if not installed):
set /p UI_TARS_PATH=""

REM Get email details
echo.
set FROM_EMAIL=<EMAIL>
set TO_EMAIL=<EMAIL>
set /p SUBJECT="Enter subject (default: Test Email from AI Agent System with UI-TARS): "
if "%SUBJECT%"=="" set SUBJECT=Test Email from AI Agent System with UI-TARS
echo.
echo Enter body (type your message and press Enter twice when done):
echo -------------------------------------------------------------
set BODY=
:body_loop
set /p LINE=""
if "%LINE%"=="" goto body_done
set BODY=%BODY%%LINE%^

goto body_loop
:body_done

if "%BODY%"=="" set BODY=This is a test email sent using browser automation with UI-TARS integration.

REM Run the UI-TARS Gmail Integration
echo.
echo Sending email from %FROM_EMAIL% to %TO_EMAIL% using %BROWSER% with UI-TARS...
echo.

if "%UI_TARS_PATH%"=="" (
    python ui_tars_gmail_integration.py --email "%FROM_EMAIL%" --to "%TO_EMAIL%" --subject "%SUBJECT%" --body "%BODY%" --browser "%BROWSER%"
) else (
    python ui_tars_gmail_integration.py --email "%FROM_EMAIL%" --to "%TO_EMAIL%" --subject "%SUBJECT%" --body "%BODY%" --browser "%BROWSER%" --ui-tars-path "%UI_TARS_PATH%"
)

echo.
if %errorlevel% equ 0 (
    echo Email sent successfully!
) else (
    echo There was an issue sending the email. Please check the logs.
)
echo.
pause
