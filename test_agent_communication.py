"""
Script to test communication between agents.
"""
import sys
import asyncio
import argparse
from pathlib import Path
import json

from core.state_manager import StateManager
from core.agent_manager import Agent<PERSON>anager
from core.logger import setup_logger
import config

# Set up logger
logger = setup_logger("test_agent_communication")

async def test_agent_communication(sender: str, recipient: str, message_type: str, content: dict):
    """
    Test communication between agents.
    
    Args:
        sender (str): Sender agent ID
        recipient (str): Recipient agent ID
        message_type (str): Message type
        content (dict): Message content
    """
    logger.info(f"Testing communication from {sender} to {recipient}")
    
    # Initialize state manager
    state_manager = StateManager()
    await state_manager.initialize()
    
    # Create shutdown event
    shutdown_event = asyncio.Event()
    
    # Initialize agent manager
    agent_manager = AgentManager(state_manager, shutdown_event)
    
    try:
        # Start agent manager
        await agent_manager.start()
        
        # Check if agents exist
        if sender not in agent_manager.agents:
            logger.error(f"Sender agent {sender} not found")
            logger.info(f"Available agents: {', '.join(agent_manager.agents.keys())}")
            return False
        
        if recipient not in agent_manager.agents:
            logger.error(f"Recipient agent {recipient} not found")
            logger.info(f"Available agents: {', '.join(agent_manager.agents.keys())}")
            return False
        
        # Create message
        message = {
            "sender_id": sender,
            "recipient_id": recipient,
            "type": message_type,
            "content": content,
            "timestamp": asyncio.get_event_loop().time(),
        }
        
        # Send message
        logger.info(f"Sending message: {message}")
        await agent_manager.send_message(message)
        
        # Wait for a moment to allow message processing
        logger.info("Waiting for message processing...")
        await asyncio.sleep(5)
        
        # Check if message was processed
        # In a real implementation, we would check for a response or other evidence
        logger.info("Message sent successfully")
        
        return True
    
    finally:
        # Stop agent manager
        await agent_manager.stop()
        
        # Close state manager
        await state_manager.close()

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Test communication between agents")
    parser.add_argument("sender", help="Sender agent ID")
    parser.add_argument("recipient", help="Recipient agent ID")
    parser.add_argument("--type", default="command", help="Message type")
    parser.add_argument("--content", default="{}", help="Message content (JSON)")
    args = parser.parse_args()
    
    # Parse content
    try:
        content = json.loads(args.content)
    except json.JSONDecodeError:
        print(f"Error: Invalid JSON content: {args.content}")
        return 1
    
    # Run test
    success = asyncio.run(test_agent_communication(args.sender, args.recipient, args.type, content))
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
