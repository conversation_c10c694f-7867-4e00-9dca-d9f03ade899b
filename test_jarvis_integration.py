"""
Test Jarvis Integration for Browser Automation.

This script tests the integration between <PERSON> and the browser automation manager
with UI-TARS and Midscene.
"""
import os
import sys
import json
import asyncio
import logging
import time
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent))

try:
    from core.logger import setup_logger
    from jarvis.browser_automation_integration import BrowserAutomationIntegration
except ImportError:
    # Fallback logging setup if core.logger is not available
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler("test_jarvis_integration.log")
        ]
    )

    def setup_logger(name):
        return logging.getLogger(name)

# Set up logger
logger = setup_logger("test_jarvis_integration")

async def load_config():
    """
    Load configuration from file.
    
    Returns:
        dict: Configuration dictionary
    """
    config_path = Path("config.json")
    if not config_path.exists():
        logger.error(f"Configuration file not found: {config_path}")
        return {}
    
    try:
        import json
        with open(config_path, "r") as f:
            config = json.load(f)
        
        logger.info(f"Configuration loaded from {config_path}")
        return config
    except Exception as e:
        logger.error(f"Error loading configuration: {e}")
        return {}

async def test_jarvis_integration():
    """
    Test the Jarvis integration for browser automation.
    
    Returns:
        bool: True if successful, False otherwise
    """
    print("\nTesting Jarvis Integration for Browser Automation")
    print("===============================================")
    
    # Load configuration
    config = await load_config()
    
    # Create browser automation integration
    integration = BrowserAutomationIntegration(config=config)
    
    try:
        # Initialize integration
        print("\nInitializing integration...")
        success = await integration.initialize()
        if not success:
            print("❌ Failed to initialize integration")
            return False
        
        print("✅ Integration initialized successfully")
        
        # Perform health check
        print("\nPerforming health check...")
        health = await integration.health_check()
        print(f"Health check result: {health['status']}")
        
        if health["status"] == "unhealthy":
            print("❌ Health check failed")
            print("Issues:")
            for issue in health["issues"]:
                print(f"- {issue}")
            
            # Try to repair
            print("\nAttempting to repair...")
            repair_result = await integration.auto_repair()
            print(f"Repair result: {repair_result['success']}")
            
            if not repair_result["success"]:
                print("❌ Repair failed")
                print(f"Message: {repair_result['message']}")
                return False
            
            print("✅ Repair successful")
        else:
            print("✅ Health check passed")
        
        # Execute browser commands
        print("\nExecuting browser commands...")
        
        # Browse to Google
        print("Browsing to Google...")
        result = await integration.execute_command("Browse to https://www.google.com")
        if "error" in result:
            print(f"❌ Failed to browse to Google: {result['error']}")
            return False
        
        print("✅ Successfully browsed to Google")
        
        # Take a screenshot
        print("Taking screenshot...")
        result = await integration.execute_command("Take a screenshot")
        if "error" in result:
            print(f"❌ Failed to take screenshot: {result['error']}")
            return False
        
        print("✅ Successfully took screenshot")
        
        # Test Gmail integration
        print("\nTesting Gmail integration...")
        
        # Send email
        print("Sending email...")
        success = await integration.send_email(
            to="<EMAIL>",
            subject="Test Email",
            body="This is a test email from the Jarvis integration."
        )
        if not success:
            print("❌ Failed to send email")
            return False
        
        print("✅ Successfully sent email")
        
        # Test Google Voice integration
        print("\nTesting Google Voice integration...")
        
        # Send text message
        print("Sending text message...")
        success = await integration.send_text_message(
            to="1234567890",
            message="This is a test message from the Jarvis integration."
        )
        if not success:
            print("❌ Failed to send text message")
            return False
        
        print("✅ Successfully sent text message")
        
        # Make call
        print("Making call...")
        success = await integration.make_call(to="1234567890")
        if not success:
            print("❌ Failed to make call")
            return False
        
        print("✅ Successfully made call")
        
        # Stop integration
        print("\nStopping integration...")
        success = await integration.stop()
        if not success:
            print("❌ Failed to stop integration")
            return False
        
        print("✅ Integration stopped successfully")
        
        print("\nJarvis integration test completed successfully")
        return True
    
    except Exception as e:
        logger.exception(f"Error testing Jarvis integration: {e}")
        print(f"❌ Error: {e}")
        
        # Try to stop integration
        try:
            await integration.stop()
        except:
            pass
        
        return False

async def main():
    """Main entry point for the script."""
    try:
        success = await test_jarvis_integration()
        return 0 if success else 1
    except Exception as e:
        logger.exception(f"Error in main: {e}")
        print(f"Error: {e}")
        return 1

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nTest cancelled")
        sys.exit(0)
