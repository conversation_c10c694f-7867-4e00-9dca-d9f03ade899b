"""
Gmail service for the Multi-Agent AI System.
Allows agents to read, send, and manage emails through Gmail.
"""
import os
import base64
import json
import pickle
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import logging
from pathlib import Path
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from email.mime.base import MI<PERSON>Base
from email import encoders

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from core.logger import setup_logger

# Set up logger
logger = setup_logger("gmail_service")

class GmailService:
    """
    Service for interacting with Gmail.
    """
    
    # Define the scopes
    SCOPES = [
        'https://www.googleapis.com/auth/gmail.readonly',
        'https://www.googleapis.com/auth/gmail.send',
        'https://www.googleapis.com/auth/gmail.compose',
        'https://www.googleapis.com/auth/gmail.modify'
    ]
    
    def __init__(self, credentials_path: str = 'credentials/gmail_credentials.json', 
                 token_path: str = 'credentials/gmail_token.pickle'):
        """
        Initialize the Gmail service.
        
        Args:
            credentials_path (str): Path to the credentials JSON file
            token_path (str): Path to save the token pickle file
        """
        self.credentials_path = credentials_path
        self.token_path = token_path
        self.service = None
        self.enabled = False
        self.user_email = None
        
        # Ensure credentials directory exists
        os.makedirs(os.path.dirname(credentials_path), exist_ok=True)
        
        # Try to initialize the service
        try:
            self._initialize_service()
            self.enabled = True
            # Get user email
            profile = self.service.users().getProfile(userId='me').execute()
            self.user_email = profile.get('emailAddress')
            logger.info(f"Gmail service initialized successfully for {self.user_email}")
        except Exception as e:
            logger.error(f"Failed to initialize Gmail service: {e}")
    
    def _initialize_service(self):
        """Initialize the Gmail API service."""
        creds = None
        
        # Load token if it exists
        if os.path.exists(self.token_path):
            with open(self.token_path, 'rb') as token:
                creds = pickle.load(token)
        
        # Refresh token if expired
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        # Otherwise, get new credentials
        elif not creds:
            if not os.path.exists(self.credentials_path):
                logger.error(f"Credentials file not found: {self.credentials_path}")
                raise FileNotFoundError(f"Credentials file not found: {self.credentials_path}")
            
            flow = InstalledAppFlow.from_client_secrets_file(self.credentials_path, self.SCOPES)
            creds = flow.run_local_server(port=0)
            
            # Save the credentials for the next run
            with open(self.token_path, 'wb') as token:
                pickle.dump(creds, token)
        
        # Build the service
        self.service = build('gmail', 'v1', credentials=creds)
    
    def is_enabled(self) -> bool:
        """Check if the service is enabled."""
        return self.enabled
    
    def get_user_email(self) -> Optional[str]:
        """Get the user's email address."""
        return self.user_email
    
    async def list_messages(self, query: Optional[str] = None, 
                           max_results: int = 20, 
                           label_ids: Optional[List[str]] = None) -> Dict:
        """
        List messages in Gmail.
        
        Args:
            query (Optional[str]): Search query
            max_results (int): Maximum number of results to return
            label_ids (Optional[List[str]]): List of label IDs to filter by
            
        Returns:
            Dict: List of messages
        """
        if not self.enabled:
            return {"error": "Gmail service is not enabled"}
        
        try:
            # Execute the query
            results = self.service.users().messages().list(
                userId='me',
                q=query,
                maxResults=max_results,
                labelIds=label_ids
            ).execute()
            
            messages = results.get('messages', [])
            
            # Get full message details
            detailed_messages = []
            for message in messages:
                msg = self.service.users().messages().get(
                    userId='me', 
                    id=message['id'],
                    format='metadata',
                    metadataHeaders=['From', 'To', 'Subject', 'Date']
                ).execute()
                
                headers = {h['name']: h['value'] for h in msg.get('payload', {}).get('headers', [])}
                
                detailed_messages.append({
                    'id': msg['id'],
                    'threadId': msg['threadId'],
                    'labelIds': msg.get('labelIds', []),
                    'snippet': msg.get('snippet', ''),
                    'from': headers.get('From', ''),
                    'to': headers.get('To', ''),
                    'subject': headers.get('Subject', ''),
                    'date': headers.get('Date', '')
                })
            
            return {
                "messages": detailed_messages,
                "next_page_token": results.get('nextPageToken', None)
            }
        
        except HttpError as e:
            logger.error(f"Error listing messages: {e}")
            return {"error": f"Failed to list messages: {str(e)}"}
    
    async def get_message(self, message_id: str, format: str = 'full') -> Dict:
        """
        Get a specific message from Gmail.
        
        Args:
            message_id (str): ID of the message to get
            format (str): Format of the message (full, minimal, raw, metadata)
            
        Returns:
            Dict: Message details
        """
        if not self.enabled:
            return {"error": "Gmail service is not enabled"}
        
        try:
            msg = self.service.users().messages().get(
                userId='me', 
                id=message_id,
                format=format
            ).execute()
            
            # Extract headers
            headers = {}
            if 'payload' in msg and 'headers' in msg['payload']:
                headers = {h['name']: h['value'] for h in msg['payload']['headers']}
            
            # Extract body
            body = ""
            if format == 'full' and 'payload' in msg:
                if 'body' in msg['payload'] and 'data' in msg['payload']['body']:
                    body = base64.urlsafe_b64decode(msg['payload']['body']['data']).decode('utf-8')
                elif 'parts' in msg['payload']:
                    for part in msg['payload']['parts']:
                        if part.get('mimeType') == 'text/plain' and 'body' in part and 'data' in part['body']:
                            body = base64.urlsafe_b64decode(part['body']['data']).decode('utf-8')
                            break
            
            return {
                'id': msg['id'],
                'threadId': msg['threadId'],
                'labelIds': msg.get('labelIds', []),
                'snippet': msg.get('snippet', ''),
                'from': headers.get('From', ''),
                'to': headers.get('To', ''),
                'subject': headers.get('Subject', ''),
                'date': headers.get('Date', ''),
                'body': body
            }
        
        except HttpError as e:
            logger.error(f"Error getting message: {e}")
            return {"error": f"Failed to get message: {str(e)}"}
    
    async def send_message(self, to: str, subject: str, body: str, 
                          cc: Optional[str] = None, 
                          bcc: Optional[str] = None,
                          attachments: Optional[List[str]] = None,
                          html_body: Optional[str] = None) -> Dict:
        """
        Send an email through Gmail.
        
        Args:
            to (str): Recipient email address
            subject (str): Email subject
            body (str): Email body (plain text)
            cc (Optional[str]): CC recipients
            bcc (Optional[str]): BCC recipients
            attachments (Optional[List[str]]): List of file paths to attach
            html_body (Optional[str]): HTML version of the email body
            
        Returns:
            Dict: Send status
        """
        if not self.enabled:
            return {"error": "Gmail service is not enabled"}
        
        try:
            # Create message
            message = MIMEMultipart('alternative')
            message['to'] = to
            message['subject'] = subject
            
            if cc:
                message['cc'] = cc
            if bcc:
                message['bcc'] = bcc
            
            # Add plain text body
            message.attach(MIMEText(body, 'plain'))
            
            # Add HTML body if provided
            if html_body:
                message.attach(MIMEText(html_body, 'html'))
            
            # Add attachments if provided
            if attachments:
                for file_path in attachments:
                    if os.path.exists(file_path):
                        with open(file_path, 'rb') as file:
                            part = MIMEBase('application', 'octet-stream')
                            part.set_payload(file.read())
                        
                        encoders.encode_base64(part)
                        part.add_header(
                            'Content-Disposition',
                            f'attachment; filename={os.path.basename(file_path)}'
                        )
                        message.attach(part)
            
            # Encode message
            raw_message = base64.urlsafe_b64encode(message.as_bytes()).decode('utf-8')
            
            # Send message
            sent_message = self.service.users().messages().send(
                userId='me',
                body={'raw': raw_message}
            ).execute()
            
            return {
                "status": "success",
                "message_id": sent_message['id'],
                "thread_id": sent_message['threadId']
            }
        
        except HttpError as e:
            logger.error(f"Error sending message: {e}")
            return {"error": f"Failed to send message: {str(e)}"}

# Factory for creating Gmail service instances
class GmailServiceFactory:
    """Factory for creating Gmail service instances."""
    
    _instance = None
    
    @classmethod
    def create_service(cls) -> GmailService:
        """Create or return the singleton instance of GmailService."""
        if cls._instance is None:
            cls._instance = GmailService()
        return cls._instance
