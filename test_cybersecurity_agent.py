"""
Test script for the Cybersecurity Agent.

This script tests the functionality of the Cybersecurity Agent and its tools.
"""
import sys
import asyncio
import argparse
from pathlib import Path
import json
import os

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent))

from core.state_manager import StateManager
from core.logger import setup_logger
from agents.cybersecurity_agent import CybersecurityAgent
from services.tool_service import ToolService
from services.vulnerability_database import VulnerabilityDatabase
import config

# Set up logger
logger = setup_logger("test_cybersecurity_agent")

async def test_tool_service():
    """Test the Tool Service."""
    logger.info("Testing Tool Service")

    # Initialize tool service
    tool_service = ToolService(config.TOOL_CONFIG)

    # List available tools
    tools = await tool_service.list_tools()
    logger.info(f"Available tools: {len(tools)}")

    for tool in tools:
        logger.info(f"Tool: {tool['name']} - {tool['description']} - Installed: {tool['installed']}")

    # Test tool installation
    for tool_name in ["nmap", "john", "sqlmap"]:
        logger.info(f"Testing installation of {tool_name}")
        installed = await tool_service.check_tool_installed(tool_name)

        if not installed:
            logger.info(f"Installing {tool_name}")
            success = await tool_service.install_tool(tool_name)
            logger.info(f"Installation {'successful' if success else 'failed'}")
        else:
            logger.info(f"{tool_name} is already installed")

    return True

async def test_vulnerability_database():
    """Test the Vulnerability Database Service."""
    logger.info("Testing Vulnerability Database Service")

    # Initialize vulnerability database service
    vuln_db = VulnerabilityDatabase(config.VULNERABILITY_DATABASE_CONFIG)
    await vuln_db.initialize()

    try:
        # Test CVE lookup
        cve_id = "CVE-2021-44228"  # Log4Shell vulnerability
        logger.info(f"Looking up CVE: {cve_id}")
        cve_details = await vuln_db.get_cve_details(cve_id)

        if "error" in cve_details:
            logger.error(f"Error looking up CVE: {cve_details['error']}")
        else:
            logger.info(f"CVE {cve_id} details:")
            logger.info(f"Description: {cve_details.get('description', 'N/A')}")
            logger.info(f"CVSS v3 Score: {cve_details.get('cvss_v3', {}).get('base_score', 'N/A')}")
            logger.info(f"Severity: {cve_details.get('cvss_v3', {}).get('severity', 'N/A')}")

        # Test vulnerability search
        search_term = "log4j"
        logger.info(f"Searching for vulnerabilities: {search_term}")
        search_results = await vuln_db.search_vulnerabilities(search_term, max_results=5)

        if "error" in search_results:
            logger.error(f"Error searching for vulnerabilities: {search_results['error']}")
        else:
            result_count = len(search_results.get("result", {}).get("CVE_Items", []))
            logger.info(f"Found {result_count} vulnerabilities for '{search_term}'")

        # Test recent vulnerabilities
        logger.info("Getting recent vulnerabilities")
        recent_vulns = await vuln_db.get_recent_vulnerabilities(days=7, max_results=5)

        if "error" in recent_vulns:
            logger.error(f"Error getting recent vulnerabilities: {recent_vulns['error']}")
        else:
            result_count = len(recent_vulns.get("result", {}).get("CVE_Items", []))
            logger.info(f"Found {result_count} recent vulnerabilities")

        return True

    finally:
        # Close the vulnerability database service
        await vuln_db.close()

async def test_network_scan(agent, target="localhost"):
    """Test network scanning functionality."""
    logger.info(f"Testing network scan on {target}")

    # Create task
    task_id = "test_network_scan"
    task = {
        "type": "network_scan",
        "target": target,
        "scan_type": "basic",
        "status": "pending",
    }

    # Save task
    await agent.state_manager.update_state("cybersecurity", "pending_tasks", {task_id: task})

    # Process task
    await agent._process_task(task_id, task)

    # Get task result
    updated_task = await agent.state_manager.get_state("cybersecurity", "pending_tasks", task_id)

    if updated_task and updated_task.get("status") == "completed":
        logger.info("Network scan completed successfully")
        logger.info(f"Summary: {updated_task.get('result', {}).get('summary', 'No summary available')}")
        return True
    else:
        logger.error("Network scan failed")
        if updated_task and updated_task.get("status") == "error":
            logger.error(f"Error: {updated_task.get('error', 'Unknown error')}")
        return False

async def test_web_security_scan(agent, target="http://localhost"):
    """Test web security scanning functionality."""
    logger.info(f"Testing web security scan on {target}")

    # Create task
    task_id = "test_web_security_scan"
    task = {
        "type": "web_security_scan",
        "target": target,
        "scan_type": "server",
        "status": "pending",
    }

    # Save task
    await agent.state_manager.update_state("cybersecurity", "pending_tasks", {task_id: task})

    # Process task
    await agent._process_task(task_id, task)

    # Get task result
    updated_task = await agent.state_manager.get_state("cybersecurity", "pending_tasks", task_id)

    if updated_task and updated_task.get("status") == "completed":
        logger.info("Web security scan completed successfully")
        logger.info(f"Summary: {updated_task.get('result', {}).get('summary', 'No summary available')}")
        return True
    else:
        logger.error("Web security scan failed")
        if updated_task and updated_task.get("status") == "error":
            logger.error(f"Error: {updated_task.get('error', 'Unknown error')}")
        return False

async def test_sql_injection(agent, url="http://localhost"):
    """Test SQL injection functionality."""
    logger.info(f"Testing SQL injection on {url}")

    # Create task
    task_id = "test_sql_injection"
    task = {
        "type": "sql_injection_test",
        "url": url,
        "status": "pending",
    }

    # Save task
    await agent.state_manager.update_state("cybersecurity", "pending_tasks", {task_id: task})

    # Process task
    await agent._process_task(task_id, task)

    # Get task result
    updated_task = await agent.state_manager.get_state("cybersecurity", "pending_tasks", task_id)

    if updated_task and updated_task.get("status") == "completed":
        logger.info("SQL injection test completed successfully")
        logger.info(f"Summary: {updated_task.get('result', {}).get('summary', 'No summary available')}")
        return True
    else:
        logger.error("SQL injection test failed")
        if updated_task and updated_task.get("status") == "error":
            logger.error(f"Error: {updated_task.get('error', 'Unknown error')}")
        return False

async def test_ai_analysis(agent):
    """Test AI-enhanced security analysis functionality."""
    logger.info("Testing AI-enhanced security analysis")

    # Create sample scan results
    scan_results = {
        "scan_id": "test_scan_001",
        "target": "example.com",
        "timestamp": "2023-05-07T12:00:00.000Z",
        "vulnerabilities": [
            {
                "name": "CVE-2021-44228 (Log4Shell)",
                "details": [
                    "Remote code execution vulnerability in Log4j",
                    "Affects Java applications using Log4j 2.0 to 2.14.1"
                ]
            },
            {
                "name": "Outdated SSL/TLS",
                "details": [
                    "Server supports TLS 1.0 which is deprecated",
                    "Weak cipher suites detected"
                ]
            }
        ],
        "open_ports": [
            {"port": "22", "service": "SSH"},
            {"port": "80", "service": "HTTP"},
            {"port": "443", "service": "HTTPS"}
        ],
        "summary": "Vulnerability scan found 2 vulnerabilities and 3 open ports."
    }

    # Create task for vulnerability analysis
    task_id = "test_vulnerability_analysis"
    task = {
        "type": "vulnerability_analysis",
        "scan_results": scan_results,
        "status": "pending",
    }

    # Save task
    await agent.state_manager.update_state("cybersecurity", "pending_tasks", {task_id: task})

    # Process task
    logger.info("Testing vulnerability analysis")
    await agent._process_task(task_id, task)

    # Get task result
    updated_task = await agent.state_manager.get_state("cybersecurity", "pending_tasks", task_id)

    if updated_task and updated_task.get("status") == "completed":
        logger.info("Vulnerability analysis completed successfully")
        logger.info(f"Summary: {updated_task.get('result', {}).get('summary', 'No summary available')}")
        return True
    else:
        logger.error("Vulnerability analysis failed")
        if updated_task and updated_task.get("status") == "error":
            logger.error(f"Error: {updated_task.get('error', 'Unknown error')}")
        return False

async def test_multi_agent_system(agent):
    """Test the multi-agent system functionality."""
    logger.info("Testing multi-agent system")

    # Create task for security assessment
    task_id = "test_security_assessment"
    task = {
        "type": "security_assessment",
        "target": "localhost",
        "options": {
            "scan_type": "basic",
            "timeout": 60,
        },
        "status": "pending",
    }

    # Save task
    await agent.state_manager.update_state("cybersecurity", "pending_tasks", {task_id: task})

    # Process task
    logger.info("Testing security assessment")
    await agent._process_task(task_id, task)

    # Get task result
    updated_task = await agent.state_manager.get_state("cybersecurity", "pending_tasks", task_id)

    if updated_task and updated_task.get("status") == "completed":
        logger.info("Security assessment completed successfully")
        logger.info(f"Result: {updated_task.get('result', {})}")
        return True
    else:
        logger.error("Security assessment failed")
        if updated_task and updated_task.get("status") == "error":
            logger.error(f"Error: {updated_task.get('error', 'Unknown error')}")
        return False

async def test_agent(test_type=None, target=None):
    """
    Test the Cybersecurity Agent.

    Args:
        test_type (str): Type of test to run (tools, network, web, sql, vuln_db, ai, multi_agent)
        target (str): Target for the test
    """
    logger.info("Testing Cybersecurity Agent")

    # Initialize state manager
    state_manager = StateManager()
    await state_manager.initialize()

    # Create shutdown event
    shutdown_event = asyncio.Event()

    # Create message queue
    message_queue = asyncio.Queue()

    try:
        # Create agent instance
        agent = CybersecurityAgent(
            agent_id="cybersecurity_agent",
            config=config.AGENT_CONFIG["cybersecurity_agent"],
            state_manager=state_manager,
            message_queue=message_queue,
            shutdown_event=shutdown_event
        )

        # Initialize agent
        logger.info("Initializing Cybersecurity Agent")
        await agent.initialize()

        # Run tests based on type
        if test_type == "tools" or test_type is None:
            await test_tool_service()

        if test_type == "network" or test_type is None:
            await test_network_scan(agent, target or "localhost")

        if test_type == "web" or test_type is None:
            await test_web_security_scan(agent, target or "http://localhost")

        if test_type == "sql" or test_type is None:
            await test_sql_injection(agent, target or "http://localhost")

        if test_type == "vuln_db" or test_type is None:
            await test_vulnerability_database()

        if test_type == "ai" or test_type is None:
            await test_ai_analysis(agent)

        if test_type == "multi_agent" or test_type is None:
            await test_multi_agent_system(agent)

        # Shut down agent
        logger.info("Shutting down Cybersecurity Agent")
        await agent.shutdown()

        return True

    except Exception as e:
        logger.exception(f"Error testing agent: {e}")
        return False

    finally:
        # Close state manager
        await state_manager.close()

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Test the Cybersecurity Agent")
    parser.add_argument("--test", choices=["tools", "network", "web", "sql", "vuln_db", "ai", "multi_agent"], help="Type of test to run")
    parser.add_argument("--target", help="Target for the test (e.g., localhost, example.com)")
    args = parser.parse_args()

    # Run tests
    success = asyncio.run(test_agent(args.test, args.target))

    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
