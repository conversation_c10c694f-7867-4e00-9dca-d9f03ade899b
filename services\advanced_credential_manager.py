"""
Advanced Credential Manager for the Multi-Agent AI System.

This module provides advanced credential management capabilities, including:
- Secure storage and retrieval of credentials
- Automatic credential recovery and refresh
- Multi-service credential management
- Credential rotation and security monitoring
"""
import asyncio
import base64
import json
import logging
import os
import pickle
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
import uuid
import hashlib
import secrets
from pathlib import Path
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

from core.logger import setup_logger
from core.state_manager import StateManager

# Set up logger
logger = setup_logger("advanced_credential_manager")

class AdvancedCredentialManager:
    """
    Advanced credential manager for secure credential handling.

    This class provides advanced credential management capabilities, including
    secure storage and retrieval, automatic recovery, and security monitoring.
    """

    def __init__(self, state_manager: StateManager, config: Dict = None):
        """
        Initialize the advanced credential manager.

        Args:
            state_manager (StateManager): System state manager
            config (Dict, optional): Configuration options
        """
        self.state_manager = state_manager
        self.config = config or {}

        # Credential storage
        self.credentials_dir = self.config.get("credentials_dir", "credentials")
        self.credentials_cache = {}
        self.service_configs = {}

        # Encryption
        self.encryption_key = None
        self.encryption_enabled = self.config.get("encryption_enabled", True)

        # Recovery settings
        self.auto_recovery_enabled = self.config.get("auto_recovery_enabled", True)
        self.recovery_attempts = {}

        # Security monitoring
        self.access_logs = []
        self.security_alerts = []

        logger.info("Advanced credential manager initialized")

    async def initialize(self):
        """Initialize the credential manager."""
        try:
            # Create credentials directory if it doesn't exist
            os.makedirs(self.credentials_dir, exist_ok=True)

            # Initialize encryption
            if self.encryption_enabled:
                await self._initialize_encryption()

            # Load service configurations
            await self._load_service_configs()

            # Load credentials into cache
            await self._load_credentials_cache()

            logger.info("Advanced credential manager initialized successfully")

        except Exception as e:
            logger.exception(f"Error initializing credential manager: {e}")
            raise

    async def _initialize_encryption(self):
        """Initialize encryption for secure credential storage."""
        try:
            # Check if encryption key exists
            key_path = os.path.join(self.credentials_dir, ".encryption_key")

            if os.path.exists(key_path):
                # Load existing key
                with open(key_path, "rb") as key_file:
                    self.encryption_key = key_file.read()
            else:
                # Generate new key
                self.encryption_key = Fernet.generate_key()

                # Save key
                with open(key_path, "wb") as key_file:
                    key_file.write(self.encryption_key)

                logger.info("Generated new encryption key")

            logger.info("Encryption initialized")

        except Exception as e:
            logger.exception(f"Error initializing encryption: {e}")
            self.encryption_enabled = False
            logger.warning("Encryption disabled due to initialization error")

    async def _load_service_configs(self):
        """Load service configurations from state manager."""
        try:
            # Load service configurations
            service_configs = await self.state_manager.get_state("credentials", "service_configs")

            if service_configs:
                self.service_configs = service_configs
                logger.info(f"Loaded configurations for {len(self.service_configs)} services")
            else:
                # Initialize with default configurations
                self.service_configs = {
                    "gmail": {
                        "type": "oauth2",
                        "scopes": [
                            "https://www.googleapis.com/auth/gmail.send",
                            "https://www.googleapis.com/auth/gmail.readonly",
                            "https://www.googleapis.com/auth/gmail.modify",
                            "https://www.googleapis.com/auth/gmail.compose"
                        ],
                        "token_expiry": 3600,
                        "auto_refresh": True,
                        "credentials_format": "json",
                        "token_format": "pickle"
                    },
                    "google_drive": {
                        "type": "oauth2",
                        "scopes": [
                            "https://www.googleapis.com/auth/drive.readonly",
                            "https://www.googleapis.com/auth/drive.file",
                            "https://www.googleapis.com/auth/drive.metadata.readonly"
                        ],
                        "token_expiry": 3600,
                        "auto_refresh": True,
                        "credentials_format": "json",
                        "token_format": "pickle"
                    },
                    "openai": {
                        "type": "api_key",
                        "token_expiry": None,
                        "auto_refresh": False,
                        "credentials_format": "text",
                        "token_format": None
                    },
                    "anthropic": {
                        "type": "api_key",
                        "token_expiry": None,
                        "auto_refresh": False,
                        "credentials_format": "text",
                        "token_format": None
                    }
                }

                # Save default configurations
                await self.state_manager.update_state("credentials", "service_configs", self.service_configs)
                logger.info("Initialized default service configurations")

        except Exception as e:
            logger.exception(f"Error loading service configurations: {e}")
            raise

    async def _load_credentials_cache(self):
        """Load credentials into cache for faster access."""
        try:
            # Clear existing cache
            self.credentials_cache = {}

            # Scan credentials directory
            for filename in os.listdir(self.credentials_dir):
                # Skip encryption key and non-credential files
                if filename == ".encryption_key" or filename.startswith("."):
                    continue

                # Parse filename to extract service and account info
                parts = filename.split("_")

                if len(parts) >= 2:
                    service_name = parts[0]

                    # Handle different credential formats
                    if filename.endswith("_credentials.json"):
                        # OAuth credentials
                        account_id = "_".join(parts[1:-1])
                        credential_type = "credentials"
                    elif filename.endswith("_token.pickle"):
                        # OAuth token
                        account_id = "_".join(parts[1:-1])
                        credential_type = "token"
                    elif filename.endswith(".key"):
                        # API key
                        account_id = "_".join(parts[1:-1])
                        credential_type = "api_key"
                    else:
                        # Unknown format
                        continue

                    # Add to cache
                    if service_name not in self.credentials_cache:
                        self.credentials_cache[service_name] = {}

                    if account_id not in self.credentials_cache[service_name]:
                        self.credentials_cache[service_name][account_id] = {}

                    self.credentials_cache[service_name][account_id][credential_type] = {
                        "path": os.path.join(self.credentials_dir, filename),
                        "last_accessed": None,
                        "last_refreshed": None,
                        "status": "available"
                    }

            logger.info(f"Loaded credentials for {len(self.credentials_cache)} services")

        except Exception as e:
            logger.exception(f"Error loading credentials cache: {e}")
            raise

    async def get_credential(
        self,
        service_name: str,
        account_id: str,
        credential_type: str = "token"
    ) -> Any:
        """
        Get a credential for a service and account.

        Args:
            service_name (str): Service name (e.g., "gmail", "openai")
            account_id (str): Account identifier
            credential_type (str): Type of credential ("token", "credentials", "api_key")

        Returns:
            Any: Credential data
        """
        try:
            # Log access
            self._log_access(service_name, account_id, credential_type, "get")

            # Check if service exists in cache
            if service_name not in self.credentials_cache:
                logger.warning(f"Service not found in cache: {service_name}")
                return None

            # Check if account exists for service
            if account_id not in self.credentials_cache[service_name]:
                logger.warning(f"Account not found for service {service_name}: {account_id}")
                return None

            # Check if credential type exists for account
            if credential_type not in self.credentials_cache[service_name][account_id]:
                logger.warning(f"Credential type not found for account {account_id}: {credential_type}")
                return None

            # Get credential info
            credential_info = self.credentials_cache[service_name][account_id][credential_type]

            # Check if credential is available
            if credential_info["status"] != "available":
                logger.warning(f"Credential not available: {service_name}/{account_id}/{credential_type}")

                # Attempt recovery if enabled
                if self.auto_recovery_enabled:
                    recovered = await self._attempt_recovery(service_name, account_id, credential_type)
                    if not recovered:
                        return None
                else:
                    return None

            # Load credential from file
            credential_path = credential_info["path"]

            if not os.path.exists(credential_path):
                logger.error(f"Credential file not found: {credential_path}")
                return None

            # Load based on format
            credential_data = None

            if credential_type == "token" and credential_path.endswith(".pickle"):
                # Load pickle token
                with open(credential_path, "rb") as token_file:
                    credential_data = pickle.load(token_file)
            elif credential_type == "credentials" and credential_path.endswith(".json"):
                # Load JSON credentials
                with open(credential_path, "r") as cred_file:
                    credential_data = json.load(cred_file)
            elif credential_type == "api_key" and credential_path.endswith(".key"):
                # Load API key
                with open(credential_path, "r") as key_file:
                    credential_data = key_file.read().strip()

            # Update access timestamp
            self.credentials_cache[service_name][account_id][credential_type]["last_accessed"] = datetime.now().isoformat()

            return credential_data

        except Exception as e:
            logger.exception(f"Error getting credential: {e}")
            return None

    async def store_credential(
        self,
        service_name: str,
        account_id: str,
        credential_type: str,
        credential_data: Any
    ) -> bool:
        """
        Store a credential for a service and account.

        Args:
            service_name (str): Service name (e.g., "gmail", "openai")
            account_id (str): Account identifier
            credential_type (str): Type of credential ("token", "credentials", "api_key")
            credential_data (Any): Credential data to store

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Log access
            self._log_access(service_name, account_id, credential_type, "store")

            # Determine file format and path
            file_extension = None

            if credential_type == "token":
                file_extension = "pickle"
            elif credential_type == "credentials":
                file_extension = "json"
            elif credential_type == "api_key":
                file_extension = "key"
            else:
                logger.error(f"Unknown credential type: {credential_type}")
                return False

            # Create safe account ID for filename
            safe_account_id = account_id.replace("@", "_at_").replace(".", "_dot_")

            # Determine filename
            if credential_type == "api_key":
                filename = f"{service_name}_{safe_account_id}.{file_extension}"
            else:
                filename = f"{service_name}_{safe_account_id}_{credential_type}.{file_extension}"

            # Full path
            credential_path = os.path.join(self.credentials_dir, filename)

            # Store credential based on format
            if credential_type == "token" and file_extension == "pickle":
                # Store pickle token
                with open(credential_path, "wb") as token_file:
                    pickle.dump(credential_data, token_file)
            elif credential_type == "credentials" and file_extension == "json":
                # Store JSON credentials
                with open(credential_path, "w") as cred_file:
                    json.dump(credential_data, cred_file, indent=2)
            elif credential_type == "api_key" and file_extension == "key":
                # Store API key
                with open(credential_path, "w") as key_file:
                    key_file.write(str(credential_data))

            # Update cache
            if service_name not in self.credentials_cache:
                self.credentials_cache[service_name] = {}

            if account_id not in self.credentials_cache[service_name]:
                self.credentials_cache[service_name][account_id] = {}

            self.credentials_cache[service_name][account_id][credential_type] = {
                "path": credential_path,
                "last_accessed": datetime.now().isoformat(),
                "last_refreshed": datetime.now().isoformat(),
                "status": "available"
            }

            logger.info(f"Stored credential: {service_name}/{account_id}/{credential_type}")
            return True

        except Exception as e:
            logger.exception(f"Error storing credential: {e}")
            return False

    async def refresh_credential(
        self,
        service_name: str,
        account_id: str,
        credential_type: str = "token"
    ) -> bool:
        """
        Refresh a credential for a service and account.

        Args:
            service_name (str): Service name (e.g., "gmail", "openai")
            account_id (str): Account identifier
            credential_type (str): Type of credential ("token", "credentials", "api_key")

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Log access
            self._log_access(service_name, account_id, credential_type, "refresh")

            # Check if service exists in cache
            if service_name not in self.credentials_cache:
                logger.warning(f"Service not found in cache: {service_name}")
                return False

            # Check if account exists for service
            if account_id not in self.credentials_cache[service_name]:
                logger.warning(f"Account not found for service {service_name}: {account_id}")
                return False

            # Check if credential type exists for account
            if credential_type not in self.credentials_cache[service_name][account_id]:
                logger.warning(f"Credential type not found for account {account_id}: {credential_type}")
                return False

            # Get credential info
            credential_info = self.credentials_cache[service_name][account_id][credential_type]

            # Check if credential is available
            if credential_info["status"] != "available":
                logger.warning(f"Credential not available: {service_name}/{account_id}/{credential_type}")
                return False

            # Get service config
            service_config = self.service_configs.get(service_name)
            if not service_config:
                logger.warning(f"Service configuration not found: {service_name}")
                return False

            # Check if refresh is supported
            if not service_config.get("auto_refresh", False):
                logger.warning(f"Refresh not supported for service: {service_name}")
                return False

            # For OAuth tokens, we need to refresh using the credentials
            if service_config.get("type") == "oauth2" and credential_type == "token":
                # Get credentials
                credentials = await self.get_credential(service_name, account_id, "credentials")
                if not credentials:
                    logger.error(f"Credentials not found for token refresh: {service_name}/{account_id}")
                    return False

                # Get token
                token = await self.get_credential(service_name, account_id, "token")
                if not token:
                    logger.error(f"Token not found for refresh: {service_name}/{account_id}")
                    return False

                # Refresh token (implementation depends on service)
                if service_name in ["gmail", "google_drive"]:
                    # For Google services, use the refresh token
                    try:
                        from google.oauth2.credentials import Credentials
                        from google_auth_oauthlib.flow import InstalledAppFlow
                        from google.auth.transport.requests import Request

                        # Create credentials object
                        creds = Credentials.from_authorized_user_info(token)

                        # Refresh token
                        if creds.expired and creds.refresh_token:
                            creds.refresh(Request())

                            # Store refreshed token
                            await self.store_credential(service_name, account_id, "token", creds)

                            logger.info(f"Refreshed token: {service_name}/{account_id}")
                            return True
                        else:
                            logger.warning(f"Token not expired or no refresh token: {service_name}/{account_id}")
                            return False
                    except Exception as e:
                        logger.exception(f"Error refreshing Google token: {e}")
                        return False
                else:
                    logger.warning(f"Token refresh not implemented for service: {service_name}")
                    return False
            else:
                logger.warning(f"Refresh not applicable for credential type: {credential_type}")
                return False

        except Exception as e:
            logger.exception(f"Error refreshing credential: {e}")
            return False

    async def _attempt_recovery(
        self,
        service_name: str,
        account_id: str,
        credential_type: str
    ) -> bool:
        """
        Attempt to recover a credential.

        Args:
            service_name (str): Service name
            account_id (str): Account identifier
            credential_type (str): Credential type

        Returns:
            bool: True if recovered, False otherwise
        """
        try:
            # Check if we've already tried to recover this credential too many times
            recovery_key = f"{service_name}:{account_id}:{credential_type}"
            if recovery_key in self.recovery_attempts:
                attempts = self.recovery_attempts[recovery_key]
                if attempts >= 3:  # Limit recovery attempts
                    logger.warning(f"Too many recovery attempts for {recovery_key}")
                    return False
                self.recovery_attempts[recovery_key] = attempts + 1
            else:
                self.recovery_attempts[recovery_key] = 1

            # Get service config
            service_config = self.service_configs.get(service_name)
            if not service_config:
                logger.warning(f"Service configuration not found: {service_name}")
                return False

            # For OAuth tokens, try to refresh
            if service_config.get("type") == "oauth2" and credential_type == "token":
                return await self.refresh_credential(service_name, account_id, credential_type)

            # For other credential types, recovery is not implemented yet
            logger.warning(f"Recovery not implemented for {service_name}/{account_id}/{credential_type}")
            return False

        except Exception as e:
            logger.exception(f"Error attempting credential recovery: {e}")
            return False

    def _log_access(
        self,
        service_name: str,
        account_id: str,
        credential_type: str,
        action: str
    ):
        """
        Log credential access for security monitoring.

        Args:
            service_name (str): Service name
            account_id (str): Account identifier
            credential_type (str): Credential type
            action (str): Action performed ("get", "store", "refresh", etc.)
        """
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "service": service_name,
            "account": account_id,
            "credential_type": credential_type,
            "action": action,
        }

        self.access_logs.append(log_entry)

        # Limit log size
        if len(self.access_logs) > 1000:
            self.access_logs = self.access_logs[-1000:]

    async def check_security(self) -> List[Dict]:
        """
        Check for security issues with credentials.

        Returns:
            List[Dict]: List of security alerts
        """
        try:
            # Clear previous alerts
            self.security_alerts = []

            # Check for expired tokens
            await self._check_expired_tokens()

            # Check for unusual access patterns
            self._check_unusual_access()

            # Check for missing credentials
            await self._check_missing_credentials()

            return self.security_alerts

        except Exception as e:
            logger.exception(f"Error checking credential security: {e}")
            return []

    async def _check_expired_tokens(self):
        """Check for expired tokens that need refreshing."""
        for service_name, accounts in self.credentials_cache.items():
            service_config = self.service_configs.get(service_name)
            if not service_config:
                continue

            # Skip if service doesn't have token expiry
            token_expiry = service_config.get("token_expiry")
            if not token_expiry:
                continue

            for account_id, credentials in accounts.items():
                if "token" in credentials:
                    token_info = credentials["token"]

                    # Check if token has been accessed recently
                    if token_info.get("last_accessed"):
                        last_accessed = datetime.fromisoformat(token_info["last_accessed"])
                        time_since_access = datetime.now() - last_accessed

                        # If token might be expired
                        if time_since_access.total_seconds() > token_expiry:
                            # Add security alert
                            self.security_alerts.append({
                                "type": "expired_token",
                                "severity": "medium",
                                "service": service_name,
                                "account": account_id,
                                "message": f"Token for {service_name}/{account_id} may be expired",
                                "timestamp": datetime.now().isoformat(),
                            })

                            # Try to refresh if auto-refresh is enabled
                            if service_config.get("auto_refresh", False):
                                await self.refresh_credential(service_name, account_id, "token")

    def _check_unusual_access(self):
        """Check for unusual access patterns in the logs."""
        # This is a simplified implementation
        # In a real system, this would involve more sophisticated analysis

        # Check for multiple accesses in short time
        if len(self.access_logs) >= 10:
            recent_logs = self.access_logs[-10:]

            # Check time span of recent logs
            first_timestamp = datetime.fromisoformat(recent_logs[0]["timestamp"])
            last_timestamp = datetime.fromisoformat(recent_logs[-1]["timestamp"])
            time_span = (last_timestamp - first_timestamp).total_seconds()

            # If 10 accesses in less than 5 seconds, that's suspicious
            if time_span < 5:
                self.security_alerts.append({
                    "type": "rapid_access",
                    "severity": "high",
                    "message": f"Unusually rapid credential access: {len(recent_logs)} accesses in {time_span:.2f} seconds",
                    "timestamp": datetime.now().isoformat(),
                })

    async def _check_missing_credentials(self):
        """Check for missing credentials that should exist."""
        # Check for services that should have credentials
        for service_name, service_config in self.service_configs.items():
            if service_name not in self.credentials_cache or not self.credentials_cache[service_name]:
                self.security_alerts.append({
                    "type": "missing_credentials",
                    "severity": "medium",
                    "service": service_name,
                    "message": f"No credentials found for service: {service_name}",
                    "timestamp": datetime.now().isoformat(),
                })
                continue

            # For OAuth services, check if both credentials and token exist
            if service_config.get("type") == "oauth2":
                for account_id, credentials in self.credentials_cache[service_name].items():
                    if "credentials" not in credentials:
                        self.security_alerts.append({
                            "type": "missing_credentials",
                            "severity": "high",
                            "service": service_name,
                            "account": account_id,
                            "message": f"OAuth credentials missing for {service_name}/{account_id}",
                            "timestamp": datetime.now().isoformat(),
                        })

                    if "token" not in credentials:
                        self.security_alerts.append({
                            "type": "missing_token",
                            "severity": "medium",
                            "service": service_name,
                            "account": account_id,
                            "message": f"OAuth token missing for {service_name}/{account_id}",
                            "timestamp": datetime.now().isoformat(),
                        })

    async def get_credential_status(self) -> Dict:
        """
        Get status of all credentials.

        Returns:
            Dict: Credential status information
        """
        status = {
            "services": {},
            "total_credentials": 0,
            "available_credentials": 0,
            "unavailable_credentials": 0,
            "security_alerts": len(self.security_alerts),
        }

        for service_name, accounts in self.credentials_cache.items():
            service_status = {
                "accounts": {},
                "total_credentials": 0,
                "available_credentials": 0,
                "unavailable_credentials": 0,
            }

            for account_id, credentials in accounts.items():
                account_status = {
                    "credentials": {},
                    "total_credentials": 0,
                    "available_credentials": 0,
                    "unavailable_credentials": 0,
                }

                for credential_type, credential_info in credentials.items():
                    credential_status = {
                        "status": credential_info["status"],
                        "last_accessed": credential_info.get("last_accessed"),
                        "last_refreshed": credential_info.get("last_refreshed"),
                    }

                    account_status["credentials"][credential_type] = credential_status
                    account_status["total_credentials"] += 1

                    if credential_info["status"] == "available":
                        account_status["available_credentials"] += 1
                    else:
                        account_status["unavailable_credentials"] += 1

                service_status["accounts"][account_id] = account_status
                service_status["total_credentials"] += account_status["total_credentials"]
                service_status["available_credentials"] += account_status["available_credentials"]
                service_status["unavailable_credentials"] += account_status["unavailable_credentials"]

            status["services"][service_name] = service_status
            status["total_credentials"] += service_status["total_credentials"]
            status["available_credentials"] += service_status["available_credentials"]
            status["unavailable_credentials"] += service_status["unavailable_credentials"]

        return status
