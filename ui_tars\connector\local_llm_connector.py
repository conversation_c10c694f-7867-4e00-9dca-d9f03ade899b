"""
Local LLM Connector for UI-TARS.

This module provides a connector to interface with local LLMs,
allowing UI-TARS to use locally installed models.
"""
import os
import sys
import json
import asyncio
import logging
import subprocess
from typing import Dict, List, Optional, Any, Union
import platform
import requests
import time
import shutil
import tempfile
from pathlib import Path

from core.logger import setup_logger

# Set up logger
logger = setup_logger("local_llm_connector")

class LocalLLMConnector:
    """
    Connector for local LLMs.
    
    This class provides methods to interface with locally installed LLMs,
    allowing UI-TARS to use these models instead of remote APIs.
    """
    
    def __init__(self, 
                 model_path: Optional[str] = None,
                 model_type: str = "ui-tars",
                 host: str = "localhost",
                 port: int = 8000,
                 api_base: Optional[str] = None,
                 quantization: str = "4bit"):
        """
        Initialize the Local LLM connector.
        
        Args:
            model_path (Optional[str]): Path to the model
            model_type (str): Type of model (ui-tars, qwen, llama, etc.)
            host (str): Host for the local server
            port (int): Port for the local server
            api_base (Optional[str]): Base URL for the API
            quantization (str): Quantization level for the model
        """
        self.model_path = model_path
        self.model_type = model_type.lower()
        self.host = host
        self.port = port
        self.api_base = api_base or f"http://{host}:{port}"
        self.quantization = quantization
        self.session = None
        self.process = None
        self.is_running = False
        self.os_type = platform.system()  # 'Windows', 'Darwin' (macOS), or 'Linux'
        
        # Find model path if not provided
        if not self.model_path:
            self._find_model_path()
    
    def _find_model_path(self):
        """Find the model path based on the model type."""
        # Common model directories
        if self.os_type == "Windows":
            base_dirs = [
                os.path.join(os.environ.get("USERPROFILE", ""), "Documents", "models"),
                os.path.join(os.environ.get("USERPROFILE", ""), "Downloads", "models"),
                os.path.join(os.environ.get("LOCALAPPDATA", ""), "models"),
                "C:\\models",
            ]
        else:  # macOS or Linux
            base_dirs = [
                os.path.expanduser("~/models"),
                os.path.expanduser("~/Documents/models"),
                os.path.expanduser("~/Downloads/models"),
                "/usr/local/models",
                "/opt/models",
            ]
        
        # Model-specific subdirectories to check
        if self.model_type == "ui-tars":
            subdirs = ["UI-TARS", "ui-tars", "UI-TARS-1.5", "ui-tars-1.5", "UI-TARS-1.5-7B", "ui-tars-1.5-7b"]
        elif self.model_type == "qwen":
            subdirs = ["Qwen", "qwen", "Qwen2.5-VL", "qwen2.5-vl", "Qwen2.5-VL-7B", "qwen2.5-vl-7b"]
        else:
            subdirs = [self.model_type]
        
        # Check all possible paths
        for base_dir in base_dirs:
            for subdir in subdirs:
                path = os.path.join(base_dir, subdir)
                if os.path.exists(path):
                    self.model_path = path
                    logger.info(f"Found model at: {path}")
                    return
        
        logger.warning(f"Could not find {self.model_type} model path")
    
    async def initialize(self):
        """Initialize the Local LLM connector."""
        logger.info("Initializing Local LLM connector")
        
        # Create a session for API requests
        self.session = requests.Session()
        
        # Check if the model path exists
        if not self.model_path or not os.path.exists(self.model_path):
            logger.warning("Model path does not exist")
            return False
        
        logger.info("Local LLM connector initialized")
        return True
    
    async def start_server(self):
        """Start the local LLM server."""
        logger.info(f"Starting local LLM server for {self.model_type}")
        
        try:
            # Check if server is already running
            try:
                response = requests.get(f"{self.api_base}/health")
                if response.status_code == 200:
                    logger.info("Local LLM server is already running")
                    self.is_running = True
                    return True
            except:
                pass
            
            # Prepare the command based on model type
            if self.model_type == "ui-tars" or self.model_type == "qwen":
                # Use text-generation-webui for UI-TARS and Qwen models
                command = [
                    "python", "-m", "text_generation_webui.server", 
                    "--model", self.model_path,
                    "--listen", "--api",
                    "--host", self.host,
                    "--port", str(self.port),
                    "--load-in-4bit" if self.quantization == "4bit" else "--load-in-8bit"
                ]
            elif self.model_type == "llama":
                # Use llama.cpp for Llama models
                command = [
                    "llama-server",
                    "-m", self.model_path,
                    "--host", self.host,
                    "--port", str(self.port),
                    "-ngl", "32"  # Use 32 layers for GPU acceleration
                ]
            else:
                # Default to text-generation-webui
                command = [
                    "python", "-m", "text_generation_webui.server", 
                    "--model", self.model_path,
                    "--listen", "--api",
                    "--host", self.host,
                    "--port", str(self.port)
                ]
            
            # Start the process
            self.process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Wait for the server to start
            max_retries = 30
            for i in range(max_retries):
                try:
                    response = requests.get(f"{self.api_base}/health")
                    if response.status_code == 200:
                        logger.info("Local LLM server started successfully")
                        self.is_running = True
                        return True
                except:
                    if i == max_retries - 1:
                        logger.warning("Timed out waiting for local LLM server to start")
                        return False
                    
                    logger.info(f"Waiting for local LLM server to start ({i+1}/{max_retries})...")
                    await asyncio.sleep(2)
            
            return False
        
        except Exception as e:
            logger.exception(f"Error starting local LLM server: {e}")
            return False
    
    async def generate_text(self, prompt: str, system_prompt: Optional[str] = None, max_tokens: int = 1000) -> str:
        """
        Generate text using the local LLM.
        
        Args:
            prompt (str): Prompt for text generation
            system_prompt (Optional[str]): System prompt for the model
            max_tokens (int): Maximum number of tokens to generate
            
        Returns:
            str: Generated text
        """
        if not self.is_running:
            logger.warning("Local LLM server is not running")
            return "Error: Local LLM server is not running"
        
        logger.info(f"Generating text with prompt: {prompt[:50]}...")
        
        try:
            # Prepare the request
            data = {
                "prompt": prompt,
                "max_tokens": max_tokens,
                "temperature": 0.7,
                "top_p": 0.9
            }
            
            # Add system prompt if provided
            if system_prompt:
                if self.model_type == "ui-tars":
                    # UI-TARS specific format
                    data["system_prompt"] = system_prompt
                else:
                    # Generic format
                    data["prompt"] = f"{system_prompt}\n\n{prompt}"
            
            # Send the request
            response = self.session.post(f"{self.api_base}/v1/completions", json=data)
            response.raise_for_status()
            
            # Parse the response
            result = response.json()
            
            # Extract the generated text
            if "choices" in result and len(result["choices"]) > 0:
                text = result["choices"][0]["text"]
                logger.info(f"Text generated successfully: {text[:50]}...")
                return text
            else:
                logger.warning(f"Unexpected response format: {result}")
                return "Error: Unexpected response format"
        
        except Exception as e:
            logger.exception(f"Error generating text: {e}")
            return f"Error: {str(e)}"
    
    async def process_image(self, image_path: str, prompt: str) -> str:
        """
        Process an image using the local LLM.
        
        Args:
            image_path (str): Path to the image
            prompt (str): Prompt for image processing
            
        Returns:
            str: Generated text
        """
        if not self.is_running:
            logger.warning("Local LLM server is not running")
            return "Error: Local LLM server is not running"
        
        logger.info(f"Processing image: {image_path}")
        
        try:
            # Check if the image exists
            if not os.path.exists(image_path):
                logger.warning(f"Image not found: {image_path}")
                return "Error: Image not found"
            
            # Encode the image as base64
            import base64
            with open(image_path, "rb") as image_file:
                encoded_image = base64.b64encode(image_file.read()).decode("utf-8")
            
            # Prepare the request
            data = {
                "prompt": prompt,
                "image": encoded_image,
                "max_tokens": 1000,
                "temperature": 0.7,
                "top_p": 0.9
            }
            
            # Send the request
            response = self.session.post(f"{self.api_base}/v1/vision", json=data)
            response.raise_for_status()
            
            # Parse the response
            result = response.json()
            
            # Extract the generated text
            if "choices" in result and len(result["choices"]) > 0:
                text = result["choices"][0]["text"]
                logger.info(f"Image processed successfully: {text[:50]}...")
                return text
            else:
                logger.warning(f"Unexpected response format: {result}")
                return "Error: Unexpected response format"
        
        except Exception as e:
            logger.exception(f"Error processing image: {e}")
            return f"Error: {str(e)}"
    
    async def stop_server(self):
        """Stop the local LLM server."""
        if not self.is_running:
            logger.info("Local LLM server is not running")
            return True
        
        logger.info("Stopping local LLM server")
        
        try:
            if self.process:
                self.process.terminate()
                await asyncio.sleep(2)
                
                # Force kill if still running
                if self.process.poll() is None:
                    self.process.kill()
                
                self.process = None
            
            self.is_running = False
            logger.info("Local LLM server stopped")
            return True
        
        except Exception as e:
            logger.exception(f"Error stopping local LLM server: {e}")
            return False
    
    async def close(self):
        """Close the Local LLM connector."""
        logger.info("Closing Local LLM connector")
        
        # Stop the server if running
        if self.is_running:
            await self.stop_server()
        
        # Close the session
        if self.session:
            self.session.close()
            self.session = None
        
        logger.info("Local LLM connector closed")
        
        return True
