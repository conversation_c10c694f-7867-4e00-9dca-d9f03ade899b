@echo off
echo Starting UI-TARS with browser automation...

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Get URL from user
set /p URL="Enter URL to open (leave empty for default): "

REM Start UI-TARS with browser automation
if "%URL%"=="" (
    python ui_tars\main.py --start --browser
) else (
    python ui_tars\main.py --start --browser --url %URL%
)

exit /b 0
