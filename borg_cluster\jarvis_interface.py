"""
Jarvis Interface for the Borg Cluster Management System.

This module provides a centralized command interface for the system,
allowing for natural language interaction, system monitoring, and control.
"""
import asyncio
import json
import logging
import os
import re
import cmd
import shlex
from typing import Dict, List, Optional, Any, Union, Set, Tuple
import uuid
from datetime import datetime, timedelta
import threading
import queue
import time
import sys
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

from core.logger import setup_logger
from core.state_manager import StateManager
from borg_cluster.borg_resource_manager import BorgResourceManager, ResourceType
from borg_cluster.mpc_server_discovery import MPCServerDiscovery
from borg_cluster.borg_load_balancer import BorgLoadBalancer

# Set up logger
logger = setup_logger("jarvis_interface")

class JarvisInterface:
    """
    Jarvis Interface for the Borg Cluster Management System.

    This class provides a centralized command interface for the system,
    allowing for natural language interaction, system monitoring, and control.
    """

    def __init__(
        self,
        state_manager: StateManager,
        resource_manager: BorgResourceManager,
        server_discovery: MPCServerDiscovery,
        load_balancer: BorgLoadBalancer,
        config: Dict = None,
    ):
        """
        Initialize the Jarvis Interface.

        Args:
            state_manager (StateManager): System state manager
            resource_manager (BorgResourceManager): Borg resource manager
            server_discovery (MPCServerDiscovery): MPC server discovery
            load_balancer (BorgLoadBalancer): Borg load balancer
            config (Dict, optional): Configuration options
        """
        self.state_manager = state_manager
        self.resource_manager = resource_manager
        self.server_discovery = server_discovery
        self.load_balancer = load_balancer
        self.config = config or {}

        # Command processing
        self.command_history = []
        self.command_aliases = {}

        # Voice interaction
        self.voice_enabled = self.config.get("voice_enabled", False)
        self.voice_input_queue = queue.Queue()
        self.voice_output_queue = queue.Queue()

        # System monitoring
        self.monitoring_interval = self.config.get("monitoring_interval", 10)  # seconds
        self.monitoring_data = {}

        # Background tasks
        self.monitor_task = None
        self.voice_task = None
        self.command_processor = None

        # Command line interface
        self.cli = None
        self.cli_thread = None

        logger.info("Jarvis Interface initialized")

    async def initialize(self):
        """Initialize the Jarvis Interface."""
        try:
            # Load existing state if available
            jarvis_state = await self.state_manager.get_state("borg", "jarvis_interface")
            if jarvis_state:
                # Restore command history
                if "command_history" in jarvis_state:
                    self.command_history = jarvis_state["command_history"]

                # Restore command aliases
                if "command_aliases" in jarvis_state:
                    self.command_aliases = jarvis_state["command_aliases"]

                logger.info("Restored Jarvis Interface state")

            # Initialize command aliases
            await self._initialize_command_aliases()

            # Start monitoring task
            self.monitor_task = asyncio.create_task(self._monitor_system())

            # Start voice task if enabled
            if self.voice_enabled:
                self.voice_task = asyncio.create_task(self._process_voice())

            # Start command line interface
            self.cli = JarvisCLI(self)
            self.cli_thread = threading.Thread(target=self._run_cli)
            self.cli_thread.daemon = True
            self.cli_thread.start()

            logger.info("Jarvis Interface initialized successfully")

        except Exception as e:
            logger.exception(f"Error initializing Jarvis Interface: {e}")
            raise

    async def _initialize_command_aliases(self):
        """Initialize command aliases."""
        # System commands
        self.command_aliases["help"] = ["?", "h", "commands"]
        self.command_aliases["status"] = ["stat", "info", "system"]
        self.command_aliases["exit"] = ["quit", "bye", "close"]

        # Resource commands
        self.command_aliases["resources"] = ["res", "resource"]
        self.command_aliases["allocate"] = ["alloc", "assign"]
        self.command_aliases["release"] = ["free", "deallocate"]

        # Server commands
        self.command_aliases["servers"] = ["server", "mpc", "mpc-servers"]
        self.command_aliases["discover"] = ["scan", "find-servers"]

        # Task commands
        self.command_aliases["tasks"] = ["task", "jobs"]
        self.command_aliases["create-task"] = ["new-task", "add-task"]
        self.command_aliases["cancel-task"] = ["stop-task", "abort-task"]

        # Agent commands
        self.command_aliases["agents"] = ["agent", "list-agents"]
        self.command_aliases["agent-status"] = ["agent-info"]

    async def process_command(self, command: str) -> str:
        """
        Process a command.

        Args:
            command (str): Command to process

        Returns:
            str: Command result
        """
        try:
            # Add to command history
            self.command_history.append({
                "command": command,
                "timestamp": datetime.now().isoformat(),
            })

            # Trim command history if needed
            if len(self.command_history) > 100:
                self.command_history = self.command_history[-100:]

            # Save state
            await self._save_state()

            # Parse command
            parts = shlex.split(command.strip())
            if not parts:
                return "Please enter a command."

            cmd_name = parts[0].lower()
            args = parts[1:]

            # Check for aliases
            for actual_cmd, aliases in self.command_aliases.items():
                if cmd_name in aliases:
                    cmd_name = actual_cmd
                    break

            # Process command
            if cmd_name == "help":
                return await self._cmd_help(args)
            elif cmd_name == "status":
                return await self._cmd_status(args)
            elif cmd_name == "resources":
                return await self._cmd_resources(args)
            elif cmd_name == "servers":
                return await self._cmd_servers(args)
            elif cmd_name == "discover":
                return await self._cmd_discover(args)
            elif cmd_name == "tasks":
                return await self._cmd_tasks(args)
            elif cmd_name == "agents":
                return await self._cmd_agents(args)
            elif cmd_name == "allocate":
                return await self._cmd_allocate(args)
            elif cmd_name == "release":
                return await self._cmd_release(args)
            elif cmd_name == "create-task":
                return await self._cmd_create_task(args)
            elif cmd_name == "cancel-task":
                return await self._cmd_cancel_task(args)
            elif cmd_name == "agent-status":
                return await self._cmd_agent_status(args)
            elif cmd_name == "exit":
                return "Exiting Jarvis Interface..."
            else:
                return f"Unknown command: {cmd_name}. Type 'help' for a list of commands."

        except Exception as e:
            logger.exception(f"Error processing command: {e}")
            return f"Error: {str(e)}"

    async def _cmd_help(self, args: List[str]) -> str:
        """
        Process the 'help' command.

        Args:
            args (List[str]): Command arguments

        Returns:
            str: Command result
        """
        if not args:
            # General help
            return """
Jarvis Interface Commands:

System Commands:
  help                 Show this help message
  status               Show system status
  exit                 Exit the Jarvis Interface

Resource Commands:
  resources            List available resources
  allocate             Allocate a resource
  release              Release a resource allocation

Server Commands:
  servers              List MPC servers
  discover             Discover MPC servers

Task Commands:
  tasks                List tasks
  create-task          Create a new task
  cancel-task          Cancel a task

Agent Commands:
  agents               List agents
  agent-status         Show agent status

Type 'help <command>' for more information on a specific command.
"""
        else:
            # Help for specific command
            cmd_name = args[0].lower()

            # Check for aliases
            for actual_cmd, aliases in self.command_aliases.items():
                if cmd_name in aliases:
                    cmd_name = actual_cmd
                    break

            if cmd_name == "status":
                return "status: Show system status\n\nUsage: status [component]\n\nComponents: resources, servers, tasks, agents"
            elif cmd_name == "resources":
                return "resources: List available resources\n\nUsage: resources [type]\n\nTypes: cpu, memory, gpu, mpc_server, network, storage"
            elif cmd_name == "servers":
                return "servers: List MPC servers\n\nUsage: servers [active|all]"
            elif cmd_name == "discover":
                return "discover: Discover MPC servers\n\nUsage: discover"
            elif cmd_name == "tasks":
                return "tasks: List tasks\n\nUsage: tasks [status]\n\nStatus: pending, assigned, running, completed, failed, canceled"
            elif cmd_name == "agents":
                return "agents: List agents\n\nUsage: agents"
            elif cmd_name == "allocate":
                return "allocate: Allocate a resource\n\nUsage: allocate <resource_type> <agent_id> <amount> [priority] [resource_id]"
            elif cmd_name == "release":
                return "release: Release a resource allocation\n\nUsage: release <allocation_id>"
            elif cmd_name == "create-task":
                return "create-task: Create a new task\n\nUsage: create-task <task_type> <parameters_json> [priority] [capabilities_json]"
            elif cmd_name == "cancel-task":
                return "cancel-task: Cancel a task\n\nUsage: cancel-task <task_id>"
            elif cmd_name == "agent-status":
                return "agent-status: Show agent status\n\nUsage: agent-status <agent_id>"
            elif cmd_name == "exit":
                return "exit: Exit the Jarvis Interface\n\nUsage: exit"
            else:
                return f"No help available for command: {cmd_name}"

    async def _cmd_status(self, args: List[str]) -> str:
        """
        Process the 'status' command.

        Args:
            args (List[str]): Command arguments

        Returns:
            str: Command result
        """
        component = args[0].lower() if args else "all"

        if component == "all":
            # Get overall system status
            result = "System Status:\n\n"

            # Resources
            cpu_resources = await self.resource_manager.get_resource_status(ResourceType.CPU)
            memory_resources = await self.resource_manager.get_resource_status(ResourceType.MEMORY)

            result += f"CPU Usage: {cpu_resources.get('local', {}).get('usage_percent', 0):.1f}%\n"
            result += f"Memory Usage: {memory_resources.get('local', {}).get('usage_percent', 0):.1f}%\n"

            # Servers
            active_servers = await self.server_discovery.get_active_servers()
            result += f"Active MPC Servers: {len(active_servers)}\n"

            # Tasks
            tasks = self.load_balancer.tasks
            pending_tasks = sum(1 for t in tasks.values() if t["status"] == "pending")
            running_tasks = sum(1 for t in tasks.values() if t["status"] == "running")

            result += f"Tasks: {len(tasks)} total, {pending_tasks} pending, {running_tasks} running\n"

            return result
        elif component == "resources":
            # Get resource status
            cpu_resources = await self.resource_manager.get_resource_status(ResourceType.CPU)
            memory_resources = await self.resource_manager.get_resource_status(ResourceType.MEMORY)
            gpu_resources = await self.resource_manager.get_resource_status(ResourceType.GPU)

            result = "Resource Status:\n\n"

            # CPU
            result += "CPU Resources:\n"
            for rid, resource in cpu_resources.items():
                result += f"  {rid}: {resource.get('usage_percent', 0):.1f}% used, "
                result += f"{resource.get('allocated', 0)}/{resource.get('total', 0)} cores allocated\n"

            # Memory
            result += "\nMemory Resources:\n"
            for rid, resource in memory_resources.items():
                total_gb = resource.get('total', 0) / (1024 ** 3)
                available_gb = resource.get('available', 0) / (1024 ** 3)
                result += f"  {rid}: {resource.get('usage_percent', 0):.1f}% used, "
                result += f"{total_gb - available_gb:.1f}/{total_gb:.1f} GB allocated\n"

            # GPU
            if gpu_resources:
                result += "\nGPU Resources:\n"
                for rid, resource in gpu_resources.items():
                    total_gb = resource.get('total', 0) / (1024 ** 3)
                    available_gb = resource.get('available', 0) / (1024 ** 3)
                    result += f"  {rid} ({resource.get('name', 'Unknown')}): "
                    result += f"{resource.get('usage_percent', 0):.1f}% used, "
                    result += f"{total_gb - available_gb:.1f}/{total_gb:.1f} GB allocated\n"

            return result
        elif component == "servers":
            # Get server status
            active_servers = await self.server_discovery.get_active_servers()

            result = "Server Status:\n\n"

            if not active_servers:
                result += "No active MPC servers found.\n"
            else:
                result += f"Active MPC Servers ({len(active_servers)}):\n"
                for server in active_servers:
                    server_id = server.get("server_id", "Unknown")
                    server_host = server.get("server_host", "Unknown")
                    server_port = server.get("server_port", "Unknown")
                    server_type = server.get("server_type", "standard")

                    result += f"  {server_id} ({server_type}): {server_host}:{server_port}\n"

            return result
        elif component == "tasks":
            # Get task status
            tasks = self.load_balancer.tasks

            result = "Task Status:\n\n"

            if not tasks:
                result += "No tasks found.\n"
            else:
                # Group tasks by status
                tasks_by_status = {}
                for task_id, task in tasks.items():
                    status = task.get("status", "unknown")
                    if status not in tasks_by_status:
                        tasks_by_status[status] = []
                    tasks_by_status[status].append(task)

                # Show counts
                result += f"Tasks ({len(tasks)} total):\n"
                for status, status_tasks in tasks_by_status.items():
                    result += f"  {status}: {len(status_tasks)}\n"

                # Show recent tasks
                result += "\nRecent Tasks:\n"
                recent_tasks = sorted(
                    tasks.values(),
                    key=lambda t: t.get("updated_at", ""),
                    reverse=True,
                )[:5]

                for task in recent_tasks:
                    task_id = task.get("id", "Unknown")
                    task_type = task.get("type", "Unknown")
                    task_status = task.get("status", "Unknown")
                    agent_id = task.get("agent_id", "None")

                    result += f"  {task_id[:8]}: {task_type} ({task_status}) - Agent: {agent_id}\n"

            return result
        elif component == "agents":
            # Get agent status
            agent_capabilities = self.load_balancer.agent_capabilities
            agent_tasks = self.load_balancer.agent_tasks

            result = "Agent Status:\n\n"

            if not agent_capabilities:
                result += "No agents registered.\n"
            else:
                result += f"Agents ({len(agent_capabilities)}):\n"
                for agent_id, capabilities in agent_capabilities.items():
                    task_count = len(agent_tasks.get(agent_id, set()))
                    result += f"  {agent_id}: {task_count} active tasks, {len(capabilities)} capabilities\n"

            return result
        else:
            return f"Unknown component: {component}"

    async def _cmd_resources(self, args: List[str]) -> str:
        """
        Process the 'resources' command.

        Args:
            args (List[str]): Command arguments

        Returns:
            str: Command result
        """
        resource_type = args[0].upper() if args else None

        if resource_type:
            # Check if resource type is valid
            if not hasattr(ResourceType, resource_type):
                return f"Invalid resource type: {resource_type}"

            # Get resources of specified type
            resources = await self.resource_manager.get_resource_status(getattr(ResourceType, resource_type))

            result = f"{resource_type} Resources:\n\n"

            if not resources:
                result += f"No {resource_type} resources found.\n"
            else:
                for rid, resource in resources.items():
                    result += f"Resource ID: {rid}\n"
                    for key, value in resource.items():
                        if key not in ["id"]:
                            result += f"  {key}: {value}\n"
                    result += "\n"

            return result
        else:
            # Get all resources
            resources = await self.resource_manager.get_resource_status()

            result = "Resources:\n\n"

            for resource_type, type_resources in resources.items():
                result += f"{resource_type} Resources ({len(type_resources)}):\n"
                for rid in type_resources:
                    result += f"  {rid}\n"
                result += "\n"

            return result

    async def _cmd_servers(self, args: List[str]) -> str:
        """
        Process the 'servers' command.

        Args:
            args (List[str]): Command arguments

        Returns:
            str: Command result
        """
        server_filter = args[0].lower() if args else "active"

        if server_filter == "active":
            # Get active servers
            active_servers = await self.server_discovery.get_active_servers()

            result = "Active MPC Servers:\n\n"

            if not active_servers:
                result += "No active MPC servers found.\n"
            else:
                for server in active_servers:
                    server_id = server.get("server_id", "Unknown")
                    server_host = server.get("server_host", "Unknown")
                    server_port = server.get("server_port", "Unknown")
                    server_type = server.get("server_type", "standard")

                    result += f"Server ID: {server_id}\n"
                    result += f"  Type: {server_type}\n"
                    result += f"  Host: {server_host}\n"
                    result += f"  Port: {server_port}\n"

                    # Show additional info if available
                    server_info = server.get("server_info", {})
                    if server_info:
                        result += "  Server Info:\n"
                        for key, value in server_info.items():
                            result += f"    {key}: {value}\n"

                    result += "\n"

            return result
        elif server_filter == "all":
            # Get all known servers
            known_servers = self.server_discovery.known_servers

            result = "All Known MPC Servers:\n\n"

            if not known_servers:
                result += "No MPC servers found.\n"
            else:
                for server_key, server in known_servers.items():
                    server_id = server.get("server_id", "Unknown")
                    server_host = server.get("server_host", "Unknown")
                    server_port = server.get("server_port", "Unknown")
                    server_type = server.get("server_type", "standard")
                    active = server.get("active", False)

                    result += f"Server ID: {server_id}\n"
                    result += f"  Type: {server_type}\n"
                    result += f"  Host: {server_host}\n"
                    result += f"  Port: {server_port}\n"
                    result += f"  Active: {active}\n"
                    result += f"  Last Seen: {server.get('last_seen', 'Never')}\n"

                    result += "\n"

            return result
        else:
            return f"Invalid filter: {server_filter}. Use 'active' or 'all'."

    async def _cmd_discover(self, _args: List[str]) -> str:
        """
        Process the 'discover' command.

        Args:
            args (List[str]): Command arguments

        Returns:
            str: Command result
        """
        # Trigger server discovery
        await self.server_discovery._discover_servers_scan()

        # Get active servers
        active_servers = await self.server_discovery.get_active_servers()

        result = "MPC Server Discovery Results:\n\n"

        if not active_servers:
            result += "No active MPC servers found.\n"
        else:
            result += f"Found {len(active_servers)} active MPC servers:\n\n"

            for server in active_servers:
                server_id = server.get("server_id", "Unknown")
                server_host = server.get("server_host", "Unknown")
                server_port = server.get("server_port", "Unknown")
                server_type = server.get("server_type", "standard")

                result += f"Server ID: {server_id}\n"
                result += f"  Type: {server_type}\n"
                result += f"  Host: {server_host}\n"
                result += f"  Port: {server_port}\n"
                result += "\n"

        return result

    async def _cmd_tasks(self, args: List[str]) -> str:
        """
        Process the 'tasks' command.

        Args:
            args (List[str]): Command arguments

        Returns:
            str: Command result
        """
        status_filter = args[0].lower() if args else None

        # Get tasks
        tasks = self.load_balancer.tasks

        result = "Tasks:\n\n"

        if not tasks:
            result += "No tasks found.\n"
        else:
            # Filter tasks by status if specified
            if status_filter:
                filtered_tasks = {
                    task_id: task
                    for task_id, task in tasks.items()
                    if task.get("status", "").lower() == status_filter
                }
            else:
                filtered_tasks = tasks

            if not filtered_tasks:
                result += f"No tasks found with status: {status_filter}\n"
            else:
                result += f"Tasks ({len(filtered_tasks)}):\n\n"

                for task_id, task in filtered_tasks.items():
                    task_type = task.get("type", "Unknown")
                    task_status = task.get("status", "Unknown")
                    agent_id = task.get("agent_id", "None")
                    created_at = task.get("created_at", "Unknown")
                    updated_at = task.get("updated_at", "Unknown")

                    result += f"Task ID: {task_id}\n"
                    result += f"  Type: {task_type}\n"
                    result += f"  Status: {task_status}\n"
                    result += f"  Agent: {agent_id}\n"
                    result += f"  Created: {created_at}\n"
                    result += f"  Updated: {updated_at}\n"

                    # Show parameters if not too large
                    parameters = task.get("parameters", {})
                    if parameters and len(str(parameters)) < 200:
                        result += f"  Parameters: {parameters}\n"

                    # Show result if available and not too large
                    task_result = task.get("result")
                    if task_result is not None and len(str(task_result)) < 200:
                        result += f"  Result: {task_result}\n"

                    # Show error if available
                    error = task.get("error")
                    if error:
                        result += f"  Error: {error}\n"

                    result += "\n"

        return result

    async def _cmd_agents(self, _args: List[str]) -> str:
        """
        Process the 'agents' command.

        Args:
            args (List[str]): Command arguments

        Returns:
            str: Command result
        """
        # Get agent information
        agent_capabilities = self.load_balancer.agent_capabilities
        agent_tasks = self.load_balancer.agent_tasks
        agent_performance = self.load_balancer.agent_performance

        result = "Agents:\n\n"

        if not agent_capabilities:
            result += "No agents registered.\n"
        else:
            for agent_id, capabilities in agent_capabilities.items():
                result += f"Agent ID: {agent_id}\n"

                # Show capabilities
                result += f"  Capabilities ({len(capabilities)}):\n"
                for capability in capabilities:
                    result += f"    {capability}\n"

                # Show active tasks
                active_task_ids = agent_tasks.get(agent_id, set())
                result += f"  Active Tasks ({len(active_task_ids)}):\n"
                for task_id in active_task_ids:
                    if task_id in self.load_balancer.tasks:
                        task = self.load_balancer.tasks[task_id]
                        task_type = task.get("type", "Unknown")
                        task_status = task.get("status", "Unknown")
                        result += f"    {task_id[:8]}: {task_type} ({task_status})\n"

                # Show performance metrics if available
                performance = agent_performance.get(agent_id, {})
                if performance:
                    result += "  Performance Metrics:\n"
                    for metric, value in performance.items():
                        if metric != "last_updated":
                            result += f"    {metric}: {value}\n"

                result += "\n"

        return result

    async def _cmd_allocate(self, args: List[str]) -> str:
        """
        Process the 'allocate' command.

        Args:
            args (List[str]): Command arguments

        Returns:
            str: Command result
        """
        if len(args) < 3:
            return "Usage: allocate <resource_type> <agent_id> <amount> [priority] [resource_id]"

        resource_type = args[0].upper()
        agent_id = args[1]
        amount = float(args[2])
        priority = int(args[3]) if len(args) > 3 else 0
        resource_id = args[4] if len(args) > 4 else None

        # Check if resource type is valid
        if not hasattr(ResourceType, resource_type):
            return f"Invalid resource type: {resource_type}"

        # Allocate resource
        try:
            allocation_id = await self.resource_manager.allocate_resource(
                resource_type=getattr(ResourceType, resource_type),
                agent_id=agent_id,
                amount=amount,
                priority=priority,
                resource_id=resource_id,
            )

            return f"Resource allocated successfully. Allocation ID: {allocation_id}"
        except Exception as e:
            return f"Error allocating resource: {str(e)}"

    async def _cmd_release(self, args: List[str]) -> str:
        """
        Process the 'release' command.

        Args:
            args (List[str]): Command arguments

        Returns:
            str: Command result
        """
        if len(args) < 1:
            return "Usage: release <allocation_id>"

        allocation_id = args[0]

        # Release allocation
        try:
            await self.resource_manager.release_allocation(allocation_id)
            return f"Allocation {allocation_id} released successfully."
        except Exception as e:
            return f"Error releasing allocation: {str(e)}"

    async def _cmd_create_task(self, args: List[str]) -> str:
        """
        Process the 'create-task' command.

        Args:
            args (List[str]): Command arguments

        Returns:
            str: Command result
        """
        if len(args) < 2:
            return "Usage: create-task <task_type> <parameters_json> [priority] [capabilities_json]"

        task_type = args[0]
        parameters_json = args[1]
        priority = int(args[2]) if len(args) > 2 else 50  # Default priority
        capabilities_json = args[3] if len(args) > 3 else "[]"

        # Parse JSON
        try:
            parameters = json.loads(parameters_json)
            required_capabilities = json.loads(capabilities_json)
        except json.JSONDecodeError as e:
            return f"Error parsing JSON: {str(e)}"

        # Create task
        try:
            task_id = await self.load_balancer.create_task(
                task_type=task_type,
                parameters=parameters,
                priority=priority,
                required_capabilities=required_capabilities,
            )

            return f"Task created successfully. Task ID: {task_id}"
        except Exception as e:
            return f"Error creating task: {str(e)}"

    async def _cmd_cancel_task(self, args: List[str]) -> str:
        """
        Process the 'cancel-task' command.

        Args:
            args (List[str]): Command arguments

        Returns:
            str: Command result
        """
        if len(args) < 1:
            return "Usage: cancel-task <task_id>"

        task_id = args[0]

        # Cancel task
        try:
            await self.load_balancer.update_task_status(
                task_id=task_id,
                status="canceled",
                error="Canceled by user",
            )

            return f"Task {task_id} canceled successfully."
        except Exception as e:
            return f"Error canceling task: {str(e)}"

    async def _cmd_agent_status(self, args: List[str]) -> str:
        """
        Process the 'agent-status' command.

        Args:
            args (List[str]): Command arguments

        Returns:
            str: Command result
        """
        if len(args) < 1:
            return "Usage: agent-status <agent_id>"

        agent_id = args[0]

        # Get agent information
        agent_capabilities = self.load_balancer.agent_capabilities.get(agent_id)
        agent_tasks = self.load_balancer.agent_tasks.get(agent_id, set())
        agent_performance = self.load_balancer.agent_performance.get(agent_id, {})

        if not agent_capabilities:
            return f"Agent {agent_id} not found."

        result = f"Agent Status: {agent_id}\n\n"

        # Show capabilities
        result += f"Capabilities ({len(agent_capabilities)}):\n"
        for capability in agent_capabilities:
            result += f"  {capability}\n"

        # Show active tasks
        result += f"\nActive Tasks ({len(agent_tasks)}):\n"
        for task_id in agent_tasks:
            if task_id in self.load_balancer.tasks:
                task = self.load_balancer.tasks[task_id]
                task_type = task.get("type", "Unknown")
                task_status = task.get("status", "Unknown")
                created_at = task.get("created_at", "Unknown")
                result += f"  {task_id}: {task_type} ({task_status}) - Created: {created_at}\n"

        # Show performance metrics if available
        if agent_performance:
            result += "\nPerformance Metrics:\n"
            for metric, value in agent_performance.items():
                if metric != "last_updated":
                    result += f"  {metric}: {value}\n"

        return result

    async def _monitor_system(self):
        """Monitor system resources and status."""
        while True:
            try:
                # Update monitoring data
                self.monitoring_data = {
                    "timestamp": datetime.now().isoformat(),
                    "resources": {},
                    "servers": {},
                    "tasks": {},
                    "agents": {},
                }

                # Monitor resources
                for resource_type in vars(ResourceType).values():
                    if isinstance(resource_type, str) and not resource_type.startswith("_"):
                        resources = await self.resource_manager.get_resource_status(resource_type)
                        self.monitoring_data["resources"][resource_type] = resources

                # Monitor servers
                active_servers = await self.server_discovery.get_active_servers()
                self.monitoring_data["servers"]["active"] = active_servers
                self.monitoring_data["servers"]["count"] = len(active_servers)

                # Monitor tasks
                tasks = self.load_balancer.tasks
                self.monitoring_data["tasks"]["all"] = tasks
                self.monitoring_data["tasks"]["count"] = len(tasks)
                self.monitoring_data["tasks"]["pending"] = sum(1 for t in tasks.values() if t["status"] == "pending")
                self.monitoring_data["tasks"]["running"] = sum(1 for t in tasks.values() if t["status"] == "running")

                # Monitor agents
                self.monitoring_data["agents"]["capabilities"] = self.load_balancer.agent_capabilities
                self.monitoring_data["agents"]["tasks"] = self.load_balancer.agent_tasks
                self.monitoring_data["agents"]["performance"] = self.load_balancer.agent_performance
                self.monitoring_data["agents"]["count"] = len(self.load_balancer.agent_capabilities)

            except Exception as e:
                logger.exception(f"Error monitoring system: {e}")

            # Sleep for monitoring interval
            await asyncio.sleep(self.monitoring_interval)

    async def _process_voice(self):
        """Process voice input and output."""
        # This is a placeholder for voice processing
        # Implement based on your specific requirements
        pass

    def _run_cli(self):
        """Run the command line interface."""
        try:
            self.cli.cmdloop()
        except Exception as e:
            logger.exception(f"Error in CLI: {e}")

    async def _save_state(self):
        """Save the current state to the state manager."""
        try:
            # Create state
            state = {
                "command_history": self.command_history,
                "command_aliases": self.command_aliases,
                "last_updated": datetime.now().isoformat(),
            }

            # Save state
            await self.state_manager.update_state("borg", "jarvis_interface", state)

        except Exception as e:
            logger.exception(f"Error saving Jarvis Interface state: {e}")

    async def close(self):
        """Close the Jarvis Interface."""
        try:
            # Cancel background tasks
            if self.monitor_task:
                self.monitor_task.cancel()
                try:
                    await self.monitor_task
                except asyncio.CancelledError:
                    pass

            if self.voice_task:
                self.voice_task.cancel()
                try:
                    await self.voice_task
                except asyncio.CancelledError:
                    pass

            # Save final state
            await self._save_state()

            logger.info("Jarvis Interface closed")

        except Exception as e:
            logger.exception(f"Error closing Jarvis Interface: {e}")
            raise


class JarvisCLI(cmd.Cmd):
    """Command line interface for the Jarvis Interface."""

    intro = "Welcome to the Jarvis Interface. Type 'help' for a list of commands."
    prompt = "Jarvis> "

    def __init__(self, jarvis_interface):
        """
        Initialize the Jarvis CLI.

        Args:
            jarvis_interface (JarvisInterface): Jarvis interface
        """
        super().__init__()
        self.jarvis = jarvis_interface
        self.loop = asyncio.get_event_loop()

    def default(self, line):
        """Process a command."""
        result = self.loop.run_until_complete(self.jarvis.process_command(line))
        print(result)

        if line.lower() in ["exit", "quit", "bye"]:
            return True

    def emptyline(self):
        """Handle empty line."""
        pass

    def do_exit(self, _arg):
        """Exit the Jarvis CLI."""
        print("Exiting Jarvis Interface...")
        return True

    def do_EOF(self, _arg):
        """Handle EOF."""
        print("Exiting Jarvis Interface...")
        return True