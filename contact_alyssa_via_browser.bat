@echo off
echo Contact Alyssa via Browser
echo =========================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Check if Selenium is installed
python -c "import selenium" >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing Selenium...
    pip install selenium webdriver-manager
)

REM Ask for Gmail credentials
echo.
echo Enter Gmail email address (default: <EMAIL>):
set /p EMAIL=""
if "%EMAIL%"=="" set EMAIL=<EMAIL>

echo.
echo Enter Gmail password (default: GodisSoGood!777):
set /p PASSWORD=""
if "%PASSWORD%"=="" set PASSWORD=GodisSoGood!777

REM Ask for recipient
echo.
echo Enter recipient email address (default: <EMAIL>):
set /p RECIPIENT=""
if "%RECIPIENT%"=="" set RECIPIENT=<EMAIL>

REM Ask for dry run option
echo.
echo Do you want to perform a dry run (don't actually send the email)? (Y/N, default: N)
set /p DRY_RUN=""
if /i "%DRY_RUN%"=="Y" (
    set DRY_RUN_OPTION=--dry-run
) else (
    set DRY_RUN_OPTION=
)

REM Run the contact script
echo.
echo Running contact script...
echo.

set COMMAND=python contact_alyssa_via_browser.py --email "%EMAIL%" --password "%PASSWORD%" --recipient "%RECIPIENT%" %DRY_RUN_OPTION% --debug

echo Executing: %COMMAND%
echo.

%COMMAND%

echo.
if %errorlevel% equ 0 (
    echo Test completed successfully!
) else (
    echo Test failed. Please check the error messages above.
)

echo.
pause
