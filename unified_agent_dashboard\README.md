# Unified Agent Dashboard

A comprehensive dashboard for monitoring and controlling all agents in the Multi-Agent AI System.

## Features

- **Real-time Agent Monitoring**: Monitor the status and activities of all agents in the system.
- **Workflow Visualization**: Visualize agent workflows and task dependencies.
- **Browser Automation Visualization**: See browser automation activities performed by UI-TARS.
- **Command Center**: Send commands to agents and view results.
- **Google ADK Integration**: Integrate with Google's Agent Development Kit for enhanced multi-agent capabilities.
- **Agent2Agent Protocol**: Enable communication between agents using the A2A protocol.

## Components

### GUI Components

- **Main Dashboard**: The main dashboard interface.
- **Agent Monitor**: Monitor agent status and activities.
- **Workflow Visualizer**: Visualize agent workflows.
- **Command Center**: Send commands to agents.
- **Browser Automation View**: View browser automation activities.

### Web Components

- **API**: RESTful API for interacting with the dashboard.
- **WebSocket**: Real-time updates via WebSocket.

### Integrations

- **Google ADK Integration**: Integration with Google's Agent Development Kit.
- **UI-TARS Integration**: Integration with UI-TARS for browser automation.
- **CrewAI Integration**: Integration with CrewAI for agent crews.
- **AlphaEvolve Integration**: Integration with AlphaEvolve for evolutionary algorithms.
- **Jarvis Integration**: Integration with Jarvis for voice commands.

## Usage

### Running the Dashboard

```bash
python run_unified_dashboard.py
```

### Command Line Arguments

- `--config`: Path to configuration file.
- `--debug`: Enable debug mode.

### Configuration

The dashboard can be configured via the settings tab or by editing the `config/unified_dashboard_settings.json` file.

## Requirements

- Python 3.8+
- Tkinter
- Matplotlib
- NetworkX
- Pillow
- Requests

## License

This project is licensed under the MIT License - see the LICENSE file for details.
