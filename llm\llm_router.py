"""
LLM router for the Multi-Agent AI System.
"""
from typing import Dict, List, Optional, Any, Union
import asyncio
import importlib
from datetime import datetime

import config
from core.logger import setup_logger
from llm.llm_connector import LLMConnector

# Set up logger
logger = setup_logger("llm_router")

class LLMRouter:
    """
    Routes requests to appropriate LLM providers.
    """
    
    def __init__(self):
        """Initialize the LLM router."""
        self.connectors: Dict[str, LLMConnector] = {}
        self.default_provider = config.DEFAULT_LLM_PROVIDER
    
    async def initialize(self):
        """Initialize and load all enabled LLM connectors."""
        logger.info("Initializing LLM router")
        
        # Load all enabled LLM connectors
        for provider, provider_config in config.LLM_CONFIG.items():
            if provider_config.get("enabled", False):
                try:
                    # Import the connector module
                    module_name = f"llm.{provider}_connector"
                    class_name = f"{provider.capitalize()}Connector"
                    
                    try:
                        module = importlib.import_module(module_name)
                        connector_class = getattr(module, class_name)
                    except (ImportError, AttributeError) as e:
                        logger.error(f"Failed to import LLM connector for {provider}: {e}")
                        continue
                    
                    # Create connector instance
                    connector = connector_class(provider_config)
                    
                    if connector.is_enabled():
                        self.connectors[provider] = connector
                        logger.info(f"Loaded LLM connector: {provider}")
                    else:
                        logger.warning(f"LLM connector {provider} is disabled")
                
                except Exception as e:
                    logger.exception(f"Error loading LLM connector for {provider}: {e}")
        
        if not self.connectors:
            logger.warning("No LLM connectors loaded")
        else:
            logger.info(f"Loaded {len(self.connectors)} LLM connectors")
            
            # Verify default provider
            if self.default_provider not in self.connectors:
                if self.connectors:
                    self.default_provider = next(iter(self.connectors.keys()))
                    logger.warning(f"Default provider {config.DEFAULT_LLM_PROVIDER} not available, using {self.default_provider}")
                else:
                    logger.error("No LLM providers available")
    
    async def generate_text(
        self,
        prompt: str,
        provider: Optional[str] = None,
        model: Optional[str] = None,
        max_tokens: int = 1000,
        temperature: float = 0.7,
        stop_sequences: Optional[List[str]] = None,
        **kwargs
    ) -> Dict:
        """
        Generate text using the specified LLM provider.
        
        Args:
            prompt (str): Input prompt
            provider (Optional[str]): LLM provider to use
            model (Optional[str]): Model to use
            max_tokens (int): Maximum number of tokens to generate
            temperature (float): Sampling temperature
            stop_sequences (Optional[List[str]]): Sequences that stop generation
            **kwargs: Additional model-specific parameters
            
        Returns:
            Dict: Response containing generated text and metadata
        """
        # Determine provider to use
        provider = provider or self.default_provider
        
        # Check if provider is available
        if provider not in self.connectors:
            logger.warning(f"Provider {provider} not available, using {self.default_provider}")
            provider = self.default_provider
            
            # If default provider also not available, return error
            if provider not in self.connectors:
                return {"error": "No LLM providers available"}
        
        # Generate text using selected provider
        return await self.connectors[provider].generate_text(
            prompt=prompt,
            model=model,
            max_tokens=max_tokens,
            temperature=temperature,
            stop_sequences=stop_sequences,
            **kwargs
        )
    
    async def generate_chat(
        self,
        messages: List[Dict[str, str]],
        provider: Optional[str] = None,
        model: Optional[str] = None,
        max_tokens: int = 1000,
        temperature: float = 0.7,
        stop_sequences: Optional[List[str]] = None,
        **kwargs
    ) -> Dict:
        """
        Generate a chat response using the specified LLM provider.
        
        Args:
            messages (List[Dict[str, str]]): List of message dictionaries
            provider (Optional[str]): LLM provider to use
            model (Optional[str]): Model to use
            max_tokens (int): Maximum number of tokens to generate
            temperature (float): Sampling temperature
            stop_sequences (Optional[List[str]]): Sequences that stop generation
            **kwargs: Additional model-specific parameters
            
        Returns:
            Dict: Response containing generated text and metadata
        """
        # Determine provider to use
        provider = provider or self.default_provider
        
        # Check if provider is available
        if provider not in self.connectors:
            logger.warning(f"Provider {provider} not available, using {self.default_provider}")
            provider = self.default_provider
            
            # If default provider also not available, return error
            if provider not in self.connectors:
                return {"error": "No LLM providers available"}
        
        # Generate chat response using selected provider
        return await self.connectors[provider].generate_chat(
            messages=messages,
            model=model,
            max_tokens=max_tokens,
            temperature=temperature,
            stop_sequences=stop_sequences,
            **kwargs
        )
    
    async def embed_text(
        self,
        text: Union[str, List[str]],
        provider: Optional[str] = None,
        model: Optional[str] = None,
        **kwargs
    ) -> Dict:
        """
        Generate embeddings for text using the specified LLM provider.
        
        Args:
            text (Union[str, List[str]]): Text to embed
            provider (Optional[str]): LLM provider to use
            model (Optional[str]): Model to use
            **kwargs: Additional model-specific parameters
            
        Returns:
            Dict: Response containing embeddings and metadata
        """
        # Determine provider to use
        provider = provider or self.default_provider
        
        # Check if provider is available
        if provider not in self.connectors:
            logger.warning(f"Provider {provider} not available, using {self.default_provider}")
            provider = self.default_provider
            
            # If default provider also not available, return error
            if provider not in self.connectors:
                return {"error": "No LLM providers available"}
        
        # Generate embeddings using selected provider
        return await self.connectors[provider].embed_text(
            text=text,
            model=model,
            **kwargs
        )
    
    def get_available_providers(self) -> List[str]:
        """
        Get list of available LLM providers.
        
        Returns:
            List[str]: List of available provider names
        """
        return list(self.connectors.keys())
    
    def get_available_models(self, provider: Optional[str] = None) -> Dict[str, List[str]]:
        """
        Get available models for specified provider or all providers.
        
        Args:
            provider (Optional[str]): Provider to get models for, or None for all
            
        Returns:
            Dict[str, List[str]]: Dictionary of provider -> list of models
        """
        if provider:
            if provider in self.connectors:
                return {provider: self.connectors[provider].get_models()}
            return {}
        
        return {
            provider: connector.get_models()
            for provider, connector in self.connectors.items()
        }
    
    def get_default_provider(self) -> str:
        """
        Get the default LLM provider.
        
        Returns:
            str: Default provider name
        """
        return self.default_provider
