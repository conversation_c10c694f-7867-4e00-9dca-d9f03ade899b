Write-Host "Starting <PERSON> with AlphaEvolve integration..." -ForegroundColor Cyan

# Set the current directory to the script directory
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location -Path $scriptPath

# Start Jarvis with AlphaEvolve integration
python start_jarvis_with_alphaevolve.py --interactive

Write-Host "Press any key to exit..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
