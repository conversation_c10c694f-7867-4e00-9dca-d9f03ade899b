"""
Script to test trading services.
"""
import sys
import asyncio
import argparse
from pathlib import Path
import json

from services.trading_service import TradingServiceFactory
from core.logger import setup_logger
import config

# Set up logger
logger = setup_logger("test_trading")

async def test_account_info(service_type: str):
    """
    Test getting account information.
    
    Args:
        service_type (str): Trading service type (alpaca/crypto)
    """
    logger.info(f"Testing {service_type} account information")
    
    # Create trading service
    trading_service = TradingServiceFactory.create_service(service_type)
    
    if not trading_service:
        logger.error(f"{service_type} trading service not available")
        return False
    
    if not trading_service.is_enabled():
        logger.error(f"{service_type} trading service is not enabled")
        logger.info(f"Check your .env file for {service_type.upper()}_API_KEY and {service_type.upper()}_API_SECRET")
        return False
    
    # Get account information
    logger.info(f"Getting {service_type} account information")
    result = await trading_service.get_account_info()
    
    # Check result
    if "error" in result:
        logger.error(f"Error getting account information: {result['error']}")
        return False
    
    # Print account information
    logger.info(f"{service_type} account information:")
    print(json.dumps(result, indent=2))
    
    return True

async def test_market_data(service_type: str, symbol: str):
    """
    Test getting market data.
    
    Args:
        service_type (str): Trading service type (alpaca/crypto)
        symbol (str): Trading symbol
    """
    logger.info(f"Testing {service_type} market data for {symbol}")
    
    # Create trading service
    trading_service = TradingServiceFactory.create_service(service_type)
    
    if not trading_service:
        logger.error(f"{service_type} trading service not available")
        return False
    
    if not trading_service.is_enabled():
        logger.error(f"{service_type} trading service is not enabled")
        logger.info(f"Check your .env file for {service_type.upper()}_API_KEY and {service_type.upper()}_API_SECRET")
        return False
    
    # Get market data
    logger.info(f"Getting {service_type} market data for {symbol}")
    result = await trading_service.get_market_data(symbol)
    
    # Check result
    if "error" in result:
        logger.error(f"Error getting market data: {result['error']}")
        return False
    
    # Print market data
    logger.info(f"{service_type} market data for {symbol}:")
    print(json.dumps(result, indent=2))
    
    return True

async def test_place_order(service_type: str, symbol: str, side: str, quantity: float, order_type: str, price: float = None):
    """
    Test placing an order.
    
    Args:
        service_type (str): Trading service type (alpaca/crypto)
        symbol (str): Trading symbol
        side (str): Order side (buy/sell)
        quantity (float): Order quantity
        order_type (str): Order type (market/limit)
        price (float, optional): Order price (for limit orders)
    """
    logger.info(f"Testing {service_type} order placement for {symbol}")
    
    # Create trading service
    trading_service = TradingServiceFactory.create_service(service_type)
    
    if not trading_service:
        logger.error(f"{service_type} trading service not available")
        return False
    
    if not trading_service.is_enabled():
        logger.error(f"{service_type} trading service is not enabled")
        logger.info(f"Check your .env file for {service_type.upper()}_API_KEY and {service_type.upper()}_API_SECRET")
        return False
    
    # Place order
    logger.info(f"Placing {side} {order_type} order for {quantity} {symbol}")
    result = await trading_service.place_order(
        symbol=symbol,
        side=side,
        quantity=quantity,
        order_type=order_type,
        price=price
    )
    
    # Check result
    if "error" in result:
        logger.error(f"Error placing order: {result['error']}")
        return False
    
    # Print order result
    logger.info(f"{service_type} order result:")
    print(json.dumps(result, indent=2))
    
    return True

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Test trading services")
    subparsers = parser.add_subparsers(dest="command", help="Command to run")
    
    # Account info command
    account_parser = subparsers.add_parser("account", help="Get account information")
    account_parser.add_argument("service", choices=["alpaca", "crypto"], help="Trading service to use")
    
    # Market data command
    market_parser = subparsers.add_parser("market", help="Get market data")
    market_parser.add_argument("service", choices=["alpaca", "crypto"], help="Trading service to use")
    market_parser.add_argument("symbol", help="Trading symbol")
    
    # Order command
    order_parser = subparsers.add_parser("order", help="Place an order")
    order_parser.add_argument("service", choices=["alpaca", "crypto"], help="Trading service to use")
    order_parser.add_argument("symbol", help="Trading symbol")
    order_parser.add_argument("side", choices=["buy", "sell"], help="Order side")
    order_parser.add_argument("quantity", type=float, help="Order quantity")
    order_parser.add_argument("--type", choices=["market", "limit"], default="market", help="Order type")
    order_parser.add_argument("--price", type=float, help="Order price (for limit orders)")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    # Run command
    if args.command == "account":
        success = asyncio.run(test_account_info(args.service))
    elif args.command == "market":
        success = asyncio.run(test_market_data(args.service, args.symbol))
    elif args.command == "order":
        success = asyncio.run(test_place_order(args.service, args.symbol, args.side, args.quantity, args.type, args.price))
    else:
        print(f"Unknown command: {args.command}")
        return 1
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
