"""
<PERSON>rip<PERSON> to test GitHub and Hugging Face integrations.
"""
import sys
import asyncio
import argparse
from pathlib import Path
import json

from services.github_service import GitHubServiceFactory
from services.huggingface_service import HuggingFaceServiceFactory
from core.logger import setup_logger
import config

# Set up logger
logger = setup_logger("test_github_huggingface")

async def test_github_search(query: str, language: Optional[str] = None, search_type: str = "code"):
    """
    Test GitHub search.
    
    Args:
        query (str): Search query
        language (Optional[str]): Filter by language
        search_type (str): Search type (code/repositories)
    """
    logger.info(f"Testing GitHub {search_type} search for '{query}'")
    
    # Create GitHub service
    github_service = GitHubServiceFactory.create_service()
    
    if not github_service:
        logger.error("GitHub service not available")
        return False
    
    if not github_service.is_enabled():
        logger.error("GitHub service is not enabled")
        logger.info("Check your .env file for GITHUB_API_KEY and ENABLE_GITHUB")
        return False
    
    # Perform search
    if search_type == "code":
        logger.info(f"Searching for code: {query}")
        results = await github_service.search_code(query, language)
    elif search_type == "repositories":
        logger.info(f"Searching for repositories: {query}")
        results = await github_service.search_repositories(query, language)
    else:
        logger.error(f"Unsupported search type: {search_type}")
        return False
    
    # Check for errors
    if "error" in results:
        logger.error(f"GitHub search error: {results['error']}")
        return False
    
    # Print results
    total_count = results.get("total_count", 0)
    items = results.get("items", [])
    
    logger.info(f"Found {total_count} results")
    
    if search_type == "code":
        for i, item in enumerate(items[:5]):  # Show top 5 results
            repo = item.get("repository", {}).get("full_name", "")
            path = item.get("path", "")
            url = item.get("html_url", "")
            
            print(f"{i+1}. {repo} - {path}")
            print(f"   URL: {url}")
    
    elif search_type == "repositories":
        for i, item in enumerate(items[:5]):  # Show top 5 results
            name = item.get("full_name", "")
            description = item.get("description", "")
            stars = item.get("stargazers_count", 0)
            url = item.get("html_url", "")
            
            print(f"{i+1}. {name} ({stars} stars)")
            print(f"   {description}")
            print(f"   URL: {url}")
    
    return True

async def test_github_repository(repo: str):
    """
    Test GitHub repository information.
    
    Args:
        repo (str): Repository name (owner/repo)
    """
    logger.info(f"Testing GitHub repository info for {repo}")
    
    # Create GitHub service
    github_service = GitHubServiceFactory.create_service()
    
    if not github_service:
        logger.error("GitHub service not available")
        return False
    
    if not github_service.is_enabled():
        logger.error("GitHub service is not enabled")
        logger.info("Check your .env file for GITHUB_API_KEY and ENABLE_GITHUB")
        return False
    
    # Split repo into owner and name
    parts = repo.split("/")
    if len(parts) != 2:
        logger.error(f"Invalid repository format: {repo}")
        return False
    
    owner, repo_name = parts
    
    # Get repository information
    logger.info(f"Getting repository info for {repo}")
    repo_info = await github_service.get_repository(owner, repo_name)
    
    # Check for errors
    if "error" in repo_info:
        logger.error(f"GitHub repository error: {repo_info['error']}")
        return False
    
    # Print repository information
    print(f"Repository: {repo_info.get('full_name')}")
    print(f"Description: {repo_info.get('description')}")
    print(f"Language: {repo_info.get('language')}")
    print(f"Stars: {repo_info.get('stargazers_count')}")
    print(f"Forks: {repo_info.get('forks_count')}")
    print(f"Open Issues: {repo_info.get('open_issues_count')}")
    print(f"Created: {repo_info.get('created_at')}")
    print(f"Last Updated: {repo_info.get('updated_at')}")
    print(f"URL: {repo_info.get('html_url')}")
    
    # Get repository structure
    logger.info(f"Getting repository structure for {repo}")
    structure = await github_service.get_repository_structure(owner, repo_name)
    
    # Check for errors
    if "error" in structure:
        logger.error(f"GitHub structure error: {structure['error']}")
        return False
    
    # Print structure (top level only)
    print("\nRepository Structure:")
    for item in structure.get("items", [])[:10]:  # Show top 10 items
        item_type = item.get("type", "")
        item_name = item.get("name", "")
        
        print(f"- [{item_type}] {item_name}")
    
    return True

async def test_huggingface_search(query: str, task_type: Optional[str] = None, search_type: str = "models"):
    """
    Test Hugging Face search.
    
    Args:
        query (str): Search query
        task_type (Optional[str]): Filter by task type
        search_type (str): Search type (models/datasets)
    """
    logger.info(f"Testing Hugging Face {search_type} search for '{query}'")
    
    # Create Hugging Face service
    huggingface_service = HuggingFaceServiceFactory.create_service()
    
    if not huggingface_service:
        logger.error("Hugging Face service not available")
        return False
    
    if not huggingface_service.is_enabled():
        logger.error("Hugging Face service is not enabled")
        logger.info("Check your .env file for HUGGINGFACE_API_KEY and ENABLE_HUGGINGFACE")
        return False
    
    # Perform search
    if search_type == "models":
        logger.info(f"Searching for models: {query}")
        results = await huggingface_service.search_models(query, task_type)
    elif search_type == "datasets":
        logger.info(f"Searching for datasets: {query}")
        results = await huggingface_service.search_datasets(query, task_type)
    else:
        logger.error(f"Unsupported search type: {search_type}")
        return False
    
    # Check for errors
    if "error" in results:
        logger.error(f"Hugging Face search error: {results['error']}")
        return False
    
    # Print results
    logger.info(f"Found {len(results)} results")
    
    for i, item in enumerate(results[:5]):  # Show top 5 results
        if search_type == "models":
            model_id = item.get("id", "")
            downloads = item.get("downloads", 0)
            likes = item.get("likes", 0)
            tags = item.get("tags", [])
            
            print(f"{i+1}. {model_id}")
            print(f"   Downloads: {downloads}, Likes: {likes}")
            print(f"   Tags: {', '.join(tags[:5])}")
        
        elif search_type == "datasets":
            dataset_id = item.get("id", "")
            downloads = item.get("downloads", 0)
            likes = item.get("likes", 0)
            
            print(f"{i+1}. {dataset_id}")
            print(f"   Downloads: {downloads}, Likes: {likes}")
    
    return True

async def test_huggingface_model(model_id: str):
    """
    Test Hugging Face model information.
    
    Args:
        model_id (str): Model ID
    """
    logger.info(f"Testing Hugging Face model info for {model_id}")
    
    # Create Hugging Face service
    huggingface_service = HuggingFaceServiceFactory.create_service()
    
    if not huggingface_service:
        logger.error("Hugging Face service not available")
        return False
    
    if not huggingface_service.is_enabled():
        logger.error("Hugging Face service is not enabled")
        logger.info("Check your .env file for HUGGINGFACE_API_KEY and ENABLE_HUGGINGFACE")
        return False
    
    # Get model card
    logger.info(f"Getting model card for {model_id}")
    model_card = await huggingface_service.get_model_card(model_id)
    
    # Check for errors
    if "error" in model_card:
        logger.error(f"Hugging Face model error: {model_card['error']}")
        return False
    
    # Print model information
    print(f"Model: {model_id}")
    
    # Print model tags
    tags = model_card.get("tags", [])
    print(f"Tags: {', '.join(tags[:10])}")
    
    # Print README preview
    readme = model_card.get("readme", "")
    preview = readme[:500] + "..." if len(readme) > 500 else readme
    print("\nREADME Preview:")
    print(preview)
    
    return True

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Test GitHub and Hugging Face integrations")
    subparsers = parser.add_subparsers(dest="command", help="Command to run")
    
    # GitHub search command
    github_search_parser = subparsers.add_parser("github-search", help="Search GitHub")
    github_search_parser.add_argument("query", help="Search query")
    github_search_parser.add_argument("--language", help="Filter by language")
    github_search_parser.add_argument("--type", choices=["code", "repositories"], default="code", help="Search type")
    
    # GitHub repository command
    github_repo_parser = subparsers.add_parser("github-repo", help="Get GitHub repository information")
    github_repo_parser.add_argument("repo", help="Repository name (owner/repo)")
    
    # Hugging Face search command
    hf_search_parser = subparsers.add_parser("hf-search", help="Search Hugging Face")
    hf_search_parser.add_argument("query", help="Search query")
    hf_search_parser.add_argument("--task", help="Filter by task type")
    hf_search_parser.add_argument("--type", choices=["models", "datasets"], default="models", help="Search type")
    
    # Hugging Face model command
    hf_model_parser = subparsers.add_parser("hf-model", help="Get Hugging Face model information")
    hf_model_parser.add_argument("model_id", help="Model ID")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    # Run command
    if args.command == "github-search":
        success = asyncio.run(test_github_search(args.query, args.language, args.type))
    elif args.command == "github-repo":
        success = asyncio.run(test_github_repository(args.repo))
    elif args.command == "hf-search":
        success = asyncio.run(test_huggingface_search(args.query, args.task, args.type))
    elif args.command == "hf-model":
        success = asyncio.run(test_huggingface_model(args.model_id))
    else:
        print(f"Unknown command: {args.command}")
        return 1
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
