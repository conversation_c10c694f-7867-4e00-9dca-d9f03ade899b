"""
Google Drive service for the Multi-Agent AI System.
Allows agents to access, upload, download, and manage files on Google Drive.
"""
import os
import json
import pickle
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import logging
from pathlib import Path

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from googleapiclient.http import MediaFileUpload, MediaIoBaseDownload

from core.logger import setup_logger

# Set up logger
logger = setup_logger("google_drive_service")

class GoogleDriveService:
    """
    Service for interacting with Google Drive.
    """
    
    # Define the scopes
    SCOPES = [
        'https://www.googleapis.com/auth/drive.metadata.readonly',
        'https://www.googleapis.com/auth/drive.file',
        'https://www.googleapis.com/auth/drive'
    ]
    
    def __init__(self, credentials_path: str = 'credentials/google_drive_credentials.json', 
                 token_path: str = 'credentials/google_drive_token.pickle'):
        """
        Initialize the Google Drive service.
        
        Args:
            credentials_path (str): Path to the credentials JSON file
            token_path (str): Path to save the token pickle file
        """
        self.credentials_path = credentials_path
        self.token_path = token_path
        self.service = None
        self.enabled = False
        
        # Ensure credentials directory exists
        os.makedirs(os.path.dirname(credentials_path), exist_ok=True)
        
        # Try to initialize the service
        try:
            self._initialize_service()
            self.enabled = True
            logger.info("Google Drive service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Google Drive service: {e}")
    
    def _initialize_service(self):
        """Initialize the Google Drive API service."""
        creds = None
        
        # Load token if it exists
        if os.path.exists(self.token_path):
            with open(self.token_path, 'rb') as token:
                creds = pickle.load(token)
        
        # Refresh token if expired
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        # Otherwise, get new credentials
        elif not creds:
            if not os.path.exists(self.credentials_path):
                logger.error(f"Credentials file not found: {self.credentials_path}")
                raise FileNotFoundError(f"Credentials file not found: {self.credentials_path}")
            
            flow = InstalledAppFlow.from_client_secrets_file(self.credentials_path, self.SCOPES)
            creds = flow.run_local_server(port=0)
            
            # Save the credentials for the next run
            with open(self.token_path, 'wb') as token:
                pickle.dump(creds, token)
        
        # Build the service
        self.service = build('drive', 'v3', credentials=creds)
    
    def is_enabled(self) -> bool:
        """Check if the service is enabled."""
        return self.enabled
    
    async def list_files(self, folder_id: Optional[str] = None, 
                         query: Optional[str] = None, 
                         max_results: int = 100) -> Dict:
        """
        List files in Google Drive.
        
        Args:
            folder_id (Optional[str]): ID of the folder to list files from
            query (Optional[str]): Search query
            max_results (int): Maximum number of results to return
            
        Returns:
            Dict: List of files
        """
        if not self.enabled:
            return {"error": "Google Drive service is not enabled"}
        
        try:
            # Build the query
            q = []
            if folder_id:
                q.append(f"'{folder_id}' in parents")
            if query:
                q.append(query)
            
            # Execute the query
            results = self.service.files().list(
                q=" and ".join(q) if q else None,
                pageSize=max_results,
                fields="nextPageToken, files(id, name, mimeType, modifiedTime, size, webViewLink)"
            ).execute()
            
            return {
                "files": results.get('files', []),
                "next_page_token": results.get('nextPageToken', None)
            }
        
        except HttpError as e:
            logger.error(f"Error listing files: {e}")
            return {"error": f"Failed to list files: {str(e)}"}
    
    async def upload_file(self, file_path: str, folder_id: Optional[str] = None, 
                          mime_type: Optional[str] = None) -> Dict:
        """
        Upload a file to Google Drive.
        
        Args:
            file_path (str): Path to the file to upload
            folder_id (Optional[str]): ID of the folder to upload to
            mime_type (Optional[str]): MIME type of the file
            
        Returns:
            Dict: Uploaded file information
        """
        if not self.enabled:
            return {"error": "Google Drive service is not enabled"}
        
        try:
            file_metadata = {
                'name': os.path.basename(file_path)
            }
            
            if folder_id:
                file_metadata['parents'] = [folder_id]
            
            media = MediaFileUpload(file_path, mimetype=mime_type, resumable=True)
            
            file = self.service.files().create(
                body=file_metadata,
                media_body=media,
                fields='id, name, mimeType, webViewLink'
            ).execute()
            
            return {
                "id": file.get('id'),
                "name": file.get('name'),
                "mime_type": file.get('mimeType'),
                "web_view_link": file.get('webViewLink')
            }
        
        except HttpError as e:
            logger.error(f"Error uploading file: {e}")
            return {"error": f"Failed to upload file: {str(e)}"}
    
    async def download_file(self, file_id: str, output_path: str) -> Dict:
        """
        Download a file from Google Drive.
        
        Args:
            file_id (str): ID of the file to download
            output_path (str): Path to save the downloaded file
            
        Returns:
            Dict: Download status
        """
        if not self.enabled:
            return {"error": "Google Drive service is not enabled"}
        
        try:
            request = self.service.files().get_media(fileId=file_id)
            
            with open(output_path, 'wb') as f:
                downloader = MediaIoBaseDownload(f, request)
                done = False
                while not done:
                    status, done = downloader.next_chunk()
            
            return {
                "status": "success",
                "message": f"File downloaded to {output_path}"
            }
        
        except HttpError as e:
            logger.error(f"Error downloading file: {e}")
            return {"error": f"Failed to download file: {str(e)}"}
    
    async def create_folder(self, folder_name: str, parent_folder_id: Optional[str] = None) -> Dict:
        """
        Create a folder in Google Drive.
        
        Args:
            folder_name (str): Name of the folder to create
            parent_folder_id (Optional[str]): ID of the parent folder
            
        Returns:
            Dict: Created folder information
        """
        if not self.enabled:
            return {"error": "Google Drive service is not enabled"}
        
        try:
            file_metadata = {
                'name': folder_name,
                'mimeType': 'application/vnd.google-apps.folder'
            }
            
            if parent_folder_id:
                file_metadata['parents'] = [parent_folder_id]
            
            folder = self.service.files().create(
                body=file_metadata,
                fields='id, name, webViewLink'
            ).execute()
            
            return {
                "id": folder.get('id'),
                "name": folder.get('name'),
                "web_view_link": folder.get('webViewLink')
            }
        
        except HttpError as e:
            logger.error(f"Error creating folder: {e}")
            return {"error": f"Failed to create folder: {str(e)}"}

# Factory for creating Google Drive service instances
class GoogleDriveServiceFactory:
    """Factory for creating Google Drive service instances."""
    
    _instance = None
    
    @classmethod
    def create_service(cls) -> GoogleDriveService:
        """Create or return the singleton instance of GoogleDriveService."""
        if cls._instance is None:
            cls._instance = GoogleDriveService()
        return cls._instance
