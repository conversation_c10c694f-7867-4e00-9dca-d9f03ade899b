"""
LLM Connector Factory for the Multi-Agent AI System.
"""
from typing import Dict, Optional

from llm.mock_llm_connector import Mock<PERSON>MConnector
from llm.llm_connector import LLMConnector
import config

class LLMConnectorFactory:
    """Factory for creating LLM connectors."""
    
    @staticmethod
    def create_connector(provider: Optional[str] = None) -> LLMConnector:
        """
        Create an LLM connector for the specified provider.
        
        Args:
            provider (Optional[str]): Provider name, defaults to DEFAULT_LLM_PROVIDER
            
        Returns:
            LLMConnector: LLM connector instance
        """
        if provider is None:
            provider = config.DEFAULT_LLM_PROVIDER
        
        # For testing purposes, use the MockLLMConnector
        return MockLLMConnector(config.LLM_CONFIG.get(provider, {}))
