"""
Test Calendly Personal Access Token

This script tests the provided Calendly Personal Access Token and saves it.
"""
import requests
import json
import os

# Create credentials directory if it doesn't exist
os.makedirs("credentials/calendly", exist_ok=True)

# Set the Personal Access Token
pat = "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

# Test the token
try:
    print("Testing Calendly Personal Access Token...")
    response = requests.get(
        "https://api.calendly.com/users/me",
        headers={
            "Content-Type": "application/json",
            "Authorization": f"Bearer {pat}"
        }
    )
    
    if response.status_code == 200:
        user_info = response.json()
        print("\nPersonal Access Token is valid!")
        print(f"User: {user_info['resource']['name']} ({user_info['resource']['email']})")
        
        # Save token to credentials file
        with open("credentials/calendly/calendly.json", "w") as f:
            json.dump({
                "api_key": pat,
                "user_uri": user_info['resource']['uri'],
                "user_uuid": user_info['resource']['uri'].split('/')[-1]
            }, f, indent=4)
        
        print("\nToken saved to credentials/calendly/calendly.json")
        
        # Get organization memberships
        print("\nGetting organization memberships...")
        org_response = requests.get(
            "https://api.calendly.com/organization_memberships",
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {pat}"
            }
        )
        
        if org_response.status_code == 200:
            org_memberships = org_response.json()
            
            if org_memberships["collection"]:
                org_uri = org_memberships["collection"][0]["organization"]
                org_uuid = org_uri.split('/')[-1]
                
                print(f"Organization URI: {org_uri}")
                print(f"Organization UUID: {org_uuid}")
                
                # Update credentials file with organization info
                with open("credentials/calendly/calendly.json", "r") as f:
                    credentials = json.load(f)
                
                credentials["organization_uri"] = org_uri
                credentials["organization_uuid"] = org_uuid
                
                with open("credentials/calendly/calendly.json", "w") as f:
                    json.dump(credentials, f, indent=4)
                
                print("Organization information saved to credentials file")
            else:
                print("No organization memberships found")
        else:
            print(f"Error getting organization memberships: {org_response.status_code}")
            print(org_response.text)
        
        # Get event types
        print("\nGetting event types...")
        event_types_response = requests.get(
            "https://api.calendly.com/event_types",
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {pat}"
            }
        )
        
        if event_types_response.status_code == 200:
            event_types = event_types_response.json()
            print(f"Found {len(event_types['collection'])} event types:")
            
            # Initialize event_types in credentials
            with open("credentials/calendly/calendly.json", "r") as f:
                credentials = json.load(f)
            
            if "event_types" not in credentials:
                credentials["event_types"] = {}
            
            # Map event types
            for i, event_type in enumerate(event_types['collection']):
                print(f"{i+1}. {event_type['name']} ({event_type['duration']} minutes)")
                print(f"   URI: {event_type['uri']}")
                print(f"   URL: {event_type['scheduling_url']}")
                
                # Try to map to insurance types
                name = event_type['name'].lower()
                uri = event_type['uri']
                url = event_type['scheduling_url']
                
                insurance_types = [
                    "auto", "home", "life", "health", "business", 
                    "renters", "umbrella", "flood", "pet"
                ]
                
                mapped = False
                
                for insurance_type in insurance_types:
                    if insurance_type in name:
                        credentials["event_types"][insurance_type] = {
                            "name": event_type['name'],
                            "uri": uri,
                            "url": url
                        }
                        mapped = True
                        print(f"   Mapped to {insurance_type} insurance")
                
                # If not mapped to a specific insurance type, check if it's the default
                if not mapped and ("15" in name or "consultation" in name.lower() or "meeting" in name.lower()):
                    credentials["event_types"]["default"] = {
                        "name": event_type['name'],
                        "uri": uri,
                        "url": url
                    }
                    print(f"   Mapped to default consultation")
            
            # Save updated credentials
            with open("credentials/calendly/calendly.json", "w") as f:
                json.dump(credentials, f, indent=4)
            
            print("\nEvent types saved to credentials file")
            
            # Test webhook subscription
            print("\nTesting webhook subscription capabilities...")
            webhook_response = requests.get(
                "https://api.calendly.com/webhook_subscriptions",
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {pat}"
                }
            )
            
            if webhook_response.status_code == 200:
                webhooks = webhook_response.json()
                print(f"Found {len(webhooks['collection'])} existing webhook subscriptions")
                
                for i, webhook in enumerate(webhooks['collection']):
                    print(f"{i+1}. URL: {webhook['callback_url']}")
                    print(f"   Events: {', '.join(webhook['events'])}")
                    print(f"   Scope: {webhook['scope']}")
                    print(f"   Created At: {webhook['created_at']}")
                
                print("\nWebhook capabilities are working correctly")
            else:
                print(f"Error testing webhook capabilities: {webhook_response.status_code}")
                print(webhook_response.text)
        else:
            print(f"Error getting event types: {event_types_response.status_code}")
            print(event_types_response.text)
    else:
        print(f"Personal Access Token is invalid: {response.status_code}")
        print(response.text)
except Exception as e:
    print(f"Error: {e}")

print("\nCalendly integration test completed")
