"""
Browser Automation Service for the Multi-Agent AI System.

This module provides a service that manages the browser automation manager
and provides a high-level API for the AI agent system.
"""
import os
import sys
import json
import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Union, Tuple
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

try:
    from core.logger import setup_logger
    from ui_tars.browser_automation_manager import BrowserAutomationManager, AutomationProvider
    from agents.gmail_agent import GmailAgent
    from agents.google_voice_agent import GoogleVoiceAgent
    from config.config_manager import load_config, get_config
except ImportError:
    # Fallback logging setup if core.logger is not available
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler("browser_automation_service.log")
        ]
    )

    def setup_logger(name):
        return logging.getLogger(name)

    # Define AutomationProvider enum if not available
    from enum import Enum
    class AutomationProvider(Enum):
        """Enum for automation providers."""
        UI_TARS = "ui_tars"
        MIDSCENE = "midscene"
        AUTO = "auto"

# Set up logger
logger = setup_logger("browser_automation_service")

class BrowserAutomationService:
    """
    Browser Automation Service for the Multi-Agent AI System.
    
    This class provides a service that manages the browser automation manager
    and provides a high-level API for the AI agent system.
    """
    
    _instance = None
    
    def __new__(cls):
        """Create a singleton instance."""
        if cls._instance is None:
            cls._instance = super(BrowserAutomationService, cls).__new__(cls)
            cls._instance.initialized = False
        return cls._instance
    
    def __init__(self):
        """Initialize the Browser Automation Service."""
        if self.initialized:
            return
        
        self.config = get_config("browser_automation_config")
        self.browser_manager = None
        self.gmail_agent = None
        self.google_voice_agent = None
        self.health_check_task = None
        self.initialized = False
        
        logger.info("Browser Automation Service initialized")
    
    async def initialize(self):
        """Initialize the Browser Automation Service."""
        if self.initialized:
            logger.info("Browser Automation Service already initialized")
            return True
        
        logger.info("Initializing Browser Automation Service")
        
        try:
            # Load configuration
            if not self.config:
                self.config = load_config("browser_automation_config")
            
            # Get provider from configuration
            provider_str = self.config.get("browser_automation", {}).get("preferred_provider", "auto")
            provider_map = {
                "ui_tars": AutomationProvider.UI_TARS,
                "midscene": AutomationProvider.MIDSCENE,
                "auto": AutomationProvider.AUTO
            }
            provider = provider_map.get(provider_str, AutomationProvider.AUTO)
            
            # Initialize browser manager
            logger.info(f"Initializing browser automation manager with provider: {provider.value}")
            self.browser_manager = BrowserAutomationManager(
                config=self.config,
                provider=provider,
                auto_start=self.config.get("browser_automation", {}).get("auto_start", True),
                auto_restart=self.config.get("browser_automation", {}).get("auto_restart", True),
                auto_fallback=self.config.get("browser_automation", {}).get("auto_fallback", True)
            )
            
            success = await self.browser_manager.initialize()
            if not success:
                logger.error("Failed to initialize browser automation manager")
                return False
            
            # Initialize Gmail agent
            logger.info("Initializing Gmail agent")
            self.gmail_agent = GmailAgent(
                browser_manager=self.browser_manager,
                email=self.config.get("gmail", {}).get("email"),
                password=self.config.get("gmail", {}).get("password"),
                config=self.config
            )
            
            # Initialize Google Voice agent
            logger.info("Initializing Google Voice agent")
            self.google_voice_agent = GoogleVoiceAgent(
                browser_manager=self.browser_manager,
                email=self.config.get("google_voice", {}).get("email"),
                password=self.config.get("google_voice", {}).get("password"),
                phone_number=self.config.get("google_voice", {}).get("phone_number"),
                config=self.config
            )
            
            # Start health check task
            health_check_interval = self.config.get("browser_automation", {}).get("health_check_interval", 300)
            if health_check_interval > 0:
                self.health_check_task = asyncio.create_task(self._health_check_loop(health_check_interval))
            
            self.initialized = True
            logger.info("Browser Automation Service initialized successfully")
            return True
        
        except Exception as e:
            logger.exception(f"Error initializing Browser Automation Service: {e}")
            return False
    
    async def _health_check_loop(self, interval: int):
        """
        Periodically perform health checks.
        
        Args:
            interval (int): Health check interval in seconds
        """
        logger.info(f"Starting health check loop with interval: {interval} seconds")
        
        while True:
            try:
                # Wait for the specified interval
                await asyncio.sleep(interval)
                
                # Perform health check
                logger.info("Performing periodic health check")
                health = await self.browser_manager.health_check()
                
                if health["status"] == "unhealthy":
                    logger.warning(f"Health check failed: {health['issues']}")
                    
                    # Try to repair
                    logger.info("Attempting to repair")
                    repair_result = await self.browser_manager.auto_repair()
                    
                    if repair_result["success"]:
                        logger.info(f"Repair successful: {repair_result['actions_taken']}")
                    else:
                        logger.error(f"Repair failed: {repair_result['message']}")
                else:
                    logger.info("Health check passed")
            
            except asyncio.CancelledError:
                logger.info("Health check loop cancelled")
                break
            
            except Exception as e:
                logger.exception(f"Error in health check loop: {e}")
                await asyncio.sleep(10)  # Wait a bit before retrying
    
    async def execute_command(self, command: str, max_retries: int = None):
        """
        Execute a browser automation command.
        
        Args:
            command (str): Command to execute
            max_retries (int): Maximum number of retries
            
        Returns:
            Dict: Command result
        """
        logger.info(f"Executing command: {command}")
        
        if not self.initialized:
            logger.warning("Browser Automation Service not initialized, initializing now")
            success = await self.initialize()
            if not success:
                logger.error("Failed to initialize Browser Automation Service")
                return {"error": "Failed to initialize Browser Automation Service"}
        
        try:
            # Use max_retries from config if not specified
            if max_retries is None:
                max_retries = self.config.get("browser_automation", {}).get("max_retries", 3)
            
            # Execute command with browser manager
            result = await self.browser_manager.execute_command(command, max_retries=max_retries)
            return result
        
        except Exception as e:
            logger.exception(f"Error executing command: {e}")
            return {"error": f"Error executing command: {str(e)}"}
    
    async def send_email(self, to: str, subject: str, body: str):
        """
        Send an email using the Gmail agent.
        
        Args:
            to (str): Recipient email address
            subject (str): Email subject
            body (str): Email body
            
        Returns:
            bool: True if successful, False otherwise
        """
        logger.info(f"Sending email to {to}")
        
        if not self.initialized:
            logger.warning("Browser Automation Service not initialized, initializing now")
            success = await self.initialize()
            if not success:
                logger.error("Failed to initialize Browser Automation Service")
                return False
        
        try:
            # Add signature if configured
            signature = self.config.get("gmail", {}).get("signature")
            if signature and not body.endswith(signature):
                body = f"{body}\n\n{signature}"
            
            # Compose email
            success = await self.gmail_agent.compose_email(to, subject, body)
            if not success:
                logger.error("Failed to compose email")
                return False
            
            # Send email
            success = await self.gmail_agent.send_email()
            if not success:
                logger.error("Failed to send email")
                return False
            
            logger.info("Email sent successfully")
            return True
        
        except Exception as e:
            logger.exception(f"Error sending email: {e}")
            return False
    
    async def send_text_message(self, to: str, message: str):
        """
        Send a text message using the Google Voice agent.
        
        Args:
            to (str): Recipient phone number
            message (str): Message text
            
        Returns:
            bool: True if successful, False otherwise
        """
        logger.info(f"Sending text message to {to}")
        
        if not self.initialized:
            logger.warning("Browser Automation Service not initialized, initializing now")
            success = await self.initialize()
            if not success:
                logger.error("Failed to initialize Browser Automation Service")
                return False
        
        try:
            # Send text message
            success = await self.google_voice_agent.send_text_message(to, message)
            if not success:
                logger.error("Failed to send text message")
                return False
            
            logger.info("Text message sent successfully")
            return True
        
        except Exception as e:
            logger.exception(f"Error sending text message: {e}")
            return False
    
    async def make_call(self, to: str):
        """
        Make a phone call using the Google Voice agent.
        
        Args:
            to (str): Recipient phone number
            
        Returns:
            bool: True if successful, False otherwise
        """
        logger.info(f"Making call to {to}")
        
        if not self.initialized:
            logger.warning("Browser Automation Service not initialized, initializing now")
            success = await self.initialize()
            if not success:
                logger.error("Failed to initialize Browser Automation Service")
                return False
        
        try:
            # Make call
            success = await self.google_voice_agent.make_call(to)
            if not success:
                logger.error("Failed to make call")
                return False
            
            logger.info("Call initiated successfully")
            return True
        
        except Exception as e:
            logger.exception(f"Error making call: {e}")
            return False
    
    async def health_check(self):
        """
        Perform a health check on the browser automation manager.
        
        Returns:
            Dict: Health check result
        """
        logger.info("Performing health check")
        
        if not self.initialized:
            logger.warning("Browser Automation Service not initialized, initializing now")
            success = await self.initialize()
            if not success:
                logger.error("Failed to initialize Browser Automation Service")
                return {
                    "status": "unhealthy",
                    "issues": ["Failed to initialize Browser Automation Service"]
                }
        
        try:
            # Perform health check on browser manager
            health = await self.browser_manager.health_check()
            return health
        
        except Exception as e:
            logger.exception(f"Error performing health check: {e}")
            return {
                "status": "unhealthy",
                "issues": [f"Error performing health check: {str(e)}"]
            }
    
    async def auto_repair(self):
        """
        Attempt to automatically repair the browser automation manager.
        
        Returns:
            Dict: Repair result
        """
        logger.info("Attempting to auto-repair")
        
        if not self.initialized:
            logger.warning("Browser Automation Service not initialized, initializing now")
            success = await self.initialize()
            if not success:
                logger.error("Failed to initialize Browser Automation Service")
                return {
                    "success": False,
                    "message": "Failed to initialize Browser Automation Service",
                    "actions_taken": []
                }
        
        try:
            # Attempt to repair browser manager
            repair_result = await self.browser_manager.auto_repair()
            return repair_result
        
        except Exception as e:
            logger.exception(f"Error performing auto-repair: {e}")
            return {
                "success": False,
                "message": f"Error performing auto-repair: {str(e)}",
                "actions_taken": []
            }
    
    async def stop(self):
        """Stop the Browser Automation Service."""
        logger.info("Stopping Browser Automation Service")
        
        if not self.initialized:
            logger.info("Browser Automation Service not initialized")
            return True
        
        try:
            # Cancel health check task
            if self.health_check_task:
                self.health_check_task.cancel()
                try:
                    await self.health_check_task
                except asyncio.CancelledError:
                    pass
            
            # Stop Gmail agent
            if self.gmail_agent:
                await self.gmail_agent.stop()
            
            # Stop Google Voice agent
            if self.google_voice_agent:
                await self.google_voice_agent.stop()
            
            # Stop browser manager
            if self.browser_manager:
                await self.browser_manager.stop()
            
            self.initialized = False
            logger.info("Browser Automation Service stopped")
            return True
        
        except Exception as e:
            logger.exception(f"Error stopping Browser Automation Service: {e}")
            return False

# Create a singleton instance
browser_automation_service = BrowserAutomationService()

def get_browser_automation_service() -> BrowserAutomationService:
    """
    Get the singleton instance of the Browser Automation Service.
    
    Returns:
        BrowserAutomationService: Singleton instance
    """
    return browser_automation_service
