"""
Email Sender Demo

This script demonstrates how to send emails using both UI-TARS browser automation
and the Gmail API. It provides a comparison of the two methods and allows the user
to choose which method to use.
"""
import os
import sys
import asyncio
import logging
import argparse
import getpass
from typing import Dict, Optional, Any, List

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("email_sender_demo")

# Import UI-TARS Gmail automation
try:
    from ui_tars_gmail_automation import GmailUITarsAutomation
except ImportError:
    logger.error("UI-TARS Gmail automation not found. Make sure ui_tars_gmail_automation.py is in the current directory.")
    sys.exit(1)

# Try to import Gmail service
try:
    from services.gmail_service import GmailService
except ImportError:
    logger.warning("Gmail service not found. API-based email sending will not be available.")
    GmailService = None

class EmailSenderDemo:
    """Demo class for sending emails using different methods."""
    
    def __init__(self):
        """Initialize the Email Sender Demo."""
        self.ui_tars_automation = None
        self.gmail_service = None
    
    async def initialize_ui_tars(self, 
                               api_url: str = "http://localhost:8080",
                               model_name: str = "UI-TARS-1.5-7B",
                               installation_path: Optional[str] = None) -> bool:
        """
        Initialize UI-TARS for browser automation.
        
        Args:
            api_url (str): URL of the UI-TARS API
            model_name (str): Name of the model to use
            installation_path (Optional[str]): Path to UI-TARS installation
            
        Returns:
            bool: True if initialization was successful, False otherwise
        """
        logger.info("Initializing UI-TARS for browser automation")
        
        # Create UI-TARS Gmail automation
        self.ui_tars_automation = GmailUITarsAutomation(
            api_url=api_url,
            model_name=model_name,
            installation_path=installation_path
        )
        
        # Initialize UI-TARS Gmail automation
        success = await self.ui_tars_automation.initialize()
        if not success:
            logger.error("Failed to initialize UI-TARS Gmail automation")
            return False
        
        logger.info("UI-TARS initialized successfully")
        return True
    
    def initialize_gmail_api(self, 
                           credentials_path: str,
                           token_path: str) -> bool:
        """
        Initialize Gmail API for email sending.
        
        Args:
            credentials_path (str): Path to the Gmail API credentials file
            token_path (str): Path to the Gmail API token file
            
        Returns:
            bool: True if initialization was successful, False otherwise
        """
        if GmailService is None:
            logger.error("Gmail service not available")
            return False
        
        logger.info("Initializing Gmail API")
        
        # Create Gmail service
        self.gmail_service = GmailService(credentials_path, token_path)
        
        # Check if Gmail service is enabled
        if not self.gmail_service.is_enabled():
            logger.error("Gmail service not enabled")
            return False
        
        logger.info("Gmail API initialized successfully")
        return True
    
    async def send_email_ui_tars(self, 
                               email_account: str,
                               password: str,
                               to_email: str, 
                               subject: str, 
                               body: str) -> Dict[str, Any]:
        """
        Send an email using UI-TARS browser automation.
        
        Args:
            email_account (str): Gmail account to send from
            password (str): Password for the Gmail account
            to_email (str): Recipient email address
            subject (str): Email subject
            body (str): Email body
            
        Returns:
            Dict[str, Any]: Result of the operation
        """
        logger.info(f"Sending email from {email_account} to {to_email} using UI-TARS")
        
        if not self.ui_tars_automation:
            logger.error("UI-TARS Gmail automation not initialized")
            return {"success": False, "error": "UI-TARS Gmail automation not initialized"}
        
        result = await self.ui_tars_automation.send_email(
            email_account=email_account,
            password=password,
            to_email=to_email,
            subject=subject,
            body=body
        )
        
        return result
    
    async def send_email_api(self, 
                           to_email: str, 
                           subject: str, 
                           body: str,
                           cc: Optional[str] = None,
                           bcc: Optional[str] = None) -> Dict[str, Any]:
        """
        Send an email using the Gmail API.
        
        Args:
            to_email (str): Recipient email address
            subject (str): Email subject
            body (str): Email body
            cc (Optional[str]): CC recipients
            bcc (Optional[str]): BCC recipients
            
        Returns:
            Dict[str, Any]: Result of the operation
        """
        logger.info(f"Sending email to {to_email} using Gmail API")
        
        if not self.gmail_service:
            logger.error("Gmail service not initialized")
            return {"success": False, "error": "Gmail service not initialized"}
        
        result = await self.gmail_service.send_message(
            to=to_email,
            subject=subject,
            body=body,
            cc=cc,
            bcc=bcc
        )
        
        if "error" in result:
            return {"success": False, "error": result["error"]}
        
        return {"success": True, "message": "Email sent successfully", "message_id": result.get("message_id")}
    
    async def shutdown(self) -> None:
        """Shut down the Email Sender Demo."""
        if self.ui_tars_automation:
            await self.ui_tars_automation.shutdown()
        logger.info("Email Sender Demo shut down")

async def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Email Sender Demo")
    parser.add_argument("--method", type=str, choices=["ui-tars", "api", "both"], default="ui-tars", help="Method to use for sending email")
    parser.add_argument("--email", type=str, default="<EMAIL>", help="Gmail account to send from")
    parser.add_argument("--to", type=str, default="<EMAIL>", help="Recipient email address")
    parser.add_argument("--subject", type=str, default="Test Email from Email Sender Demo", help="Email subject")
    parser.add_argument("--body", type=str, default="This is a test email sent using the Email Sender Demo.", help="Email body")
    parser.add_argument("--api-url", type=str, default="http://localhost:8080", help="URL of the UI-TARS API")
    parser.add_argument("--model", type=str, default="UI-TARS-1.5-7B", help="Name of the model to use")
    parser.add_argument("--installation-path", type=str, help="Path to UI-TARS installation")
    parser.add_argument("--credentials-path", type=str, default="credentials/gmail_flofaction_dot_insurance_at_gmail_dot_com_credentials.json", help="Path to the Gmail API credentials file")
    parser.add_argument("--token-path", type=str, default="credentials/gmail_flofaction_dot_insurance_at_gmail_dot_com_token.pickle", help="Path to the Gmail API token file")
    
    args = parser.parse_args()
    
    # Create Email Sender Demo
    demo = EmailSenderDemo()
    
    try:
        # Initialize based on method
        if args.method in ["ui-tars", "both"]:
            ui_tars_initialized = await demo.initialize_ui_tars(
                api_url=args.api_url,
                model_name=args.model,
                installation_path=args.installation_path
            )
            
            if not ui_tars_initialized and args.method == "ui-tars":
                logger.error("Failed to initialize UI-TARS")
                return
        
        if args.method in ["api", "both"]:
            api_initialized = demo.initialize_gmail_api(
                credentials_path=args.credentials_path,
                token_path=args.token_path
            )
            
            if not api_initialized and args.method == "api":
                logger.error("Failed to initialize Gmail API")
                return
        
        # Get password for UI-TARS method
        password = None
        if args.method in ["ui-tars", "both"]:
            password = getpass.getpass(f"Enter password for {args.email}: ")
        
        # Send email using UI-TARS
        if args.method in ["ui-tars", "both"] and demo.ui_tars_automation:
            logger.info("Sending email using UI-TARS browser automation")
            ui_tars_result = await demo.send_email_ui_tars(
                email_account=args.email,
                password=password,
                to_email=args.to,
                subject=args.subject,
                body=args.body
            )
            
            if ui_tars_result["success"]:
                logger.info("Email sent successfully using UI-TARS")
            else:
                logger.error(f"Failed to send email using UI-TARS: {ui_tars_result['error']}")
        
        # Send email using Gmail API
        if args.method in ["api", "both"] and demo.gmail_service:
            logger.info("Sending email using Gmail API")
            api_result = await demo.send_email_api(
                to_email=args.to,
                subject=args.subject,
                body=args.body
            )
            
            if api_result["success"]:
                logger.info("Email sent successfully using Gmail API")
            else:
                logger.error(f"Failed to send email using Gmail API: {api_result['error']}")
        
        # Compare methods if both were used
        if args.method == "both":
            logger.info("\nComparison of methods:")
            logger.info("UI-TARS browser automation:")
            logger.info("  - Pros: Visual feedback, can handle CAPTCHAs, works with any email provider")
            logger.info("  - Cons: Slower, requires password, less reliable")
            
            logger.info("\nGmail API:")
            logger.info("  - Pros: Faster, more reliable, no password needed (uses OAuth)")
            logger.info("  - Cons: Limited to Gmail, requires API setup, no visual feedback")
    
    finally:
        # Shut down
        await demo.shutdown()

if __name__ == "__main__":
    asyncio.run(main())
