@echo off
echo UI-TARS Browser Detection Fixer
echo =============================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Ask for UI-TARS path
echo Enter the path to UI-TARS executable (leave empty to auto-detect):
set /p UI_TARS_PATH=""

REM Ask for configuration path
echo.
echo Enter the path to UI-TARS configuration file (leave empty to auto-detect):
set /p CONFIG_PATH=""

REM Ask for browser type
echo.
echo Select browser type:
echo 1. Auto-detect (default)
echo 2. Chrome
echo 3. Edge
echo 4. Firefox
echo 5. Brave
echo.
set /p BROWSER_CHOICE="Enter choice (1-5): "

if "%BROWSER_CHOICE%"=="2" (
    set BROWSER_TYPE=chrome
) else if "%BROWSER_CHOICE%"=="3" (
    set BROWSER_TYPE=edge
) else if "%BROWSER_CHOICE%"=="4" (
    set BROWSER_TYPE=firefox
) else if "%BROWSER_CHOICE%"=="5" (
    set BROWSER_TYPE=brave
) else (
    set BROWSER_TYPE=
)

REM Ask for advanced options
echo.
echo Enable advanced options? (Y/N, default: Y)
set /p ADVANCED_OPTIONS="Enable advanced options? "

if /i "%ADVANCED_OPTIONS%"=="N" (
    set SANDBOX_OPTION=--no-sandbox
    set VIRTUAL_PC_OPTION=--no-virtual-pc
    set DPO_OPTION=--no-dpo
    set DEBUG_OPTION=
) else (
    set SANDBOX_OPTION=
    set VIRTUAL_PC_OPTION=
    set DPO_OPTION=
    set DEBUG_OPTION=--debug
)

REM Ask whether to start UI-TARS
echo.
echo Start UI-TARS after fixing? (Y/N, default: Y)
set /p START_UI_TARS="Start UI-TARS? "

if /i "%START_UI_TARS%"=="N" (
    set START_OPTION=
) else (
    set START_OPTION=--start
)

REM Run the browser fix script
echo.
echo Running UI-TARS browser detection fixer...
echo.

set COMMAND=python fix_ui_tars_browser.py

if not "%UI_TARS_PATH%"=="" (
    set COMMAND=%COMMAND% --path "%UI_TARS_PATH%"
)

if not "%CONFIG_PATH%"=="" (
    set COMMAND=%COMMAND% --config "%CONFIG_PATH%"
)

if not "%BROWSER_TYPE%"=="" (
    set COMMAND=%COMMAND% --browser %BROWSER_TYPE%
)

set COMMAND=%COMMAND% %SANDBOX_OPTION% %VIRTUAL_PC_OPTION% %DPO_OPTION% %DEBUG_OPTION% %START_OPTION%

echo Executing: %COMMAND%
echo.

%COMMAND%

echo.
if %errorlevel% equ 0 (
    echo UI-TARS browser detection issues fixed successfully!
) else (
    echo There was an error fixing UI-TARS browser detection issues.
)

echo.
pause
