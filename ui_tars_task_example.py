"""
UI-TARS Task Example.

This script demonstrates how to use UI-TARS to complete a specific task.
"""
import os
import sys
import json
import asyncio
import logging
import argparse
from ui_tars_agent_integration import UITarsAgent

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("ui_tars_task_example")

async def search_and_summarize(query: str, agent: UITarsAgent):
    """
    Search for information and summarize the results.
    
    Args:
        query (str): Search query
        agent (UITarsAgent): UI-TARS Agent
    """
    logger.info(f"Searching for: {query}")
    
    # Search the web
    search_result = await agent.search_web(query)
    logger.info(f"Search completed")
    
    # Extract the search results
    if "error" in search_result:
        logger.error(f"Error searching: {search_result['error']}")
        return f"Error: {search_result['error']}"
    
    # Ask UI-TARS to summarize the results
    summarize_command = f"Summarize the search results for '{query}' in 3-5 bullet points"
    summary_result = await agent.execute_command(summarize_command)
    
    if "error" in summary_result:
        logger.error(f"Error summarizing: {summary_result['error']}")
        return f"Error: {summary_result['error']}"
    
    # Extract the summary
    summary = summary_result.get("generated_text", "No summary available")
    
    return summary

async def fill_contact_form(website: str, contact_info: dict, agent: UITarsAgent):
    """
    Fill out a contact form on a website.
    
    Args:
        website (str): Website URL
        contact_info (dict): Contact information
        agent (UITarsAgent): UI-TARS Agent
    """
    logger.info(f"Filling contact form on: {website}")
    
    # Browse to the website
    browse_result = await agent.browse_website(website, "navigate to the contact form")
    logger.info(f"Navigation completed")
    
    # Fill out the form
    form_result = await agent.fill_form(website, contact_info)
    logger.info(f"Form filled")
    
    # Take a screenshot
    screenshot_result = await agent.take_screenshot()
    logger.info(f"Screenshot taken")
    
    # Extract the result
    if "error" in form_result:
        logger.error(f"Error filling form: {form_result['error']}")
        return f"Error: {form_result['error']}"
    
    result = form_result.get("generated_text", "No result available")
    
    return result

async def extract_product_information(website: str, product_type: str, agent: UITarsAgent):
    """
    Extract product information from a website.
    
    Args:
        website (str): Website URL
        product_type (str): Type of product
        agent (UITarsAgent): UI-TARS Agent
    """
    logger.info(f"Extracting {product_type} information from: {website}")
    
    # Browse to the website
    browse_result = await agent.browse_website(website, f"find {product_type} products")
    logger.info(f"Navigation completed")
    
    # Extract the product information
    extract_result = await agent.extract_data(website, f"{product_type} product information")
    logger.info(f"Data extraction completed")
    
    # Extract the result
    if "error" in extract_result:
        logger.error(f"Error extracting data: {extract_result['error']}")
        return f"Error: {extract_result['error']}"
    
    result = extract_result.get("generated_text", "No result available")
    
    return result

async def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="UI-TARS Task Example")
    parser.add_argument("--task", type=str, default="search", help="Task to perform (search, form, extract)")
    parser.add_argument("--query", type=str, default="UI-TARS documentation", help="Search query")
    parser.add_argument("--website", type=str, default="https://www.example.com", help="Website URL")
    parser.add_argument("--product", type=str, default="electronics", help="Product type")
    
    args = parser.parse_args()
    
    # Create a UI-TARS Agent
    agent = UITarsAgent()
    
    # Initialize the agent
    initialized = await agent.initialize()
    if not initialized:
        logger.error("Failed to initialize UI-TARS Agent")
        return
    
    try:
        # Perform the requested task
        if args.task == "search":
            # Search and summarize
            result = await search_and_summarize(args.query, agent)
            print("\n=== Search and Summarize Results ===")
            print(f"Query: {args.query}")
            print(f"Summary:\n{result}")
        
        elif args.task == "form":
            # Fill out a contact form
            contact_info = {
                "name": "John Doe",
                "email": "<EMAIL>",
                "subject": "Inquiry about your services",
                "message": "I would like to learn more about your services. Please contact me at your earliest convenience."
            }
            
            result = await fill_contact_form(args.website, contact_info, agent)
            print("\n=== Contact Form Results ===")
            print(f"Website: {args.website}")
            print(f"Result:\n{result}")
        
        elif args.task == "extract":
            # Extract product information
            result = await extract_product_information(args.website, args.product, agent)
            print("\n=== Product Information Extraction Results ===")
            print(f"Website: {args.website}")
            print(f"Product Type: {args.product}")
            print(f"Information:\n{result}")
        
        else:
            logger.error(f"Unknown task: {args.task}")
    
    finally:
        # Shut down the agent
        await agent.shutdown()

if __name__ == "__main__":
    asyncio.run(main())
