# Gmail Integration for AI Agent System

This guide provides step-by-step instructions for setting up Gmail integration with your AI Agent System for multiple Gmail accounts.

## Interactive Scripts

We've created interactive scripts that will guide you through the process with visual feedback:

1. **interactive_gmail_setup.py** - Interactive wizard for setting up Gmail integration
2. **send_test_email_interactive.py** - Interactive tool for sending test emails

## Running the Scripts

To run these scripts, you need to use the full path to your Python executable:

```
"C:\Program Files\Python310\python.exe" interactive_gmail_setup.py
```

Or if you're using PowerShell:

```
& "C:\Program Files\Python310\python.exe" interactive_gmail_setup.py
```

## Step 1: Set Up Gmail Integration

Run the interactive Gmail setup script:

```
"C:\Program Files\Python310\python.exe" interactive_gmail_setup.py
```

This script will guide you through the following steps:

1. **Set up OAuth settings**:
   - Configure OAuth consent screen
   - Update authorized redirect URIs
   - Enable Gmail API

2. **Copy credentials files** for your Gmail accounts:
   - <EMAIL>
   - <EMAIL>
   - <EMAIL>
   - And any other accounts you want to integrate

3. **Test Gmail authentication** for each account

## Step 2: Send Test Emails

After setting up Gmail integration, you can send test emails using the interactive email sender:

```
"C:\Program Files\Python310\python.exe" send_test_email_interactive.py
```

This script will:
1. Show you a list of authenticated Gmail accounts
2. Let you select an account to send a test email from
3. Guide you through entering the recipient, subject, and body
4. Send the email and show you the result in real-time

## Troubleshooting

If you encounter any issues:

1. **Browser doesn't open**:
   - The script will ask you if the browser opened correctly
   - If not, it will try alternative methods
   - If all else fails, it will provide you with the URL to open manually

2. **Authentication errors**:
   - Make sure your OAuth consent screen is properly configured
   - Add all required scopes to your OAuth consent screen
   - Add all your Gmail accounts as test users
   - Add http://localhost:55253/ and http://localhost:0/ to the authorized redirect URIs
   - Make sure the Gmail API is enabled for your project

3. **Python not found**:
   - Use the full path to your Python executable:
     ```
     "C:\Program Files\Python310\python.exe" script_name.py
     ```

## Credentials Files

The credentials files for your Gmail accounts are stored in the `credentials` directory:

- `credentials/gmail_Flofactionllc_at_gmail_dot_com_credentials.json`
- `credentials/gmail_sandra_dot_insurancebroker28_at_gmail_dot_com_credentials.json`
- `credentials/gmail_flofaction_dot_insurance_at_gmail_dot_com_credentials.json`

After successful authentication, token files will also be created:

- `credentials/gmail_Flofactionllc_at_gmail_dot_com_token.pickle`
- `credentials/gmail_sandra_dot_insurancebroker28_at_gmail_dot_com_token.pickle`
- `credentials/gmail_flofaction_dot_insurance_at_gmail_dot_com_token.pickle`

These token files contain the OAuth tokens needed to access your Gmail accounts without re-authenticating each time.

## Using Gmail in Your AI Agent System

Once you have set up and tested Gmail authentication for all your accounts, you can use the Gmail service in your AI Agent System:

- For a single Gmail account, use `GmailServiceFactory.create_service()`
- For multiple Gmail accounts, use `MultiAccountGmailServiceFactory.create_service()`

Example:

```python
from services.multi_account_gmail_service import MultiAccountGmailServiceFactory

# Create the multi-account Gmail service
gmail_service = MultiAccountGmailServiceFactory.create_service()

# Check which accounts are enabled
enabled_accounts = gmail_service.get_enabled_accounts()
print(f"Enabled accounts: {enabled_accounts}")

# Send an email from a specific account
result = await gmail_service.send_message_from_account(
    email="<EMAIL>",
    to="<EMAIL>",
    subject="Test Email",
    body="This is a test email sent from the AI Agent System."
)
```
