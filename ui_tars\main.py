"""
UI-TARS Main Module.

This module provides the main entry point for the UI-TARS integration.
"""
import os
import sys
import json
import asyncio
import logging
import argparse
from typing import Dict, List, Optional, Any, Union

from core.logger import setup_logger
from ui_tars.connector.ui_tars_connector import UITarsConnector
from ui_tars.connector.midscene_connector import MidsceneConnector
from ui_tars.connector.local_llm_connector import LocalLLMConnector
from ui_tars.gui.dashboard import UITarsDashboard

# Set up logger
logger = setup_logger("ui_tars_main")

async def initialize_connectors(config: Dict):
    """
    Initialize the UI-TARS connectors.
    
    Args:
        config (Dict): Configuration for the connectors
        
    Returns:
        Tuple: UI-TARS connector, Midscene connector, Local LLM connector
    """
    logger.info("Initializing UI-TARS connectors")
    
    # Initialize UI-TARS connector
    ui_tars_connector = UITarsConnector(
        api_url=config.get("ui_tars", {}).get("api_url"),
        api_key=config.get("ui_tars", {}).get("api_key"),
        model_name=config.get("ui_tars", {}).get("model_name"),
        installation_path=config.get("ui_tars", {}).get("installation_path")
    )
    
    # Initialize Midscene connector
    midscene_connector = MidsceneConnector(
        api_url=config.get("midscene", {}).get("api_url"),
        api_key=config.get("midscene", {}).get("api_key"),
        model_name=config.get("midscene", {}).get("model_name"),
        installation_path=config.get("midscene", {}).get("installation_path"),
        browser_type=config.get("midscene", {}).get("browser_type", "chrome"),
        android_enabled=config.get("midscene", {}).get("android_enabled", False)
    )
    
    # Initialize Local LLM connector
    local_llm_connector = LocalLLMConnector(
        model_path=config.get("local_llm", {}).get("model_path"),
        model_type=config.get("local_llm", {}).get("model_type", "ui-tars"),
        host=config.get("local_llm", {}).get("host", "localhost"),
        port=config.get("local_llm", {}).get("port", 8000),
        api_base=config.get("local_llm", {}).get("api_base"),
        quantization=config.get("local_llm", {}).get("quantization", "4bit")
    )
    
    # Initialize connectors
    await ui_tars_connector.initialize()
    await midscene_connector.initialize()
    await local_llm_connector.initialize()
    
    logger.info("UI-TARS connectors initialized")
    
    return ui_tars_connector, midscene_connector, local_llm_connector

def load_config(config_path: str) -> Dict:
    """
    Load configuration from a file.
    
    Args:
        config_path (str): Path to the configuration file
        
    Returns:
        Dict: Configuration
    """
    try:
        # Check if the configuration file exists
        if not os.path.exists(config_path):
            logger.warning(f"Configuration file not found: {config_path}")
            return {}
        
        # Load the configuration
        with open(config_path, "r") as f:
            config = json.load(f)
        
        logger.info(f"Configuration loaded from {config_path}")
        return config
    
    except Exception as e:
        logger.exception(f"Error loading configuration: {e}")
        return {}

async def main():
    """Main entry point for the UI-TARS integration."""
    logger.info("Starting UI-TARS integration")
    
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="UI-TARS Integration")
    parser.add_argument("--config", type=str, default="settings/ui_tars_config.json", help="Path to the configuration file")
    parser.add_argument("--gui", action="store_true", help="Start the GUI dashboard")
    parser.add_argument("--start", action="store_true", help="Start UI-TARS automatically")
    parser.add_argument("--browser", action="store_true", help="Start browser automation")
    parser.add_argument("--url", type=str, help="URL to open in the browser")
    parser.add_argument("--android", action="store_true", help="Start Android automation")
    parser.add_argument("--device", type=str, help="Android device ID")
    parser.add_argument("--local-llm", action="store_true", help="Start local LLM server")
    args = parser.parse_args()
    
    # Load configuration
    config = load_config(args.config)
    
    # Initialize connectors
    ui_tars_connector, midscene_connector, local_llm_connector = await initialize_connectors(config)
    
    # Start UI-TARS if requested
    if args.start:
        logger.info("Starting UI-TARS")
        await ui_tars_connector.start()
    
    # Start browser automation if requested
    if args.browser:
        logger.info("Starting browser automation")
        await midscene_connector.start_browser_automation(args.url)
    
    # Start Android automation if requested
    if args.android:
        logger.info("Starting Android automation")
        await midscene_connector.start_android_automation(args.device)
    
    # Start local LLM server if requested
    if args.local_llm:
        logger.info("Starting local LLM server")
        await local_llm_connector.start_server()
    
    # Start the GUI dashboard if requested
    if args.gui:
        logger.info("Starting GUI dashboard")
        dashboard = UITarsDashboard(
            ui_tars_connector=ui_tars_connector,
            midscene_connector=midscene_connector,
            local_llm_connector=local_llm_connector
        )
        dashboard.run()
    
    # Wait for user input if not running the GUI
    if not args.gui:
        logger.info("Press Enter to exit")
        input()
    
    # Clean up
    logger.info("Cleaning up")
    
    # Stop UI-TARS if running
    if ui_tars_connector.is_running:
        logger.info("Stopping UI-TARS")
        await ui_tars_connector.stop()
    
    # Stop Midscene if running
    if midscene_connector.is_running:
        logger.info("Stopping Midscene")
        await midscene_connector.stop()
    
    # Stop local LLM server if running
    if local_llm_connector.is_running:
        logger.info("Stopping local LLM server")
        await local_llm_connector.stop_server()
    
    logger.info("UI-TARS integration stopped")

if __name__ == "__main__":
    # Run the main function
    asyncio.run(main())
