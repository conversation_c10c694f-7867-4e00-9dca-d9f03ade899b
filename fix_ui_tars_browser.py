"""
Fix UI-TARS Browser Detection Issues

This script fixes browser detection issues in UI-TARS 1.5 by:
1. Detecting browsers using enhanced detection
2. Setting up a browser sandbox environment
3. Configuring UI-TARS to use the detected browser
4. Enabling DPO for improved performance
5. Setting up virtual PC capabilities if needed
"""
import os
import sys
import json
import asyncio
import logging
import argparse
import platform
import shutil
import tempfile
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Union

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("fix_ui_tars_browser.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("fix_ui_tars_browser")

# Import UI-TARS utilities
try:
    from ui_tars.utils.enhanced_browser_detection import Enhanced<PERSON>rowserDete<PERSON>, BrowserInfo
    from ui_tars.utils.browser_sandbox import Browser<PERSON>andbox
    from ui_tars.utils.dpo_optimizer import DPOOptimizer
except ImportError:
    logger.warning("UI-TARS utilities not found in path, using local imports")
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    try:
        from ui_tars.utils.enhanced_browser_detection import EnhancedBrowserDetector, BrowserInfo
        from ui_tars.utils.browser_sandbox import BrowserSandbox
        from ui_tars.utils.dpo_optimizer import DPOOptimizer
    except ImportError:
        logger.error("Failed to import UI-TARS utilities. Make sure they are installed.")
        sys.exit(1)

class UITarsBrowserFixer:
    """Fix UI-TARS browser detection issues."""
    
    def __init__(self, 
                 ui_tars_path: Optional[str] = None,
                 config_path: Optional[str] = None,
                 browser_type: Optional[str] = None,
                 sandbox_mode: bool = True,
                 virtual_pc: bool = True,
                 dpo_enabled: bool = True,
                 debug_mode: bool = False):
        """
        Initialize the UI-TARS browser fixer.
        
        Args:
            ui_tars_path (Optional[str]): Path to UI-TARS executable
            config_path (Optional[str]): Path to UI-TARS configuration file
            browser_type (Optional[str]): Type of browser to use
            sandbox_mode (bool): Whether to use sandbox mode
            virtual_pc (bool): Whether to use virtual PC mode
            dpo_enabled (bool): Whether to enable DPO
            debug_mode (bool): Whether to enable debug mode
        """
        self.ui_tars_path = ui_tars_path
        self.config_path = config_path
        self.browser_type = browser_type
        self.sandbox_mode = sandbox_mode
        self.virtual_pc = virtual_pc
        self.dpo_enabled = dpo_enabled
        self.debug_mode = debug_mode
        self.os_type = platform.system()
        self.browser_detector = EnhancedBrowserDetector()
        self.browser_sandbox = None
        self.dpo_optimizer = None
        self.temp_dir = None
        self.ui_tars_process = None
        self.browser_process = None
        
        # Create temp directory
        self.temp_dir = tempfile.mkdtemp(prefix="ui_tars_browser_fix_")
        logger.info(f"Created temporary directory: {self.temp_dir}")
        
        # Find UI-TARS executable if not provided
        if not self.ui_tars_path:
            self.ui_tars_path = self._find_ui_tars_executable()
            
        # Find config path if not provided
        if not self.config_path:
            self.config_path = self._find_ui_tars_config()
            
    def _find_ui_tars_executable(self) -> Optional[str]:
        """Find the UI-TARS executable."""
        logger.info("Searching for UI-TARS executable...")
        
        if self.os_type == "Windows":
            # Common installation locations on Windows
            possible_paths = [
                os.path.join(os.environ.get("PROGRAMFILES", "C:\\Program Files"), "UI-TARS", "UI-TARS.exe"),
                os.path.join(os.environ.get("PROGRAMFILES(X86)", "C:\\Program Files (x86)"), "UI-TARS", "UI-TARS.exe"),
                os.path.join(os.environ.get("LOCALAPPDATA", "C:\\Users\\<USER>\\AppData\\Local".format(os.getlogin())), "UI-TARS", "UI-TARS.exe"),
                "UI-TARS.exe"
            ]
        elif self.os_type == "Darwin":  # macOS
            # Common installation locations on macOS
            possible_paths = [
                "/Applications/UI-TARS.app/Contents/MacOS/UI-TARS",
                os.path.expanduser("~/Applications/UI-TARS.app/Contents/MacOS/UI-TARS"),
            ]
        else:  # Linux
            # Common installation locations on Linux
            possible_paths = [
                "/usr/local/bin/ui-tars",
                "/usr/bin/ui-tars",
                os.path.expanduser("~/.local/bin/ui-tars"),
            ]
            
        # Check if any of the paths exist
        for path in possible_paths:
            if os.path.exists(path):
                logger.info(f"Found UI-TARS executable at: {path}")
                return path
                
        # Try to find in PATH
        try:
            if self.os_type == "Windows":
                result = subprocess.run(["where", "UI-TARS.exe"], capture_output=True, text=True)
            else:
                result = subprocess.run(["which", "ui-tars"], capture_output=True, text=True)
                
            if result.returncode == 0:
                path = result.stdout.strip()
                logger.info(f"Found UI-TARS executable in PATH: {path}")
                return path
        except Exception as e:
            logger.debug(f"Error searching for UI-TARS in PATH: {e}")
            
        logger.warning("Could not find UI-TARS executable")
        return None
        
    def _find_ui_tars_config(self) -> Optional[str]:
        """Find the UI-TARS configuration file."""
        logger.info("Searching for UI-TARS configuration file...")
        
        # Check common locations
        possible_paths = [
            "config/ui_tars_config.json",
            "ui_tars/config.json",
            "ui_tars_config.json",
            "ui_tars_config_v2.json"
        ]
        
        # Check if any of the paths exist
        for path in possible_paths:
            if os.path.exists(path):
                logger.info(f"Found UI-TARS configuration at: {path}")
                return path
                
        logger.warning("Could not find UI-TARS configuration file")
        return None
        
    async def detect_and_fix_browser_issues(self) -> bool:
        """Detect and fix browser detection issues."""
        logger.info("Detecting and fixing browser issues...")
        
        # Detect browsers
        browsers = self.browser_detector.detect_browsers(force_refresh=True)
        
        if not browsers:
            logger.error("No browsers detected")
            return False
            
        # Select browser
        selected_browser = None
        
        if self.browser_type:
            # Use specified browser if available
            selected_browser = self.browser_detector.get_browser(self.browser_type)
            if not selected_browser:
                logger.warning(f"Specified browser {self.browser_type} not found, using default")
                
        if not selected_browser:
            # Use default browser
            selected_browser = self.browser_detector.get_default_browser()
            
        if not selected_browser:
            # Use first available browser
            selected_browser = next(iter(browsers.values()))
            
        logger.info(f"Selected browser: {selected_browser}")
        
        # Create browser sandbox
        self.browser_sandbox = BrowserSandbox(
            browser_type=selected_browser.browser_type,
            user_data_dir=os.path.join(self.temp_dir, "browser_data"),
            remote_debugging_port=9222,
            headless=False,
            isolation_level="high" if self.sandbox_mode else "low",
            temp_dir=self.temp_dir
        )
        
        # Create DPO optimizer if enabled
        if self.dpo_enabled:
            self.dpo_optimizer = DPOOptimizer(
                model_name="UI-TARS-1.5-7B",
                preference_data_path=os.path.join(self.temp_dir, "dpo_preferences.json")
            )
            
        # Update or create UI-TARS configuration
        success = self._update_ui_tars_config(selected_browser)
        if not success:
            logger.error("Failed to update UI-TARS configuration")
            return False
            
        logger.info("Browser detection issues fixed successfully")
        return True
        
    def _update_ui_tars_config(self, browser_info: BrowserInfo) -> bool:
        """Update UI-TARS configuration with browser information."""
        logger.info("Updating UI-TARS configuration...")
        
        config_data = {}
        
        # Load existing configuration if available
        if self.config_path and os.path.exists(self.config_path):
            try:
                with open(self.config_path, "r") as f:
                    config_data = json.load(f)
            except Exception as e:
                logger.error(f"Error loading configuration: {e}")
                config_data = {}
                
        # Create new configuration if needed
        if not config_data:
            config_data = {
                "ui_tars": {
                    "version": "1.5",
                    "enabled": True
                }
            }
            
        # Update browser configuration
        if "ui_tars" not in config_data:
            config_data["ui_tars"] = {}
            
        if "browser" not in config_data["ui_tars"]:
            config_data["ui_tars"]["browser"] = {}
            
        # Update browser settings
        config_data["ui_tars"]["browser"].update({
            "type": browser_info.browser_type,
            "executable_path": browser_info.executable_path,
            "user_data_dir": browser_info.user_data_dir or os.path.join(self.temp_dir, "browser_data"),
            "profile_directory": browser_info.default_profile or "Default",
            "detection": {
                "auto_detect": True,
                "fallback_types": ["chrome", "edge", "firefox", "brave"]
            }
        })
        
        # Update sandbox settings
        if self.sandbox_mode:
            if "sandbox" not in config_data["ui_tars"]:
                config_data["ui_tars"]["sandbox"] = {}
                
            config_data["ui_tars"]["sandbox"].update({
                "enabled": True,
                "isolation_level": "high",
                "temp_dir": self.temp_dir
            })
            
        # Update virtual PC settings
        if self.virtual_pc:
            if "virtual_pc" not in config_data["ui_tars"]:
                config_data["ui_tars"]["virtual_pc"] = {}
                
            config_data["ui_tars"]["virtual_pc"].update({
                "enabled": True,
                "memory_mb": 2048,
                "cpu_cores": 2,
                "temp_dir": self.temp_dir
            })
            
        # Update DPO settings
        if self.dpo_enabled:
            if "dpo" not in config_data["ui_tars"]:
                config_data["ui_tars"]["dpo"] = {}
                
            config_data["ui_tars"]["dpo"].update({
                "enabled": True,
                "preference_model": "default"
            })
            
        # Update debug settings
        if "debug" not in config_data["ui_tars"]:
            config_data["ui_tars"]["debug"] = {}
            
        config_data["ui_tars"]["debug"].update({
            "enabled": self.debug_mode,
            "log_level": "debug" if self.debug_mode else "info",
            "log_file": os.path.join(self.temp_dir, "ui_tars_debug.log")
        })
        
        # Save updated configuration
        config_path = self.config_path
        if not config_path:
            config_path = os.path.join(self.temp_dir, "ui_tars_fixed_config.json")
            self.config_path = config_path
            
        try:
            with open(config_path, "w") as f:
                json.dump(config_data, f, indent=2)
                
            logger.info(f"Updated configuration saved to: {config_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving configuration: {e}")
            return False
            
    async def start_browser_sandbox(self) -> bool:
        """Start the browser sandbox."""
        if not self.browser_sandbox:
            logger.error("Browser sandbox not initialized")
            return False
            
        logger.info("Starting browser sandbox...")
        
        success = await self.browser_sandbox.start()
        if not success:
            logger.error("Failed to start browser sandbox")
            return False
            
        logger.info("Browser sandbox started successfully")
        return True
        
    async def start_ui_tars(self) -> bool:
        """Start UI-TARS with fixed configuration."""
        if not self.ui_tars_path:
            logger.error("UI-TARS executable not found")
            return False
            
        if not os.path.exists(self.ui_tars_path):
            logger.error(f"UI-TARS executable not found at: {self.ui_tars_path}")
            return False
            
        logger.info(f"Starting UI-TARS from: {self.ui_tars_path}")
        
        try:
            # Prepare command
            command = [self.ui_tars_path]
            
            if self.config_path:
                command.extend(["--config", self.config_path])
                
            # Add additional options
            if self.sandbox_mode:
                command.append("--sandbox")
                
            if self.virtual_pc:
                command.append("--virtual-pc")
                
            if self.dpo_enabled:
                command.append("--dpo")
                
            if self.debug_mode:
                command.append("--debug")
                
            # Start UI-TARS process
            self.ui_tars_process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Wait for the process to start
            await asyncio.sleep(5)
            
            # Check if process is still running
            if self.ui_tars_process.poll() is not None:
                logger.error(f"UI-TARS process exited with code: {self.ui_tars_process.returncode}")
                return False
                
            logger.info("UI-TARS started successfully")
            return True
            
        except Exception as e:
            logger.exception(f"Error starting UI-TARS: {e}")
            return False
            
    async def cleanup(self) -> None:
        """Clean up resources."""
        logger.info("Cleaning up resources...")
        
        # Stop UI-TARS process
        if self.ui_tars_process:
            try:
                self.ui_tars_process.terminate()
                await asyncio.sleep(2)
                
                if self.ui_tars_process.poll() is None:
                    self.ui_tars_process.kill()
                    
                logger.info("UI-TARS process terminated")
            except Exception as e:
                logger.error(f"Error terminating UI-TARS process: {e}")
                
        # Clean up browser sandbox
        if self.browser_sandbox:
            await self.browser_sandbox.cleanup()
            
        # Remove temp directory
        if self.temp_dir and os.path.exists(self.temp_dir):
            try:
                shutil.rmtree(self.temp_dir)
                logger.info(f"Temporary directory removed: {self.temp_dir}")
            except Exception as e:
                logger.error(f"Error removing temporary directory: {e}")

async def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Fix UI-TARS Browser Detection Issues")
    parser.add_argument("--path", type=str, help="Path to UI-TARS executable")
    parser.add_argument("--config", type=str, help="Path to UI-TARS configuration file")
    parser.add_argument("--browser", type=str, help="Type of browser to use (chrome, edge, firefox, brave)")
    parser.add_argument("--no-sandbox", action="store_true", help="Disable sandbox mode")
    parser.add_argument("--no-virtual-pc", action="store_true", help="Disable virtual PC mode")
    parser.add_argument("--no-dpo", action="store_true", help="Disable DPO")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    parser.add_argument("--start", action="store_true", help="Start UI-TARS after fixing")
    
    args = parser.parse_args()
    
    # Set log level
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        
    print("UI-TARS Browser Detection Fixer")
    print("===============================")
    print()
    
    # Create fixer
    fixer = UITarsBrowserFixer(
        ui_tars_path=args.path,
        config_path=args.config,
        browser_type=args.browser,
        sandbox_mode=not args.no_sandbox,
        virtual_pc=not args.no_virtual_pc,
        dpo_enabled=not args.no_dpo,
        debug_mode=args.debug
    )
    
    try:
        # Detect and fix browser issues
        success = await fixer.detect_and_fix_browser_issues()
        if not success:
            print("❌ Failed to fix browser detection issues")
            return 1
            
        print("✅ Browser detection issues fixed successfully")
        
        # Start browser sandbox if needed
        if not args.no_sandbox:
            sandbox_success = await fixer.start_browser_sandbox()
            if not sandbox_success:
                print("❌ Failed to start browser sandbox")
            else:
                print("✅ Browser sandbox started successfully")
                
        # Start UI-TARS if requested
        if args.start:
            ui_tars_success = await fixer.start_ui_tars()
            if not ui_tars_success:
                print("❌ Failed to start UI-TARS")
                return 1
                
            print("✅ UI-TARS started successfully with fixed configuration")
            
            # Keep running until user interrupts
            print()
            print("UI-TARS is running. Press Ctrl+C to stop...")
            
            try:
                while True:
                    await asyncio.sleep(1)
            except KeyboardInterrupt:
                print("Stopping UI-TARS...")
                
        return 0
        
    finally:
        # Clean up resources
        await fixer.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
