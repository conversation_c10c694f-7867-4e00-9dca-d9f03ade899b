@echo off
REM Start MPC Server Monitoring
REM This script starts the MPC server monitoring to ensure servers stay active

echo.
echo ===================================
echo    Start MPC Server Monitoring
echo ===================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Set configuration file path
set CONFIG_FILE=mpc_servers/config.json

REM Check if configuration file exists
if not exist "%CONFIG_FILE%" (
    echo Configuration file not found: %CONFIG_FILE%
    echo Creating default configuration file...
    
    REM Create directory if it doesn't exist
    if not exist "mpc_servers" mkdir mpc_servers
    
    REM Create default configuration file
    echo {> "%CONFIG_FILE%"
    echo     "servers": [>> "%CONFIG_FILE%"
    echo         {>> "%CONFIG_FILE%"
    echo             "id": "standard-mpc-server",>> "%CONFIG_FILE%"
    echo             "type": "standard",>> "%CONFIG_FILE%"
    echo             "host": "0.0.0.0",>> "%CONFIG_FILE%"
    echo             "port": 8765,>> "%CONFIG_FILE%"
    echo             "use_ssl": false,>> "%CONFIG_FILE%"
    echo             "auto_restart": true>> "%CONFIG_FILE%"
    echo         },>> "%CONFIG_FILE%"
    echo         {>> "%CONFIG_FILE%"
    echo             "id": "simple-mpc-server",>> "%CONFIG_FILE%"
    echo             "type": "simple",>> "%CONFIG_FILE%"
    echo             "host": "0.0.0.0",>> "%CONFIG_FILE%"
    echo             "port": 8766,>> "%CONFIG_FILE%"
    echo             "use_ssl": false,>> "%CONFIG_FILE%"
    echo             "auto_restart": true>> "%CONFIG_FILE%"
    echo         },>> "%CONFIG_FILE%"
    echo         {>> "%CONFIG_FILE%"
    echo             "id": "advanced-mpc-server",>> "%CONFIG_FILE%"
    echo             "type": "advanced",>> "%CONFIG_FILE%"
    echo             "host": "0.0.0.0",>> "%CONFIG_FILE%"
    echo             "port": 8767,>> "%CONFIG_FILE%"
    echo             "use_ssl": false,>> "%CONFIG_FILE%"
    echo             "security_tools_dir": "../tools",>> "%CONFIG_FILE%"
    echo             "auto_restart": true>> "%CONFIG_FILE%"
    echo         }>> "%CONFIG_FILE%"
    echo     ]>> "%CONFIG_FILE%"
    echo }>> "%CONFIG_FILE%"
    
    echo Default configuration file created: %CONFIG_FILE%
)

REM Set check interval
set /p CHECK_INTERVAL="Enter check interval in seconds (default: 60): "
if "%CHECK_INTERVAL%"=="" set CHECK_INTERVAL=60

REM Start monitoring
echo.
echo Starting MPC server monitoring with interval %CHECK_INTERVAL% seconds...
echo.

start "MPC Server Monitoring" cmd /k python monitor_mpc_servers.py --config "%CONFIG_FILE%" --interval %CHECK_INTERVAL%

echo.
echo MPC server monitoring started in a new window.
echo.
echo To stop monitoring, close the monitoring window or press Ctrl+C in that window.
echo.

pause
