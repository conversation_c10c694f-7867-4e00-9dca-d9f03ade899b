"""
Base agent class for the Multi-Agent AI System.

This module provides the base class for all agents in the system,
with common functionality for state management, communication,
and advanced memory capabilities.
"""
import asyncio
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Optional, Any, Union, Tuple
import uuid
import enum

from core.logger import setup_logger
from core.state_manager import StateManager
from core.advanced_memory import AdvancedMemory

class MessagePriority(enum.IntEnum):
    """Message priority levels."""
    LOW = 0
    NORMAL = 1
    HIGH = 2
    CRITICAL = 3

class BaseAgent(ABC):
    """
    Base class for all agents in the system.

    This abstract class defines the interface and common functionality
    for all specialized agents.
    """

    def __init__(
        self,
        agent_id: str,
        name: str = None,
        description: str = None,
        config: Optional[Dict] = None,
        message_queue: Optional[asyncio.Queue] = None,
        state_manager: Optional[StateManager] = None,
        shutdown_event: Optional[asyncio.Event] = None
    ):
        """
        Initialize the base agent.

        Args:
            agent_id (str): Unique identifier for the agent
            name (str, optional): Name of the agent
            description (str, optional): Description of the agent
            config (Optional[Dict]): Agent configuration
            message_queue (Optional[asyncio.Queue]): Queue for inter-agent communication
            state_manager (Optional[StateManager]): System state manager
            shutdown_event (Optional[asyncio.Event]): Event to signal system shutdown
        """
        self.agent_id = agent_id
        self.config = config or {}
        self.state_manager = state_manager
        self.message_queue = message_queue
        self.shutdown_event = shutdown_event

        self.name = name or (config.get("name", f"Agent-{agent_id}") if config else f"Agent-{agent_id}")
        self.description = description or (config.get("description", "") if config else "")
        self.status = "initializing"
        self.last_active = None
        self.polling_interval = config.get("polling_interval", 300) if config else 300  # seconds

        # Set up logger
        self.logger = setup_logger(f"agent.{agent_id}")

        # Agent state
        self.state = {}
        self.tasks = []

        # Advanced memory
        self.memory = None

        # Pending responses
        self.pending_responses = {}

        # Message tracking
        self.message_stats = {
            "sent": 0,
            "received": 0,
            "errors": 0,
        }

        # Services
        self.services = {}

    async def initialize(self):
        """Initialize the agent and load its state."""
        self.logger.info(f"Initializing agent: {self.name}")

        # Initialize state
        if self.state_manager:
            # Load agent state
            agent_state = await self.state_manager.get_state("agents", self.agent_id)
            if agent_state:
                self.state = agent_state
                self.logger.info(f"Loaded existing state for agent {self.agent_id}")
            else:
                # Initialize with default state
                self.state = {
                    "created_at": datetime.now().isoformat(),
                    "last_active": None,
                    "status": "initialized",
                    "tasks": [],
                    "capabilities": self.config.get("capabilities", []) if self.config else [],
                }
                await self.state_manager.update_state("agents", self.agent_id, self.state)
                self.logger.info(f"Created new state for agent {self.agent_id}")

            # Initialize advanced memory
            self.memory = AdvancedMemory(self.agent_id, self.state_manager)
            await self.memory.initialize()
            self.logger.info(f"Initialized advanced memory for agent {self.agent_id}")
        else:
            # Initialize with default state without state manager
            self.state = {
                "created_at": datetime.now().isoformat(),
                "last_active": None,
                "status": "initialized",
                "tasks": [],
                "capabilities": self.config.get("capabilities", []) if self.config else [],
            }
            self.logger.info(f"Created new in-memory state for agent {self.agent_id} (no state manager)")

            # Create a simple memory implementation
            class SimpleMemory:
                def __init__(self, logger):
                    self.memories = []
                    self.logger = logger

                async def add_memory(self, content, memory_type="episodic", source="test", importance=0.5, metadata=None):
                    memory = {
                        "id": str(uuid.uuid4()),
                        "content": content,
                        "type": memory_type,
                        "source": source,
                        "importance": importance,
                        "metadata": metadata or {},
                        "timestamp": datetime.now().isoformat()
                    }
                    self.memories.append(memory)
                    self.logger.debug(f"Added memory: {memory['id']}")
                    return memory

                async def initialize(self):
                    pass

            self.memory = SimpleMemory(self.logger)
            await self.memory.initialize()
            self.logger.info(f"Initialized simple memory for agent {self.agent_id}")

        # Access shared services from agent manager if available
        try:
            # Get agent manager from the message queue
            if hasattr(self.message_queue, "agent_manager"):
                agent_manager = self.message_queue.agent_manager

                # Get available services
                services = agent_manager.get_all_services()

                # Register services with this agent
                for service_id, service in services.items():
                    self.register_service(service_id, service)
        except Exception as e:
            self.logger.warning(f"Error accessing shared services: {e}")

        # Set status to initialized
        self.status = "initialized"
        await self._update_status("initialized")

    async def run(self):
        """Run the agent's main loop."""
        self.logger.info(f"Starting agent: {self.name}")
        await self._update_status("running")

        try:
            while not self.shutdown_event.is_set():
                # Update last active timestamp
                self.last_active = datetime.now()
                await self._update_last_active()

                # Execute agent-specific logic
                await self.execute_cycle()

                # Wait for next cycle
                await asyncio.sleep(self.polling_interval)

        except asyncio.CancelledError:
            self.logger.info(f"Agent {self.agent_id} cancelled")
            raise
        except Exception as e:
            self.logger.exception(f"Error in agent {self.agent_id}: {e}")
            await self._update_status("error")

    @abstractmethod
    async def execute_cycle(self):
        """
        Execute one cycle of the agent's logic.

        This method should be implemented by each specialized agent.
        """
        pass

    async def shutdown(self):
        """Shutdown the agent and save its state."""
        self.logger.info(f"Shutting down agent: {self.name}")
        await self._update_status("shutdown")

        # Save final state
        if self.state_manager:
            await self.state_manager.update_state("agents", self.agent_id, self.state)

    async def receive_message(self, message: Dict):
        """
        Process a received message.

        Args:
            message (Dict): Message to process
        """
        self.logger.debug(f"Received message: {message}")

        # Update message stats
        self.message_stats["received"] += 1

        # Store message in memory
        await self.memory.add_memory(
            content=message,
            memory_type="episodic",
            source=f"message:{message.get('sender_id')}",
            metadata={
                "sender_id": message.get("sender_id"),
                "message_type": message.get("type"),
                "timestamp": message.get("timestamp"),
            }
        )

        # Check if this is a response to a pending message
        in_response_to = message.get("in_response_to")
        if in_response_to and in_response_to in self.pending_responses:
            self.pending_responses[in_response_to] = message

        # Process message based on type
        message_type = message.get("type", "unknown")

        try:
            if message_type == "command":
                await self.handle_command(message)
            elif message_type == "query":
                await self.handle_query(message)
            elif message_type == "notification":
                await self.handle_notification(message)
            elif message_type == "response":
                await self.handle_response(message)
            elif message_type == "error":
                await self.handle_error(message)
            elif message_type == "broadcast":
                await self.handle_broadcast(message)
            elif message_type == "collaboration":
                await self.handle_collaboration(message)
            elif message_type == "delegation":
                await self.handle_delegation(message)
            elif message_type == "feedback":
                await self.handle_feedback(message)
            else:
                await self.handle_unknown_message(message)
        except Exception as e:
            self.logger.exception(f"Error handling message: {e}")
            self.message_stats["errors"] += 1

            # Send error response
            await self.send_message(
                message.get("sender_id"),
                "error",
                {
                    "error": str(e),
                    "original_message": message.get("id"),
                },
                priority=MessagePriority.HIGH
            )

    async def send_message(
        self,
        recipient_id: Optional[str],
        message_type: str,
        content: Any,
        priority: MessagePriority = MessagePriority.NORMAL,
        in_response_to: Optional[str] = None,
        metadata: Optional[Dict] = None,
    ) -> str:
        """
        Send a message to another agent.

        Args:
            recipient_id (Optional[str]): Recipient agent ID, None for broadcast
            message_type (str): Type of message
            content (Any): Message content
            priority (MessagePriority): Message priority
            in_response_to (Optional[str]): ID of message this is responding to
            metadata (Optional[Dict]): Additional metadata

        Returns:
            str: Message ID
        """
        # Create message
        message_id = str(uuid.uuid4())
        message = {
            "id": message_id,
            "sender_id": self.agent_id,
            "sender_name": self.name,
            "recipient_id": recipient_id,
            "type": message_type,
            "content": content,
            "timestamp": datetime.now().isoformat(),
            "in_response_to": in_response_to,
            "metadata": metadata or {},
            "priority": priority.value,
        }

        # Add to queue
        await self.message_queue.put(message)

        # Update stats
        self.message_stats["sent"] += 1

        # Store in memory
        await self.memory.add_memory(
            content=message,
            memory_type="episodic",
            source="outgoing_message",
            metadata={
                "recipient_id": recipient_id,
                "message_type": message_type,
                "priority": priority.value,
            }
        )

        self.logger.debug(f"Sent message {message_id} to {recipient_id}: {message_type}")

        return message_id

    async def handle_command(self, message: Dict):
        """
        Handle a command message.

        Args:
            message (Dict): Command message
        """
        self.logger.debug(f"Handling command: {message}")

        # Default implementation - can be overridden by specialized agents
        command = message.get("content", {}).get("command")
        if command == "status":
            # Respond with status
            await self.send_message(
                message.get("sender_id"),
                "response",
                {
                    "command": "status",
                    "status": self.status,
                    "last_active": self.last_active.isoformat() if self.last_active else None,
                }
            )
        else:
            # Unknown command
            await self.send_message(
                message.get("sender_id"),
                "error",
                {
                    "command": command,
                    "error": "Unknown command",
                }
            )

    async def handle_query(self, message: Dict):
        """
        Handle a query message.

        Args:
            message (Dict): Query message
        """
        self.logger.debug(f"Handling query: {message}")

        # Default implementation - should be overridden by specialized agents
        query = message.get("content", {}).get("query")
        await self.send_message(
            message.get("sender_id"),
            "response",
            {
                "query": query,
                "error": "Query not implemented",
            }
        )

    async def handle_notification(self, message: Dict):
        """
        Handle a notification message.

        Args:
            message (Dict): Notification message
        """
        self.logger.debug(f"Handling notification: {message}")

        # Default implementation - can be overridden by specialized agents
        # Just acknowledge receipt
        await self.send_message(
            message.get("sender_id"),
            "acknowledgement",
            {
                "notification_id": message.get("id"),
                "status": "received",
            }
        )

    async def handle_response(self, message: Dict):
        """
        Handle a response message.

        Args:
            message (Dict): Response message
        """
        self.logger.debug(f"Handling response: {message}")

        # Default implementation - can be overridden by specialized agents
        # Just store the response
        in_response_to = message.get("in_response_to")
        if in_response_to:
            self.pending_responses[in_response_to] = message

    async def handle_error(self, message: Dict):
        """
        Handle an error message.

        Args:
            message (Dict): Error message
        """
        self.logger.warning(f"Handling error: {message}")

        # Default implementation - can be overridden by specialized agents
        # Just log the error
        content = message.get("content", {})
        error = content.get("error", "Unknown error")
        original_message = content.get("original_message")

        self.logger.error(f"Error from {message.get('sender_id')}: {error} (original message: {original_message})")

    async def handle_broadcast(self, message: Dict):
        """
        Handle a broadcast message.

        Args:
            message (Dict): Broadcast message
        """
        self.logger.debug(f"Handling broadcast: {message}")

        # Default implementation - can be overridden by specialized agents
        # Just acknowledge receipt
        await self.send_message(
            message.get("sender_id"),
            "acknowledgement",
            {
                "broadcast_id": message.get("id"),
                "status": "received",
            }
        )

    async def handle_collaboration(self, message: Dict):
        """
        Handle a collaboration message.

        Args:
            message (Dict): Collaboration message
        """
        self.logger.debug(f"Handling collaboration: {message}")

        # Default implementation - can be overridden by specialized agents
        # Just acknowledge receipt
        await self.send_message(
            message.get("sender_id"),
            "response",
            {
                "collaboration_id": message.get("id"),
                "status": "received",
                "message": "Collaboration not implemented",
            },
            in_response_to=message.get("id")
        )

    async def handle_delegation(self, message: Dict):
        """
        Handle a delegation message.

        Args:
            message (Dict): Delegation message
        """
        self.logger.debug(f"Handling delegation: {message}")

        # Default implementation - can be overridden by specialized agents
        # Just acknowledge receipt
        await self.send_message(
            message.get("sender_id"),
            "response",
            {
                "delegation_id": message.get("id"),
                "status": "received",
                "message": "Delegation not implemented",
            },
            in_response_to=message.get("id")
        )

    async def handle_feedback(self, message: Dict):
        """
        Handle a feedback message.

        Args:
            message (Dict): Feedback message
        """
        self.logger.debug(f"Handling feedback: {message}")

        # Default implementation - can be overridden by specialized agents
        # Just acknowledge receipt
        await self.send_message(
            message.get("sender_id"),
            "acknowledgement",
            {
                "feedback_id": message.get("id"),
                "status": "received",
            }
        )

    async def handle_unknown_message(self, message: Dict):
        """
        Handle an unknown message type.

        Args:
            message (Dict): Unknown message
        """
        self.logger.warning(f"Received unknown message type: {message}")

        # Respond with error
        await self.send_message(
            message.get("sender_id"),
            "error",
            {
                "error": "Unknown message type",
                "original_message": message.get("id"),
            },
            priority=MessagePriority.NORMAL
        )

    async def _update_status(self, status: str):
        """
        Update the agent's status.

        Args:
            status (str): New status
        """
        self.status = status
        self.state["status"] = status
        if self.state_manager:
            await self.state_manager.update_state("agents", self.agent_id, self.state)

    async def _update_last_active(self):
        """Update the agent's last active timestamp."""
        self.last_active = datetime.now()
        self.state["last_active"] = self.last_active.isoformat()
        if self.state_manager:
            await self.state_manager.update_state("agents", self.agent_id, self.state)

    def get_service(self, service_id: str) -> Optional[Any]:
        """
        Get a service by ID.

        Args:
            service_id (str): Service identifier

        Returns:
            Optional[Any]: Service instance if found, None otherwise
        """
        return self.services.get(service_id)

    def register_service(self, service_id: str, service: Any) -> None:
        """
        Register a service for this agent.

        Args:
            service_id (str): Service identifier
            service (Any): Service instance
        """
        self.services[service_id] = service
        self.logger.info(f"Registered service: {service_id}")

    async def execute_action(self, action: str, params: Optional[Dict] = None) -> Dict:
        """
        Execute an action by name with parameters.

        This method is used by the workflow engine to execute agent actions.

        Args:
            action (str): Action name (method name)
            params (Optional[Dict]): Action parameters

        Returns:
            Dict: Action result
        """
        self.logger.info(f"Executing action: {action} with params: {params}")

        # Check if the action exists
        if not hasattr(self, action) or not callable(getattr(self, action)):
            error_msg = f"Action not found: {action}"
            self.logger.error(error_msg)
            return {"error": error_msg}

        try:
            # Get the action method
            action_method = getattr(self, action)

            # Execute the action with parameters
            if params:
                result = await action_method(**params)
            else:
                result = await action_method()

            return result if result is not None else {"status": "completed"}

        except Exception as e:
            error_msg = f"Error executing action {action}: {str(e)}"
            self.logger.exception(error_msg)
            return {"error": error_msg}
