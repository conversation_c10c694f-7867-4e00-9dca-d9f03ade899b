@echo off
echo Starting Enhanced UI-TARS with Jarvis integration...

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Check if required packages are installed
python -c "import requests, psutil, asyncio" >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing required packages...
    python -m pip install requests psutil
)

REM Run diagnostic first
echo Running diagnostic check...
python ui_tars_diagnostic.py --fix --start --enhanced

REM Start enhanced UI-TARS with <PERSON> integration
echo Starting enhanced UI-TARS with Jarvis integration...
python start_enhanced_ui_tars.py --all

pause
