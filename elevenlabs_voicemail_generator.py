"""
ElevenLabs Voicemail Generator for the Multi-Agent AI System.

This module provides integration with ElevenLabs for generating voicemails
with female voices for the insurance drip campaign.
"""
import asyncio
import json
import logging
import os
import sys
import time
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
import aiohttp
import base64

# Add parent directory to path to import from core
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.logger import setup_logger

# Set up logger
logger = setup_logger("elevenlabs_voicemail_generator")

class ElevenLabsVoicemailGenerator:
    """
    ElevenLabs Voicemail Generator for the Multi-Agent AI System.

    This class provides integration with ElevenLabs for generating voicemails
    with female voices for the insurance drip campaign.
    """
    
    def __init__(self, 
                 api_key: Optional[str] = None,
                 voice_id: str = "21m00Tcm4TlvDq8ikWAM",  # Default female voice (<PERSON>)
                 output_dir: str = "voicemails"):
        """
        Initialize the ElevenLabs Voicemail Generator.
        
        Args:
            api_key (Optional[str]): ElevenLabs API key
            voice_id (str): Voice ID to use
            output_dir (str): Directory to save voicemail files
        """
        self.api_key = api_key or os.environ.get("ELEVENLABS_API_KEY")
        self.voice_id = voice_id
        self.output_dir = output_dir
        self.api_url = "https://api.elevenlabs.io/v1"
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Available female voices
        self.female_voices = {
            "rachel": "21m00Tcm4TlvDq8ikWAM",  # Rachel
            "domi": "AZnzlk1XvdvUeBnXmlld",    # Domi
            "bella": "EXAVITQu4vr4xnSDxMaL",   # Bella
            "elli": "MF3mGyEYCl7XYWbV9V6O",    # Elli
            "grace": "oWAxZDx7w5VEj9dCyTzz"     # Grace
        }
    
    async def check_api_status(self) -> Dict[str, Any]:
        """
        Check the status of the ElevenLabs API.
        
        Returns:
            Dict[str, Any]: API status
        """
        if not self.api_key:
            return {"status": "error", "error": "API key not provided"}
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.api_url}/user/subscription",
                    headers={"xi-api-key": self.api_key}
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            "status": "available",
                            "tier": data.get("tier"),
                            "character_count": data.get("character_count"),
                            "character_limit": data.get("character_limit")
                        }
                    else:
                        return {
                            "status": "error",
                            "error": f"API error: {response.status}",
                            "details": await response.text()
                        }
        except Exception as e:
            logger.exception(f"Error checking ElevenLabs API status: {e}")
            return {"status": "error", "error": str(e)}
    
    async def list_voices(self) -> Dict[str, Any]:
        """
        List available voices.
        
        Returns:
            Dict[str, Any]: Available voices
        """
        if not self.api_key:
            return {"status": "error", "error": "API key not provided"}
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.api_url}/voices",
                    headers={"xi-api-key": self.api_key}
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        voices = data.get("voices", [])
                        
                        # Filter female voices
                        female_voices = [
                            voice for voice in voices
                            if voice.get("labels", {}).get("gender") == "female"
                        ]
                        
                        return {
                            "status": "success",
                            "voices": voices,
                            "female_voices": female_voices
                        }
                    else:
                        return {
                            "status": "error",
                            "error": f"API error: {response.status}",
                            "details": await response.text()
                        }
        except Exception as e:
            logger.exception(f"Error listing ElevenLabs voices: {e}")
            return {"status": "error", "error": str(e)}
    
    async def generate_voicemail(self, 
                               script: str, 
                               voice_name: Optional[str] = None,
                               client_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate a voicemail using ElevenLabs.
        
        Args:
            script (str): Voicemail script
            voice_name (Optional[str]): Voice name to use
            client_name (Optional[str]): Client name for filename
            
        Returns:
            Dict[str, Any]: Result of the operation
        """
        if not self.api_key:
            return {"status": "error", "error": "API key not provided"}
        
        # Determine voice ID
        voice_id = self.voice_id
        if voice_name and voice_name.lower() in self.female_voices:
            voice_id = self.female_voices[voice_name.lower()]
        
        # Generate filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"voicemail_{timestamp}.mp3"
        if client_name:
            filename = f"voicemail_{client_name.replace(' ', '_')}_{timestamp}.mp3"
        
        filepath = os.path.join(self.output_dir, filename)
        
        try:
            # Prepare request
            url = f"{self.api_url}/text-to-speech/{voice_id}"
            headers = {
                "xi-api-key": self.api_key,
                "Content-Type": "application/json"
            }
            data = {
                "text": script,
                "model_id": "eleven_monolingual_v1",
                "voice_settings": {
                    "stability": 0.5,
                    "similarity_boost": 0.75
                }
            }
            
            # Make request
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, json=data) as response:
                    if response.status == 200:
                        # Save audio file
                        with open(filepath, "wb") as f:
                            f.write(await response.read())
                        
                        logger.info(f"Voicemail generated and saved to {filepath}")
                        
                        return {
                            "status": "success",
                            "filepath": filepath,
                            "filename": filename,
                            "voice_id": voice_id,
                            "voice_name": voice_name or "default"
                        }
                    else:
                        error_text = await response.text()
                        logger.error(f"Error generating voicemail: {response.status} - {error_text}")
                        
                        return {
                            "status": "error",
                            "error": f"API error: {response.status}",
                            "details": error_text
                        }
        except Exception as e:
            logger.exception(f"Error generating voicemail: {e}")
            return {"status": "error", "error": str(e)}
    
    async def generate_voicemail_for_client(self,
                                          client_name: str,
                                          phone_number: str,
                                          insurance_type: str,
                                          voice_name: str = "rachel") -> Dict[str, Any]:
        """
        Generate a personalized voicemail for a client.
        
        Args:
            client_name (str): Client's name
            phone_number (str): Client's phone number
            insurance_type (str): Type of insurance
            voice_name (str): Voice name to use
            
        Returns:
            Dict[str, Any]: Result of the operation
        """
        # Extract first name
        first_name = client_name.split()[0] if client_name else ""
        
        # Generate script
        script = f"""
        Hi {first_name}, this is Sandra from Flo Faction Insurance. 
        I'm calling about your interest in {insurance_type} insurance. 
        I'd love to discuss your options and answer any questions you might have.
        Please give me a call back at ************ when you have a moment.
        Thanks, and have a great day!
        """
        
        # Generate voicemail
        result = await self.generate_voicemail(
            script=script,
            voice_name=voice_name,
            client_name=client_name
        )
        
        return result
