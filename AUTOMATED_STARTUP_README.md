# Automated Startup System for AI Agent Framework

This system provides automated startup capabilities for the AI agent framework, allowing it to launch automatically on Windows startup and immediately present the Jarvis AI interface without requiring manual activation.

## Components

The automated startup system consists of the following components:

1. **auto_start_ai_system.ps1**: Main PowerShell script that starts all components
2. **start_jarvis_with_terminal.ps1**: <PERSON><PERSON><PERSON> that starts <PERSON> in a terminal window
3. **setup_ai_system_autostart.ps1**: <PERSON><PERSON>t that sets up Windows Task Scheduler
4. **borg_cluster/jarvis_interface_autostart.py**: Modified Jarvis interface with auto-start support
5. **crewai_integration.py**: Integration with CrewAI framework

## Installation

To install the automated startup system:

1. Run the setup script as Administrator:
   ```powershell
   powershell -ExecutionPolicy Bypass -File setup_ai_system_autostart.ps1
   ```

2. This will:
   - Create a scheduled task to start the AI system at login
   - Create a desktop shortcut for manual startup
   - Generate a README file with detailed instructions

## Manual Startup

If you need to start the system manually:

1. Double-click the "AI Agent System" shortcut on your desktop, or
2. Run the following command:
   ```powershell
   powershell -ExecutionPolicy Bypass -File auto_start_ai_system.ps1
   ```

## Components Started Automatically

When the system starts, the following components are initialized:

1. **AlphaEvolve Service**: Runs in the background for evolutionary optimization
2. **MPC Servers**: Provides multi-party computation capabilities
3. **UI-TARS 1.5**: Provides browser automation and UI interaction
4. **Jarvis AI Interface**: Provides natural language interaction with the system
5. **VS Code**: Opens with the project workspace for monitoring and development
6. **CrewAI Framework**: Provides agent crew management capabilities

## Using the System

Once started, the Jarvis AI Interface will be immediately ready to accept natural language commands.

### Key Features

1. **Immediate Activation**: Jarvis is ready to accept commands as soon as the system starts
2. **Exit Key Combination**: Press Ctrl+Alt+X to exit Jarvis and return to the regular terminal
3. **VS Code Integration**: VS Code opens automatically with the project workspace
4. **Terminal Integration**: A terminal window opens with the Jarvis interface

### CrewAI Integration

The system includes integration with the CrewAI framework, which provides:

1. **Agent Crews**: Organize agents into functional crews for complex tasks
2. **Task Management**: Delegate tasks to appropriate agents within crews
3. **Autonomous Operation**: Crews can work autonomously on delegated tasks
4. **Load Balancing**: Distribute workload across agents to prevent overloading

Default crews are created for:
- Insurance business operations
- Trading and asset management
- Social media marketing
- Music management
- Cybersecurity

## Troubleshooting

If the system doesn't start automatically:

1. Check the Task Scheduler to ensure the task is enabled
2. Try running the desktop shortcut manually
3. Check the logs in the 'logs' directory for any errors
4. Run setup_ai_system_autostart.ps1 again as Administrator

## Uninstalling

To remove the autostart configuration:

1. Open Task Scheduler and delete the 'StartAIAgentSystem' task
2. Delete the desktop shortcut

## Dependencies

The system requires the following dependencies:

1. Python 3.8 or higher
2. PowerShell 5.1 or higher
3. Windows Task Scheduler
4. CrewAI (install with `pip install crewai>=0.28.0`)
5. Keyboard module (install with `pip install keyboard`)

## Configuration

You can customize the system by modifying the following files:

1. **auto_start_ai_system.ps1**: Change which components are started
2. **borg_cluster/jarvis_interface_autostart.py**: Modify Jarvis interface behavior
3. **crewai_integration.py**: Configure CrewAI integration and agent crews

## Security Considerations

The system runs with elevated privileges to ensure all components can start properly. This is necessary for:

1. Starting services
2. Opening applications
3. Managing system resources

However, this means the system has access to sensitive operations. Ensure you trust all components before enabling autostart.

## Updating

When updating the AI agent framework, you may need to update the automated startup system as well. To do this:

1. Stop any running instances of the system
2. Update the framework files
3. Run setup_ai_system_autostart.ps1 again to update the autostart configuration
