"""
Jarvis AlphaEvolve Integration.

This script integrates AlphaEvolve with the Jarvis Interface,
enabling command-line control of evolutionary optimization processes.
"""
import asyncio
import argparse
import logging
import os
import sys
import signal
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent))

from core.logger import setup_logger
from core.state_manager import StateManager
from borg_cluster.jarvis_interface import JarvisInter<PERSON>
from alpha_evolve.alpha_evolve_engine import AlphaEvolveEngine
from alpha_evolve.integration.jarvis_integration import JarvisAlphaEvolveCommands
from alpha_evolve.integration.borg_integration import BorgIntegration
from alpha_evolve.integration.agent_integration import AgentIntegration
from core.agent_manager import AgentManager

# Set up logger
logger = setup_logger("jarvis_alpha_evolve_integration")

# Global flag to control system shutdown
shutdown_event = asyncio.Event()

async def integrate_with_jarvis():
    """Integrate AlphaEvolve with the Jarvis Interface."""
    logger.info("Integrating AlphaEvolve with Jarvis Interface")
    
    # Initialize state manager
    state_manager = StateManager()
    await state_manager.initialize()
    
    # Initialize AlphaEvolve engine
    alpha_evolve_engine = AlphaEvolveEngine(
        state_manager=state_manager,
    )
    await alpha_evolve_engine.initialize()
    
    # Initialize Jarvis interface
    jarvis_interface = JarvisInterface()
    await jarvis_interface.initialize()
    
    # Initialize Borg integration
    borg_integration = BorgIntegration(
        alpha_evolve_engine=alpha_evolve_engine,
    )
    await borg_integration.initialize()
    
    # Initialize agent integration
    agent_integration = AgentIntegration(
        alpha_evolve_engine=alpha_evolve_engine,
    )
    await agent_integration.initialize()
    
    # Initialize Jarvis commands
    jarvis_commands = JarvisAlphaEvolveCommands(
        jarvis_interface=jarvis_interface,
        alpha_evolve_engine=alpha_evolve_engine,
        borg_integration=borg_integration,
        agent_integration=agent_integration,
    )
    await jarvis_commands.initialize()
    
    logger.info("AlphaEvolve integrated with Jarvis Interface")
    
    # Wait for shutdown signal
    await shutdown_event.wait()
    
    # Shutdown components
    await alpha_evolve_engine.shutdown()
    await borg_integration.shutdown()
    await agent_integration.shutdown()
    
    logger.info("AlphaEvolve integration shut down")

def signal_handler():
    """Handle termination signals."""
    logger.info("Received termination signal")
    shutdown_event.set()

def main():
    """Main entry point."""
    # Set up signal handlers
    for sig in (signal.SIGINT, signal.SIGTERM):
        signal.signal(sig, lambda signum, frame: signal_handler())
    
    # Run integration
    asyncio.run(integrate_with_jarvis())

if __name__ == "__main__":
    main()
