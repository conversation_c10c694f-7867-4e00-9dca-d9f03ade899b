"""
Create a Desktop OAuth client for Gmail authentication.
This script helps you create a Desktop OAuth client that doesn't require verification.
"""
import os
import sys
import webbrowser
import subprocess
from pathlib import Path

def clear_screen():
    """Clear the terminal screen."""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_header():
    """Print the script header."""
    clear_screen()
    print("=" * 80)
    print("                     CREATE DESKTOP OAUTH CLIENT")
    print("=" * 80)
    print("\nThis script will help you create a Desktop OAuth client that doesn't require verification.")
    print("You'll be guided through each step with clear instructions.")
    print("\n")

def open_browser_with_confirmation(url, description):
    """
    Open a URL in the browser and confirm with the user.
    
    Args:
        url (str): URL to open
        description (str): Description of what the URL is for
    """
    print(f"\nOpening {description} in your browser...")
    print(f"URL: {url}")
    
    # Try to open the browser
    try:
        # First attempt with the default browser
        webbrowser.open(url)
        print("✓ Browser should be opening now.")
    except Exception as e:
        print(f"Error opening browser: {e}")
        print("\nPlease manually open this URL in your browser:")
        print(url)
    
    # Ask for confirmation
    confirmation = input("\nDid the browser open correctly? (y/n): ").lower()
    
    if confirmation != 'y':
        print("\nLet's try again with a different method.")
        
        try:
            # Try with a specific browser
            if os.name == 'nt':  # Windows
                os.startfile(url)
            else:
                # Try common browsers on other platforms
                browsers = ['google-chrome', 'chrome', 'firefox', 'safari']
                for browser in browsers:
                    try:
                        subprocess.Popen([browser, url])
                        break
                    except:
                        continue
            
            print("✓ Browser should be opening now with an alternative method.")
        except Exception as e:
            print(f"Error opening browser with alternative method: {e}")
            print("\nPlease manually open this URL in your browser:")
            print(url)
        
        # Final confirmation
        input("\nPress Enter when you have the page open in your browser...")
    
    return

def create_desktop_oauth_client():
    """Create a Desktop OAuth client."""
    print_header()
    
    print("STEP 1: GO TO GOOGLE CLOUD CONSOLE")
    print("-" * 80)
    
    # Open Google Cloud Console
    open_browser_with_confirmation(
        "https://console.cloud.google.com/apis/credentials",
        "Google Cloud Console Credentials page"
    )
    
    print("\nSTEP 2: CREATE A NEW OAUTH CLIENT ID")
    print("-" * 80)
    
    print("\nFollow these steps to create a new OAuth client ID:")
    print("1. Click on '+ CREATE CREDENTIALS' at the top of the page")
    print("2. Select 'OAuth client ID' from the dropdown menu")
    print("3. For 'Application type', select 'Desktop app'")
    print("4. Enter a name for your client (e.g., 'Gmail Desktop Client')")
    print("5. Click 'CREATE'")
    
    input("\nPress Enter when you've created the OAuth client ID...")
    
    print("\nSTEP 3: DOWNLOAD THE CLIENT SECRET JSON FILE")
    print("-" * 80)
    
    print("\nAfter creating the OAuth client ID, you'll see a dialog with your client ID and client secret.")
    print("Click 'DOWNLOAD JSON' to download the client secret JSON file.")
    
    input("\nPress Enter when you've downloaded the client secret JSON file...")
    
    print("\nSTEP 4: SAVE THE CLIENT SECRET JSON FILE")
    print("-" * 80)
    
    print("\nNow you need to save the client secret JSON file for each Gmail account you want to authenticate.")
    
    # Create credentials directory if it doesn't exist
    os.makedirs('credentials', exist_ok=True)
    
    # Ask for the path to the downloaded file
    downloaded_file = input("\nEnter the path to the downloaded client secret JSON file: ")
    
    if not os.path.exists(downloaded_file):
        print(f"\nError: File not found at {downloaded_file}")
        retry = input("Do you want to try again? (y/n): ").lower()
        if retry == 'y':
            return create_desktop_oauth_client()
        return
    
    # Ask which accounts to configure
    print("\nWhich Gmail accounts do you want to configure with this client?")
    print("1. <EMAIL>")
    print("2. <EMAIL>")
    print("3. <EMAIL>")
    print("4. All of the above")
    
    choice = input("\nEnter your choice (1-4): ")
    
    accounts = []
    if choice == '1':
        accounts = ['<EMAIL>']
    elif choice == '2':
        accounts = ['<EMAIL>']
    elif choice == '3':
        accounts = ['<EMAIL>']
    elif choice == '4':
        accounts = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ]
    else:
        print("\nInvalid choice. Please try again.")
        return create_desktop_oauth_client()
    
    # Copy the file for each account
    for email in accounts:
        safe_email = email.replace("@", "_at_").replace(".", "_dot_")
        credentials_path = f'credentials/gmail_{safe_email}_credentials.json'
        
        try:
            import shutil
            shutil.copy(downloaded_file, credentials_path)
            print(f"\n✓ Credentials file copied to {credentials_path} for {email}")
        except Exception as e:
            print(f"\nError copying file for {email}: {e}")
    
    print("\n✓ Desktop OAuth client created and configured successfully!")
    print("\nNext steps:")
    print("1. Run the test_gmail_auth.py script to test authentication")
    print("2. If you still encounter issues, try using a different Google account")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    try:
        create_desktop_oauth_client()
    except KeyboardInterrupt:
        print("\n\nExiting...")
        sys.exit(0)
