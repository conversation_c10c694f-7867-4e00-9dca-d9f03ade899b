"""
Refresh Calendly OAuth Token

This script refreshes your Calendly OAuth token.
"""
import requests
import json
import os
import time
from datetime import datetime, <PERSON><PERSON><PERSON>

def refresh_token():
    """Refresh the Calendly OAuth token."""
    # Load credentials
    try:
        with open("credentials/calendly/calendly.json", "r") as f:
            credentials = json.load(f)
        
        # Check if we have a refresh token
        if "refresh_token" not in credentials:
            print("No refresh token found in credentials file")
            print("Please run get_calendly_token.py to get a new token")
            return False
        
        # Check if we have client ID and client secret
        if "client_id" not in credentials:
            client_id = input("Enter your Calendly client ID: ")
            credentials["client_id"] = client_id
        else:
            client_id = credentials["client_id"]
        
        if "client_secret" not in credentials:
            client_secret = input("Enter your Calendly client secret: ")
        else:
            client_secret = input("Enter your Calendly client secret: ")
        
        # Get refresh token
        refresh_token = credentials["refresh_token"]
        
        # Exchange refresh token for new access token
        response = requests.post(
            "https://auth.calendly.com/oauth/token",
            data={
                "client_id": client_id,
                "client_secret": client_secret,
                "refresh_token": refresh_token,
                "grant_type": "refresh_token"
            }
        )
        
        if response.status_code == 200:
            token_data = response.json()
            print("\nOAuth token refreshed successfully!")
            print(f"Access Token: {token_data['access_token']}")
            print(f"Refresh Token: {token_data['refresh_token']}")
            print(f"Expires In: {token_data['expires_in']} seconds")
            
            # Update credentials file
            credentials["api_key"] = token_data['access_token']
            credentials["refresh_token"] = token_data['refresh_token']
            credentials["expires_in"] = token_data['expires_in']
            credentials["token_type"] = token_data['token_type']
            credentials["created_at"] = int(time.time())
            
            with open("credentials/calendly/calendly.json", "w") as f:
                json.dump(credentials, f, indent=4)
            
            print("\nToken saved to credentials/calendly/calendly.json")
            
            # Test the token
            print("\nTesting the token...")
            user_response = requests.get(
                "https://api.calendly.com/users/me",
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {token_data['access_token']}"
                }
            )
            
            if user_response.status_code == 200:
                user_info = user_response.json()
                print("Token is valid!")
                print(f"User: {user_info['resource']['name']} ({user_info['resource']['email']})")
                return True
            else:
                print(f"Error testing token: {user_response.status_code}")
                print(user_response.text)
                return False
        else:
            print(f"Error refreshing token: {response.status_code}")
            print(response.text)
            return False
    
    except Exception as e:
        print(f"Error: {e}")
        return False

def check_token_expiration():
    """Check if the token is expired or about to expire."""
    try:
        with open("credentials/calendly/calendly.json", "r") as f:
            credentials = json.load(f)
        
        # Check if we have created_at and expires_in
        if "created_at" not in credentials or "expires_in" not in credentials:
            print("Token expiration information not found in credentials file")
            return True  # Assume token is expired
        
        created_at = credentials["created_at"]
        expires_in = credentials["expires_in"]
        
        # Calculate expiration time
        expiration_time = datetime.fromtimestamp(created_at) + timedelta(seconds=expires_in)
        
        # Check if token is expired or about to expire (within 5 minutes)
        if datetime.now() >= expiration_time - timedelta(minutes=5):
            print("Token is expired or about to expire")
            return True
        else:
            print("Token is still valid")
            print(f"Expires at: {expiration_time}")
            print(f"Time remaining: {expiration_time - datetime.now()}")
            return False
    
    except Exception as e:
        print(f"Error checking token expiration: {e}")
        return True  # Assume token is expired

def main():
    """Refresh the Calendly OAuth token if needed."""
    if check_token_expiration():
        print("Refreshing token...")
        refresh_token()
    else:
        print("Token does not need to be refreshed")

if __name__ == "__main__":
    main()
