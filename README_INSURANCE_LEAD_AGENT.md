# Insurance Lead Agent: Omnichannel Lead Handler

The Insurance Lead Agent is a specialized AI agent designed to monitor and respond to insurance leads from multiple channels (Facebook, Instagram, TikTok, and website). It provides real-time responses, qualifies leads, guides them toward scheduling appointments, and tracks all interactions.

## Features

- **Omnichannel Monitoring**: Monitors leads from Facebook, Instagram, TikTok, and website forms
- **Real-time Response**: Responds to leads within 30 seconds when possible
- **Lead Qualification**: Identifies insurance type needs through conversation
- **Appointment Booking**: Provides personalized Calendly booking links based on insurance type
- **Comprehensive Logging**: Tracks all interactions with standardized logging format
- **Error Handling**: Implements retry logic and escalation paths for failures
- **Security**: Includes fraud detection and credential recovery mechanisms
- **Web Interface**: Provides a web-based dashboard for monitoring and testing

## Installation

### Prerequisites

- Python 3.8 or higher
- pip (Python package manager)

### Dependencies

Install the required dependencies:

```bash
pip install aiohttp aiohttp_jinja2 jinja2 matplotlib numpy twilio
```

## Configuration

The agent is configured through the `config/lead_agent_config.json` file, which includes:

- Channel-specific settings (Facebook, Instagram, TikTok, website)
- Response templates for different scenarios
- Booking links for different insurance types
- Escalation settings
- Security settings
- Logging format

### Credentials

The agent requires credentials for various services:

1. **Social Media Credentials**:
   - Facebook: `credentials/social_media/facebook.json`
   - Instagram: `credentials/social_media/instagram.json`
   - TikTok: `credentials/social_media/tiktok.json`

2. **Calendly Credentials**:
   - `credentials/calendly/calendly.json`

3. **Communication Credentials**:
   - Email: `credentials/communication/email.json`
   - Twilio (SMS): `credentials/communication/twilio.json`

## Usage

### Running the Agent

To run the Insurance Lead Agent:

```bash
python run_insurance_lead_agent.py
```

Optional arguments:
- `--config`: Path to lead agent configuration file (default: `config/lead_agent_config.json`)
- `--interval`: Interval between agent cycles in seconds (default: 60)

### Running the Agent with Web Interface

To run the Insurance Lead Agent with a web interface for monitoring:

```bash
python run_insurance_lead_agent_with_ui.py
```

Optional arguments:
- `--config`: Path to lead agent configuration file (default: `config/lead_agent_config.json`)
- `--interval`: Interval between agent cycles in seconds (default: 60)
- `--host`: Host to bind web server to (default: `localhost`)
- `--port`: Port to bind web server to (default: 8080)

This will start the agent and open a web browser with the monitoring dashboard.

## Testing

The agent includes comprehensive test scripts for each integration:

```bash
python tests/run_all_tests.py
```

Optional arguments:
- `--test`: Test to run (choices: calendly, facebook, instagram, tiktok, website, communication, monitoring, all; default: all)
- `--generate-data`: Generate test data for monitoring test

See `tests/README.md` for more details on testing.

## Directory Structure

```
.
├── agents/
│   └── insurance_lead_agent.py       # Main agent implementation
├── config/
│   └── lead_agent_config.json        # Agent configuration
├── credentials/                      # API credentials
│   ├── calendly/
│   ├── communication/
│   └── social_media/
├── reports/                          # Generated reports
├── services/
│   └── social_media_service.py       # Social media integration
├── templates/                        # Web interface templates
├── tests/                            # Test scripts
│   ├── test_calendly_integration.py
│   ├── test_facebook_integration.py
│   ├── test_instagram_integration.py
│   ├── test_tiktok_integration.py
│   ├── test_website_lead_handling.py
│   ├── test_communication.py
│   ├── test_monitoring.py
│   └── run_all_tests.py
├── run_insurance_lead_agent.py       # Script to run the agent
└── run_insurance_lead_agent_with_ui.py  # Script to run the agent with web interface
```

## Web Interface

The web interface provides:

1. **Channel Status**: Shows the status of each channel (Facebook, Instagram, TikTok, website)
2. **Recent Leads**: Displays the most recent leads
3. **Recent Interactions**: Shows the most recent interactions
4. **Lead Simulation**: Allows you to simulate leads from different channels

## Response Format

The agent follows a structured response format:

1. **Greeting**: "Hi [First Name], thanks for reaching out!"
2. **Qualification**: "Can I ask what kind of insurance you're looking for?"
3. **Info Response**: Brief answer to the question
4. **Booking CTA**: "Let's get you scheduled – here's a link: [insert link]"
5. **Error Handling**: "Having trouble at the moment – I've flagged this and someone will follow up!"

## Logging Format

All interactions are logged in the following format:

```
[Date | Time] | [Channel] | [UserHandle/ID] | [Summary] | [Status: success/failure/escalated]
```

## Monitoring and Analytics

The agent includes monitoring and analytics capabilities:

1. **Response Time Analysis**: Tracks response times and compares them to the target time
2. **Channel Performance**: Analyzes lead qualification and booking rates by channel
3. **Insurance Type Distribution**: Shows the distribution of insurance types

## Error Handling

The agent implements robust error handling:

1. **Retry Logic**: Attempts to retry failed responses once
2. **Escalation**: Escalates to human operators after failed retries
3. **Credential Recovery**: Handles authentication failures gracefully
4. **Fraud Detection**: Flags potential fraud or toxic messages

## Security

The agent includes security features:

1. **Fraud Detection**: Identifies potential fraud based on keywords
2. **Toxic Content Detection**: Flags potentially inappropriate messages
3. **Credential Management**: Securely handles API credentials
4. **Privacy Compliance**: Ensures GDPR/CCPA compliance in data handling

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
