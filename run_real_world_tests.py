"""
<PERSON><PERSON>t to run real-world cybersecurity tests with actual credentials.

This script uses stored credentials to perform real-world security tests
on systems that you have authorization to test.
"""
import sys
import os
import argparse
import getpass
import asyncio
from pathlib import Path
import json
import logging

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent))

from config.secure_credentials import CredentialManager
from core.state_manager import StateManager
from core.logger import setup_logger
from agents.cybersecurity_agent import CybersecurityAgent
from services.tool_service import ToolService
from services.vulnerability_database import VulnerabilityDatabase

# Set up logger
logger = setup_logger("run_real_world_tests")

async def run_test(test_type, target, options, master_password):
    """
    Run a real-world test.
    
    Args:
        test_type (str): Type of test to run
        target (str): Target to test
        options (dict): Test options
        master_password (str): Master password for credential decryption
    """
    logger.info(f"Running {test_type} test on {target}")
    
    # Initialize state manager
    state_manager = StateManager()
    await state_manager.initialize()
    
    # Create shutdown event
    shutdown_event = asyncio.Event()
    
    # Create message queue
    message_queue = asyncio.Queue()
    
    try:
        # Create agent instance
        agent = CybersecurityAgent(
            agent_id="cybersecurity_agent",
            config={
                "name": "Cybersecurity Assistant",
                "description": "Performs security testing and vulnerability analysis",
                "llm_provider": "anthropic",
                "enabled": True,
                "polling_interval": 300,
            },
            state_manager=state_manager,
            message_queue=message_queue,
            shutdown_event=shutdown_event
        )
        
        # Initialize agent
        logger.info("Initializing Cybersecurity Agent")
        await agent.initialize()
        
        # Load credentials
        credential_manager = CredentialManager(master_password)
        
        # Check if credentials exist for the target
        credential = credential_manager.get_credential(target)
        if not credential:
            logger.error(f"No credentials found for {target}")
            return
        
        # Create task
        task_id = f"{test_type}_{target.replace('.', '_')}"
        task = {
            "type": test_type,
            "target": target,
            "status": "pending",
            "credentials": {
                "username": credential["username"],
                "password": credential["password"],
            },
            "options": options,
        }
        
        # Add additional info from credentials if available
        if credential.get("additional_info"):
            task["additional_info"] = credential["additional_info"]
        
        # Save task
        await state_manager.update_state("cybersecurity", "pending_tasks", {task_id: task})
        
        # Process task
        logger.info(f"Processing task: {task_id}")
        await agent._process_task(task_id, task)
        
        # Get task result
        updated_task = await state_manager.get_state("cybersecurity", "pending_tasks", task_id)
        
        if updated_task and updated_task.get("status") == "completed":
            logger.info(f"Test completed successfully")
            
            # Save result to file
            result_file = Path(f"results/{test_type}_{target.replace('.', '_')}.json")
            result_file.parent.mkdir(exist_ok=True)
            
            # Remove sensitive information
            result = updated_task.get("result", {})
            if "credentials" in result:
                del result["credentials"]
            
            with open(result_file, "w") as f:
                json.dump(result, f, indent=2)
            
            logger.info(f"Results saved to {result_file}")
            
            # Print summary
            print("\nTest Summary:")
            print(f"Test type: {test_type}")
            print(f"Target: {target}")
            print(f"Status: {updated_task.get('status')}")
            print(f"Summary: {result.get('summary', 'No summary available')}")
            print(f"Detailed results saved to: {result_file}")
        else:
            logger.error(f"Test failed")
            if updated_task and updated_task.get("status") == "error":
                logger.error(f"Error: {updated_task.get('error', 'Unknown error')}")
    
    except Exception as e:
        logger.exception(f"Error running test: {e}")
    
    finally:
        # Shut down agent
        logger.info("Shutting down Cybersecurity Agent")
        await agent.shutdown()
        
        # Close state manager
        await state_manager.close()

def list_available_tests():
    """List available test types."""
    print("\nAvailable Test Types:")
    print("1. network_scan - Basic network scanning")
    print("2. vulnerability_scan - Vulnerability scanning")
    print("3. web_security_scan - Web application security scanning")
    print("4. password_audit - Password strength testing")
    print("5. sql_injection_test - SQL injection testing")
    print("6. security_assessment - Comprehensive security assessment (multi-agent)")
    print("7. vulnerability_assessment - Vulnerability assessment (multi-agent)")

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Run real-world cybersecurity tests")
    parser.add_argument("--test-type", help="Type of test to run")
    parser.add_argument("--target", help="Target to test")
    parser.add_argument("--options", help="Test options (JSON string)")
    parser.add_argument("--master-password", help="Master password for credential decryption")
    parser.add_argument("--list-tests", action="store_true", help="List available test types")
    args = parser.parse_args()
    
    if args.list_tests:
        list_available_tests()
        return
    
    # Interactive mode if arguments are missing
    test_type = args.test_type
    target = args.target
    options_str = args.options
    master_password = args.master_password
    
    if not test_type:
        list_available_tests()
        test_type = input("\nEnter test type: ")
    
    if not target:
        target = input("Enter target (e.g., example.com): ")
    
    if not options_str:
        options_str = input("Enter options (JSON string, or press Enter for default): ")
    
    if not options_str:
        options = {}
    else:
        try:
            options = json.loads(options_str)
        except json.JSONDecodeError:
            print("Invalid JSON. Using empty options.")
            options = {}
    
    if not master_password:
        master_password = getpass.getpass("Enter master password for credential decryption: ")
    
    # Run test
    asyncio.run(run_test(test_type, target, options, master_password))

if __name__ == "__main__":
    main()
