"""
Fix UI-TARS Browser Integration

This script fixes UI-TARS browser integration issues by:
1. Starting a browser with remote debugging enabled
2. Creating a proper UI-TARS configuration
3. Ensuring the browser is properly detected
"""
import os
import sys
import json
import time
import socket
import logging
import argparse
import platform
import subprocess
import requests
import shutil
import tempfile
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("fix_ui_tars_browser_integration.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("fix_ui_tars_browser_integration")

def find_ui_tars_executable():
    """Find the UI-TARS executable."""
    logger.info("Searching for UI-TARS executable...")
    
    os_type = platform.system()
    
    if os_type == "Windows":
        # Common installation locations on Windows
        possible_paths = [
            os.path.join(os.environ.get("PROGRAMFILES", "C:\\Program Files"), "UI-TARS", "UI-TARS.exe"),
            os.path.join(os.environ.get("PROGRAMFILES(X86)", "C:\\Program Files (x86)"), "UI-TARS", "UI-TARS.exe"),
            os.path.join(os.environ.get("LOCALAPPDATA", "C:\\Users\\<USER>\\AppData\\Local".format(os.getlogin())), "UI-TARS", "UI-TARS.exe"),
            "UI-TARS.exe"
        ]
    elif os_type == "Darwin":  # macOS
        # Common installation locations on macOS
        possible_paths = [
            "/Applications/UI-TARS.app/Contents/MacOS/UI-TARS",
            os.path.expanduser("~/Applications/UI-TARS.app/Contents/MacOS/UI-TARS"),
        ]
    else:  # Linux
        # Common installation locations on Linux
        possible_paths = [
            "/usr/local/bin/ui-tars",
            "/usr/bin/ui-tars",
            os.path.expanduser("~/.local/bin/ui-tars"),
        ]
        
    # Check if any of the paths exist
    for path in possible_paths:
        if os.path.exists(path):
            logger.info(f"Found UI-TARS executable at: {path}")
            return path
            
    # Try to find in PATH
    try:
        if os_type == "Windows":
            result = subprocess.run(["where", "UI-TARS.exe"], capture_output=True, text=True)
        else:
            result = subprocess.run(["which", "ui-tars"], capture_output=True, text=True)
            
        if result.returncode == 0:
            path = result.stdout.strip()
            logger.info(f"Found UI-TARS executable in PATH: {path}")
            return path
    except Exception as e:
        logger.debug(f"Error searching for UI-TARS in PATH: {e}")
        
    logger.warning("Could not find UI-TARS executable")
    return None

def detect_browsers():
    """Detect installed browsers."""
    logger.info("Detecting installed browsers...")
    
    browsers = {}
    os_type = platform.system()
    
    if os_type == "Windows":
        # Check for common browsers on Windows
        paths = [
            (r"C:\Program Files\Google\Chrome\Application\chrome.exe", "chrome"),
            (r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe", "chrome"),
            (r"C:\Program Files\Mozilla Firefox\firefox.exe", "firefox"),
            (r"C:\Program Files (x86)\Mozilla Firefox\firefox.exe", "firefox"),
            (r"C:\Program Files\Microsoft\Edge\Application\msedge.exe", "edge"),
            (r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe", "edge"),
            (r"C:\Program Files\BraveSoftware\Brave-Browser\Application\brave.exe", "brave"),
            (r"C:\Program Files (x86)\BraveSoftware\Brave-Browser\Application\brave.exe", "brave"),
        ]
    elif os_type == "Darwin":  # macOS
        # Check for common browsers on macOS
        paths = [
            ("/Applications/Google Chrome.app/Contents/MacOS/Google Chrome", "chrome"),
            ("/Applications/Firefox.app/Contents/MacOS/firefox", "firefox"),
            ("/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge", "edge"),
            ("/Applications/Brave Browser.app/Contents/MacOS/Brave Browser", "brave"),
        ]
    else:  # Linux
        # Check for common browsers on Linux
        paths = [
            ("/usr/bin/google-chrome", "chrome"),
            ("/usr/bin/firefox", "firefox"),
            ("/usr/bin/microsoft-edge", "edge"),
            ("/usr/bin/brave-browser", "brave"),
        ]
        
    # Check if any of the paths exist
    for path, browser_type in paths:
        if os.path.exists(path):
            browsers[browser_type] = path
            logger.info(f"Found {browser_type} browser at: {path}")
            
    return browsers

def check_port_open(host, port, timeout=5):
    """Check if a port is open."""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(timeout)
            result = s.connect_ex((host, port))
            return result == 0
    except Exception as e:
        logger.debug(f"Error checking port {port}: {e}")
        return False

def kill_processes(process_names):
    """Kill processes by name."""
    logger.info(f"Killing processes: {process_names}")
    
    for process_name in process_names:
        try:
            if platform.system() == "Windows":
                subprocess.run(["taskkill", "/F", "/IM", process_name], 
                              stdout=subprocess.PIPE, 
                              stderr=subprocess.PIPE)
            else:
                subprocess.run(["pkill", "-f", process_name], 
                              stdout=subprocess.PIPE, 
                              stderr=subprocess.PIPE)
                
            logger.info(f"Killed process: {process_name}")
        except Exception as e:
            logger.debug(f"Error killing process {process_name}: {e}")

def start_browser_with_remote_debugging(browser_path, browser_type, user_data_dir, port=9222):
    """Start a browser with remote debugging enabled."""
    logger.info(f"Starting {browser_type} with remote debugging on port {port}...")
    
    # Create user data directory if it doesn't exist
    os.makedirs(user_data_dir, exist_ok=True)
    
    # Prepare command
    command = [browser_path]
    
    if browser_type in ["chrome", "edge", "brave"]:
        command.extend([
            f"--remote-debugging-port={port}",
            f"--user-data-dir={user_data_dir}",
            "--no-first-run",
            "--no-default-browser-check",
            "--disable-extensions",
            "--disable-component-extensions-with-background-pages",
            "--disable-background-networking",
            "--disable-client-side-phishing-detection",
            "--disable-sync",
            "--metrics-recording-only",
            "--disable-default-apps",
            "--no-default-browser-check",
            "--no-first-run",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding",
            "--disable-background-timer-throttling",
            "about:blank"
        ])
    elif browser_type == "firefox":
        command.extend([
            "--remote-debugging-port", str(port),
            "--profile", user_data_dir,
            "--no-remote",
            "about:blank"
        ])
    
    # Start browser process
    try:
        # For Windows, use subprocess.Popen with CREATE_NO_WINDOW flag
        if platform.system() == "Windows":
            CREATE_NO_WINDOW = 0x08000000
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                creationflags=CREATE_NO_WINDOW
            )
        else:
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
        
        # Wait for the browser to start
        time.sleep(5)
        
        # Check if process is still running
        if process.poll() is not None:
            logger.error(f"Browser process exited with code: {process.returncode}")
            return None
            
        # Check if debugging port is open
        for _ in range(5):  # Try 5 times
            if check_port_open("localhost", port):
                logger.info(f"Browser started with remote debugging on port {port}")
                return process
                
            time.sleep(1)
            
        logger.warning(f"Browser started but debugging port {port} is not open")
        return process
        
    except Exception as e:
        logger.exception(f"Error starting browser: {e}")
        return None

def create_ui_tars_config(config_path, browser_type, browser_path, user_data_dir):
    """Create UI-TARS configuration."""
    logger.info(f"Creating UI-TARS configuration at {config_path}...")
    
    # Create configuration
    config = {
        "ui_tars": {
            "version": "1.5",
            "enabled": True,
            "browser": {
                "type": browser_type,
                "executable_path": browser_path,
                "user_data_dir": user_data_dir,
                "profile_directory": "Default",
                "remote_debugging_port": 9222,
                "detection": {
                    "auto_detect": True,
                    "fallback_types": ["chrome", "edge", "firefox", "brave"]
                }
            },
            "api": {
                "host": "localhost",
                "port": 8080,
                "timeout": 30,
                "retry_attempts": 3
            },
            "debug": {
                "enabled": True,
                "log_level": "debug",
                "log_file": "ui_tars_debug.log"
            }
        }
    }
    
    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(os.path.abspath(config_path)), exist_ok=True)
    
    # Save configuration
    try:
        with open(config_path, "w") as f:
            json.dump(config, f, indent=2)
            
        logger.info(f"Created UI-TARS configuration at {config_path}")
        return True
    except Exception as e:
        logger.exception(f"Error creating UI-TARS configuration: {e}")
        return False

def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Fix UI-TARS Browser Integration")
    parser.add_argument("--path", type=str, help="Path to UI-TARS executable")
    parser.add_argument("--config", type=str, help="Path to UI-TARS configuration file")
    parser.add_argument("--browser", type=str, choices=["chrome", "edge", "firefox", "brave"], help="Type of browser to use")
    parser.add_argument("--port", type=int, default=9222, help="Remote debugging port")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    
    args = parser.parse_args()
    
    # Set log level
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        
    print("Fix UI-TARS Browser Integration")
    print("==============================")
    print()
    
    # Find UI-TARS executable
    ui_tars_path = args.path or find_ui_tars_executable()
    if not ui_tars_path:
        print("❌ UI-TARS executable not found")
        print("Please install UI-TARS or provide the correct path with --path")
        return 1
        
    print(f"✅ UI-TARS executable found at: {ui_tars_path}")
    
    # Detect browsers
    browsers = detect_browsers()
    if not browsers:
        print("❌ No browsers detected")
        print("Please install a supported browser (Chrome, Edge, Firefox, or Brave)")
        return 1
        
    print(f"✅ Found {len(browsers)} browsers:")
    for browser_type, path in browsers.items():
        print(f"  - {browser_type}: {path}")
        
    # Select browser
    selected_browser = None
    selected_path = None
    
    if args.browser and args.browser in browsers:
        selected_browser = args.browser
        selected_path = browsers[args.browser]
    elif "chrome" in browsers:
        selected_browser = "chrome"
        selected_path = browsers["chrome"]
    elif browsers:
        # Use first available browser
        selected_browser = next(iter(browsers.keys()))
        selected_path = browsers[selected_browser]
        
    if not selected_browser:
        print("❌ No suitable browser selected")
        return 1
        
    print(f"✅ Selected browser: {selected_browser} at {selected_path}")
    
    # Kill existing browser and UI-TARS processes
    print("Killing existing processes...")
    kill_processes(["UI-TARS.exe", "chrome.exe", "msedge.exe", "firefox.exe", "brave.exe"])
    time.sleep(2)
    
    # Create user data directory
    user_data_dir = os.path.join(os.environ.get("LOCALAPPDATA", ""), "UI-TARS", "browser_data")
    print(f"Creating user data directory at: {user_data_dir}")
    os.makedirs(user_data_dir, exist_ok=True)
    
    # Start browser with remote debugging
    print(f"Starting {selected_browser} with remote debugging...")
    browser_process = start_browser_with_remote_debugging(
        selected_path, 
        selected_browser, 
        user_data_dir, 
        args.port
    )
    
    if not browser_process:
        print(f"❌ Failed to start {selected_browser} with remote debugging")
        return 1
        
    print(f"✅ Started {selected_browser} with remote debugging on port {args.port}")
    
    # Create UI-TARS configuration
    config_path = args.config or "config/ui_tars_config.json"
    success = create_ui_tars_config(config_path, selected_browser, selected_path, user_data_dir)
    
    if not success:
        print("❌ Failed to create UI-TARS configuration")
        return 1
        
    print(f"✅ Created UI-TARS configuration at: {config_path}")
    
    # Print instructions
    print()
    print("Browser integration has been fixed. Now you can start UI-TARS with:")
    print(f"  \"{ui_tars_path}\" --config \"{os.path.abspath(config_path)}\"")
    print()
    print("Or run the UI-TARS application normally, and it should detect the browser.")
    print()
    print("Important: Keep the browser window open while using UI-TARS.")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
