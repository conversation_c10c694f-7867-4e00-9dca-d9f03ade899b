"""
Test Gmail authentication for all configured accounts.
This script helps you test Gmail authentication for all configured accounts.
"""
import os
import sys
import pickle
import webbrowser
from pathlib import Path

try:
    from google.auth.transport.requests import Request
    from google.oauth2.credentials import Credentials
    from google_auth_oauthlib.flow import InstalledAppFlow
    from googleapiclient.discovery import build
    from googleapiclient.errors import HttpError
except ImportError:
    print("Required packages not found. Installing...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", 
                          "google-auth", "google-auth-oauthlib", 
                          "google-auth-httplib2", "google-api-python-client"])
    
    from google.auth.transport.requests import Request
    from google.oauth2.credentials import Credentials
    from google_auth_oauthlib.flow import InstalledAppFlow
    from googleapiclient.discovery import build
    from googleapiclient.errors import HttpError

def get_configured_accounts():
    """
    Get a list of configured Gmail accounts.
    
    Returns:
        list: List of configured Gmail accounts
    """
    accounts = []
    credentials_dir = 'credentials'
    
    if not os.path.exists(credentials_dir):
        return accounts
    
    for filename in os.listdir(credentials_dir):
        if filename.startswith('gmail_') and filename.endswith('_credentials.json'):
            # Extract email from filename
            email_part = filename[6:-16]  # Remove 'gmail_' prefix and '_credentials.json' suffix
            email = email_part.replace('_at_', '@').replace('_dot_', '.')
            accounts.append({
                'email': email,
                'credentials_path': os.path.join(credentials_dir, filename),
                'token_path': os.path.join(credentials_dir, filename.replace('_credentials.json', '_token.pickle'))
            })
    
    return accounts

def test_gmail_auth(account):
    """
    Test Gmail authentication for a specific account.
    
    Args:
        account (dict): Account information
        
    Returns:
        bool: True if authentication was successful, False otherwise
    """
    email = account['email']
    credentials_path = account['credentials_path']
    token_path = account['token_path']
    
    print(f"\n=== Testing Gmail Authentication for {email} ===")
    
    # Gmail API scopes
    SCOPES = [
        'https://www.googleapis.com/auth/gmail.readonly',
        'https://www.googleapis.com/auth/gmail.send',
        'https://www.googleapis.com/auth/gmail.compose',
        'https://www.googleapis.com/auth/gmail.modify'
    ]
    
    # Check if credentials file exists
    if not os.path.exists(credentials_path):
        print(f"Error: Credentials file not found at {credentials_path}")
        return False
    
    # Remove token file if it exists (to force re-authentication)
    if os.path.exists(token_path):
        os.remove(token_path)
        print(f"Removed existing token file: {token_path}")
    
    try:
        creds = None
        
        # Get new credentials
        flow = InstalledAppFlow.from_client_secrets_file(credentials_path, SCOPES)
        creds = flow.run_local_server(port=0)
        
        # Save the credentials for the next run
        with open(token_path, 'wb') as token:
            pickle.dump(creds, token)
        
        # Build the service
        service = build('gmail', 'v1', credentials=creds)
        
        # Get user profile
        profile = service.users().getProfile(userId='me').execute()
        user_email = profile.get('emailAddress')
        
        print(f"Successfully authenticated as {user_email}")
        
        # List a few messages to test the connection
        results = service.users().messages().list(userId='me', maxResults=5).execute()
        messages = results.get('messages', [])
        
        if not messages:
            print("No messages found.")
        else:
            print(f"Found {len(messages)} messages.")
            
            # Get the first message details
            msg = service.users().messages().get(userId='me', id=messages[0]['id']).execute()
            headers = msg['payload']['headers']
            subject = next((header['value'] for header in headers if header['name'] == 'Subject'), 'No subject')
            sender = next((header['value'] for header in headers if header['name'] == 'From'), 'Unknown sender')
            
            print(f"Latest message: '{subject}' from {sender}")
        
        return True
    
    except Exception as e:
        print(f"Error testing Gmail authentication: {e}")
        
        # Provide troubleshooting guidance
        print("\nTroubleshooting steps:")
        print("1. Make sure your OAuth consent screen is properly configured")
        print("2. Add all required scopes to your OAuth consent screen")
        print(f"3. Add {email} as a test user")
        print("4. Add http://localhost:0/ to the authorized redirect URIs")
        print("5. Make sure the Gmail API is enabled for your project")
        
        return False

def main():
    """Main entry point."""
    print("=== Test Gmail Authentication for All Accounts ===")
    
    # Get configured accounts
    accounts = get_configured_accounts()
    
    if not accounts:
        print("No Gmail accounts configured.")
        print("Please run setup_gmail_credentials.py to configure your accounts.")
        return
    
    print(f"Found {len(accounts)} configured Gmail accounts:")
    for i, account in enumerate(accounts):
        print(f"{i+1}. {account['email']}")
    
    # Ask which account to test
    print("\nOptions:")
    print("1. Test all accounts")
    print("2. Select an account to test")
    
    choice = input("\nEnter your choice (1 or 2): ")
    
    if choice == '1':
        # Test all accounts
        results = {}
        for account in accounts:
            results[account['email']] = test_gmail_auth(account)
        
        # Print summary
        print("\n=== Authentication Summary ===")
        for email, success in results.items():
            status = "SUCCESS" if success else "FAILED"
            print(f"{email}: {status}")
    
    elif choice == '2':
        # Select an account to test
        print("\nSelect an account to test:")
        for i, account in enumerate(accounts):
            print(f"{i+1}. {account['email']}")
        
        try:
            index = int(input("\nEnter the account number: ")) - 1
            if 0 <= index < len(accounts):
                test_gmail_auth(accounts[index])
            else:
                print("Invalid selection.")
        except ValueError:
            print("Invalid selection.")
    
    else:
        print("Invalid choice.")

if __name__ == "__main__":
    main()
