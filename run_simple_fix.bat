@echo off
echo Simple Fix for UI-TARS Browser Integration Issues
echo ===============================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Run the simple fix script
echo Running simple fix script...
echo.

python simple_fix_ui_tars_browser.py

echo.
if %errorlevel% equ 0 (
    echo UI-TARS browser integration issues fixed successfully!
) else (
    echo There was an error fixing UI-TARS browser integration issues.
)

echo.
pause
