"""
Fix OAuth consent screen issues.
This script helps you fix OAuth consent screen issues that cause "blocked" messages.
"""
import os
import sys
import webbrowser
import subprocess
from pathlib import Path

def clear_screen():
    """Clear the terminal screen."""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_header():
    """Print the script header."""
    clear_screen()
    print("=" * 80)
    print("                     FIX OAUTH CONSENT SCREEN")
    print("=" * 80)
    print("\nThis script will help you fix OAuth consent screen issues.")
    print("You'll be guided through each step with clear instructions.")
    print("\n")

def open_browser_with_confirmation(url, description):
    """
    Open a URL in the browser and confirm with the user.
    
    Args:
        url (str): URL to open
        description (str): Description of what the URL is for
    """
    print(f"\nOpening {description} in your browser...")
    print(f"URL: {url}")
    
    # Try to open the browser
    try:
        # First attempt with the default browser
        webbrowser.open(url)
        print("✓ Browser should be opening now.")
    except Exception as e:
        print(f"Error opening browser: {e}")
        print("\nPlease manually open this URL in your browser:")
        print(url)
    
    # Ask for confirmation
    confirmation = input("\nDid the browser open correctly? (y/n): ").lower()
    
    if confirmation != 'y':
        print("\nLet's try again with a different method.")
        
        try:
            # Try with a specific browser
            if os.name == 'nt':  # Windows
                os.startfile(url)
            else:
                # Try common browsers on other platforms
                browsers = ['google-chrome', 'chrome', 'firefox', 'safari']
                for browser in browsers:
                    try:
                        subprocess.Popen([browser, url])
                        break
                    except:
                        continue
            
            print("✓ Browser should be opening now with an alternative method.")
        except Exception as e:
            print(f"Error opening browser with alternative method: {e}")
            print("\nPlease manually open this URL in your browser:")
            print(url)
        
        # Final confirmation
        input("\nPress Enter when you have the page open in your browser...")
    
    return

def fix_oauth_consent_screen():
    """Fix OAuth consent screen issues."""
    print_header()
    
    print("STEP 1: VERIFY OAUTH CONSENT SCREEN SETTINGS")
    print("-" * 80)
    
    print("\nTo fix the 'blocked' message, you need to verify your OAuth consent screen settings:")
    print("1. Make sure your app information is complete and accurate")
    print("2. Add all required scopes")
    print("3. Add all your Gmail accounts as test users")
    print("4. Consider publishing your app to production (if you're the only user)")
    
    # Open OAuth consent screen
    open_browser_with_confirmation(
        "https://console.cloud.google.com/apis/credentials/consent",
        "OAuth consent screen"
    )
    
    print("\nVerify the following settings:")
    print("- App name: Should be descriptive and clear")
    print("- User support email: Should be a valid email address")
    print("- Developer contact information: Should be a valid email address")
    print("- Authorized domains: Should include domains you own (if any)")
    print("- App logo: Should be a valid image (optional)")
    
    input("\nPress Enter when you've verified these settings...")
    
    print("\nSTEP 2: ADD REQUIRED SCOPES")
    print("-" * 80)
    
    print("\nMake sure the following scopes are added to your OAuth consent screen:")
    print("- https://www.googleapis.com/auth/gmail.readonly")
    print("- https://www.googleapis.com/auth/gmail.send")
    print("- https://www.googleapis.com/auth/gmail.compose")
    print("- https://www.googleapis.com/auth/gmail.modify")
    
    input("\nPress Enter when you've added these scopes...")
    
    print("\nSTEP 3: ADD TEST USERS")
    print("-" * 80)
    
    print("\nIf your app is in 'Testing' mode, you need to add all your Gmail accounts as test users:")
    print("- <EMAIL>")
    print("- <EMAIL>")
    print("- <EMAIL>")
    print("- <EMAIL>")
    print("- <EMAIL>")
    print("- <EMAIL>")
    
    input("\nPress Enter when you've added these test users...")
    
    print("\nSTEP 4: CONSIDER PUBLISHING YOUR APP")
    print("-" * 80)
    
    print("\nIf you're the only user of your app, you can publish it to production to avoid the 'blocked' message.")
    print("This will require verification from Google, but you can start using the app immediately.")
    
    publish = input("\nDo you want to publish your app to production? (y/n): ").lower()
    
    if publish == 'y':
        print("\nTo publish your app to production:")
        print("1. Click on 'PUBLISH APP' button")
        print("2. Follow the instructions to complete the verification process")
        
        input("\nPress Enter when you've started the publishing process...")
    
    print("\nSTEP 5: UPDATE AUTHORIZED REDIRECT URIS")
    print("-" * 80)
    
    print("\nMake sure the following redirect URIs are added to your OAuth client:")
    print("- http://localhost:55253/")
    print("- http://localhost:0/")
    print("- http://localhost:8080/")
    print("- http://localhost:8090/")
    print("- http://localhost:8888/")
    
    # Open Credentials page
    open_browser_with_confirmation(
        "https://console.cloud.google.com/apis/credentials",
        "Credentials page"
    )
    
    input("\nPress Enter when you've updated the redirect URIs...")
    
    print("\nSTEP 6: ENABLE GMAIL API")
    print("-" * 80)
    
    print("\nMake sure the Gmail API is enabled for your project.")
    
    # Open API Library
    open_browser_with_confirmation(
        "https://console.cloud.google.com/apis/library/gmail.googleapis.com",
        "Gmail API page"
    )
    
    input("\nPress Enter when you've enabled the Gmail API...")
    
    print("\n✓ OAuth consent screen has been updated.")
    print("✓ You should now be able to authenticate with your Gmail accounts.")
    
    print("\nIf you still encounter issues, try the following:")
    print("1. Use a different Google account for authentication")
    print("2. Create a new OAuth client ID")
    print("3. Create a new Google Cloud project")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    try:
        fix_oauth_consent_screen()
    except KeyboardInterrupt:
        print("\n\nExiting...")
        sys.exit(0)
