"""
Agent <PERSON>ail Demo

This script demonstrates how to use the Gmail browser automation with the agent system.
It provides a simple example of sending an <NAME_EMAIL>
to <EMAIL>.
"""
import os
import sys
import getpass
from gmail_browser_automation import GmailBrowserAutomation
from credential_store import CredentialStore

class EmailAgent:
    """Simple agent for sending emails using Gmail browser automation."""

    def __init__(self, browser_type="chrome"):
        """
        Initialize the Email Agent.

        Args:
            browser_type (str): Type of browser to use (chrome, firefox, edge)
        """
        self.browser_type = browser_type
        self.gmail_automation = None
        self.credential_store = CredentialStore()

        # Store default credentials if not already stored
        if not self.credential_store.has_credential("gmail", "<EMAIL>", "password"):
            self.credential_store.store_credential("gmail", "<EMAIL>", "password", "GodisSoGood!777")
            print("Stored default <NAME_EMAIL>")

    def initialize(self):
        """
        Initialize the Email Agent.

        Returns:
            bool: True if initialization was successful, False otherwise
        """
        print("Initializing Email Agent...")

        # Create Gmail browser automation
        self.gmail_automation = GmailBrowserAutomation(browser_type=self.browser_type)

        # Initialize browser automation
        success = self.gmail_automation.initialize()
        if not success:
            print("Failed to initialize Gmail browser automation")
            return False

        print("Email Agent initialized successfully")
        return True

    def send_email(self, from_email, to_email, subject, body, password=None):
        """
        Send an email using browser automation.

        Args:
            from_email (str): Gmail account to send from
            to_email (str): Recipient email address
            subject (str): Email subject
            body (str): Email body
            password (str, optional): Password for the Gmail account. If None, will try to use stored credentials.

        Returns:
            dict: Result of the operation
        """
        print(f"Sending email from {from_email} to {to_email}...")

        if not self.gmail_automation:
            print("Gmail browser automation not initialized")
            return {"success": False, "error": "Gmail browser automation not initialized"}

        # Get password from credential store if not provided
        if password is None:
            stored_password = self.credential_store.get_credential("gmail", from_email, "password")
            if stored_password:
                password = stored_password
                print(f"Using stored credentials for {from_email}")
            else:
                # If no stored password, prompt for it
                password = getpass.getpass(f"Enter password for {from_email}: ")

                # Ask if the user wants to store the password
                store_password = input("Store this password for future use? (y/n): ").lower() == 'y'
                if store_password:
                    self.credential_store.store_credential("gmail", from_email, "password", password)
                    print(f"Stored credentials for {from_email}")

        result = self.gmail_automation.send_email(
            email_account=from_email,
            password=password,
            to_email=to_email,
            subject=subject,
            body=body
        )

        return result

    def shutdown(self):
        """Shut down the Email Agent."""
        if self.gmail_automation:
            self.gmail_automation.shutdown()
        print("Email Agent shut down")

def main():
    """Main entry point for the script."""
    # Email details
    from_email = "<EMAIL>"
    to_email = "<EMAIL>"
    subject = "Test Email from AI Agent System with Stored Credentials"
    body = "This is a test email sent using browser automation with stored credentials."

    # Create Email Agent
    agent = EmailAgent(browser_type="chrome")

    # Initialize
    initialized = agent.initialize()
    if not initialized:
        print("Failed to initialize Email Agent")
        return

    try:
        # Send email using stored credentials
        result = agent.send_email(
            from_email=from_email,
            to_email=to_email,
            subject=subject,
            body=body
            # No password provided, will use stored credentials
        )

        if result["success"]:
            print("Email sent successfully")
        else:
            print(f"Failed to send email: {result['error']}")

    finally:
        # Shut down
        agent.shutdown()

if __name__ == "__main__":
    main()
