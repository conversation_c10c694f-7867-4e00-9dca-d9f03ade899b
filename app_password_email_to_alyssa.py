"""
App Password Email to <PERSON><PERSON>

This script sends an email to <PERSON><PERSON> with information about insurance options
using the Gmail SMTP server with an app password.
"""
import os
import sys
import smtplib
import logging
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("app_password_email_to_alyssa.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("app_password_email_to_alyssa")

# Email configuration
EMAIL_ACCOUNT = "<EMAIL>"
APP_PASSWORD = "fnkesylgdubokwyr"  # App password from previous script
RECIPIENT_EMAIL = "<EMAIL>"  # Test recipient (change to <PERSON><PERSON>'s email when ready)
EMAIL_SUBJECT = "IUL Policy and Health Insurance Options - Flo Faction Insurance"

# Email body
EMAIL_BODY = """Hello <PERSON><PERSON>,

I hope this email finds you well. My name is <PERSON> from Flo Faction Insurance, and I'm reaching out regarding your interest in an IUL (Indexed Universal Life) policy structured for maximum cash value growth, plus basic health, dental and vision plans.

With your budget of $100/month, I've put together some options that I believe will work well for you:

1. IUL Policy ($60-70/month): This would be structured for optimal cash value growth while maintaining the life insurance benefit.

2. Basic Health Coverage ($20-25/month): This would cover your essential needs like checkups, physicals, and bloodwork.

3. Dental and Vision ($10-15/month): Combined plan for regular dental cleanings and eye exams.

I'd be happy to walk you through these options in more detail and answer any questions you might have. Please feel free to call or text me at ************, or you can reply to this email.

You can also schedule a time to talk using my calendar link: https://calendly.com/flofaction-insurance/30min

Looking forward to helping you secure the coverage you need!

Best regards,
Sandra
Flo Faction Insurance
************
<EMAIL>
"""

def send_email_with_app_password() -> Dict[str, Any]:
    """
    Send an email to Alyssa using Gmail SMTP server with an app password.
    
    Returns:
        Dict[str, Any]: Result of the operation
    """
    logger.info("Starting email sending with app password")
    
    try:
        # Create message
        message = MIMEMultipart()
        message["From"] = EMAIL_ACCOUNT
        message["To"] = RECIPIENT_EMAIL
        message["Subject"] = EMAIL_SUBJECT
        
        # Attach body
        message.attach(MIMEText(EMAIL_BODY, "plain"))
        
        # Connect to Gmail SMTP server
        logger.info("Connecting to Gmail SMTP server")
        server = smtplib.SMTP("smtp.gmail.com", 587)
        server.starttls()
        
        # Login with app password
        logger.info(f"Logging in as {EMAIL_ACCOUNT} with app password")
        server.login(EMAIL_ACCOUNT, APP_PASSWORD)
        
        # Send email
        logger.info(f"Sending email to {RECIPIENT_EMAIL}")
        server.send_message(message)
        
        # Close connection
        server.quit()
        
        logger.info("Email sent successfully")
        return {"success": True, "message": "Email sent successfully"}
        
    except Exception as e:
        logger.exception(f"Error sending email: {e}")
        return {"success": False, "error": str(e)}

def main():
    """Main function."""
    print("Starting App Password Email to Alyssa")
    print("===================================")
    print()
    
    result = send_email_with_app_password()
    
    if result.get("success"):
        print("✅ Email sent successfully!")
    else:
        print(f"❌ Failed to send email: {result.get('error')}")
    
    print()
    print("Process completed")

if __name__ == "__main__":
    main()
