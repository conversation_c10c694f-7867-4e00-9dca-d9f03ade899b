"""
Multi-Party Computation (MPC) Server for secure distributed computation.

This module implements a secure MPC server that allows multiple parties to
compute functions together without revealing their private inputs to each other.
"""
import asyncio
import json
import logging
import os
import secrets
import socket
import ssl
import time
from typing import Dict, List, Optional, Any, Callable, Union
import uuid

from core.logger import setup_logger

# Set up logger
logger = setup_logger("mpc_server")

class MPCServer:
    """
    Multi-Party Computation Server for secure distributed computation.
    
    This class implements a secure MPC server that allows multiple parties to
    compute functions together without revealing their private inputs to each other.
    """
    
    def __init__(
        self,
        server_id: str,
        host: str = "0.0.0.0",
        port: int = 8765,
        use_ssl: bool = True,
        cert_file: Optional[str] = None,
        key_file: Optional[str] = None,
    ):
        """
        Initialize the MPC server.
        
        Args:
            server_id (str): Unique identifier for this server
            host (str): Host to bind to
            port (int): Port to bind to
            use_ssl (bool): Whether to use SSL
            cert_file (Optional[str]): Path to SSL certificate file
            key_file (Optional[str]): Path to SSL key file
        """
        self.server_id = server_id
        self.host = host
        self.port = port
        self.use_ssl = use_ssl
        self.cert_file = cert_file
        self.key_file = key_file
        
        # Server state
        self.running = False
        self.clients = {}
        self.computations = {}
        self.server = None
        
        # Security
        self.session_keys = {}
        
        logger.info(f"MPC Server {server_id} initialized")
    
    async def start(self):
        """Start the MPC server."""
        if self.running:
            logger.warning("MPC Server already running")
            return
        
        try:
            # Create server
            server = await asyncio.start_server(
                self._handle_client,
                self.host,
                self.port,
                ssl=self._create_ssl_context() if self.use_ssl else None,
            )
            
            self.server = server
            self.running = True
            
            logger.info(f"MPC Server {self.server_id} started on {self.host}:{self.port}")
            
            # Serve forever
            async with server:
                await server.serve_forever()
                
        except Exception as e:
            logger.exception(f"Error starting MPC Server: {e}")
            self.running = False
    
    async def stop(self):
        """Stop the MPC server."""
        if not self.running:
            logger.warning("MPC Server not running")
            return
        
        try:
            # Close server
            if self.server:
                self.server.close()
                await self.server.wait_closed()
                self.server = None
            
            self.running = False
            logger.info(f"MPC Server {self.server_id} stopped")
            
        except Exception as e:
            logger.exception(f"Error stopping MPC Server: {e}")
    
    def _create_ssl_context(self) -> ssl.SSLContext:
        """
        Create SSL context for secure connections.
        
        Returns:
            ssl.SSLContext: SSL context
        """
        if not self.cert_file or not self.key_file:
            raise ValueError("SSL certificate and key files must be provided")
        
        ssl_context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
        ssl_context.load_cert_chain(self.cert_file, self.key_file)
        ssl_context.check_hostname = False
        
        return ssl_context
    
    async def _handle_client(self, reader, writer):
        """
        Handle a client connection.
        
        Args:
            reader: StreamReader for reading from client
            writer: StreamWriter for writing to client
        """
        client_id = str(uuid.uuid4())
        peer_name = writer.get_extra_info("peername")
        logger.info(f"New client connected: {client_id} from {peer_name}")
        
        # Add client to clients dict
        self.clients[client_id] = {
            "reader": reader,
            "writer": writer,
            "peer_name": peer_name,
            "connected_at": time.time(),
            "last_active": time.time(),
        }
        
        try:
            while True:
                # Read message from client
                data = await reader.read(4096)
                if not data:
                    break
                
                # Update last active time
                self.clients[client_id]["last_active"] = time.time()
                
                # Process message
                try:
                    message = json.loads(data.decode())
                    response = await self._process_message(client_id, message)
                    
                    # Send response
                    writer.write(json.dumps(response).encode())
                    await writer.drain()
                    
                except json.JSONDecodeError:
                    logger.warning(f"Invalid JSON from client {client_id}")
                    writer.write(json.dumps({"error": "Invalid JSON"}).encode())
                    await writer.drain()
                    
                except Exception as e:
                    logger.exception(f"Error processing message from client {client_id}: {e}")
                    writer.write(json.dumps({"error": str(e)}).encode())
                    await writer.drain()
                    
        except asyncio.CancelledError:
            pass
        except Exception as e:
            logger.exception(f"Error handling client {client_id}: {e}")
        finally:
            # Remove client from clients dict
            if client_id in self.clients:
                del self.clients[client_id]
            
            # Close connection
            writer.close()
            try:
                await writer.wait_closed()
            except:
                pass
            
            logger.info(f"Client disconnected: {client_id}")
    
    async def _process_message(self, client_id: str, message: Dict) -> Dict:
        """
        Process a message from a client.
        
        Args:
            client_id (str): Client identifier
            message (Dict): Message from client
            
        Returns:
            Dict: Response message
        """
        message_type = message.get("type")
        
        if message_type == "hello":
            return await self._handle_hello(client_id, message)
        elif message_type == "create_computation":
            return await self._handle_create_computation(client_id, message)
        elif message_type == "join_computation":
            return await self._handle_join_computation(client_id, message)
        elif message_type == "submit_input":
            return await self._handle_submit_input(client_id, message)
        elif message_type == "get_result":
            return await self._handle_get_result(client_id, message)
        else:
            return {"error": f"Unknown message type: {message_type}"}
