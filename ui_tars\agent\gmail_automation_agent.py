"""
Gmail Automation Agent using Enhanced UI-TARS.

This module provides a specialized agent for Gmail automation using the Enhanced UI-TARS agent.
"""
import os
import sys
import json
import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import uuid
import time
import tempfile
from pathlib import Path

try:
    from ui_tars.agent.enhanced_ui_tars_agent import EnhancedUITarsAgent
    from core.logger import setup_logger
except ImportError:
    # Fallback imports for standalone usage
    from enhanced_ui_tars_agent import EnhancedUITarsAgent

    # Fallback logging setup
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler("gmail_automation.log")
        ]
    )

    def setup_logger(name):
        return logging.getLogger(name)

# Set up logger
logger = setup_logger("gmail_automation_agent")

class GmailAutomationAgent(EnhancedUITarsAgent):
    """
    Gmail Automation Agent using Enhanced UI-TARS.

    This agent provides specialized methods for Gmail automation using UI-TARS.
    """

    def __init__(self,
                 agent_id: str = "gmail_automation_agent",
                 config: Optional[Dict] = None,
                 state_manager = None,
                 message_queue = None,
                 shutdown_event = None,
                 api_url: Optional[str] = "http://localhost:8080",
                 api_key: Optional[str] = None,
                 model_name: Optional[str] = "UI-TARS-1.5-7B",
                 installation_path: Optional[str] = None,
                 browser_type: str = "chrome",
                 browser_path: Optional[str] = None,
                 remote_debugging_port: int = 9222,
                 auto_start: bool = True,
                 auto_restart: bool = True,
                 llm_router = None,
                 gmail_url: str = "https://mail.google.com",
                 default_email: Optional[str] = None,
                 default_password: Optional[str] = None,
                 use_app_password: bool = False,
                 max_retries: int = 3,
                 retry_delay: int = 5):
        """
        Initialize the Gmail Automation Agent.

        Args:
            agent_id (str): Agent identifier
            config (Optional[Dict]): Agent configuration
            state_manager: State manager instance
            message_queue: Message queue for agent communication
            shutdown_event: Event to signal agent shutdown
            api_url (Optional[str]): URL of the UI-TARS API
            api_key (Optional[str]): API key for UI-TARS
            model_name (Optional[str]): Name of the model to use
            installation_path (Optional[str]): Path to UI-TARS installation
            browser_type (str): Type of browser to use
            browser_path (Optional[str]): Path to browser executable
            remote_debugging_port (int): Port for browser remote debugging
            auto_start (bool): Whether to automatically start UI-TARS
            auto_restart (bool): Whether to automatically restart UI-TARS on failure
            llm_router: LLM router instance
            gmail_url (str): URL of Gmail
            default_email (Optional[str]): Default Gmail email address
            default_password (Optional[str]): Default Gmail password
            use_app_password (bool): Whether to use app password
            max_retries (int): Maximum number of retries
            retry_delay (int): Delay between retries in seconds
        """
        # Initialize base agent
        super().__init__(
            agent_id=agent_id,
            config=config,
            state_manager=state_manager,
            message_queue=message_queue,
            shutdown_event=shutdown_event,
            api_url=api_url,
            api_key=api_key,
            model_name=model_name,
            installation_path=installation_path,
            browser_type=browser_type,
            browser_path=browser_path,
            remote_debugging_port=remote_debugging_port,
            auto_start=auto_start,
            auto_restart=auto_restart,
            llm_router=llm_router
        )

        # Gmail settings
        self.gmail_url = gmail_url
        self.default_email = default_email
        self.default_password = default_password
        self.use_app_password = use_app_password
        self.max_retries = max_retries
        self.retry_delay = retry_delay

        # Gmail state
        self.is_logged_in = False
        self.current_email = None
        self.login_time = None
        self.last_activity_time = None
        self.session_timeout = 3600  # 1 hour

        self.logger.info("Gmail Automation Agent initialized")

    async def initialize(self):
        """Initialize the Gmail Automation Agent."""
        self.logger.info("Initializing Gmail Automation Agent")

        # Initialize base agent
        success = await super().initialize()
        if not success:
            self.logger.error("Failed to initialize base agent")
            return False

        self.logger.info("Gmail Automation Agent initialized successfully")
        return True

    async def login_to_gmail(self, email: Optional[str] = None, password: Optional[str] = None) -> Dict:
        """
        Log in to Gmail.

        Args:
            email (Optional[str]): Gmail email address
            password (Optional[str]): Gmail password

        Returns:
            Dict: Result of the operation
        """
        email = email or self.default_email
        password = password or self.default_password

        if not email or not password:
            return {"success": False, "error": "Email and password are required"}

        self.logger.info(f"Logging in to Gmail with email: {email}")

        try:
            # Navigate to Gmail
            browse_result = await self.browse_website(self.gmail_url, "navigate to Gmail login page")

            # Wait for page to load
            await asyncio.sleep(3)

            # Take a screenshot
            screenshot_result = await self.take_screenshot()

            # Analyze the screen
            analysis_result = await self.analyze_screen()

            # Check if already logged in
            if "compose" in str(analysis_result).lower() or "inbox" in str(analysis_result).lower():
                self.logger.info("Already logged in to Gmail")
                self.is_logged_in = True
                self.current_email = email
                self.login_time = datetime.now()
                self.last_activity_time = datetime.now()

                return {
                    "success": True,
                    "message": "Already logged in to Gmail",
                    "email": email
                }

            # Enter email
            email_command = f"Enter the email address {email} in the email input field and click Next"
            email_result = await self.execute_command(email_command)

            # Wait for password page
            await asyncio.sleep(3)

            # Take a screenshot
            screenshot_result = await self.take_screenshot()

            # Enter password
            password_command = f"Enter the password in the password field and click Next"
            password_result = await self.execute_command(password_command)

            # Wait for login to complete
            await asyncio.sleep(5)

            # Take a screenshot
            screenshot_result = await self.take_screenshot()

            # Analyze the screen to check if login was successful
            analysis_result = await self.analyze_screen()

            # Check if login was successful
            if "compose" in str(analysis_result).lower() or "inbox" in str(analysis_result).lower():
                self.logger.info("Successfully logged in to Gmail")
                self.is_logged_in = True
                self.current_email = email
                self.login_time = datetime.now()
                self.last_activity_time = datetime.now()

                return {
                    "success": True,
                    "message": "Successfully logged in to Gmail",
                    "email": email
                }
            else:
                self.logger.error("Failed to log in to Gmail")
                return {
                    "success": False,
                    "error": "Failed to log in to Gmail",
                    "analysis": analysis_result
                }

        except Exception as e:
            error_msg = f"Error logging in to Gmail: {str(e)}"
            self.logger.exception(error_msg)
            return {"success": False, "error": error_msg}

    async def send_email(self,
                        to: str,
                        subject: str,
                        body: str,
                        cc: Optional[str] = None,
                        bcc: Optional[str] = None,
                        attachments: Optional[List[str]] = None) -> Dict:
        """
        Send an email using Gmail.

        Args:
            to (str): Recipient email address
            subject (str): Email subject
            body (str): Email body
            cc (Optional[str]): CC recipients
            bcc (Optional[str]): BCC recipients
            attachments (Optional[List[str]]): List of attachment file paths

        Returns:
            Dict: Result of the operation
        """
        if not self.is_logged_in:
            login_result = await self.login_to_gmail()
            if not login_result["success"]:
                return login_result

        self.logger.info(f"Sending email to: {to}")

        try:
            # Click Compose button
            compose_command = "Click the Compose button"
            compose_result = await self.execute_command(compose_command)

            # Wait for compose window to open
            await asyncio.sleep(3)

            # Enter recipient
            to_command = f"Enter the email address {to} in the To field"
            to_result = await self.execute_command(to_command)

            # Enter CC if provided
            if cc:
                cc_command = f"Enter the email address {cc} in the CC field"
                cc_result = await self.execute_command(cc_command)

            # Enter BCC if provided
            if bcc:
                bcc_command = f"Enter the email address {bcc} in the BCC field"
                bcc_result = await self.execute_command(bcc_command)

            # Enter subject
            subject_command = f"Enter the subject '{subject}' in the Subject field"
            subject_result = await self.execute_command(subject_command)

            # Enter body
            body_command = f"Enter the following text in the email body: {body}"
            body_result = await self.execute_command(body_command)

            # Add attachments if provided
            if attachments:
                for attachment in attachments:
                    attachment_command = f"Click the attachment button and upload the file {attachment}"
                    attachment_result = await self.execute_command(attachment_command)
                    await asyncio.sleep(3)  # Wait for upload

            # Send email
            send_command = "Click the Send button"
            send_result = await self.execute_command(send_command)

            # Wait for confirmation
            await asyncio.sleep(3)

            # Update last activity time
            self.last_activity_time = datetime.now()

            return {
                "success": True,
                "message": "Email sent successfully",
                "to": to,
                "subject": subject
            }

        except Exception as e:
            error_msg = f"Error sending email: {str(e)}"
            self.logger.exception(error_msg)
            return {"success": False, "error": error_msg}

    async def check_inbox(self, search_query: Optional[str] = None, max_results: int = 10) -> Dict:
        """
        Check Gmail inbox.

        Args:
            search_query (Optional[str]): Search query to filter emails
            max_results (int): Maximum number of results to return

        Returns:
            Dict: Result of the operation
        """
        if not self.is_logged_in:
            login_result = await self.login_to_gmail()
            if not login_result["success"]:
                return login_result

        self.logger.info(f"Checking inbox with query: {search_query}")

        try:
            # Navigate to inbox
            inbox_command = "Click on the Inbox link in the left sidebar"
            inbox_result = await self.execute_command(inbox_command)

            # Wait for inbox to load
            await asyncio.sleep(3)

            # Search if query provided
            if search_query:
                search_command = f"Enter the search query '{search_query}' in the search box and press Enter"
                search_result = await self.execute_command(search_command)

                # Wait for search results
                await asyncio.sleep(3)

            # Analyze the screen to get email list
            analysis_result = await self.analyze_screen()

            # Update last activity time
            self.last_activity_time = datetime.now()

            return {
                "success": True,
                "message": "Inbox checked successfully",
                "search_query": search_query,
                "analysis": analysis_result
            }

        except Exception as e:
            error_msg = f"Error checking inbox: {str(e)}"
            self.logger.exception(error_msg)
            return {"success": False, "error": error_msg}

    async def reply_to_email(self, email_subject: str, reply_body: str) -> Dict:
        """
        Reply to an email in Gmail.

        Args:
            email_subject (str): Subject of the email to reply to
            reply_body (str): Reply body

        Returns:
            Dict: Result of the operation
        """
        if not self.is_logged_in:
            login_result = await self.login_to_gmail()
            if not login_result["success"]:
                return login_result

        self.logger.info(f"Replying to email with subject: {email_subject}")

        try:
            # Search for the email
            search_result = await self.check_inbox(f"subject:{email_subject}")
            if not search_result["success"]:
                return search_result

            # Click on the email
            open_email_command = f"Click on the email with subject '{email_subject}'"
            open_email_result = await self.execute_command(open_email_command)

            # Wait for email to open
            await asyncio.sleep(3)

            # Click reply button
            reply_command = "Click the Reply button"
            reply_result = await self.execute_command(reply_command)

            # Wait for reply form to open
            await asyncio.sleep(2)

            # Enter reply body
            body_command = f"Enter the following text in the reply body: {reply_body}"
            body_result = await self.execute_command(body_command)

            # Send reply
            send_command = "Click the Send button"
            send_result = await self.execute_command(send_command)

            # Wait for confirmation
            await asyncio.sleep(3)

            # Update last activity time
            self.last_activity_time = datetime.now()

            return {
                "success": True,
                "message": "Reply sent successfully",
                "email_subject": email_subject
            }

        except Exception as e:
            error_msg = f"Error replying to email: {str(e)}"
            self.logger.exception(error_msg)
            return {"success": False, "error": error_msg}

    async def forward_email(self, email_subject: str, forward_to: str, forward_note: Optional[str] = None) -> Dict:
        """
        Forward an email in Gmail.

        Args:
            email_subject (str): Subject of the email to forward
            forward_to (str): Email address to forward to
            forward_note (Optional[str]): Note to add to the forwarded email

        Returns:
            Dict: Result of the operation
        """
        if not self.is_logged_in:
            login_result = await self.login_to_gmail()
            if not login_result["success"]:
                return login_result

        self.logger.info(f"Forwarding email with subject: {email_subject} to: {forward_to}")

        try:
            # Search for the email
            search_result = await self.check_inbox(f"subject:{email_subject}")
            if not search_result["success"]:
                return search_result

            # Click on the email
            open_email_command = f"Click on the email with subject '{email_subject}'"
            open_email_result = await self.execute_command(open_email_command)

            # Wait for email to open
            await asyncio.sleep(3)

            # Click forward button
            forward_command = "Click the Forward button"
            forward_result = await self.execute_command(forward_command)

            # Wait for forward form to open
            await asyncio.sleep(2)

            # Enter recipient
            to_command = f"Enter the email address {forward_to} in the To field"
            to_result = await self.execute_command(to_command)

            # Enter forward note if provided
            if forward_note:
                note_command = f"Enter the following text at the beginning of the email body: {forward_note}"
                note_result = await self.execute_command(note_command)

            # Send forward
            send_command = "Click the Send button"
            send_result = await self.execute_command(send_command)

            # Wait for confirmation
            await asyncio.sleep(3)

            # Update last activity time
            self.last_activity_time = datetime.now()

            return {
                "success": True,
                "message": "Email forwarded successfully",
                "email_subject": email_subject,
                "forward_to": forward_to
            }

        except Exception as e:
            error_msg = f"Error forwarding email: {str(e)}"
            self.logger.exception(error_msg)
            return {"success": False, "error": error_msg}

    async def logout_from_gmail(self) -> Dict:
        """
        Log out from Gmail.

        Returns:
            Dict: Result of the operation
        """
        if not self.is_logged_in:
            return {
                "success": True,
                "message": "Already logged out from Gmail"
            }

        self.logger.info("Logging out from Gmail")

        try:
            # Click on profile picture
            profile_command = "Click on the profile picture or icon in the top-right corner"
            profile_result = await self.execute_command(profile_command)

            # Wait for menu to open
            await asyncio.sleep(2)

            # Click sign out
            signout_command = "Click on the Sign out button"
            signout_result = await self.execute_command(signout_command)

            # Wait for logout to complete
            await asyncio.sleep(3)

            # Reset login state
            self.is_logged_in = False
            self.current_email = None
            self.login_time = None
            self.last_activity_time = None

            return {
                "success": True,
                "message": "Successfully logged out from Gmail"
            }

        except Exception as e:
            error_msg = f"Error logging out from Gmail: {str(e)}"
            self.logger.exception(error_msg)
            return {"success": False, "error": error_msg}

    async def check_session(self) -> bool:
        """
        Check if the Gmail session is still valid.

        Returns:
            bool: True if session is valid, False otherwise
        """
        if not self.is_logged_in or not self.last_activity_time:
            return False

        # Check if session has timed out
        elapsed_seconds = (datetime.now() - self.last_activity_time).total_seconds()
        if elapsed_seconds > self.session_timeout:
            self.logger.info(f"Gmail session timed out after {elapsed_seconds} seconds")
            self.is_logged_in = False
            return False

        return True
