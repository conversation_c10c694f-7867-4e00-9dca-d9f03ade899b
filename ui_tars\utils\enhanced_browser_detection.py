"""
Enhanced Browser Detection for UI-TARS.

This module provides enhanced browser detection capabilities for UI-TARS 1.5,
allowing it to reliably detect and use browsers across different platforms.
"""
import os
import sys
import json
import asyncio
import logging
import subprocess
import platform
import socket
import time
import re
import winreg
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Union

from core.logger import setup_logger

# Set up logger
logger = setup_logger("ui_tars_enhanced_browser_detection")

class BrowserInfo:
    """Information about a browser installation."""
    
    def __init__(self, 
                 browser_type: str,
                 executable_path: str,
                 version: Optional[str] = None,
                 user_data_dir: Optional[str] = None,
                 default_profile: Optional[str] = None,
                 is_default: bool = False):
        """
        Initialize browser information.
        
        Args:
            browser_type (str): Type of browser
            executable_path (str): Path to browser executable
            version (Optional[str]): Browser version
            user_data_dir (Optional[str]): Path to user data directory
            default_profile (Optional[str]): Default profile name
            is_default (bool): Whether this is the default browser
        """
        self.browser_type = browser_type
        self.executable_path = executable_path
        self.version = version
        self.user_data_dir = user_data_dir
        self.default_profile = default_profile
        self.is_default = is_default
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "browser_type": self.browser_type,
            "executable_path": self.executable_path,
            "version": self.version,
            "user_data_dir": self.user_data_dir,
            "default_profile": self.default_profile,
            "is_default": self.is_default
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BrowserInfo':
        """Create from dictionary."""
        return cls(
            browser_type=data.get("browser_type", ""),
            executable_path=data.get("executable_path", ""),
            version=data.get("version"),
            user_data_dir=data.get("user_data_dir"),
            default_profile=data.get("default_profile"),
            is_default=data.get("is_default", False)
        )
        
    def __str__(self) -> str:
        """String representation."""
        return f"{self.browser_type} ({self.version or 'unknown'}) at {self.executable_path}"

class EnhancedBrowserDetector:
    """
    Enhanced browser detection for UI-TARS 1.5.
    
    This class provides enhanced browser detection capabilities for UI-TARS 1.5,
    allowing it to reliably detect and use browsers across different platforms.
    """
    
    def __init__(self, cache_file: Optional[str] = None):
        """
        Initialize the browser detector.
        
        Args:
            cache_file (Optional[str]): Path to cache file
        """
        self.os_type = platform.system()
        self.browsers = {}
        self.default_browser = None
        self.cache_file = cache_file
        
        # Create default cache file if not provided
        if not self.cache_file:
            self.cache_file = os.path.join(
                os.path.expanduser("~"),
                ".ui_tars",
                "browser_cache.json"
            )
            
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(self.cache_file), exist_ok=True)
            
    def detect_browsers(self, force_refresh: bool = False) -> Dict[str, BrowserInfo]:
        """
        Detect installed browsers.
        
        Args:
            force_refresh (bool): Whether to force a refresh of the cache
            
        Returns:
            Dict[str, BrowserInfo]: Dictionary of detected browsers
        """
        # Try to load from cache first
        if not force_refresh and self._load_from_cache():
            return self.browsers
            
        logger.info("Detecting installed browsers...")
        
        # Clear existing browsers
        self.browsers = {}
        
        # Detect browsers based on OS
        if self.os_type == "Windows":
            self._detect_windows_browsers()
        elif self.os_type == "Darwin":  # macOS
            self._detect_macos_browsers()
        else:  # Linux
            self._detect_linux_browsers()
            
        # Detect default browser
        self._detect_default_browser()
        
        # Save to cache
        self._save_to_cache()
        
        logger.info(f"Detected {len(self.browsers)} browsers")
        for browser_type, browser_info in self.browsers.items():
            logger.info(f"- {browser_info}")
            
        return self.browsers
        
    def _detect_windows_browsers(self) -> None:
        """Detect browsers on Windows."""
        # Common browser registry locations
        browser_registry = {
            "chrome": {
                "path": r"SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\chrome.exe",
                "user_data": r"Google\Chrome\User Data",
                "default_profile": "Default"
            },
            "edge": {
                "path": r"SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\msedge.exe",
                "user_data": r"Microsoft\Edge\User Data",
                "default_profile": "Default"
            },
            "firefox": {
                "path": r"SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\firefox.exe",
                "user_data": r"Mozilla\Firefox\Profiles",
                "default_profile": None  # Firefox profiles are named differently
            },
            "brave": {
                "path": r"SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\brave.exe",
                "user_data": r"BraveSoftware\Brave-Browser\User Data",
                "default_profile": "Default"
            }
        }
        
        # Check registry for each browser
        for browser_type, registry_info in browser_registry.items():
            try:
                # Open registry key
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, registry_info["path"]) as key:
                    # Get executable path
                    executable_path = winreg.QueryValue(key, None)
                    
                    if executable_path and os.path.exists(executable_path):
                        # Get version
                        version = self._get_browser_version(executable_path, browser_type)
                        
                        # Get user data directory
                        user_data_dir = os.path.join(
                            os.environ.get("LOCALAPPDATA", ""),
                            registry_info["user_data"]
                        )
                        
                        # Create browser info
                        self.browsers[browser_type] = BrowserInfo(
                            browser_type=browser_type,
                            executable_path=executable_path,
                            version=version,
                            user_data_dir=user_data_dir,
                            default_profile=registry_info["default_profile"]
                        )
                        
                        logger.info(f"Found {browser_type} at {executable_path}")
            except Exception as e:
                logger.debug(f"Error detecting {browser_type}: {e}")
                
        # Check common installation paths as fallback
        if "chrome" not in self.browsers:
            self._check_common_path(
                "chrome",
                [
                    r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                    r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
                ],
                r"Google\Chrome\User Data",
                "Default"
            )
            
        if "edge" not in self.browsers:
            self._check_common_path(
                "edge",
                [
                    r"C:\Program Files\Microsoft\Edge\Application\msedge.exe",
                    r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe"
                ],
                r"Microsoft\Edge\User Data",
                "Default"
            )
            
        if "firefox" not in self.browsers:
            self._check_common_path(
                "firefox",
                [
                    r"C:\Program Files\Mozilla Firefox\firefox.exe",
                    r"C:\Program Files (x86)\Mozilla Firefox\firefox.exe"
                ],
                r"Mozilla\Firefox\Profiles",
                None
            )
            
        if "brave" not in self.browsers:
            self._check_common_path(
                "brave",
                [
                    r"C:\Program Files\BraveSoftware\Brave-Browser\Application\brave.exe",
                    r"C:\Program Files (x86)\BraveSoftware\Brave-Browser\Application\brave.exe"
                ],
                r"BraveSoftware\Brave-Browser\User Data",
                "Default"
            )
            
    def _check_common_path(self, 
                          browser_type: str, 
                          paths: List[str],
                          user_data_subpath: str,
                          default_profile: Optional[str]) -> None:
        """Check common installation paths for a browser."""
        for path in paths:
            if os.path.exists(path):
                # Get version
                version = self._get_browser_version(path, browser_type)
                
                # Get user data directory
                user_data_dir = os.path.join(
                    os.environ.get("LOCALAPPDATA", ""),
                    user_data_subpath
                )
                
                # Create browser info
                self.browsers[browser_type] = BrowserInfo(
                    browser_type=browser_type,
                    executable_path=path,
                    version=version,
                    user_data_dir=user_data_dir,
                    default_profile=default_profile
                )
                
                logger.info(f"Found {browser_type} at {path}")
                break
                
    def _detect_macos_browsers(self) -> None:
        """Detect browsers on macOS."""
        # Common browser paths on macOS
        browser_paths = {
            "chrome": {
                "paths": [
                    "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                    os.path.expanduser("~/Applications/Google Chrome.app/Contents/MacOS/Google Chrome")
                ],
                "user_data": os.path.expanduser("~/Library/Application Support/Google/Chrome"),
                "default_profile": "Default"
            },
            "edge": {
                "paths": [
                    "/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge",
                    os.path.expanduser("~/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge")
                ],
                "user_data": os.path.expanduser("~/Library/Application Support/Microsoft Edge"),
                "default_profile": "Default"
            },
            "firefox": {
                "paths": [
                    "/Applications/Firefox.app/Contents/MacOS/firefox",
                    os.path.expanduser("~/Applications/Firefox.app/Contents/MacOS/firefox")
                ],
                "user_data": os.path.expanduser("~/Library/Application Support/Firefox/Profiles"),
                "default_profile": None
            },
            "brave": {
                "paths": [
                    "/Applications/Brave Browser.app/Contents/MacOS/Brave Browser",
                    os.path.expanduser("~/Applications/Brave Browser.app/Contents/MacOS/Brave Browser")
                ],
                "user_data": os.path.expanduser("~/Library/Application Support/BraveSoftware/Brave-Browser"),
                "default_profile": "Default"
            }
        }
        
        # Check each browser
        for browser_type, browser_info in browser_paths.items():
            for path in browser_info["paths"]:
                if os.path.exists(path):
                    # Get version
                    version = self._get_browser_version(path, browser_type)
                    
                    # Create browser info
                    self.browsers[browser_type] = BrowserInfo(
                        browser_type=browser_type,
                        executable_path=path,
                        version=version,
                        user_data_dir=browser_info["user_data"],
                        default_profile=browser_info["default_profile"]
                    )
                    
                    logger.info(f"Found {browser_type} at {path}")
                    break
                    
    def _detect_linux_browsers(self) -> None:
        """Detect browsers on Linux."""
        # Try to find browsers using 'which' command
        for browser_type, cmd in [
            ("chrome", "google-chrome"),
            ("chrome", "google-chrome-stable"),
            ("edge", "microsoft-edge"),
            ("edge", "microsoft-edge-stable"),
            ("firefox", "firefox"),
            ("brave", "brave-browser")
        ]:
            try:
                result = subprocess.run(
                    ["which", cmd],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                
                if result.returncode == 0:
                    path = result.stdout.strip()
                    
                    if os.path.exists(path) and browser_type not in self.browsers:
                        # Get version
                        version = self._get_browser_version(path, browser_type)
                        
                        # Get user data directory
                        user_data_dir = None
                        default_profile = None
                        
                        if browser_type == "chrome":
                            user_data_dir = os.path.expanduser("~/.config/google-chrome")
                            default_profile = "Default"
                        elif browser_type == "edge":
                            user_data_dir = os.path.expanduser("~/.config/microsoft-edge")
                            default_profile = "Default"
                        elif browser_type == "firefox":
                            user_data_dir = os.path.expanduser("~/.mozilla/firefox")
                            default_profile = None
                        elif browser_type == "brave":
                            user_data_dir = os.path.expanduser("~/.config/BraveSoftware/Brave-Browser")
                            default_profile = "Default"
                            
                        # Create browser info
                        self.browsers[browser_type] = BrowserInfo(
                            browser_type=browser_type,
                            executable_path=path,
                            version=version,
                            user_data_dir=user_data_dir,
                            default_profile=default_profile
                        )
                        
                        logger.info(f"Found {browser_type} at {path}")
            except Exception as e:
                logger.debug(f"Error detecting {browser_type} using 'which {cmd}': {e}")
                
    def _get_browser_version(self, executable_path: str, browser_type: str) -> Optional[str]:
        """Get browser version."""
        try:
            if browser_type in ["chrome", "edge", "brave"]:
                # Chrome, Edge, and Brave use the same version flag
                result = subprocess.run(
                    [executable_path, "--version"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                
                if result.returncode == 0:
                    # Extract version number
                    match = re.search(r"(\d+\.\d+\.\d+\.\d+)", result.stdout)
                    if match:
                        return match.group(1)
            elif browser_type == "firefox":
                # Firefox uses a different version flag
                result = subprocess.run(
                    [executable_path, "--version"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                
                if result.returncode == 0:
                    # Extract version number
                    match = re.search(r"(\d+\.\d+(\.\d+)?)", result.stdout)
                    if match:
                        return match.group(1)
        except Exception as e:
            logger.debug(f"Error getting {browser_type} version: {e}")
            
        return None
        
    def _detect_default_browser(self) -> None:
        """Detect the default browser."""
        try:
            if self.os_type == "Windows":
                # Check Windows registry for default browser
                with winreg.OpenKey(
                    winreg.HKEY_CURRENT_USER,
                    r"SOFTWARE\Microsoft\Windows\Shell\Associations\UrlAssociations\http\UserChoice"
                ) as key:
                    prog_id = winreg.QueryValueEx(key, "ProgId")[0]
                    
                    if "Chrome" in prog_id:
                        self.default_browser = "chrome"
                    elif "Edge" in prog_id:
                        self.default_browser = "edge"
                    elif "Firefox" in prog_id:
                        self.default_browser = "firefox"
                    elif "Brave" in prog_id:
                        self.default_browser = "brave"
            elif self.os_type == "Darwin":  # macOS
                # Check macOS default browser
                result = subprocess.run(
                    ["defaults", "read", "com.apple.LaunchServices/com.apple.launchservices.secure", "LSHandlers"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                
                if result.returncode == 0:
                    output = result.stdout
                    
                    if "com.google.chrome" in output:
                        self.default_browser = "chrome"
                    elif "com.microsoft.edge" in output:
                        self.default_browser = "edge"
                    elif "org.mozilla.firefox" in output:
                        self.default_browser = "firefox"
                    elif "com.brave.Browser" in output:
                        self.default_browser = "brave"
            else:  # Linux
                # Check Linux default browser using xdg-settings
                result = subprocess.run(
                    ["xdg-settings", "get", "default-web-browser"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                
                if result.returncode == 0:
                    output = result.stdout.strip()
                    
                    if "chrome" in output:
                        self.default_browser = "chrome"
                    elif "edge" in output:
                        self.default_browser = "edge"
                    elif "firefox" in output:
                        self.default_browser = "firefox"
                    elif "brave" in output:
                        self.default_browser = "brave"
        except Exception as e:
            logger.debug(f"Error detecting default browser: {e}")
            
        # Mark default browser
        if self.default_browser and self.default_browser in self.browsers:
            self.browsers[self.default_browser].is_default = True
            logger.info(f"Default browser: {self.default_browser}")
            
    def _load_from_cache(self) -> bool:
        """Load browser information from cache."""
        if not os.path.exists(self.cache_file):
            return False
            
        try:
            with open(self.cache_file, "r") as f:
                data = json.load(f)
                
                # Check if cache is for current OS
                if data.get("os_type") != self.os_type:
                    return False
                    
                # Check if cache is recent (less than 1 day old)
                if time.time() - data.get("timestamp", 0) > 86400:
                    return False
                    
                # Load browsers
                self.browsers = {}
                for browser_type, browser_data in data.get("browsers", {}).items():
                    self.browsers[browser_type] = BrowserInfo.from_dict(browser_data)
                    
                # Load default browser
                self.default_browser = data.get("default_browser")
                
                logger.info(f"Loaded {len(self.browsers)} browsers from cache")
                return True
                
        except Exception as e:
            logger.debug(f"Error loading browser cache: {e}")
            return False
            
    def _save_to_cache(self) -> None:
        """Save browser information to cache."""
        try:
            # Create cache data
            cache_data = {
                "os_type": self.os_type,
                "timestamp": time.time(),
                "default_browser": self.default_browser,
                "browsers": {
                    browser_type: browser_info.to_dict()
                    for browser_type, browser_info in self.browsers.items()
                }
            }
            
            # Save to file
            with open(self.cache_file, "w") as f:
                json.dump(cache_data, f, indent=2)
                
            logger.info(f"Saved {len(self.browsers)} browsers to cache")
            
        except Exception as e:
            logger.debug(f"Error saving browser cache: {e}")
            
    def get_browser(self, browser_type: Optional[str] = None) -> Optional[BrowserInfo]:
        """
        Get information for a specific browser.
        
        Args:
            browser_type (Optional[str]): Type of browser to get, or None for default
            
        Returns:
            Optional[BrowserInfo]: Browser information, or None if not found
        """
        # If no browser type specified, use default
        if not browser_type:
            if self.default_browser and self.default_browser in self.browsers:
                return self.browsers[self.default_browser]
            elif self.browsers:
                # Return first available browser
                return next(iter(self.browsers.values()))
            else:
                return None
                
        # Return specified browser if available
        return self.browsers.get(browser_type)
        
    def get_all_browsers(self) -> Dict[str, BrowserInfo]:
        """Get all detected browsers."""
        return self.browsers
        
    def get_default_browser(self) -> Optional[BrowserInfo]:
        """Get the default browser."""
        if self.default_browser and self.default_browser in self.browsers:
            return self.browsers[self.default_browser]
        return None
