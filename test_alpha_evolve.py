"""
Test AlphaEvolve Implementation.

This script runs a test evolution to verify the AlphaEvolve implementation.
"""
import asyncio
import argparse
import logging
import os
import sys
import json
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent))

from core.logger import setup_logger
from core.state_manager import StateManager
from alpha_evolve.alpha_evolve_engine import AlphaEvolveEngine
from alpha_evolve_monitor import AlphaEvolveMonitor

# Set up logger
logger = setup_logger("test_alpha_evolve")

async def run_test_evolution(args):
    """
    Run a test evolution.
    
    Args:
        args: Command-line arguments
    """
    logger.info("Running test evolution")
    
    # Initialize state manager
    state_manager = StateManager()
    await state_manager.initialize()
    
    # Initialize AlphaEvolve engine
    config_path = f"config/alpha_evolve_{args.domain}_config.json" if args.domain else "config/alpha_evolve_config.json"
    
    if not os.path.exists(config_path):
        logger.warning(f"Configuration file not found: {config_path}")
        logger.info("Using default configuration")
        config_path = "config/alpha_evolve_config.json"
    
    alpha_evolve_engine = AlphaEvolveEngine(
        state_manager=state_manager,
        config_path=config_path,
    )
    await alpha_evolve_engine.initialize()
    
    # Initialize monitor if requested
    monitor = None
    
    if args.monitor:
        monitor = AlphaEvolveMonitor(state_manager)
        await monitor.initialize()
    
    # Define test problem
    if args.problem_type == "sorting":
        problem = {
            "type": "optimization",
            "objective": "Find the most efficient sorting algorithm for a list of integers",
            "requirements": [
                "The algorithm must correctly sort a list of integers in ascending order",
                "The algorithm should be as efficient as possible in terms of time complexity",
                "The algorithm should handle edge cases (empty list, single element, etc.)",
            ],
            "inputs": {
                "data": "List[int]",
            },
            "outputs": "List[int]",
        }
    elif args.problem_type == "trading":
        problem = {
            "type": "agent_enhancement",
            "agent_id": "trading_agent",
            "capability": "market_analysis",
            "optimization_metric": "prediction_accuracy",
            "objective": "Enhance the market analysis capability of the trading agent",
            "requirements": [
                "The implementation must analyze market data to predict future price movements",
                "It should use technical indicators and pattern recognition",
                "It must handle different timeframes (1m, 5m, 15m, 1h, 4h, 1d)",
                "It should provide confidence scores for predictions",
            ],
            "interface": "def analyze_market(symbol: str, timeframe: str, indicators: List[str]) -> Dict[str, Any]",
        }
    elif args.problem_type == "insurance":
        problem = {
            "type": "agent_enhancement",
            "agent_id": "insurance_agent",
            "capability": "risk_assessment",
            "optimization_metric": "assessment_accuracy",
            "objective": "Enhance the risk assessment capability of the insurance agent",
            "requirements": [
                "The implementation must accurately assess risk factors for insurance applicants",
                "It should calculate appropriate premium rates based on risk factors",
                "It must comply with insurance regulations",
                "It should handle different insurance types (life, health, property)",
            ],
            "interface": "def assess_risk(applicant_data: Dict[str, Any], insurance_type: str) -> Dict[str, Any]",
        }
    else:
        problem = {
            "type": "optimization",
            "objective": f"Solve a {args.problem_type} problem",
            "requirements": [
                "The solution must be correct and efficient",
                "It should handle edge cases appropriately",
                "It must be well-structured and readable",
            ],
        }
    
    # Run evolution
    logger.info(f"Running evolution for problem type: {args.problem_type}")
    
    result = await alpha_evolve_engine.evolve(
        problem=problem,
        generations=args.generations,
        population_size=args.population_size,
        fitness_threshold=args.fitness_threshold,
        timeout=args.timeout,
    )
    
    # Print result
    logger.info(f"Evolution completed with status: {result['status']}")
    logger.info(f"Evolution ID: {result['evolution_id']}")
    logger.info(f"Generations: {result['generations']}")
    logger.info(f"Best fitness: {result['best_fitness']}")
    
    if result.get("best_solution"):
        logger.info("Best solution found:")
        logger.info(result["best_solution"]["code"])
    
    # Save result to file
    result_path = Path(f"test_evolution_{args.problem_type}_result.json")
    
    with open(result_path, "w") as f:
        # Convert result to JSON-serializable format
        serializable_result = {
            "evolution_id": result["evolution_id"],
            "status": result["status"],
            "generations": result["generations"],
            "best_fitness": result["best_fitness"],
        }
        
        if result.get("best_solution"):
            serializable_result["best_solution"] = {
                "code": result["best_solution"]["code"],
            }
        
        json.dump(serializable_result, f, indent=2)
    
    logger.info(f"Result saved to {result_path}")
    
    # Collect metrics if monitoring
    if monitor:
        await monitor.collect_evolution_metrics(alpha_evolve_engine)
        await monitor.collect_system_metrics()
        await monitor.generate_reports()
    
    # Shutdown AlphaEvolve engine
    await alpha_evolve_engine.shutdown()

def main():
    """Main entry point."""
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="Test AlphaEvolve Implementation")
    parser.add_argument("--problem-type", type=str, default="sorting", help="Problem type to test")
    parser.add_argument("--domain", type=str, help="Domain-specific configuration to use")
    parser.add_argument("--generations", type=int, default=10, help="Number of generations")
    parser.add_argument("--population-size", type=int, default=5, help="Population size")
    parser.add_argument("--fitness-threshold", type=float, default=0.95, help="Fitness threshold")
    parser.add_argument("--timeout", type=int, default=60, help="Timeout in seconds")
    parser.add_argument("--monitor", action="store_true", help="Enable performance monitoring")
    args = parser.parse_args()
    
    # Run test evolution
    asyncio.run(run_test_evolution(args))

if __name__ == "__main__":
    main()
