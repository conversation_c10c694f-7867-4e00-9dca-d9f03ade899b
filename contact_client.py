"""
Contact Client

This script uses the Client Communication Agent to contact a client.
It's part of the AI agent system for handling client communications.
"""
import os
import sys
import argparse
import logging
from agents.client_communication_agent import ClientCommunicationAgent

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/contact_client.log", mode='a'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("contact_client")

def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Contact a client using the AI agent system")
    parser.add_argument("--client", type=str, default="Alyssa", help="Client name to contact")
    parser.add_argument("--template", type=str, default="iul_policy", help="Template to use for communication")
    parser.add_argument("--agent-name", type=str, default="<PERSON>", help="Name of the agent")
    parser.add_argument("--agent-email", type=str, default="<EMAIL>", help="Email of the agent")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")

    args = parser.parse_args()

    # Set log level
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)

    print("Contact Client")
    print("==============")
    print()

    # Create directories
    os.makedirs("logs", exist_ok=True)

    # Create agent
    agent = ClientCommunicationAgent(
        agent_name=args.agent_name,
        agent_email=args.agent_email,
        debug=args.debug
    )

    try:
        # Get client
        client = agent.get_client(args.client)
        if not client:
            print(f"❌ Client not found: {args.client}")
            return 1

        print(f"Client: {client.get('full_name', client.get('name'))} ({client.get('email')})")
        print(f"Lead Type: {client.get('lead_type', 'unknown')}")
        print(f"Template: {args.template}")
        print()

        # Send email based on lead type and template
        if client.get('lead_type') == "insurance" and args.template == "iul_policy":
            print("Sending IUL policy email...")
            success = agent.send_iul_email_to_client(args.client)
        elif client.get('lead_type') == "music" and args.template == "music_services":
            print("Sending music services email...")
            # Future implementation for music services
            print("Music services email not implemented yet")
            success = False
        elif client.get('lead_type') == "marketing" and args.template == "marketing_services":
            print("Sending marketing services email...")
            # Future implementation for marketing services
            print("Marketing services email not implemented yet")
            success = False
        else:
            print(f"Sending email using template: {args.template}...")
            success = agent.send_email_to_client(args.client, args.template)

        if success:
            print(f"✅ Successfully contacted {client.get('name')}")
        else:
            print(f"❌ Failed to contact {client.get('name')}")
            return 1

        return 0
    finally:
        # Stop agent
        agent.stop()

if __name__ == "__main__":
    sys.exit(main())
