"""
Test script for Calendly integration.

This script tests the Calendly integration for the Insurance Lead Agent.
"""
import os
import sys
import json
import asyncio
import argparse
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import aiohttp

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.calendar_integration import CalendarIntegration
from core.logger import setup_logger

# Set up logger
logger = setup_logger("test_calendly_integration")

async def test_calendly_connection(api_key: str, user_uri: str = None):
    """
    Test connection to Calendly API.
    
    Args:
        api_key (str): Calendly API key
        user_uri (str, optional): Calendly user URI
        
    Returns:
        bool: Success status
    """
    logger.info("Testing Calendly connection...")
    
    # Create configuration
    config = {
        "enabled": True,
        "calendly_enabled": True,
        "calendly_api_key": api_key,
        "calendly_user_uri": user_uri
    }
    
    # Create calendar integration
    calendar_integration = CalendarIntegration(config)
    
    try:
        # Check Calendly status
        status = await calendar_integration.check_calendly_status()
        
        if status.get("status") == "available":
            logger.info("Calendly connection successful!")
            logger.info(f"User details: {status.get('details', {})}")
            
            # If user_uri is not provided, get it from the response
            if not user_uri and "details" in status:
                user_uri = status["details"].get("resource", {}).get("uri")
                logger.info(f"User URI: {user_uri}")
                
                # Save user URI to credentials file
                if user_uri:
                    await save_user_uri(api_key, user_uri)
            
            return True
        else:
            logger.error(f"Calendly connection failed: {status.get('error', 'Unknown error')}")
            return False
    
    except Exception as e:
        logger.exception(f"Error testing Calendly connection: {e}")
        return False

async def get_event_types(api_key: str, user_uri: str):
    """
    Get Calendly event types.
    
    Args:
        api_key (str): Calendly API key
        user_uri (str): Calendly user URI
        
    Returns:
        List[Dict]: List of event types
    """
    logger.info("Getting Calendly event types...")
    
    try:
        async with aiohttp.ClientSession() as session:
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            
            # Get event types
            async with session.get(
                f"https://api.calendly.com/event_types?user={user_uri}",
                headers=headers
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    event_types = data.get("collection", [])
                    
                    logger.info(f"Found {len(event_types)} event types")
                    
                    # Print event types
                    for i, event_type in enumerate(event_types):
                        name = event_type.get("name", "Unknown")
                        uri = event_type.get("uri", "")
                        slug = event_type.get("slug", "")
                        duration = event_type.get("duration", 0)
                        
                        logger.info(f"{i+1}. {name} ({duration} min) - {uri}")
                        logger.info(f"   URL: https://calendly.com/{slug}")
                    
                    # Save event types to credentials file
                    await save_event_types(api_key, user_uri, event_types)
                    
                    return event_types
                else:
                    error_data = await response.text()
                    logger.error(f"Failed to get event types: {error_data}")
                    return []
    
    except Exception as e:
        logger.exception(f"Error getting Calendly event types: {e}")
        return []

async def create_scheduling_link(api_key: str, event_type_uri: str):
    """
    Create a Calendly scheduling link.
    
    Args:
        api_key (str): Calendly API key
        event_type_uri (str): Event type URI
        
    Returns:
        Dict: Scheduling link result
    """
    logger.info(f"Creating scheduling link for event type: {event_type_uri}")
    
    # Create configuration
    config = {
        "enabled": True,
        "calendly_enabled": True,
        "calendly_api_key": api_key
    }
    
    # Create calendar integration
    calendar_integration = CalendarIntegration(config)
    
    try:
        # Create scheduling link
        result = await calendar_integration.create_calendly_scheduling_link(
            event_type_uri,
            {
                "max_booking_time": (datetime.now() + timedelta(days=30)).isoformat(),
                "name": f"Insurance Consultation {datetime.now().strftime('%Y-%m-%d')}"
            }
        )
        
        if result.get("success"):
            logger.info("Scheduling link created successfully!")
            logger.info(f"Booking URL: {result.get('booking_url')}")
            return result
        else:
            logger.error(f"Failed to create scheduling link: {result.get('error', 'Unknown error')}")
            return result
    
    except Exception as e:
        logger.exception(f"Error creating scheduling link: {e}")
        return {"error": str(e)}

async def save_user_uri(api_key: str, user_uri: str):
    """
    Save Calendly user URI to credentials file.
    
    Args:
        api_key (str): Calendly API key
        user_uri (str): Calendly user URI
    """
    try:
        # Load existing credentials
        credentials_path = "credentials/calendly/calendly.json"
        
        with open(credentials_path, "r") as f:
            credentials = json.load(f)
        
        # Update credentials
        credentials["api_key"] = api_key
        credentials["user_uri"] = user_uri
        credentials["last_updated"] = datetime.now().isoformat()
        
        # Save credentials
        with open(credentials_path, "w") as f:
            json.dump(credentials, f, indent=4)
        
        logger.info(f"Saved user URI to {credentials_path}")
    
    except Exception as e:
        logger.exception(f"Error saving user URI: {e}")

async def save_event_types(api_key: str, user_uri: str, event_types: List[Dict]):
    """
    Save Calendly event types to credentials file.
    
    Args:
        api_key (str): Calendly API key
        user_uri (str): Calendly user URI
        event_types (List[Dict]): List of event types
    """
    try:
        # Load existing credentials
        credentials_path = "credentials/calendly/calendly.json"
        
        with open(credentials_path, "r") as f:
            credentials = json.load(f)
        
        # Update credentials
        credentials["api_key"] = api_key
        credentials["user_uri"] = user_uri
        credentials["last_updated"] = datetime.now().isoformat()
        
        # Map event types to insurance types
        for event_type in event_types:
            name = event_type.get("name", "").lower()
            uri = event_type.get("uri", "")
            slug = event_type.get("slug", "")
            url = f"https://calendly.com/{slug}"
            
            # Try to match event type to insurance type
            for insurance_type, event_info in credentials["event_types"].items():
                if insurance_type.lower() in name or insurance_type.lower() in slug:
                    credentials["event_types"][insurance_type]["uri"] = uri
                    credentials["event_types"][insurance_type]["url"] = url
                    logger.info(f"Mapped {name} to {insurance_type}")
            
            # If no match found, check if it's the default
            if "15" in name and "min" in name:
                credentials["event_types"]["default"]["uri"] = uri
                credentials["event_types"]["default"]["url"] = url
                logger.info(f"Mapped {name} to default")
        
        # Save credentials
        with open(credentials_path, "w") as f:
            json.dump(credentials, f, indent=4)
        
        logger.info(f"Saved event types to {credentials_path}")
    
    except Exception as e:
        logger.exception(f"Error saving event types: {e}")

async def main():
    """Run the Calendly integration test."""
    parser = argparse.ArgumentParser(description="Calendly Integration Test")
    parser.add_argument("--api-key", type=str, help="Calendly API key")
    parser.add_argument("--user-uri", type=str, help="Calendly user URI")
    parser.add_argument("--event-type-uri", type=str, help="Event type URI for creating scheduling link")
    args = parser.parse_args()
    
    # If API key is not provided, try to load from credentials file
    api_key = args.api_key
    user_uri = args.user_uri
    event_type_uri = args.event_type_uri
    
    if not api_key:
        try:
            with open("credentials/calendly/calendly.json", "r") as f:
                credentials = json.load(f)
                api_key = credentials.get("api_key", "")
                user_uri = credentials.get("user_uri", "")
        except Exception as e:
            logger.exception(f"Error loading credentials: {e}")
    
    if not api_key:
        logger.error("Calendly API key is required")
        print("Please provide a Calendly API key using the --api-key argument")
        print("You can find your API key at https://calendly.com/integrations/api_webhooks")
        return
    
    # Test Calendly connection
    connection_success = await test_calendly_connection(api_key, user_uri)
    
    if not connection_success:
        return
    
    # If user_uri is still not available, we can't continue
    if not user_uri:
        try:
            with open("credentials/calendly/calendly.json", "r") as f:
                credentials = json.load(f)
                user_uri = credentials.get("user_uri", "")
        except Exception:
            pass
    
    if not user_uri:
        logger.error("Calendly user URI is required for further tests")
        return
    
    # Get event types
    event_types = await get_event_types(api_key, user_uri)
    
    if not event_types:
        return
    
    # Create scheduling link if event type URI is provided
    if event_type_uri:
        await create_scheduling_link(api_key, event_type_uri)
    elif event_types:
        # Use the first event type
        event_type_uri = event_types[0].get("uri", "")
        if event_type_uri:
            await create_scheduling_link(api_key, event_type_uri)

if __name__ == "__main__":
    asyncio.run(main())
