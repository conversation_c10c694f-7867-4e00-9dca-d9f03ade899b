# Insurance Drip Campaign System

This system implements an automated drip campaign for insurance leads, managing the sequence of calls, voicemails, texts, and emails with decreasing frequency. It uses UI-TARS 1.5 for browser automation to handle Google Voice calls/texts and Gmail emails.

## Overview

The Insurance Drip Campaign System follows this workflow:

1. **Initial Contact Sequence**:
   - First phone call attempt
   - If no answer, immediate second call attempt
   - If still no answer, leave a voicemail using ElevenLabs with <PERSON>'s female voice
   - Follow up with a text message
   - Send an email

2. **Follow-up Sequence**:
   - Continue with calls, texts, and emails at decreasing frequency
   - Gradually reduce contact frequency over time
   - Stop if the client responds with "STOP" or you manually stop the campaign

## Prerequisites

Before using this system, make sure you have:

1. Python 3.8 or higher installed
2. UI-TARS 1.5 installed and configured
3. Chrome browser installed
4. Gmail account credentials (<EMAIL>)
5. Google Voice account set up with the phone number ************
6. ElevenLabs API key for voicemail generation

## Installation

1. Install required Python packages:
   ```
   pip install -r requirements.txt
   ```

2. Configure UI-TARS 1.5 for browser automation:
   - Make sure UI-TARS is properly installed
   - Configure UI-TARS to work with Chrome browser
   - Set up UI-TARS API server

3. Set up ElevenLabs API key:
   - Get an API key from ElevenLabs
   - Set it as an environment variable or provide it when running the system

## Usage

### Starting a Drip Campaign

To start a drip campaign for a new insurance lead, run:

```
start_insurance_drip_campaign.bat
```

This will:
1. Prompt you for client information (name, phone, email, insurance type, budget)
2. Start the drip campaign with the initial sequence
3. Optionally run the agent to process scheduled communications

Alternatively, you can use the Python script directly:

```
python run_drip_campaign.py start --name "Client Name" --phone "1234567890" --email "<EMAIL>" --insurance-type "life" --budget "$100/month"
```

### Stopping a Drip Campaign

To stop an active drip campaign, run:

```
stop_insurance_drip_campaign.bat
```

This will:
1. Prompt you for the identifier type (campaign ID, name, phone, or email)
2. Ask for the specific identifier
3. Stop the campaign and cancel all scheduled communications

Alternatively, you can use the Python script directly:

```
python run_drip_campaign.py stop --identifier "Client Name" --identifier-type "name"
```

### Running the Agent

To run the drip campaign agent, which processes scheduled communications:

```
python run_drip_campaign.py run
```

This will:
1. Check for scheduled communications that need to be sent
2. Send communications using UI-TARS browser automation
3. Process client responses
4. Continue running until stopped with Ctrl+C

### Handling Client Responses

To manually handle a response from a client:

```
python run_drip_campaign.py response --response-type "text" --sender "1234567890" --content "Yes, I'm interested"
```

## Configuration

The drip campaign is configured in `config/drip_campaign_config.json`, which defines:

- Campaign sequences and timing
- Message templates for calls, voicemails, texts, and emails
- Working hours and other settings

You can customize this configuration to adjust the campaign flow, message content, and timing.

## Components

The system consists of the following components:

1. **Insurance Drip Campaign Agent** (`insurance_drip_campaign_agent.py`):
   - Manages the drip campaign workflow
   - Schedules and sends communications
   - Processes client responses

2. **Google Voice UI-TARS Automation** (`google_voice_ui_tars_automation.py`):
   - Provides browser automation for Google Voice
   - Handles making calls, sending texts, and leaving voicemails

3. **Gmail UI-TARS Automation** (`ui_tars_gmail_automation.py`):
   - Provides browser automation for Gmail
   - Handles sending emails

4. **ElevenLabs Voicemail Generator** (`elevenlabs_voicemail_generator.py`):
   - Generates voicemails using ElevenLabs API
   - Creates female voice recordings for voicemails

5. **Drip Campaign Workflow** (`drip_campaign_workflow.py`):
   - Defines the overall workflow
   - Provides high-level functions for managing campaigns

6. **Run Drip Campaign** (`run_drip_campaign.py`):
   - Command-line interface for the system
   - Handles starting/stopping campaigns and running the agent

## Troubleshooting

If you encounter issues:

1. Check the logs in the `logs` directory
2. Verify that UI-TARS is properly configured and running
3. Ensure that the Google Voice and Gmail accounts are accessible
4. Check that the ElevenLabs API key is valid

## Notes

- The system operates within configured working hours (default: 9 AM to 6 PM, Monday to Friday)
- Communications are scheduled with decreasing frequency over time
- The campaign stops if the client responds with "STOP" or you manually stop it
- All communications are tracked and can be reviewed in the agent's state
