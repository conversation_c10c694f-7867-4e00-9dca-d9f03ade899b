"""
Insurance Drip Campaign Agent for the Multi-Agent AI System.

This agent implements the drip campaign for insurance leads, managing
the sequence of calls, voicemails, texts, and emails with decreasing frequency.
"""
import asyncio
import json
import logging
import os
import sys
import time
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta

# Add parent directory to path to import from core
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.logger import setup_logger
from core.agent_base import AgentBase
from google_voice_ui_tars_automation import GoogleVoiceUITarsAutomation
from ui_tars_gmail_automation import GmailUITarsAutomation
from elevenlabs_voicemail_generator import ElevenLabsVoicemailGenerator
from services.google_drive_service import GoogleDriveService
from services.gmail_service import GmailService

# Set up logger
logger = setup_logger("insurance_drip_campaign_agent")

class InsuranceDripCampaignAgent(AgentBase):
    """
    Insurance Drip Campaign Agent for the Multi-Agent AI System.

    This agent implements the drip campaign for insurance leads, managing
    the sequence of calls, voicemails, texts, and emails with decreasing frequency.
    """

    def __init__(self,
                 agent_id: str = "insurance_drip_campaign_agent",
                 config_path: str = "config/drip_campaign_config.json",
                 ui_tars_api_url: str = "http://localhost:8080",
                 ui_tars_model_name: str = "UI-TARS-1.5-7B",
                 ui_tars_installation_path: Optional[str] = None,
                 elevenlabs_api_key: Optional[str] = None,
                 gmail_credentials_path: Optional[str] = None,
                 google_drive_credentials_path: Optional[str] = None,
                 **kwargs):
        """
        Initialize the Insurance Drip Campaign Agent.

        Args:
            agent_id (str): Agent identifier
            config_path (str): Path to drip campaign configuration
            ui_tars_api_url (str): URL of the UI-TARS API
            ui_tars_model_name (str): Name of the model to use
            ui_tars_installation_path (Optional[str]): Path to UI-TARS installation
            elevenlabs_api_key (Optional[str]): ElevenLabs API key
            gmail_credentials_path (Optional[str]): Path to Gmail API credentials
            google_drive_credentials_path (Optional[str]): Path to Google Drive API credentials
            **kwargs: Additional arguments to pass to the base agent
        """
        super().__init__(agent_id=agent_id, **kwargs)

        self.config_path = config_path
        self.ui_tars_api_url = ui_tars_api_url
        self.ui_tars_model_name = ui_tars_model_name
        self.ui_tars_installation_path = ui_tars_installation_path
        self.elevenlabs_api_key = elevenlabs_api_key
        self.gmail_credentials_path = gmail_credentials_path or "credentials/gmail/client_secret.json"
        self.google_drive_credentials_path = google_drive_credentials_path or "credentials/google_drive/client_secret.json"

        self.config = {}
        self.campaigns = {}
        self.active_campaigns = {}
        self.scheduled_communications = {}

        self.google_voice_automation = None
        self.gmail_automation = None
        self.voicemail_generator = None
        self.gmail_service = None
        self.google_drive_service = None

        self.email_account = "<EMAIL>"
        self.email_password = "GodisSoGood!777"
        self.phone_number = "**********"

        # Create directories for attachments and files
        os.makedirs("attachments", exist_ok=True)
        os.makedirs("downloads", exist_ok=True)

    async def initialize(self) -> bool:
        """
        Initialize the Insurance Drip Campaign Agent.

        Returns:
            bool: True if initialization was successful, False otherwise
        """
        self.logger.info("Initializing Insurance Drip Campaign Agent")

        # Initialize base agent
        await super().initialize()

        # Load configuration
        await self._load_config()

        # Initialize Google Voice automation
        self.google_voice_automation = GoogleVoiceUITarsAutomation(
            ui_tars_api_url=self.ui_tars_api_url,
            ui_tars_model_name=self.ui_tars_model_name,
            ui_tars_installation_path=self.ui_tars_installation_path
        )

        # Initialize Gmail automation
        self.gmail_automation = GmailUITarsAutomation(
            api_url=self.ui_tars_api_url,
            model_name=self.ui_tars_model_name,
            installation_path=self.ui_tars_installation_path
        )

        # Initialize ElevenLabs voicemail generator
        self.voicemail_generator = ElevenLabsVoicemailGenerator(
            api_key=self.elevenlabs_api_key
        )

        # Initialize Gmail service
        try:
            self.gmail_service = GmailService(
                credentials_path=self.gmail_credentials_path,
                token_path="credentials/gmail/token.pickle"
            )
            self.logger.info("Gmail service initialized successfully")
        except Exception as e:
            self.logger.warning(f"Failed to initialize Gmail service: {e}")
            self.logger.info("Will use UI-TARS for Gmail automation instead")

        # Initialize Google Drive service
        try:
            self.google_drive_service = GoogleDriveService(
                credentials_path=self.google_drive_credentials_path,
                token_path="credentials/google_drive/token.pickle"
            )
            self.logger.info("Google Drive service initialized successfully")
        except Exception as e:
            self.logger.warning(f"Failed to initialize Google Drive service: {e}")

        # Load active campaigns
        await self._load_active_campaigns()

        # Load scheduled communications
        await self._load_scheduled_communications()

        self.logger.info("Insurance Drip Campaign Agent initialized successfully")
        return True

    async def _load_config(self) -> None:
        """Load drip campaign configuration."""
        try:
            with open(self.config_path, "r", encoding="utf-8") as f:
                self.config = json.load(f)

            self.campaigns = self.config.get("campaigns", {})
            self.logger.info(f"Loaded drip campaign configuration with {len(self.campaigns)} campaigns")
        except Exception as e:
            self.logger.exception(f"Error loading drip campaign configuration: {e}")
            self.config = {}
            self.campaigns = {}

    async def _load_active_campaigns(self) -> None:
        """Load active campaigns from state manager."""
        try:
            active_campaigns = await self.state_manager.get_state("drip_campaign", "active_campaigns")
            if active_campaigns:
                self.active_campaigns = active_campaigns
                self.logger.info(f"Loaded {len(self.active_campaigns)} active campaigns from state manager")
            else:
                self.active_campaigns = {}
                self.logger.info("No active campaigns found in state manager")
        except Exception as e:
            self.logger.exception(f"Error loading active campaigns: {e}")
            self.active_campaigns = {}

    async def _load_scheduled_communications(self) -> None:
        """Load scheduled communications from state manager."""
        try:
            scheduled_communications = await self.state_manager.get_state("drip_campaign", "scheduled_communications")
            if scheduled_communications:
                self.scheduled_communications = scheduled_communications
                self.logger.info(f"Loaded {len(self.scheduled_communications)} scheduled communications from state manager")
            else:
                self.scheduled_communications = {}
                self.logger.info("No scheduled communications found in state manager")
        except Exception as e:
            self.logger.exception(f"Error loading scheduled communications: {e}")
            self.scheduled_communications = {}

    async def _save_active_campaigns(self) -> None:
        """Save active campaigns to state manager."""
        try:
            await self.state_manager.update_state("drip_campaign", "active_campaigns", self.active_campaigns)
            self.logger.info(f"Saved {len(self.active_campaigns)} active campaigns to state manager")
        except Exception as e:
            self.logger.exception(f"Error saving active campaigns: {e}")

    async def _save_scheduled_communications(self) -> None:
        """Save scheduled communications to state manager."""
        try:
            await self.state_manager.update_state("drip_campaign", "scheduled_communications", self.scheduled_communications)
            self.logger.info(f"Saved {len(self.scheduled_communications)} scheduled communications to state manager")
        except Exception as e:
            self.logger.exception(f"Error saving scheduled communications: {e}")

    async def execute_cycle(self) -> None:
        """Execute one cycle of the agent's logic."""
        self.logger.debug("Executing Insurance Drip Campaign Agent cycle")

        try:
            # Check for new leads to start campaigns for
            await self._check_new_leads()

            # Check for scheduled communications
            await self._check_scheduled_communications()

            # Check for campaign responses
            await self._check_campaign_responses()

            # Save state
            await self._save_active_campaigns()
            await self._save_scheduled_communications()

        except Exception as e:
            self.logger.exception(f"Error in Insurance Drip Campaign Agent cycle: {e}")

    async def _check_new_leads(self) -> None:
        """Check for new leads to start campaigns for."""
        # This would integrate with a lead management system
        # For now, we'll just log a message
        self.logger.debug("Checking for new leads")

    async def _check_scheduled_communications(self) -> None:
        """Check for scheduled communications that need to be sent."""
        current_time = datetime.now()

        # Check if we're within working hours
        if not self._is_within_working_hours(current_time):
            self.logger.debug("Outside of working hours, skipping scheduled communications")
            return

        for comm_id, comm in list(self.scheduled_communications.items()):
            if comm.get("status") != "scheduled":
                continue

            scheduled_time = datetime.fromisoformat(comm.get("scheduled_time"))

            # Check if it's time to send
            if current_time >= scheduled_time:
                await self._send_scheduled_communication(comm_id, comm)

    async def _send_scheduled_communication(self, comm_id: str, comm: Dict) -> None:
        """
        Send a scheduled communication.

        Args:
            comm_id (str): Communication identifier
            comm (Dict): Communication data
        """
        comm_type = comm.get("type")
        recipient = comm.get("recipient")
        campaign_id = comm.get("campaign_id")

        self.logger.info(f"Sending {comm_type} to {recipient} for campaign {campaign_id}")

        result = {"success": False, "error": "Unknown communication type"}

        try:
            if comm_type == "call":
                result = await self._make_call(comm)
            elif comm_type == "voicemail":
                result = await self._leave_voicemail(comm)
            elif comm_type == "text":
                result = await self._send_text(comm)
            elif comm_type == "email":
                result = await self._send_email(comm)
            else:
                self.logger.warning(f"Unknown communication type: {comm_type}")

            # Update communication status
            if result.get("success"):
                comm["status"] = "sent"
                comm["sent_time"] = datetime.now().isoformat()
                comm["result"] = result
                self.scheduled_communications[comm_id] = comm

                self.logger.info(f"Successfully sent {comm_type} to {recipient}")
            else:
                comm["status"] = "failed"
                comm["error"] = result.get("error")
                comm["retry_count"] = comm.get("retry_count", 0) + 1

                # Reschedule if retry count is below threshold
                if comm.get("retry_count", 0) < 3:
                    comm["status"] = "scheduled"
                    comm["scheduled_time"] = (datetime.now() + timedelta(minutes=30)).isoformat()
                    self.logger.info(f"Rescheduled {comm_type} to {recipient} for {comm['scheduled_time']}")

                self.scheduled_communications[comm_id] = comm

                self.logger.warning(f"Failed to send {comm_type} to {recipient}: {result.get('error')}")

        except Exception as e:
            self.logger.exception(f"Error sending {comm_type} to {recipient}: {e}")

            # Update communication status
            comm["status"] = "failed"
            comm["error"] = str(e)
            comm["retry_count"] = comm.get("retry_count", 0) + 1

            # Reschedule if retry count is below threshold
            if comm.get("retry_count", 0) < 3:
                comm["status"] = "scheduled"
                comm["scheduled_time"] = (datetime.now() + timedelta(minutes=30)).isoformat()
                self.logger.info(f"Rescheduled {comm_type} to {recipient} for {comm['scheduled_time']}")

            self.scheduled_communications[comm_id] = comm

    async def _make_call(self, comm: Dict) -> Dict:
        """
        Make a phone call.

        Args:
            comm (Dict): Communication data

        Returns:
            Dict: Result of the operation
        """
        recipient = comm.get("recipient")
        template_id = comm.get("template_id")
        template_vars = comm.get("template_vars", {})

        # Initialize Google Voice automation if needed
        if not self.google_voice_automation.is_initialized:
            await self.google_voice_automation.initialize()

        # Log in to Google Voice if needed
        if not self.google_voice_automation.is_logged_in:
            await self.google_voice_automation.login(
                email_account=self.email_account,
                password=self.email_password
            )

        # Make the call
        result = await self.google_voice_automation.make_call(recipient)

        return result

    async def _leave_voicemail(self, comm: Dict) -> Dict:
        """
        Leave a voicemail.

        Args:
            comm (Dict): Communication data

        Returns:
            Dict: Result of the operation
        """
        recipient = comm.get("recipient")
        template_id = comm.get("template_id")
        template_vars = comm.get("template_vars", {})

        # Get template
        template = self._get_template("voicemail", template_id)
        if not template:
            return {"success": False, "error": f"Voicemail template not found: {template_id}"}

        # Apply template variables
        script = template.get("script", "")
        for key, value in template_vars.items():
            script = script.replace(f"{{{key}}}", str(value))

        # Generate voicemail audio
        voice_name = template.get("voice_name", "rachel")
        client_name = template_vars.get("client_name", "")

        self.logger.info(f"Generating voicemail audio using ElevenLabs with voice: {voice_name}")

        voicemail_result = await self.voicemail_generator.generate_voicemail(
            script=script,
            voice_name=voice_name,
            client_name=client_name
        )

        if voicemail_result.get("status") != "success":
            self.logger.error(f"Failed to generate voicemail: {voicemail_result.get('error')}")
            return {"success": False, "error": f"Failed to generate voicemail: {voicemail_result.get('error')}"}

        # Get the voicemail audio file path
        voicemail_audio_path = voicemail_result.get("filepath")
        self.logger.info(f"Voicemail audio generated successfully: {voicemail_audio_path}")

        # Initialize Google Voice automation if needed
        if not self.google_voice_automation.is_initialized:
            await self.google_voice_automation.initialize()

        # Log in to Google Voice if needed
        if not self.google_voice_automation.is_logged_in:
            await self.google_voice_automation.login(
                email_account=self.email_account,
                password=self.email_password
            )

        # Leave voicemail with the generated audio
        self.logger.info(f"Leaving voicemail for {recipient} with audio file")
        result = await self.google_voice_automation.leave_voicemail(
            phone_number=recipient,
            voicemail_audio_path=voicemail_audio_path
        )

        # Add voicemail details to result
        if result.get("success"):
            result["voicemail_audio"] = voicemail_audio_path
            result["voice_name"] = voice_name
            result["script"] = script

        return result

    async def _send_text(self, comm: Dict) -> Dict:
        """
        Send a text message.

        Args:
            comm (Dict): Communication data

        Returns:
            Dict: Result of the operation
        """
        recipient = comm.get("recipient")
        template_id = comm.get("template_id")
        template_vars = comm.get("template_vars", {})

        # Get template
        template = self._get_template("text", template_id)
        if not template:
            return {"success": False, "error": f"Text template not found: {template_id}"}

        # Apply template variables
        message = template.get("message", "")
        for key, value in template_vars.items():
            message = message.replace(f"{{{key}}}", str(value))

        # Initialize Google Voice automation if needed
        if not self.google_voice_automation.is_initialized:
            await self.google_voice_automation.initialize()

        # Log in to Google Voice if needed
        if not self.google_voice_automation.is_logged_in:
            await self.google_voice_automation.login(
                email_account=self.email_account,
                password=self.email_password
            )

        # Send text message
        result = await self.google_voice_automation.send_text_message(recipient, message)

        return result

    async def _send_email(self, comm: Dict) -> Dict:
        """
        Send an email.

        Args:
            comm (Dict): Communication data

        Returns:
            Dict: Result of the operation
        """
        recipient = comm.get("recipient")
        template_id = comm.get("template_id")
        template_vars = comm.get("template_vars", {})
        attachments = comm.get("attachments", [])
        drive_files = comm.get("drive_files", [])

        # Get template
        template = self._get_template("email", template_id)
        if not template:
            return {"success": False, "error": f"Email template not found: {template_id}"}

        # Apply template variables
        subject = template.get("subject", "")
        body = template.get("body", "")
        html_body = template.get("html_body", "")

        for key, value in template_vars.items():
            subject = subject.replace(f"{{{key}}}", str(value))
            body = body.replace(f"{{{key}}}", str(value))
            if html_body:
                html_body = html_body.replace(f"{{{key}}}", str(value))

        # Process Google Drive files if any
        if drive_files and self.google_drive_service and self.google_drive_service.is_enabled():
            self.logger.info(f"Processing {len(drive_files)} Google Drive files")

            for file_id in drive_files:
                try:
                    # Download file from Google Drive
                    file_info = await self.google_drive_service.get_file_info(file_id)
                    if "error" in file_info:
                        self.logger.warning(f"Error getting file info: {file_info['error']}")
                        continue

                    file_name = file_info.get("name", f"file_{file_id}")
                    download_path = os.path.join("downloads", file_name)

                    download_result = await self.google_drive_service.download_file(file_id, download_path)
                    if "error" in download_result:
                        self.logger.warning(f"Error downloading file: {download_result['error']}")
                        continue

                    # Add to attachments
                    attachments.append(download_path)
                    self.logger.info(f"Added Google Drive file to attachments: {file_name}")

                except Exception as e:
                    self.logger.exception(f"Error processing Google Drive file {file_id}: {e}")

        # Try to send email using Gmail API first
        if self.gmail_service and self.gmail_service.is_enabled():
            try:
                self.logger.info(f"Sending email to {recipient} using Gmail API")

                # Send email with Gmail API
                api_result = await self.gmail_service.send_message(
                    to=recipient,
                    subject=subject,
                    body=body,
                    html_body=html_body,
                    attachments=attachments
                )

                if "error" not in api_result:
                    return {"success": True, "method": "gmail_api", "result": api_result}
                else:
                    self.logger.warning(f"Failed to send email using Gmail API: {api_result['error']}")
                    # Fall back to UI-TARS
            except Exception as e:
                self.logger.exception(f"Error sending email using Gmail API: {e}")
                # Fall back to UI-TARS

        # Fall back to UI-TARS if Gmail API failed or not available
        try:
            self.logger.info(f"Sending email to {recipient} using UI-TARS")

            # Initialize Gmail automation if needed
            if not self.gmail_automation.is_initialized:
                await self.gmail_automation.initialize()

            # Send email with UI-TARS
            # Note: UI-TARS doesn't support attachments directly, so we'll just send the email without attachments
            result = await self.gmail_automation.send_email(
                email_account=self.email_account,
                password=self.email_password,
                to_email=recipient,
                subject=subject,
                body=body
            )

            # If we had attachments but couldn't send them, log a warning
            if attachments:
                self.logger.warning(f"Sent email without {len(attachments)} attachments using UI-TARS")
                result["attachments_sent"] = False

            return result

        except Exception as e:
            self.logger.exception(f"Error sending email using UI-TARS: {e}")
            return {"success": False, "error": str(e)}

    async def _check_campaign_responses(self) -> None:
        """Check for responses to active campaigns."""
        # This would integrate with email and SMS monitoring systems
        # For now, we'll just log a message
        self.logger.debug("Checking for campaign responses")

    def _get_template(self, template_type: str, template_id: str) -> Optional[Dict]:
        """
        Get a template by type and ID.

        Args:
            template_type (str): Template type (call, voicemail, text, email)
            template_id (str): Template identifier

        Returns:
            Optional[Dict]: Template data or None if not found
        """
        templates = self.config.get("templates", {}).get(template_type, {})
        return templates.get(template_id)

    def _is_within_working_hours(self, current_time: datetime) -> bool:
        """
        Check if the current time is within working hours.

        Args:
            current_time (datetime): Current time

        Returns:
            bool: True if within working hours, False otherwise
        """
        settings = self.config.get("settings", {})
        working_hours = settings.get("working_hours", {})

        start_hour = working_hours.get("start_hour", 9)
        end_hour = working_hours.get("end_hour", 18)
        working_days = working_hours.get("days", [1, 2, 3, 4, 5])  # Monday to Friday

        # Check if current day is a working day
        if current_time.weekday() + 1 not in working_days:
            return False

        # Check if current hour is within working hours
        if current_time.hour < start_hour or current_time.hour >= end_hour:
            return False

        return True
