"""
Test script for Facebook integration.

This script tests the Facebook integration for the Insurance Lead Agent.
"""
import os
import sys
import json
import asyncio
import argparse
from typing import Dict, List, Optional, Any
from datetime import datetime
import aiohttp

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.social_media_service import SocialMediaService
from core.logger import setup_logger

# Set up logger
logger = setup_logger("test_facebook_integration")

async def test_facebook_connection(app_id: str, app_secret: str, access_token: str, page_id: str = None):
    """
    Test connection to Facebook API.
    
    Args:
        app_id (str): Facebook App ID
        app_secret (str): Facebook App Secret
        access_token (str): Facebook Access Token
        page_id (str, optional): Facebook Page ID
        
    Returns:
        bool: Success status
    """
    logger.info("Testing Facebook connection...")
    
    # Create configuration
    config = {
        "enabled": True,
        "app_id": app_id,
        "app_secret": app_secret,
        "access_token": access_token,
        "page_id": page_id
    }
    
    # Create social media service
    social_media_service = SocialMediaService("facebook", config)
    
    try:
        # Check Facebook status
        status = await social_media_service.check_status()
        
        if status.get("status") == "available":
            logger.info("Facebook connection successful!")
            
            # Save credentials
            await save_credentials(app_id, app_secret, access_token, page_id)
            
            return True
        else:
            logger.error(f"Facebook connection failed: {status.get('error', 'Unknown error')}")
            return False
    
    except Exception as e:
        logger.exception(f"Error testing Facebook connection: {e}")
        return False

async def get_facebook_pages(access_token: str):
    """
    Get Facebook pages.
    
    Args:
        access_token (str): Facebook Access Token
        
    Returns:
        List[Dict]: List of pages
    """
    logger.info("Getting Facebook pages...")
    
    try:
        async with aiohttp.ClientSession() as session:
            # Get pages
            async with session.get(
                f"https://graph.facebook.com/v18.0/me/accounts?access_token={access_token}"
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    pages = data.get("data", [])
                    
                    logger.info(f"Found {len(pages)} pages")
                    
                    # Print pages
                    for i, page in enumerate(pages):
                        name = page.get("name", "Unknown")
                        page_id = page.get("id", "")
                        page_access_token = page.get("access_token", "")
                        
                        logger.info(f"{i+1}. {name} (ID: {page_id})")
                        logger.info(f"   Access Token: {page_access_token[:10]}...")
                    
                    return pages
                else:
                    error_data = await response.text()
                    logger.error(f"Failed to get pages: {error_data}")
                    return []
    
    except Exception as e:
        logger.exception(f"Error getting Facebook pages: {e}")
        return []

async def get_page_conversations(page_id: str, page_access_token: str):
    """
    Get Facebook page conversations.
    
    Args:
        page_id (str): Facebook Page ID
        page_access_token (str): Facebook Page Access Token
        
    Returns:
        List[Dict]: List of conversations
    """
    logger.info(f"Getting conversations for page {page_id}...")
    
    try:
        async with aiohttp.ClientSession() as session:
            # Get conversations
            async with session.get(
                f"https://graph.facebook.com/v18.0/{page_id}/conversations?access_token={page_access_token}"
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    conversations = data.get("data", [])
                    
                    logger.info(f"Found {len(conversations)} conversations")
                    
                    # Print conversations
                    for i, conversation in enumerate(conversations):
                        conversation_id = conversation.get("id", "")
                        
                        logger.info(f"{i+1}. Conversation ID: {conversation_id}")
                        
                        # Get messages for this conversation
                        await get_conversation_messages(conversation_id, page_access_token)
                    
                    return conversations
                else:
                    error_data = await response.text()
                    logger.error(f"Failed to get conversations: {error_data}")
                    return []
    
    except Exception as e:
        logger.exception(f"Error getting Facebook conversations: {e}")
        return []

async def get_conversation_messages(conversation_id: str, page_access_token: str):
    """
    Get messages for a Facebook conversation.
    
    Args:
        conversation_id (str): Facebook Conversation ID
        page_access_token (str): Facebook Page Access Token
        
    Returns:
        List[Dict]: List of messages
    """
    logger.info(f"Getting messages for conversation {conversation_id}...")
    
    try:
        async with aiohttp.ClientSession() as session:
            # Get messages
            async with session.get(
                f"https://graph.facebook.com/v18.0/{conversation_id}/messages?access_token={page_access_token}"
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    messages = data.get("data", [])
                    
                    logger.info(f"Found {len(messages)} messages")
                    
                    # Print messages
                    for i, message in enumerate(messages[:5]):  # Limit to 5 messages
                        message_id = message.get("id", "")
                        message_text = message.get("message", "")
                        from_id = message.get("from", {}).get("id", "")
                        
                        logger.info(f"{i+1}. Message: {message_text[:50]}...")
                        logger.info(f"   From: {from_id}, ID: {message_id}")
                    
                    return messages
                else:
                    error_data = await response.text()
                    logger.error(f"Failed to get messages: {error_data}")
                    return []
    
    except Exception as e:
        logger.exception(f"Error getting Facebook messages: {e}")
        return []

async def send_test_message(page_id: str, recipient_id: str, page_access_token: str):
    """
    Send a test message.
    
    Args:
        page_id (str): Facebook Page ID
        recipient_id (str): Recipient ID
        page_access_token (str): Facebook Page Access Token
        
    Returns:
        Dict: Response
    """
    logger.info(f"Sending test message to {recipient_id}...")
    
    try:
        async with aiohttp.ClientSession() as session:
            # Send message
            async with session.post(
                f"https://graph.facebook.com/v18.0/{page_id}/messages",
                json={
                    "recipient": {"id": recipient_id},
                    "message": {"text": "This is a test message from the Insurance Lead Agent."}
                },
                params={"access_token": page_access_token}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info("Message sent successfully!")
                    logger.info(f"Response: {data}")
                    return data
                else:
                    error_data = await response.text()
                    logger.error(f"Failed to send message: {error_data}")
                    return {"error": error_data}
    
    except Exception as e:
        logger.exception(f"Error sending Facebook message: {e}")
        return {"error": str(e)}

async def save_credentials(app_id: str, app_secret: str, access_token: str, page_id: str = None):
    """
    Save Facebook credentials to file.
    
    Args:
        app_id (str): Facebook App ID
        app_secret (str): Facebook App Secret
        access_token (str): Facebook Access Token
        page_id (str, optional): Facebook Page ID
    """
    try:
        # Load existing credentials
        credentials_path = "credentials/social_media/facebook.json"
        
        with open(credentials_path, "r") as f:
            credentials = json.load(f)
        
        # Update credentials
        credentials["app_id"] = app_id
        credentials["app_secret"] = app_secret
        credentials["access_token"] = access_token
        
        if page_id:
            credentials["page_id"] = page_id
        
        credentials["last_updated"] = datetime.now().isoformat()
        
        # Save credentials
        with open(credentials_path, "w") as f:
            json.dump(credentials, f, indent=4)
        
        logger.info(f"Saved credentials to {credentials_path}")
    
    except Exception as e:
        logger.exception(f"Error saving credentials: {e}")

async def main():
    """Run the Facebook integration test."""
    parser = argparse.ArgumentParser(description="Facebook Integration Test")
    parser.add_argument("--app-id", type=str, help="Facebook App ID")
    parser.add_argument("--app-secret", type=str, help="Facebook App Secret")
    parser.add_argument("--access-token", type=str, help="Facebook Access Token")
    parser.add_argument("--page-id", type=str, help="Facebook Page ID")
    parser.add_argument("--recipient-id", type=str, help="Recipient ID for test message")
    args = parser.parse_args()
    
    # If credentials are not provided, try to load from file
    app_id = args.app_id
    app_secret = args.app_secret
    access_token = args.access_token
    page_id = args.page_id
    recipient_id = args.recipient_id
    
    if not app_id or not app_secret or not access_token:
        try:
            with open("credentials/social_media/facebook.json", "r") as f:
                credentials = json.load(f)
                app_id = app_id or credentials.get("app_id", "")
                app_secret = app_secret or credentials.get("app_secret", "")
                access_token = access_token or credentials.get("access_token", "")
                page_id = page_id or credentials.get("page_id", "")
        except Exception as e:
            logger.exception(f"Error loading credentials: {e}")
    
    if not app_id or not app_secret or not access_token:
        logger.error("Facebook App ID, App Secret, and Access Token are required")
        print("Please provide Facebook credentials using the --app-id, --app-secret, and --access-token arguments")
        print("You can find these credentials in your Facebook Developer account")
        return
    
    # Test Facebook connection
    connection_success = await test_facebook_connection(app_id, app_secret, access_token, page_id)
    
    if not connection_success:
        return
    
    # Get Facebook pages
    pages = await get_facebook_pages(access_token)
    
    if not pages:
        return
    
    # If page_id is not provided, use the first page
    if not page_id and pages:
        page_id = pages[0].get("id", "")
        page_access_token = pages[0].get("access_token", "")
        
        # Save page ID
        await save_credentials(app_id, app_secret, access_token, page_id)
    else:
        # Find page access token
        page_access_token = None
        for page in pages:
            if page.get("id") == page_id:
                page_access_token = page.get("access_token", "")
                break
    
    if not page_id or not page_access_token:
        logger.error("Facebook Page ID and Page Access Token are required for further tests")
        return
    
    # Get page conversations
    conversations = await get_page_conversations(page_id, page_access_token)
    
    # Send test message if recipient ID is provided
    if recipient_id:
        await send_test_message(page_id, recipient_id, page_access_token)

if __name__ == "__main__":
    asyncio.run(main())
