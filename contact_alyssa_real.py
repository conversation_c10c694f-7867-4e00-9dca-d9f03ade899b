#!/usr/bin/env python3
"""
Contact Alyssa - Real Implementation

This script sends the initial contact messages to <PERSON><PERSON> using
the Gmail browser automation and other services.
"""

import os
import sys
import json
import asyncio
import argparse
from datetime import datetime
from typing import Dict, Optional

# Import required modules
try:
    from gmail_browser_automation import GmailBrowserAutomation
    from credential_store import CredentialStore
except ImportError:
    print("Warning: Gmail browser automation modules not found. Email functionality will be limited.")
    GmailBrowserAutomation = None
    CredentialStore = None

def load_config():
    """Load configuration from the communication_services.json file."""
    try:
        config_path = os.path.join("config", "communication_services.json")
        with open(config_path, "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading configuration: {e}")
        return {}

def format_template(template, vars_dict):
    """Format a template with the given variables."""
    if isinstance(template, dict):
        subject = template.get("subject", "")
        body = template.get("body", "").format(**vars_dict)
        return subject, body
    else:
        return "", template.format(**vars_dict)

class AlyssaContact:
    """Class to handle contacting Alyssa Chirinos."""

    def __init__(self):
        """Initialize the contact handler."""
        self.config = load_config()
        self.gmail_automation = None
        self.credential_store = None

    def initialize(self):
        """Initialize the services."""
        # Initialize credential store if available
        if CredentialStore:
            self.credential_store = CredentialStore()

            # Store default credentials if not already stored
            if not self.credential_store.has_credential("gmail", "<EMAIL>", "password"):
                self.credential_store.store_credential("gmail", "<EMAIL>", "password", "GodisSoGood!777")
                print("Stored default <NAME_EMAIL>")

    def send_email(self, browser_type="chrome"):
        """
        Send email to Alyssa using Gmail browser automation.

        Args:
            browser_type (str): Type of browser to use (chrome, firefox, edge)

        Returns:
            bool: True if successful, False otherwise
        """
        if not GmailBrowserAutomation:
            print("Gmail browser automation not available. Cannot send email.")
            return False

        try:
            # Get email templates
            email_templates = self.config.get("email_integration", {}).get("email_templates", {})

            # Client information
            client_info = {
                "client_name": "Alyssa Chirinos",
                "first_name": "Alyssa",
                "agent_name": "Sandra",
                "phone_number": "(*************",
                "dob": "8/16/97",
                "address": "Bradenton, Florida",
                "insurance_type": "IUL with Dental, Vision, and Basic Health",
                "estimated_premium": "$100/month",
                "notes": "Primary interest is IUL. Also interested in dental, vision, and basic private health coverage for checkups, physicals, and bloodwork. TOTAL BUDGET IS $100/MONTH. Need to check all carriers for best solution within budget."
            }

            # Format initial email
            if "new_client" not in email_templates:
                print("Initial email template not found.")
                return False

            initial_subject, initial_body = format_template(email_templates["new_client"], client_info)

            # Format quote email
            if "new_client_quote" not in email_templates:
                print("Quote email template not found.")
                return False

            quote_subject, quote_body = format_template(email_templates["new_client_quote"], client_info)

            # Initialize Gmail automation
            self.gmail_automation = GmailBrowserAutomation(browser_type=browser_type)

            # Initialize the browser
            if not self.gmail_automation.initialize():
                print("Failed to initialize browser")
                return False

            # Send initial email
            print("Sending initial email...")
            initial_result = self.gmail_automation.send_email(
                email_account="<EMAIL>",
                password="GodisSoGood!777",
                to_email="<EMAIL>",
                subject=initial_subject,
                body=initial_body
            )

            if initial_result["success"]:
                print("Initial email sent successfully!")
            else:
                print(f"Failed to send initial email: {initial_result.get('error', 'Unknown error')}")
                self.gmail_automation.shutdown()
                return False

            # Send quote email
            print("Sending quote email...")
            quote_result = self.gmail_automation.send_email(
                email_account="<EMAIL>",
                password="GodisSoGood!777",
                to_email="<EMAIL>",
                subject=quote_subject,
                body=quote_body
            )

            if quote_result["success"]:
                print("Quote email sent successfully!")
            else:
                print(f"Failed to send quote email: {quote_result.get('error', 'Unknown error')}")
                self.gmail_automation.shutdown()
                return False

            # Close browser
            self.gmail_automation.shutdown()

            return True

        except Exception as e:
            print(f"Error sending email: {e}")
            if self.gmail_automation:
                self.gmail_automation.shutdown()
            return False

    def send_text(self):
        """
        Send text message to Alyssa.

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Get text template
            text_templates = self.config.get("voice_calling_service", {}).get("text_templates", {})

            if "new_client" not in text_templates:
                print("Text template not found.")
                return False

            # Client information
            client_info = {
                "client_name": "Alyssa Chirinos",
                "first_name": "Alyssa",
                "agent_name": "Sandra",
                "phone_number": "(*************"
            }

            # Format message
            message = text_templates["new_client"].format(**client_info)

            # For now, just print the message that would be sent
            print("\nTEXT MESSAGE:")
            print(f"To: 9419294330")
            print(f"From: (*************")
            print(f"Message: {message}")

            # In a real implementation, this would use Twilio or another SMS service
            print("\nNote: Text message would be sent via Twilio in a real implementation.")

            return True

        except Exception as e:
            print(f"Error sending text: {e}")
            return False

    def leave_voicemail(self):
        """
        Leave voicemail for Alyssa.

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Get voicemail template
            voicemail_templates = self.config.get("voice_calling_service", {}).get("voicemail_templates", {})

            if "new_client" not in voicemail_templates:
                print("Voicemail template not found.")
                return False

            # Client information
            client_info = {
                "client_name": "Alyssa Chirinos",
                "first_name": "Alyssa",
                "agent_name": "Sandra",
                "phone_number": "(*************"
            }

            # Format message
            message = voicemail_templates["new_client"].format(**client_info)

            # For now, just print the message that would be sent
            print("\nVOICEMAIL:")
            print(f"To: 9419294330")
            print(f"From: (*************")
            print(f"Voice: ElevenLabs female voice")
            print(f"Message: {message}")

            # In a real implementation, this would use Twilio and ElevenLabs
            print("\nNote: Voicemail would be left via Twilio and ElevenLabs in a real implementation.")

            return True

        except Exception as e:
            print(f"Error leaving voicemail: {e}")
            return False

def main():
    """Main function to run the script."""
    parser = argparse.ArgumentParser(description="Contact Alyssa Chirinos")
    parser.add_argument("--email-only", action="store_true", help="Send only email")
    parser.add_argument("--text-only", action="store_true", help="Send only text message")
    parser.add_argument("--voicemail-only", action="store_true", help="Leave only voicemail")
    parser.add_argument("--browser", default="chrome", choices=["chrome", "firefox", "edge"], help="Browser to use for Gmail automation")

    args = parser.parse_args()

    # Initialize contact handler
    contact = AlyssaContact()
    contact.initialize()

    # Determine what to send
    send_email = not (args.text_only or args.voicemail_only) or args.email_only
    send_text = not (args.email_only or args.voicemail_only) or args.text_only
    leave_voicemail = not (args.email_only or args.text_only) or args.voicemail_only

    # Send messages
    results = {}

    if send_email:
        print("Sending email to Alyssa...")
        results["email"] = contact.send_email(browser_type=args.browser)

    if send_text:
        print("Sending text message to Alyssa...")
        results["text"] = contact.send_text()

    if leave_voicemail:
        print("Leaving voicemail for Alyssa...")
        results["voicemail"] = contact.leave_voicemail()

    # Print summary
    print("\nCONTACT SUMMARY:")
    for method, success in results.items():
        print(f"{method.capitalize()}: {'Success' if success else 'Failed'}")

    # Print follow-up schedule
    print("\nFOLLOW-UP SCHEDULE:")
    today = datetime.now()
    print(f"Day 2 ({(today.day + 2) % 30 or 30}/{today.month}/{today.year}): Send follow-up text message")
    print(f"Day 4 ({(today.day + 4) % 30 or 30}/{today.month}/{today.year}): Make follow-up call")
    print(f"Day 7 ({(today.day + 7) % 30 or 30}/{today.month}/{today.year}): Send follow-up email")
    print(f"Day 14 ({(today.day + 14) % 30 or 30}/{today.month}/{today.year}): Send final check-in text")

if __name__ == "__main__":
    main()
