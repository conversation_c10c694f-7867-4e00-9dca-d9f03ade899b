"""
Script to test email account access.

This script tests access to email accounts using stored credentials.
"""
import sys
import os
import argparse
import getpass
import imaplib
import smtplib
import email
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from pathlib import Path
import json
import logging
import ssl

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent))

from config.secure_credentials import CredentialManager

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger("email_access_test")

# Email provider configurations
EMAIL_PROVIDERS = {
    "gmail": {
        "imap_server": "imap.gmail.com",
        "imap_port": 993,
        "smtp_server": "smtp.gmail.com",
        "smtp_port": 587,
    },
    "outlook": {
        "imap_server": "outlook.office365.com",
        "imap_port": 993,
        "smtp_server": "smtp.office365.com",
        "smtp_port": 587,
    },
    "yahoo": {
        "imap_server": "imap.mail.yahoo.com",
        "imap_port": 993,
        "smtp_server": "smtp.mail.yahoo.com",
        "smtp_port": 587,
    },
}

def test_imap_access(provider, username, password):
    """
    Test IMAP access to an email account.
    
    Args:
        provider (str): Email provider (gmail, outlook, yahoo)
        username (str): Email username
        password (str): Email password
        
    Returns:
        dict: Test results
    """
    logger.info(f"Testing IMAP access for {username} on {provider}")
    
    # Get provider configuration
    provider_config = EMAIL_PROVIDERS.get(provider.lower())
    if not provider_config:
        return {
            "success": False,
            "error": f"Unsupported email provider: {provider}",
        }
    
    try:
        # Connect to IMAP server
        context = ssl.create_default_context()
        mail = imaplib.IMAP4_SSL(
            provider_config["imap_server"],
            provider_config["imap_port"],
            ssl_context=context,
        )
        
        # Login
        mail.login(username, password)
        
        # List mailboxes
        status, mailboxes = mail.list()
        
        # Select inbox
        mail.select("INBOX")
        
        # Search for recent emails
        status, data = mail.search(None, "ALL")
        
        # Get email count
        email_ids = data[0].split()
        email_count = len(email_ids)
        
        # Get most recent email
        recent_email = None
        if email_ids:
            latest_email_id = email_ids[-1]
            status, data = mail.fetch(latest_email_id, "(RFC822)")
            raw_email = data[0][1]
            msg = email.message_from_bytes(raw_email)
            
            recent_email = {
                "subject": msg.get("Subject", ""),
                "from": msg.get("From", ""),
                "date": msg.get("Date", ""),
            }
        
        # Logout
        mail.logout()
        
        return {
            "success": True,
            "mailbox_count": len(mailboxes),
            "email_count": email_count,
            "recent_email": recent_email,
        }
    
    except Exception as e:
        logger.exception(f"Error testing IMAP access: {e}")
        return {
            "success": False,
            "error": str(e),
        }

def test_smtp_access(provider, username, password, test_recipient=None):
    """
    Test SMTP access to an email account.
    
    Args:
        provider (str): Email provider (gmail, outlook, yahoo)
        username (str): Email username
        password (str): Email password
        test_recipient (str): Email address to send test email to
        
    Returns:
        dict: Test results
    """
    logger.info(f"Testing SMTP access for {username} on {provider}")
    
    # Get provider configuration
    provider_config = EMAIL_PROVIDERS.get(provider.lower())
    if not provider_config:
        return {
            "success": False,
            "error": f"Unsupported email provider: {provider}",
        }
    
    # Skip SMTP test if no test recipient
    if not test_recipient:
        return {
            "success": None,
            "error": "No test recipient specified, skipping SMTP test",
        }
    
    try:
        # Connect to SMTP server
        smtp = smtplib.SMTP(
            provider_config["smtp_server"],
            provider_config["smtp_port"],
        )
        
        # Start TLS
        smtp.starttls()
        
        # Login
        smtp.login(username, password)
        
        # Create test message
        msg = MIMEMultipart()
        msg["From"] = username
        msg["To"] = test_recipient
        msg["Subject"] = "Email Access Test"
        
        body = "This is a test email sent by the email_access_test.py script."
        msg.attach(MIMEText(body, "plain"))
        
        # Send message
        smtp.send_message(msg)
        
        # Quit
        smtp.quit()
        
        return {
            "success": True,
            "test_recipient": test_recipient,
        }
    
    except Exception as e:
        logger.exception(f"Error testing SMTP access: {e}")
        return {
            "success": False,
            "error": str(e),
        }

def test_email_access(service, master_password, test_recipient=None):
    """
    Test email account access.
    
    Args:
        service (str): Service name in credential manager
        master_password (str): Master password for credential decryption
        test_recipient (str): Email address to send test email to
    """
    logger.info(f"Testing email access for {service}")
    
    # Load credentials
    credential_manager = CredentialManager(master_password)
    
    # Check if credentials exist for the service
    credential = credential_manager.get_credential(service)
    if not credential:
        logger.error(f"No credentials found for {service}")
        return
    
    # Get username and password
    username = credential["username"]
    password = credential["password"]
    
    # Get provider from additional info or service name
    provider = credential.get("additional_info", {}).get("provider", service)
    
    # Test IMAP access
    imap_result = test_imap_access(provider, username, password)
    
    # Test SMTP access if test recipient provided
    smtp_result = test_smtp_access(provider, username, password, test_recipient)
    
    # Combine results
    result = {
        "service": service,
        "username": username,
        "provider": provider,
        "imap_test": imap_result,
        "smtp_test": smtp_result,
    }
    
    # Save result to file
    result_file = Path(f"results/email_access_{service}.json")
    result_file.parent.mkdir(exist_ok=True)
    
    with open(result_file, "w") as f:
        json.dump(result, f, indent=2)
    
    logger.info(f"Results saved to {result_file}")
    
    # Print summary
    print("\nEmail Access Test Summary:")
    print(f"Service: {service}")
    print(f"Username: {username}")
    print(f"Provider: {provider}")
    
    print("\nIMAP Test:")
    if imap_result["success"]:
        print("  Status: Success")
        print(f"  Mailbox count: {imap_result.get('mailbox_count', 'N/A')}")
        print(f"  Email count: {imap_result.get('email_count', 'N/A')}")
        
        if imap_result.get("recent_email"):
            print("\n  Most recent email:")
            print(f"    Subject: {imap_result['recent_email'].get('subject', 'N/A')}")
            print(f"    From: {imap_result['recent_email'].get('from', 'N/A')}")
            print(f"    Date: {imap_result['recent_email'].get('date', 'N/A')}")
    else:
        print("  Status: Failed")
        print(f"  Error: {imap_result.get('error', 'Unknown error')}")
    
    print("\nSMTP Test:")
    if smtp_result["success"] is None:
        print("  Status: Skipped")
        print(f"  Reason: {smtp_result.get('error', 'Unknown reason')}")
    elif smtp_result["success"]:
        print("  Status: Success")
        print(f"  Test email sent to: {smtp_result.get('test_recipient', 'N/A')}")
    else:
        print("  Status: Failed")
        print(f"  Error: {smtp_result.get('error', 'Unknown error')}")

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Test email account access")
    parser.add_argument("--service", help="Service name in credential manager")
    parser.add_argument("--master-password", help="Master password for credential decryption")
    parser.add_argument("--test-recipient", help="Email address to send test email to")
    args = parser.parse_args()
    
    # Interactive mode if arguments are missing
    service = args.service
    master_password = args.master_password
    test_recipient = args.test_recipient
    
    if not service:
        service = input("Enter service name (as stored in credential manager): ")
    
    if not master_password:
        master_password = getpass.getpass("Enter master password for credential decryption: ")
    
    if not test_recipient:
        test_recipient = input("Enter email address to send test email to (optional, press Enter to skip): ")
        if not test_recipient:
            test_recipient = None
    
    # Run test
    test_email_access(service, master_password, test_recipient)

if __name__ == "__main__":
    main()
