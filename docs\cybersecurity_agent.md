# Cybersecurity Agent

The Cybersecurity Agent is a specialized agent for security testing and vulnerability analysis. It integrates various cybersecurity tools to provide a comprehensive security testing platform.

## Features

### Basic Security Testing

- Network scanning and enumeration
- Vulnerability scanning
- Password auditing
- Web application security testing
- SQL injection testing
- WiFi security assessment
- Security reporting

### AI-Enhanced Security Analysis

- Vulnerability analysis with AI-powered recommendations
- Threat modeling with attack vector identification
- Security recommendations with implementation steps
- Incident response analysis

### Vulnerability Database Integration

- CVE lookup and details
- Vulnerability search
- Recent vulnerability tracking

### Multi-Agent Collaboration (as of May 2025)

- Specialized agents for different security tasks
- Collaborative workflows for comprehensive security assessments
- Agent communication and coordination
- Role-based security analysis

## Supported Tools

The Cybersecurity Agent integrates the following tools:

1. **Nmap** - Network mapper for network discovery and security auditing
2. **John the Ripper** - Password cracker
3. **Wireshark** - Network protocol analyzer
4. **Metasploit Framework** - Penetration testing framework
5. **Burp Suite** - Web vulnerability scanner
6. **Aircrack-ng** - WiFi security assessment tools
7. **SQLMap** - Automatic SQL injection tool
8. **OWASP ZAP** - Web application security scanner
9. **theHarvester** - E-mail, subdomain and name harvester
10. **Nikto** - Web server scanner
11. **PentestGPT** - AI-powered penetration testing tool

## Configuration

The Cybersecurity Agent can be configured in the `.env` file:

```env
ENABLE_CYBERSECURITY_AGENT=True
CYBERSECURITY_AGENT_LLM=anthropic
CYBERSECURITY_AGENT_POLLING_INTERVAL=300

# Tool service settings
ENABLE_TOOL_SERVICE=True
ENABLE_NMAP=True
ENABLE_JOHN=True
ENABLE_WIRESHARK=True
ENABLE_METASPLOIT=True
ENABLE_BURPSUITE=True
ENABLE_AIRCRACK=True
ENABLE_SQLMAP=True
ENABLE_ZAPROXY=True
ENABLE_THEHARVESTER=True
ENABLE_NIKTO=True
ENABLE_PENTESTGPT=True

# Vulnerability database settings
ENABLE_VULNERABILITY_DATABASE=True
NVD_API_KEY=your_nvd_api_key
VULNERABILITY_CACHE_TTL=86400
```

## Usage

### Network Scanning

To perform a network scan:

```python
task = {
    "type": "network_scan",
    "target": "example.com",
    "scan_type": "basic",  # Options: basic, comprehensive, quick
}
```

### Vulnerability Scanning

To perform a vulnerability scan:

```python
task = {
    "type": "vulnerability_scan",
    "target": "example.com",
    "scan_type": "network",  # Options: network, web
}
```

### Web Security Scanning

To perform a web security scan:

```python
task = {
    "type": "web_security_scan",
    "target": "http://example.com",
    "scan_type": "server",  # Options: server, application, sql_injection
}
```

### SQL Injection Testing

To perform an SQL injection test:

```python
task = {
    "type": "sql_injection_test",
    "url": "http://example.com/page.php?id=1",
}
```

### Security Reporting

To generate a security report:

```python
task = {
    "type": "security_report",
    "target": "example.com",
    "report_type": "comprehensive",  # Options: summary, comprehensive
}
```

### AI-Enhanced Vulnerability Analysis

To perform an AI-enhanced vulnerability analysis:

```python
task = {
    "type": "vulnerability_analysis",
    "scan_results": scan_results,  # Results from a previous scan
}
```

### AI-Enhanced Threat Modeling

To perform AI-enhanced threat modeling:

```python
task = {
    "type": "threat_modeling",
    "system_description": "Web application with user authentication and payment processing",
    "scan_results": scan_results,  # Results from a previous scan
}
```

### AI-Enhanced Security Recommendations

To generate AI-enhanced security recommendations:

```python
task = {
    "type": "security_recommendations",
    "security_posture": "Current security measures include a firewall and regular updates",
    "scan_results": scan_results,  # Results from a previous scan
}
```

### AI-Enhanced Incident Analysis

To analyze a security incident:

```python
task = {
    "type": "incident_analysis",
    "incident_details": "Unauthorized access detected to the admin panel at 2:30 AM",
}
```

### CVE Lookup

To look up details for a specific CVE:

```python
task = {
    "type": "cve_lookup",
    "cve_id": "CVE-2021-44228",  # Log4Shell vulnerability
}
```

### Multi-Agent Security Assessment

To perform a comprehensive security assessment using multiple specialized agents:

```python
task = {
    "type": "security_assessment",
    "target": "example.com",
    "options": {
        "scan_type": "comprehensive",
        "timeout": 3600,  # 1 hour timeout
    },
}
```

### Multi-Agent Vulnerability Assessment

To perform a vulnerability assessment using multiple specialized agents:

```python
task = {
    "type": "vulnerability_assessment",
    "target": "example.com",
    "options": {
        "scan_type": "basic",
        "timeout": 1800,  # 30 minutes timeout
    },
}
```

### Collaborative Analysis

To perform a collaborative analysis using multiple specialized agents:

```python
task = {
    "type": "collaborative_analysis",
    "query": "Analyze the security implications of the recent Log4j vulnerability for our web application",
    "data": {
        "application_type": "web",
        "technologies": ["Java", "Spring Boot", "Log4j"],
        "environment": "AWS",
    },
}
```

## Testing

You can test the Cybersecurity Agent using the provided test script:

```bash
python test_cybersecurity_agent.py
```

To test specific functionality:

```bash
python test_cybersecurity_agent.py --test network --target example.com
python test_cybersecurity_agent.py --test web --target http://example.com
python test_cybersecurity_agent.py --test sql --target http://example.com/page.php?id=1
python test_cybersecurity_agent.py --test vuln_db
python test_cybersecurity_agent.py --test ai
python test_cybersecurity_agent.py --test multi_agent
```

## Security Considerations

The Cybersecurity Agent is designed for security testing in controlled environments. Please ensure you have proper authorization before using these tools on any system or network.

## Adding New Tools

To add a new tool to the Cybersecurity Agent:

1. Update the tool registry in `config.py`:

```python
"new_tool": {
    "description": "Description of the new tool",
    "category": "category",
    "install_method": "auto",
    "enabled": os.getenv("ENABLE_NEW_TOOL", "True").lower() == "true",
},
```

1. Add the tool-specific implementation in `agents/cybersecurity_agent_tools.py`:

```python
async def _handle_new_tool_task(agent, task: Dict) -> Dict:
    """
    Handle a new tool task.

    Args:
        agent: Cybersecurity agent instance
        task (Dict): Task data

    Returns:
        Dict: Task results
    """
    # Implementation goes here
    ...
```

1. Update the `_process_task` method in `agents/cybersecurity_agent.py` to handle the new task type:

```python
elif task_type == "new_tool_task":
    result = await _handle_new_tool_task(self, task)
```

1. Add the new capability to the agent's capabilities list in `agents/cybersecurity_agent.py`:

```python
self.capabilities = [
    ...,
    "new_tool_task",
]
```

## Limitations

- Some tools may require root/administrator privileges to run properly
- Tool availability depends on the operating system and environment
- Some tools may not be available for automatic installation and may need to be installed manually
