@echo off
echo Direct Browser Email Automation
echo =============================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Check if Selenium is installed
python -c "import selenium" >nul 2>&1
if %errorlevel% neq 0 (
    echo Selenium is not installed. Installing now...
    pip install selenium
    
    if %errorlevel% neq 0 (
        echo Failed to install Selenium. Please install it manually with: pip install selenium
        exit /b 1
    )
)

REM Ask for browser
echo Choose a browser:
echo 1. Chrome
echo 2. Firefox
echo 3. Edge
echo.
set /p BROWSER_CHOICE="Enter your choice (1-3): "

if "%BROWSER_CHOICE%"=="1" (
    set BROWSER=chrome
) else if "%BROWSER_CHOICE%"=="2" (
    set BROWSER=firefox
) else if "%BROWSER_CHOICE%"=="3" (
    set BROWSER=edge
) else (
    echo Invalid choice. Using Chrome by default.
    set BROWSER=chrome
)

REM Get email details
set FROM_EMAIL=<EMAIL>
set TO_EMAIL=<EMAIL>
set /p SUBJECT="Enter subject (default: Test Email from Direct Browser Automation): "
if "%SUBJECT%"=="" set SUBJECT=Test Email from Direct Browser Automation
set /p BODY="Enter body (default: This is a test email sent using direct browser automation.): "
if "%BODY%"=="" set BODY=This is a test email sent using direct browser automation.

REM Run the Direct Browser Email Automation
echo.
echo Sending email from %FROM_EMAIL% to %TO_EMAIL% using %BROWSER%...
echo.

python direct_browser_email.py --email "%FROM_EMAIL%" --to "%TO_EMAIL%" --subject "%SUBJECT%" --body "%BODY%" --browser "%BROWSER%"

echo.
echo Done.
pause
