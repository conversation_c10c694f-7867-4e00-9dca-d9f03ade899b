"""
Test script for the entire system.
"""
import asyncio
import logging
import signal
import sys
from datetime import datetime
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("test_system")

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent))

# Import necessary modules
from core.state_manager import StateManager
from core.agent_manager import AgentManager
from core.workflow_engine import WorkflowEngine
from llm.llm_router import LLMRouter

# Global flag to control system shutdown
shutdown_event = asyncio.Event()

def handle_shutdown(sig, frame):
    """Handle shutdown signals gracefully."""
    logger.info(f"Received shutdown signal {sig}")
    shutdown_event.set()

async def test_system():
    """Test the entire system."""
    logger.info("Starting system test")
    
    try:
        # Initialize state manager
        state_manager = StateManager()
        await state_manager.initialize()
        logger.info("State manager initialized")
        
        # Initialize agent manager
        agent_manager = AgentManager(state_manager, shutdown_event)
        logger.info("Agent manager initialized")
        
        # Initialize workflow engine
        workflow_engine = WorkflowEngine(agent_manager, state_manager)
        logger.info("Workflow engine initialized")
        
        # Register workflow engine with agent manager
        agent_manager.register_service("workflow_engine", workflow_engine)
        logger.info("Workflow engine registered with agent manager")
        
        # Initialize workflow engine
        await workflow_engine.initialize()
        logger.info("Workflow engine initialized")
        
        # Check if workflows were loaded
        logger.info(f"Loaded workflows: {list(workflow_engine.workflows.keys())}")
        
        # Trigger system startup event
        logger.info("Triggering system startup event")
        await workflow_engine.trigger_event("system_startup", {
            "timestamp": datetime.now().isoformat()
        })
        
        # Wait for a while to let workflows run
        logger.info("Waiting for workflows to run")
        await asyncio.sleep(10)
        
        # Check active workflows
        logger.info(f"Active workflows: {list(workflow_engine.active_workflows.keys())}")
        
        # Trigger a new email event
        logger.info("Triggering new email event")
        await workflow_engine.trigger_event("new_email", {
            "account": "<EMAIL>",
            "email_id": "test_email_123",
            "from": "<EMAIL>",
            "subject": "Test Email",
            "body": "This is a test email"
        })
        
        # Wait for a while to let workflows run
        logger.info("Waiting for workflows to run")
        await asyncio.sleep(10)
        
        # Check active workflows again
        logger.info(f"Active workflows: {list(workflow_engine.active_workflows.keys())}")
        
        # Shutdown workflow engine
        logger.info("Shutting down workflow engine")
        await workflow_engine.shutdown()
        
        # Shutdown agent manager
        logger.info("Shutting down agent manager")
        await agent_manager.stop()
        
        # Close state manager
        logger.info("Closing state manager")
        await state_manager.close()
        
        logger.info("System test completed successfully")
        
    except Exception as e:
        logger.exception(f"Error in system test: {e}")

if __name__ == "__main__":
    # Register signal handlers
    signal.signal(signal.SIGINT, handle_shutdown)
    signal.signal(signal.SIGTERM, handle_shutdown)
    
    # Run the test
    asyncio.run(test_system())
