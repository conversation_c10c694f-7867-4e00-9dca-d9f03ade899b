"""
Browser Automation Manager for the Multi-Agent AI System.

This module provides a manager for browser automation that can use either
UI-TARS or Midscene, with automatic fallback for improved reliability.
"""
import os
import sys
import json
import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Union, Tuple
from enum import Enum

try:
    from core.logger import setup_logger
    from ui_tars.connector.enhanced_ui_tars_connector import EnhancedUI<PERSON>arsConnector
    from ui_tars.connector.enhanced_midscene_connector import EnhancedMidsceneConnector
except ImportError:
    # Fallback logging setup if core.logger is not available
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler("browser_automation_manager.log")
        ]
    )

    def setup_logger(name):
        return logging.getLogger(name)

# Set up logger
logger = setup_logger("browser_automation_manager")

class AutomationProvider(Enum):
    """Enum for automation providers."""
    UI_TARS = "ui_tars"
    MIDSCENE = "midscene"
    AUTO = "auto"

class BrowserAutomationManager:
    """
    Manager for browser automation that can use either UI-TARS or Midscene.
    
    This class provides a unified interface for browser automation, with
    automatic fallback between UI-TARS and Midscene for improved reliability.
    """
    
    def __init__(self,
                 config: Optional[Dict] = None,
                 provider: AutomationProvider = AutomationProvider.AUTO,
                 ui_tars_api_url: Optional[str] = "http://localhost:8080",
                 ui_tars_api_key: Optional[str] = None,
                 midscene_api_url: Optional[str] = "http://localhost:8081",
                 midscene_api_key: Optional[str] = None,
                 model_name: Optional[str] = "UI-TARS-1.5-7B",
                 browser_type: str = "chrome",
                 browser_path: Optional[str] = None,
                 auto_start: bool = True,
                 auto_restart: bool = True,
                 auto_fallback: bool = True):
        """
        Initialize the Browser Automation Manager.
        
        Args:
            config (Optional[Dict]): Configuration dictionary
            provider (AutomationProvider): Preferred automation provider
            ui_tars_api_url (Optional[str]): URL of the UI-TARS API
            ui_tars_api_key (Optional[str]): API key for UI-TARS
            midscene_api_url (Optional[str]): URL of the Midscene API
            midscene_api_key (Optional[str]): API key for Midscene
            model_name (Optional[str]): Name of the model to use
            browser_type (str): Type of browser to use
            browser_path (Optional[str]): Path to browser executable
            auto_start (bool): Whether to automatically start the provider
            auto_restart (bool): Whether to automatically restart the provider on failure
            auto_fallback (bool): Whether to automatically fallback to another provider on failure
        """
        self.config = config or {}
        self.provider = provider
        self.ui_tars_api_url = ui_tars_api_url
        self.ui_tars_api_key = ui_tars_api_key
        self.midscene_api_url = midscene_api_url
        self.midscene_api_key = midscene_api_key
        self.model_name = model_name
        self.browser_type = browser_type
        self.browser_path = browser_path
        self.auto_start = auto_start
        self.auto_restart = auto_restart
        self.auto_fallback = auto_fallback
        
        # Extract configuration from config dictionary if provided
        if self.config:
            ui_tars_config = self.config.get("ui_tars", {})
            midscene_config = self.config.get("midscene", {})
            
            self.ui_tars_api_url = ui_tars_config.get("api_url", self.ui_tars_api_url)
            self.ui_tars_api_key = ui_tars_config.get("api_key", self.ui_tars_api_key)
            self.midscene_api_url = midscene_config.get("api_url", self.midscene_api_url)
            self.midscene_api_key = midscene_config.get("api_key", self.midscene_api_key)
            
            # Use the same model name for both providers if specified in config
            if "model_name" in ui_tars_config:
                self.model_name = ui_tars_config.get("model_name")
            elif "model_name" in midscene_config:
                self.model_name = midscene_config.get("model_name")
            
            # Use the same browser type for both providers if specified in config
            if "browser_type" in ui_tars_config:
                self.browser_type = ui_tars_config.get("browser_type")
            elif "browser_type" in midscene_config:
                self.browser_type = midscene_config.get("browser_type")
        
        # Initialize connectors
        self.ui_tars_connector = None
        self.midscene_connector = None
        self.active_connector = None
        self.active_provider = None
        self.initialized = False
        self.lock = asyncio.Lock()
    
    async def initialize(self):
        """Initialize the Browser Automation Manager."""
        logger.info("Initializing Browser Automation Manager")
        
        async with self.lock:
            if self.initialized:
                logger.info("Browser Automation Manager already initialized")
                return True
            
            # Initialize connectors based on preferred provider
            if self.provider == AutomationProvider.UI_TARS or self.provider == AutomationProvider.AUTO:
                # Initialize UI-TARS connector
                logger.info("Initializing UI-TARS connector")
                self.ui_tars_connector = EnhancedUITarsConnector(
                    api_url=self.ui_tars_api_url,
                    api_key=self.ui_tars_api_key,
                    model_name=self.model_name,
                    browser_type=self.browser_type,
                    browser_path=self.browser_path,
                    auto_start=self.auto_start,
                    auto_restart=self.auto_restart
                )
                
                ui_tars_success = await self.ui_tars_connector.initialize()
                if ui_tars_success:
                    logger.info("UI-TARS connector initialized successfully")
                    self.active_connector = self.ui_tars_connector
                    self.active_provider = AutomationProvider.UI_TARS
                    self.initialized = True
                    return True
                else:
                    logger.warning("Failed to initialize UI-TARS connector")
            
            if self.provider == AutomationProvider.MIDSCENE or (self.provider == AutomationProvider.AUTO and not self.initialized):
                # Initialize Midscene connector
                logger.info("Initializing Midscene connector")
                self.midscene_connector = EnhancedMidsceneConnector(
                    api_url=self.midscene_api_url,
                    api_key=self.midscene_api_key,
                    model_name=self.model_name,
                    browser_type=self.browser_type,
                    browser_path=self.browser_path,
                    auto_start=self.auto_start,
                    auto_restart=self.auto_restart
                )
                
                midscene_success = await self.midscene_connector.initialize()
                if midscene_success:
                    logger.info("Midscene connector initialized successfully")
                    self.active_connector = self.midscene_connector
                    self.active_provider = AutomationProvider.MIDSCENE
                    self.initialized = True
                    return True
                else:
                    logger.warning("Failed to initialize Midscene connector")
            
            logger.error("Failed to initialize any browser automation provider")
            return False
    
    async def execute_command(self, command: str, max_retries: int = 3) -> Dict:
        """
        Execute a command with automatic fallback.
        
        Args:
            command (str): Command to execute
            max_retries (int): Maximum number of retries
            
        Returns:
            Dict: Response from the provider
        """
        if not self.initialized:
            success = await self.initialize()
            if not success:
                return {"error": "Failed to initialize browser automation manager"}
        
        logger.info(f"Executing command: {command}")
        
        # Try with active connector first
        result = await self.active_connector.execute_command(command, max_retries=max_retries)
        
        # Check if command failed and fallback is enabled
        if "error" in result and self.auto_fallback:
            logger.warning(f"Command failed with {self.active_provider.value}, trying fallback")
            
            # Switch to fallback provider
            fallback_provider = AutomationProvider.MIDSCENE if self.active_provider == AutomationProvider.UI_TARS else AutomationProvider.UI_TARS
            fallback_connector = self.midscene_connector if fallback_provider == AutomationProvider.MIDSCENE else self.ui_tars_connector
            
            # Initialize fallback connector if needed
            if fallback_connector is None:
                if fallback_provider == AutomationProvider.UI_TARS:
                    fallback_connector = EnhancedUITarsConnector(
                        api_url=self.ui_tars_api_url,
                        api_key=self.ui_tars_api_key,
                        model_name=self.model_name,
                        browser_type=self.browser_type,
                        browser_path=self.browser_path,
                        auto_start=self.auto_start,
                        auto_restart=self.auto_restart
                    )
                else:
                    fallback_connector = EnhancedMidsceneConnector(
                        api_url=self.midscene_api_url,
                        api_key=self.midscene_api_key,
                        model_name=self.model_name,
                        browser_type=self.browser_type,
                        browser_path=self.browser_path,
                        auto_start=self.auto_start,
                        auto_restart=self.auto_restart
                    )
                
                # Initialize fallback connector
                fallback_success = await fallback_connector.initialize()
                if not fallback_success:
                    logger.error(f"Failed to initialize fallback connector ({fallback_provider.value})")
                    return result  # Return original error
            
            # Try with fallback connector
            logger.info(f"Trying command with fallback provider ({fallback_provider.value})")
            fallback_result = await fallback_connector.execute_command(command, max_retries=max_retries)
            
            # If fallback succeeded, switch to it as the active connector
            if "error" not in fallback_result:
                logger.info(f"Fallback to {fallback_provider.value} succeeded, switching providers")
                self.active_connector = fallback_connector
                self.active_provider = fallback_provider
                return fallback_result
            else:
                logger.warning(f"Fallback to {fallback_provider.value} also failed")
                return result  # Return original error
        
        return result
