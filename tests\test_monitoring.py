"""
Test script for monitoring and analytics.

This script tests the monitoring and analytics functionality for the Insurance Lead Agent.
"""
import os
import sys
import json
import asyncio
import argparse
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import uuid
import random
import matplotlib.pyplot as plt
import numpy as np

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.insurance_lead_agent import InsuranceLeadAgent
from core.state_manager import StateManager
from core.logger import setup_logger
from llm.llm_router import LLMRouter

# Set up logger
logger = setup_logger("test_monitoring")

async def generate_test_data(agent: InsuranceLeadAgent, num_leads: int = 10, days: int = 7):
    """
    Generate test data for monitoring and analytics.
    
    Args:
        agent (InsuranceLeadAgent): Insurance Lead Agent
        num_leads (int): Number of leads to generate per day
        days (int): Number of days to generate data for
        
    Returns:
        Dict: Generated data summary
    """
    logger.info(f"Generating test data: {num_leads} leads per day for {days} days")
    
    channels = ["facebook", "instagram", "tiktok", "website"]
    insurance_types = ["auto", "home", "life", "health", "business", "renters", "umbrella", "flood", "pet"]
    
    # Generate leads for each day
    for day in range(days):
        # Calculate date
        date = datetime.now() - timedelta(days=days-day-1)
        
        logger.info(f"Generating leads for {date.strftime('%Y-%m-%d')}")
        
        # Generate leads
        for i in range(num_leads):
            # Generate random lead data
            channel = random.choice(channels)
            insurance_type = random.choice(insurance_types)
            lead_id = f"{channel}-{uuid.uuid4()}"
            user_handle = f"Test User {i+1}"
            
            # Create lead
            agent.leads[lead_id] = {
                "lead_id": lead_id,
                "channel": channel,
                "user_handle": user_handle,
                "first_contact": (date + timedelta(hours=random.randint(0, 23), minutes=random.randint(0, 59))).isoformat(),
                "last_contact": (date + timedelta(hours=random.randint(0, 23), minutes=random.randint(0, 59))).isoformat(),
                "status": random.choice(["new", "greeted", "qualifying", "qualified", "booked"]),
                "qualified": random.choice([True, False]),
                "insurance_type": insurance_type if random.random() > 0.3 else None,
                "appointment_booked": random.choice([True, False]),
                "appointment_id": f"appointment-{uuid.uuid4()}" if random.random() > 0.7 else None,
                "notes": []
            }
            
            # Generate interactions
            num_interactions = random.randint(1, 5)
            
            for j in range(num_interactions):
                interaction_id = f"{lead_id}-{j+1}"
                direction = random.choice(["incoming", "outgoing"])
                content = f"Test message {j+1} for {insurance_type} insurance"
                status = random.choice(["received", "success", "failure", "escalated"])
                
                # Create interaction
                agent.interactions[interaction_id] = {
                    "interaction_id": interaction_id,
                    "lead_id": lead_id,
                    "channel": channel,
                    "direction": direction,
                    "content": content,
                    "status": status,
                    "timestamp": (date + timedelta(hours=random.randint(0, 23), minutes=random.randint(0, 59))).isoformat()
                }
    
    # Save data
    await agent.state_manager.update_state("insurance_lead", "leads", agent.leads)
    await agent.state_manager.update_state("insurance_lead", "interactions", agent.interactions)
    
    logger.info(f"Generated {len(agent.leads)} leads and {len(agent.interactions)} interactions")
    
    return {
        "leads": len(agent.leads),
        "interactions": len(agent.interactions)
    }

async def analyze_response_times(agent: InsuranceLeadAgent):
    """
    Analyze response times.
    
    Args:
        agent (InsuranceLeadAgent): Insurance Lead Agent
        
    Returns:
        Dict: Response time analysis
    """
    logger.info("Analyzing response times...")
    
    try:
        # Group interactions by lead
        lead_interactions = {}
        
        for interaction_id, interaction in agent.interactions.items():
            lead_id = interaction.get("lead_id")
            
            if lead_id not in lead_interactions:
                lead_interactions[lead_id] = []
            
            lead_interactions[lead_id].append(interaction)
        
        # Calculate response times
        response_times = []
        
        for lead_id, interactions in lead_interactions.items():
            # Sort interactions by timestamp
            interactions.sort(key=lambda x: x.get("timestamp", ""))
            
            # Calculate response times
            for i in range(1, len(interactions)):
                prev_interaction = interactions[i-1]
                curr_interaction = interactions[i]
                
                # Only calculate if previous is incoming and current is outgoing
                if prev_interaction.get("direction") == "incoming" and curr_interaction.get("direction") == "outgoing":
                    prev_time = datetime.fromisoformat(prev_interaction.get("timestamp", ""))
                    curr_time = datetime.fromisoformat(curr_interaction.get("timestamp", ""))
                    
                    # Calculate response time in seconds
                    response_time = (curr_time - prev_time).total_seconds()
                    
                    response_times.append(response_time)
        
        # Calculate statistics
        if response_times:
            avg_response_time = sum(response_times) / len(response_times)
            min_response_time = min(response_times)
            max_response_time = max(response_times)
            median_response_time = sorted(response_times)[len(response_times) // 2]
            
            # Count responses within target time
            target_time = agent.response_time_target
            responses_within_target = sum(1 for t in response_times if t <= target_time)
            percentage_within_target = responses_within_target / len(response_times) * 100
            
            logger.info(f"Average response time: {avg_response_time:.2f} seconds")
            logger.info(f"Median response time: {median_response_time:.2f} seconds")
            logger.info(f"Min response time: {min_response_time:.2f} seconds")
            logger.info(f"Max response time: {max_response_time:.2f} seconds")
            logger.info(f"Responses within target time ({target_time} seconds): {percentage_within_target:.2f}%")
            
            # Create histogram
            plt.figure(figsize=(10, 6))
            plt.hist(response_times, bins=20, alpha=0.7, color='blue')
            plt.axvline(x=target_time, color='red', linestyle='--', label=f'Target Time ({target_time} seconds)')
            plt.axvline(x=avg_response_time, color='green', linestyle='-', label=f'Average ({avg_response_time:.2f} seconds)')
            plt.xlabel('Response Time (seconds)')
            plt.ylabel('Frequency')
            plt.title('Response Time Distribution')
            plt.legend()
            plt.grid(True, alpha=0.3)
            
            # Save plot
            os.makedirs("reports", exist_ok=True)
            plt.savefig("reports/response_time_distribution.png")
            logger.info("Response time distribution saved to reports/response_time_distribution.png")
            
            return {
                "average": avg_response_time,
                "median": median_response_time,
                "min": min_response_time,
                "max": max_response_time,
                "within_target": percentage_within_target,
                "count": len(response_times)
            }
        else:
            logger.warning("No response times found")
            return {}
    
    except Exception as e:
        logger.exception(f"Error analyzing response times: {e}")
        return {}

async def analyze_channel_performance(agent: InsuranceLeadAgent):
    """
    Analyze channel performance.
    
    Args:
        agent (InsuranceLeadAgent): Insurance Lead Agent
        
    Returns:
        Dict: Channel performance analysis
    """
    logger.info("Analyzing channel performance...")
    
    try:
        # Count leads by channel
        channel_counts = {}
        
        for lead_id, lead in agent.leads.items():
            channel = lead.get("channel")
            
            if channel not in channel_counts:
                channel_counts[channel] = {
                    "total": 0,
                    "qualified": 0,
                    "booked": 0
                }
            
            channel_counts[channel]["total"] += 1
            
            if lead.get("qualified"):
                channel_counts[channel]["qualified"] += 1
            
            if lead.get("appointment_booked"):
                channel_counts[channel]["booked"] += 1
        
        # Calculate conversion rates
        for channel, counts in channel_counts.items():
            if counts["total"] > 0:
                counts["qualification_rate"] = counts["qualified"] / counts["total"] * 100
                counts["booking_rate"] = counts["booked"] / counts["total"] * 100
                
                if counts["qualified"] > 0:
                    counts["qualified_booking_rate"] = counts["booked"] / counts["qualified"] * 100
                else:
                    counts["qualified_booking_rate"] = 0
        
        # Print results
        for channel, counts in channel_counts.items():
            logger.info(f"Channel: {channel}")
            logger.info(f"  Total leads: {counts['total']}")
            logger.info(f"  Qualified leads: {counts['qualified']} ({counts.get('qualification_rate', 0):.2f}%)")
            logger.info(f"  Booked appointments: {counts['booked']} ({counts.get('booking_rate', 0):.2f}%)")
            logger.info(f"  Qualified booking rate: {counts.get('qualified_booking_rate', 0):.2f}%")
        
        # Create bar chart
        channels = list(channel_counts.keys())
        qualification_rates = [counts.get("qualification_rate", 0) for counts in channel_counts.values()]
        booking_rates = [counts.get("booking_rate", 0) for counts in channel_counts.values()]
        
        x = np.arange(len(channels))
        width = 0.35
        
        fig, ax = plt.subplots(figsize=(10, 6))
        rects1 = ax.bar(x - width/2, qualification_rates, width, label='Qualification Rate')
        rects2 = ax.bar(x + width/2, booking_rates, width, label='Booking Rate')
        
        ax.set_xlabel('Channel')
        ax.set_ylabel('Rate (%)')
        ax.set_title('Channel Performance')
        ax.set_xticks(x)
        ax.set_xticklabels(channels)
        ax.legend()
        
        ax.bar_label(rects1, fmt='%.1f%%')
        ax.bar_label(rects2, fmt='%.1f%%')
        
        fig.tight_layout()
        
        # Save plot
        os.makedirs("reports", exist_ok=True)
        plt.savefig("reports/channel_performance.png")
        logger.info("Channel performance chart saved to reports/channel_performance.png")
        
        return channel_counts
    
    except Exception as e:
        logger.exception(f"Error analyzing channel performance: {e}")
        return {}

async def analyze_insurance_types(agent: InsuranceLeadAgent):
    """
    Analyze insurance types.
    
    Args:
        agent (InsuranceLeadAgent): Insurance Lead Agent
        
    Returns:
        Dict: Insurance type analysis
    """
    logger.info("Analyzing insurance types...")
    
    try:
        # Count leads by insurance type
        type_counts = {}
        
        for lead_id, lead in agent.leads.items():
            insurance_type = lead.get("insurance_type")
            
            if not insurance_type:
                insurance_type = "unknown"
            
            if insurance_type not in type_counts:
                type_counts[insurance_type] = {
                    "total": 0,
                    "booked": 0
                }
            
            type_counts[insurance_type]["total"] += 1
            
            if lead.get("appointment_booked"):
                type_counts[insurance_type]["booked"] += 1
        
        # Calculate booking rates
        for insurance_type, counts in type_counts.items():
            if counts["total"] > 0:
                counts["booking_rate"] = counts["booked"] / counts["total"] * 100
        
        # Print results
        for insurance_type, counts in type_counts.items():
            logger.info(f"Insurance type: {insurance_type}")
            logger.info(f"  Total leads: {counts['total']}")
            logger.info(f"  Booked appointments: {counts['booked']} ({counts.get('booking_rate', 0):.2f}%)")
        
        # Create pie chart
        labels = list(type_counts.keys())
        sizes = [counts["total"] for counts in type_counts.values()]
        
        fig, ax = plt.subplots(figsize=(10, 6))
        ax.pie(sizes, labels=labels, autopct='%1.1f%%', startangle=90)
        ax.axis('equal')
        plt.title('Insurance Type Distribution')
        
        # Save plot
        os.makedirs("reports", exist_ok=True)
        plt.savefig("reports/insurance_type_distribution.png")
        logger.info("Insurance type distribution saved to reports/insurance_type_distribution.png")
        
        return type_counts
    
    except Exception as e:
        logger.exception(f"Error analyzing insurance types: {e}")
        return {}

async def main():
    """Run the monitoring and analytics test."""
    parser = argparse.ArgumentParser(description="Monitoring and Analytics Test")
    parser.add_argument("--generate-data", action="store_true", help="Generate test data")
    parser.add_argument("--num-leads", type=int, default=10, help="Number of leads to generate per day")
    parser.add_argument("--days", type=int, default=7, help="Number of days to generate data for")
    args = parser.parse_args()
    
    # Create state manager
    state_manager = StateManager(use_database=False)
    await state_manager.initialize()
    
    # Create LLM router
    llm_router = LLMRouter()
    await llm_router.initialize()
    
    # Create message queue and shutdown event
    message_queue = asyncio.Queue()
    shutdown_event = asyncio.Event()
    
    # Load lead agent configuration
    try:
        with open("config/lead_agent_config.json", "r") as f:
            lead_config = json.load(f)
    except Exception as e:
        logger.exception(f"Error loading lead agent configuration: {e}")
        return
    
    # Create agent configuration
    agent_config = {
        "name": "Insurance Lead Agent",
        "description": "Handles leads from multiple channels",
        "llm_provider": "anthropic",
        "lead_agent_config": lead_config
    }
    
    # Create and initialize the agent
    agent = InsuranceLeadAgent(
        agent_id="insurance_lead_agent_monitoring",
        config=agent_config,
        state_manager=state_manager,
        message_queue=message_queue,
        shutdown_event=shutdown_event
    )
    
    # Set LLM router
    agent.llm_router = llm_router
    
    # Initialize the agent
    await agent.initialize()
    
    logger.info("Insurance Lead Agent initialized")
    
    # Generate test data if requested
    if args.generate_data:
        await generate_test_data(agent, args.num_leads, args.days)
    
    # Analyze response times
    await analyze_response_times(agent)
    
    # Analyze channel performance
    await analyze_channel_performance(agent)
    
    # Analyze insurance types
    await analyze_insurance_types(agent)
    
    # Clean up
    await state_manager.close()
    
    logger.info("Test complete")

if __name__ == "__main__":
    asyncio.run(main())
