"""
Simple test script for the Insurance Lead Agent.
"""
import os
import sys
import json
import asyncio
from datetime import datetime
from pathlib import Path

# Create necessary directories
os.makedirs("credentials/social_media", exist_ok=True)
os.makedirs("credentials/calendly", exist_ok=True)
os.makedirs("credentials/communication", exist_ok=True)
os.makedirs("data", exist_ok=True)
os.makedirs("reports", exist_ok=True)

# Create a simple lead
lead = {
    "lead_id": "test-123",
    "channel": "website",
    "user_handle": "<PERSON>",
    "email": "<EMAIL>",
    "phone": "************",
    "message": "I'm interested in auto insurance",
    "first_contact": datetime.now().isoformat(),
    "last_contact": datetime.now().isoformat(),
    "status": "new",
    "qualified": False,
    "insurance_type": "auto",
    "appointment_booked": False,
    "appointment_id": None,
    "notes": []
}

# Create a simple interaction
interaction = {
    "interaction_id": "interaction-123",
    "lead_id": "test-123",
    "channel": "website",
    "direction": "incoming",
    "content": "I'm interested in auto insurance",
    "status": "received",
    "timestamp": datetime.now().isoformat()
}

# Create a simple response
response = {
    "interaction_id": "interaction-124",
    "lead_id": "test-123",
    "channel": "website",
    "direction": "outgoing",
    "content": "Hi John, thanks for reaching out! Can I ask what kind of insurance you're looking for?",
    "status": "success",
    "timestamp": (datetime.now()).isoformat()
}

# Save data to files
with open("data/leads.json", "w") as f:
    json.dump({"test-123": lead}, f, indent=2)

with open("data/interactions.json", "w") as f:
    json.dump({
        "interaction-123": interaction,
        "interaction-124": response
    }, f, indent=2)

# Create a simple report
print("Insurance Lead Agent Test")
print("========================")
print(f"Lead: {lead['user_handle']} ({lead['lead_id']})")
print(f"Channel: {lead['channel']}")
print(f"Insurance Type: {lead['insurance_type']}")
print(f"Status: {lead['status']}")
print(f"Qualified: {lead['qualified']}")
print(f"Appointment Booked: {lead['appointment_booked']}")
print("\nInteractions:")
print(f"1. [{interaction['timestamp']}] {interaction['direction']}: {interaction['content']}")
print(f"2. [{response['timestamp']}] {response['direction']}: {response['content']}")
print("\nTest completed successfully!")
