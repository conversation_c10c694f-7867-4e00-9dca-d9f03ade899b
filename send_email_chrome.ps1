# PowerShell script to send an email to <PERSON><PERSON>. using Chrome automation
# This script properly uses Chrome for browser automation instead of Internet Explorer

# Email content
$recipientEmail = "<EMAIL>" # Replace with actual email
$emailSubject = "URGENT: Your Insurance Options - Coverage Available Within Your $100 Monthly Budget"
$emailBody = @"
Hi Alyssa,

I hope this message finds you well. We've been trying to reach you through multiple channels (email, phone calls, voicemails, and texts) regarding your insurance needs, and I wanted to follow up personally as this is time-sensitive.

Based on your specific situation and $100 monthly budget, we have options ready for you that provide excellent coverage:

For your IUL policy (approximately $65/month):
- Cash value growth potential tied to market performance without the downside risk
- Death benefit protection for your loved ones
- Tax-free access to your cash value for future needs
- Living benefits that allow access to your death benefit if you become critically ill

For your health/dental/vision package (approximately $35/month):
- Comprehensive health coverage with our top-tier carriers that offer exceptional benefits
- Dental coverage including preventive care, basic procedures, and major work
- Vision benefits covering exams, frames, and contacts

What makes us the best agency to handle your insurance needs:
1. Our carriers offer some of the most comprehensive health benefits in the industry, with lower deductibles and better coverage than you'll find elsewhere
2. We have flexible IUL, whole life, and term policy options that can be customized to your exact needs
3. Our mortgage protection extends for the entire life of your loan, unlike competitors who offer limited coverage periods
4. For qualified applicants like yourself, we can secure over $1 million in coverage

We need to speak with you as soon as possible to secure this coverage before rates change. I have the following time slots available tomorrow (Monday):
- 10:00 AM - 10:30 AM
- 1:00 PM - 1:30 PM
- 4:00 PM - 4:30 PM

Or Tuesday:
- 9:00 AM - 9:30 AM
- 2:00 PM - 2:30 PM

Please let me know which time works best for you, or you can schedule directly through our Calendly link:
https://calendly.com/flofaction/insurance-consultation

It's critical that we connect in the next 24-48 hours to ensure we can lock in these rates for you.

Looking forward to speaking with you soon,

Paul Edwards
Flo Faction Insurance
(772) 208-9646
"@

# Function to log messages with timestamps
function Write-Log {
    param (
        [Parameter(Mandatory=$true)]
        [string]$Message,
        
        [Parameter(Mandatory=$false)]
        [ValidateSet("INFO", "WARNING", "ERROR", "SUCCESS")]
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "$timestamp - $Level - $Message"
    
    # Output to console with color based on level
    switch ($Level) {
        "INFO" { Write-Host $logMessage -ForegroundColor Cyan }
        "WARNING" { Write-Host $logMessage -ForegroundColor Yellow }
        "ERROR" { Write-Host $logMessage -ForegroundColor Red }
        "SUCCESS" { Write-Host $logMessage -ForegroundColor Green }
    }
}

# Function to wait for a specified number of seconds
function Wait-Seconds {
    param (
        [int]$Seconds
    )
    Start-Sleep -Seconds $Seconds
}

# Function to send an email using Chrome automation
function Send-EmailWithChrome {
    try {
        Write-Log "Starting email automation to Alyssa C. using Chrome..." -Level "INFO"
        
        # Check if Chrome is running
        $chromeProcess = Get-Process -Name "chrome" -ErrorAction SilentlyContinue
        if (-not $chromeProcess) {
            Write-Log "Chrome is not running. Starting Chrome..." -Level "INFO"
            Start-Process "chrome.exe"
            Wait-Seconds 3
        }
        
        # Create a new Chrome window for Gmail
        Write-Log "Opening Gmail in Chrome..." -Level "INFO"
        Start-Process "chrome.exe" -ArgumentList "https://mail.google.com/mail/u/0/#inbox?compose=new"
        Wait-Seconds 5
        
        # Create automation object
        $wshell = New-Object -ComObject WScript.Shell
        
        # Activate Chrome window
        $wshell.AppActivate("Gmail")
        Wait-Seconds 2
        
        # Check if we need to log in
        $title = $wshell.AppActivate("Sign in")
        if ($title) {
            Write-Log "Login page detected, please log in manually" -Level "WARNING"
            
            # Wait for manual login
            Write-Host "Please log in to Gmail manually and press Enter when done..." -ForegroundColor Yellow
            Read-Host
            
            # Navigate to compose again after login
            Start-Process "chrome.exe" -ArgumentList "https://mail.google.com/mail/u/0/#inbox?compose=new"
            Wait-Seconds 5
            $wshell.AppActivate("Gmail")
            Wait-Seconds 2
        }
        
        # Wait for compose form to load
        Write-Log "Waiting for Gmail compose form to load..." -Level "INFO"
        Wait-Seconds 5
        
        # Fill in recipient
        Write-Log "Entering recipient: $recipientEmail" -Level "INFO"
        $wshell.SendKeys("$recipientEmail")
        Wait-Seconds 1
        $wshell.SendKeys("{TAB}")
        Wait-Seconds 1
        
        # Fill in subject
        Write-Log "Entering subject: $emailSubject" -Level "INFO"
        $wshell.SendKeys("$emailSubject")
        Wait-Seconds 1
        $wshell.SendKeys("{TAB}")
        Wait-Seconds 1
        
        # Fill in email body
        Write-Log "Entering email body..." -Level "INFO"
        $emailBodyLines = $emailBody -split "`n"
        foreach ($line in $emailBodyLines) {
            $wshell.SendKeys("$line")
            $wshell.SendKeys("{ENTER}")
            Wait-Seconds 0.1
        }
        Wait-Seconds 2
        
        # Send the email
        Write-Log "Sending email..." -Level "INFO"
        $wshell.SendKeys("^{ENTER}")  # Ctrl+Enter to send
        Wait-Seconds 3
        
        # Create a confirmation file
        $confirmationContent = @"
EMAIL SENT CONFIRMATION
------------------------
Date: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
From: Paul Edwards - Flo Faction Insurance <<EMAIL>>
To: $recipientEmail
Subject: $emailSubject
Status: SENT SUCCESSFULLY
Method: Chrome Browser Automation
"@
        
        $confirmationFile = "email_confirmation_chrome.txt"
        $confirmationContent | Out-File -FilePath $confirmationFile -Encoding utf8
        
        Write-Log "Email sent successfully to $recipientEmail!" -Level "SUCCESS"
        Write-Log "Confirmation saved to: $confirmationFile" -Level "SUCCESS"
        
        return $true
    } catch {
        Write-Log "Error sending email: $_" -Level "ERROR"
        return $false
    }
}

# Main execution
$success = Send-EmailWithChrome

if ($success) {
    Write-Log "Email automation completed successfully!" -Level "SUCCESS"
    exit 0
} else {
    Write-Log "Email automation failed!" -Level "ERROR"
    exit 1
}
