"""
Code Evaluator for AlphaEvolve.

This module provides code evaluation capabilities for AlphaEvolve,
enabling the assessment of code quality, correctness, and performance.
"""
import asyncio
import json
import logging
import os
import tempfile
import time
import subprocess
import ast
import re
import platform

# Check if resource module is available (Unix-like systems)
try:
    import resource
    RESOURCE_MODULE_AVAILABLE = True
except ImportError:
    RESOURCE_MODULE_AVAILABLE = False
from typing import Dict, List, Optional, Any, Union, Tuple
import uuid
from datetime import datetime
import traceback
import sys
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

from core.logger import setup_logger

# Set up logger
logger = setup_logger("code_evaluator")

class CodeEvaluator:
    """
    Code Evaluator for AlphaEvolve.

    This class provides code evaluation capabilities for AlphaEvolve,
    enabling the assessment of code quality, correctness, and performance.
    """

    def __init__(self, config: Dict = None):
        """
        Initialize the Code Evaluator.

        Args:
            config (Dict, optional): Configuration
        """
        self.config = config or {}
        self.initialized = False

        # Evaluation parameters
        self.timeout = self.config.get("timeout", 10)
        self.max_memory = self.config.get("max_memory", "1GB")

        # Convert max_memory to bytes
        if isinstance(self.max_memory, str):
            if self.max_memory.endswith("GB"):
                self.max_memory_bytes = int(float(self.max_memory[:-2]) * 1024 * 1024 * 1024)
            elif self.max_memory.endswith("MB"):
                self.max_memory_bytes = int(float(self.max_memory[:-2]) * 1024 * 1024)
            elif self.max_memory.endswith("KB"):
                self.max_memory_bytes = int(float(self.max_memory[:-2]) * 1024)
            else:
                self.max_memory_bytes = int(self.max_memory)
        else:
            self.max_memory_bytes = int(self.max_memory)

        # Evaluation metrics
        self.metrics = self.config.get("metrics", ["correctness", "efficiency", "complexity"])

        # Evaluation history
        self.evaluation_history = []

    async def initialize(self):
        """Initialize the Code Evaluator."""
        logger.info("Initializing Code Evaluator")

        # Create temporary directory for code evaluation
        self.temp_dir = tempfile.mkdtemp(prefix="alpha_evolve_")
        logger.info(f"Created temporary directory: {self.temp_dir}")

        self.initialized = True
        logger.info("Code Evaluator initialized")

    async def evaluate_code(self, code: str, problem: Dict) -> float:
        """
        Evaluate code quality, correctness, and performance.

        Args:
            code (str): Code to evaluate
            problem (Dict): Problem definition

        Returns:
            float: Fitness score (0.0 to 1.0)
        """
        if not self.initialized:
            await self.initialize()

        evaluation_id = str(uuid.uuid4())

        try:
            # Parse code to check for syntax errors
            try:
                ast.parse(code)
                syntax_valid = True
            except SyntaxError:
                syntax_valid = False
                logger.warning(f"Syntax error in code: {traceback.format_exc()}")

            if not syntax_valid:
                return 0.0

            # Prepare evaluation
            problem_type = problem.get("type", "unknown")

            # Select evaluation method based on problem type
            if problem_type == "optimization":
                fitness = await self._evaluate_optimization(code, problem)
            elif problem_type == "agent_enhancement":
                fitness = await self._evaluate_agent_enhancement(code, problem)
            else:
                # Default evaluation
                fitness = await self._evaluate_generic(code, problem)

            # Record evaluation
            self._record_evaluation(evaluation_id, code, problem, fitness)

            return fitness

        except Exception as e:
            logger.exception(f"Error evaluating code: {e}")

            # Record failed evaluation
            self._record_evaluation(
                evaluation_id, code, problem, 0.0,
                error=str(e), traceback=traceback.format_exc()
            )

            return 0.0

    async def _evaluate_optimization(self, code: str, problem: Dict) -> float:
        """
        Evaluate code for optimization problems.

        Args:
            code (str): Code to evaluate
            problem (Dict): Problem definition

        Returns:
            float: Fitness score (0.0 to 1.0)
        """
        # Extract test cases from problem
        test_cases = problem.get("test_cases", [])

        if not test_cases:
            # Generate test cases if not provided
            test_cases = self._generate_test_cases(problem)

        # Prepare test harness
        test_file_path = os.path.join(self.temp_dir, f"test_{uuid.uuid4()}.py")

        with open(test_file_path, "w") as f:
            f.write(self._generate_test_harness(code, test_cases))

        # Run tests
        correctness, execution_times = await self._run_tests(test_file_path)

        if not correctness:
            return 0.0

        # Calculate efficiency score
        if execution_times:
            efficiency = 1.0 / (1.0 + sum(execution_times) / len(execution_times))
        else:
            efficiency = 0.0

        # Calculate complexity score
        complexity_score = self._calculate_complexity_score(code)

        # Calculate overall fitness
        fitness = 0.6 * correctness + 0.3 * efficiency + 0.1 * complexity_score

        return fitness

    async def _evaluate_agent_enhancement(self, code: str, problem: Dict) -> float:
        """
        Evaluate code for agent enhancement problems.

        Args:
            code (str): Code to evaluate
            problem (Dict): Problem definition

        Returns:
            float: Fitness score (0.0 to 1.0)
        """
        # Extract agent information
        agent_id = problem.get("agent_id")
        capability = problem.get("capability")
        optimization_metric = problem.get("optimization_metric")

        if not agent_id or not capability:
            logger.warning("Missing agent_id or capability in problem definition")
            return 0.0

        # Check if code is syntactically valid for the agent
        agent_compatibility = self._check_agent_compatibility(code, agent_id, capability)

        if not agent_compatibility:
            return 0.0

        # Evaluate code quality
        complexity_score = self._calculate_complexity_score(code)

        # Evaluate capability implementation
        capability_score = self._evaluate_capability_implementation(code, capability)

        # Evaluate optimization metric
        optimization_score = self._evaluate_optimization_metric(code, optimization_metric)

        # Calculate overall fitness
        fitness = 0.3 * agent_compatibility + 0.2 * complexity_score + 0.3 * capability_score + 0.2 * optimization_score

        return fitness

    async def _evaluate_generic(self, code: str, problem: Dict) -> float:
        """
        Evaluate code for generic problems.

        Args:
            code (str): Code to evaluate
            problem (Dict): Problem definition

        Returns:
            float: Fitness score (0.0 to 1.0)
        """
        # Check syntax
        try:
            ast.parse(code)
            syntax_score = 1.0
        except SyntaxError:
            syntax_score = 0.0

        if syntax_score == 0.0:
            return 0.0

        # Calculate complexity score
        complexity_score = self._calculate_complexity_score(code)

        # Check if code matches problem description
        relevance_score = self._calculate_relevance_score(code, problem)

        # Calculate overall fitness
        fitness = 0.4 * syntax_score + 0.3 * complexity_score + 0.3 * relevance_score

        return fitness

    def _generate_test_cases(self, problem: Dict) -> List[Dict]:
        """
        Generate test cases for a problem.

        Args:
            problem (Dict): Problem definition

        Returns:
            List[Dict]: Generated test cases
        """
        # Simple test case generation
        test_cases = []

        # Add basic test cases
        test_cases.append({
            "input": {"data": [1, 2, 3, 4, 5]},
            "expected_output": None,  # Will be ignored in generic testing
        })

        test_cases.append({
            "input": {"data": []},
            "expected_output": None,
        })

        test_cases.append({
            "input": {"data": [10, 20, 30]},
            "expected_output": None,
        })

        return test_cases

    def _generate_test_harness(self, code: str, test_cases: List[Dict]) -> str:
        """
        Generate test harness for code evaluation.

        Args:
            code (str): Code to evaluate
            test_cases (List[Dict]): Test cases

        Returns:
            str: Test harness code
        """
        test_harness = f"""
import time
import json
import traceback
import sys

# Code to evaluate
{code}

# Test cases
test_cases = {json.dumps(test_cases, indent=2)}

def run_tests():
    results = []

    for i, test_case in enumerate(test_cases):
        try:
            # Prepare input
            input_data = test_case["input"]

            # Measure execution time
            start_time = time.time()

            # Execute code
            if "solve" in globals():
                output = solve(**input_data)
            elif "optimize" in globals():
                output = optimize(**input_data)
            elif "enhance" in globals():
                output = enhance(**input_data)
            else:
                # Try to find a suitable function
                for func_name in globals():
                    if func_name.startswith("_"):
                        continue
                    if callable(globals()[func_name]):
                        output = globals()[func_name](**input_data)
                        break
                else:
                    raise ValueError("No suitable function found")

            # Calculate execution time
            execution_time = time.time() - start_time

            # Check expected output if provided
            expected_output = test_case.get("expected_output")
            if expected_output is not None:
                correct = output == expected_output
            else:
                # If no expected output, just check if execution succeeded
                correct = True

            results.append({
                "test_case": i,
                "correct": correct,
                "execution_time": execution_time,
                "error": None
            })

        except Exception as e:
            results.append({
                "test_case": i,
                "correct": False,
                "execution_time": None,
                "error": str(e),
                "traceback": traceback.format_exc()
            })

    return results

if __name__ == "__main__":
    results = run_tests()
    print(json.dumps(results))
"""

        return test_harness

    async def _run_tests(self, test_file_path: str) -> Tuple[float, List[float]]:
        """
        Run tests and collect results.

        Args:
            test_file_path (str): Path to test file

        Returns:
            Tuple[float, List[float]]: Correctness score and execution times
        """
        try:
            # Run test harness with resource limits
            process = await asyncio.create_subprocess_exec(
                sys.executable, test_file_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                limit=self.max_memory_bytes,
            )

            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(), timeout=self.timeout
                )
            except asyncio.TimeoutError:
                process.kill()
                logger.warning(f"Test execution timed out after {self.timeout} seconds")
                return 0.0, []

            if process.returncode != 0:
                logger.warning(f"Test execution failed with return code {process.returncode}")
                logger.debug(f"stderr: {stderr.decode()}")
                return 0.0, []

            # Parse results
            try:
                results = json.loads(stdout.decode())
            except json.JSONDecodeError:
                logger.warning("Failed to parse test results")
                return 0.0, []

            # Calculate correctness score
            correct_count = sum(1 for r in results if r.get("correct", False))
            correctness = correct_count / len(results) if results else 0.0

            # Collect execution times
            execution_times = [r.get("execution_time") for r in results if r.get("execution_time") is not None]

            return correctness, execution_times

        except Exception as e:
            logger.exception(f"Error running tests: {e}")
            return 0.0, []

        finally:
            # Clean up test file
            try:
                os.remove(test_file_path)
            except Exception:
                pass

    def _calculate_complexity_score(self, code: str) -> float:
        """
        Calculate code complexity score.

        Args:
            code (str): Code to evaluate

        Returns:
            float: Complexity score (0.0 to 1.0, higher is better/less complex)
        """
        try:
            # Parse code
            tree = ast.parse(code)

            # Count various complexity indicators
            class ComplexityVisitor(ast.NodeVisitor):
                def __init__(self):
                    self.line_count = 0
                    self.function_count = 0
                    self.class_count = 0
                    self.loop_count = 0
                    self.branch_count = 0
                    self.import_count = 0

                def visit_FunctionDef(self, node):
                    self.function_count += 1
                    self.generic_visit(node)

                def visit_ClassDef(self, node):
                    self.class_count += 1
                    self.generic_visit(node)

                def visit_For(self, node):
                    self.loop_count += 1
                    self.generic_visit(node)

                def visit_While(self, node):
                    self.loop_count += 1
                    self.generic_visit(node)

                def visit_If(self, node):
                    self.branch_count += 1
                    self.generic_visit(node)

                def visit_Import(self, node):
                    self.import_count += 1
                    self.generic_visit(node)

                def visit_ImportFrom(self, node):
                    self.import_count += 1
                    self.generic_visit(node)

            visitor = ComplexityVisitor()
            visitor.visit(tree)

            # Count lines of code (excluding blank lines and comments)
            lines = [line for line in code.split("\n") if line.strip() and not line.strip().startswith("#")]
            visitor.line_count = len(lines)

            # Calculate cyclomatic complexity (simplified)
            cyclomatic_complexity = 1 + visitor.branch_count + visitor.loop_count

            # Calculate normalized complexity score
            # Lower complexity is better, so we invert the score
            complexity = visitor.line_count * 0.01 + cyclomatic_complexity * 0.1
            normalized_score = 1.0 / (1.0 + complexity)

            return normalized_score

        except Exception as e:
            logger.error(f"Error calculating complexity score: {e}")
            return 0.5  # Default to middle score on error

    def _calculate_relevance_score(self, code: str, problem: Dict) -> float:
        """
        Calculate how relevant the code is to the problem.

        Args:
            code (str): Code to evaluate
            problem (Dict): Problem definition

        Returns:
            float: Relevance score (0.0 to 1.0)
        """
        # Extract keywords from problem
        problem_description = problem.get("objective", "")
        if not problem_description:
            return 0.5  # Default to middle score if no description

        keywords = set(re.findall(r'\b\w+\b', problem_description.lower()))

        # Count keyword occurrences in code
        code_lower = code.lower()
        keyword_count = sum(1 for keyword in keywords if keyword in code_lower)

        # Calculate relevance score
        if not keywords:
            return 0.5

        relevance = min(1.0, keyword_count / len(keywords))

        return relevance

    def _check_agent_compatibility(self, code: str, agent_id: str, capability: str) -> float:
        """
        Check if code is compatible with agent and capability.

        Args:
            code (str): Code to evaluate
            agent_id (str): Agent ID
            capability (str): Capability

        Returns:
            float: Compatibility score (0.0 to 1.0)
        """
        # This is a simplified implementation
        # In a real system, this would check against agent interfaces

        # Check if code contains agent-specific patterns
        agent_patterns = {
            "trading_agent": ["market", "trade", "portfolio", "asset", "stock"],
            "insurance_agent": ["policy", "premium", "claim", "coverage", "risk"],
            "security_agent": ["vulnerability", "scan", "threat", "attack", "defense"],
            "marketing_agent": ["campaign", "audience", "content", "engagement", "conversion"],
        }

        # Check if code contains capability-specific patterns
        capability_patterns = {
            "market_analysis": ["analyze", "market", "trend", "prediction", "forecast"],
            "portfolio_optimization": ["optimize", "portfolio", "allocation", "risk", "return"],
            "lead_qualification": ["qualify", "lead", "score", "prospect", "customer"],
            "vulnerability_scanning": ["scan", "vulnerability", "detect", "security", "threat"],
        }

        # Count pattern matches
        agent_pattern_matches = 0
        if agent_id in agent_patterns:
            agent_pattern_matches = sum(1 for pattern in agent_patterns[agent_id] if pattern in code.lower())

        capability_pattern_matches = 0
        if capability in capability_patterns:
            capability_pattern_matches = sum(1 for pattern in capability_patterns[capability] if pattern in code.lower())

        # Calculate compatibility score
        agent_score = min(1.0, agent_pattern_matches / 5) if agent_id in agent_patterns else 0.5
        capability_score = min(1.0, capability_pattern_matches / 5) if capability in capability_patterns else 0.5

        compatibility = 0.5 * agent_score + 0.5 * capability_score

        return compatibility

    def _evaluate_capability_implementation(self, code: str, capability: str) -> float:
        """
        Evaluate how well the code implements the capability.

        Args:
            code (str): Code to evaluate
            capability (str): Capability

        Returns:
            float: Implementation score (0.0 to 1.0)
        """
        # This is a simplified implementation
        # In a real system, this would run capability-specific tests

        # Check if code contains capability-specific function names
        capability_functions = {
            "market_analysis": ["analyze_market", "predict_trend", "calculate_indicators"],
            "portfolio_optimization": ["optimize_portfolio", "allocate_assets", "calculate_risk"],
            "lead_qualification": ["qualify_lead", "score_prospect", "evaluate_customer"],
            "vulnerability_scanning": ["scan_vulnerabilities", "detect_threats", "analyze_security"],
        }

        # Count function matches
        function_matches = 0
        if capability in capability_functions:
            for func_name in capability_functions[capability]:
                if f"def {func_name}" in code:
                    function_matches += 1

        # Calculate implementation score
        if capability in capability_functions:
            implementation_score = function_matches / len(capability_functions[capability])
        else:
            implementation_score = 0.5

        return implementation_score

    def _evaluate_optimization_metric(self, code: str, optimization_metric: str) -> float:
        """
        Evaluate how well the code optimizes for the given metric.

        Args:
            code (str): Code to evaluate
            optimization_metric (str): Optimization metric

        Returns:
            float: Optimization score (0.0 to 1.0)
        """
        # This is a simplified implementation
        # In a real system, this would run metric-specific benchmarks

        # Define optimization patterns for different metrics
        optimization_patterns = {
            "execution_speed": ["cache", "memoize", "optimize", "efficient", "fast"],
            "memory_usage": ["memory", "efficient", "compact", "optimize", "reduce"],
            "accuracy": ["accurate", "precise", "correct", "validate", "verify"],
            "prediction_accuracy": ["accuracy", "precision", "recall", "f1", "error"],
        }

        # Count pattern matches
        pattern_matches = 0
        if optimization_metric in optimization_patterns:
            pattern_matches = sum(1 for pattern in optimization_patterns[optimization_metric] if pattern in code.lower())

        # Calculate optimization score
        if optimization_metric in optimization_patterns:
            optimization_score = min(1.0, pattern_matches / len(optimization_patterns[optimization_metric]))
        else:
            optimization_score = 0.5

        return optimization_score

    def _record_evaluation(
        self,
        evaluation_id: str,
        code: str,
        problem: Dict,
        fitness: float,
        error: str = None,
        traceback: str = None,
    ):
        """
        Record code evaluation for analysis.

        Args:
            evaluation_id (str): Evaluation ID
            code (str): Evaluated code
            problem (Dict): Problem definition
            fitness (float): Fitness score
            error (str, optional): Error message
            traceback (str, optional): Error traceback
        """
        evaluation = {
            "id": evaluation_id,
            "code_hash": hash(code),
            "problem_type": problem.get("type", "unknown"),
            "fitness": fitness,
            "timestamp": datetime.now().isoformat(),
        }

        if error:
            evaluation["error"] = error

        if traceback:
            evaluation["traceback"] = traceback

        self.evaluation_history.append(evaluation)

        # Keep only the last 1000 evaluations
        if len(self.evaluation_history) > 1000:
            self.evaluation_history = self.evaluation_history[-1000:]

    async def shutdown(self):
        """Shutdown the Code Evaluator."""
        logger.info("Shutting down Code Evaluator")

        # Clean up temporary directory
        try:
            import shutil
            shutil.rmtree(self.temp_dir)
            logger.info(f"Removed temporary directory: {self.temp_dir}")
        except Exception as e:
            logger.error(f"Error removing temporary directory: {e}")

        logger.info("Code Evaluator shut down")
