"""
Simple script to send a test <NAME_EMAIL> to <EMAIL>.
"""
import os
import sys
import asyncio
from services.gmail_service import GmailService

async def send_test_email():
    """Send a test email."""
    print("Sending test <NAME_EMAIL> to <EMAIL>...")
    
    # Create Gmail service
    credentials_path = 'credentials/gmail_flofaction_dot_insurance_at_gmail_dot_com_credentials.json'
    token_path = 'credentials/gmail_flofaction_dot_insurance_at_gmail_dot_com_token.pickle'
    
    gmail_service = GmailService(credentials_path, token_path)
    
    if not gmail_service.is_enabled():
        print("Error: Gmail service is not enabled. Please check your credentials.")
        return
    
    # Send email
    result = await gmail_service.send_message(
        to="<EMAIL>",
        subject="Test Email from AI Agent System",
        body="This is a test email sent from the AI Agent System using the Gmail API. If you're receiving this, the integration is working correctly!"
    )
    
    if "error" in result:
        print(f"Error sending email: {result['error']}")
    else:
        print("Email sent successfully!")
        print(f"Message ID: {result['message_id']}")

if __name__ == "__main__":
    asyncio.run(send_test_email())
