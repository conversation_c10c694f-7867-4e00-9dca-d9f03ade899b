#!/usr/bin/env python3
"""
Test Gmail Service

This script tests the Gmail service by sending a test email.
"""

import os
import sys
import asyncio
import argparse
from typing import Dict, Any

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import required modules
from services.gmail_service import GmailService
from core.logger import setup_logger

# Set up logger
logger = setup_logger("test_gmail_service")

async def test_gmail_service(to_email: str, subject: str, body: str) -> Dict[str, Any]:
    """
    Test the Gmail service by sending a test email.
    
    Args:
        to_email (str): Recipient email address
        subject (str): Email subject
        body (str): Email body
        
    Returns:
        Dict[str, Any]: Result of the operation
    """
    # Ensure directories exist
    os.makedirs("credentials", exist_ok=True)
    os.makedirs("tokens", exist_ok=True)
    
    # Initialize Gmail service
    gmail_service = GmailService(
        credentials_path="credentials/gmail_credentials.json",
        token_path="tokens/gmail_token.json"
    )
    
    if not gmail_service.is_enabled():
        return {"success": False, "error": "Gmail service is not enabled"}
    
    # Send the email
    result = await gmail_service.send_message(
        to=to_email,
        subject=subject,
        body=body
    )
    
    if "error" in result:
        return {"success": False, "error": result["error"]}
    
    return {"success": True, "message": "Email sent successfully", "details": result}

async def main():
    """Main function to run the script."""
    parser = argparse.ArgumentParser(description="Test Gmail Service")
    parser.add_argument("--to", default="<EMAIL>", help="Recipient email address")
    parser.add_argument("--subject", default="Test Email from AI Agent System", help="Email subject")
    parser.add_argument("--body", default="This is a test email sent from the AI Agent System.", help="Email body")
    
    args = parser.parse_args()
    
    logger.info(f"Testing Gmail service by sending an email to {args.to}")
    
    # Test Gmail service
    result = await test_gmail_service(args.to, args.subject, args.body)
    
    if result["success"]:
        logger.info("Gmail service test successful!")
        logger.info(f"Email sent to {args.to}")
    else:
        logger.error(f"Gmail service test failed: {result['error']}")
    
    return result

if __name__ == "__main__":
    asyncio.run(main())
