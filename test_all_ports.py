"""
Test All Ports

This script tests all ports to find which one UI-TARS might be using.
"""
import os
import sys
import time
import asyncio
import logging
import argparse
import requests
from typing import Dict, Optional, Any, List

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("test_all_ports")

async def test_port(host: str, port: int) -> Dict[str, Any]:
    """
    Test if a port is open and if it might be UI-TARS.
    
    Args:
        host (str): Host to test
        port (int): Port to test
        
    Returns:
        Dict[str, Any]: Test results
    """
    logger.info(f"Testing {host}:{port}")
    
    # Create a session for API requests
    session = requests.Session()
    
    # Try different endpoints
    endpoints = [
        "",
        "/",
        "/v1",
        "/v1/models",
        "/v1/completions",
        "/v1/vision",
        "/health",
        "/api",
        "/api/v1",
        "/api/v1/models",
        "/api/v1/completions",
        "/api/v1/vision"
    ]
    
    results = {}
    
    for endpoint in endpoints:
        url = f"http://{host}:{port}{endpoint}"
        
        try:
            response = session.get(url, timeout=2)
            status_code = response.status_code
            content_type = response.headers.get("Content-Type", "")
            
            results[endpoint] = {
                "status_code": status_code,
                "content_type": content_type,
                "success": status_code < 400
            }
            
            if status_code < 400:
                logger.info(f"  ✓ {url} - {status_code}")
            else:
                logger.info(f"  ✗ {url} - {status_code}")
        
        except requests.exceptions.RequestException as e:
            results[endpoint] = {
                "error": str(e),
                "success": False
            }
            logger.info(f"  ✗ {url} - {str(e)}")
    
    # Check if any endpoint was successful
    success = any(result.get("success", False) for result in results.values())
    
    return {
        "host": host,
        "port": port,
        "success": success,
        "endpoints": results
    }

async def test_all_ports(host: str, start_port: int, end_port: int) -> List[Dict[str, Any]]:
    """
    Test a range of ports.
    
    Args:
        host (str): Host to test
        start_port (int): Start port
        end_port (int): End port
        
    Returns:
        List[Dict[str, Any]]: Test results
    """
    logger.info(f"Testing ports {start_port} to {end_port} on {host}")
    
    results = []
    
    for port in range(start_port, end_port + 1):
        result = await test_port(host, port)
        
        if result["success"]:
            results.append(result)
    
    return results

async def test_common_ports(host: str) -> List[Dict[str, Any]]:
    """
    Test common ports that UI-TARS might be using.
    
    Args:
        host (str): Host to test
        
    Returns:
        List[Dict[str, Any]]: Test results
    """
    logger.info(f"Testing common ports on {host}")
    
    # Common ports that UI-TARS might be using
    ports = [
        8080,  # Default UI-TARS port
        8000,  # Alternative port
        3000,  # Common web server port
        5000,  # Common web server port
        7860,  # Gradio port
        8888,  # Jupyter port
        9000,  # Alternative port
        1234,  # Random port
        4321,  # Random port
        5678,  # Random port
        8765   # Random port
    ]
    
    results = []
    
    for port in ports:
        result = await test_port(host, port)
        
        if result["success"]:
            results.append(result)
    
    return results

async def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Test All Ports")
    parser.add_argument("--host", type=str, default="localhost", help="Host to test")
    parser.add_argument("--start-port", type=int, default=8000, help="Start port")
    parser.add_argument("--end-port", type=int, default=9000, help="End port")
    parser.add_argument("--common", action="store_true", help="Test common ports only")
    
    args = parser.parse_args()
    
    # Test ports
    if args.common:
        results = await test_common_ports(args.host)
    else:
        results = await test_all_ports(args.host, args.start_port, args.end_port)
    
    # Print results
    if results:
        logger.info(f"Found {len(results)} potential UI-TARS ports:")
        
        for result in results:
            logger.info(f"- {result['host']}:{result['port']}")
            
            for endpoint, endpoint_result in result["endpoints"].items():
                if endpoint_result.get("success", False):
                    logger.info(f"  ✓ {endpoint} - {endpoint_result.get('status_code')}")
    else:
        logger.info("No potential UI-TARS ports found.")
        logger.info("Make sure UI-TARS is running and the API server is enabled.")
    
    return 0

if __name__ == "__main__":
    asyncio.run(main())
