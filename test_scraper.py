"""
<PERSON><PERSON><PERSON> to test the web scraper.
"""
import sys
import asyncio
import argparse
from pathlib import Path
import json

from utils.web_scraper import WebScraper
from core.logger import setup_logger

# Set up logger
logger = setup_logger("test_scraper")

async def test_fetch_page(url: str):
    """
    Test fetching a web page.
    
    Args:
        url (str): URL to fetch
    """
    logger.info(f"Testing page fetch for {url}")
    
    # Create web scraper
    scraper = WebScraper()
    await scraper.initialize()
    
    try:
        # Fetch page
        logger.info(f"Fetching {url}")
        html = await scraper.fetch_page(url)
        
        if not html:
            logger.error(f"Failed to fetch {url}")
            return False
        
        # Print page length
        logger.info(f"Fetched {len(html)} bytes from {url}")
        
        return True
    
    finally:
        # Close scraper
        await scraper.close()

async def test_extract_text(url: str):
    """
    Test extracting text from a web page.
    
    Args:
        url (str): URL to fetch
    """
    logger.info(f"Testing text extraction for {url}")
    
    # Create web scraper
    scraper = WebScraper()
    await scraper.initialize()
    
    try:
        # Fetch page
        logger.info(f"Fetching {url}")
        html = await scraper.fetch_page(url)
        
        if not html:
            logger.error(f"Failed to fetch {url}")
            return False
        
        # Extract text
        logger.info(f"Extracting text from {url}")
        text = await scraper.extract_text(html)
        
        # Print text preview
        preview = text[:500] + "..." if len(text) > 500 else text
        logger.info(f"Extracted {len(text)} characters of text")
        print("\n" + "-" * 80)
        print(preview)
        print("-" * 80 + "\n")
        
        return True
    
    finally:
        # Close scraper
        await scraper.close()

async def test_extract_links(url: str):
    """
    Test extracting links from a web page.
    
    Args:
        url (str): URL to fetch
    """
    logger.info(f"Testing link extraction for {url}")
    
    # Create web scraper
    scraper = WebScraper()
    await scraper.initialize()
    
    try:
        # Fetch page
        logger.info(f"Fetching {url}")
        html = await scraper.fetch_page(url)
        
        if not html:
            logger.error(f"Failed to fetch {url}")
            return False
        
        # Extract links
        logger.info(f"Extracting links from {url}")
        links = await scraper.extract_links(html, url)
        
        # Print links
        logger.info(f"Extracted {len(links)} links")
        for i, link in enumerate(links[:10]):  # Print first 10 links
            print(f"{i+1}. {link['text'][:50]}: {link['url']}")
        
        if len(links) > 10:
            print(f"... and {len(links) - 10} more links")
        
        return True
    
    finally:
        # Close scraper
        await scraper.close()

async def test_scrape_article(url: str):
    """
    Test scraping an article.
    
    Args:
        url (str): URL to fetch
    """
    logger.info(f"Testing article scraping for {url}")
    
    # Create web scraper
    scraper = WebScraper()
    await scraper.initialize()
    
    try:
        # Scrape article
        logger.info(f"Scraping article from {url}")
        article = await scraper.scrape_article(url)
        
        if not article:
            logger.error(f"Failed to scrape article from {url}")
            return False
        
        # Print article
        logger.info(f"Scraped article: {article.get('title')}")
        print("\n" + "-" * 80)
        print(f"Title: {article.get('title')}")
        print(f"URL: {article.get('url')}")
        print("\nContent Preview:")
        content = article.get('text', '')
        preview = content[:500] + "..." if len(content) > 500 else content
        print(preview)
        print("-" * 80 + "\n")
        
        return True
    
    finally:
        # Close scraper
        await scraper.close()

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Test web scraper")
    parser.add_argument("url", help="URL to scrape")
    parser.add_argument("--mode", choices=["fetch", "text", "links", "article"], default="article", help="Test mode")
    args = parser.parse_args()
    
    # Run test
    if args.mode == "fetch":
        success = asyncio.run(test_fetch_page(args.url))
    elif args.mode == "text":
        success = asyncio.run(test_extract_text(args.url))
    elif args.mode == "links":
        success = asyncio.run(test_extract_links(args.url))
    elif args.mode == "article":
        success = asyncio.run(test_scrape_article(args.url))
    else:
        print(f"Unknown mode: {args.mode}")
        return 1
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
