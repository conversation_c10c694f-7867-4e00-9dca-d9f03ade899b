"""
Security Tools Service for the Multi-Agent AI System.

This module provides integration with various security tools including:
- Nmap for network scanning
- <PERSON> the Ripper for password cracking
- Wireshark for network analysis
- Metasploit for penetration testing
- Burp Suite for web application security
- Aircrack-ng for wireless security
- SQLMap for SQL injection testing
- OWASP ZAP for web application security
- theHarvester for information gathering
- Nikto for web server scanning
- PentestGPT for AI-powered security testing
"""
import asyncio
import json
import logging
import os
import subprocess
import platform
import shutil
from typing import Dict, List, Optional, Any, Union
import uuid
from datetime import datetime
import aiohttp

from core.logger import setup_logger
from PentestGPT.pentestgpt_connector import PentestGPTConnector

# Set up logger
logger = setup_logger("security_tools_service")

class SecurityToolsService:
    """
    Service for integrating with various security tools.
    
    This class provides an interface for interacting with security tools
    for tasks such as network scanning, vulnerability assessment, and
    penetration testing.
    """
    
    def __init__(self, config: Dict):
        """
        Initialize the security tools service.
        
        Args:
            config (Dict): Security tools configuration
        """
        self.config = config
        self.enabled = config.get("enabled", False)
        self.tools_dir = config.get("tools_dir", "./tools")
        self.tool_registry = config.get("tool_registry", {})
        
        # PentestGPT connector
        self.pentestgpt = None
        
        # Platform-specific settings
        self.platform = platform.system().lower()
        
        logger.info("Security tools service initialized")
    
    async def initialize(self):
        """Initialize the security tools service."""
        if not self.enabled:
            logger.warning("Security tools service is disabled")
            return
        
        try:
            # Create tools directory if it doesn't exist
            os.makedirs(self.tools_dir, exist_ok=True)
            
            # Initialize PentestGPT connector
            pentestgpt_config = self.config.get("pentestgpt", {})
            if pentestgpt_config.get("enabled", False):
                self.pentestgpt = PentestGPTConnector(pentestgpt_config)
                await self.pentestgpt.initialize()
            
            # Check installed tools
            await self._check_installed_tools()
            
            logger.info("Security tools service initialized")
            
        except Exception as e:
            logger.exception(f"Error initializing security tools service: {e}")
            self.enabled = False
    
    async def _check_installed_tools(self):
        """Check which security tools are installed."""
        for tool_id, tool_config in self.tool_registry.items():
            if tool_config.get("enabled", False):
                installed = await self.check_tool_installed(tool_id)
                if installed:
                    logger.info(f"Tool {tool_id} is installed")
                else:
                    logger.warning(f"Tool {tool_id} is not installed")
                    
                    # Auto-install if configured
                    if tool_config.get("install_method") == "auto":
                        logger.info(f"Attempting to install {tool_id}")
                        await self.install_tool(tool_id)
    
    async def check_tool_installed(self, tool_id: str) -> bool:
        """
        Check if a security tool is installed.
        
        Args:
            tool_id (str): Tool identifier
            
        Returns:
            bool: True if installed, False otherwise
        """
        if tool_id not in self.tool_registry:
            logger.warning(f"Unknown tool: {tool_id}")
            return False
        
        tool_config = self.tool_registry[tool_id]
        
        # Get check command based on platform
        check_cmd = None
        if self.platform == "windows":
            check_cmd = tool_config.get("windows", {}).get("check_cmd")
        elif self.platform == "linux":
            check_cmd = tool_config.get("linux", {}).get("check_cmd")
        elif self.platform == "darwin":
            check_cmd = tool_config.get("darwin", {}).get("check_cmd")
        
        if not check_cmd:
            logger.warning(f"No check command for {tool_id} on {self.platform}")
            return False
        
        try:
            # Run check command
            process = await asyncio.create_subprocess_shell(
                check_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
            )
            
            stdout, stderr = await process.communicate()
            
            return process.returncode == 0
            
        except Exception as e:
            logger.exception(f"Error checking if {tool_id} is installed: {e}")
            return False
    
    async def install_tool(self, tool_id: str) -> bool:
        """
        Install a security tool.
        
        Args:
            tool_id (str): Tool identifier
            
        Returns:
            bool: True if installed successfully, False otherwise
        """
        if tool_id not in self.tool_registry:
            logger.warning(f"Unknown tool: {tool_id}")
            return False
        
        tool_config = self.tool_registry[tool_id]
        
        # Get install command based on platform
        install_cmd = None
        if self.platform == "windows":
            install_cmd = tool_config.get("windows", {}).get("install_cmd")
        elif self.platform == "linux":
            install_cmd = tool_config.get("linux", {}).get("install_cmd")
        elif self.platform == "darwin":
            install_cmd = tool_config.get("darwin", {}).get("install_cmd")
        
        if not install_cmd:
            logger.warning(f"No install command for {tool_id} on {self.platform}")
            return False
        
        try:
            # Run install command
            process = await asyncio.create_subprocess_shell(
                install_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                logger.error(f"Error installing {tool_id}: {stderr.decode()}")
                return False
            
            logger.info(f"Tool {tool_id} installed successfully")
            return True
            
        except Exception as e:
            logger.exception(f"Error installing {tool_id}: {e}")
            return False
    
    async def run_tool(self, tool_id: str, args: List[str] = None) -> Dict:
        """
        Run a security tool.
        
        Args:
            tool_id (str): Tool identifier
            args (List[str]): Tool arguments
            
        Returns:
            Dict: Tool execution results
        """
        if not self.enabled:
            return {"error": "Security tools service is disabled"}
        
        if tool_id not in self.tool_registry:
            return {"error": f"Unknown tool: {tool_id}"}
        
        tool_config = self.tool_registry[tool_id]
        
        if not tool_config.get("enabled", False):
            return {"error": f"Tool {tool_id} is disabled"}
        
        # Check if tool is installed
        installed = await self.check_tool_installed(tool_id)
        if not installed:
            return {"error": f"Tool {tool_id} is not installed"}
        
        # Get command based on platform
        cmd = None
        if self.platform == "windows":
            cmd = tool_config.get("windows", {}).get("cmd")
        elif self.platform == "linux":
            cmd = tool_config.get("linux", {}).get("cmd")
        elif self.platform == "darwin":
            cmd = tool_config.get("darwin", {}).get("cmd")
        
        if not cmd:
            return {"error": f"No command for {tool_id} on {self.platform}"}
        
        # Prepare command with arguments
        full_cmd = [cmd]
        if args:
            full_cmd.extend(args)
        
        try:
            # Run command
            process = await asyncio.create_subprocess_exec(
                *full_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
            )
            
            stdout, stderr = await process.communicate()
            
            return {
                "tool_id": tool_id,
                "args": args,
                "returncode": process.returncode,
                "stdout": stdout.decode(),
                "stderr": stderr.decode(),
                "timestamp": datetime.now().isoformat(),
            }
            
        except Exception as e:
            logger.exception(f"Error running {tool_id}: {e}")
            return {"error": str(e)}
    
    async def run_network_scan(self, target: str, scan_type: str = "basic") -> Dict:
        """
        Run a network scan using Nmap.
        
        Args:
            target (str): Target to scan (IP, hostname, network range)
            scan_type (str): Type of scan (basic, comprehensive, quick)
            
        Returns:
            Dict: Scan results
        """
        # Prepare Nmap arguments based on scan type
        if scan_type == "basic":
            args = ["-sV", target]
        elif scan_type == "comprehensive":
            args = ["-sS", "-sV", "-sC", "-A", "-O", target]
        elif scan_type == "quick":
            args = ["-T4", "-F", target]
        else:
            args = [target]
        
        # Run Nmap
        result = await self.run_tool("nmap", args=args)
        
        # Parse Nmap output
        if "error" not in result:
            # Extract open ports
            open_ports = []
            for line in result["stdout"].splitlines():
                if "open" in line and "/tcp" in line:
                    parts = line.split()
                    if len(parts) >= 3:
                        port_info = parts[0].split("/")[0]
                        service = parts[2] if len(parts) > 2 else "unknown"
                        open_ports.append({"port": port_info, "service": service})
            
            result["open_ports"] = open_ports
        
        return result
