@echo off
echo UI-TARS 1.5 Implementation Test
echo =============================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Ask for UI-TARS path
echo Enter the path to UI-TARS executable (leave empty to auto-detect):
set /p UI_TARS_PATH=""

REM Ask for configuration path
echo.
echo Enter the path to UI-TARS configuration file (leave empty to auto-detect):
set /p CONFIG_PATH=""

REM Ask for browser type
echo.
echo Select browser type:
echo 1. Auto-detect (default)
echo 2. Chrome
echo 3. Edge
echo 4. Firefox
echo 5. Brave
echo.
set /p BROWSER_CHOICE="Enter choice (1-5): "

if "%BROWSER_CHOICE%"=="2" (
    set BROWSER_TYPE=chrome
) else if "%BROWSER_CHOICE%"=="3" (
    set BROWSER_TYPE=edge
) else if "%BROWSER_CHOICE%"=="4" (
    set BROWSER_TYPE=firefox
) else if "%BROWSER_CHOICE%"=="5" (
    set BROWSER_TYPE=brave
) else (
    set BROWSER_TYPE=
)

REM Ask for test options
echo.
echo Select test options:
echo 1. Run all tests (default)
echo 2. Skip sandbox test
echo 3. Skip virtual PC test
echo 4. Skip DPO test
echo 5. Skip all advanced tests
echo.
set /p TEST_CHOICE="Enter choice (1-5): "

if "%TEST_CHOICE%"=="2" (
    set TEST_OPTIONS=--no-sandbox
) else if "%TEST_CHOICE%"=="3" (
    set TEST_OPTIONS=--no-virtual-pc
) else if "%TEST_CHOICE%"=="4" (
    set TEST_OPTIONS=--no-dpo
) else if "%TEST_CHOICE%"=="5" (
    set TEST_OPTIONS=--no-sandbox --no-virtual-pc --no-dpo
) else (
    set TEST_OPTIONS=
)

REM Run the test script
echo.
echo Running UI-TARS implementation test...
echo.

set COMMAND=python test_ui_tars_implementation.py

if not "%UI_TARS_PATH%"=="" (
    set COMMAND=%COMMAND% --path "%UI_TARS_PATH%"
)

if not "%CONFIG_PATH%"=="" (
    set COMMAND=%COMMAND% --config "%CONFIG_PATH%"
)

if not "%BROWSER_TYPE%"=="" (
    set COMMAND=%COMMAND% --browser %BROWSER_TYPE%
)

set COMMAND=%COMMAND% %TEST_OPTIONS% --debug

echo Executing: %COMMAND%
echo.

%COMMAND%

echo.
if %errorlevel% equ 0 (
    echo UI-TARS implementation test passed successfully!
) else (
    echo UI-TARS implementation test failed. Please check the recommendations above.
)

echo.
pause
