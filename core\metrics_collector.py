"""
Metrics Collector for the Multi-Agent AI System.

This module provides metrics collection capabilities for the system,
allowing for performance monitoring and evaluation.
"""
import asyncio
import json
import logging
import os
from typing import Dict, List, Optional, Any, Union
import uuid
from datetime import datetime, timedelta
import time
import psutil
import numpy as np
from pathlib import Path

from core.logger import setup_logger
from core.state_manager import StateManager

# Set up logger
logger = setup_logger("metrics_collector")

class MetricsCollector:
    """
    Collector for system metrics.
    
    This class provides metrics collection capabilities for the system,
    allowing for performance monitoring and evaluation.
    """
    
    def __init__(
        self,
        state_manager: StateManager,
        collection_interval: int = 60,  # seconds
        retention_period: int = 86400,  # seconds (1 day)
    ):
        """
        Initialize the metrics collector.
        
        Args:
            state_manager (StateManager): System state manager
            collection_interval (int): Interval between metric collections in seconds
            retention_period (int): Period to retain metrics in seconds
        """
        self.state_manager = state_manager
        self.collection_interval = collection_interval
        self.retention_period = retention_period
        
        # Metrics storage
        self.system_metrics = []
        self.agent_metrics = {}
        self.workflow_metrics = {}
        self.message_metrics = []
        
        # Collection task
        self.collection_task = None
        self.running = False
    
    async def start(self):
        """Start the metrics collector."""
        if self.running:
            logger.warning("Metrics collector already running")
            return
        
        logger.info("Starting metrics collector")
        self.running = True
        
        # Load existing metrics
        await self._load_metrics()
        
        # Start collection task
        self.collection_task = asyncio.create_task(self._collection_loop())
    
    async def stop(self):
        """Stop the metrics collector."""
        if not self.running:
            logger.warning("Metrics collector not running")
            return
        
        logger.info("Stopping metrics collector")
        self.running = False
        
        # Cancel collection task
        if self.collection_task:
            self.collection_task.cancel()
            try:
                await self.collection_task
            except asyncio.CancelledError:
                pass
        
        # Save final metrics
        await self._save_metrics()
    
    async def _load_metrics(self):
        """Load metrics from state manager."""
        # Load system metrics
        system_metrics = await self.state_manager.get_state("metrics", "system")
        if system_metrics:
            self.system_metrics = system_metrics
        
        # Load agent metrics
        agent_metrics = await self.state_manager.get_state("metrics", "agents")
        if agent_metrics:
            self.agent_metrics = agent_metrics
        
        # Load workflow metrics
        workflow_metrics = await self.state_manager.get_state("metrics", "workflows")
        if workflow_metrics:
            self.workflow_metrics = workflow_metrics
        
        # Load message metrics
        message_metrics = await self.state_manager.get_state("metrics", "messages")
        if message_metrics:
            self.message_metrics = message_metrics
        
        logger.info("Loaded existing metrics")
    
    async def _save_metrics(self):
        """Save metrics to state manager."""
        # Save system metrics
        await self.state_manager.update_state("metrics", "system", self.system_metrics)
        
        # Save agent metrics
        await self.state_manager.update_state("metrics", "agents", self.agent_metrics)
        
        # Save workflow metrics
        await self.state_manager.update_state("metrics", "workflows", self.workflow_metrics)
        
        # Save message metrics
        await self.state_manager.update_state("metrics", "messages", self.message_metrics)
        
        logger.info("Saved metrics")
    
    async def _collection_loop(self):
        """Metrics collection loop."""
        try:
            while self.running:
                # Collect metrics
                await self._collect_metrics()
                
                # Prune old metrics
                self._prune_metrics()
                
                # Save metrics
                await self._save_metrics()
                
                # Wait for next collection
                await asyncio.sleep(self.collection_interval)
        
        except asyncio.CancelledError:
            logger.info("Metrics collection task cancelled")
            raise
        
        except Exception as e:
            logger.exception(f"Error in metrics collection: {e}")
    
    async def _collect_metrics(self):
        """Collect metrics."""
        timestamp = datetime.now().isoformat()
        
        # Collect system metrics
        system_metrics = self._collect_system_metrics()
        system_metrics["timestamp"] = timestamp
        self.system_metrics.append(system_metrics)
        
        # Collect agent metrics
        await self._collect_agent_metrics(timestamp)
        
        # Collect workflow metrics
        await self._collect_workflow_metrics(timestamp)
        
        # Collect message metrics
        await self._collect_message_metrics(timestamp)
        
        logger.debug("Collected metrics")
    
    def _collect_system_metrics(self) -> Dict:
        """
        Collect system metrics.
        
        Returns:
            Dict: System metrics
        """
        # CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # Memory usage
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        memory_used = memory.used
        memory_total = memory.total
        
        # Disk usage
        disk = psutil.disk_usage("/")
        disk_percent = disk.percent
        disk_used = disk.used
        disk_total = disk.total
        
        # Network I/O
        net_io = psutil.net_io_counters()
        net_bytes_sent = net_io.bytes_sent
        net_bytes_recv = net_io.bytes_recv
        
        return {
            "cpu_percent": cpu_percent,
            "memory_percent": memory_percent,
            "memory_used": memory_used,
            "memory_total": memory_total,
            "disk_percent": disk_percent,
            "disk_used": disk_used,
            "disk_total": disk_total,
            "net_bytes_sent": net_bytes_sent,
            "net_bytes_recv": net_bytes_recv,
        }
    
    async def _collect_agent_metrics(self, timestamp: str):
        """
        Collect agent metrics.
        
        Args:
            timestamp (str): Collection timestamp
        """
        # Get all agents
        agents = await self.state_manager.get_state("agents")
        
        if not agents:
            return
        
        for agent_id, agent_data in agents.items():
            # Initialize agent metrics if not exists
            if agent_id not in self.agent_metrics:
                self.agent_metrics[agent_id] = []
            
            # Get agent status
            status = agent_data.get("status", "unknown")
            
            # Get agent message stats
            message_stats = agent_data.get("message_stats", {})
            messages_sent = message_stats.get("sent", 0)
            messages_received = message_stats.get("received", 0)
            message_errors = message_stats.get("errors", 0)
            
            # Get agent task stats
            tasks = agent_data.get("tasks", [])
            tasks_completed = sum(1 for task in tasks if task.get("status") == "completed")
            tasks_failed = sum(1 for task in tasks if task.get("status") == "failed")
            tasks_pending = sum(1 for task in tasks if task.get("status") == "pending")
            
            # Get agent memory usage
            memory_size = len(json.dumps(agent_data))
            
            # Add metrics
            self.agent_metrics[agent_id].append({
                "timestamp": timestamp,
                "status": status,
                "messages_sent": messages_sent,
                "messages_received": messages_received,
                "message_errors": message_errors,
                "tasks_completed": tasks_completed,
                "tasks_failed": tasks_failed,
                "tasks_pending": tasks_pending,
                "memory_size": memory_size,
            })
    
    async def _collect_workflow_metrics(self, timestamp: str):
        """
        Collect workflow metrics.
        
        Args:
            timestamp (str): Collection timestamp
        """
        # Get all workflows
        workflows = await self.state_manager.get_state("coordinator", "workflows")
        
        if not workflows:
            return
        
        # Get workflow executions
        executions = await self.state_manager.get_state("advanced_coordinator", "workflow_executions")
        
        if not executions:
            executions = {}
        
        for workflow_id, workflow in workflows.items():
            # Initialize workflow metrics if not exists
            if workflow_id not in self.workflow_metrics:
                self.workflow_metrics[workflow_id] = []
            
            # Count executions
            workflow_executions = [
                execution for execution in executions.values()
                if execution.get("workflow_id") == workflow_id
            ]
            
            executions_completed = sum(1 for execution in workflow_executions if execution.get("status") == "completed")
            executions_failed = sum(1 for execution in workflow_executions if execution.get("status") == "failed")
            executions_running = sum(1 for execution in workflow_executions if execution.get("status") == "running")
            
            # Calculate average execution time
            execution_times = []
            for execution in workflow_executions:
                if execution.get("status") == "completed":
                    start_time = datetime.fromisoformat(execution.get("created_at", "2000-01-01T00:00:00"))
                    end_time = datetime.fromisoformat(execution.get("completed_at", "2000-01-01T00:00:00"))
                    execution_time = (end_time - start_time).total_seconds()
                    execution_times.append(execution_time)
            
            avg_execution_time = np.mean(execution_times) if execution_times else 0
            
            # Add metrics
            self.workflow_metrics[workflow_id].append({
                "timestamp": timestamp,
                "executions_completed": executions_completed,
                "executions_failed": executions_failed,
                "executions_running": executions_running,
                "avg_execution_time": avg_execution_time,
            })
    
    async def _collect_message_metrics(self, timestamp: str):
        """
        Collect message metrics.
        
        Args:
            timestamp (str): Collection timestamp
        """
        # Get agent manager message stats
        agent_manager = await self.state_manager.get_state("agent_manager")
        
        if not agent_manager:
            return
        
        message_stats = agent_manager.get("message_stats", {})
        messages_processed = message_stats.get("processed", 0)
        message_errors = message_stats.get("errors", 0)
        broadcasts = message_stats.get("broadcasts", 0)
        
        # Get coordinator message stats
        coordinator = await self.state_manager.get_state("coordinator")
        
        if coordinator:
            coordinator_message_stats = coordinator.get("message_stats", {})
            coordinator_sent = coordinator_message_stats.get("sent", 0)
            coordinator_received = coordinator_message_stats.get("received", 0)
            coordinator_errors = coordinator_message_stats.get("errors", 0)
        else:
            coordinator_sent = 0
            coordinator_received = 0
            coordinator_errors = 0
        
        # Add metrics
        self.message_metrics.append({
            "timestamp": timestamp,
            "messages_processed": messages_processed,
            "message_errors": message_errors,
            "broadcasts": broadcasts,
            "coordinator_sent": coordinator_sent,
            "coordinator_received": coordinator_received,
            "coordinator_errors": coordinator_errors,
        })
    
    def _prune_metrics(self):
        """Prune old metrics."""
        now = datetime.now()
        cutoff = now - timedelta(seconds=self.retention_period)
        cutoff_str = cutoff.isoformat()
        
        # Prune system metrics
        self.system_metrics = [
            metric for metric in self.system_metrics
            if metric.get("timestamp", "2000-01-01T00:00:00") >= cutoff_str
        ]
        
        # Prune agent metrics
        for agent_id in self.agent_metrics:
            self.agent_metrics[agent_id] = [
                metric for metric in self.agent_metrics[agent_id]
                if metric.get("timestamp", "2000-01-01T00:00:00") >= cutoff_str
            ]
        
        # Prune workflow metrics
        for workflow_id in self.workflow_metrics:
            self.workflow_metrics[workflow_id] = [
                metric for metric in self.workflow_metrics[workflow_id]
                if metric.get("timestamp", "2000-01-01T00:00:00") >= cutoff_str
            ]
        
        # Prune message metrics
        self.message_metrics = [
            metric for metric in self.message_metrics
            if metric.get("timestamp", "2000-01-01T00:00:00") >= cutoff_str
        ]
        
        logger.debug("Pruned old metrics")
    
    async def get_system_metrics(self, time_window: Optional[int] = None) -> List[Dict]:
        """
        Get system metrics.
        
        Args:
            time_window (Optional[int]): Time window in seconds
            
        Returns:
            List[Dict]: System metrics
        """
        if time_window:
            now = datetime.now()
            cutoff = now - timedelta(seconds=time_window)
            cutoff_str = cutoff.isoformat()
            
            return [
                metric for metric in self.system_metrics
                if metric.get("timestamp", "2000-01-01T00:00:00") >= cutoff_str
            ]
        else:
            return self.system_metrics
    
    async def get_agent_metrics(self, agent_id: Optional[str] = None, time_window: Optional[int] = None) -> Dict:
        """
        Get agent metrics.
        
        Args:
            agent_id (Optional[str]): Agent ID, None for all agents
            time_window (Optional[int]): Time window in seconds
            
        Returns:
            Dict: Agent metrics
        """
        if agent_id:
            if agent_id not in self.agent_metrics:
                return {}
            
            if time_window:
                now = datetime.now()
                cutoff = now - timedelta(seconds=time_window)
                cutoff_str = cutoff.isoformat()
                
                return {
                    agent_id: [
                        metric for metric in self.agent_metrics[agent_id]
                        if metric.get("timestamp", "2000-01-01T00:00:00") >= cutoff_str
                    ]
                }
            else:
                return {agent_id: self.agent_metrics[agent_id]}
        else:
            if time_window:
                now = datetime.now()
                cutoff = now - timedelta(seconds=time_window)
                cutoff_str = cutoff.isoformat()
                
                return {
                    agent_id: [
                        metric for metric in metrics
                        if metric.get("timestamp", "2000-01-01T00:00:00") >= cutoff_str
                    ]
                    for agent_id, metrics in self.agent_metrics.items()
                }
            else:
                return self.agent_metrics
