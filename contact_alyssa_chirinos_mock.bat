@echo off
REM Contact Alyssa <PERSON>ript (Mock Version)
REM This script runs the mock_client_outreach.py script with Alyssa's information

echo.
echo ===================================
echo    Contact Alyssa <PERSON> (MOCK)
echo ===================================
echo.

REM Set client information
set CLIENT_NAME=Alyssa <PERSON> Chirinos
set CLIENT_EMAIL=<EMAIL>
set CLIENT_PHONE=**********
set CLIENT_DOB=8/16/97
set CLIENT_ADDRESS=Bradenton, Florida
set INSURANCE_TYPE=IUL with Dental, Vision, and Basic Health
set PREMIUM=100
set AGENT_NAME=Sandra
set ADDITIONAL_NOTES=Primary interest is IUL. Also interested in dental, vision, and basic private health coverage for checkups, physicals, and bloodwork. TOTAL BUDGET IS $100/MONTH. Need to check all carriers for best solution within budget.

echo Client: %CLIENT_NAME%
echo Email: %CLIENT_EMAIL%
echo Phone: %CLIENT_PHONE%
echo DOB: %CLIENT_DOB%
echo Address: %CLIENT_ADDRESS%
echo Insurance Type: %INSURANCE_TYPE%
echo Estimated Premium: $%PREMIUM%/month
echo Agent: %AGENT_NAME%
echo.
echo Additional Notes:
echo %ADDITIONAL_NOTES%
echo.

REM Ask for confirmation
set /p CONFIRM="Proceed with mock outreach? (y/n): "
if /i not "%CONFIRM%"=="y" (
    echo Outreach cancelled.
    goto end
)

echo.
echo Running mock outreach for %CLIENT_NAME%...
echo.

REM Run the command
python client_outreach.py --name "%CLIENT_NAME%" --email "%CLIENT_EMAIL%" --phone "%CLIENT_PHONE%" --dob "%CLIENT_DOB%" --address "%CLIENT_ADDRESS%" --insurance-type "%INSURANCE_TYPE%" --premium "%PREMIUM%" --agent "%AGENT_NAME%" --notes "%ADDITIONAL_NOTES%" --quote --mock

echo.
echo Mock outreach completed.
echo.

:end
pause
