"""
Prompt Engineering for AlphaEvolve.

This module provides prompt engineering capabilities for AlphaEvolve,
enabling the generation of effective prompts for code generation.
"""
import asyncio
import json
import logging
import os
import random
from typing import Dict, List, Optional, Any, Union
import uuid
from datetime import datetime

# Add the project root to the Python path
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

from core.logger import setup_logger

# Set up logger
logger = setup_logger("prompt_engineering")

class PromptManager:
    """
    Prompt Manager for AlphaEvolve.

    This class provides prompt engineering capabilities for AlphaEvolve,
    enabling the generation of effective prompts for code generation.
    """

    def __init__(self, config: Dict = None):
        """
        Initialize the Prompt Manager.

        Args:
            config (Dict, optional): Configuration
        """
        self.config = config or {}
        self.initialized = False
        
        # Prompt templates directory
        self.templates_dir = self.config.get("templates_dir", "alpha_evolve/templates")
        
        # Prompt templates
        self.templates = {}
        
        # Prompt history
        self.prompt_history = []
        
    async def initialize(self):
        """Initialize the Prompt Manager."""
        logger.info("Initializing Prompt Manager")
        
        # Load prompt templates
        await self._load_templates()
        
        self.initialized = True
        logger.info("Prompt Manager initialized")
    
    async def _load_templates(self):
        """Load prompt templates from directory."""
        try:
            # Create templates directory if it doesn't exist
            os.makedirs(self.templates_dir, exist_ok=True)
            
            # Check if templates directory exists
            if os.path.isdir(self.templates_dir):
                # Load templates from files
                for filename in os.listdir(self.templates_dir):
                    if filename.endswith(".json"):
                        template_path = os.path.join(self.templates_dir, filename)
                        with open(template_path, "r") as f:
                            template = json.load(f)
                        
                        template_id = os.path.splitext(filename)[0]
                        self.templates[template_id] = template
                        logger.info(f"Loaded template: {template_id}")
            
            # If no templates found, use default templates
            if not self.templates:
                self._create_default_templates()
        
        except Exception as e:
            logger.exception(f"Error loading templates: {e}")
            self._create_default_templates()
    
    def _create_default_templates(self):
        """Create default prompt templates."""
        logger.info("Creating default prompt templates")
        
        # Default templates
        self.templates = {
            "optimization": {
                "name": "Optimization",
                "description": "Template for optimization problems",
                "template": """
You are tasked with creating an efficient algorithm for the following optimization problem:

{objective}

Requirements:
{requirements}

Constraints:
{constraints}

Your solution should be implemented as a Python function that takes the following inputs:
{inputs}

The function should return:
{outputs}

Focus on creating an efficient and correct solution.
""",
                "variables": ["objective", "requirements", "constraints", "inputs", "outputs"],
            },
            "agent_enhancement": {
                "name": "Agent Enhancement",
                "description": "Template for agent enhancement problems",
                "template": """
You are tasked with enhancing the {capability} capability of the {agent_id} agent.

The goal is to optimize for {optimization_metric}.

Your solution should be implemented as a Python function that follows this interface:
{interface}

Make sure your implementation is:
1. Efficient
2. Robust
3. Well-integrated with the agent's existing capabilities
4. Optimized for {optimization_metric}
""",
                "variables": ["agent_id", "capability", "optimization_metric", "interface"],
            },
            "algorithm_discovery": {
                "name": "Algorithm Discovery",
                "description": "Template for algorithm discovery problems",
                "template": """
You are tasked with discovering a new algorithm for the following problem:

{problem_description}

Your algorithm should:
{requirements}

Implement your solution as a Python function with the following signature:
{function_signature}

Focus on creating a novel and efficient approach.
""",
                "variables": ["problem_description", "requirements", "function_signature"],
            },
            "code_optimization": {
                "name": "Code Optimization",
                "description": "Template for code optimization problems",
                "template": """
Optimize the following code for {optimization_goal}:

```python
{original_code}
```

Your optimized solution should:
1. Maintain the same functionality
2. Improve {optimization_goal}
3. Be well-structured and readable

Return only the optimized code.
""",
                "variables": ["original_code", "optimization_goal"],
            },
        }
        
        # Save default templates to files
        try:
            for template_id, template in self.templates.items():
                template_path = os.path.join(self.templates_dir, f"{template_id}.json")
                with open(template_path, "w") as f:
                    json.dump(template, f, indent=2)
                logger.info(f"Saved template: {template_id}")
        except Exception as e:
            logger.error(f"Error saving templates: {e}")
    
    async def generate_prompts(self, problem: Dict, count: int) -> List[str]:
        """
        Generate prompts for a problem.
        
        Args:
            problem (Dict): Problem definition
            count (int): Number of prompts to generate
            
        Returns:
            List[str]: Generated prompts
        """
        if not self.initialized:
            await self.initialize()
        
        prompts = []
        
        # Get problem type
        problem_type = problem.get("type", "unknown")
        
        # Get template for problem type
        template = self.templates.get(problem_type)
        
        if not template:
            # Use default template
            template = self.templates.get("optimization")
            if not template:
                # Create a simple prompt
                for i in range(count):
                    prompt = f"Create a Python function to solve the following problem: {problem.get('objective', 'unknown problem')}"
                    prompts.append(prompt)
                return prompts
        
        # Generate prompts using template
        for i in range(count):
            try:
                # Fill template variables
                template_vars = {}
                for var in template.get("variables", []):
                    if var in problem:
                        template_vars[var] = problem[var]
                    else:
                        # Use default value or placeholder
                        template_vars[var] = f"[{var}]"
                
                # Apply template
                prompt = template["template"]
                for var, value in template_vars.items():
                    prompt = prompt.replace(f"{{{var}}}", str(value))
                
                # Add variation to prompt
                prompt = self._add_prompt_variation(prompt, i, count)
                
                prompts.append(prompt)
                
                # Record prompt
                self._record_prompt(prompt, problem_type, template["name"])
            
            except Exception as e:
                logger.error(f"Error generating prompt {i}: {e}")
                # Use a simple prompt as fallback
                prompt = f"Create a Python function to solve the following problem: {problem.get('objective', 'unknown problem')}"
                prompts.append(prompt)
        
        return prompts
    
    def _add_prompt_variation(self, prompt: str, index: int, total: int) -> str:
        """
        Add variation to a prompt to encourage diversity.
        
        Args:
            prompt (str): Base prompt
            index (int): Prompt index
            total (int): Total number of prompts
            
        Returns:
            str: Prompt with variation
        """
        # Define variation strategies
        variation_strategies = [
            # Focus on efficiency
            "\nOptimize your solution for maximum efficiency.",
            # Focus on readability
            "\nFocus on creating a clear, readable solution.",
            # Focus on novel approach
            "\nTry to use a novel or unconventional approach.",
            # Focus on simplicity
            "\nAim for the simplest possible solution.",
            # Focus on robustness
            "\nEnsure your solution is robust to edge cases.",
        ]
        
        # Add random variation
        if index < len(variation_strategies):
            # Use each strategy at least once
            variation = variation_strategies[index]
        else:
            # Use random strategy for remaining prompts
            variation = random.choice(variation_strategies)
        
        return f"{prompt}{variation}"
    
    def _record_prompt(self, prompt: str, problem_type: str, template_name: str):
        """
        Record prompt for analysis.
        
        Args:
            prompt (str): Generated prompt
            problem_type (str): Problem type
            template_name (str): Template name
        """
        prompt_record = {
            "id": str(uuid.uuid4()),
            "prompt": prompt,
            "problem_type": problem_type,
            "template_name": template_name,
            "timestamp": datetime.now().isoformat(),
        }
        
        self.prompt_history.append(prompt_record)
        
        # Keep only the last 1000 prompts
        if len(self.prompt_history) > 1000:
            self.prompt_history = self.prompt_history[-1000:]
    
    async def generate_optimization_prompt(
        self,
        code: str,
        problem: Dict,
        variation_index: int = 0,
    ) -> str:
        """
        Generate a prompt for code optimization.
        
        Args:
            code (str): Code to optimize
            problem (Dict): Problem definition
            variation_index (int, optional): Variation index
            
        Returns:
            str: Optimization prompt
        """
        if not self.initialized:
            await self.initialize()
        
        # Get optimization template
        template = self.templates.get("code_optimization")
        
        if not template:
            # Use a simple prompt
            return f"Optimize the following code:\n\n```python\n{code}\n```"
        
        # Get optimization goal
        optimization_goal = problem.get("optimization_metric", "efficiency")
        
        # Fill template variables
        template_vars = {
            "original_code": code,
            "optimization_goal": optimization_goal,
        }
        
        # Apply template
        prompt = template["template"]
        for var, value in template_vars.items():
            prompt = prompt.replace(f"{{{var}}}", str(value))
        
        # Add variation
        optimization_variations = [
            # Focus on time complexity
            "\nFocus on reducing time complexity.",
            # Focus on space complexity
            "\nFocus on reducing space complexity.",
            # Focus on readability
            "\nMaintain or improve readability while optimizing.",
            # Focus on algorithm selection
            "\nConsider using a different algorithm or data structure.",
            # Focus on parallelization
            "\nConsider opportunities for parallelization or vectorization.",
        ]
        
        if variation_index < len(optimization_variations):
            variation = optimization_variations[variation_index]
        else:
            variation = random.choice(optimization_variations)
        
        prompt = f"{prompt}{variation}"
        
        # Record prompt
        self._record_prompt(prompt, "code_optimization", template["name"])
        
        return prompt
    
    async def create_template(self, template_id: str, template: Dict) -> bool:
        """
        Create a new prompt template.
        
        Args:
            template_id (str): Template ID
            template (Dict): Template definition
            
        Returns:
            bool: Success flag
        """
        if not self.initialized:
            await self.initialize()
        
        try:
            # Validate template
            required_fields = ["name", "description", "template", "variables"]
            for field in required_fields:
                if field not in template:
                    logger.error(f"Missing required field in template: {field}")
                    return False
            
            # Add template
            self.templates[template_id] = template
            
            # Save template to file
            template_path = os.path.join(self.templates_dir, f"{template_id}.json")
            with open(template_path, "w") as f:
                json.dump(template, f, indent=2)
            
            logger.info(f"Created template: {template_id}")
            return True
        
        except Exception as e:
            logger.exception(f"Error creating template: {e}")
            return False
    
    async def update_template(self, template_id: str, template: Dict) -> bool:
        """
        Update an existing prompt template.
        
        Args:
            template_id (str): Template ID
            template (Dict): Template definition
            
        Returns:
            bool: Success flag
        """
        if not self.initialized:
            await self.initialize()
        
        if template_id not in self.templates:
            logger.error(f"Template not found: {template_id}")
            return False
        
        try:
            # Update template
            self.templates[template_id] = template
            
            # Save template to file
            template_path = os.path.join(self.templates_dir, f"{template_id}.json")
            with open(template_path, "w") as f:
                json.dump(template, f, indent=2)
            
            logger.info(f"Updated template: {template_id}")
            return True
        
        except Exception as e:
            logger.exception(f"Error updating template: {e}")
            return False
    
    async def delete_template(self, template_id: str) -> bool:
        """
        Delete a prompt template.
        
        Args:
            template_id (str): Template ID
            
        Returns:
            bool: Success flag
        """
        if not self.initialized:
            await self.initialize()
        
        if template_id not in self.templates:
            logger.error(f"Template not found: {template_id}")
            return False
        
        try:
            # Delete template
            del self.templates[template_id]
            
            # Delete template file
            template_path = os.path.join(self.templates_dir, f"{template_id}.json")
            if os.path.exists(template_path):
                os.remove(template_path)
            
            logger.info(f"Deleted template: {template_id}")
            return True
        
        except Exception as e:
            logger.exception(f"Error deleting template: {e}")
            return False
    
    async def get_template(self, template_id: str) -> Optional[Dict]:
        """
        Get a prompt template.
        
        Args:
            template_id (str): Template ID
            
        Returns:
            Optional[Dict]: Template definition
        """
        if not self.initialized:
            await self.initialize()
        
        return self.templates.get(template_id)
