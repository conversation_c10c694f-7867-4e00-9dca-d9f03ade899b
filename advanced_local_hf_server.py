"""
Advanced Local Hugging Face API Server for UI-TARS.

This script creates a local API server that mimics the Hugging Face API
but uses local models. This version actually loads and uses the models.
"""
import os
import json
import logging
import argparse
import threading
from typing import Dict, List, Optional, Any, Union

from fastapi import Fast<PERSON><PERSON>, HTTPException, Request, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("advanced_local_hf_server")

# Create FastAPI app
app = FastAPI(title="Advanced Local Hugging Face API Server")

# Add CORS middleware to allow requests from UI-TARS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Define request models
class GenerationRequest(BaseModel):
    inputs: str
    parameters: Optional[Dict[str, Any]] = None

class ChatRequest(BaseModel):
    model: Optional[str] = None
    messages: List[Dict[str, str]]
    temperature: Optional[float] = 0.7
    max_tokens: Optional[int] = 1000
    stream: Optional[bool] = False

# Global variables
MODEL_PATH = "C:/Users/<USER>/models/UI-TARS-1.5-7B"
MODEL_NAME = "UI-TARS-1.5-7B"
USE_DUMMY_RESPONSES = False
MODEL_LOCK = threading.Lock()
MODEL = None

# Function to load the model
def load_model(model_path: str):
    """Load the model from the given path."""
    global MODEL
    
    if MODEL is not None:
        return MODEL
    
    with MODEL_LOCK:
        if MODEL is not None:
            return MODEL
        
        logger.info(f"Loading model from {model_path}...")
        
        try:
            # Try to import transformers
            from transformers import AutoModelForCausalLM, AutoTokenizer, pipeline
            
            # Check if the model path exists
            if not os.path.exists(model_path):
                logger.error(f"Model path does not exist: {model_path}")
                return None
            
            # Load the tokenizer and model
            tokenizer = AutoTokenizer.from_pretrained(model_path)
            model = AutoModelForCausalLM.from_pretrained(
                model_path,
                device_map="auto",
                load_in_4bit=True  # Use 4-bit quantization for efficiency
            )
            
            # Create a text generation pipeline
            pipe = pipeline(
                "text-generation",
                model=model,
                tokenizer=tokenizer,
                max_length=2048,
                do_sample=True,
                temperature=0.7,
                top_p=0.9
            )
            
            MODEL = {
                "pipe": pipe,
                "tokenizer": tokenizer,
                "model": model
            }
            
            logger.info("Model loaded successfully")
            return MODEL
        
        except Exception as e:
            logger.exception(f"Error loading model: {e}")
            return None

# Function to generate text
def generate_text(prompt: str, max_tokens: int = 1000, temperature: float = 0.7):
    """Generate text using the loaded model."""
    global MODEL
    
    if USE_DUMMY_RESPONSES:
        return "This is a dummy response from the local Hugging Face API server."
    
    # Load the model if not already loaded
    if MODEL is None:
        MODEL = load_model(MODEL_PATH)
    
    if MODEL is None:
        return "Error: Failed to load model."
    
    try:
        # Generate text
        result = MODEL["pipe"](
            prompt,
            max_length=max_tokens,
            do_sample=True,
            temperature=temperature,
            top_p=0.9
        )
        
        # Extract the generated text
        generated_text = result[0]["generated_text"]
        
        # Remove the prompt from the generated text
        if generated_text.startswith(prompt):
            generated_text = generated_text[len(prompt):].strip()
        
        return generated_text
    
    except Exception as e:
        logger.exception(f"Error generating text: {e}")
        return f"Error: {str(e)}"

# Routes
@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "name": "Advanced Local Hugging Face API Server",
        "version": "1.0.0",
        "description": "A local API server that mimics the Hugging Face API but uses local models.",
        "models": [MODEL_NAME],
        "status": "running"
    }

@app.post("/models/{model_name}/generate")
async def generate(model_name: str, request: GenerationRequest, background_tasks: BackgroundTasks):
    """Generate text using a local model."""
    logger.info(f"Generating text with model: {model_name}")
    logger.info(f"Input: {request.inputs}")
    
    # Get parameters
    parameters = request.parameters or {}
    max_tokens = parameters.get("max_tokens", 1000)
    temperature = parameters.get("temperature", 0.7)
    
    if USE_DUMMY_RESPONSES:
        return {
            "generated_text": "This is a dummy response from the local Hugging Face API server."
        }
    
    try:
        # Generate text
        generated_text = generate_text(request.inputs, max_tokens, temperature)
        
        return {
            "generated_text": generated_text
        }
    
    except Exception as e:
        logger.exception(f"Error generating text: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/chat/completions")
async def chat_completions(request: ChatRequest):
    """Chat completions endpoint for OpenAI-compatible API."""
    logger.info(f"Chat completion request: {request.model}")
    
    # Extract the messages
    messages = request.messages
    if not messages:
        raise HTTPException(status_code=400, detail="No messages provided")
    
    # Format the messages into a prompt
    prompt = ""
    for message in messages:
        role = message.get("role", "user")
        content = message.get("content", "")
        
        if role == "system":
            prompt += f"System: {content}\n\n"
        elif role == "user":
            prompt += f"User: {content}\n\n"
        elif role == "assistant":
            prompt += f"Assistant: {content}\n\n"
    
    prompt += "Assistant: "
    
    logger.info(f"Formatted prompt: {prompt}")
    
    if USE_DUMMY_RESPONSES:
        response_text = "This is a dummy response from the local Hugging Face API server."
    else:
        # Generate text
        response_text = generate_text(prompt, request.max_tokens, request.temperature)
    
    # Format the response in OpenAI-compatible format
    response = {
        "id": "chatcmpl-local-123456",
        "object": "chat.completion",
        "created": 1683000000,
        "model": request.model or MODEL_NAME,
        "choices": [
            {
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": response_text
                },
                "finish_reason": "stop"
            }
        ],
        "usage": {
            "prompt_tokens": len(prompt.split()),
            "completion_tokens": len(response_text.split()),
            "total_tokens": len(prompt.split()) + len(response_text.split())
        }
    }
    
    return response

@app.post("/models/{model_name}")
async def model_inference(model_name: str, request: Request):
    """Generic model inference endpoint."""
    logger.info(f"Model inference request for: {model_name}")
    
    # Parse the request body
    body = await request.json()
    logger.info(f"Request body: {body}")
    
    # Extract the input text
    input_text = body.get("inputs", "")
    if not input_text:
        raise HTTPException(status_code=400, detail="No input text provided")
    
    # Get parameters
    parameters = body.get("parameters", {})
    max_tokens = parameters.get("max_tokens", 1000)
    temperature = parameters.get("temperature", 0.7)
    
    if USE_DUMMY_RESPONSES:
        return {
            "generated_text": "This is a dummy response from the local Hugging Face API server."
        }
    
    try:
        # Generate text
        generated_text = generate_text(input_text, max_tokens, temperature)
        
        return {
            "generated_text": generated_text
        }
    
    except Exception as e:
        logger.exception(f"Error in model inference: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/models")
async def list_models():
    """List available models."""
    return {
        "models": [
            {
                "id": MODEL_NAME,
                "name": MODEL_NAME,
                "path": MODEL_PATH
            }
        ]
    }

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "ok"}

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Advanced Local Hugging Face API Server")
    parser.add_argument("--host", type=str, default="127.0.0.1", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    parser.add_argument("--model-path", type=str, default=MODEL_PATH, help="Path to the model")
    parser.add_argument("--model-name", type=str, default=MODEL_NAME, help="Name of the model")
    parser.add_argument("--dummy", action="store_true", help="Use dummy responses")
    parser.add_argument("--preload", action="store_true", help="Preload the model")
    
    args = parser.parse_args()
    
    global MODEL_PATH, MODEL_NAME, USE_DUMMY_RESPONSES
    MODEL_PATH = args.model_path
    MODEL_NAME = args.model_name
    USE_DUMMY_RESPONSES = args.dummy
    
    logger.info(f"Starting Advanced Local Hugging Face API Server")
    logger.info(f"Host: {args.host}")
    logger.info(f"Port: {args.port}")
    logger.info(f"Model Path: {MODEL_PATH}")
    logger.info(f"Model Name: {MODEL_NAME}")
    logger.info(f"Using Dummy Responses: {USE_DUMMY_RESPONSES}")
    
    # Preload the model if requested
    if args.preload and not USE_DUMMY_RESPONSES:
        logger.info("Preloading model...")
        load_model(MODEL_PATH)
    
    # Start the server
    uvicorn.run(app, host=args.host, port=args.port)

if __name__ == "__main__":
    main()
