"""
Insurance Agent for Flo Faction Insurance business operations.
"""
import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any
import json
import re

from agents.base_agent import BaseAgent
from core.logger import setup_logger
from llm.llm_router import LLMRouter

class InsuranceAgent(BaseAgent):
    """
    Agent specialized for insurance business operations.

    This agent handles tasks related to Flo Faction Insurance,
    including customer inquiries, policy management, and claims processing.
    """

    def __init__(
        self,
        agent_id: str,
        config: Dict,
        state_manager,
        message_queue,
        shutdown_event
    ):
        """Initialize the insurance agent."""
        super().__init__(agent_id, config, state_manager, message_queue, shutdown_event)

        # Insurance-specific configuration
        self.llm_provider = config.get("llm_provider", "anthropic")
        self.llm_router = None

        # Insurance business data
        self.policies = {}
        self.customers = {}
        self.claims = {}
        self.quotes = {}

        # Agent capabilities
        self.capabilities = [
            "policy_lookup",
            "customer_lookup",
            "claim_processing",
            "quote_generation",
            "renewal_notification",
            "payment_processing",
            "risk_assessment",
            "fraud_detection",
            "policy_recommendation",
            "customer_segmentation",
            "premium_calculation",
            "underwriting_automation",
            "claims_analysis",
            "predictive_analytics",
            "customer_retention",
            "cross_selling",
            "regulatory_compliance",
            "document_processing",
            "email_communication",
            "customer_support",
            "market_analysis",
            "competitor_analysis",
            "product_development",
            "agent_management",
            "commission_calculation",
            "secure_data_processing",
            "quantum_risk_modeling",
            "machine_learning_predictions",
            "advanced_fraud_detection",
            "natural_language_processing",
        ]

    async def initialize(self):
        """Initialize the insurance agent."""
        await super().initialize()

        # Initialize LLM router
        self.llm_router = LLMRouter()
        await self.llm_router.initialize()

        # Load insurance business data
        await self._load_business_data()

        self.logger.info(f"Insurance agent initialized with provider: {self.llm_provider}")

    async def _load_business_data(self):
        """Load insurance business data from state manager."""
        # Load policies
        policies_data = await self.state_manager.get_state("insurance", "policies")
        if policies_data:
            self.policies = policies_data
            self.logger.info(f"Loaded {len(self.policies)} policies")

        # Load customers
        customers_data = await self.state_manager.get_state("insurance", "customers")
        if customers_data:
            self.customers = customers_data
            self.logger.info(f"Loaded {len(self.customers)} customers")

        # Load claims
        claims_data = await self.state_manager.get_state("insurance", "claims")
        if claims_data:
            self.claims = claims_data
            self.logger.info(f"Loaded {len(self.claims)} claims")

        # Load quotes
        quotes_data = await self.state_manager.get_state("insurance", "quotes")
        if quotes_data:
            self.quotes = quotes_data
            self.logger.info(f"Loaded {len(self.quotes)} quotes")

    async def execute_cycle(self):
        """Execute one cycle of the insurance agent's logic."""
        self.logger.debug("Executing insurance agent cycle")

        try:
            # Check for pending tasks
            pending_tasks = await self.state_manager.get_state("insurance", "pending_tasks")
            if pending_tasks:
                for task_id, task in pending_tasks.items():
                    if task.get("status") == "pending":
                        await self._process_task(task_id, task)

            # Check for policy renewals
            await self._check_policy_renewals()

            # Check for pending claims
            await self._check_pending_claims()

            # Check for pending quotes
            await self._check_pending_quotes()

            # Update state with any changes
            await self._save_business_data()

        except Exception as e:
            self.logger.exception(f"Error in insurance agent cycle: {e}")

    async def _process_task(self, task_id: str, task: Dict):
        """
        Process a pending task.

        Args:
            task_id (str): Task identifier
            task (Dict): Task data
        """
        task_type = task.get("type")
        self.logger.info(f"Processing task: {task_id} ({task_type})")

        try:
            if task_type == "policy_inquiry":
                await self._handle_policy_inquiry(task)
            elif task_type == "claim_submission":
                await self._handle_claim_submission(task)
            elif task_type == "quote_request":
                await self._handle_quote_request(task)
            elif task_type == "customer_update":
                await self._handle_customer_update(task)
            else:
                self.logger.warning(f"Unknown task type: {task_type}")
                return

            # Update task status
            task["status"] = "completed"
            task["completed_at"] = datetime.now().isoformat()

            # Update pending tasks
            pending_tasks = await self.state_manager.get_state("insurance", "pending_tasks") or {}
            pending_tasks[task_id] = task
            await self.state_manager.update_state("insurance", "pending_tasks", pending_tasks)

        except Exception as e:
            self.logger.exception(f"Error processing task {task_id}: {e}")

            # Update task status
            task["status"] = "error"
            task["error"] = str(e)

            # Update pending tasks
            pending_tasks = await self.state_manager.get_state("insurance", "pending_tasks") or {}
            pending_tasks[task_id] = task
            await self.state_manager.update_state("insurance", "pending_tasks", pending_tasks)

    async def _handle_policy_inquiry(self, task: Dict):
        """
        Handle a policy inquiry task.

        Args:
            task (Dict): Task data
        """
        policy_id = task.get("policy_id")
        customer_id = task.get("customer_id")
        inquiry = task.get("inquiry")

        # Get policy information
        policy = self.policies.get(policy_id)
        if not policy:
            raise ValueError(f"Policy not found: {policy_id}")

        # Get customer information
        customer = self.customers.get(customer_id)
        if not customer:
            raise ValueError(f"Customer not found: {customer_id}")

        # Generate response using LLM
        prompt = f"""
        You are an insurance assistant for Flo Faction Insurance. Please respond to the following inquiry:

        Customer: {customer.get('name')}
        Policy ID: {policy_id}
        Policy Type: {policy.get('type')}
        Policy Status: {policy.get('status')}

        Inquiry: {inquiry}

        Please provide a helpful, accurate, and professional response.
        """

        response = await self.llm_router.generate_text(
            prompt=prompt,
            provider=self.llm_provider,
            max_tokens=500,
            temperature=0.7
        )

        # Store response in task
        task["response"] = response.get("text")

        # If communication is requested, send response to customer
        if task.get("send_response", False):
            # This would integrate with communication services
            self.logger.info(f"Would send response to customer {customer_id}")

    async def _handle_claim_submission(self, task: Dict):
        """
        Handle a claim submission task.

        Args:
            task (Dict): Task data
        """
        policy_id = task.get("policy_id")
        customer_id = task.get("customer_id")
        claim_details = task.get("claim_details", {})

        # Validate policy
        policy = self.policies.get(policy_id)
        if not policy:
            raise ValueError(f"Policy not found: {policy_id}")

        if policy.get("status") != "active":
            raise ValueError(f"Policy is not active: {policy_id}")

        # Create new claim
        claim_id = f"CLM-{datetime.now().strftime('%Y%m%d')}-{len(self.claims) + 1:04d}"

        new_claim = {
            "claim_id": claim_id,
            "policy_id": policy_id,
            "customer_id": customer_id,
            "submission_date": datetime.now().isoformat(),
            "status": "pending_review",
            "details": claim_details,
            "history": [
                {
                    "timestamp": datetime.now().isoformat(),
                    "status": "submitted",
                    "notes": "Claim submitted for review",
                }
            ]
        }

        # Add claim to database
        self.claims[claim_id] = new_claim

        # Update task with claim ID
        task["claim_id"] = claim_id

        # Notify customer of claim submission
        if task.get("send_notification", False):
            # This would integrate with communication services
            self.logger.info(f"Would send claim submission notification to customer {customer_id}")

    async def _handle_quote_request(self, task: Dict):
        """
        Handle a quote request task.

        Args:
            task (Dict): Task data
        """
        customer_info = task.get("customer_info", {})
        coverage_details = task.get("coverage_details", {})

        # Generate quote ID
        quote_id = f"QTE-{datetime.now().strftime('%Y%m%d')}-{len(self.quotes) + 1:04d}"

        # Calculate premium (simplified example)
        base_premium = 500.0
        coverage_factor = coverage_details.get("coverage_amount", 100000) / 100000
        risk_factor = 1.0

        if coverage_details.get("type") == "health":
            base_premium = 300.0
            if customer_info.get("age", 30) > 50:
                risk_factor = 1.5
        elif coverage_details.get("type") == "life":
            base_premium = 200.0
            if customer_info.get("age", 30) > 60:
                risk_factor = 2.0

        premium = base_premium * coverage_factor * risk_factor

        # Create quote
        new_quote = {
            "quote_id": quote_id,
            "customer_info": customer_info,
            "coverage_details": coverage_details,
            "premium": premium,
            "created_at": datetime.now().isoformat(),
            "expires_at": datetime.now().replace(
                month=datetime.now().month + 1 if datetime.now().month < 12 else 1,
                year=datetime.now().year if datetime.now().month < 12 else datetime.now().year + 1
            ).isoformat(),
            "status": "active"
        }

        # Add quote to database
        self.quotes[quote_id] = new_quote

        # Update task with quote ID and premium
        task["quote_id"] = quote_id
        task["premium"] = premium

        # Send quote to customer if requested
        if task.get("send_quote", False):
            # This would integrate with communication services
            self.logger.info(f"Would send quote to customer: {customer_info.get('email')}")

    async def _handle_customer_update(self, task: Dict):
        """
        Handle a customer update task.

        Args:
            task (Dict): Task data
        """
        customer_id = task.get("customer_id")
        updates = task.get("updates", {})

        # Check if customer exists
        if customer_id not in self.customers:
            # Create new customer
            self.customers[customer_id] = {
                "customer_id": customer_id,
                "created_at": datetime.now().isoformat(),
            }

        # Update customer information
        customer = self.customers[customer_id]
        for key, value in updates.items():
            customer[key] = value

        # Add update timestamp
        customer["updated_at"] = datetime.now().isoformat()

        # Save updated customer
        self.customers[customer_id] = customer

        # Update task with customer ID
        task["customer_id"] = customer_id

    async def _check_policy_renewals(self):
        """Check for policies due for renewal."""
        current_date = datetime.now().date()

        for policy_id, policy in self.policies.items():
            # Skip if policy is not active
            if policy.get("status") != "active":
                continue

            # Check if policy is due for renewal (within 30 days)
            expiry_date = datetime.fromisoformat(policy.get("expiry_date")).date()
            days_to_expiry = (expiry_date - current_date).days

            if 0 < days_to_expiry <= 30:
                # Check if renewal notification already sent
                if not policy.get("renewal_notification_sent"):
                    self.logger.info(f"Policy {policy_id} due for renewal in {days_to_expiry} days")

                    # Mark notification as sent
                    policy["renewal_notification_sent"] = True
                    self.policies[policy_id] = policy

                    # Create renewal task
                    await self._create_renewal_task(policy_id, policy)

    async def _create_renewal_task(self, policy_id: str, policy: Dict):
        """
        Create a renewal task for a policy.

        Args:
            policy_id (str): Policy identifier
            policy (Dict): Policy data
        """
        task_id = f"TASK-{datetime.now().strftime('%Y%m%d')}-{policy_id}"

        task = {
            "task_id": task_id,
            "type": "policy_renewal",
            "policy_id": policy_id,
            "customer_id": policy.get("customer_id"),
            "created_at": datetime.now().isoformat(),
            "status": "pending",
            "expiry_date": policy.get("expiry_date"),
        }

        # Add task to pending tasks
        pending_tasks = await self.state_manager.get_state("insurance", "pending_tasks") or {}
        pending_tasks[task_id] = task
        await self.state_manager.update_state("insurance", "pending_tasks", pending_tasks)

    async def _check_pending_claims(self):
        """Check for pending claims that need processing."""
        for claim_id, claim in self.claims.items():
            if claim.get("status") == "pending_review":
                # In a real system, this would involve more complex logic
                # For now, we'll just update the status
                self.logger.info(f"Processing pending claim: {claim_id}")

                claim["status"] = "under_review"
                claim["history"].append({
                    "timestamp": datetime.now().isoformat(),
                    "status": "under_review",
                    "notes": "Claim is now under review by an adjuster",
                })

                self.claims[claim_id] = claim

    async def _check_pending_quotes(self):
        """Check for pending quotes that need follow-up."""
        current_date = datetime.now()

        for quote_id, quote in self.quotes.items():
            if quote.get("status") == "active":
                # Check if quote is about to expire (within 7 days)
                expiry_date = datetime.fromisoformat(quote.get("expires_at"))
                days_to_expiry = (expiry_date - current_date).days

                if 0 < days_to_expiry <= 7 and not quote.get("reminder_sent"):
                    self.logger.info(f"Quote {quote_id} expires in {days_to_expiry} days")

                    # Mark reminder as sent
                    quote["reminder_sent"] = True
                    self.quotes[quote_id] = quote

                    # In a real system, send a reminder to the customer

    async def _save_business_data(self):
        """Save insurance business data to state manager."""
        # Save policies
        await self.state_manager.update_state("insurance", "policies", self.policies)

        # Save customers
        await self.state_manager.update_state("insurance", "customers", self.customers)

        # Save claims
        await self.state_manager.update_state("insurance", "claims", self.claims)

        # Save quotes
        await self.state_manager.update_state("insurance", "quotes", self.quotes)

    async def run_risk_assessment(self, customer_id: str = None, policy_id: str = None) -> Dict:
        """
        Run advanced risk assessment for a customer or policy.

        Args:
            customer_id (str, optional): Customer identifier
            policy_id (str, optional): Policy identifier

        Returns:
            Dict: Risk assessment results
        """
        self.logger.info(f"Running risk assessment for customer: {customer_id}, policy: {policy_id}")

        # Get advanced reasoning service
        advanced_reasoning = self.get_service("advanced_reasoning")
        if not advanced_reasoning:
            raise ValueError("Advanced reasoning service not available")

        # Determine what to assess
        if policy_id:
            # Get policy
            policy = self.policies.get(policy_id)
            if not policy:
                raise ValueError(f"Policy not found: {policy_id}")

            # Get customer for this policy
            customer_id = policy.get("customer_id")
            customer = self.customers.get(customer_id)

            # Assess policy risk
            return await self._assess_policy_risk(policy, customer, advanced_reasoning)

        elif customer_id:
            # Get customer
            customer = self.customers.get(customer_id)
            if not customer:
                raise ValueError(f"Customer not found: {customer_id}")

            # Assess customer risk
            return await self._assess_customer_risk(customer, advanced_reasoning)

        else:
            raise ValueError("Either customer_id or policy_id must be provided")

    async def _assess_policy_risk(self, policy: Dict, customer: Dict, reasoning_service) -> Dict:
        """
        Assess risk for a specific policy.

        Args:
            policy (Dict): Policy data
            customer (Dict): Customer data
            reasoning_service: Advanced reasoning service

        Returns:
            Dict: Risk assessment results
        """
        # Prepare context for reasoning
        policy_type = policy.get("type", "unknown")
        coverage_amount = policy.get("coverage_amount", 0)

        context = f"""
        Policy Information:
        - Policy ID: {policy.get("policy_id")}
        - Type: {policy_type}
        - Status: {policy.get("status")}
        - Coverage Amount: ${coverage_amount}
        - Start Date: {policy.get("start_date")}
        - Expiry Date: {policy.get("expiry_date")}

        Customer Information:
        - Customer ID: {customer.get("customer_id")}
        - Name: {customer.get("name")}
        - Age: {customer.get("age")}
        - Address: {customer.get("address")}
        - Occupation: {customer.get("occupation")}
        - Credit Score: {customer.get("credit_score")}

        Claims History:
        """

        # Add claims history
        customer_claims = [claim for claim_id, claim in self.claims.items()
                          if claim.get("customer_id") == customer.get("customer_id")]

        if customer_claims:
            for claim in customer_claims:
                context += f"""
                - Claim ID: {claim.get("claim_id")}
                - Submission Date: {claim.get("submission_date")}
                - Status: {claim.get("status")}
                - Amount: ${claim.get("details", {}).get("amount", 0)}
                - Type: {claim.get("details", {}).get("type", "unknown")}
                """
        else:
            context += "- No claims history\n"

        # Perform causal reasoning for risk assessment
        question = f"What is the risk level for this {policy_type} policy and what factors contribute to this risk assessment?"

        reasoning_result = await reasoning_service.causal_reasoning(
            context=context,
            question=question,
            variables=["policy type", "coverage amount", "customer age", "claims history", "credit score"]
        )

        # Extract risk assessment
        risk_assessment = self._extract_risk_assessment(reasoning_result.get("reasoning", ""))

        # Create result
        result = {
            "policy_id": policy.get("policy_id"),
            "customer_id": customer.get("customer_id"),
            "timestamp": datetime.now().isoformat(),
            "risk_level": risk_assessment.get("risk_level"),
            "risk_score": risk_assessment.get("risk_score"),
            "risk_factors": risk_assessment.get("risk_factors", []),
            "reasoning": reasoning_result.get("reasoning"),
            "recommendations": risk_assessment.get("recommendations", []),
        }

        # Update policy with risk assessment
        policy["risk_assessment"] = {
            "risk_level": risk_assessment.get("risk_level"),
            "risk_score": risk_assessment.get("risk_score"),
            "last_assessed": datetime.now().isoformat(),
        }

        # Save updated policy
        self.policies[policy.get("policy_id")] = policy

        return result

    async def _assess_customer_risk(self, customer: Dict, reasoning_service) -> Dict:
        """
        Assess risk for a customer across all policies.

        Args:
            customer (Dict): Customer data
            reasoning_service: Advanced reasoning service

        Returns:
            Dict: Risk assessment results
        """
        # Get customer policies
        customer_id = customer.get("customer_id")
        customer_policies = {policy_id: policy for policy_id, policy in self.policies.items()
                            if policy.get("customer_id") == customer_id}

        # Get customer claims
        customer_claims = {claim_id: claim for claim_id, claim in self.claims.items()
                          if claim.get("customer_id") == customer_id}

        # Prepare context for reasoning
        context = f"""
        Customer Information:
        - Customer ID: {customer.get("customer_id")}
        - Name: {customer.get("name")}
        - Age: {customer.get("age")}
        - Address: {customer.get("address")}
        - Occupation: {customer.get("occupation")}
        - Credit Score: {customer.get("credit_score")}

        Policies:
        """

        if customer_policies:
            for policy_id, policy in customer_policies.items():
                context += f"""
                - Policy ID: {policy_id}
                - Type: {policy.get("type")}
                - Status: {policy.get("status")}
                - Coverage Amount: ${policy.get("coverage_amount")}
                """
        else:
            context += "- No active policies\n"

        context += "\nClaims History:\n"

        if customer_claims:
            for claim_id, claim in customer_claims.items():
                context += f"""
                - Claim ID: {claim_id}
                - Submission Date: {claim.get("submission_date")}
                - Status: {claim.get("status")}
                - Amount: ${claim.get("details", {}).get("amount", 0)}
                - Type: {claim.get("details", {}).get("type", "unknown")}
                """
        else:
            context += "- No claims history\n"

        # Perform causal reasoning for risk assessment
        question = "What is the overall risk level for this customer and what factors contribute to this risk assessment?"

        reasoning_result = await reasoning_service.causal_reasoning(
            context=context,
            question=question,
            variables=["age", "occupation", "credit score", "claims history", "policy types"]
        )

        # Extract risk assessment
        risk_assessment = self._extract_risk_assessment(reasoning_result.get("reasoning", ""))

        # Create result
        result = {
            "customer_id": customer_id,
            "timestamp": datetime.now().isoformat(),
            "risk_level": risk_assessment.get("risk_level"),
            "risk_score": risk_assessment.get("risk_score"),
            "risk_factors": risk_assessment.get("risk_factors", []),
            "reasoning": reasoning_result.get("reasoning"),
            "recommendations": risk_assessment.get("recommendations", []),
            "policies": list(customer_policies.keys()),
        }

        # Update customer with risk assessment
        customer["risk_assessment"] = {
            "risk_level": risk_assessment.get("risk_level"),
            "risk_score": risk_assessment.get("risk_score"),
            "last_assessed": datetime.now().isoformat(),
        }

        # Save updated customer
        self.customers[customer_id] = customer

        return result

    def _extract_risk_assessment(self, reasoning: str) -> Dict:
        """
        Extract risk assessment from reasoning text.

        Args:
            reasoning (str): Reasoning text

        Returns:
            Dict: Risk assessment information
        """
        # This is a simplified implementation
        # In a real system, this would use NLP to extract risk assessment

        # Look for risk level mentions
        risk_levels = {
            "very low": 1,
            "low": 2,
            "medium": 3,
            "moderate": 3,
            "high": 4,
            "very high": 5,
            "extreme": 5
        }

        found_level = None
        risk_score = 3  # Default medium risk

        for level, score in risk_levels.items():
            if level in reasoning.lower():
                found_level = level
                risk_score = score
                break

        if not found_level:
            found_level = "medium"  # Default

        # Extract risk factors
        risk_factors = []
        lines = reasoning.split("\n")
        for line in lines:
            if "risk factor" in line.lower() or "concern" in line.lower():
                risk_factors.append(line.strip())

        # Extract recommendations
        recommendations = []
        for line in lines:
            if "recommend" in line.lower() or "suggest" in line.lower() or "should" in line.lower():
                recommendations.append(line.strip())

        return {
            "risk_level": found_level,
            "risk_score": risk_score,
            "risk_factors": risk_factors,
            "recommendations": recommendations,
        }

    async def run_fraud_detection(self, claim_id: str = None) -> Dict:
        """
        Run advanced fraud detection on claims.

        Args:
            claim_id (str, optional): Claim identifier

        Returns:
            Dict: Fraud detection results
        """
        self.logger.info(f"Running fraud detection for claim: {claim_id}")

        # Get advanced reasoning service
        advanced_reasoning = self.get_service("advanced_reasoning")
        if not advanced_reasoning:
            raise ValueError("Advanced reasoning service not available")

        # Get quantum connector for advanced pattern detection
        quantum_connector = self.get_service("quantum_connector")

        # Determine what to analyze
        if claim_id:
            # Get specific claim
            claim = self.claims.get(claim_id)
            if not claim:
                raise ValueError(f"Claim not found: {claim_id}")

            claims_to_analyze = {claim_id: claim}
        else:
            # Analyze all pending claims
            claims_to_analyze = {claim_id: claim for claim_id, claim in self.claims.items()
                               if claim.get("status") in ["pending_review", "under_review"]}

        # Initialize results
        results = {
            "timestamp": datetime.now().isoformat(),
            "claims": {},
            "summary": {
                "total_claims": len(claims_to_analyze),
                "fraudulent_claims": 0,
                "suspicious_claims": 0,
                "legitimate_claims": 0,
            }
        }

        # Analyze each claim
        for cid, claim in claims_to_analyze.items():
            try:
                # Get policy and customer
                policy_id = claim.get("policy_id")
                policy = self.policies.get(policy_id)

                customer_id = claim.get("customer_id")
                customer = self.customers.get(customer_id)

                # Prepare context for fraud detection
                context = f"""
                Claim Information:
                - Claim ID: {claim.get("claim_id")}
                - Submission Date: {claim.get("submission_date")}
                - Status: {claim.get("status")}
                - Details: {json.dumps(claim.get("details", {}), indent=2)}

                Policy Information:
                - Policy ID: {policy.get("policy_id")}
                - Type: {policy.get("type")}
                - Coverage Amount: ${policy.get("coverage_amount")}
                - Start Date: {policy.get("start_date")}
                - Expiry Date: {policy.get("expiry_date")}

                Customer Information:
                - Customer ID: {customer.get("customer_id")}
                - Name: {customer.get("name")}
                - Age: {customer.get("age")}
                - Address: {customer.get("address")}
                - Occupation: {customer.get("occupation")}

                Claims History:
                """

                # Add previous claims
                previous_claims = [c for cid, c in self.claims.items()
                                 if c.get("customer_id") == customer_id and cid != claim.get("claim_id")]

                if previous_claims:
                    for prev_claim in previous_claims:
                        context += f"""
                        - Claim ID: {prev_claim.get("claim_id")}
                        - Submission Date: {prev_claim.get("submission_date")}
                        - Status: {prev_claim.get("status")}
                        - Amount: ${prev_claim.get("details", {}).get("amount", 0)}
                        - Type: {prev_claim.get("details", {}).get("type", "unknown")}
                        """
                else:
                    context += "- No previous claims\n"

                # Use quantum pattern detection if available
                quantum_patterns = None
                if quantum_connector:
                    try:
                        # Run quantum pattern detection algorithm
                        quantum_result = await quantum_connector.run_quantum_algorithm(
                            algorithm="pattern_detection",
                            parameters={
                                "claim_data": claim,
                                "historical_claims": previous_claims,
                                "customer_data": customer,
                            }
                        )

                        quantum_patterns = quantum_result.get("detected_patterns", [])

                        # Add quantum patterns to context
                        if quantum_patterns:
                            context += "\nQuantum Pattern Analysis:\n"
                            for pattern in quantum_patterns:
                                context += f"- {pattern}\n"
                    except Exception as e:
                        self.logger.error(f"Error in quantum pattern detection: {e}")

                # Perform counterfactual reasoning for fraud detection
                question = "Is this claim legitimate or potentially fraudulent? What indicators support this assessment?"

                reasoning_result = await advanced_reasoning.counterfactual_reasoning(
                    context=context,
                    factual_outcome="The claim was submitted as described",
                    counterfactual_condition="If the claim is fraudulent, what inconsistencies or red flags would be present?"
                )

                # Extract fraud assessment
                fraud_assessment = self._extract_fraud_assessment(reasoning_result.get("reasoning", ""))

                # Add quantum patterns to fraud indicators if available
                if quantum_patterns:
                    fraud_assessment["fraud_indicators"].extend(quantum_patterns)

                # Store result
                results["claims"][cid] = {
                    "fraud_probability": fraud_assessment.get("fraud_probability"),
                    "assessment": fraud_assessment.get("assessment"),
                    "fraud_indicators": fraud_assessment.get("fraud_indicators", []),
                    "legitimacy_indicators": fraud_assessment.get("legitimacy_indicators", []),
                    "reasoning": reasoning_result.get("reasoning"),
                    "recommended_action": fraud_assessment.get("recommended_action"),
                }

                # Update summary
                if fraud_assessment.get("assessment") == "fraudulent":
                    results["summary"]["fraudulent_claims"] += 1
                elif fraud_assessment.get("assessment") == "suspicious":
                    results["summary"]["suspicious_claims"] += 1
                else:
                    results["summary"]["legitimate_claims"] += 1

                # Update claim with fraud assessment
                claim["fraud_assessment"] = {
                    "assessment": fraud_assessment.get("assessment"),
                    "fraud_probability": fraud_assessment.get("fraud_probability"),
                    "last_assessed": datetime.now().isoformat(),
                }

                # Update claim status if fraudulent
                if fraud_assessment.get("assessment") == "fraudulent":
                    claim["status"] = "flagged_for_investigation"
                    claim["history"].append({
                        "timestamp": datetime.now().isoformat(),
                        "status": "flagged_for_investigation",
                        "notes": "Flagged by fraud detection system",
                    })

                # Save updated claim
                self.claims[cid] = claim

            except Exception as e:
                self.logger.error(f"Error analyzing claim {cid}: {e}")
                results["claims"][cid] = {
                    "error": str(e)
                }

        return results

    def _extract_fraud_assessment(self, reasoning: str) -> Dict:
        """
        Extract fraud assessment from reasoning text.

        Args:
            reasoning (str): Reasoning text

        Returns:
            Dict: Fraud assessment information
        """
        # This is a simplified implementation
        # In a real system, this would use NLP to extract fraud assessment

        # Count fraud-related words
        fraud_words = ["fraud", "fraudulent", "suspicious", "inconsistent", "red flag",
                      "misrepresent", "false", "fabricated", "exaggerated"]

        legitimacy_words = ["legitimate", "consistent", "valid", "reasonable",
                           "justified", "authentic", "genuine", "supported"]

        fraud_count = sum(reasoning.lower().count(word) for word in fraud_words)
        legitimacy_count = sum(reasoning.lower().count(word) for word in legitimacy_words)

        # Calculate fraud probability
        total_count = fraud_count + legitimacy_count
        if total_count > 0:
            fraud_probability = fraud_count / total_count
        else:
            fraud_probability = 0.5  # Default to 50% if no indicators found

        # Determine assessment
        if fraud_probability > 0.7:
            assessment = "fraudulent"
            recommended_action = "investigate"
        elif fraud_probability > 0.4:
            assessment = "suspicious"
            recommended_action = "review"
        else:
            assessment = "legitimate"
            recommended_action = "process"

        # Extract fraud indicators
        fraud_indicators = []
        legitimacy_indicators = []

        lines = reasoning.split("\n")
        for line in lines:
            line = line.strip()
            if any(word in line.lower() for word in fraud_words):
                fraud_indicators.append(line)
            elif any(word in line.lower() for word in legitimacy_words):
                legitimacy_indicators.append(line)

        return {
            "assessment": assessment,
            "fraud_probability": fraud_probability,
            "fraud_indicators": fraud_indicators,
            "legitimacy_indicators": legitimacy_indicators,
            "recommended_action": recommended_action,
        }

    async def handle_query(self, message: Dict):
        """
        Handle a query message.

        Args:
            message (Dict): Query message
        """
        query = message.get("content", {}).get("query")
        query_type = message.get("content", {}).get("type")

        if query_type == "policy":
            policy_id = message.get("content", {}).get("policy_id")
            if policy_id in self.policies:
                await self.send_message(
                    message.get("sender_id"),
                    "response",
                    {
                        "query": query,
                        "policy": self.policies[policy_id],
                    }
                )
            else:
                await self.send_message(
                    message.get("sender_id"),
                    "error",
                    {
                        "query": query,
                        "error": f"Policy not found: {policy_id}",
                    }
                )
        elif query_type == "customer":
            customer_id = message.get("content", {}).get("customer_id")
            if customer_id in self.customers:
                await self.send_message(
                    message.get("sender_id"),
                    "response",
                    {
                        "query": query,
                        "customer": self.customers[customer_id],
                    }
                )
            else:
                await self.send_message(
                    message.get("sender_id"),
                    "error",
                    {
                        "query": query,
                        "error": f"Customer not found: {customer_id}",
                    }
                )
        elif query_type == "claim":
            claim_id = message.get("content", {}).get("claim_id")
            if claim_id in self.claims:
                await self.send_message(
                    message.get("sender_id"),
                    "response",
                    {
                        "query": query,
                        "claim": self.claims[claim_id],
                    }
                )
            else:
                await self.send_message(
                    message.get("sender_id"),
                    "error",
                    {
                        "query": query,
                        "error": f"Claim not found: {claim_id}",
                    }
                )
        else:
            await super().handle_query(message)
