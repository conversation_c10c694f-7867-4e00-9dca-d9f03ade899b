"""
Setup script for the Multi-Agent AI System.
"""
import os
import sys
import shutil
from pathlib import Path
import argparse

def create_directories():
    """Create necessary directories."""
    directories = ["data", "logs", "models"]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"Created directory: {directory}")

def create_env_file():
    """Create .env file from .env.example if it doesn't exist."""
    if not os.path.exists(".env") and os.path.exists(".env.example"):
        shutil.copy(".env.example", ".env")
        print("Created .env file from .env.example")
        print("Please edit .env file with your API keys and settings")
    elif os.path.exists(".env"):
        print(".env file already exists")
    else:
        print("Warning: .env.example file not found")

def setup_virtual_environment():
    """Set up a virtual environment."""
    if os.path.exists("venv"):
        print("Virtual environment already exists")
        return
    
    print("Creating virtual environment...")
    os.system(f"{sys.executable} -m venv venv")
    
    # Determine the pip command based on the platform
    if sys.platform == "win32":
        pip_cmd = ".\\venv\\Scripts\\pip"
    else:
        pip_cmd = "./venv/bin/pip"
    
    print("Upgrading pip...")
    os.system(f"{pip_cmd} install --upgrade pip")
    
    print("Installing dependencies...")
    os.system(f"{pip_cmd} install -r requirements.txt")
    
    print("Virtual environment setup complete")

def main():
    """Main setup function."""
    parser = argparse.ArgumentParser(description="Setup the Multi-Agent AI System")
    parser.add_argument("--no-venv", action="store_true", help="Skip virtual environment setup")
    args = parser.parse_args()
    
    print("Setting up Multi-Agent AI System...")
    
    create_directories()
    create_env_file()
    
    if not args.no_venv:
        setup_virtual_environment()
    
    print("\nSetup complete!")
    print("\nTo start the system:")
    
    if sys.platform == "win32" and not args.no_venv:
        print("1. Activate the virtual environment: .\\venv\\Scripts\\activate")
    elif not args.no_venv:
        print("1. Activate the virtual environment: source venv/bin/activate")
    
    print("2. Edit the .env file with your API keys and settings")
    print("3. Run the system: python main.py")

if __name__ == "__main__":
    main()
