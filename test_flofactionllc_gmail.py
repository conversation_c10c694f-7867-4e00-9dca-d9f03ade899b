"""
Test Gmail <NAME_EMAIL>.
This script helps you test Gmail authentication <NAME_EMAIL> account.
"""
import os
import sys
import pickle
import webbrowser
from pathlib import Path

try:
    from google.auth.transport.requests import Request
    from google.oauth2.credentials import Credentials
    from google_auth_oauthlib.flow import InstalledAppFlow
    from googleapiclient.discovery import build
    from googleapiclient.errors import HttpError
except ImportError:
    print("Required packages not found. Installing...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", 
                          "google-auth", "google-auth-oauthlib", 
                          "google-auth-httplib2", "google-api-python-client"])
    
    from google.auth.transport.requests import Request
    from google.oauth2.credentials import Credentials
    from google_auth_oauthlib.flow import InstalledAppFlow
    from googleapiclient.discovery import build
    from googleapiclient.errors import HttpError

def test_gmail_auth():
    """
    Test Gmail <NAME_EMAIL>.
    """
    print("\n=== Testing Gmail <NAME_EMAIL> ===")
    
    # Gmail API scopes
    SCOPES = [
        'https://www.googleapis.com/auth/gmail.readonly',
        'https://www.googleapis.com/auth/gmail.send',
        'https://www.googleapis.com/auth/gmail.compose',
        'https://www.googleapis.com/auth/gmail.modify'
    ]
    
    # Credentials file path
    credentials_path = 'credentials/gmail_Flofactionllc_at_gmail_dot_com_credentials.json'
    token_path = 'credentials/gmail_Flofactionllc_at_gmail_dot_com_token.pickle'
    
    # Check if credentials file exists
    if not os.path.exists(credentials_path):
        print(f"Error: Credentials file not found at {credentials_path}")
        return False
    
    # Remove token file if it exists (to force re-authentication)
    if os.path.exists(token_path):
        os.remove(token_path)
        print(f"Removed existing token file: {token_path}")
    
    try:
        creds = None
        
        # Get new credentials
        flow = InstalledAppFlow.from_client_secrets_file(credentials_path, SCOPES)
        creds = flow.run_local_server(port=0)
        
        # Save the credentials for the next run
        with open(token_path, 'wb') as token:
            pickle.dump(creds, token)
        
        # Build the service
        service = build('gmail', 'v1', credentials=creds)
        
        # Get user profile
        profile = service.users().getProfile(userId='me').execute()
        user_email = profile.get('emailAddress')
        
        print(f"Successfully authenticated as {user_email}")
        
        # List a few messages to test the connection
        results = service.users().messages().list(userId='me', maxResults=5).execute()
        messages = results.get('messages', [])
        
        if not messages:
            print("No messages found.")
        else:
            print(f"Found {len(messages)} messages.")
            
            # Get the first message details
            msg = service.users().messages().get(userId='me', id=messages[0]['id']).execute()
            headers = msg['payload']['headers']
            subject = next((header['value'] for header in headers if header['name'] == 'Subject'), 'No subject')
            sender = next((header['value'] for header in headers if header['name'] == 'From'), 'Unknown sender')
            
            print(f"Latest message: '{subject}' from {sender}")
        
        return True
    
    except Exception as e:
        print(f"Error testing Gmail authentication: {e}")
        
        # Provide troubleshooting guidance
        print("\nTroubleshooting steps:")
        print("1. Make sure your OAuth consent screen is properly configured")
        print("2. Add all required scopes to your OAuth consent screen")
        print("3. Add <EMAIL> as a test user")
        print("4. Add http://localhost:0/ to the authorized redirect URIs")
        print("5. Make sure the Gmail API is enabled for your project")
        
        # Open Google Cloud Console
        print("\nOpening Google Cloud Console in your browser...")
        webbrowser.open("https://console.cloud.google.com/apis/credentials")
        
        return False

if __name__ == "__main__":
    test_gmail_auth()
