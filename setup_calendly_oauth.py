"""
Setup Calendly OAuth

This script helps you set up OAuth for Calendly.
"""
import webbrowser
import http.server
import socketserver
import threading
import urllib.parse
import json
import os
from datetime import datetime

# Create credentials directory if it doesn't exist
os.makedirs("credentials/calendly", exist_ok=True)

# Ask for client ID
client_id = input("Enter your Calendly client ID: ")
redirect_uri = input("Enter your redirect URI (default: http://localhost:8000/callback): ") or "http://localhost:8000/callback"

# Save client ID to credentials file
try:
    if os.path.exists("credentials/calendly/calendly.json"):
        with open("credentials/calendly/calendly.json", "r") as f:
            credentials = json.load(f)
    else:
        credentials = {}
    
    credentials["client_id"] = client_id
    credentials["redirect_uri"] = redirect_uri
    
    with open("credentials/calendly/calendly.json", "w") as f:
        json.dump(credentials, f, indent=4)
    
    print("Client ID saved to credentials/calendly/calendly.json")
except Exception as e:
    print(f"Error saving client ID: {e}")

# Create authorization URL
auth_url = f"https://auth.calendly.com/oauth/authorize?client_id={client_id}&response_type=code&redirect_uri={urllib.parse.quote(redirect_uri)}"

print(f"\nAuthorization URL: {auth_url}")
print("\nOpening browser to authorize application...")

# Open browser to authorization URL
webbrowser.open(auth_url)

# Create a simple HTTP server to handle the callback
class CallbackHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        # Parse the query string
        query = urllib.parse.urlparse(self.path).query
        params = urllib.parse.parse_qs(query)
        
        # Check if the code parameter is present
        if "code" in params:
            code = params["code"][0]
            
            # Send response
            self.send_response(200)
            self.send_header("Content-type", "text/html")
            self.end_headers()
            
            # Create response HTML
            html = f"""
            <html>
            <head>
                <title>Calendly OAuth Authorization</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }}
                    .container {{ max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }}
                    h1 {{ color: #333; }}
                    .code {{ background-color: #f5f5f5; padding: 10px; border-radius: 3px; font-family: monospace; word-break: break-all; }}
                    .instructions {{ margin-top: 20px; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>Authorization Successful!</h1>
                    <p>Your authorization code is:</p>
                    <div class="code">{code}</div>
                    <div class="instructions">
                        <p>Please copy this code and paste it into the terminal where you ran the script.</p>
                        <p>You can close this window after copying the code.</p>
                    </div>
                </div>
            </body>
            </html>
            """
            
            self.wfile.write(html.encode())
            
            # Save the code to a file
            try:
                with open("credentials/calendly/auth_code.txt", "w") as f:
                    f.write(code)
                
                print(f"\nAuthorization code received: {code}")
                print("Code saved to credentials/calendly/auth_code.txt")
                print("\nNext steps:")
                print("1. Run get_calendly_token.py to exchange the authorization code for an access token")
                print("2. Use the access token to make API calls to Calendly")
            except Exception as e:
                print(f"Error saving authorization code: {e}")
            
            # Shutdown the server
            threading.Thread(target=self.server.shutdown).start()
        else:
            # Send error response
            self.send_response(400)
            self.send_header("Content-type", "text/html")
            self.end_headers()
            
            html = """
            <html>
            <head>
                <title>Calendly OAuth Authorization</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
                    h1 { color: #c00; }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>Authorization Failed</h1>
                    <p>No authorization code was received.</p>
                    <p>Please try again.</p>
                </div>
            </body>
            </html>
            """
            
            self.wfile.write(html.encode())

# Parse the redirect URI to get the host and port
parsed_uri = urllib.parse.urlparse(redirect_uri)
host = parsed_uri.hostname or "localhost"
port = parsed_uri.port or 8000

# Start the server
try:
    with socketserver.TCPServer((host, port), CallbackHandler) as httpd:
        print(f"\nStarting server at {host}:{port}")
        print("Waiting for authorization callback...")
        httpd.serve_forever()
except KeyboardInterrupt:
    print("\nServer stopped by user")
except Exception as e:
    print(f"\nError starting server: {e}")
    print("\nPlease manually copy the authorization code from the browser and run get_calendly_token.py")
