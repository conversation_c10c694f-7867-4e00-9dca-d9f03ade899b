"""
Test script for the workflow engine.
"""
import asyncio
import json
import logging
import os
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("test_workflow_engine")

# Import workflow engine
from core.workflow_engine import WorkflowEngine
from core.agent_manager import AgentManager
from core.state_manager import StateManager

class MockAgent:
    """Mock agent for testing."""
    
    def __init__(self, agent_id, name):
        self.agent_id = agent_id
        self.name = name
        self.logger = logging.getLogger(f"agent.{agent_id}")
    
    async def execute_action(self, action, parameters):
        """Execute a mock action."""
        self.logger.info(f"Executing action '{action}' with parameters: {parameters}")
        return {
            "status": "success",
            "action": action,
            "parameters": parameters,
            "result": f"Mock result for {action}"
        }

class MockAgentManager:
    """Mock agent manager for testing."""
    
    def __init__(self):
        self.agents = {}
        self.services = {}
        self.logger = logging.getLogger("agent_manager")
    
    def register_agent(self, agent_id, agent):
        """Register a mock agent."""
        self.agents[agent_id] = agent
        self.logger.info(f"Registered agent '{agent_id}'")
    
    def get_agent(self, agent_id):
        """Get a mock agent."""
        if agent_id in self.agents:
            return self.agents[agent_id]
        return None
    
    def register_service(self, service_id, service):
        """Register a mock service."""
        self.services[service_id] = service
        self.logger.info(f"Registered service '{service_id}'")
    
    def get_service(self, service_id):
        """Get a mock service."""
        if service_id in self.services:
            return self.services[service_id]
        return None

async def test_workflow_engine():
    """Test the workflow engine."""
    logger.info("Starting workflow engine test")
    
    # Create mock agents
    agent_manager = MockAgentManager()
    agent_manager.register_agent("multi_account_email_agent", MockAgent("multi_account_email_agent", "Multi-Account Email Agent"))
    agent_manager.register_agent("insurance_lead_agent", MockAgent("insurance_lead_agent", "Insurance Lead Agent"))
    agent_manager.register_agent("insurance_agent", MockAgent("insurance_agent", "Insurance Agent"))
    agent_manager.register_agent("agent_coordinator", MockAgent("agent_coordinator", "Agent Coordinator"))
    agent_manager.register_agent("communication_agent", MockAgent("communication_agent", "Communication Agent"))
    
    # Create state manager
    state_manager = StateManager()
    await state_manager.initialize()
    
    # Create workflow engine
    workflow_engine = WorkflowEngine(agent_manager, state_manager)
    
    # Initialize workflow engine
    await workflow_engine.initialize()
    
    # Check if workflows were loaded
    logger.info(f"Loaded workflows: {list(workflow_engine.workflows.keys())}")
    
    # Trigger a workflow
    if "email_processing" in workflow_engine.workflows:
        logger.info("Triggering email_processing workflow")
        await workflow_engine.trigger_workflow("email_processing", {
            "type": "command",
            "command": "process_emails",
            "timestamp": "2023-06-01T12:00:00Z"
        })
    else:
        logger.warning("email_processing workflow not found")
    
    # Trigger an event
    logger.info("Triggering new_email event")
    await workflow_engine.trigger_event("new_email", {
        "account": "<EMAIL>",
        "email_id": "mock_email_123",
        "from": "<EMAIL>",
        "subject": "Test Email",
        "body": "This is a test email"
    })
    
    # Wait for workflows to complete
    logger.info("Waiting for workflows to complete")
    await asyncio.sleep(5)
    
    # Check active workflows
    logger.info(f"Active workflows: {list(workflow_engine.active_workflows.keys())}")
    
    # Shutdown workflow engine
    await workflow_engine.shutdown()
    
    logger.info("Workflow engine test completed")

if __name__ == "__main__":
    asyncio.run(test_workflow_engine())
