"""
Hugging Face service for the Multi-Agent AI System.
"""
import asyncio
from typing import Dict, List, Optional, Any, Union
import aiohttp
import os
from datetime import datetime
import json
import re

from core.logger import setup_logger
import config

# Set up logger
logger = setup_logger("huggingface_service")

class HuggingFaceService:
    """
    Service for interacting with the Hugging Face Hub.
    
    This service provides functionality for:
    - Searching for models
    - Getting model information
    - Downloading models
    - Managing model deployments
    """
    
    def __init__(self, config: Dict):
        """
        Initialize the Hugging Face service.
        
        Args:
            config (Dict): Service configuration
        """
        self.api_key = config.get("api_key", "")
        self.api_url = "https://huggingface.co/api"
        self.enabled = config.get("enabled", False)
        self.cache_dir = config.get("cache_dir", "data/huggingface_cache")
        self.inference_endpoints = config.get("inference_endpoints", {})
        
        # Create cache directory if it doesn't exist
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # Validate configuration
        if not self.api_key:
            logger.warning("Hugging Face API key not provided")
            self.enabled = False
    
    async def search_models(
        self, 
        query: str, 
        task: Optional[str] = None, 
        library: Optional[str] = None,
        sort: str = "downloads",
        limit: int = 10
    ) -> Dict:
        """
        Search for models on the Hugging Face Hub.
        
        Args:
            query (str): Search query
            task (Optional[str]): Filter by task (e.g., text-generation, translation)
            library (Optional[str]): Filter by library (e.g., transformers, diffusers)
            sort (str): Sort by (downloads, likes, modified)
            limit (int): Maximum number of results
            
        Returns:
            Dict: Search results
        """
        if not self.enabled:
            return {"error": "Hugging Face service is not enabled"}
        
        # Build search query
        params = {
            "search": query,
            "sort": sort,
            "limit": limit,
        }
        
        if task:
            params["filter"] = f"task:{task}"
        
        if library:
            if "filter" in params:
                params["filter"] += f",library:{library}"
            else:
                params["filter"] = f"library:{library}"
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.api_url}/models",
                    headers=self._get_headers(),
                    params=params,
                ) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        error_text = await response.text()
                        logger.error(f"Failed to search models: {response.status} - {error_text}")
                        return {
                            "error": f"API error: {response.status}",
                            "details": error_text,
                        }
        except Exception as e:
            logger.exception(f"Error searching models: {e}")
            return {"error": f"Failed to search models: {str(e)}"}
    
    async def get_model_info(self, model_id: str) -> Dict:
        """
        Get information about a model.
        
        Args:
            model_id (str): Model ID (e.g., "gpt2", "facebook/bart-large")
            
        Returns:
            Dict: Model information
        """
        if not self.enabled:
            return {"error": "Hugging Face service is not enabled"}
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.api_url}/models/{model_id}",
                    headers=self._get_headers(),
                ) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        error_text = await response.text()
                        logger.error(f"Failed to get model info: {response.status} - {error_text}")
                        return {
                            "error": f"API error: {response.status}",
                            "details": error_text,
                        }
        except Exception as e:
            logger.exception(f"Error getting model info: {e}")
            return {"error": f"Failed to get model info: {str(e)}"}
    
    async def get_model_tags(self, model_id: str) -> Dict:
        """
        Get tags for a model.
        
        Args:
            model_id (str): Model ID
            
        Returns:
            Dict: Model tags
        """
        if not self.enabled:
            return {"error": "Hugging Face service is not enabled"}
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.api_url}/models/{model_id}/tags",
                    headers=self._get_headers(),
                ) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        error_text = await response.text()
                        logger.error(f"Failed to get model tags: {response.status} - {error_text}")
                        return {
                            "error": f"API error: {response.status}",
                            "details": error_text,
                        }
        except Exception as e:
            logger.exception(f"Error getting model tags: {e}")
            return {"error": f"Failed to get model tags: {str(e)}"}
    
    async def get_model_readme(self, model_id: str) -> Dict:
        """
        Get the README for a model.
        
        Args:
            model_id (str): Model ID
            
        Returns:
            Dict: Model README
        """
        if not self.enabled:
            return {"error": "Hugging Face service is not enabled"}
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.api_url}/models/{model_id}/readme",
                    headers=self._get_headers(),
                ) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        error_text = await response.text()
                        logger.error(f"Failed to get model README: {response.status} - {error_text}")
                        return {
                            "error": f"API error: {response.status}",
                            "details": error_text,
                        }
        except Exception as e:
            logger.exception(f"Error getting model README: {e}")
            return {"error": f"Failed to get model README: {str(e)}"}
    
    async def search_datasets(
        self, 
        query: str, 
        task: Optional[str] = None,
        sort: str = "downloads",
        limit: int = 10
    ) -> Dict:
        """
        Search for datasets on the Hugging Face Hub.
        
        Args:
            query (str): Search query
            task (Optional[str]): Filter by task
            sort (str): Sort by (downloads, likes, modified)
            limit (int): Maximum number of results
            
        Returns:
            Dict: Search results
        """
        if not self.enabled:
            return {"error": "Hugging Face service is not enabled"}
        
        # Build search query
        params = {
            "search": query,
            "sort": sort,
            "limit": limit,
        }
        
        if task:
            params["filter"] = f"task:{task}"
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.api_url}/datasets",
                    headers=self._get_headers(),
                    params=params,
                ) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        error_text = await response.text()
                        logger.error(f"Failed to search datasets: {response.status} - {error_text}")
                        return {
                            "error": f"API error: {response.status}",
                            "details": error_text,
                        }
        except Exception as e:
            logger.exception(f"Error searching datasets: {e}")
            return {"error": f"Failed to search datasets: {str(e)}"}
    
    async def get_inference_endpoints(self) -> Dict:
        """
        Get all inference endpoints.
        
        Returns:
            Dict: Inference endpoints
        """
        if not self.enabled:
            return {"error": "Hugging Face service is not enabled"}
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.api_url}/endpoints",
                    headers=self._get_headers(),
                ) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        error_text = await response.text()
                        logger.error(f"Failed to get inference endpoints: {response.status} - {error_text}")
                        return {
                            "error": f"API error: {response.status}",
                            "details": error_text,
                        }
        except Exception as e:
            logger.exception(f"Error getting inference endpoints: {e}")
            return {"error": f"Failed to get inference endpoints: {str(e)}"}
    
    async def get_model_card(self, model_id: str) -> Dict:
        """
        Get the model card for a model.
        
        Args:
            model_id (str): Model ID
            
        Returns:
            Dict: Model card
        """
        # Get model info
        model_info = await self.get_model_info(model_id)
        if "error" in model_info:
            return model_info
        
        # Get model tags
        model_tags = await self.get_model_tags(model_id)
        if "error" in model_tags:
            model_tags = {"tags": []}
        
        # Get model README
        model_readme = await self.get_model_readme(model_id)
        if "error" in model_readme:
            model_readme = {"readme": ""}
        
        # Combine information into a model card
        return {
            "id": model_id,
            "info": model_info,
            "tags": model_tags.get("tags", []),
            "readme": model_readme.get("readme", ""),
        }
    
    async def find_best_models_for_task(self, task: str, limit: int = 5) -> Dict:
        """
        Find the best models for a specific task.
        
        Args:
            task (str): Task to find models for
            limit (int): Maximum number of models to return
            
        Returns:
            Dict: Best models for the task
        """
        if not self.enabled:
            return {"error": "Hugging Face service is not enabled"}
        
        # Search for models with the specified task
        search_results = await self.search_models("", task=task, sort="downloads", limit=limit)
        
        if "error" in search_results:
            return search_results
        
        # Extract relevant information
        models = []
        for model in search_results:
            models.append({
                "id": model.get("id", ""),
                "name": model.get("modelId", ""),
                "downloads": model.get("downloads", 0),
                "likes": model.get("likes", 0),
                "tags": model.get("tags", []),
                "pipeline_tag": model.get("pipeline_tag", ""),
                "library": model.get("library", {}).get("id", ""),
            })
        
        return {
            "task": task,
            "models": models,
        }
    
    def _get_headers(self) -> Dict[str, str]:
        """
        Get headers for Hugging Face API requests.
        
        Returns:
            Dict[str, str]: Headers
        """
        return {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }
    
    def is_enabled(self) -> bool:
        """
        Check if the service is enabled.
        
        Returns:
            bool: True if enabled, False otherwise
        """
        return self.enabled


class HuggingFaceServiceFactory:
    """
    Factory for creating Hugging Face service.
    """
    
    @staticmethod
    def create_service() -> Optional[HuggingFaceService]:
        """
        Create a Hugging Face service.
        
        Returns:
            Optional[HuggingFaceService]: Hugging Face service instance
        """
        return HuggingFaceService(config.HUGGINGFACE_CONFIG)
