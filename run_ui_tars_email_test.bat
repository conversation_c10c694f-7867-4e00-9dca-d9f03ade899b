@echo off
echo UI-TARS Email Test
echo =================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Get UI-TARS path
set DEFAULT_PATH="C:\Program Files\UI-TARS\UI-TARS.exe"
set /p UI_TARS_PATH="Enter path to UI-TARS executable (default: %DEFAULT_PATH%): "
if "%UI_TARS_PATH%"=="" set UI_TARS_PATH=%DEFAULT_PATH%

REM Run the test script
echo.
echo Running UI-TARS email test with path: %UI_TARS_PATH%
echo.

python test_ui_tars_email_with_path.py --ui-tars-path "%UI_TARS_PATH%"

echo.
echo Done.
pause
