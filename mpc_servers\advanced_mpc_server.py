"""
Advanced Multi-Party Computation (MPC) Server for secure distributed computation.

This module implements an advanced MPC server with support for more complex
secure computations and integration with security tools.
"""
import asyncio
import json
import logging
import os
import secrets
import socket
import ssl
import time
import subprocess
import hashlib
from typing import Dict, List, Optional, Any, Callable, Union
import uuid
from datetime import datetime
import numpy as np

from core.logger import setup_logger

# Set up logger
logger = setup_logger("advanced_mpc_server")

class AdvancedMPCServer:
    """
    Advanced Multi-Party Computation Server for secure distributed computation.

    This class implements an advanced MPC server with support for more complex
    secure computations and integration with security tools.
    """

    def __init__(
        self,
        server_id: str,
        host: str = "0.0.0.0",
        port: int = 8767,
        use_ssl: bool = True,
        cert_file: Optional[str] = None,
        key_file: Optional[str] = None,
        security_tools_dir: Optional[str] = None,
    ):
        """
        Initialize the advanced MPC server.

        Args:
            server_id (str): Unique identifier for this server
            host (str): Host to bind to
            port (int): Port to bind to
            use_ssl (bool): Whether to use SSL
            cert_file (Optional[str]): Path to SSL certificate file
            key_file (Optional[str]): Path to SSL key file
            security_tools_dir (Optional[str]): Directory containing security tools
        """
        self.server_id = server_id
        self.host = host
        self.port = port
        self.use_ssl = use_ssl
        self.cert_file = cert_file
        self.key_file = key_file
        self.security_tools_dir = security_tools_dir or os.path.join(os.path.dirname(__file__), "../tools")

        # Server state
        self.running = False
        self.clients = {}
        self.computations = {}
        self.server = None

        # Security
        self.session_keys = {}

        # Supported computation types
        self.computation_handlers = {
            # Basic MPC computations
            "secure_sum": self._compute_secure_sum,
            "secure_average": self._compute_secure_average,
            "secure_min": self._compute_secure_min,
            "secure_max": self._compute_secure_max,
            "secure_set_intersection": self._compute_secure_set_intersection,

            # Advanced MPC computations
            "secure_linear_regression": self._compute_secure_linear_regression,
            "secure_kmeans": self._compute_secure_kmeans,
            "secure_decision_tree": self._compute_secure_decision_tree,
            "secure_neural_network": self._compute_secure_neural_network,

            # Security tool integrations
            "secure_password_audit": self._compute_secure_password_audit,
            "secure_network_scan": self._compute_secure_network_scan,
            "secure_vulnerability_assessment": self._compute_secure_vulnerability_assessment,
        }

        logger.info(f"Advanced MPC Server {server_id} initialized")

    async def start(self):
        """Start the advanced MPC server."""
        if self.running:
            logger.warning("Advanced MPC Server already running")
            return

        try:
            # Create server
            server = await asyncio.start_server(
                self._handle_client,
                self.host,
                self.port,
                ssl=self._create_ssl_context() if self.use_ssl else None,
            )

            self.server = server
            self.running = True

            logger.info(f"Advanced MPC Server {self.server_id} started on {self.host}:{self.port}")

            # Serve forever
            async with server:
                await server.serve_forever()

        except Exception as e:
            logger.exception(f"Error starting Advanced MPC Server: {e}")
            self.running = False

    async def stop(self):
        """Stop the advanced MPC server."""
        if not self.running:
            logger.warning("Advanced MPC Server not running")
            return

        try:
            # Close server
            if self.server:
                self.server.close()
                await self.server.wait_closed()
                self.server = None

            self.running = False
            logger.info(f"Advanced MPC Server {self.server_id} stopped")

        except Exception as e:
            logger.exception(f"Error stopping Advanced MPC Server: {e}")

    def _create_ssl_context(self) -> ssl.SSLContext:
        """
        Create SSL context for secure connections.

        Returns:
            ssl.SSLContext: SSL context
        """
        if not self.cert_file or not self.key_file:
            raise ValueError("SSL certificate and key files must be provided")

        ssl_context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
        ssl_context.load_cert_chain(self.cert_file, self.key_file)
        ssl_context.check_hostname = False

        return ssl_context

    async def _handle_client(self, reader, writer):
        """
        Handle a client connection.

        Args:
            reader: StreamReader for reading from client
            writer: StreamWriter for writing to client
        """
        client_id = str(uuid.uuid4())
        peer_name = writer.get_extra_info("peername")
        logger.info(f"New client connected: {client_id} from {peer_name}")

        # Add client to clients dict
        self.clients[client_id] = {
            "reader": reader,
            "writer": writer,
            "peer_name": peer_name,
            "connected_at": datetime.now().isoformat(),
            "last_active": datetime.now().isoformat(),
        }

        try:
            while True:
                # Read message from client
                data = await reader.read(4096)
                if not data:
                    break

                # Update last active time
                self.clients[client_id]["last_active"] = datetime.now().isoformat()

                # Process message
                try:
                    message = json.loads(data.decode())
                    response = await self._process_message(client_id, message)

                    # Send response
                    writer.write(json.dumps(response).encode())
                    await writer.drain()

                except json.JSONDecodeError:
                    logger.warning(f"Invalid JSON from client {client_id}")
                    writer.write(json.dumps({"error": "Invalid JSON"}).encode())
                    await writer.drain()

                except Exception as e:
                    logger.exception(f"Error processing message from client {client_id}: {e}")
                    writer.write(json.dumps({"error": str(e)}).encode())
                    await writer.drain()

        except asyncio.CancelledError:
            pass
        except Exception as e:
            logger.exception(f"Error handling client {client_id}: {e}")
        finally:
            # Remove client from clients dict
            if client_id in self.clients:
                del self.clients[client_id]

            # Close connection
            writer.close()
            try:
                await writer.wait_closed()
            except:
                pass

            logger.info(f"Client disconnected: {client_id}")

    async def _process_message(self, client_id: str, message: Dict) -> Dict:
        """
        Process a message from a client.

        Args:
            client_id (str): Client identifier
            message (Dict): Message from client

        Returns:
            Dict: Response message
        """
        message_type = message.get("type")

        if message_type == "hello":
            return await self._handle_hello(client_id, message)
        elif message_type == "create_computation":
            return await self._handle_create_computation(client_id, message)
        elif message_type == "submit_input":
            return await self._handle_submit_input(client_id, message)
        elif message_type == "get_result":
            return await self._handle_get_result(client_id, message)
        elif message_type == "list_computations":
            return await self._handle_list_computations(client_id, message)
        elif message_type == "get_computation_status":
            return await self._handle_get_computation_status(client_id, message)
        else:
            return {"error": f"Unknown message type: {message_type}"}

    async def _handle_hello(self, client_id: str, message: Dict) -> Dict:
        """
        Handle a hello message from a client.

        Args:
            client_id (str): Client identifier
            message (Dict): Hello message

        Returns:
            Dict: Response message
        """
        # Generate session key
        session_key = secrets.token_hex(16)
        self.session_keys[client_id] = session_key

        # Update client info
        self.clients[client_id]["client_id"] = message.get("client_id", client_id)

        # Get supported computation types
        supported_computations = list(self.computation_handlers.keys())

        return {
            "type": "hello_response",
            "server_id": self.server_id,
            "session_key": session_key,
            "supported_computations": supported_computations,
            "timestamp": datetime.now().isoformat(),
        }

    async def _handle_create_computation(self, client_id: str, message: Dict) -> Dict:
        """
        Handle a create computation message from a client.

        Args:
            client_id (str): Client identifier
            message (Dict): Create computation message

        Returns:
            Dict: Response message
        """
        computation_type = message.get("computation_type")
        parameters = message.get("parameters", {})

        # Check if computation type is supported
        if computation_type not in self.computation_handlers:
            return {"error": f"Unsupported computation type: {computation_type}"}

        # Generate computation ID
        computation_id = str(uuid.uuid4())

        # Create computation
        self.computations[computation_id] = {
            "type": computation_type,
            "parameters": parameters,
            "creator": client_id,
            "created_at": datetime.now().isoformat(),
            "status": "created",
            "inputs": {},
            "result": None,
        }

        return {
            "type": "create_computation_response",
            "computation_id": computation_id,
            "status": "created",
            "timestamp": datetime.now().isoformat(),
        }

    async def _handle_submit_input(self, client_id: str, message: Dict) -> Dict:
        """
        Handle a submit input message from a client.

        Args:
            client_id (str): Client identifier
            message (Dict): Submit input message

        Returns:
            Dict: Response message
        """
        computation_id = message.get("computation_id")
        input_data = message.get("input_data")

        # Check if computation exists
        if computation_id not in self.computations:
            return {"error": f"Unknown computation: {computation_id}"}

        # Add input to computation
        self.computations[computation_id]["inputs"][client_id] = input_data

        # Check if all inputs are received
        computation = self.computations[computation_id]
        num_parties = computation["parameters"].get("num_parties", 2)

        if len(computation["inputs"]) >= num_parties:
            # Compute result
            computation["status"] = "computing"

            try:
                # Get computation handler
                handler = self.computation_handlers[computation["type"]]

                # Compute result
                result = await handler(computation)

                # Store result
                computation["result"] = result
                computation["status"] = "completed"
                computation["completed_at"] = datetime.now().isoformat()

            except Exception as e:
                logger.exception(f"Error computing result for {computation_id}: {e}")
                computation["status"] = "error"
                computation["error"] = str(e)

        return {
            "type": "submit_input_response",
            "computation_id": computation_id,
            "status": self.computations[computation_id]["status"],
            "timestamp": datetime.now().isoformat(),
        }

    async def _handle_get_result(self, client_id: str, message: Dict) -> Dict:
        """
        Handle a get result message from a client.

        Args:
            client_id (str): Client identifier
            message (Dict): Get result message

        Returns:
            Dict: Response message
        """
        computation_id = message.get("computation_id")

        # Check if computation exists
        if computation_id not in self.computations:
            return {"error": f"Unknown computation: {computation_id}"}

        # Get computation
        computation = self.computations[computation_id]

        # Check if computation is completed
        if computation["status"] != "completed":
            return {
                "type": "get_result_response",
                "computation_id": computation_id,
                "status": computation["status"],
                "timestamp": datetime.now().isoformat(),
            }

        return {
            "type": "get_result_response",
            "computation_id": computation_id,
            "status": "completed",
            "result": computation["result"],
            "timestamp": datetime.now().isoformat(),
        }

    async def _handle_list_computations(self, client_id: str, message: Dict) -> Dict:
        """
        Handle a list computations message from a client.

        Args:
            client_id (str): Client identifier
            message (Dict): List computations message

        Returns:
            Dict: Response message
        """
        # Get computations created by this client
        client_computations = {}
        for comp_id, comp in self.computations.items():
            if comp["creator"] == client_id:
                client_computations[comp_id] = {
                    "type": comp["type"],
                    "status": comp["status"],
                    "created_at": comp["created_at"],
                    "num_inputs": len(comp["inputs"]),
                }

        return {
            "type": "list_computations_response",
            "computations": client_computations,
            "timestamp": datetime.now().isoformat(),
        }

    async def _handle_get_computation_status(self, client_id: str, message: Dict) -> Dict:
        """
        Handle a get computation status message from a client.

        Args:
            client_id (str): Client identifier
            message (Dict): Get computation status message

        Returns:
            Dict: Response message
        """
        computation_id = message.get("computation_id")

        # Check if computation exists
        if computation_id not in self.computations:
            return {"error": f"Unknown computation: {computation_id}"}

        # Get computation
        computation = self.computations[computation_id]

        return {
            "type": "get_computation_status_response",
            "computation_id": computation_id,
            "status": computation["status"],
            "created_at": computation["created_at"],
            "num_inputs": len(computation["inputs"]),
            "timestamp": datetime.now().isoformat(),
        }

    # Basic MPC computation methods

    async def _compute_secure_sum(self, computation: Dict) -> Any:
        """
        Compute the sum of inputs securely.

        Args:
            computation (Dict): Computation data

        Returns:
            Any: Computation result
        """
        inputs = list(computation["inputs"].values())
        return sum(inputs)

    async def _compute_secure_average(self, computation: Dict) -> Any:
        """
        Compute the average of inputs securely.

        Args:
            computation (Dict): Computation data

        Returns:
            Any: Computation result
        """
        inputs = list(computation["inputs"].values())
        return sum(inputs) / len(inputs)

    async def _compute_secure_min(self, computation: Dict) -> Any:
        """
        Compute the minimum of inputs securely.

        Args:
            computation (Dict): Computation data

        Returns:
            Any: Computation result
        """
        inputs = list(computation["inputs"].values())
        return min(inputs)

    async def _compute_secure_max(self, computation: Dict) -> Any:
        """
        Compute the maximum of inputs securely.

        Args:
            computation (Dict): Computation data

        Returns:
            Any: Computation result
        """
        inputs = list(computation["inputs"].values())
        return max(inputs)

    async def _compute_secure_set_intersection(self, computation: Dict) -> Any:
        """
        Compute the intersection of sets securely.

        Args:
            computation (Dict): Computation data

        Returns:
            Any: Computation result
        """
        inputs = list(computation["inputs"].values())
        result = set(inputs[0])
        for input_set in inputs[1:]:
            result = result.intersection(set(input_set))
        return list(result)

    # Advanced MPC computation methods

    async def _compute_secure_linear_regression(self, computation: Dict) -> Any:
        """
        Compute linear regression securely.

        Args:
            computation (Dict): Computation data

        Returns:
            Any: Computation result
        """
        # Extract inputs
        inputs = list(computation["inputs"].values())

        # Combine data from all parties
        X_data = []
        y_data = []

        for input_data in inputs:
            X_data.extend(input_data.get("X", []))
            y_data.extend(input_data.get("y", []))

        # Convert to numpy arrays
        X = np.array(X_data)
        y = np.array(y_data)

        # Add constant term for intercept
        X = np.column_stack((np.ones(X.shape[0]), X))

        # Compute coefficients using normal equation
        # (X^T X)^(-1) X^T y
        try:
            coefficients = np.linalg.inv(X.T.dot(X)).dot(X.T).dot(y)

            # Convert to list for JSON serialization
            return {
                "coefficients": coefficients.tolist(),
                "num_samples": len(y),
                "num_features": X.shape[1] - 1,  # Subtract 1 for intercept
            }
        except np.linalg.LinAlgError:
            return {
                "error": "Singular matrix, cannot compute coefficients",
                "num_samples": len(y),
                "num_features": X.shape[1] - 1,
            }

    async def _compute_secure_kmeans(self, computation: Dict) -> Any:
        """
        Compute K-means clustering securely.

        Args:
            computation (Dict): Computation data

        Returns:
            Any: Computation result
        """
        # Extract inputs
        inputs = list(computation["inputs"].values())

        # Get parameters
        k = computation["parameters"].get("k", 3)
        max_iterations = computation["parameters"].get("max_iterations", 10)

        # Combine data from all parties
        data_points = []
        for input_data in inputs:
            data_points.extend(input_data.get("data", []))

        # Convert to numpy array
        data = np.array(data_points)

        # Initialize centroids randomly
        indices = np.random.choice(data.shape[0], k, replace=False)
        centroids = data[indices]

        # Run K-means algorithm
        for _ in range(max_iterations):
            # Assign points to clusters
            distances = np.sqrt(((data[:, np.newaxis, :] - centroids[np.newaxis, :, :]) ** 2).sum(axis=2))
            labels = np.argmin(distances, axis=1)

            # Update centroids
            new_centroids = np.array([data[labels == i].mean(axis=0) if np.sum(labels == i) > 0 else centroids[i] for i in range(k)])

            # Check convergence
            if np.all(centroids == new_centroids):
                break

            centroids = new_centroids

        # Count points in each cluster
        cluster_counts = [int(np.sum(labels == i)) for i in range(k)]

        return {
            "centroids": centroids.tolist(),
            "cluster_counts": cluster_counts,
            "num_points": len(data),
            "num_iterations": max_iterations,
        }

    async def _compute_secure_decision_tree(self, computation: Dict) -> Any:
        """
        Compute a simple decision tree securely.

        Args:
            computation (Dict): Computation data

        Returns:
            Any: Computation result
        """
        # This is a simplified implementation
        # In a real system, this would use a secure decision tree algorithm

        # Extract inputs
        inputs = list(computation["inputs"].values())

        # Combine data from all parties
        X_data = []
        y_data = []

        for input_data in inputs:
            X_data.extend(input_data.get("X", []))
            y_data.extend(input_data.get("y", []))

        # Convert to numpy arrays
        X = np.array(X_data)
        y = np.array(y_data)

        # Simplified decision tree (just a single split)
        # Find the feature with the highest correlation with the target
        correlations = []
        for i in range(X.shape[1]):
            corr = np.corrcoef(X[:, i], y)[0, 1]
            correlations.append((i, abs(corr)))

        # Sort by correlation
        correlations.sort(key=lambda x: x[1], reverse=True)

        # Get the feature with the highest correlation
        best_feature = correlations[0][0]

        # Find the best split point
        feature_values = X[:, best_feature]
        split_point = np.median(feature_values)

        # Split the data
        left_mask = feature_values <= split_point
        right_mask = ~left_mask

        # Calculate the majority class for each split
        left_class = np.bincount(y[left_mask].astype(int)).argmax() if np.any(left_mask) else 0
        right_class = np.bincount(y[right_mask].astype(int)).argmax() if np.any(right_mask) else 0

        # Create a simple decision tree
        tree = {
            "feature": int(best_feature),
            "split_point": float(split_point),
            "left_class": int(left_class),
            "right_class": int(right_class),
            "num_samples": len(y),
            "num_features": X.shape[1],
        }

        return tree

    async def _compute_secure_neural_network(self, computation: Dict) -> Any:
        """
        Compute a simple neural network securely.

        Args:
            computation (Dict): Computation data

        Returns:
            Any: Computation result
        """
        # This is a simplified implementation
        # In a real system, this would use a secure neural network algorithm

        # Extract inputs
        inputs = list(computation["inputs"].values())

        # Combine data from all parties
        X_data = []
        y_data = []

        for input_data in inputs:
            X_data.extend(input_data.get("X", []))
            y_data.extend(input_data.get("y", []))

        # Convert to numpy arrays
        X = np.array(X_data)
        y = np.array(y_data)

        # Get parameters
        hidden_size = computation["parameters"].get("hidden_size", 5)
        learning_rate = computation["parameters"].get("learning_rate", 0.01)
        num_iterations = computation["parameters"].get("num_iterations", 100)

        # Initialize weights
        input_size = X.shape[1]
        output_size = 1 if len(y.shape) == 1 else y.shape[1]

        np.random.seed(42)  # For reproducibility
        W1 = np.random.randn(input_size, hidden_size) * 0.01
        b1 = np.zeros((1, hidden_size))
        W2 = np.random.randn(hidden_size, output_size) * 0.01
        b2 = np.zeros((1, output_size))

        # Simplified training loop
        for _ in range(num_iterations):
            # Forward pass
            Z1 = X.dot(W1) + b1
            A1 = np.maximum(0, Z1)  # ReLU activation
            Z2 = A1.dot(W2) + b2

            # Compute loss (mean squared error)
            loss = np.mean((Z2 - y.reshape(-1, output_size)) ** 2)

            # Backward pass (simplified)
            dZ2 = 2 * (Z2 - y.reshape(-1, output_size)) / X.shape[0]
            dW2 = A1.T.dot(dZ2)
            db2 = np.sum(dZ2, axis=0, keepdims=True)

            dA1 = dZ2.dot(W2.T)
            dZ1 = dA1 * (Z1 > 0)  # ReLU derivative
            dW1 = X.T.dot(dZ1)
            db1 = np.sum(dZ1, axis=0, keepdims=True)

            # Update weights
            W1 -= learning_rate * dW1
            b1 -= learning_rate * db1
            W2 -= learning_rate * dW2
            b2 -= learning_rate * db2

        # Return the trained model
        model = {
            "W1": W1.tolist(),
            "b1": b1.tolist(),
            "W2": W2.tolist(),
            "b2": b2.tolist(),
            "input_size": input_size,
            "hidden_size": hidden_size,
            "output_size": output_size,
            "final_loss": float(loss),
            "num_samples": len(y),
        }

        return model

    # Security tool integration methods

    async def _compute_secure_password_audit(self, computation: Dict) -> Any:
        """
        Perform a secure password audit using John the Ripper.

        Args:
            computation (Dict): Computation data

        Returns:
            Any: Computation result
        """
        # Extract inputs
        inputs = list(computation["inputs"].values())

        # Combine password hashes from all parties
        password_hashes = []
        for input_data in inputs:
            password_hashes.extend(input_data.get("password_hashes", []))

        # Create a temporary file with password hashes
        temp_file = os.path.join(self.security_tools_dir, f"temp_hashes_{uuid.uuid4()}.txt")
        try:
            with open(temp_file, "w") as f:
                for hash_entry in password_hashes:
                    f.write(f"{hash_entry}\n")

            # Check if John the Ripper is available
            john_path = os.path.join(self.security_tools_dir, "john")
            if not os.path.exists(john_path):
                john_path = "john"  # Try system path

            # Run John the Ripper
            cmd = [john_path, "--format=raw-md5", temp_file]
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()

            # Parse results
            cracked_passwords = []
            if process.returncode == 0:
                output = stdout.decode()
                for line in output.splitlines():
                    if ":" in line:
                        parts = line.split(":")
                        if len(parts) >= 2:
                            username = parts[0]
                            password = parts[1]
                            cracked_passwords.append({"username": username, "password": password})

            return {
                "cracked_passwords": cracked_passwords,
                "total_passwords": len(password_hashes),
                "cracked_count": len(cracked_passwords),
                "cracked_percentage": len(cracked_passwords) / len(password_hashes) * 100 if password_hashes else 0,
            }

        finally:
            # Clean up temporary file
            if os.path.exists(temp_file):
                os.remove(temp_file)

    async def _compute_secure_network_scan(self, computation: Dict) -> Any:
        """
        Perform a secure network scan using Nmap.

        Args:
            computation (Dict): Computation data

        Returns:
            Any: Computation result
        """
        # Extract inputs
        inputs = list(computation["inputs"].values())

        # Combine targets from all parties
        targets = []
        for input_data in inputs:
            targets.extend(input_data.get("targets", []))

        # Get scan options
        scan_options = computation["parameters"].get("scan_options", "-sV -p 1-1000")

        # Check if Nmap is available
        nmap_path = os.path.join(self.security_tools_dir, "nmap")
        if not os.path.exists(nmap_path):
            nmap_path = "nmap"  # Try system path

        # Run Nmap for each target
        results = {}
        for target in targets:
            # Run Nmap
            cmd = [nmap_path] + scan_options.split() + [target]
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()

            # Parse results
            open_ports = []
            if process.returncode == 0:
                output = stdout.decode()
                for line in output.splitlines():
                    if "open" in line and "/tcp" in line:
                        parts = line.split()
                        if len(parts) >= 3:
                            port_info = parts[0].split("/")[0]
                            service = parts[2] if len(parts) > 2 else "unknown"
                            open_ports.append({"port": port_info, "service": service})

            results[target] = {
                "open_ports": open_ports,
                "num_open_ports": len(open_ports),
                "raw_output": stdout.decode(),
            }

        return {
            "scan_results": results,
            "num_targets": len(targets),
            "scan_options": scan_options,
        }

    async def _compute_secure_vulnerability_assessment(self, computation: Dict) -> Any:
        """
        Perform a secure vulnerability assessment.

        Args:
            computation (Dict): Computation data

        Returns:
            Any: Computation result
        """
        # Extract inputs
        inputs = list(computation["inputs"].values())

        # Combine targets from all parties
        targets = []
        for input_data in inputs:
            targets.extend(input_data.get("targets", []))

        # Get assessment type
        assessment_type = computation["parameters"].get("assessment_type", "web")

        if assessment_type == "web":
            # Use Nikto for web vulnerability assessment
            nikto_path = os.path.join(self.security_tools_dir, "nikto")
            if not os.path.exists(nikto_path):
                nikto_path = "nikto"  # Try system path

            # Run Nikto for each target
            results = {}
            for target in targets:
                # Run Nikto
                cmd = [nikto_path, "-h", target]
                process = await asyncio.create_subprocess_exec(
                    *cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, stderr = await process.communicate()

                # Parse results
                vulnerabilities = []
                if process.returncode == 0:
                    output = stdout.decode()
                    for line in output.splitlines():
                        if "+ " in line:  # Nikto vulnerability line
                            vulnerabilities.append(line.strip())

                results[target] = {
                    "vulnerabilities": vulnerabilities,
                    "num_vulnerabilities": len(vulnerabilities),
                    "raw_output": stdout.decode(),
                }

            return {
                "assessment_results": results,
                "num_targets": len(targets),
                "assessment_type": assessment_type,
            }

        else:
            return {
                "error": f"Unsupported assessment type: {assessment_type}",
                "supported_types": ["web"],
            }
