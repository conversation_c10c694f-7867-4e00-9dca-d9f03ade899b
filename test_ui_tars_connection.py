"""
Test UI-TARS Connection

This script tests the connection to UI-TARS 1.5 and runs a simple command.
"""
import os
import sys
import time
import asyncio
import logging
import argparse
import requests
from typing import Dict, Optional, Any

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("test_ui_tars_connection")

class UITarsConnectionTester:
    """Class to test the connection to UI-TARS 1.5."""

    def __init__(self,
                 api_url: str = "http://127.0.0.1:8000",
                 api_key: str = "dummy_key",
                 model_name: str = "UI-TARS-1.5-7B"):
        """
        Initialize the UI-TARS Connection Tester.

        Args:
            api_url (str): URL of the UI-TARS API
            api_key (str): API key for UI-TARS
            model_name (str): Name of the model to use
        """
        self.api_url = api_url
        self.api_key = api_key
        self.model_name = model_name
        self.session = None

    async def initialize(self) -> bool:
        """
        Initialize the connection tester.

        Returns:
            bool: True if initialization was successful, False otherwise
        """
        logger.info("Initializing UI-TARS Connection Tester")
        logger.info(f"Using API URL: {self.api_url}")

        # Create a session for API requests
        self.session = requests.Session()

        # Set up headers
        if self.api_key:
            self.session.headers.update({"Authorization": f"Bearer {self.api_key}"})

        # Check if UI-TARS API is running
        try:
            # Try to access the API - first try models endpoint
            base_url = self.api_url

            # Remove trailing /v1 if present for health check
            if base_url.endswith('/v1'):
                health_url = base_url.rsplit('/v1', 1)[0] + '/health'
            else:
                health_url = base_url + '/health'

            logger.info(f"Trying health endpoint: {health_url}")
            try:
                response = self.session.get(health_url, timeout=5)
                if response.status_code == 200:
                    logger.info("UI-TARS API is running (health endpoint)")
                    return True
            except requests.exceptions.RequestException as e:
                logger.warning(f"Health endpoint not available: {e}")

            # Try models endpoint
            models_url = base_url
            if not models_url.endswith('/models'):
                if not models_url.endswith('/v1'):
                    models_url = models_url + '/v1/models'
                else:
                    models_url = models_url + '/models'

            logger.info(f"Trying models endpoint: {models_url}")
            response = self.session.get(models_url, timeout=5)

            if response.status_code == 200:
                logger.info("UI-TARS API is running (models endpoint)")
                return True
            else:
                logger.error(f"UI-TARS API returned status code {response.status_code}")
                return False

        except requests.exceptions.RequestException as e:
            logger.error(f"Error connecting to UI-TARS API: {e}")

            # Try one more alternative - completions endpoint
            try:
                completions_url = self.api_url
                if not completions_url.endswith('/completions'):
                    if not completions_url.endswith('/v1'):
                        completions_url = completions_url + '/v1/completions'
                    else:
                        completions_url = completions_url + '/completions'

                logger.info(f"Trying completions endpoint: {completions_url}")

                # Just check if the endpoint exists, don't actually send a request
                response = self.session.options(completions_url, timeout=5)

                if response.status_code < 400:  # Any non-error status code
                    logger.info("UI-TARS API is running (completions endpoint)")
                    return True
                else:
                    logger.error(f"UI-TARS API returned status code {response.status_code}")
                    return False

            except requests.exceptions.RequestException as e2:
                logger.error(f"Error connecting to UI-TARS API (alternative endpoint): {e2}")
                return False

    async def test_simple_command(self) -> Dict[str, Any]:
        """
        Test a simple command with UI-TARS.

        Returns:
            Dict[str, Any]: Result of the command
        """
        logger.info("Testing simple command with UI-TARS")

        try:
            # Prepare the request
            payload = {
                "model": self.model_name,
                "prompt": "What is UI-TARS?",
                "max_tokens": 100
            }

            # Determine the completions endpoint
            completions_url = self.api_url
            if not completions_url.endswith('/completions'):
                if not completions_url.endswith('/v1'):
                    completions_url = completions_url + '/v1/completions'
                else:
                    completions_url = completions_url + '/completions'

            logger.info(f"Sending request to: {completions_url}")

            # Send the request
            response = self.session.post(completions_url, json=payload, timeout=30)

            if response.status_code == 200:
                logger.info("Simple command successful")
                return {"success": True, "response": response.json()}
            else:
                logger.error(f"Simple command failed with status code {response.status_code}")
                logger.error(f"Response content: {response.text}")
                return {"success": False, "error": f"Status code: {response.status_code}"}

        except requests.exceptions.RequestException as e:
            logger.error(f"Error sending simple command: {e}")
            return {"success": False, "error": str(e)}

    async def test_screenshot_command(self, screenshot_path: str) -> Dict[str, Any]:
        """
        Test a screenshot command with UI-TARS.

        Args:
            screenshot_path (str): Path to the screenshot

        Returns:
            Dict[str, Any]: Result of the command
        """
        logger.info("Testing screenshot command with UI-TARS")

        if not os.path.exists(screenshot_path):
            logger.error(f"Screenshot not found: {screenshot_path}")
            return {"success": False, "error": f"Screenshot not found: {screenshot_path}"}

        try:
            # Prepare the request
            files = {
                "file": (os.path.basename(screenshot_path), open(screenshot_path, "rb"), "image/png")
            }

            data = {
                "model": self.model_name,
                "prompt": "What do you see in this screenshot?",
                "max_tokens": 200
            }

            # Determine the vision endpoint
            vision_url = self.api_url
            if not vision_url.endswith('/vision'):
                if not vision_url.endswith('/v1'):
                    vision_url = vision_url + '/v1/vision'
                else:
                    vision_url = vision_url + '/vision'

            logger.info(f"Sending request to: {vision_url}")

            # Send the request
            response = self.session.post(vision_url, files=files, data=data, timeout=60)

            if response.status_code == 200:
                logger.info("Screenshot command successful")
                return {"success": True, "response": response.json()}
            else:
                logger.error(f"Screenshot command failed with status code {response.status_code}")
                logger.error(f"Response content: {response.text}")
                return {"success": False, "error": f"Status code: {response.status_code}"}

        except requests.exceptions.RequestException as e:
            logger.error(f"Error sending screenshot command: {e}")
            return {"success": False, "error": str(e)}
        except Exception as e:
            logger.error(f"Error processing screenshot: {e}")
            return {"success": False, "error": str(e)}

    async def shutdown(self) -> None:
        """Shut down the connection tester."""
        if self.session:
            self.session.close()
        logger.info("UI-TARS Connection Tester shut down")

async def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Test UI-TARS Connection")
    parser.add_argument("--api-url", type=str, default="http://127.0.0.1:8000", help="URL of the UI-TARS API")
    parser.add_argument("--api-key", type=str, default="dummy_key", help="API key for UI-TARS")
    parser.add_argument("--model", type=str, default="UI-TARS-1.5-7B", help="Name of the model to use")
    parser.add_argument("--screenshot", type=str, help="Path to a screenshot to test vision capabilities")

    args = parser.parse_args()

    # Create UI-TARS Connection Tester
    tester = UITarsConnectionTester(
        api_url=args.api_url,
        api_key=args.api_key,
        model_name=args.model
    )

    # Initialize
    initialized = await tester.initialize()
    if not initialized:
        logger.error("Failed to initialize UI-TARS Connection Tester")
        return

    try:
        # Test simple command
        result = await tester.test_simple_command()

        if result["success"]:
            logger.info("Simple command test passed")
            logger.info(f"Response: {result['response']}")
        else:
            logger.error(f"Simple command test failed: {result['error']}")

        # Test screenshot command if screenshot provided
        if args.screenshot:
            screenshot_result = await tester.test_screenshot_command(args.screenshot)

            if screenshot_result["success"]:
                logger.info("Screenshot command test passed")
                logger.info(f"Response: {screenshot_result['response']}")
            else:
                logger.error(f"Screenshot command test failed: {screenshot_result['error']}")

    finally:
        # Shut down
        await tester.shutdown()

if __name__ == "__main__":
    asyncio.run(main())
