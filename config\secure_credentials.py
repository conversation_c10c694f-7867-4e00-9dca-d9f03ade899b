"""
Secure credentials storage for cybersecurity testing.

This module provides secure storage and access to credentials
used for cybersecurity testing. All credentials are encrypted
at rest and only decrypted when needed.
"""
import os
import json
import base64
from pathlib import Path
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

# Set up encryption
def generate_key(password, salt=None):
    """Generate an encryption key from a password."""
    if salt is None:
        salt = os.urandom(16)
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=salt,
        iterations=100000,
    )
    key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
    return key, salt

def encrypt_data(data, key):
    """Encrypt data using the provided key."""
    f = Fernet(key)
    return f.encrypt(json.dumps(data).encode())

def decrypt_data(encrypted_data, key):
    """Decrypt data using the provided key."""
    f = Fernet(key)
    return json.loads(f.decrypt(encrypted_data).decode())

class CredentialManager:
    """
    Manager for securely storing and accessing credentials.
    """
    
    def __init__(self, master_password, credentials_file=None):
        """
        Initialize the credential manager.
        
        Args:
            master_password (str): Master password for encrypting/decrypting credentials
            credentials_file (str): Path to the credentials file
        """
        self.master_password = master_password
        
        if credentials_file:
            self.credentials_file = Path(credentials_file)
        else:
            self.credentials_file = Path(__file__).parent / "credentials.enc"
        
        # Initialize credentials
        self.credentials = {}
        self.salt = None
        self.key = None
        
        # Load credentials if file exists
        if self.credentials_file.exists():
            self._load_credentials()
        else:
            # Generate new key and save empty credentials
            self.key, self.salt = generate_key(self.master_password)
            self._save_credentials()
    
    def _load_credentials(self):
        """Load credentials from file."""
        with open(self.credentials_file, "rb") as f:
            # First 16 bytes are the salt
            self.salt = f.read(16)
            encrypted_data = f.read()
        
        # Generate key from password and salt
        self.key, _ = generate_key(self.master_password, self.salt)
        
        # Decrypt credentials
        try:
            self.credentials = decrypt_data(encrypted_data, self.key)
        except Exception as e:
            raise ValueError(f"Failed to decrypt credentials: {e}")
    
    def _save_credentials(self):
        """Save credentials to file."""
        # Encrypt credentials
        encrypted_data = encrypt_data(self.credentials, self.key)
        
        # Save salt and encrypted data
        with open(self.credentials_file, "wb") as f:
            f.write(self.salt)
            f.write(encrypted_data)
    
    def add_credential(self, service, username, password, additional_info=None):
        """
        Add a credential.
        
        Args:
            service (str): Service name
            username (str): Username
            password (str): Password
            additional_info (dict): Additional information
        """
        self.credentials[service] = {
            "username": username,
            "password": password,
            "additional_info": additional_info or {},
        }
        
        self._save_credentials()
    
    def get_credential(self, service):
        """
        Get a credential.
        
        Args:
            service (str): Service name
            
        Returns:
            dict: Credential information
        """
        return self.credentials.get(service)
    
    def remove_credential(self, service):
        """
        Remove a credential.
        
        Args:
            service (str): Service name
        """
        if service in self.credentials:
            del self.credentials[service]
            self._save_credentials()
    
    def list_services(self):
        """
        List all services with stored credentials.
        
        Returns:
            list: List of service names
        """
        return list(self.credentials.keys())

# Example usage:
# credential_manager = CredentialManager("your_master_password")
# credential_manager.add_credential("example.com", "username", "password")
# credential = credential_manager.get_credential("example.com")
