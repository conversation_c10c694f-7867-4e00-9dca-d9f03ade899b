@echo off
REM Contact <PERSON><PERSON>ript
REM This script runs the contact_alyssa_real.py script to send emails, texts, and voicemails to Alyssa

echo.
echo ===================================
echo    Contact <PERSON><PERSON>
echo ===================================
echo.

echo This script will send the following to <PERSON><PERSON>:
echo  1. Initial <NAME_EMAIL>
echo  2. Quote <NAME_EMAIL>
echo  3. Text message from (*************
echo  4. Voicemail from (************* using ElevenLabs female voice
echo.

REM Ask for confirmation
set /p CONFIRM="Proceed with outreach? (y/n): "
if /i not "%CONFIRM%"=="y" (
    echo Outreach cancelled.
    goto end
)

echo.
echo Running outreach for Alyssa Chirinos...
echo.

REM Run the command
python contact_alyssa_real.py --browser chrome

echo.
echo Outreach completed.
echo.

:end
pause
