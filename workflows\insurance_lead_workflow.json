{"name": "insurance_lead_workflow", "description": "Workflow for processing and following up with insurance leads", "version": "1.0.0", "triggers": [{"type": "schedule", "schedule": "0 9 * * 1-5", "description": "Process leads every weekday at 9 AM"}, {"type": "event", "event": "new_lead", "description": "Triggered when a new lead is received"}, {"type": "command", "command": "process_leads", "description": "Manually triggered lead processing"}], "steps": [{"id": "get_new_leads", "agent": "insurance_lead_agent", "action": "get_new_leads", "parameters": {"max_leads": 5, "days_since_contact": 7}, "next": {"condition": "result.leads.length > 0", "true": "qualify_leads", "false": "end"}}, {"id": "qualify_leads", "agent": "insurance_lead_agent", "action": "qualify_leads", "parameters": {"leads": "${previous.result.leads}"}, "next": "prioritize_leads"}, {"id": "prioritize_leads", "agent": "insurance_lead_agent", "action": "prioritize_leads", "parameters": {"qualified_leads": "${previous.result.qualified_leads}"}, "next": "prepare_outreach"}, {"id": "prepare_outreach", "agent": "insurance_lead_agent", "action": "prepare_outreach", "parameters": {"prioritized_leads": "${previous.result.prioritized_leads}", "outreach_type": "email"}, "next": "send_outreach"}, {"id": "send_outreach", "agent": "multi_account_email_agent", "action": "send_email", "parameters": {"account_email": "<EMAIL>", "to": "${previous.result.lead.email}", "subject": "${previous.result.subject}", "body": "${previous.result.body}"}, "next": "schedule_followup"}, {"id": "schedule_followup", "agent": "insurance_lead_agent", "action": "schedule_followup", "parameters": {"lead": "${steps.prepare_outreach.result.lead}", "outreach_type": "email", "outreach_result": "${previous.result}", "followup_days": 3}, "next": "end"}, {"id": "end", "type": "end"}]}