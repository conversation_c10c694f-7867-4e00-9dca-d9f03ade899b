"""
Run All Tests.

This script runs all tests for the enhanced UI-TARS integration.
"""
import os
import sys
import asyncio
import argparse
import logging
import subprocess
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent))

try:
    from core.logger import setup_logger
except ImportError as e:
    print(f"Error importing required modules: {e}")
    print("Make sure you're running this script from the project root directory.")
    sys.exit(1)

# Set up logger
logger = setup_logger("test_all")

async def run_diagnostic():
    """
    Run the UI-TARS diagnostic tool.
    
    Returns:
        bool: True if successful, False otherwise
    """
    logger.info("Running UI-TARS diagnostic tool")
    
    try:
        result = subprocess.run(["python", "ui_tars_diagnostic.py", "--fix", "--start"], capture_output=True, text=True)
        
        logger.info(f"Diagnostic tool output:\n{result.stdout}")
        
        if result.returncode != 0:
            logger.error(f"Diagnostic tool failed with return code {result.returncode}")
            logger.error(f"Error output:\n{result.stderr}")
            return False
        
        return True
    
    except Exception as e:
        logger.exception(f"Error running diagnostic tool: {e}")
        return False

async def run_ui_tars_test():
    """
    Run the UI-TARS integration test.
    
    Returns:
        bool: True if successful, False otherwise
    """
    logger.info("Running UI-TARS integration test")
    
    try:
        result = subprocess.run(["python", "test_ui_tars_integration.py", "--skip-gmail", "--skip-voice"], capture_output=True, text=True)
        
        logger.info(f"UI-TARS integration test output:\n{result.stdout}")
        
        if result.returncode != 0:
            logger.error(f"UI-TARS integration test failed with return code {result.returncode}")
            logger.error(f"Error output:\n{result.stderr}")
            return False
        
        return True
    
    except Exception as e:
        logger.exception(f"Error running UI-TARS integration test: {e}")
        return False

async def run_gmail_test():
    """
    Run the Gmail integration test.
    
    Returns:
        bool: True if successful, False otherwise
    """
    logger.info("Running Gmail integration test")
    
    try:
        result = subprocess.run(["python", "test_gmail_integration.py"], capture_output=True, text=True)
        
        logger.info(f"Gmail integration test output:\n{result.stdout}")
        
        if result.returncode != 0:
            logger.error(f"Gmail integration test failed with return code {result.returncode}")
            logger.error(f"Error output:\n{result.stderr}")
            return False
        
        return True
    
    except Exception as e:
        logger.exception(f"Error running Gmail integration test: {e}")
        return False

async def run_google_voice_test():
    """
    Run the Google Voice integration test.
    
    Returns:
        bool: True if successful, False otherwise
    """
    logger.info("Running Google Voice integration test")
    
    try:
        result = subprocess.run(["python", "test_google_voice_integration.py"], capture_output=True, text=True)
        
        logger.info(f"Google Voice integration test output:\n{result.stdout}")
        
        if result.returncode != 0:
            logger.error(f"Google Voice integration test failed with return code {result.returncode}")
            logger.error(f"Error output:\n{result.stderr}")
            return False
        
        return True
    
    except Exception as e:
        logger.exception(f"Error running Google Voice integration test: {e}")
        return False

async def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Run All Tests")
    parser.add_argument("--skip-diagnostic", action="store_true", help="Skip running the diagnostic tool")
    parser.add_argument("--skip-ui-tars", action="store_true", help="Skip testing the UI-TARS integration")
    parser.add_argument("--skip-gmail", action="store_true", help="Skip testing the Gmail integration")
    parser.add_argument("--skip-voice", action="store_true", help="Skip testing the Google Voice integration")
    
    args = parser.parse_args()
    
    print("Running All Tests")
    print("================")
    print()
    
    results = {}
    
    # Run diagnostic tool
    if not args.skip_diagnostic:
        print("Running diagnostic tool...")
        diagnostic_success = await run_diagnostic()
        results["Diagnostic Tool"] = "Success" if diagnostic_success else "Failed"
        print(f"Diagnostic tool: {results['Diagnostic Tool']}")
        print()
    
    # Run UI-TARS integration test
    if not args.skip_ui_tars:
        print("Running UI-TARS integration test...")
        ui_tars_success = await run_ui_tars_test()
        results["UI-TARS Integration"] = "Success" if ui_tars_success else "Failed"
        print(f"UI-TARS integration test: {results['UI-TARS Integration']}")
        print()
    
    # Run Gmail integration test
    if not args.skip_gmail:
        print("Running Gmail integration test...")
        gmail_success = await run_gmail_test()
        results["Gmail Integration"] = "Success" if gmail_success else "Failed"
        print(f"Gmail integration test: {results['Gmail Integration']}")
        print()
    
    # Run Google Voice integration test
    if not args.skip_voice:
        print("Running Google Voice integration test...")
        voice_success = await run_google_voice_test()
        results["Google Voice Integration"] = "Success" if voice_success else "Failed"
        print(f"Google Voice integration test: {results['Google Voice Integration']}")
        print()
    
    # Print summary
    print("Test Summary")
    print("===========")
    for test_name, result in results.items():
        print(f"{test_name}: {result}")
    
    # Check if all tests passed
    all_passed = all(result == "Success" for result in results.values())
    
    if all_passed:
        print("\nAll tests passed!")
        return 0
    else:
        print("\nSome tests failed. Check the logs for details.")
        return 1

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nTests cancelled")
        sys.exit(0)
