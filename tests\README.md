# Insurance Lead Agent Tests

This directory contains tests for the Insurance Lead Agent, which handles leads from multiple channels (Facebook, Instagram, TikTok, and website).

## Test Scripts

### 1. Calendly Integration Test

Tests the integration with <PERSON>nd<PERSON> for appointment booking.

```bash
python tests/test_calendly_integration.py --api-key YOUR_API_KEY
```

Optional arguments:
- `--api-key`: Calendly API key
- `--user-uri`: Calendly user URI
- `--event-type-uri`: Event type URI for creating scheduling link

### 2. Facebook Integration Test

Tests the integration with Facebook for lead handling.

```bash
python tests/test_facebook_integration.py --app-id YOUR_APP_ID --app-secret YOUR_APP_SECRET --access-token YOUR_ACCESS_TOKEN
```

Optional arguments:
- `--app-id`: Facebook App ID
- `--app-secret`: Facebook App Secret
- `--access-token`: Facebook Access Token
- `--page-id`: Facebook Page ID
- `--recipient-id`: Recipient ID for test message

### 3. Instagram Integration Test

Tests the integration with Instagram for lead handling.

```bash
python tests/test_instagram_integration.py --app-id YOUR_APP_ID --app-secret YOUR_APP_SECRET --access-token YOUR_ACCESS_TOKEN
```

Optional arguments:
- `--app-id`: Instagram App ID (same as Facebook App ID)
- `--app-secret`: Instagram App Secret (same as Facebook App Secret)
- `--access-token`: Instagram Access Token
- `--business-account-id`: Instagram Business Account ID
- `--recipient-id`: Recipient ID for test message

### 4. TikTok Integration Test

Tests the integration with TikTok for lead handling.

```bash
python tests/test_tiktok_integration.py --app-id YOUR_APP_ID --app-secret YOUR_APP_SECRET --access-token YOUR_ACCESS_TOKEN
```

Optional arguments:
- `--app-id`: TikTok App ID
- `--app-secret`: TikTok App Secret
- `--access-token`: TikTok Access Token
- `--recipient-id`: Recipient ID for test message

### 5. Website Lead Handling Test

Tests the website lead handling functionality.

```bash
python tests/test_website_lead_handling.py --name "John Doe" --email "<EMAIL>" --phone "************" --message "I'm interested in auto insurance" --insurance-type "auto"
```

Optional arguments:
- `--name`: Lead name
- `--email`: Lead email
- `--phone`: Lead phone
- `--message`: Lead message
- `--insurance-type`: Insurance type

### 6. Communication Test

Tests the email and SMS communication functionality.

```bash
python tests/test_communication.py --email-recipient "<EMAIL>" --sms-recipient "+***********"
```

Optional arguments:
- `--email-recipient`: Email recipient
- `--email-subject`: Email subject
- `--email-message`: Email message
- `--smtp-server`: SMTP server
- `--smtp-port`: SMTP port
- `--smtp-username`: SMTP username
- `--smtp-password`: SMTP password
- `--sms-recipient`: SMS recipient
- `--sms-message`: SMS message
- `--twilio-account-sid`: Twilio Account SID
- `--twilio-auth-token`: Twilio Auth Token
- `--twilio-phone-number`: Twilio Phone Number

### 7. Monitoring and Analytics Test

Tests the monitoring and analytics functionality.

```bash
python tests/test_monitoring.py --generate-data --num-leads 10 --days 7
```

Optional arguments:
- `--generate-data`: Generate test data
- `--num-leads`: Number of leads to generate per day (default: 10)
- `--days`: Number of days to generate data for (default: 7)

## Running All Tests

To run all tests, use the `run_all_tests.py` script:

```bash
python tests/run_all_tests.py
```

Optional arguments:
- `--test`: Test to run (choices: calendly, facebook, instagram, tiktok, website, communication, monitoring, all; default: all)
- `--generate-data`: Generate test data for monitoring test

## Credentials

The tests will look for credentials in the following locations:

- Calendly: `credentials/calendly/calendly.json`
- Facebook: `credentials/social_media/facebook.json`
- Instagram: `credentials/social_media/instagram.json`
- TikTok: `credentials/social_media/tiktok.json`
- Email: `credentials/communication/email.json`
- Twilio: `credentials/communication/twilio.json`

If credentials are not found, you can provide them as command-line arguments.

## Reports

The monitoring and analytics test generates reports in the `reports` directory:

- `response_time_distribution.png`: Distribution of response times
- `channel_performance.png`: Performance comparison of different channels
- `insurance_type_distribution.png`: Distribution of insurance types

## Dependencies

These tests require the following Python packages:

- aiohttp
- matplotlib
- numpy
- twilio (for SMS testing)

Install them with:

```bash
pip install aiohttp matplotlib numpy twilio
```
