"""
<PERSON><PERSON><PERSON> to create a new LLM connector.
"""
import sys
import os
import argparse
from pathlib import Path
import re

def create_llm_connector(provider_name: str, api_url: str):
    """
    Create a new LLM connector.
    
    Args:
        provider_name (str): Name of the LLM provider (snake_case)
        api_url (str): API URL for the provider
    """
    # Validate provider name
    if not re.match(r'^[a-z][a-z0-9_]*$', provider_name):
        print(f"Error: Invalid provider name: {provider_name}")
        print("Provider name must be in snake_case (lowercase with underscores)")
        return False
    
    # Create class name (PascalCase)
    class_name = "".join(word.capitalize() for word in provider_name.split("_")) + "Connector"
    
    # Create file path
    file_path = Path(f"llm/{provider_name}_connector.py")
    
    # Check if file already exists
    if file_path.exists():
        print(f"Error: Connector file already exists: {file_path}")
        return False
    
    # Create connector file
    print(f"Creating connector file: {file_path}")
    
    # Connector file template
    template = f'''"""
{provider_name.capitalize()} connector for the Multi-Agent AI System.
"""
from typing import Dict, List, Optional, Any, Union
import asyncio
import json
from datetime import datetime
import aiohttp
from tenacity import retry, stop_after_attempt, wait_exponential

from llm.llm_connector import LLMConnector
from core.logger import setup_logger

class {class_name}(LLMConnector):
    """
    Connector for {provider_name.capitalize()} API.
    """
    
    def __init__(self, config: Dict):
        """
        Initialize the {provider_name.capitalize()} connector.
        
        Args:
            config (Dict): LLM configuration
        """
        self.provider = "{provider_name}"
        super().__init__(config)
        
        # {provider_name.capitalize()}-specific configuration
        self.api_url = "{api_url}"
        self.api_version = config.get("api_version", "v1")  # Update as needed
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=1, max=10))
    async def generate_text(
        self,
        prompt: str,
        model: Optional[str] = None,
        max_tokens: int = 1000,
        temperature: float = 0.7,
        stop_sequences: Optional[List[str]] = None,
        **kwargs
    ) -> Dict:
        """
        Generate text using {provider_name.capitalize()}.
        
        Args:
            prompt (str): Input prompt
            model (Optional[str]): Model to use, defaults to default_model
            max_tokens (int): Maximum number of tokens to generate
            temperature (float): Sampling temperature
            stop_sequences (Optional[List[str]]): Sequences that stop generation
            **kwargs: Additional model-specific parameters
            
        Returns:
            Dict: Response containing generated text and metadata
        """
        if not self.enabled:
            return {{"error": f"{{{self.provider}}} is not enabled"}}
        
        if not model:
            model = self.default_model
        
        if model not in self.models:
            self.logger.warning(f"Model {{model}} not available, using {{self.default_model}}")
            model = self.default_model
        
        # Convert prompt to chat format
        messages = [{{"role": "user", "content": prompt}}]
        return await self.generate_chat(messages, model, max_tokens, temperature, stop_sequences, **kwargs)
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=1, max=10))
    async def generate_chat(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        max_tokens: int = 1000,
        temperature: float = 0.7,
        stop_sequences: Optional[List[str]] = None,
        **kwargs
    ) -> Dict:
        """
        Generate a chat response using {provider_name.capitalize()}.
        
        Args:
            messages (List[Dict[str, str]]): List of message dictionaries
            model (Optional[str]): Model to use, defaults to default_model
            max_tokens (int): Maximum number of tokens to generate
            temperature (float): Sampling temperature
            stop_sequences (Optional[List[str]]): Sequences that stop generation
            **kwargs: Additional model-specific parameters
            
        Returns:
            Dict: Response containing generated text and metadata
        """
        if not self.enabled:
            return {{"error": f"{{{self.provider}}} is not enabled"}}
        
        if not model:
            model = self.default_model
        
        if model not in self.models:
            self.logger.warning(f"Model {{model}} not available, using {{self.default_model}}")
            model = self.default_model
        
        # Prepare request payload
        payload = {{
            "model": model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
        }}
        
        # Add stop sequences if provided
        if stop_sequences:
            payload["stop"] = stop_sequences
        
        # Add additional parameters
        for key, value in kwargs.items():
            payload[key] = value
        
        # Make API request
        start_time = datetime.now()
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{{self.api_url}}/chat/completions",
                    headers={{
                        "Authorization": f"Bearer {{self.api_key}}",
                        "Content-Type": "application/json",
                    }},
                    json=payload,
                ) as response:
                    response_json = await response.json()
                    
                    if response.status != 200:
                        self.logger.error(f"{provider_name.capitalize()} API error: {{response.status}} - {{response_json}}")
                        return {{
                            "error": f"API error: {{response.status}}",
                            "details": response_json,
                        }}
                    
                    # Process response
                    end_time = datetime.now()
                    latency = (end_time - start_time).total_seconds()
                    
                    # Extract text from response
                    # Update this based on the actual API response format
                    text = response_json.get("choices", [{{}}])[0].get("message", {{}}).get("content", "")
                    
                    return {{
                        "text": text,
                        "model": model,
                        "provider": self.provider,
                        "latency": latency,
                        "raw_response": response_json,
                    }}
        
        except Exception as e:
            self.logger.exception(f"Error calling {provider_name.capitalize()} API: {{e}}")
            return {{
                "error": f"API request failed: {{str(e)}}",
                "model": model,
                "provider": self.provider,
            }}
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=1, max=10))
    async def embed_text(
        self,
        text: Union[str, List[str]],
        model: Optional[str] = None,
        **kwargs
    ) -> Dict:
        """
        Generate embeddings for text using {provider_name.capitalize()}.
        
        Args:
            text (Union[str, List[str]]): Text to embed
            model (Optional[str]): Model to use, defaults to default_model
            **kwargs: Additional model-specific parameters
            
        Returns:
            Dict: Response containing embeddings and metadata
        """
        if not self.enabled:
            return {{"error": f"{{{self.provider}}} is not enabled"}}
        
        if not model:
            model = self.default_model
        
        if model not in self.models:
            self.logger.warning(f"Model {{model}} not available, using {{self.default_model}}")
            model = self.default_model
        
        # Prepare request payload
        payload = {{
            "model": model,
            "input": text if isinstance(text, list) else [text],
        }}
        
        # Add additional parameters
        for key, value in kwargs.items():
            payload[key] = value
        
        # Make API request
        start_time = datetime.now()
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{{self.api_url}}/embeddings",
                    headers={{
                        "Authorization": f"Bearer {{self.api_key}}",
                        "Content-Type": "application/json",
                    }},
                    json=payload,
                ) as response:
                    response_json = await response.json()
                    
                    if response.status != 200:
                        self.logger.error(f"{provider_name.capitalize()} API error: {{response.status}} - {{response_json}}")
                        return {{
                            "error": f"API error: {{response.status}}",
                            "details": response_json,
                        }}
                    
                    # Process response
                    end_time = datetime.now()
                    latency = (end_time - start_time).total_seconds()
                    
                    # Extract embeddings from response
                    # Update this based on the actual API response format
                    embeddings = [item.get("embedding", []) for item in response_json.get("data", [])]
                    
                    return {{
                        "embeddings": embeddings,
                        "model": model,
                        "provider": self.provider,
                        "latency": latency,
                        "raw_response": response_json,
                    }}
        
        except Exception as e:
            self.logger.exception(f"Error calling {provider_name.capitalize()} API: {{e}}")
            return {{
                "error": f"API request failed: {{str(e)}}",
                "model": model,
                "provider": self.provider,
            }}
'''
    
    # Write connector file
    with open(file_path, "w") as f:
        f.write(template)
    
    print(f"Connector file created: {file_path}")
    
    # Update config.py
    print("Updating config.py with new LLM connector")
    
    # Read config.py
    config_path = Path("config.py")
    with open(config_path, "r") as f:
        config_content = f.read()
    
    # Find LLM_CONFIG section
    llm_config_match = re.search(r'LLM_CONFIG\s*=\s*{([^}]*)}', config_content, re.DOTALL)
    if not llm_config_match:
        print("Error: Could not find LLM_CONFIG section in config.py")
        return False
    
    # Extract LLM_CONFIG content
    llm_config_content = llm_config_match.group(1)
    
    # Check if provider already exists in config
    if f'"{provider_name}"' in llm_config_content:
        print(f"Error: Provider {provider_name} already exists in config.py")
        return False
    
    # Create new LLM config
    new_llm_config = f'''
    "{provider_name}": {{
        "api_key": os.getenv("{provider_name.upper()}_API_KEY", ""),
        "models": ["model1", "model2"],  # Update with actual models
        "default_model": "model1",  # Update with actual default model
        "enabled": os.getenv("ENABLE_{provider_name.upper()}", "False").lower() == "true",
    }},'''
    
    # Add new LLM config to LLM_CONFIG
    new_llm_config_content = llm_config_content + new_llm_config
    
    # Replace LLM_CONFIG content
    new_config_content = config_content.replace(llm_config_content, new_llm_config_content)
    
    # Write updated config.py
    with open(config_path, "w") as f:
        f.write(new_config_content)
    
    print("config.py updated with new LLM connector")
    
    # Update .env.example
    print("Updating .env.example with new LLM connector")
    
    # Read .env.example
    env_path = Path(".env.example")
    with open(env_path, "r") as f:
        env_content = f.read()
    
    # Create new LLM env vars
    new_llm_env = f'''
{provider_name.upper()}_API_KEY=your_{provider_name}_api_key
ENABLE_{provider_name.upper()}=False
'''
    
    # Add new LLM env vars to .env.example
    new_env_content = env_content + new_llm_env
    
    # Write updated .env.example
    with open(env_path, "w") as f:
        f.write(new_env_content)
    
    print(".env.example updated with new LLM connector")
    
    # Print next steps
    print("\nNext steps:")
    print(f"1. Edit llm/{provider_name}_connector.py to implement the connector's logic")
    print(f"2. Update the models list in config.py for the {provider_name} provider")
    print(f"3. Add your {provider_name.upper()}_API_KEY to your .env file")
    print(f"4. Set ENABLE_{provider_name.upper()}=True in your .env file")
    print("5. Restart the system to load the new LLM connector")
    
    return True

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Create a new LLM connector")
    parser.add_argument("name", help="Name of the LLM provider (snake_case)")
    parser.add_argument("--api-url", default="https://api.example.com", help="API URL for the provider")
    args = parser.parse_args()
    
    # Create LLM connector
    success = create_llm_connector(args.name, args.api_url)
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
