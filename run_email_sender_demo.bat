@echo off
echo Email Sender Demo
echo ================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Ask for method
echo Choose a method to send email:
echo 1. UI-TARS browser automation
echo 2. Gmail API
echo 3. Both (for comparison)
echo.
set /p METHOD_CHOICE="Enter your choice (1-3): "

if "%METHOD_CHOICE%"=="1" (
    set METHOD=ui-tars
) else if "%METHOD_CHOICE%"=="2" (
    set METHOD=api
) else if "%METHOD_CHOICE%"=="3" (
    set METHOD=both
) else (
    echo Invalid choice. Using UI-TARS by default.
    set METHOD=ui-tars
)

REM Get email details
set FROM_EMAIL=<EMAIL>
set TO_EMAIL=<EMAIL>
set /p SUBJECT="Enter subject (default: Test Email from Email Sender Demo): "
if "%SUBJECT%"=="" set SUBJECT=Test Email from Email Sender Demo
set /p BODY="Enter body (default: This is a test email sent using the Email Sender Demo.): "
if "%BODY%"=="" set BODY=This is a test email sent using the Email Sender Demo.

REM Run the Email Sender Demo
echo.
echo Sending email from %FROM_EMAIL% to %TO_EMAIL% using %METHOD%...
echo.

python email_sender_demo.py --method "%METHOD%" --email "%FROM_EMAIL%" --to "%TO_EMAIL%" --subject "%SUBJECT%" --body "%BODY%"

echo.
echo Done.
pause
