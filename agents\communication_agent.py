"""
Communication Agent for handling calls, emails, and text messages.
"""
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import json
import re
import uuid

from agents.base_agent import BaseAgent
from core.logger import setup_logger
from llm.llm_router import <PERSON><PERSON>outer
from services.communication_service import CommunicationServiceFactory

class CommunicationAgent(BaseAgent):
    """
    Agent specialized for handling various forms of communication.
    
    This agent handles tasks related to phone calls, voicemails, emails,
    and text messages, including both outbound and inbound communication.
    """
    
    def __init__(
        self,
        agent_id: str,
        config: Dict,
        state_manager,
        message_queue,
        shutdown_event
    ):
        """Initialize the communication agent."""
        super().__init__(agent_id, config, state_manager, message_queue, shutdown_event)
        
        # Communication-specific configuration
        self.llm_provider = config.get("llm_provider", "anthropic")
        self.llm_router = None
        
        # Communication services
        self.email_service = None
        self.sms_service = None
        self.voice_service = None
        
        # Communication data
        self.contacts = {}
        self.message_history = {}
        self.scheduled_communications = {}
        self.templates = {}
        
        # Agent capabilities
        self.capabilities = [
            "email_handling",
            "sms_handling",
            "call_handling",
            "voicemail_handling",
            "scheduled_communication",
            "template_management",
        ]
    
    async def initialize(self):
        """Initialize the communication agent."""
        await super().initialize()
        
        # Initialize LLM router
        self.llm_router = LLMRouter()
        await self.llm_router.initialize()
        
        # Initialize communication services
        self.email_service = CommunicationServiceFactory.create_service("email")
        self.sms_service = CommunicationServiceFactory.create_service("sms")
        self.voice_service = CommunicationServiceFactory.create_service("voice")
        
        # Load communication data
        await self._load_communication_data()
        
        self.logger.info(f"Communication agent initialized with provider: {self.llm_provider}")
    
    async def _load_communication_data(self):
        """Load communication data from state manager."""
        # Load contacts
        contacts_data = await self.state_manager.get_state("communication", "contacts")
        if contacts_data:
            self.contacts = contacts_data
            self.logger.info(f"Loaded {len(self.contacts)} contacts")
        
        # Load message history
        history_data = await self.state_manager.get_state("communication", "message_history")
        if history_data:
            self.message_history = history_data
            self.logger.info(f"Loaded message history with {len(self.message_history)} entries")
        
        # Load scheduled communications
        scheduled_data = await self.state_manager.get_state("communication", "scheduled_communications")
        if scheduled_data:
            self.scheduled_communications = scheduled_data
            self.logger.info(f"Loaded {len(self.scheduled_communications)} scheduled communications")
        
        # Load templates
        templates_data = await self.state_manager.get_state("communication", "templates")
        if templates_data:
            self.templates = templates_data
            self.logger.info(f"Loaded {len(self.templates)} communication templates")
    
    async def execute_cycle(self):
        """Execute one cycle of the communication agent's logic."""
        self.logger.debug("Executing communication agent cycle")
        
        try:
            # Check for pending tasks
            pending_tasks = await self.state_manager.get_state("communication", "pending_tasks")
            if pending_tasks:
                for task_id, task in pending_tasks.items():
                    if task.get("status") == "pending":
                        await self._process_task(task_id, task)
            
            # Check for scheduled communications
            await self._check_scheduled_communications()
            
            # Update state with any changes
            await self._save_communication_data()
            
        except Exception as e:
            self.logger.exception(f"Error in communication agent cycle: {e}")
    
    async def _process_task(self, task_id: str, task: Dict):
        """
        Process a pending task.
        
        Args:
            task_id (str): Task identifier
            task (Dict): Task data
        """
        task_type = task.get("type")
        self.logger.info(f"Processing task: {task_id} ({task_type})")
        
        try:
            if task_type == "send_email":
                await self._handle_send_email(task)
            elif task_type == "send_sms":
                await self._handle_send_sms(task)
            elif task_type == "make_call":
                await self._handle_make_call(task)
            elif task_type == "create_template":
                await self._handle_create_template(task)
            elif task_type == "schedule_communication":
                await self._handle_schedule_communication(task)
            else:
                self.logger.warning(f"Unknown task type: {task_type}")
                return
            
            # Update task status
            task["status"] = "completed"
            task["completed_at"] = datetime.now().isoformat()
            
            # Update pending tasks
            pending_tasks = await self.state_manager.get_state("communication", "pending_tasks") or {}
            pending_tasks[task_id] = task
            await self.state_manager.update_state("communication", "pending_tasks", pending_tasks)
            
        except Exception as e:
            self.logger.exception(f"Error processing task {task_id}: {e}")
            
            # Update task status
            task["status"] = "error"
            task["error"] = str(e)
            
            # Update pending tasks
            pending_tasks = await self.state_manager.get_state("communication", "pending_tasks") or {}
            pending_tasks[task_id] = task
            await self.state_manager.update_state("communication", "pending_tasks", pending_tasks)
    
    async def _handle_send_email(self, task: Dict):
        """
        Handle a send email task.
        
        Args:
            task (Dict): Task data
        """
        recipient = task.get("recipient")
        subject = task.get("subject", "")
        content = task.get("content", "")
        template_id = task.get("template_id")
        
        # If template is specified, use it
        if template_id and template_id in self.templates:
            template = self.templates[template_id]
            
            if template.get("type") != "email":
                raise ValueError(f"Template {template_id} is not an email template")
            
            # Apply template
            template_content = template.get("content", "")
            template_subject = template.get("subject", "")
            
            # Replace placeholders in template
            placeholders = task.get("placeholders", {})
            for key, value in placeholders.items():
                template_content = template_content.replace(f"{{{key}}}", value)
                template_subject = template_subject.replace(f"{{{key}}}", value)
            
            subject = template_subject if subject == "" else subject
            content = template_content if content == "" else content
        
        # Check if email service is available
        if not self.email_service or not self.email_service.is_enabled():
            raise ValueError("Email service is not available")
        
        # Send email
        result = await self.email_service.send_message(
            recipient=recipient,
            content=content,
            subject=subject,
            html_content=task.get("html_content"),
            cc=task.get("cc", []),
            bcc=task.get("bcc", []),
        )
        
        # Store result in task
        task["result"] = result
        
        # Add to message history
        message_id = str(uuid.uuid4())
        self.message_history[message_id] = {
            "id": message_id,
            "type": "email",
            "direction": "outbound",
            "recipient": recipient,
            "subject": subject,
            "content": content,
            "timestamp": datetime.now().isoformat(),
            "status": "sent" if "error" not in result else "failed",
            "result": result,
        }
    
    async def _handle_send_sms(self, task: Dict):
        """
        Handle a send SMS task.
        
        Args:
            task (Dict): Task data
        """
        recipient = task.get("recipient")
        content = task.get("content", "")
        template_id = task.get("template_id")
        
        # If template is specified, use it
        if template_id and template_id in self.templates:
            template = self.templates[template_id]
            
            if template.get("type") != "sms":
                raise ValueError(f"Template {template_id} is not an SMS template")
            
            # Apply template
            template_content = template.get("content", "")
            
            # Replace placeholders in template
            placeholders = task.get("placeholders", {})
            for key, value in placeholders.items():
                template_content = template_content.replace(f"{{{key}}}", value)
            
            content = template_content if content == "" else content
        
        # Check if SMS service is available
        if not self.sms_service or not self.sms_service.is_enabled():
            raise ValueError("SMS service is not available")
        
        # Send SMS
        result = await self.sms_service.send_message(
            recipient=recipient,
            content=content,
        )
        
        # Store result in task
        task["result"] = result
        
        # Add to message history
        message_id = str(uuid.uuid4())
        self.message_history[message_id] = {
            "id": message_id,
            "type": "sms",
            "direction": "outbound",
            "recipient": recipient,
            "content": content,
            "timestamp": datetime.now().isoformat(),
            "status": "sent" if "error" not in result else "failed",
            "result": result,
        }
    
    async def _handle_make_call(self, task: Dict):
        """
        Handle a make call task.
        
        Args:
            task (Dict): Task data
        """
        recipient = task.get("recipient")
        content = task.get("content", "")
        template_id = task.get("template_id")
        
        # If template is specified, use it
        if template_id and template_id in self.templates:
            template = self.templates[template_id]
            
            if template.get("type") != "voice":
                raise ValueError(f"Template {template_id} is not a voice template")
            
            # Apply template
            template_content = template.get("content", "")
            
            # Replace placeholders in template
            placeholders = task.get("placeholders", {})
            for key, value in placeholders.items():
                template_content = template_content.replace(f"{{{key}}}", value)
            
            content = template_content if content == "" else content
        
        # Check if voice service is available
        if not self.voice_service or not self.voice_service.is_enabled():
            raise ValueError("Voice service is not available")
        
        # Make call
        result = await self.voice_service.send_message(
            recipient=recipient,
            content=content,
            twiml_url=task.get("twiml_url"),
        )
        
        # Store result in task
        task["result"] = result
        
        # Add to message history
        message_id = str(uuid.uuid4())
        self.message_history[message_id] = {
            "id": message_id,
            "type": "voice",
            "direction": "outbound",
            "recipient": recipient,
            "content": content,
            "timestamp": datetime.now().isoformat(),
            "status": "initiated" if "error" not in result else "failed",
            "result": result,
        }
    
    async def _handle_create_template(self, task: Dict):
        """
        Handle a create template task.
        
        Args:
            task (Dict): Task data
        """
        template_name = task.get("template_name")
        template_type = task.get("template_type")  # email, sms, voice
        content = task.get("content", "")
        subject = task.get("subject", "")  # for email templates
        description = task.get("description", "")
        
        # Generate template content using LLM if not provided
        if not content:
            prompt = f"""
            You are a communication specialist creating a {template_type} template named "{template_name}".
            
            Description: {description}
            
            Please create a professional {template_type} template that:
            1. Is clear and concise
            2. Uses appropriate tone for the medium
            3. Includes placeholders in {{placeholder}} format where dynamic content should go
            
            {f"Include a subject line for this email template." if template_type == "email" else ""}
            
            The template should be ready to use without further editing.
            """
            
            response = await self.llm_router.generate_text(
                prompt=prompt,
                provider=self.llm_provider,
                max_tokens=800,
                temperature=0.7
            )
            
            generated_content = response.get("text", "")
            
            # Extract subject for email templates
            if template_type == "email" and not subject:
                # Look for a subject line in the generated content
                subject_match = re.search(r"Subject:(.+?)(?:\n|$)", generated_content)
                if subject_match:
                    subject = subject_match.group(1).strip()
                    # Remove the subject line from the content
                    generated_content = re.sub(r"Subject:.+?(?:\n|$)", "", generated_content, 1)
            
            content = generated_content.strip()
        
        # Create template
        template_id = str(uuid.uuid4())
        self.templates[template_id] = {
            "id": template_id,
            "name": template_name,
            "type": template_type,
            "content": content,
            "subject": subject if template_type == "email" else "",
            "description": description,
            "created_at": datetime.now().isoformat(),
        }
        
        # Store template ID in task
        task["template_id"] = template_id
        task["template"] = self.templates[template_id]
    
    async def _handle_schedule_communication(self, task: Dict):
        """
        Handle a schedule communication task.
        
        Args:
            task (Dict): Task data
        """
        communication_type = task.get("communication_type")  # email, sms, voice
        recipient = task.get("recipient")
        scheduled_time = task.get("scheduled_time")
        content = task.get("content", "")
        subject = task.get("subject", "")  # for email
        template_id = task.get("template_id")
        
        # Create scheduled communication
        schedule_id = str(uuid.uuid4())
        self.scheduled_communications[schedule_id] = {
            "id": schedule_id,
            "type": communication_type,
            "recipient": recipient,
            "scheduled_time": scheduled_time,
            "content": content,
            "subject": subject,
            "template_id": template_id,
            "placeholders": task.get("placeholders", {}),
            "created_at": datetime.now().isoformat(),
            "status": "scheduled",
        }
        
        # Store schedule ID in task
        task["schedule_id"] = schedule_id
    
    async def _check_scheduled_communications(self):
        """Check for scheduled communications that need to be sent."""
        current_time = datetime.now()
        
        for schedule_id, schedule in self.scheduled_communications.items():
            if schedule.get("status") != "scheduled":
                continue
            
            scheduled_time = datetime.fromisoformat(schedule.get("scheduled_time"))
            
            # Check if it's time to send
            if current_time >= scheduled_time:
                await self._send_scheduled_communication(schedule_id, schedule)
    
    async def _send_scheduled_communication(self, schedule_id: str, schedule: Dict):
        """
        Send a scheduled communication.
        
        Args:
            schedule_id (str): Schedule identifier
            schedule (Dict): Schedule data
        """
        communication_type = schedule.get("type")
        
        # Create appropriate task based on communication type
        task_id = f"TASK-{communication_type.upper()}-{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        task = {
            "task_id": task_id,
            "type": f"send_{communication_type}",
            "recipient": schedule.get("recipient"),
            "content": schedule.get("content"),
            "template_id": schedule.get("template_id"),
            "placeholders": schedule.get("placeholders", {}),
            "created_at": datetime.now().isoformat(),
            "status": "pending",
            "schedule_id": schedule_id,
        }
        
        # Add email-specific fields
        if communication_type == "email":
            task["subject"] = schedule.get("subject", "")
        
        # Add task to pending tasks
        pending_tasks = await self.state_manager.get_state("communication", "pending_tasks") or {}
        pending_tasks[task_id] = task
        await self.state_manager.update_state("communication", "pending_tasks", pending_tasks)
        
        # Update schedule status
        schedule["status"] = "processing"
        schedule["task_id"] = task_id
        self.scheduled_communications[schedule_id] = schedule
    
    async def _save_communication_data(self):
        """Save communication data to state manager."""
        # Save contacts
        await self.state_manager.update_state("communication", "contacts", self.contacts)
        
        # Save message history
        await self.state_manager.update_state("communication", "message_history", self.message_history)
        
        # Save scheduled communications
        await self.state_manager.update_state("communication", "scheduled_communications", self.scheduled_communications)
        
        # Save templates
        await self.state_manager.update_state("communication", "templates", self.templates)
    
    async def handle_command(self, message: Dict):
        """
        Handle a command message.
        
        Args:
            message (Dict): Command message
        """
        command = message.get("content", {}).get("command")
        
        if command == "send_email":
            # Create send email task
            task_id = f"TASK-EMAIL-{datetime.now().strftime('%Y%m%d%H%M%S')}"
            
            task = {
                "task_id": task_id,
                "type": "send_email",
                "recipient": message.get("content", {}).get("recipient"),
                "subject": message.get("content", {}).get("subject", ""),
                "content": message.get("content", {}).get("content", ""),
                "html_content": message.get("content", {}).get("html_content"),
                "template_id": message.get("content", {}).get("template_id"),
                "placeholders": message.get("content", {}).get("placeholders", {}),
                "cc": message.get("content", {}).get("cc", []),
                "bcc": message.get("content", {}).get("bcc", []),
                "created_at": datetime.now().isoformat(),
                "status": "pending",
                "requester_id": message.get("sender_id"),
            }
            
            # Add task to pending tasks
            pending_tasks = await self.state_manager.get_state("communication", "pending_tasks") or {}
            pending_tasks[task_id] = task
            await self.state_manager.update_state("communication", "pending_tasks", pending_tasks)
            
            # Acknowledge receipt
            await self.send_message(
                message.get("sender_id"),
                "response",
                {
                    "command": command,
                    "task_id": task_id,
                    "status": "processing",
                }
            )
            
        elif command == "send_sms":
            # Create send SMS task
            task_id = f"TASK-SMS-{datetime.now().strftime('%Y%m%d%H%M%S')}"
            
            task = {
                "task_id": task_id,
                "type": "send_sms",
                "recipient": message.get("content", {}).get("recipient"),
                "content": message.get("content", {}).get("content", ""),
                "template_id": message.get("content", {}).get("template_id"),
                "placeholders": message.get("content", {}).get("placeholders", {}),
                "created_at": datetime.now().isoformat(),
                "status": "pending",
                "requester_id": message.get("sender_id"),
            }
            
            # Add task to pending tasks
            pending_tasks = await self.state_manager.get_state("communication", "pending_tasks") or {}
            pending_tasks[task_id] = task
            await self.state_manager.update_state("communication", "pending_tasks", pending_tasks)
            
            # Acknowledge receipt
            await self.send_message(
                message.get("sender_id"),
                "response",
                {
                    "command": command,
                    "task_id": task_id,
                    "status": "processing",
                }
            )
            
        elif command == "create_template":
            # Create template task
            task_id = f"TASK-TEMPLATE-{datetime.now().strftime('%Y%m%d%H%M%S')}"
            
            task = {
                "task_id": task_id,
                "type": "create_template",
                "template_name": message.get("content", {}).get("template_name"),
                "template_type": message.get("content", {}).get("template_type"),
                "content": message.get("content", {}).get("content", ""),
                "subject": message.get("content", {}).get("subject", ""),
                "description": message.get("content", {}).get("description", ""),
                "created_at": datetime.now().isoformat(),
                "status": "pending",
                "requester_id": message.get("sender_id"),
            }
            
            # Add task to pending tasks
            pending_tasks = await self.state_manager.get_state("communication", "pending_tasks") or {}
            pending_tasks[task_id] = task
            await self.state_manager.update_state("communication", "pending_tasks", pending_tasks)
            
            # Acknowledge receipt
            await self.send_message(
                message.get("sender_id"),
                "response",
                {
                    "command": command,
                    "task_id": task_id,
                    "status": "processing",
                }
            )
            
        else:
            await super().handle_command(message)
