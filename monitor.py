"""
Script to monitor the system state.
"""
import sys
import asyncio
import argparse
from pathlib import Path
import json
import time
from datetime import datetime

from core.state_manager import StateManager
from core.logger import setup_logger
import config

# Set up logger
logger = setup_logger("monitor")

async def monitor_system(interval: int = 5, section: str = None, key: str = None):
    """
    Monitor the system state.
    
    Args:
        interval (int): Monitoring interval in seconds
        section (str, optional): State section to monitor
        key (str, optional): Specific key within section
    """
    logger.info("Starting system monitor")
    
    # Initialize state manager
    state_manager = StateManager()
    await state_manager.initialize()
    
    try:
        # Monitor loop
        while True:
            # Get state
            state = await state_manager.get_state(section, key)
            
            # Print state
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f"\n[{timestamp}] System State:")
            
            if section:
                if key:
                    print(f"{section}.{key}:")
                else:
                    print(f"{section}:")
            
            print(json.dumps(state, indent=2))
            
            # Wait for next interval
            await asyncio.sleep(interval)
    
    except KeyboardInterrupt:
        logger.info("Monitoring stopped by user")
    
    finally:
        # Close state manager
        await state_manager.close()

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Monitor the system state")
    parser.add_argument("--interval", type=int, default=5, help="Monitoring interval in seconds")
    parser.add_argument("--section", help="State section to monitor")
    parser.add_argument("--key", help="Specific key within section")
    args = parser.parse_args()
    
    # Run monitor
    try:
        asyncio.run(monitor_system(args.interval, args.section, args.key))
        return 0
    except Exception as e:
        logger.exception(f"Error in monitor: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
