"""
Send a test email using Gmail.
This script sends a test email using Gmail after successful authentication.
"""
import os
import sys
import pickle
import base64
import subprocess
from email.mime.text import MIMEText
from pathlib import Path

def clear_screen():
    """Clear the terminal screen."""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_header():
    """Print the script header."""
    clear_screen()
    print("=" * 80)
    print("                     SEND GMAIL TEST EMAIL")
    print("=" * 80)
    print("\nThis script sends a test email using Gmail after successful authentication.")
    print("You'll be able to see the email being sent in real-time.")
    print("\n")

def get_authenticated_accounts():
    """
    Get a list of authenticated Gmail accounts.
    
    Returns:
        list: List of authenticated Gmail accounts
    """
    accounts = []
    credentials_dir = 'credentials'
    
    if not os.path.exists(credentials_dir):
        return accounts
    
    for filename in os.listdir(credentials_dir):
        if filename.startswith('gmail_') and filename.endswith('_token.pickle'):
            # Extract email from filename
            email_part = filename[6:-13]  # Remove 'gmail_' prefix and '_token.pickle' suffix
            email = email_part.replace('_at_', '@').replace('_dot_', '.')
            token_path = os.path.join(credentials_dir, filename)
            credentials_path = os.path.join(credentials_dir, filename.replace('_token.pickle', '_credentials.json'))
            
            if os.path.exists(credentials_path):
                accounts.append({
                    'email': email,
                    'credentials_path': credentials_path,
                    'token_path': token_path
                })
    
    return accounts

def send_test_email(account):
    """
    Send a test email using Gmail.
    
    Args:
        account (dict): Account information
        
    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    email = account['email']
    credentials_path = account['credentials_path']
    token_path = account['token_path']
    
    print_header()
    print(f"SENDING TEST EMAIL FROM {email}")
    print("-" * 80)
    
    # Install required packages if not already installed
    try:
        from google.auth.transport.requests import Request
        from google.oauth2.credentials import Credentials
        from google_auth_oauthlib.flow import InstalledAppFlow
        from googleapiclient.discovery import build
        from googleapiclient.errors import HttpError
    except ImportError:
        print("\nInstalling required packages...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", 
                              "google-auth", "google-auth-oauthlib", 
                              "google-auth-httplib2", "google-api-python-client"])
        
        from google.auth.transport.requests import Request
        from google.oauth2.credentials import Credentials
        from google_auth_oauthlib.flow import InstalledAppFlow
        from googleapiclient.discovery import build
        from googleapiclient.errors import HttpError
    
    # Check if token file exists
    if not os.path.exists(token_path):
        print(f"\nError: Token file not found at {token_path}")
        print("Please run simple_gmail_auth.py to authenticate first.")
        return False
    
    try:
        print("\nLoading credentials...")
        
        # Load credentials
        with open(token_path, 'rb') as token:
            creds = pickle.load(token)
        
        # Refresh token if expired
        if creds.expired and creds.refresh_token:
            print("Refreshing expired token...")
            creds.refresh(Request())
            
            # Save the refreshed credentials
            with open(token_path, 'wb') as token:
                pickle.dump(creds, token)
        
        # Build the Gmail service
        print("Building Gmail service...")
        service = build('gmail', 'v1', credentials=creds)
        
        # Get user profile to confirm authentication
        profile = service.users().getProfile(userId='me').execute()
        authenticated_email = profile.get('emailAddress')
        
        print(f"✓ Successfully authenticated as {authenticated_email}")
        
        # Get email details from user
        print("\nEnter the details for your test email:")
        to_email = input("To: ")
        subject = input("Subject: ")
        body = input("Body: ")
        
        # Create the email message
        print("\nCreating email message...")
        message = MIMEText(body)
        message['to'] = to_email
        message['from'] = authenticated_email
        message['subject'] = subject
        
        # Encode the message
        raw_message = base64.urlsafe_b64encode(message.as_bytes()).decode()
        
        # Send the email
        print("\nSending email...")
        send_message = service.users().messages().send(
            userId='me',
            body={'raw': raw_message}
        ).execute()
        
        print(f"\n✓ Email sent successfully! Message ID: {send_message['id']}")
        return True
    
    except Exception as e:
        print(f"\nError sending email: {e}")
        
        retry = input("\nDo you want to try again? (y/n): ").lower()
        if retry == 'y':
            return send_test_email(account)
        
        return False

def main():
    """Main entry point."""
    print_header()
    
    # Get authenticated accounts
    accounts = get_authenticated_accounts()
    
    if not accounts:
        print("\nNo authenticated Gmail accounts found.")
        print("Please run simple_gmail_auth.py to authenticate first.")
        return
    
    print("\nAuthenticated Gmail accounts:")
    for i, account in enumerate(accounts):
        print(f"{i+1}. {account['email']}")
    
    print(f"{len(accounts)+1}. Exit")
    
    try:
        index = int(input("\nSelect an account to send a test email from: ")) - 1
        
        if index == len(accounts):
            # Exit
            print("\nExiting...")
            return
        
        elif 0 <= index < len(accounts):
            # Send test email
            send_test_email(accounts[index])
            
            # Ask if user wants to send another email
            another = input("\nDo you want to send another email? (y/n): ").lower()
            if another == 'y':
                main()
        
        else:
            print("\nInvalid selection.")
            input("\nPress Enter to try again...")
            main()
    
    except ValueError:
        print("\nInvalid selection.")
        input("\nPress Enter to try again...")
        main()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nExiting...")
        sys.exit(0)
