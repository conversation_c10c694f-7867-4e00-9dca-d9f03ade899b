{"id": "trading_strategy_workflow", "name": "Advanced Trading Strategy Workflow", "description": "Executes a sophisticated trading strategy with risk assessment, sentiment analysis, and quantum optimization", "version": "1.0", "created_at": "2023-05-01T12:00:00Z", "updated_at": "2023-05-01T12:00:00Z", "steps": [{"id": "step_1", "name": "Market Analysis", "description": "Analyze market conditions for target symbols", "type": "agent_task", "agent_id": "trading_agent", "task_type": "market_analysis", "parameters": {"symbols": "${symbols}", "timeframe": "${timeframe}", "indicators": ["moving_average", "rsi", "macd", "bollinger_bands"]}}, {"id": "step_2", "name": "Sentiment Analysis", "description": "Analyze market sentiment for target symbols", "type": "agent_task", "agent_id": "trading_agent", "task_type": "sentiment_analysis", "parameters": {"symbols": "${symbols}", "include_news": true, "include_social_media": true}}, {"id": "step_3", "name": "Portfolio Analysis", "description": "Analyze current portfolio composition and performance", "type": "agent_task", "agent_id": "trading_agent", "task_type": "portfolio_analysis", "parameters": {"portfolio_id": "${portfolio_id}", "include_performance_metrics": true, "include_risk_metrics": true}}, {"id": "step_4", "name": "Strategy Selection", "description": "Select appropriate trading strategy based on market conditions", "type": "reasoning", "reasoning_type": "causal", "context": "Market Analysis: ${market_analysis}\nSentiment Analysis: ${sentiment_analysis}\nPortfolio Analysis: ${portfolio_analysis}", "question": "What trading strategy is most appropriate given the current market conditions, sentiment, and portfolio composition?", "variables": ["market_trend", "volatility", "sentiment", "portfolio_exposure", "risk_tolerance"]}, {"id": "step_5", "name": "Risk Assessment", "description": "Assess risk of proposed trading strategy", "type": "parallel", "branches": [[{"id": "step_5a", "name": "Trading Risk Assessment", "description": "Assess market and trading risks", "type": "agent_task", "agent_id": "trading_agent", "task_type": "risk_assessment", "parameters": {"portfolio_id": "${portfolio_id}", "symbols": "${symbols}", "strategy": "${selected_strategy}"}}], [{"id": "step_5b", "name": "Insurance Risk Assessment", "description": "Assess insurance implications of trading strategy", "type": "agent_task", "agent_id": "insurance_agent", "task_type": "risk_assessment", "parameters": {"customer_id": "${customer_id}", "asset_class": "${asset_class}", "exposure_amount": "${exposure_amount}"}}]]}, {"id": "step_6", "name": "Risk Evaluation", "description": "Evaluate overall risk profile", "type": "transformation", "transformation_type": "aggregate", "aggregation": {"combined_risk_score": {"type": "avg", "sources": ["trading_risk.risk_score", "insurance_risk.risk_score"]}, "risk_level": {"type": "max", "sources": ["trading_risk.risk_level", "insurance_risk.risk_level"]}, "risk_factors": {"type": "concat", "sources": ["trading_risk.risk_factors", "insurance_risk.risk_factors"], "separator": ", "}}}, {"id": "step_7", "name": "Portfolio Optimization", "description": "Optimize portfolio allocation using quantum computing", "type": "condition", "condition": {"type": "simple", "field": "combined_risk.risk_level", "operator": "in", "value": ["low", "medium"]}, "true_branch": [{"id": "step_7a", "name": "Quantum Portfolio Optimization", "description": "Optimize portfolio using quantum computing", "type": "agent_task", "agent_id": "trading_agent", "task_type": "quantum_optimization", "parameters": {"portfolio_id": "${portfolio_id}", "risk_tolerance": "${risk_tolerance}", "target_symbols": "${symbols}", "optimization_objective": "sharpe_ratio"}}], "false_branch": [{"id": "step_7b", "name": "Risk Mitigation", "description": "Apply risk mitigation strategies", "type": "agent_task", "agent_id": "trading_agent", "task_type": "risk_mitigation", "parameters": {"portfolio_id": "${portfolio_id}", "risk_factors": "${combined_risk.risk_factors}", "risk_level": "${combined_risk.risk_level}"}}]}, {"id": "step_8", "name": "Insurance Coverage Check", "description": "Check if additional insurance coverage is needed", "type": "agent_task", "agent_id": "insurance_agent", "task_type": "coverage_analysis", "parameters": {"customer_id": "${customer_id}", "portfolio_value": "${portfolio_value}", "asset_allocation": "${optimized_allocation}", "risk_level": "${combined_risk.risk_level}"}}, {"id": "step_9", "name": "Insurance Recommendation", "description": "Process insurance recommendation", "type": "condition", "condition": {"type": "simple", "field": "coverage_analysis.additional_coverage_needed", "operator": "eq", "value": true}, "true_branch": [{"id": "step_9a", "name": "Generate Insurance Quote", "description": "Generate quote for additional insurance coverage", "type": "agent_task", "agent_id": "insurance_agent", "task_type": "quote_generation", "parameters": {"customer_id": "${customer_id}", "coverage_type": "${coverage_analysis.recommended_coverage_type}", "coverage_amount": "${coverage_analysis.recommended_coverage_amount}", "risk_factors": "${combined_risk.risk_factors}"}}], "false_branch": []}, {"id": "step_10", "name": "Trade Execution Decision", "description": "Decide whether to execute trades", "type": "condition", "condition": {"type": "and", "conditions": [{"type": "simple", "field": "combined_risk.risk_level", "operator": "in", "value": ["low", "medium"]}, {"type": "simple", "field": "optimized_allocation.expected_return", "operator": "gt", "value": 0.05}]}, "true_branch": [{"id": "step_10a", "name": "Execute Trades", "description": "Execute trades based on optimized allocation", "type": "agent_task", "agent_id": "trading_agent", "task_type": "execute_trades", "parameters": {"portfolio_id": "${portfolio_id}", "allocation": "${optimized_allocation.weights}", "execution_strategy": "vwap", "time_horizon": "day"}}], "false_branch": [{"id": "step_10b", "name": "Generate Trading Alert", "description": "Generate alert for manual review", "type": "agent_task", "agent_id": "trading_agent", "task_type": "generate_alert", "parameters": {"alert_type": "trading_opportunity", "priority": "medium", "message": "Trading opportunity identified but requires manual review due to risk factors or low expected return", "data": {"portfolio_id": "${portfolio_id}", "symbols": "${symbols}", "risk_level": "${combined_risk.risk_level}", "expected_return": "${optimized_allocation.expected_return}", "risk_factors": "${combined_risk.risk_factors}"}}}]}, {"id": "step_11", "name": "Workflow Summary", "description": "Generate summary of workflow execution", "type": "transformation", "transformation_type": "map", "mapping": {"workflow_id": "trading_strategy_workflow", "portfolio_id": "portfolio_id", "customer_id": "customer_id", "symbols": "symbols", "market_sentiment": "sentiment_analysis.market_sentiment", "risk_level": "combined_risk.risk_level", "expected_return": "optimized_allocation.expected_return", "trades_executed": {"source": "trades_executed", "transform": {"type": "bool"}}, "insurance_quote_generated": {"source": "insurance_quote.quote_id", "transform": {"type": "bool"}}, "completed_at": {"source": "current_timestamp", "transform": {"type": "string"}}}}], "input_schema": {"type": "object", "required": ["portfolio_id", "customer_id", "symbols", "timeframe", "risk_tolerance"], "properties": {"portfolio_id": {"type": "string", "description": "Unique identifier for the portfolio"}, "customer_id": {"type": "string", "description": "Unique identifier for the customer"}, "symbols": {"type": "array", "items": {"type": "string"}, "description": "List of trading symbols to analyze"}, "timeframe": {"type": "string", "description": "Timeframe for analysis", "enum": ["1d", "1w", "1m", "3m", "6m", "1y"], "default": "1d"}, "risk_tolerance": {"type": "number", "description": "Risk tolerance level (0.0 to 1.0)", "minimum": 0.0, "maximum": 1.0, "default": 0.5}, "asset_class": {"type": "string", "description": "Primary asset class", "enum": ["stocks", "bonds", "crypto", "forex", "commodities"], "default": "stocks"}, "portfolio_value": {"type": "number", "description": "Current portfolio value in dollars"}, "exposure_amount": {"type": "number", "description": "Amount of exposure for insurance purposes"}}}, "output_schema": {"type": "object", "properties": {"workflow_id": {"type": "string", "description": "Identifier for the workflow"}, "portfolio_id": {"type": "string", "description": "Unique identifier for the portfolio"}, "customer_id": {"type": "string", "description": "Unique identifier for the customer"}, "symbols": {"type": "array", "items": {"type": "string"}, "description": "List of trading symbols analyzed"}, "market_sentiment": {"type": "object", "description": "Overall market sentiment"}, "risk_level": {"type": "string", "description": "Combined risk level assessment", "enum": ["low", "medium", "high", "very_high"]}, "expected_return": {"type": "number", "description": "Expected return from optimized allocation"}, "trades_executed": {"type": "boolean", "description": "Whether trades were executed"}, "insurance_quote_generated": {"type": "boolean", "description": "Whether an insurance quote was generated"}, "completed_at": {"type": "string", "description": "Timestamp when workflow completed"}}}}