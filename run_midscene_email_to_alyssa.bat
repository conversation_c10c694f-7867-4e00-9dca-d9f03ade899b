@echo off
echo Midscene Email to Alyssa
echo ======================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python 3.8 or higher.
    exit /b 1
)

REM Run the script
echo Running Midscene Email to Alyssa script...
echo.

python midscene_email_to_alyssa.py

echo.
if %errorlevel% equ 0 (
    echo Script completed successfully!
) else (
    echo There was an error running the script.
)

echo.
pause
