"""
Command-Line Interface for Browser Automation.

This module provides a command-line interface for the browser automation service.
"""
import os
import sys
import json
import asyncio
import logging
import argparse
from typing import Dict, List, Optional, Any, Union, Tuple
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

try:
    from services.browser_automation_service import get_browser_automation_service
    from ui_tars.browser_automation_manager import AutomationProvider
except ImportError:
    # Define AutomationProvider enum if not available
    from enum import Enum
    class AutomationProvider(Enum):
        """Enum for automation providers."""
        UI_TARS = "ui_tars"
        MIDSCENE = "midscene"
        AUTO = "auto"

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("browser_automation_cli.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("browser_automation_cli")

async def execute_command(args):
    """
    Execute a browser automation command.
    
    Args:
        args: Command-line arguments
    """
    service = get_browser_automation_service()
    
    # Initialize service
    await service.initialize()
    
    # Execute command
    result = await service.execute_command(args.command)
    
    # Print result
    if "error" in result:
        print(f"Error: {result['error']}")
    else:
        print("Command executed successfully")
        print(f"Result: {result}")
    
    # Stop service if not interactive
    if not args.interactive:
        await service.stop()

async def send_email(args):
    """
    Send an email.
    
    Args:
        args: Command-line arguments
    """
    service = get_browser_automation_service()
    
    # Initialize service
    await service.initialize()
    
    # Send email
    success = await service.send_email(args.to, args.subject, args.body)
    
    # Print result
    if success:
        print("Email sent successfully")
    else:
        print("Failed to send email")
    
    # Stop service if not interactive
    if not args.interactive:
        await service.stop()

async def send_text(args):
    """
    Send a text message.
    
    Args:
        args: Command-line arguments
    """
    service = get_browser_automation_service()
    
    # Initialize service
    await service.initialize()
    
    # Send text message
    success = await service.send_text_message(args.to, args.message)
    
    # Print result
    if success:
        print("Text message sent successfully")
    else:
        print("Failed to send text message")
    
    # Stop service if not interactive
    if not args.interactive:
        await service.stop()

async def make_call(args):
    """
    Make a phone call.
    
    Args:
        args: Command-line arguments
    """
    service = get_browser_automation_service()
    
    # Initialize service
    await service.initialize()
    
    # Make call
    success = await service.make_call(args.to)
    
    # Print result
    if success:
        print("Call initiated successfully")
    else:
        print("Failed to make call")
    
    # Stop service if not interactive
    if not args.interactive:
        await service.stop()

async def health_check(args):
    """
    Perform a health check.
    
    Args:
        args: Command-line arguments
    """
    service = get_browser_automation_service()
    
    # Initialize service
    await service.initialize()
    
    # Perform health check
    health = await service.health_check()
    
    # Print result
    print(f"Health check result: {health['status']}")
    
    if health["status"] == "unhealthy":
        print("Issues:")
        for issue in health["issues"]:
            print(f"- {issue}")
        
        if args.repair:
            print("\nAttempting to repair...")
            repair_result = await service.auto_repair()
            
            print(f"Repair result: {repair_result['success']}")
            print(f"Message: {repair_result['message']}")
            
            if repair_result["actions_taken"]:
                print("Actions taken:")
                for action in repair_result["actions_taken"]:
                    print(f"- {action}")
    
    # Stop service if not interactive
    if not args.interactive:
        await service.stop()

async def interactive_mode(args):
    """
    Run in interactive mode.
    
    Args:
        args: Command-line arguments
    """
    service = get_browser_automation_service()
    
    # Initialize service
    await service.initialize()
    
    print("\nBrowser Automation Interactive Mode")
    print("==================================")
    print("Type 'exit' to quit, 'help' for commands")
    print()
    
    while True:
        try:
            command = input("Command> ")
            
            if command.lower() in ["exit", "quit", "q"]:
                break
            
            if command.lower() == "help":
                print("\nAvailable commands:")
                print("  browse <url> - Browse to a URL")
                print("  screenshot - Take a screenshot")
                print("  click <text> - Click on element with text")
                print("  type <text> - Type text")
                print("  email <to> <subject> <body> - Send an email")
                print("  text <to> <message> - Send a text message")
                print("  call <to> - Make a phone call")
                print("  health - Perform health check")
                print("  repair - Attempt to repair")
                print("  exit - Exit interactive mode")
                print()
                continue
            
            if command.lower() == "health":
                health = await service.health_check()
                print(f"Health check result: {health['status']}")
                if health["issues"]:
                    print("Issues:")
                    for issue in health["issues"]:
                        print(f"- {issue}")
                continue
            
            if command.lower() == "repair":
                repair_result = await service.auto_repair()
                print(f"Repair result: {repair_result['success']}")
                print(f"Message: {repair_result['message']}")
                if repair_result["actions_taken"]:
                    print("Actions taken:")
                    for action in repair_result["actions_taken"]:
                        print(f"- {action}")
                continue
            
            if command.lower().startswith("browse "):
                url = command[7:].strip()
                print(f"Browsing to {url}...")
                result = await service.execute_command(f"Browse to {url}")
                if "error" in result:
                    print(f"Error: {result['error']}")
                else:
                    print("Command executed successfully")
                continue
            
            if command.lower() == "screenshot":
                print("Taking screenshot...")
                result = await service.execute_command("Take a screenshot")
                if "error" in result:
                    print(f"Error: {result['error']}")
                else:
                    print("Command executed successfully")
                continue
            
            if command.lower().startswith("click "):
                text = command[6:].strip()
                print(f"Clicking on '{text}'...")
                result = await service.execute_command(f"Click on '{text}'")
                if "error" in result:
                    print(f"Error: {result['error']}")
                else:
                    print("Command executed successfully")
                continue
            
            if command.lower().startswith("type "):
                text = command[5:].strip()
                print(f"Typing '{text}'...")
                result = await service.execute_command(f"Type '{text}'")
                if "error" in result:
                    print(f"Error: {result['error']}")
                else:
                    print("Command executed successfully")
                continue
            
            if command.lower().startswith("email "):
                parts = command[6:].strip().split(" ", 2)
                if len(parts) < 3:
                    print("Error: Invalid email command format")
                    print("Usage: email <to> <subject> <body>")
                    continue
                
                to, subject, body = parts
                print(f"Sending email to {to}...")
                success = await service.send_email(to, subject, body)
                if success:
                    print("Email sent successfully")
                else:
                    print("Failed to send email")
                continue
            
            if command.lower().startswith("text "):
                parts = command[5:].strip().split(" ", 1)
                if len(parts) < 2:
                    print("Error: Invalid text command format")
                    print("Usage: text <to> <message>")
                    continue
                
                to, message = parts
                print(f"Sending text message to {to}...")
                success = await service.send_text_message(to, message)
                if success:
                    print("Text message sent successfully")
                else:
                    print("Failed to send text message")
                continue
            
            if command.lower().startswith("call "):
                to = command[5:].strip()
                print(f"Making call to {to}...")
                success = await service.make_call(to)
                if success:
                    print("Call initiated successfully")
                else:
                    print("Failed to make call")
                continue
            
            # Execute command directly
            print(f"Executing command: {command}")
            result = await service.execute_command(command)
            if "error" in result:
                print(f"Error: {result['error']}")
            else:
                print("Command executed successfully")
        
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"Error: {e}")
    
    print("Exiting interactive mode")
    
    # Stop service
    await service.stop()

def main():
    """Main entry point for the command-line interface."""
    parser = argparse.ArgumentParser(description="Browser Automation CLI")
    parser.add_argument("--interactive", "-i", action="store_true", help="Run in interactive mode")
    
    subparsers = parser.add_subparsers(dest="command_type", help="Command type")
    
    # Execute command
    execute_parser = subparsers.add_parser("execute", help="Execute a browser automation command")
    execute_parser.add_argument("command", help="Command to execute")
    execute_parser.set_defaults(func=execute_command)
    
    # Send email
    email_parser = subparsers.add_parser("email", help="Send an email")
    email_parser.add_argument("to", help="Recipient email address")
    email_parser.add_argument("subject", help="Email subject")
    email_parser.add_argument("body", help="Email body")
    email_parser.set_defaults(func=send_email)
    
    # Send text message
    text_parser = subparsers.add_parser("text", help="Send a text message")
    text_parser.add_argument("to", help="Recipient phone number")
    text_parser.add_argument("message", help="Message text")
    text_parser.set_defaults(func=send_text)
    
    # Make call
    call_parser = subparsers.add_parser("call", help="Make a phone call")
    call_parser.add_argument("to", help="Recipient phone number")
    call_parser.set_defaults(func=make_call)
    
    # Health check
    health_parser = subparsers.add_parser("health", help="Perform a health check")
    health_parser.add_argument("--repair", "-r", action="store_true", help="Attempt to repair if unhealthy")
    health_parser.set_defaults(func=health_check)
    
    args = parser.parse_args()
    
    if args.interactive:
        asyncio.run(interactive_mode(args))
    elif args.command_type:
        asyncio.run(args.func(args))
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
