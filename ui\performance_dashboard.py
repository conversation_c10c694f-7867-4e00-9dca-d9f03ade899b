"""
Performance Dashboard for the Multi-Agent AI System.

This module provides a dashboard for visualizing system performance metrics.
"""
import os
import json
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import numpy as np
from pathlib import Path

from core.logger import setup_logger
from core.state_manager import StateManager
from core.metrics_collector import MetricsCollector

# Set up logger
logger = setup_logger("performance_dashboard")

class PerformanceDashboard:
    """
    Dashboard for visualizing system performance metrics.
    
    This class provides a dashboard for visualizing system performance metrics,
    including CPU usage, memory usage, agent performance, and workflow performance.
    """
    
    def __init__(
        self,
        state_manager: StateManager,
        metrics_collector: MetricsCollector,
        output_dir: Optional[str] = None
    ):
        """
        Initialize the performance dashboard.
        
        Args:
            state_manager (StateManager): System state manager
            metrics_collector (MetricsCollector): Metrics collector
            output_dir (Optional[str]): Directory to save visualizations
        """
        self.state_manager = state_manager
        self.metrics_collector = metrics_collector
        self.output_dir = output_dir or "visualizations"
        
        # Create output directory if it doesn't exist
        os.makedirs(self.output_dir, exist_ok=True)
    
    async def generate_system_dashboard(
        self,
        time_window: int = 3600,  # 1 hour
        save: bool = True
    ) -> Optional[str]:
        """
        Generate a system performance dashboard.
        
        Args:
            time_window (int): Time window in seconds
            save (bool): Whether to save the dashboard
            
        Returns:
            Optional[str]: Path to the saved dashboard if save=True, None otherwise
        """
        # Get system metrics
        metrics = await self.metrics_collector.get_system_metrics(time_window)
        
        if not metrics:
            logger.error("No system metrics available")
            return None
        
        # Extract data
        timestamps = [datetime.fromisoformat(metric["timestamp"]) for metric in metrics]
        cpu_percent = [metric["cpu_percent"] for metric in metrics]
        memory_percent = [metric["memory_percent"] for metric in metrics]
        disk_percent = [metric["disk_percent"] for metric in metrics]
        
        # Create figure
        fig, axs = plt.subplots(3, 1, figsize=(12, 10), sharex=True)
        
        # Set title
        fig.suptitle("System Performance Dashboard", fontsize=16)
        
        # Plot CPU usage
        axs[0].plot(timestamps, cpu_percent, "b-", label="CPU Usage")
        axs[0].set_ylabel("CPU Usage (%)")
        axs[0].set_ylim(0, 100)
        axs[0].grid(True)
        axs[0].legend()
        
        # Plot memory usage
        axs[1].plot(timestamps, memory_percent, "g-", label="Memory Usage")
        axs[1].set_ylabel("Memory Usage (%)")
        axs[1].set_ylim(0, 100)
        axs[1].grid(True)
        axs[1].legend()
        
        # Plot disk usage
        axs[2].plot(timestamps, disk_percent, "r-", label="Disk Usage")
        axs[2].set_ylabel("Disk Usage (%)")
        axs[2].set_ylim(0, 100)
        axs[2].grid(True)
        axs[2].legend()
        
        # Format x-axis
        axs[2].set_xlabel("Time")
        axs[2].xaxis.set_major_formatter(mdates.DateFormatter("%H:%M:%S"))
        axs[2].xaxis.set_major_locator(mdates.AutoDateLocator())
        
        # Adjust layout
        plt.tight_layout()
        plt.subplots_adjust(top=0.9)
        
        # Save or show
        if save:
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            filename = f"system_dashboard_{timestamp}.png"
            filepath = os.path.join(self.output_dir, filename)
            plt.savefig(filepath)
            plt.close()
            logger.info(f"System dashboard saved to {filepath}")
            return filepath
        else:
            plt.show()
            plt.close()
            return None
    
    async def generate_agent_dashboard(
        self,
        agent_id: Optional[str] = None,
        time_window: int = 3600,  # 1 hour
        save: bool = True
    ) -> Optional[str]:
        """
        Generate an agent performance dashboard.
        
        Args:
            agent_id (Optional[str]): Agent ID, None for all agents
            time_window (int): Time window in seconds
            save (bool): Whether to save the dashboard
            
        Returns:
            Optional[str]: Path to the saved dashboard if save=True, None otherwise
        """
        # Get agent metrics
        agent_metrics = await self.metrics_collector.get_agent_metrics(agent_id, time_window)
        
        if not agent_metrics:
            logger.error("No agent metrics available")
            return None
        
        # Create figure
        fig, axs = plt.subplots(2, 2, figsize=(14, 10))
        
        # Set title
        if agent_id:
            fig.suptitle(f"Agent Performance Dashboard: {agent_id}", fontsize=16)
        else:
            fig.suptitle("Agent Performance Dashboard", fontsize=16)
        
        # Plot message metrics for each agent
        for i, (agent_id, metrics) in enumerate(agent_metrics.items()):
            if not metrics:
                continue
            
            # Extract data
            timestamps = [datetime.fromisoformat(metric["timestamp"]) for metric in metrics]
            messages_sent = [metric["messages_sent"] for metric in metrics]
            messages_received = [metric["messages_received"] for metric in metrics]
            message_errors = [metric["message_errors"] for metric in metrics]
            
            # Plot messages sent/received
            axs[0, 0].plot(timestamps, messages_sent, label=f"{agent_id} Sent")
            axs[0, 0].plot(timestamps, messages_received, label=f"{agent_id} Received")
            
            # Plot message errors
            axs[0, 1].plot(timestamps, message_errors, label=f"{agent_id} Errors")
            
            # Extract task data
            tasks_completed = [metric["tasks_completed"] for metric in metrics]
            tasks_failed = [metric["tasks_failed"] for metric in metrics]
            tasks_pending = [metric["tasks_pending"] for metric in metrics]
            
            # Plot tasks completed/failed
            axs[1, 0].plot(timestamps, tasks_completed, label=f"{agent_id} Completed")
            axs[1, 0].plot(timestamps, tasks_failed, label=f"{agent_id} Failed")
            
            # Plot tasks pending
            axs[1, 1].plot(timestamps, tasks_pending, label=f"{agent_id} Pending")
        
        # Set labels and grid
        axs[0, 0].set_ylabel("Message Count")
        axs[0, 0].set_title("Messages Sent/Received")
        axs[0, 0].grid(True)
        axs[0, 0].legend()
        
        axs[0, 1].set_ylabel("Error Count")
        axs[0, 1].set_title("Message Errors")
        axs[0, 1].grid(True)
        axs[0, 1].legend()
        
        axs[1, 0].set_ylabel("Task Count")
        axs[1, 0].set_title("Tasks Completed/Failed")
        axs[1, 0].grid(True)
        axs[1, 0].legend()
        
        axs[1, 1].set_ylabel("Task Count")
        axs[1, 1].set_title("Tasks Pending")
        axs[1, 1].grid(True)
        axs[1, 1].legend()
        
        # Format x-axis
        for ax in axs.flat:
            ax.xaxis.set_major_formatter(mdates.DateFormatter("%H:%M:%S"))
            ax.xaxis.set_major_locator(mdates.AutoDateLocator())
        
        # Adjust layout
        plt.tight_layout()
        plt.subplots_adjust(top=0.9)
        
        # Save or show
        if save:
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            if agent_id:
                filename = f"agent_dashboard_{agent_id}_{timestamp}.png"
            else:
                filename = f"agent_dashboard_{timestamp}.png"
            filepath = os.path.join(self.output_dir, filename)
            plt.savefig(filepath)
            plt.close()
            logger.info(f"Agent dashboard saved to {filepath}")
            return filepath
        else:
            plt.show()
            plt.close()
            return None
    
    async def generate_workflow_dashboard(
        self,
        workflow_id: Optional[str] = None,
        time_window: int = 3600,  # 1 hour
        save: bool = True
    ) -> Optional[str]:
        """
        Generate a workflow performance dashboard.
        
        Args:
            workflow_id (Optional[str]): Workflow ID, None for all workflows
            time_window (int): Time window in seconds
            save (bool): Whether to save the dashboard
            
        Returns:
            Optional[str]: Path to the saved dashboard if save=True, None otherwise
        """
        # Get workflow metrics
        workflow_metrics = await self.metrics_collector.get_workflow_metrics(workflow_id, time_window)
        
        if not workflow_metrics:
            logger.error("No workflow metrics available")
            return None
        
        # Create figure
        fig, axs = plt.subplots(2, 1, figsize=(12, 10), sharex=True)
        
        # Set title
        if workflow_id:
            fig.suptitle(f"Workflow Performance Dashboard: {workflow_id}", fontsize=16)
        else:
            fig.suptitle("Workflow Performance Dashboard", fontsize=16)
        
        # Plot execution metrics for each workflow
        for workflow_id, metrics in workflow_metrics.items():
            if not metrics:
                continue
            
            # Extract data
            timestamps = [datetime.fromisoformat(metric["timestamp"]) for metric in metrics]
            executions_completed = [metric["executions_completed"] for metric in metrics]
            executions_failed = [metric["executions_failed"] for metric in metrics]
            executions_running = [metric["executions_running"] for metric in metrics]
            avg_execution_time = [metric["avg_execution_time"] for metric in metrics]
            
            # Plot executions
            axs[0].plot(timestamps, executions_completed, label=f"{workflow_id} Completed")
            axs[0].plot(timestamps, executions_failed, label=f"{workflow_id} Failed")
            axs[0].plot(timestamps, executions_running, label=f"{workflow_id} Running")
            
            # Plot average execution time
            axs[1].plot(timestamps, avg_execution_time, label=f"{workflow_id} Avg Time")
        
        # Set labels and grid
        axs[0].set_ylabel("Execution Count")
        axs[0].set_title("Workflow Executions")
        axs[0].grid(True)
        axs[0].legend()
        
        axs[1].set_ylabel("Execution Time (s)")
        axs[1].set_title("Average Execution Time")
        axs[1].grid(True)
        axs[1].legend()
        
        # Format x-axis
        axs[1].set_xlabel("Time")
        axs[1].xaxis.set_major_formatter(mdates.DateFormatter("%H:%M:%S"))
        axs[1].xaxis.set_major_locator(mdates.AutoDateLocator())
        
        # Adjust layout
        plt.tight_layout()
        plt.subplots_adjust(top=0.9)
        
        # Save or show
        if save:
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            if workflow_id:
                filename = f"workflow_dashboard_{workflow_id}_{timestamp}.png"
            else:
                filename = f"workflow_dashboard_{timestamp}.png"
            filepath = os.path.join(self.output_dir, filename)
            plt.savefig(filepath)
            plt.close()
            logger.info(f"Workflow dashboard saved to {filepath}")
            return filepath
        else:
            plt.show()
            plt.close()
            return None
    
    async def generate_message_dashboard(
        self,
        time_window: int = 3600,  # 1 hour
        save: bool = True
    ) -> Optional[str]:
        """
        Generate a message performance dashboard.
        
        Args:
            time_window (int): Time window in seconds
            save (bool): Whether to save the dashboard
            
        Returns:
            Optional[str]: Path to the saved dashboard if save=True, None otherwise
        """
        # Get message metrics
        metrics = await self.metrics_collector.get_message_metrics(time_window)
        
        if not metrics:
            logger.error("No message metrics available")
            return None
        
        # Extract data
        timestamps = [datetime.fromisoformat(metric["timestamp"]) for metric in metrics]
        messages_processed = [metric["messages_processed"] for metric in metrics]
        message_errors = [metric["message_errors"] for metric in metrics]
        broadcasts = [metric["broadcasts"] for metric in metrics]
        coordinator_sent = [metric["coordinator_sent"] for metric in metrics]
        coordinator_received = [metric["coordinator_received"] for metric in metrics]
        
        # Create figure
        fig, axs = plt.subplots(2, 1, figsize=(12, 10), sharex=True)
        
        # Set title
        fig.suptitle("Message Performance Dashboard", fontsize=16)
        
        # Plot message counts
        axs[0].plot(timestamps, messages_processed, label="Processed")
        axs[0].plot(timestamps, message_errors, label="Errors")
        axs[0].plot(timestamps, broadcasts, label="Broadcasts")
        
        # Plot coordinator messages
        axs[1].plot(timestamps, coordinator_sent, label="Coordinator Sent")
        axs[1].plot(timestamps, coordinator_received, label="Coordinator Received")
        
        # Set labels and grid
        axs[0].set_ylabel("Message Count")
        axs[0].set_title("System Messages")
        axs[0].grid(True)
        axs[0].legend()
        
        axs[1].set_ylabel("Message Count")
        axs[1].set_title("Coordinator Messages")
        axs[1].grid(True)
        axs[1].legend()
        
        # Format x-axis
        axs[1].set_xlabel("Time")
        axs[1].xaxis.set_major_formatter(mdates.DateFormatter("%H:%M:%S"))
        axs[1].xaxis.set_major_locator(mdates.AutoDateLocator())
        
        # Adjust layout
        plt.tight_layout()
        plt.subplots_adjust(top=0.9)
        
        # Save or show
        if save:
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            filename = f"message_dashboard_{timestamp}.png"
            filepath = os.path.join(self.output_dir, filename)
            plt.savefig(filepath)
            plt.close()
            logger.info(f"Message dashboard saved to {filepath}")
            return filepath
        else:
            plt.show()
            plt.close()
            return None
