"""
Simple Fix for UI-TARS Browser Integration Issues

This script provides a simplified fix for UI-TARS browser integration issues.
"""
import os
import sys
import json
import logging
import platform
import subprocess
import tempfile
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("simple_fix_ui_tars_browser.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("simple_fix_ui_tars_browser")

def find_chrome():
    """Find Chrome browser."""
    logger.info("Searching for Chrome browser...")
    
    os_type = platform.system()
    chrome_paths = []
    
    if os_type == "Windows":
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
        ]
    elif os_type == "Darwin":  # macOS
        chrome_paths = [
            "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
            os.path.expanduser("~/Applications/Google Chrome.app/Contents/MacOS/Google Chrome")
        ]
    else:  # Linux
        chrome_paths = [
            "/usr/bin/google-chrome",
            "/usr/bin/google-chrome-stable"
        ]
    
    for path in chrome_paths:
        if os.path.exists(path):
            logger.info(f"Found Chrome at: {path}")
            return path
    
    logger.warning("Chrome not found")
    return None

def find_ui_tars():
    """Find UI-TARS executable."""
    logger.info("Searching for UI-TARS executable...")
    
    os_type = platform.system()
    ui_tars_paths = []
    
    if os_type == "Windows":
        ui_tars_paths = [
            os.path.join(os.environ.get("PROGRAMFILES", "C:\\Program Files"), "UI-TARS", "UI-TARS.exe"),
            os.path.join(os.environ.get("PROGRAMFILES(X86)", "C:\\Program Files (x86)"), "UI-TARS", "UI-TARS.exe"),
            os.path.join(os.environ.get("LOCALAPPDATA", "C:\\Users\\<USER>\\AppData\\Local".format(os.getlogin())), "UI-TARS", "UI-TARS.exe")
        ]
    elif os_type == "Darwin":  # macOS
        ui_tars_paths = [
            "/Applications/UI-TARS.app/Contents/MacOS/UI-TARS",
            os.path.expanduser("~/Applications/UI-TARS.app/Contents/MacOS/UI-TARS")
        ]
    else:  # Linux
        ui_tars_paths = [
            "/usr/local/bin/ui-tars",
            "/usr/bin/ui-tars",
            os.path.expanduser("~/.local/bin/ui-tars")
        ]
    
    for path in ui_tars_paths:
        if os.path.exists(path):
            logger.info(f"Found UI-TARS at: {path}")
            return path
    
    logger.warning("UI-TARS not found")
    return None

def create_ui_tars_config(chrome_path, config_path=None):
    """Create UI-TARS configuration with Chrome browser."""
    logger.info("Creating UI-TARS configuration...")
    
    if not chrome_path:
        logger.error("Chrome path not provided")
        return None
    
    # Create temp directory for browser data
    temp_dir = tempfile.mkdtemp(prefix="ui_tars_browser_fix_")
    logger.info(f"Created temporary directory: {temp_dir}")
    
    # Create user data directory
    user_data_dir = os.path.join(temp_dir, "browser_data")
    os.makedirs(user_data_dir, exist_ok=True)
    
    # Create configuration
    config = {
        "ui_tars": {
            "version": "1.5",
            "enabled": True,
            "browser": {
                "type": "chrome",
                "executable_path": chrome_path,
                "user_data_dir": user_data_dir,
                "profile_directory": "Default",
                "detection": {
                    "auto_detect": True,
                    "fallback_types": ["chrome", "edge", "firefox", "brave"]
                }
            },
            "sandbox": {
                "enabled": True,
                "isolation_level": "high",
                "temp_dir": temp_dir
            },
            "debug": {
                "enabled": True,
                "log_level": "debug",
                "log_file": os.path.join(temp_dir, "ui_tars_debug.log")
            }
        }
    }
    
    # Save configuration
    if not config_path:
        config_path = os.path.join(temp_dir, "ui_tars_config.json")
    
    try:
        with open(config_path, "w") as f:
            json.dump(config, f, indent=2)
        
        logger.info(f"Saved UI-TARS configuration to: {config_path}")
        return config_path
    except Exception as e:
        logger.error(f"Error saving configuration: {e}")
        return None

def main():
    """Main function."""
    logger.info("Starting simple fix for UI-TARS browser integration issues")
    
    # Find Chrome
    chrome_path = find_chrome()
    if not chrome_path:
        logger.error("Chrome not found. Please install Chrome and try again.")
        return 1
    
    # Find UI-TARS
    ui_tars_path = find_ui_tars()
    if not ui_tars_path:
        logger.error("UI-TARS not found. Please install UI-TARS and try again.")
        return 1
    
    # Create UI-TARS configuration
    config_path = create_ui_tars_config(chrome_path)
    if not config_path:
        logger.error("Failed to create UI-TARS configuration")
        return 1
    
    logger.info("UI-TARS browser integration issues fixed successfully")
    logger.info(f"UI-TARS executable: {ui_tars_path}")
    logger.info(f"Chrome browser: {chrome_path}")
    logger.info(f"Configuration file: {config_path}")
    
    print("\nUI-TARS Browser Integration Fix")
    print("==============================")
    print(f"UI-TARS executable: {ui_tars_path}")
    print(f"Chrome browser: {chrome_path}")
    print(f"Configuration file: {config_path}")
    print("\nTo start UI-TARS with the fixed configuration, run:")
    print(f'"{ui_tars_path}" --config "{config_path}"')
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
