@echo off
REM Setup Alyssa Follow-up Schedule
REM This script sets up a follow-up schedule for <PERSON><PERSON>

echo.
echo ===================================
echo    Setup Alyssa Follow-up Schedule
echo ===================================
echo.

echo This script will create a follow-up schedule for <PERSON><PERSON>:
echo  1. Day 2: Text message follow-up
echo  2. Day 4: Call follow-up
echo  3. Day 7: Email follow-up
echo  4. Day 14: Final text follow-up
echo.

REM Ask for confirmation
set /p CONFIRM="Create follow-up schedule? (y/n): "
if /i not "%CONFIRM%"=="y" (
    echo Follow-up schedule creation cancelled.
    goto end
)

echo.
echo Creating follow-up schedule for Alyssa Chirinos...
echo.

REM Run the command
python setup_alyssa_followup.py --name "<PERSON><PERSON>" --email "<EMAIL>" --phone "9419294330"

echo.
echo Follow-up schedule created.
echo.

:end
pause
