# Install AlphaEvolve as a Windows service
# This script requires <PERSON><PERSON><PERSON> (Non-Sucking Service Manager)
# Download NSSM from https://nssm.cc/download

# Check if running as administrator
$currentPrincipal = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
$isAdmin = $currentPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

if (-not $isAdmin) {
    Write-Host "This script must be run as Administrator. Please restart PowerShell as Administrator and try again." -ForegroundColor Red
    exit
}

# Get the current script directory
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path

# Define the service name
$serviceName = "AlphaEvolveService"

# Check if NSSM is installed
$nssmPath = "C:\Program Files\nssm\nssm.exe"
if (-not (Test-Path $nssmPath)) {
    # Try to find NSSM in the current directory
    $nssmPath = Join-Path -Path $scriptPath -ChildPath "nssm.exe"
    if (-not (Test-Path $nssmPath)) {
        Write-Host "NSSM (Non-Sucking Service Manager) not found. Please download it from https://nssm.cc/download and place nssm.exe in this directory or install it to C:\Program Files\nssm\" -ForegroundColor Red
        exit
    }
}

# Check if Python is installed
$pythonPath = "python"
try {
    $pythonVersion = & python --version
    Write-Host "Found Python: $pythonVersion" -ForegroundColor Green
}
catch {
    Write-Host "Python not found. Please install Python and make sure it's in your PATH." -ForegroundColor Red
    exit
}

# Define the service parameters
$pythonScript = Join-Path -Path $scriptPath -ChildPath "run_alphaevolve_service.py"
$workingDirectory = $scriptPath

# Check if the service already exists
$serviceExists = Get-Service -Name $serviceName -ErrorAction SilentlyContinue

if ($serviceExists) {
    Write-Host "Service '$serviceName' already exists. Stopping and removing it..." -ForegroundColor Yellow
    
    # Stop the service
    Stop-Service -Name $serviceName -Force
    
    # Remove the service
    & $nssmPath remove $serviceName confirm
    
    Write-Host "Service removed." -ForegroundColor Green
}

# Install the service
Write-Host "Installing AlphaEvolve as a Windows service..." -ForegroundColor Cyan

# Create the service
& $nssmPath install $serviceName $pythonPath
& $nssmPath set $serviceName AppParameters "$pythonScript"
& $nssmPath set $serviceName AppDirectory "$workingDirectory"
& $nssmPath set $serviceName DisplayName "AlphaEvolve Service"
& $nssmPath set $serviceName Description "Runs AlphaEvolve in the background, continuing work on previous tasks and waiting for new requests."
& $nssmPath set $serviceName Start SERVICE_AUTO_START
& $nssmPath set $serviceName ObjectName LocalSystem
& $nssmPath set $serviceName AppStdout "$workingDirectory\logs\alphaevolve_service_stdout.log"
& $nssmPath set $serviceName AppStderr "$workingDirectory\logs\alphaevolve_service_stderr.log"
& $nssmPath set $serviceName AppRotateFiles 1
& $nssmPath set $serviceName AppRotateBytes 10485760
& $nssmPath set $serviceName AppRotateOnline 1

# Create logs directory if it doesn't exist
$logsDir = Join-Path -Path $workingDirectory -ChildPath "logs"
if (-not (Test-Path $logsDir)) {
    New-Item -Path $logsDir -ItemType Directory | Out-Null
}

# Start the service
Write-Host "Starting AlphaEvolve service..." -ForegroundColor Cyan
Start-Service -Name $serviceName

# Check if the service is running
$service = Get-Service -Name $serviceName
if ($service.Status -eq "Running") {
    Write-Host "AlphaEvolve service is now running!" -ForegroundColor Green
    Write-Host "The service will start automatically when Windows starts." -ForegroundColor Green
    Write-Host "You can manage the service in the Windows Services management console." -ForegroundColor Cyan
    Write-Host "Service logs are saved in: $logsDir" -ForegroundColor Cyan
} else {
    Write-Host "Failed to start AlphaEvolve service. Please check the logs in: $logsDir" -ForegroundColor Red
}

# Create a shortcut to the Jarvis interface
$desktopPath = [Environment]::GetFolderPath("Desktop")
$shortcutPath = Join-Path -Path $desktopPath -ChildPath "Jarvis Interface.lnk"

# Create a WScript.Shell object
$shell = New-Object -ComObject WScript.Shell

# Create the shortcut
$shortcut = $shell.CreateShortcut($shortcutPath)
$shortcut.TargetPath = "powershell.exe"
$shortcut.Arguments = "-ExecutionPolicy Bypass -File `"$scriptPath\start_jarvis.ps1`""
$shortcut.WorkingDirectory = $scriptPath
$shortcut.Description = "Start Jarvis Interface (AlphaEvolve is already running in the background)"
$shortcut.IconLocation = "powershell.exe,0"
$shortcut.Save()

Write-Host "Created shortcut on your desktop: 'Jarvis Interface.lnk'" -ForegroundColor Green
Write-Host "You can use this shortcut to open the Jarvis interface. AlphaEvolve will already be running in the background." -ForegroundColor Cyan

# Pause to see the message
Write-Host "Press any key to exit..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
