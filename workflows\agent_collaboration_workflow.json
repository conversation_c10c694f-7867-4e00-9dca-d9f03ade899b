{"name": "agent_collaboration_workflow", "description": "Workflow for enabling collaboration between different agents", "version": "1.0.0", "triggers": [{"type": "event", "event": "collaboration_request", "description": "Triggered when an agent requests collaboration"}, {"type": "schedule", "schedule": "0 */4 * * *", "description": "Check for collaboration opportunities every 4 hours"}], "steps": [{"id": "identify_collaboration_need", "agent": "agent_coordinator", "action": "identify_collaboration_need", "parameters": {"context": "${trigger.context}"}, "next": {"condition": "result.collaboration_needed", "true": "select_agents", "false": "end"}}, {"id": "select_agents", "agent": "agent_coordinator", "action": "select_agents", "parameters": {"task": "${previous.result.task}", "required_capabilities": "${previous.result.required_capabilities}"}, "next": "create_collaboration_session"}, {"id": "create_collaboration_session", "agent": "agent_coordinator", "action": "create_collaboration_session", "parameters": {"agents": "${previous.result.selected_agents}", "task": "${steps.identify_collaboration_need.result.task}", "context": "${steps.identify_collaboration_need.result.context}"}, "next": "execute_collaboration"}, {"id": "execute_collaboration", "agent": "agent_coordinator", "action": "execute_collaboration", "parameters": {"session_id": "${previous.result.session_id}"}, "next": "evaluate_results"}, {"id": "evaluate_results", "agent": "agent_coordinator", "action": "evaluate_collaboration_results", "parameters": {"session_id": "${steps.create_collaboration_session.result.session_id}", "results": "${previous.result.results}"}, "next": "store_knowledge"}, {"id": "store_knowledge", "agent": "agent_coordinator", "action": "store_collaboration_knowledge", "parameters": {"session_id": "${steps.create_collaboration_session.result.session_id}", "evaluation": "${previous.result.evaluation}", "knowledge": "${previous.result.knowledge}"}, "next": "end"}, {"id": "end", "type": "end"}]}